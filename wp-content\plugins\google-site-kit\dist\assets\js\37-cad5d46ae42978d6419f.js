(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[37],{1221:function(e,t,r){"use strict";r.r(t);var l=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var l in r)Object.prototype.hasOwnProperty.call(r,l)&&(e[l]=r[l])}return e}).apply(this,arguments)}var n=l.createElement("g",{filter:"url(#ghost-card-red_svg__filter0_d_4824_80809)"},l.createElement("rect",{width:165,height:90,rx:11,fill:"#fff"}),l.createElement("rect",{x:.5,y:.5,width:164,height:89,rx:10.5,stroke:"#EBEEF0"})),a=l.createElement("rect",{x:16,y:16,width:25,height:8,rx:4,fill:"#EBEEF0"}),o=l.createElement("rect",{x:16,y:33,width:50,height:20,rx:10,fill:"#FFDED3"}),c=l.createElement("rect",{x:16,y:68,width:133,height:8,rx:4,fill:"#EBEEF0"}),f=l.createElement("path",{d:"M24.997 40l6.594 6.593m0 0l.073-5.201m-.073 5.201l-5.202.074",stroke:"#fff",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"}),h=l.createElement("rect",{x:38,y:40,width:20,height:6,rx:2,fill:"#fff"}),d=l.createElement("defs",null,l.createElement("filter",{id:"ghost-card-red_svg__filter0_d_4824_80809",x:0,y:0,width:169,height:95,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},l.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),l.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),l.createElement("feOffset",{dx:4,dy:5}),l.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),l.createElement("feColorMatrix",{values:"0 0 0 0 0.921569 0 0 0 0 0.933333 0 0 0 0 0.941176 0 0 0 1 0"}),l.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_4824_80809"}),l.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_4824_80809",result:"shape"})));t.default=function SvgGhostCardRed(e){return l.createElement("svg",i({viewBox:"0 0 169 95",fill:"none"},e),n,a,o,c,f,h,d)}}}]);