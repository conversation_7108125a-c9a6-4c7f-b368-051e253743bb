(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[25],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(59),a=n(39),i=n(57);function o(t,n){var o,c=Object(r.a)(n),s=t.activeModules,u=t.referenceSiteURL,l=t.userIDHash,g=t.userRoles,f=void 0===g?[]:g,d=t.isAuthenticated,p=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(a.b,"]"))),!o){o=!0;var r=(null==f?void 0:f.length)?f.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:u,plugin_version:p||"",enabled_features:Array.from(i.a).join(","),active_modules:s.join(","),authenticated:d?"1":"0",user_properties:{user_roles:r,user_identifier:l}});var g=n.createElement("script");return g.setAttribute(a.b,""),g.async=!0,g.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(a.a),n.head.appendChild(g),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(a.a)}}}}}).call(this,n(28))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var r=n(5),a=n.n(r),i=n(6),o=n.n(i),c=n(16),s=n.n(c),u=n(59);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function f(e,t,n,r){var i=Object(u.a)(t);return function(){var t=s()(a.a.mark((function t(o,c,s,u){var l;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),l={send_to:"site_kit",event_category:o,event_label:s,value:u},t.abrupt("return",new Promise((function(e){var t,n,a=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(a),e()};i("event",c,g(g({},l),{},{event_callback:s})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,a){return t.apply(this,arguments)}}()}},105:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),u=n(11),l=n.n(u);function VisuallyHidden(t){var n=t.className,r=t.children,i=o()(t,["className","children"]);return r?e.createElement("span",a()({},i,{className:l()("screen-reader-text",n)}),r):null}VisuallyHidden.propTypes={className:s.a.string,children:s.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(4))},120:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var r=n(1),a=n.n(r),i=n(0),o=n(2),c=n(3),s=n(10),u=n(35),l=n(54);function ErrorNotice(t){var n,r=t.error,a=t.hasButton,g=void 0!==a&&a,f=t.storeName,d=t.message,p=void 0===d?r.message:d,m=t.noPrefix,b=void 0!==m&&m,v=t.skipRetryMessage,h=t.Icon,y=Object(c.useDispatch)(),O=Object(c.useSelect)((function(e){return f?e(f).getSelectorDataForError(r):null})),j=Object(i.useCallback)((function(){y(O.storeName).invalidateResolution(O.name,O.args)}),[y,O]);if(!r||Object(u.f)(r))return null;var S=g&&Object(u.d)(r,O);return g||v||(p=Object(o.sprintf)(
/* translators: %1$s: Error message from Google API. */
Object(o.__)("%1$s%2$s Please try again.","google-site-kit"),p,p.endsWith(".")?"":".")),e.createElement(i.Fragment,null,h&&e.createElement("div",{className:"googlesitekit-error-notice__icon"},e.createElement(h,{width:"24",height:"24"})),e.createElement(l.a,{message:p,reconnectURL:null===(n=r.data)||void 0===n?void 0:n.reconnectURL,noPrefix:b}),S&&e.createElement(s.Button,{className:"googlesitekit-error-notice__retry-button",onClick:j},Object(o.__)("Retry","google-site-kit")))}ErrorNotice.propTypes={error:a.a.shape({message:a.a.string}),hasButton:a.a.bool,storeName:a.a.string,message:a.a.string,noPrefix:a.a.bool,skipRetryMessage:a.a.bool,Icon:a.a.elementType}}).call(this,n(4))},122:function(e,t,n){"use strict";n.d(t,"d",(function(){return c})),n.d(t,"b",(function(){return s})),n.d(t,"a",(function(){return u})),n.d(t,"c",(function(){return l})),n.d(t,"e",(function(){return g}));var r=n(33),a=n.n(r),i=n(14),o=n(178);function c(e){var t=function(e){return"string"==typeof e&&/^[a-zA-Z0-9_]+$/.test(e)};return"string"==typeof e?e.split(",").every(t):Object(o.c)(e,(function(e){var n=e.hasOwnProperty("name")&&t(e.name);if(!e.hasOwnProperty("expression"))return n;var r="string"==typeof e.expression;return n&&r}),t)}function s(e){return Object(o.c)(e,(function(e){return e.hasOwnProperty("name")&&"string"==typeof e.name}))}function u(e){var t=["string"];return Object.keys(e).every((function(n){if(t.includes(a()(e[n])))return!0;if(Array.isArray(e[n]))return e[n].every((function(e){return t.includes(a()(e))}));if(Object(i.isPlainObject)(e[n])){var r=Object.keys(e[n]);return!!r.includes("filterType")&&!("emptyFilter"!==e[n].filterType&&!r.includes("value"))}return!1}))}function l(e){var t=["string"],n=["numericFilter","betweenFilter"];return Object.values(e).every((function(e){if(t.includes(a()(e)))return!0;if(Array.isArray(e))return e.every((function(e){return t.includes(a()(e))}));if(!Object(i.isPlainObject)(e))return!1;var r=e.filterType,o=e.value,c=e.fromValue,s=e.toValue;if(r&&!n.includes(r))return!1;var u=Object.keys(e);return r&&"numericFilter"!==r?"betweenFilter"===r&&(u.includes("fromValue")&&u.includes("toValue")&&[c,s].every((function(e){return!Object(i.isPlainObject)(e)||"int64Value"in e}))):u.includes("operation")&&u.includes("value")&&(!Object(i.isPlainObject)(o)||"int64Value"in o)}))}function g(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(i.isPlainObject)(e)&&((!e.hasOwnProperty("desc")||"boolean"==typeof e.desc)&&(e.metric?!e.dimension&&"string"==typeof(null===(t=e.metric)||void 0===t?void 0:t.metricName):!!e.dimension&&"string"==typeof(null===(n=e.dimension)||void 0===n?void 0:n.dimensionName)));var t,n}))}},126:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",a({viewBox:"0 0 13 13"},e),i)}},127:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",a({viewBox:"0 0 13 13"},e),i)}},128:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},129:function(e,t,n){"use strict";n.d(t,"a",(function(){return m})),n.d(t,"c",(function(){return v})),n.d(t,"b",(function(){return h}));var r=n(25),a=n.n(r),i=n(6),o=n.n(i),c=n(5),s=n.n(c),u=n(12),l=n.n(u),g=n(3),f=n.n(g),d=n(37),p=n(9),m=function(e){var t;l()(e,"storeName is required to create a snapshot store.");var n={},r={deleteSnapshot:s.a.mark((function e(){var t;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"DELETE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})),restoreSnapshot:s.a.mark((function e(){var t,n,r,a,i,o,c=arguments;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},n=t.clearAfterRestore,r=void 0===n||n,e.next=4,{payload:{},type:"RESTORE_SNAPSHOT"};case 4:if(a=e.sent,i=a.cacheHit,o=a.value,!i){e.next=13;break}return e.next=10,{payload:{snapshot:o},type:"SET_STATE_FROM_SNAPSHOT"};case 10:if(!r){e.next=13;break}return e.next=13,{payload:{},type:"DELETE_SNAPSHOT"};case 13:return e.abrupt("return",i);case 14:case"end":return e.stop()}}),e)})),createSnapshot:s.a.mark((function e(){var t;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"CREATE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))},i=(t={},o()(t,"DELETE_SNAPSHOT",(function(){return Object(d.c)("datastore::cache::".concat(e))})),o()(t,"CREATE_SNAPSHOT",Object(g.createRegistryControl)((function(t){return function(){return Object(d.f)("datastore::cache::".concat(e),t.stores[e].store.getState())}}))),o()(t,"RESTORE_SNAPSHOT",(function(){return Object(d.d)("datastore::cache::".concat(e),p.b)})),t);return{initialState:n,actions:r,controls:i,reducer:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1?arguments[1]:void 0,r=t.type,i=t.payload;switch(r){case"SET_STATE_FROM_SNAPSHOT":var o=i.snapshot,c=(o.error,a()(o,["error"]));return c;default:return e}}}},b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.a;return Object.values(e.stores).filter((function(e){return Object.keys(e.getActions()).includes("restoreSnapshot")}))},v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.a;return Promise.all(b(e).map((function(e){return e.getActions().createSnapshot()})))},h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.a;return Promise.all(b(e).map((function(e){return e.getActions().restoreSnapshot()})))}},1293:function(e,t,n){"use strict";n.r(t);var r=n(3),a=n.n(r),i=n(190),o=n.n(i),c=n(2),s=(n(763),n(764),n(942)),u=(n(768),n(943)),l=(n(769),n(770),n(944)),g=n(789),f=n(46),d=n(129),p=n(6),m=n.n(p),b=n(25),v=n.n(b),h=n(5),y=n.n(h),O=n(16),j=n.n(O),S=n(12),_=n.n(S),E=n(45),k=n.n(E),w=n(29),A=n(357),C=n(200),D=n(19),N=n(13),x=n(62),T=n(8),I={selectors:{areSettingsEditDependenciesLoaded:Object(r.createRegistrySelector)((function(e){return function(){return e(f.g).hasFinishedResolution("getAccounts")}}))}};function P(){return(P=j()(y.a.mark((function e(t){var n,r,a,i,o,c,s,u,l,g,d,p,m;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.select,r=t.dispatch,a=n(f.g).getAccountID(),n(f.g).getContainerID()!==f.b){e.next=16;break}return i=n(w.a).getValue(f.f,"containerName"),e.next=7,r(f.g).createContainer(a,f.d,{containerName:i});case 7:if(o=e.sent,c=o.response,!(s=o.error)){e.next=12;break}return e.abrupt("return",{error:s});case 12:return e.next=14,r(f.g).setContainerID(c.publicId);case 14:return e.next=16,r(f.g).setInternalContainerID(c.containerId);case 16:if(n(f.g).getAMPContainerID()!==f.b){e.next=30;break}return u=n(w.a).getValue(f.f,"ampContainerName"),e.next=21,r(f.g).createContainer(a,f.c,{containerName:u});case 21:if(l=e.sent,g=l.response,!(d=l.error)){e.next=26;break}return e.abrupt("return",{error:d});case 26:return e.next=28,r(f.g).setAMPContainerID(g.publicId);case 28:return e.next=30,r(f.g).setInternalAMPContainerID(g.containerId);case 30:if(!n(f.g).haveSettingsChanged()){e.next=41;break}return e.next=33,r(f.g).saveSettings();case 33:if(p=e.sent,!(m=p.error)){e.next=37;break}return e.abrupt("return",{error:m});case 37:if(!n(D.a).isModuleConnected("analytics-4")){e.next=41;break}return e.next=41,r(T.r).fetchGetSettings();case 41:return e.next=43,k.a.invalidateCache("modules","tagmanager");case 43:return e.abrupt("return",{});case 44:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function R(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function M(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?R(Object(n),!0).forEach((function(t){m()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):R(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var L,F,G,U,V,q,B,H,W=o.a.createModuleStore("tagmanager",{ownedSettingsSlugs:["accountID","ampContainerID","containerID","internalAMPContainerID","internalContainerID"],storeName:f.g,settingSlugs:["accountID","ampContainerID","containerID","internalContainerID","internalAMPContainerID","useSnippet","ownerID"],submitChanges:function(e){return P.apply(this,arguments)},validateCanSubmitChanges:function(e){var t=Object(x.e)(e),n=t(f.g),r=n.getAccountID,a=n.getContainerID,i=n.getContainers,o=n.getAMPContainerID,c=n.getInternalContainerID,s=n.getInternalAMPContainerID,u=n.haveSettingsChanged,l=n.isDoingSubmitChanges,g=t(N.c),d=g.isAMP,p=g.isSecondaryAMP,m=r();if(_()(!l(),C.a),_()(u(),C.b),_()(Object(A.c)(m),"a valid accountID is required to submit changes"),a()===f.b){var b=e(w.a).getValue(f.f,"containerName");_()(Object(A.e)(b),"a valid container name is required to submit changes");var v=i(m),h=Object(A.a)(b);_()(Object(A.b)(b,v),'a container with "'.concat(h,'" name already exists'))}if(d()){var y=o();if(_()(Object(A.f)(y),"a valid ampContainerID selection is required to submit changes"),Object(A.d)(y)&&_()(Object(A.g)(s()),"a valid internalAMPContainerID is required to submit changes"),y===f.b){var O=e(w.a).getValue(f.f,"ampContainerName");_()(Object(A.e)(O),"a valid container name is required to submit changes");var j=i(m),S=Object(A.a)(O);_()(Object(A.b)(O,j),'an AMP container with "'.concat(S,'" name already exists'))}}d()&&!p()||(_()(Object(A.f)(a()),"a valid containerID selection is required to submit changes"),Object(A.d)(a())&&_()(Object(A.g)(c()),"a valid internalContainerID is required to submit changes"))}});F=(L=W).actions,G=L.selectors,U=v()(L,["actions","selectors"]),V=F.setAmpContainerID,q=v()(F,["setAmpContainerID"]),B=G.getAmpContainerID,H=v()(G,["getAmpContainerID"]);var K=W=M(M({},U),{},{actions:M(M({},q),{},{setAMPContainerID:V}),selectors:M(M({},H),{},{getAMPContainerID:B})}),$=n(271),z=n(48);function X(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?X(Object(n),!0).forEach((function(t){m()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):X(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Z=Object(z.a)({baseName:"getAccounts",controlCallback:function(){return k.a.get("modules","tagmanager","accounts",null,{useCache:!1})},reducerCallback:function(e,t){return Y(Y({},e),{},{accounts:t})}}),J={accounts:void 0},Q={resetAccounts:y.a.mark((function e(){var t,n;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:return t=e.sent,n=t.dispatch,e.next=6,{payload:{},type:"RESET_ACCOUNTS"};case 6:n(f.g).invalidateResolutionForStoreSelector("getAccounts");case 7:case"end":return e.stop()}}),e)})),selectAccount:Object(x.f)((function(e){_()(Object($.c)(e),"A valid accountID selection is required to select.")}),y.a.mark((function e(t){var n,a,i,o,c,s,u,l,g;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:if(n=e.sent,a=n.dispatch,i=n.select,o=n.resolveSelect,t!==i(f.g).getAccountID()){e.next=8;break}return e.abrupt("return");case 8:if(a(f.g).setSettings({accountID:t,containerID:"",internalContainerID:"",ampContainerID:"",internalAMPContainerID:""}),T.a!==t&&!i(f.g).hasExistingTag()){e.next=11;break}return e.abrupt("return");case 11:if(c=i(N.c),s=c.isAMP,u=c.isSecondaryAMP,s()&&!u()){e.next=17;break}return e.next=15,r.commonActions.await(o(f.g).getWebContainers(t));case 15:(l=e.sent).length?1===l.length&&(a(f.g).setContainerID(l[0].publicId),a(f.g).setInternalContainerID(l[0].containerId)):(a(f.g).setContainerID(f.b),a(f.g).setInternalContainerID(""));case 17:if(!s()){e.next=22;break}return e.next=20,r.commonActions.await(o(f.g).getAMPContainers(t));case 20:(g=e.sent).length?1===g.length&&(a(f.g).setAMPContainerID(g[0].publicId),a(f.g).setInternalAMPContainerID(g[0].containerId)):(a(f.g).setAMPContainerID(f.b),a(f.g).setInternalAMPContainerID(""));case 22:case"end":return e.stop()}}),e)})))},ee={getAccounts:y.a.mark((function e(){var t,n,a,i,o,c;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:if(n=e.sent,a=n.select,i=n.dispatch,o=a(f.g).getAccounts()){e.next=11;break}return e.next=9,Z.actions.fetchGetAccounts();case 9:c=e.sent,o=c.response;case 11:1!==(null===(t=o)||void 0===t?void 0:t.length)||a(f.g).getAccountID()||i(f.g).selectAccount(o[0].accountId);case 12:case"end":return e.stop()}}),e)}))},te={getAccounts:function(e){return e.accounts},isDoingGetAccounts:Object(r.createRegistrySelector)((function(e){return function(){return e(f.g).isFetchingGetAccounts()}}))},ne=Object(r.combineStores)(Z,{initialState:J,actions:Q,reducer:function(e,t){switch(t.type){case"RESET_ACCOUNTS":return Y(Y({},e),{},{accounts:void 0,settings:Y(Y({},e.settings),{},{accountID:void 0,ampContainerID:void 0,containerID:void 0,internalAMPContainerID:void 0,internalContainerID:void 0})});default:return e}},resolvers:ee,selectors:te}),re=(ne.initialState,ne.actions,ne.controls,ne.reducer,ne.resolvers,ne.selectors,ne),ae=n(27),ie=n.n(ae),oe=y.a.mark(fe);function ce(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function se(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ce(Object(n),!0).forEach((function(t){m()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ce(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ue=Object(z.a)({baseName:"getContainers",argsToParams:function(e){return{accountID:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.accountID;_()(Object($.b)(t),"A valid accountID is required to fetch containers.")},controlCallback:function(e){var t=e.accountID;return k.a.get("modules","tagmanager","containers",{accountID:t},{useCache:!1})},reducerCallback:function(e,t,n){var r=n.accountID;return se(se({},e),{},{containers:se(se({},e.containers),{},m()({},r,t))})}}),le=Object(z.a)({baseName:"createContainer",argsToParams:function(e,t,n){return{accountID:e,usageContext:t,containerName:n.containerName}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.accountID,n=e.usageContext,r=e.containerName;_()(Object($.b)(t),"A valid accountID is required to create a container."),_()(Object($.h)(n),"A valid usageContext is required to create a container."),_()(Object($.e)(r),"A valid containerName is required to create a container.")},controlCallback:function(e){var t=e.accountID,n=e.usageContext,r=e.containerName;return k.a.set("modules","tagmanager","create-container",{accountID:t,usageContext:n,name:r})},reducerCallback:function(e,t,n){var r=n.accountID;return se(se({},e),{},{containers:se(se({},e.containers),{},m()({},r,[].concat(ie()(e.containers[r]||[]),[t])))})}}),ge={createContainer:Object(x.f)((function(e,t,n){var r=n.containerName;_()(Object($.b)(e),"A valid accountID is required to create a container."),_()(Object($.h)(t),"A valid usageContext is required to create a container."),_()(Object($.e)(r),"A valid containerName is required to create a container.")}),y.a.mark((function e(t,n,r){var a,i,o,c;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=r.containerName,e.next=3,le.actions.fetchCreateContainer(t,n,{containerName:a});case 3:return i=e.sent,o=i.response,c=i.error,e.abrupt("return",{response:o,error:c});case 7:case"end":return e.stop()}}),e)}))),selectContainerByID:Object(x.f)((function(e){_()(Object($.d)(e),"A valid container ID is required to select a container by ID.")}),y.a.mark((function e(t){var n,a,i,o,c,s;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:if(n=e.sent,a=n.select,i=n.dispatch,o=n.resolveSelect,c=a(f.g).getAccountID(),Object($.b)(c)){e.next=9;break}return e.abrupt("return");case 9:return e.next=11,r.commonActions.await(o(f.g).getContainers(c));case 11:if(s=a(f.g).getContainerByID(c,t)){e.next=14;break}return e.abrupt("return");case 14:s.usageContext.includes(f.d)?(i(f.g).setContainerID(t),i(f.g).setInternalContainerID(s.containerId)):s.usageContext.includes(f.c)&&(i(f.g).setAMPContainerID(t),i(f.g).setInternalAMPContainerID(s.containerId));case 15:case"end":return e.stop()}}),e)})))};function fe(e){var t,n;return y.a.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,r.commonActions.getRegistry();case 2:return t=a.sent,n=t.resolveSelect,a.next=6,r.commonActions.await(n(f.g).getContainers(e));case 6:case"end":return a.stop()}}),oe)}var de={getContainers:y.a.mark((function e(t){var n;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Object($.b)(t)){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,r.commonActions.getRegistry();case 4:if(n=e.sent,(0,n.select)(f.g).getContainers(t)){e.next=9;break}return e.next=9,ue.actions.fetchGetContainers(t);case 9:case"end":return e.stop()}}),e)})),getWebContainers:fe,getAMPContainers:fe},pe={getContainerByID:Object(r.createRegistrySelector)((function(e){return function(t,n,r){var a=e(f.g).getContainers(n);if(void 0!==a)return a.find((function(e){var t=e.publicId;return r===t}))||null}})),getWebContainers:Object(r.createRegistrySelector)((function(e){return function(t,n){var r=e(f.g).getContainers(n);if(Array.isArray(r))return r.filter((function(e){return e.usageContext.includes(f.d)}))}})),getAMPContainers:Object(r.createRegistrySelector)((function(e){return function(t,n){var r=e(f.g).getContainers(n);if(Array.isArray(r))return r.filter((function(e){return e.usageContext.includes(f.c)}))}})),getContainers:function(e,t){return e.containers[t]},isDoingGetContainers:Object(r.createRegistrySelector)((function(e){return function(t,n){return e(f.g).isFetchingGetContainers(n)}})),isDoingCreateContainer:function(e){return Object.values(e.isFetchingCreateContainer).some(Boolean)},getPrimaryContainerID:Object(r.createRegistrySelector)((function(e){return function(){var t=e(N.c).isPrimaryAMP();if(void 0!==t)return t?e(f.g).getAMPContainerID():e(f.g).getContainerID()}}))},me=Object(r.combineStores)(ue,le,{initialState:{containers:{}},actions:ge,controls:{},resolvers:de,selectors:pe}),be=(me.initialState,me.actions,me.controls,me.reducer,me.resolvers,me.selectors,me),ve=n(568),he=n(766),ye=Object(ve.a)({storeName:f.g,tagMatchers:he.a,isValidTag:$.d}),Oe=(ye.initialState,ye.actions,ye.controls,ye.reducer,ye.resolvers,ye.selectors,ye),je=n(67);function Se(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _e(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Se(Object(n),!0).forEach((function(t){m()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Se(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Ee,ke=Object(z.a)({baseName:"getLiveContainerVersion",argsToParams:function(e,t){return{accountID:e,internalContainerID:t}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.accountID,n=e.internalContainerID;_()(Object($.b)(t),"A valid accountID is required to fetch or receive a live container version."),_()(Object($.g)(n),"A valid internalContainerID is required to fetch or receive a live container version.")},controlCallback:(Ee=j()(y.a.mark((function e(t){var n,r;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.accountID,r=t.internalContainerID,e.prev=1,e.next=4,k.a.get("modules","tagmanager","live-container-version",{accountID:n,internalContainerID:r},{useCache:!1});case 4:return e.abrupt("return",e.sent);case 7:if(e.prev=7,e.t0=e.catch(1),404!==e.t0.code||!e.t0.message.includes("container version not found")){e.next=11;break}return e.abrupt("return",null);case 11:throw e.t0;case 12:case"end":return e.stop()}}),e,null,[[1,7]])}))),function(e){return Ee.apply(this,arguments)}),reducerCallback:function(e,t,n){var r=n.accountID,a=n.internalContainerID;return _e(_e({},e),{},{liveContainerVersions:_e(_e({},e.liveContainerVersions),{},m()({},"".concat(r,"::").concat(a),t))})}}),we={getLiveContainerVersion:y.a.mark((function e(t,n){var a;return y.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Object($.b)(t)&&Object($.g)(n)){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,r.commonActions.getRegistry();case 4:if(a=e.sent,void 0!==(0,a.select)(f.g).getLiveContainerVersion(t,n)){e.next=9;break}return e.next=9,ke.actions.fetchGetLiveContainerVersion(t,n);case 9:case"end":return e.stop()}}),e)}))},Ae={getLiveContainerGoogleTag:Object(r.createRegistrySelector)((function(e){return function(t,n,r){var a=e(f.g).getLiveContainerVersion(n,r);if(void 0!==a)return(null==a?void 0:a.tag)&&a.tag.find((function(e){return"googtag"===e.type}))||null}})),getLiveContainerGoogleTagID:Object(r.createRegistrySelector)((function(e){return function(t,n,r){var a=e(f.g).getLiveContainerGoogleTag(n,r);if(void 0!==a){if(null==a?void 0:a.parameter){var i,o,c=null===(i=a.parameter.find((function(e){return"tagId"===e.key})))||void 0===i?void 0:i.value;if(null===(o=c)||void 0===o?void 0:o.startsWith("{{")){var s;c=c.replace(/(\{\{|\}\})/g,"");var u=e(f.g).getLiveContainerVariable(n,r,c);c=null==u||null===(s=u.parameter.find((function(e){return"value"===e.key})))||void 0===s?void 0:s.value}if(Object(je.c)(c))return c}return null}}})),getCurrentGTMGoogleTagID:Object(r.createRegistrySelector)((function(e){return function(){var t=e(f.g).getAccountID();if(!Object($.b)(t))return null;var n=e(f.g).getInternalContainerID();return Object($.g)(n)?e(f.g).getLiveContainerGoogleTagID(t,n):null}})),getLiveContainerVariable:Object(r.createRegistrySelector)((function(e){return function(t,n,r,a){var i=e(f.g).getLiveContainerVersion(n,r);if(void 0!==i)return(null==i?void 0:i.variable)&&i.variable.find((function(e){return e.name===a}))||null}})),getLiveContainerVersion:function(e,t,n){return e.liveContainerVersions["".concat(t,"::").concat(n)]},isDoingGetLiveContainerVersion:Object(r.createRegistrySelector)((function(e){return function(t,n,r){return e(f.g).isFetchingGetLiveContainerVersion(n,r)}}))},Ce=Object(r.combineStores)(ke,{initialState:{liveContainerVersions:{}},resolvers:we,selectors:Ae}),De=(Ce.initialState,Ce.actions,Ce.controls,Ce.reducer,Ce.resolvers,Ce.selectors,Ce),Ne=n(157),xe=n(7),Te={selectors:{getServiceURL:Object(r.createRegistrySelector)((function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.path,a=n.query,i="https://tagmanager.google.com/";if(a&&(i=Object(Ne.a)(i,a)),r){var o="/".concat(r.replace(/^\//,""));i="".concat(i,"#").concat(o)}var c=e(xe.a).getAccountChooserURL(i);if(void 0!==c)return c}}))}},Ie=Object(r.combineStores)(K,re,be,Oe,De,Object(d.a)(f.g),I,Te);Ie.initialState,Ie.actions,Ie.controls,Ie.reducer,Ie.resolvers,Ie.selectors;a.a.registerStore(f.g,Ie),o.a.registerModule("tagmanager",{storeName:f.g,SettingsEditComponent:u.a,SettingsViewComponent:l.a,SetupComponent:s.a,Icon:g.a,features:[Object(c.__)("You will not be able to create tags without updating code","google-site-kit")]})},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var r="core/site",a="primary",i="secondary"},141:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return StoreErrorNotices}));var r=n(1),a=n.n(r),i=n(3),o=n(120),c=n(19),s=n(35),u=n(169);function StoreErrorNotices(t){var n=t.hasButton,r=void 0!==n&&n,a=t.moduleSlug,l=t.storeName,g=Object(i.useSelect)((function(e){return e(l).getErrors()})),f=Object(i.useSelect)((function(e){return e(c.a).getModule(a)})),d=[];return g.filter((function(e){return!(!(null==e?void 0:e.message)||d.includes(e.message))&&(d.push(e.message),!0)})).map((function(t,n){var a=t.message;return Object(s.e)(t)&&(a=Object(u.a)(a,f)),e.createElement(o.a,{key:n,error:t,hasButton:r,storeName:l,message:a})}))}StoreErrorNotices.propTypes={hasButton:a.a.bool,storeName:a.a.string.isRequired,moduleSlug:a.a.string}}).call(this,n(4))},142:function(e,t,n){"use strict";var r=n(166);n.d(t,"c",(function(){return r.a}));var a=n(65);n.d(t,"b",(function(){return a.c})),n.d(t,"a",(function(){return a.a}))},166:function(e,t,n){"use strict";(function(e){var r=n(11),a=n.n(r),i=n(1),o=n.n(i),c=n(2),s=n(3),u=n(201),l=n(210),g=n(65),f=n(7),d=n(10),p=n(0),m=Object(p.forwardRef)((function(t,n){var r=t.className,i=t.children,o=t.type,p=t.dismiss,m=void 0===p?"":p,b=t.dismissCallback,v=t.dismissLabel,h=void 0===v?Object(c.__)("OK, Got it!","google-site-kit"):v,y=t.Icon,O=void 0===y?Object(g.d)(o):y,j=t.OuterCTA,S=Object(s.useDispatch)(f.a).dismissItem,_=Object(s.useSelect)((function(e){return m?e(f.a).isItemDismissed(m):void 0}));if(m&&_)return null;var E=i?l.a:u.a;return e.createElement("div",{ref:n,className:a()(r,"googlesitekit-settings-notice","googlesitekit-settings-notice--".concat(o),{"googlesitekit-settings-notice--single-row":!i,"googlesitekit-settings-notice--multi-row":i})},e.createElement("div",{className:"googlesitekit-settings-notice__icon"},e.createElement(O,{width:"20",height:"20"})),e.createElement("div",{className:"googlesitekit-settings-notice__body"},e.createElement(E,t)),m&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(d.Button,{tertiary:!0,onClick:function(){"string"==typeof m&&S(m),null==b||b()}},h)),j&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(j,null)))}));m.propTypes={className:o.a.string,children:o.a.node,notice:o.a.node.isRequired,type:o.a.oneOf([g.a,g.c,g.b]),Icon:o.a.elementType,LearnMore:o.a.elementType,CTA:o.a.elementType,OuterCTA:o.a.elementType,dismiss:o.a.string,dismissLabel:o.a.string,dismissCallback:o.a.func},m.defaultProps={type:g.a},t.a=m}).call(this,n(4))},169:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(2);function a(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},a=n.slug,i=void 0===a?"":a,o=n.name,c=void 0===o?"":o,s=n.owner,u=void 0===s?{}:s;if(!i||!c)return e;var l="",g="";return"analytics-4"===i?e.match(/account/i)?l=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?l=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(l=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):"search-console"===i&&(l=Object(r.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),l||(l=Object(r.sprintf)(
/* translators: %s: module name */
Object(r.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),c)),u&&u.login&&(g=Object(r.sprintf)(
/* translators: %s: owner name */
Object(r.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),u.login)),g||(g=Object(r.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(l," ").concat(g)}},178:function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"d",(function(){return s}));var r=n(33),a=n.n(r);function i(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return!0};return"string"==typeof e?n(e):!("object"!==a()(e)||!t(e))||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e?n(e):"object"===a()(e)&&t(e)}))}function o(e){var t=e.startDate,n=e.endDate,r=t&&t.match(/^\d{4}-\d{2}-\d{2}$/),a=n&&n.match(/^\d{4}-\d{2}-\d{2}$/);return r&&a}function c(e){var t=function(e){var t=e.hasOwnProperty("fieldName")&&!!e.fieldName,n=e.hasOwnProperty("sortOrder")&&/(ASCENDING|DESCENDING)/i.test(e.sortOrder.toString());return t&&n};return Array.isArray(e)?e.every((function(e){return"object"===a()(e)&&t(e)})):"object"===a()(e)&&t(e)}function s(e){return"string"==typeof e||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e}))}},18:function(e,t,n){"use strict";var r=n(0),a=n(61);t.a=function(){return Object(r.useContext)(a.b)}},182:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(1),a=n.n(r),i=" ";function DisplaySetting(e){return e.value||i}DisplaySetting.propTypes={value:a.a.oneOfType([a.a.string,a.a.bool,a.a.number])},t.b=DisplaySetting},186:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M0 0h2v7H0zm0 10h2v2H0z",fill:"currentColor",fillRule:"evenodd"});t.a=function SvgWarningIcon(e){return r.createElement("svg",a({viewBox:"0 0 2 12"},e),i)}},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r="core/modules",a="insufficient_module_dependencies"},190:function(e,t){e.exports=googlesitekit.modules},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(11),s=n.n(c),u=n(1),l=n.n(u),g=n(146),f=n(0),d=n(2),p=n(126),m=n(127),b=n(128),v=n(70),h=n(76),y=Object(f.forwardRef)((function(t,n){var r,i=t["aria-label"],c=t.secondary,u=void 0!==c&&c,l=t.arrow,f=void 0!==l&&l,y=t.back,O=void 0!==y&&y,j=t.caps,S=void 0!==j&&j,_=t.children,E=t.className,k=void 0===E?"":E,w=t.danger,A=void 0!==w&&w,C=t.disabled,D=void 0!==C&&C,N=t.external,x=void 0!==N&&N,T=t.hideExternalIndicator,I=void 0!==T&&T,P=t.href,R=void 0===P?"":P,M=t.inverse,L=void 0!==M&&M,F=t.noFlex,G=void 0!==F&&F,U=t.onClick,V=t.small,q=void 0!==V&&V,B=t.standalone,H=void 0!==B&&B,W=t.linkButton,K=void 0!==W&&W,$=t.to,z=t.leadingIcon,X=t.trailingIcon,Y=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),Z=R||$||!U?$?"ROUTER_LINK":x?"EXTERNAL_LINK":"LINK":D?"BUTTON_DISABLED":"BUTTON",J="BUTTON"===Z||"BUTTON_DISABLED"===Z?"button":"ROUTER_LINK"===Z?g.b:"a",Q=("EXTERNAL_LINK"===Z&&(r=Object(d._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===Z&&(r=Object(d._x)("(disabled)","screen reader text","google-site-kit")),r?i?"".concat(i," ").concat(r):"string"==typeof _?"".concat(_," ").concat(r):void 0:i),ee=z,te=X;return O&&(ee=e.createElement(b.a,{width:14,height:14})),x&&!I&&(te=e.createElement(v.a,{width:14,height:14})),f&&!L&&(te=e.createElement(p.a,{width:14,height:14})),f&&L&&(te=e.createElement(m.a,{width:14,height:14})),e.createElement(J,a()({"aria-label":Q,className:s()("googlesitekit-cta-link",k,{"googlesitekit-cta-link--secondary":u,"googlesitekit-cta-link--inverse":L,"googlesitekit-cta-link--small":q,"googlesitekit-cta-link--caps":S,"googlesitekit-cta-link--danger":A,"googlesitekit-cta-link--disabled":D,"googlesitekit-cta-link--standalone":H,"googlesitekit-cta-link--link-button":K,"googlesitekit-cta-link--no-flex":!!G}),disabled:D,href:"LINK"!==Z&&"EXTERNAL_LINK"!==Z||D?void 0:R,onClick:U,rel:"EXTERNAL_LINK"===Z?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===Z?"_blank":void 0,to:$},Y),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},_),!!te&&e.createElement(h.a,{marginLeft:5},te))}));y.propTypes={arrow:l.a.bool,back:l.a.bool,caps:l.a.bool,children:l.a.node,className:l.a.string,danger:l.a.bool,disabled:l.a.bool,external:l.a.bool,hideExternalIndicator:l.a.bool,href:l.a.string,inverse:l.a.bool,leadingIcon:l.a.node,linkButton:l.a.bool,noFlex:l.a.bool,onClick:l.a.func,small:l.a.bool,standalone:l.a.bool,to:l.a.string,trailingIcon:l.a.node},t.a=y}).call(this,n(4))},200:function(e,t,n){"use strict";n.d(t,"a",(function(){return _})),n.d(t,"b",(function(){return E})),n.d(t,"c",(function(){return k})),n.d(t,"g",(function(){return w})),n.d(t,"f",(function(){return A})),n.d(t,"d",(function(){return C})),n.d(t,"e",(function(){return D}));var r=n(16),a=n.n(r),i=n(5),o=n.n(i),c=n(6),s=n.n(c),u=n(12),l=n.n(u),g=n(14),f=n(45),d=n.n(f),p=n(3),m=n(62),b=n(82),v=n(48),h=n(64);function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var j=h.a.clearError,S=h.a.receiveError,_="cannot submit changes while submitting changes",E="cannot submit changes if settings have not changed",k=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=r.ownedSettingsSlugs,i=void 0===a?void 0:a,c=r.storeName,u=void 0===c?void 0:c,f=r.settingSlugs,h=void 0===f?[]:f,y=r.initialSettings,_=void 0===y?void 0:y,E=r.validateHaveSettingsChanged,k=void 0===E?D():E;l()(e,"type is required."),l()(t,"identifier is required."),l()(n,"datapoint is required.");var w=u||"".concat(e,"/").concat(t),A={ownedSettingsSlugs:i,settings:_,savedSettings:void 0},C=Object(v.a)({baseName:"getSettings",controlCallback:function(){return d.a.get(e,t,n,{},{useCache:!1})},reducerCallback:function(e,t){return O(O({},e),{},{savedSettings:O({},t),settings:O(O({},t),e.settings||{})})}}),N=Object(v.a)({baseName:"saveSettings",controlCallback:function(r){var a=r.values;return d.a.set(e,t,n,a)},reducerCallback:function(e,t){return O(O({},e),{},{savedSettings:O({},t),settings:O({},t)})},argsToParams:function(e){return{values:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.values;l()(Object(g.isPlainObject)(t),"values is required.")}}),x={},T={setSettings:function(e){return l()(Object(g.isPlainObject)(e),"values is required."),{payload:{values:e},type:"SET_SETTINGS"}},rollbackSettings:function(){return{payload:{},type:"ROLLBACK_SETTINGS"}},rollbackSetting:function(e){return l()(e,"setting is required."),{payload:{setting:e},type:"ROLLBACK_SETTING"}},saveSettings:o.a.mark((function e(){var t,n,r,a,i;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,j("saveSettings",[]);case 5:return n=t.select(w).getSettings(),e.next=8,N.actions.fetchSaveSettings(n);case 8:if(r=e.sent,a=r.response,!(i=r.error)){e.next=14;break}return e.next=14,S(i,"saveSettings",[]);case 14:return e.abrupt("return",{response:a,error:i});case 15:case"end":return e.stop()}}),e)}))},I={},P=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:A,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.payload;switch(n){case"SET_SETTINGS":var a=r.values;return O(O({},e),{},{settings:O(O({},e.settings||{}),a)});case"ROLLBACK_SETTINGS":return O(O({},e),{},{settings:e.savedSettings});case"ROLLBACK_SETTING":var i=r.setting;return e.savedSettings[i]?O(O({},e),{},{settings:O(O({},e.settings||{}),{},s()({},i,e.savedSettings[i]))}):O({},e);default:return void 0!==x[n]?x[n](e,{type:n,payload:r}):e}},R={getSettings:o.a.mark((function e(){var t;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.commonActions.getRegistry();case 2:if(t=e.sent,t.select(w).getSettings()){e.next=7;break}return e.next=7,C.actions.fetchGetSettings();case 7:case"end":return e.stop()}}),e)}))},M=Object(m.g)(k),L=M.safeSelector,F=M.dangerousSelector,G={haveSettingsChanged:L,__dangerousHaveSettingsChanged:F,getSettings:function(e){return e.settings},hasSettingChanged:function(e,t){l()(t,"setting is required.");var n=e.settings,r=e.savedSettings;return!(!n||!r)&&!Object(g.isEqual)(n[t],r[t])},isDoingSaveSettings:function(e){return Object.values(e.isFetchingSaveSettings).some(Boolean)},getOwnedSettingsSlugs:function(e){return e.ownedSettingsSlugs},haveOwnedSettingsChanged:Object(p.createRegistrySelector)((function(e){return function(){var t=e(w).getOwnedSettingsSlugs();return e(w).haveSettingsChanged(t)}}))};h.forEach((function(e){var t=Object(b.b)(e),n=Object(b.a)(e);T["set".concat(t)]=function(e){return l()(void 0!==e,"value is required for calls to set".concat(t,"().")),{payload:{value:e},type:"SET_".concat(n)}},x["SET_".concat(n)]=function(t,n){var r=n.payload.value;return O(O({},t),{},{settings:O(O({},t.settings||{}),{},s()({},e,r))})},G["get".concat(t)]=Object(p.createRegistrySelector)((function(t){return function(){return(t(w).getSettings()||{})[e]}}))}));var U=Object(p.combineStores)(p.commonStore,C,N,{initialState:A,actions:T,controls:I,reducer:P,resolvers:R,selectors:G});return O(O({},U),{},{STORE_NAME:w})};function w(e,t){return function(){var n=a()(o.a.mark((function n(r){var a,i,c,s;return o.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(a=r.select,i=r.dispatch,!a(t).haveSettingsChanged()){n.next=8;break}return n.next=4,i(t).saveSettings();case 4:if(c=n.sent,!(s=c.error)){n.next=8;break}return n.abrupt("return",{error:s});case 8:return n.next=10,d.a.invalidateCache("modules",e);case 10:return n.abrupt("return",{});case 11:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}function A(e){return function(t){var n=t.select,r=t.dispatch;return n(e).haveSettingsChanged()?r(e).rollbackSettings():{}}}function C(e){return function(t){var n=Object(m.e)(t)(e),r=n.haveSettingsChanged,a=n.isDoingSubmitChanges;l()(!a(),_),l()(r(),E)}}function D(){return function(e,t,n){var r=t.settings,a=t.savedSettings;n&&l()(!Object(g.isEqual)(Object(g.pick)(r,n),Object(g.pick)(a,n)),E),l()(!Object(g.isEqual)(r,a),E)}}},201:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeSingleRow}));var r=n(1),a=n.n(r),i=n(0);function SettingsNoticeSingleRow(t){var n=t.notice,r=t.LearnMore,a=t.CTA;return e.createElement(i.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),a&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(a,null)))}SettingsNoticeSingleRow.propTypes={notice:a.a.node.isRequired,LearnMore:a.a.elementType,CTA:a.a.elementType}}).call(this,n(4))},210:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeMultiRow}));var r=n(1),a=n.n(r),i=n(0);function SettingsNoticeMultiRow(t){var n=t.notice,r=t.LearnMore,a=t.CTA,o=t.children;return e.createElement(i.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),e.createElement("div",{className:"googlesitekit-settings-notice__inner-row"},e.createElement("div",{className:"googlesitekit-settings-notice__children-container"},o),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),a&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(a,null))))}SettingsNoticeMultiRow.propTypes={children:a.a.node.isRequired,notice:a.a.node.isRequired,LearnMore:a.a.elementType,CTA:a.a.elementType}}).call(this,n(4))},271:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"d",(function(){return s})),n.d(t,"e",(function(){return u})),n.d(t,"a",(function(){return l})),n.d(t,"f",(function(){return g})),n.d(t,"g",(function(){return f})),n.d(t,"h",(function(){return d}));var r=n(9),a=n(46),i=n(623);function o(e){return Object(r.x)(e)}function c(e){return e===a.a||o(e)}function s(e){return"string"==typeof e&&/^GTM-[A-Z0-9]+$/.test(e)}function u(e){return"string"==typeof e&&Object(i.a)(e).length>0}function l(e,t){var n=Object(i.a)(e);return!Array.isArray(t)||!t.some((function(e){var t=e.name;return Object(i.a)(t)===n}))}function g(e){return e===a.b||s(e)}function f(e){return Object(r.x)(e)}function d(e){return[a.d,a.c].includes(e)}},273:function(e,t,n){"use strict";(function(e){var r=n(55),a=n.n(r),i=n(274),o=e._googlesitekitAPIFetchData||{},c=o.nonce,s=o.nonceEndpoint,u=o.preloadedData,l=o.rootURL;a.a.nonceEndpoint=s,a.a.nonceMiddleware=a.a.createNonceMiddleware(c),a.a.rootURLMiddleware=a.a.createRootURLMiddleware(l),a.a.preloadingMiddleware=Object(i.a)(u),a.a.use(a.a.nonceMiddleware),a.a.use(a.a.mediaUploadMiddleware),a.a.use(a.a.rootURLMiddleware),a.a.use(a.a.preloadingMiddleware),t.default=a.a}).call(this,n(28))},274:function(e,t,n){"use strict";var r=n(262);t.a=function(e){var t=Object.keys(e).reduce((function(t,n){return t[Object(r.getStablePath)(n)]=e[n],t}),{}),n=!1;return function(e,a){if(n)return a(e);setTimeout((function(){n=!0}),3e3);var i=e.parse,o=void 0===i||i,c=e.path;if("string"==typeof e.path){var s,u=(null===(s=e.method)||void 0===s?void 0:s.toUpperCase())||"GET",l=Object(r.getStablePath)(c);if(o&&"GET"===u&&t[l]){var g=Promise.resolve(t[l].body);return delete t[l],g}if("OPTIONS"===u&&t[u]&&t[u][l]){var f=Promise.resolve(t[u][l]);return delete t[u][l],f}}return a(e)}}},275:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var r=n(6),a=n.n(r),i=n(25),o=n.n(i),c=n(63),s=n.n(c),u=n(14);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f=s()((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.metrics,n=e.dimensions,r=o()(e,["metrics","dimensions"]);return g({metrics:d(t),dimensions:p(n)},r)})),d=function(e){return Object(u.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(u.isPlainObject)(e)}))},p=function(e){return Object(u.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(u.isPlainObject)(e)}))}},29:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/forms"},3:function(e,t){e.exports=googlesitekit.data},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},342:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(14),a=n(122);function i(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(r.isPlainObject)(e)&&(!(!e.hasOwnProperty("fieldNames")||!Array.isArray(e.fieldNames)||0===e.fieldNames.length)&&(!(!e.hasOwnProperty("limit")||"number"!=typeof e.limit)&&!(e.hasOwnProperty("orderby")&&!Object(a.e)(e.orderby))))}))}},35:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"c",(function(){return u})),n.d(t,"d",(function(){return l})),n.d(t,"b",(function(){return g}));n(14);var r=n(2),a="missing_required_scopes",i="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===a}function s(e){var t;return[i,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function u(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function l(e,t){return!(!(null==t?void 0:t.storeName)||s(e)||c(e)||u(e))}function g(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(r.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(r.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},355:function(e,t,n){"use strict";var r=n(935);n.d(t,"b",(function(){return r.a}));var a=n(936);n.d(t,"c",(function(){return a.a}));n(765);var i=n(937);n.d(t,"a",(function(){return i.a}));var o=n(938);n.d(t,"d",(function(){return o.a}));n(614),n(615);var c=n(939);n.d(t,"e",(function(){return c.a}));var s=n(940);n.d(t,"f",(function(){return s.a}));n(616),n(767);var u=n(941);n.d(t,"g",(function(){return u.a}))},357:function(e,t,n){"use strict";n.d(t,"a",(function(){return r.a})),n.d(t,"c",(function(){return a.b})),n.d(t,"d",(function(){return a.d})),n.d(t,"e",(function(){return a.e})),n.d(t,"b",(function(){return a.a})),n.d(t,"f",(function(){return a.f})),n.d(t,"g",(function(){return a.g}));var r=n(623),a=n(271);n(766)},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return O})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return y}));var r=n(99),a=e._googlesitekitTrackingData||{},i=a.activeModules,o=void 0===i?[]:i,c=a.isSiteKitScreen,s=a.trackingEnabled,u=a.trackingID,l=a.referenceSiteURL,g=a.userIDHash,f=a.isAuthenticated,d={activeModules:o,trackingEnabled:s,trackingID:u,referenceSiteURL:l,userIDHash:g,isSiteKitScreen:c,userRoles:a.userRoles,isAuthenticated:f,pluginVersion:"1.151.0"},p=Object(r.a)(d),m=p.enableTracking,b=p.disableTracking,v=(p.isTrackingEnabled,p.initializeSnippet),h=p.trackEvent,y=p.trackEventOnce;function O(e){e?m():b()}c&&s&&v()}).call(this,n(28))},364:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M0 19h22L11 0 0 19zm12-3h-2v-2h2v2zm0-4h-2V8h2v4z",fill:"currentColor"});t.a=function SvgWarningV2(e){return r.createElement("svg",a({viewBox:"0 0 22 19"},e),i)}},369:function(e,t,n){"use strict";n.d(t,"a",(function(){return p})),n.d(t,"b",(function(){return m}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(12),s=n.n(c),u=n(14),l=n(273),g=n(106),f=n(157),d=n(13),p=function(e,t){var n=t.find((function(t){return t.test(e)}));return!!n&&n.exec(e)[1]},m=Object(u.memoize)(function(){var e=o()(a.a.mark((function e(t){var n,r,i,o;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.homeURL,r=t.ampMode,s()(Object(g.a)(n),"homeURL must be valid URL"),i=[n],d.b!==r){e.next=14;break}return e.prev=4,e.next=7,Object(l.default)({path:"/wp/v2/posts?per_page=1"}).then((function(e){return e.slice(0,1).map((function(e){return Object(f.a)(e.link,{amp:1})})).pop()}));case 7:(o=e.sent)&&i.push(o),e.next=14;break;case 11:return e.prev=11,e.t0=e.catch(4),e.abrupt("return",i);case 14:return e.abrupt("return",i);case 15:case"end":return e.stop()}}),e,null,[[4,11]])})));return function(t){return e.apply(this,arguments)}}())},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return y})),n.d(t,"c",(function(){return O})),n.d(t,"e",(function(){return j})),n.d(t,"b",(function(){return S}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return u(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return u(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var l,g="googlesitekit_",f="".concat(g).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),d=["sessionStorage","localStorage"],p=[].concat(d),m=function(){var t=o()(a.a.mark((function t(n){var r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,i="__storage_test__",r.setItem(i,i),r.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function b(){return v.apply(this,arguments)}function v(){return(v=o()(a.a.mark((function t(){var n,r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===l){t.next=2;break}return t.abrupt("return",l);case 2:n=s(p),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(i=r.value,!l){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,m(i);case 11:if(!t.sent){t.next=13;break}l=e[i];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===l&&(l=null),t.abrupt("return",l);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=o()(a.a.mark((function e(t){var n,r,i,o,c,s,u;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(f).concat(t)))){e.next=10;break}if(i=JSON.parse(r),o=i.timestamp,c=i.ttl,s=i.value,u=i.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:u});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),y=function(){var t=o()(a.a.mark((function t(n,r){var i,o,s,u,l,g,d,p,m=arguments;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=m.length>2&&void 0!==m[2]?m[2]:{},o=i.ttl,s=void 0===o?c.b:o,u=i.timestamp,l=void 0===u?Math.round(Date.now()/1e3):u,g=i.isError,d=void 0!==g&&g,t.next=3,b();case 3:if(!(p=t.sent)){t.next=14;break}return t.prev=5,p.setItem("".concat(f).concat(n),JSON.stringify({timestamp:l,ttl:s,value:r,isError:d})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),O=function(){var t=o()(a.a.mark((function t(n){var r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,i=n.startsWith(g)?n:"".concat(f).concat(n),r.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),j=function(){var t=o()(a.a.mark((function t(){var n,r,i,o;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],i=0;i<n.length;i++)0===(o=n.key(i)).indexOf(g)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),S=function(){var e=o()(a.a.mark((function e(){var t,n,r,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!e.sent){e.next=25;break}return e.next=6,j();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return i=r.value,e.next=14,O(i);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r="_googlesitekitDataLayer",a="data-googlesitekit-gtag"},426:function(e,t,n){"use strict";function r(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return e.reduce((function(e,t,r){return e+t+encodeURIComponent(n[r]||"")}),"")}n.d(t,"a",(function(){return r}))},45:function(e,t){e.exports=googlesitekit.api},46:function(e,t,n){"use strict";n.d(t,"g",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"f",(function(){return s})),n.d(t,"h",(function(){return u})),n.d(t,"e",(function(){return l})),n.d(t,"i",(function(){return g}));var r="modules/tagmanager",a="account_create",i="container_create",o="web",c="amp",s="tagmanagerSetup",u="https://www.googleapis.com/auth/tagmanager.readonly",l="https://www.googleapis.com/auth/tagmanager.edit.containers",g="SETUP_WITH_ANALYTICS"},48:function(e,t,n){"use strict";n.d(t,"a",(function(){return O}));var r=n(5),a=n.n(r),i=n(6),o=n.n(i),c=n(12),s=n.n(c),u=n(14),l=n(64),g=n(82),f=n(9);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var m=function(e){return e},b=function(){return{}},v=function(){},h=l.a.clearError,y=l.a.receiveError,O=function(e){var t,n,r=a.a.mark(R),i=e.baseName,c=e.controlCallback,l=e.reducerCallback,d=void 0===l?m:l,O=e.argsToParams,j=void 0===O?b:O,S=e.validateParams,_=void 0===S?v:S;s()(i,"baseName is required."),s()("function"==typeof c,"controlCallback is required and must be a function."),s()("function"==typeof d,"reducerCallback must be a function."),s()("function"==typeof j,"argsToParams must be a function."),s()("function"==typeof _,"validateParams must be a function.");try{_(j()),n=!1}catch(e){n=!0}var E=Object(g.b)(i),k=Object(g.a)(i),w="FETCH_".concat(k),A="START_".concat(w),C="FINISH_".concat(w),D="CATCH_".concat(w),N="RECEIVE_".concat(k),x="fetch".concat(E),T="receive".concat(E),I="isFetching".concat(E),P=o()({},I,{});function R(e,t){var n,o;return a.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,{payload:{params:e},type:A};case 2:return r.next=4,h(i,t);case 4:return r.prev=4,r.next=7,{payload:{params:e},type:w};case 7:return n=r.sent,r.next=10,M[T](n,e);case 10:return r.next=12,{payload:{params:e},type:C};case 12:r.next=21;break;case 14:return r.prev=14,r.t0=r.catch(4),o=r.t0,r.next=19,y(o,i,t);case 19:return r.next=21,{payload:{params:e},type:D};case 21:return r.abrupt("return",{response:n,error:o});case 22:case"end":return r.stop()}}),r,null,[[4,14]])}var M=(t={},o()(t,x,(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=j.apply(void 0,t);return _(r),R(r,t)})),o()(t,T,(function(e,t){return s()(void 0!==e,"response is required."),n?(s()(Object(u.isPlainObject)(t),"params is required."),_(t)):t={},{payload:{response:e,params:t},type:N}})),t),L=o()({},w,(function(e){var t=e.payload;return c(t.params)})),F=o()({},I,(function(e){if(void 0===e[I])return!1;var t;try{for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];t=j.apply(void 0,r),_(t)}catch(e){return!1}return!!e[I][Object(f.H)(t)]}));return{initialState:P,actions:M,controls:L,reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case A:var a=r.params;return p(p({},e),{},o()({},I,p(p({},e[I]),{},o()({},Object(f.H)(a),!0))));case N:var i=r.response,c=r.params;return d(e,i,c);case C:var s=r.params;return p(p({},e),{},o()({},I,p(p({},e[I]),{},o()({},Object(f.H)(s),!1))));case D:var u=r.params;return p(p({},e),{},o()({},I,p(p({},e[I]),{},o()({},Object(f.H)(u),!1))));default:return e}},resolvers:{},selectors:F}}},506:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccessibleWarningIcon}));var r=n(1),a=n.n(r),i=n(0),o=n(2),c=n(105),s=n(364);function AccessibleWarningIcon(t){var n=t.height,r=void 0===n?12:n,a=t.screenReaderText,u=void 0===a?Object(o.__)("Error","google-site-kit"):a,l=t.width,g=void 0===l?14:l;return e.createElement(i.Fragment,null,e.createElement(c.a,null,u),e.createElement(s.a,{width:g,height:r}))}AccessibleWarningIcon.propTypes={height:a.a.number,screenReaderText:a.a.string,width:a.a.number}}).call(this,n(4))},54:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,r=t.reconnectURL,a=t.noPrefix;if(!n)return null;var s=n;void 0!==a&&a||(s=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),r&&Object(i.a)(r)&&(s=s+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),r));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(s,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:a.a.string.isRequired,reconnectURL:a.a.string,noPrefix:a.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},568:function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var r=n(6),a=n.n(r),i=n(5),o=n.n(i),c=n(16),s=n.n(c),u=n(12),l=n.n(u),g=n(3),f=n(13),d=n(369);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function b(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return v(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return v(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.storeName,n=e.isValidTag,r=e.tagMatchers;l()("string"==typeof t&&t,"storeName is required."),l()("function"==typeof n,"isValidTag must be a function."),l()(Array.isArray(r),"tagMatchers must be an Array.");var i={existingTag:void 0},c={fetchGetExistingTag:function(){return{payload:{},type:"FETCH_GET_EXISTING_TAG"}},receiveGetExistingTag:function(e){return l()(null===e||"string"==typeof e,"existingTag must be a tag string or null."),{payload:{existingTag:n(e)?e:null},type:"RECEIVE_GET_EXISTING_TAG"}}},u=a()({},"FETCH_GET_EXISTING_TAG",Object(g.createRegistryControl)((function(e){return s()(o.a.mark((function t(){var n,a,i,c,s,u,l,g,p,m;return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.select(f.c).getHomeURL(),a=e.select(f.c).getAMPMode(),t.next=4,Object(d.b)({homeURL:n,ampMode:a});case 4:i=t.sent,c=e.resolveSelect(f.c),s=c.getHTMLForURL,u=b(i),t.prev=7,u.s();case 9:if((l=u.n()).done){t.next=19;break}return g=l.value,t.next=13,s(g);case 13:if(p=t.sent,!(m=Object(d.a)(p,r))){t.next=17;break}return t.abrupt("return",m);case 17:t.next=9;break;case 19:t.next=24;break;case 21:t.prev=21,t.t0=t.catch(7),u.e(t.t0);case 24:return t.prev=24,u.f(),t.finish(24);case 27:return t.abrupt("return",null);case 28:case"end":return t.stop()}}),t,null,[[7,21,24,27]])})))}))),p=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.payload;switch(n){case"RECEIVE_GET_EXISTING_TAG":var a=r.existingTag;return m(m({},e),{},{existingTag:a});default:return e}},v={getExistingTag:o.a.mark((function e(){var n,r;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,g.commonActions.getRegistry();case 2:if(void 0!==(n=e.sent).select(t).getExistingTag()){e.next=8;break}return e.next=6,c.fetchGetExistingTag();case 6:r=e.sent,n.dispatch(t).receiveGetExistingTag(r);case 8:case"end":return e.stop()}}),e)}))},h={getExistingTag:function(e){return e.existingTag},hasExistingTag:Object(g.createRegistrySelector)((function(e){return function(){var n=e(t).getExistingTag();if(void 0!==n)return!!n}}))},y={initialState:i,actions:c,controls:u,reducer:p,resolvers:v,selectors:h};return m(m({},y),{},{STORE_NAME:t})}},57:function(e,t,n){"use strict";(function(e){var r,a;n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var i=new Set((null===(r=e)||void 0===r||null===(a=r._googlesitekitBaseData)||void 0===a?void 0:a.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;return t instanceof Set&&t.has(e)}}).call(this,n(28))},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(39);function a(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(0),a=Object(r.createContext)(""),i=(a.Consumer,a.Provider);t.b=a},614:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ContainerNameTextField}));var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(11),s=n.n(c),u=n(0),l=n(2),g=n(3),f=n(46),d=n(29),p=n(506),m=n(10),b=n(357);function ContainerNameTextField(t){var n=t.label,r=t.name,i=Object(g.useSelect)((function(e){var t=e(f.g).getAccountID();return e(f.g).getContainers(t)})),o=Object(g.useSelect)((function(e){return e(d.a).getValue(f.f,r)})),c=Object(g.useDispatch)(d.a).setValues,v=Object(u.useCallback)((function(e){var t=e.currentTarget;c(f.f,a()({},r,t.value))}),[r,c]),h=Object(b.b)(o,i),y=!(!o||h)&&Object(l.__)("A container with this name already exists","google-site-kit"),O=!(!o||h)&&e.createElement("span",{className:"googlesitekit-text-field-icon--error"},e.createElement(p.a,null));return e.createElement("div",{className:s()("googlesitekit-tagmanager-containername","googlesitekit-tagmanager-".concat(r))},e.createElement(m.TextField,{className:s()({"mdc-text-field--error":!o||!h}),label:n,outlined:!0,helperText:y,trailingIcon:O,id:r,name:r,value:o,onChange:v}))}ContainerNameTextField.propTypes={label:o.a.string.isRequired,name:o.a.string.isRequired}}).call(this,n(4))},615:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ContainerSelect}));var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(11),s=n.n(c),u=n(1),l=n.n(u),g=n(2),f=n(10),d=n(3),p=n(46),m=n(357);function ContainerSelect(t){var n=t.containers,r=t.className,i=t.value,c=o()(t,["containers","className","value"]),u=Object(d.useSelect)((function(e){return e(p.g).getAccountID()})),l=Object(d.useSelect)((function(e){return e(p.g).hasFinishedResolution("getAccounts")})),b=Object(d.useSelect)((function(e){return e(p.g).hasFinishedResolution("getContainers",[u])}));return l&&b?e.createElement(f.Select,a()({className:s()("googlesitekit-tagmanager__select-container",r),disabled:!Object(m.c)(u),value:i,enhanced:!0,outlined:!0},c),(n||[]).concat({publicId:p.b,name:Object(g.__)("Set up a new container","google-site-kit")}).map((function(t){var n=t.publicId,r=t.name,a=t.containerId;return e.createElement(f.Option,{key:n,value:n,"data-internal-id":a},n===p.b?r:Object(g.sprintf)(
/* translators: 1: container name, 2: container ID */
Object(g._x)("%1$s (%2$s)","Tag Manager container name and ID","google-site-kit"),r,n))}))):e.createElement(f.ProgressBar,{small:!0})}ContainerSelect.propTypes={containers:l.a.arrayOf(l.a.object)}}).call(this,n(4))},616:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UseSnippetSwitch}));var r=n(1),a=n.n(r),i=n(0),o=n(2),c=n(3),s=n(10),u=n(46),l=n(9),g=n(18);function UseSnippetSwitch(t){var n=t.description,r=Object(c.useSelect)((function(e){return e(u.g).getUseSnippet()})),a=Object(g.a)(),f=Object(c.useDispatch)(u.g).setUseSnippet,d=Object(i.useCallback)((function(){var e=!r;f(e),Object(l.I)("".concat(a,"_tagmanager"),e?"enable_tag":"disable_tag")}),[f,r,a]);return void 0===r?null:e.createElement("div",{className:"googlesitekit-tagmanager-usesnippet"},e.createElement(s.Switch,{label:Object(o.__)("Let Site Kit place code on your site","google-site-kit"),checked:r,onClick:d,hideLabel:!1}),n)}UseSnippetSwitch.propTypes={description:a.a.node}}).call(this,n(4))},62:function(e,t,n){"use strict";n.d(t,"a",(function(){return w})),n.d(t,"b",(function(){return A})),n.d(t,"c",(function(){return C})),n.d(t,"d",(function(){return N})),n.d(t,"e",(function(){return x})),n.d(t,"g",(function(){return I})),n.d(t,"f",(function(){return P}));var r,a=n(5),i=n.n(a),o=n(27),c=n.n(o),s=n(6),u=n.n(s),l=n(12),g=n.n(l),f=n(63),d=n.n(f),p=n(14),m=n(116);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){u()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.reduce((function(e,t){return v(v({},e),t)}),{}),a=t.reduce((function(e,t){return[].concat(c()(e),c()(Object.keys(t)))}),[]),i=D(a);return g()(0===i.length,"collect() cannot accept collections with duplicate keys. Your call to collect() contains the following duplicated functions: ".concat(i.join(", "),". Check your data stores for duplicates.")),r},y=h,O=h,j=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,a=[].concat(t);return"function"!=typeof a[0]&&(r=a.shift()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return a.reduce((function(e,n){return n(e,t)}),e)}},S=h,_=h,E=h,k=function(e){return e},w=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=E.apply(void 0,c()(t.map((function(e){return e.initialState||{}}))));return{initialState:r,controls:O.apply(void 0,c()(t.map((function(e){return e.controls||{}})))),actions:y.apply(void 0,c()(t.map((function(e){return e.actions||{}})))),reducer:j.apply(void 0,[r].concat(c()(t.map((function(e){return e.reducer||k}))))),resolvers:S.apply(void 0,c()(t.map((function(e){return e.resolvers||{}})))),selectors:_.apply(void 0,c()(t.map((function(e){return e.selectors||{}}))))}},A={getRegistry:function(){return{payload:{},type:"GET_REGISTRY"}},await:i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{payload:{value:t},type:"AWAIT"});case 1:case"end":return e.stop()}}),e)}))},C=(r={},u()(r,"GET_REGISTRY",Object(m.a)((function(e){return function(){return e}}))),u()(r,"AWAIT",(function(e){return e.payload.value})),r),D=function(e){for(var t=[],n={},r=0;r<e.length;r++){var a=e[r];n[a]=n[a]>=1?n[a]+1:1,n[a]>1&&t.push(a)}return t},N={actions:A,controls:C,reducer:k},x=function(e){return function(t){return T(e(t))}},T=d()((function(e){return Object(p.mapValues)(e,(function(e,t){return function(){var n=e.apply(void 0,arguments);return g()(void 0!==n,"".concat(t,"(...) is not resolved")),n}}))}));function I(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.negate,r=void 0!==n&&n,a=Object(m.b)((function(t){return function(n){var a=!r,i=!!r;try{for(var o=arguments.length,c=new Array(o>1?o-1:0),s=1;s<o;s++)c[s-1]=arguments[s];return e.apply(void 0,[t,n].concat(c)),a}catch(e){return i}}})),i=Object(m.b)((function(t){return function(n){for(var r=arguments.length,a=new Array(r>1?r-1:0),i=1;i<r;i++)a[i-1]=arguments[i];e.apply(void 0,[t,n].concat(a))}}));return{safeSelector:a,dangerousSelector:i}}function P(e,t){return g()("function"==typeof e,"a validator function is required."),g()("function"==typeof t,"an action creator function is required."),g()("Generator"!==e[Symbol.toStringTag]&&"GeneratorFunction"!==e[Symbol.toStringTag],"an action’s validator function must not be a generator."),function(){return e.apply(void 0,arguments),t.apply(void 0,arguments)}}},623:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(14);function a(e){var t=e;return t=(t=(t=(t=(t=(t=Object(r.unescape)(t)).trim()).replace(/^_+/,"")).normalize("NFD").replace(/[\u0300-\u036f]/g,"")).replace(/[^a-zA-Z0-9_., -]/g,"")).replace(/\s+/g," ")}},64:function(e,t,n){"use strict";n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return v}));var r=n(6),a=n.n(r),i=n(33),o=n.n(i),c=n(116),s=n(12),u=n.n(s),l=n(96),g=n.n(l),f=n(9);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e,t){if(t&&Array.isArray(t)){var n=t.map((function(e){return"object"===o()(e)?Object(f.H)(e):e}));return"".concat(e,"::").concat(g()(JSON.stringify(n)))}return e}var b={receiveError:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(e,"error is required."),u()(t,"baseName is required."),u()(n&&Array.isArray(n),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:n}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return u()(e,"baseName is required."),u()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function v(e){u()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(n,"selectorName is required."),t.getError(e,n,r)},getErrorForAction:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(n,"actionName is required."),t.getError(e,n,r)},getError:function(e,t,n){var r=e.errors;return u()(t,"baseName is required."),r[m(t,n)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var n=Object.keys(e.errors).find((function(n){return e.errors[n]===t}));return n?{baseName:n.substring(0,n.indexOf("::")),args:e.errorArgs[n]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(n,r){var a=t(e).getMetaDataForError(r);if(a){var i=a.baseName,o=a.args;if(!!t(e)[i])return{storeName:e,name:i,args:o}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:b,controls:{},reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case"RECEIVE_ERROR":var i=r.baseName,o=r.args,c=r.error,s=m(i,o);return p(p({},e),{},{errors:p(p({},e.errors||{}),{},a()({},s,c)),errorArgs:p(p({},e.errorArgs||{}),{},a()({},s,o))});case"CLEAR_ERROR":var u=r.baseName,l=r.args,g=p({},e),f=m(u,l);return g.errors=p({},e.errors||{}),g.errorArgs=p({},e.errorArgs||{}),delete g.errors[f],delete g.errorArgs[f],g;case"CLEAR_ERRORS":var d=r.baseName,b=p({},e);if(d)for(var v in b.errors=p({},e.errors||{}),b.errorArgs=p({},e.errorArgs||{}),b.errors)(v===d||v.startsWith("".concat(d,"::")))&&(delete b.errors[v],delete b.errorArgs[v]);else b.errors={},b.errorArgs={};return b;default:return e}},resolvers:{},selectors:t}}},65:function(e,t,n){"use strict";n.d(t,"c",(function(){return m})),n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return v})),n.d(t,"d",(function(){return y}));var r=n(6),a=n.n(r),i=n(0);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var c=i.createElement("path",{d:"M10 15.27L16.18 19l-1.64-7.03L20 7.24l-7.19-.61L10 0 7.19 6.63 0 7.24l5.46 4.73L3.82 19 10 15.27z"});var s=function SvgInfoIcon(e){return i.createElement("svg",o({viewBox:"0 0 20 20",fill:"currentColor"},e),c)};function u(){return(u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var l=i.createElement("path",{d:"M0 4h2v7H0zm0-4h2v2H0z",fill:"currentColor",fillRule:"evenodd"});var g,f=function SvgSuggestionIcon(e){return i.createElement("svg",u({viewBox:"0 0 2 11"},e),l)},d=n(186),p=n(74),m="warning",b="info",v="suggestion",h=(g={},a()(g,b,s),a()(g,m,d.a),a()(g,v,f),g),y=function(e){return h[e]||p.a}},67:function(e,t,n){"use strict";n.d(t,"b",(function(){return f})),n.d(t,"e",(function(){return d})),n.d(t,"f",(function(){return p})),n.d(t,"g",(function(){return m})),n.d(t,"i",(function(){return b})),n.d(t,"h",(function(){return v})),n.d(t,"d",(function(){return h})),n.d(t,"c",(function(){return y})),n.d(t,"l",(function(){return O})),n.d(t,"k",(function(){return j})),n.d(t,"j",(function(){return S}));var r=n(12),a=n.n(r),i=n(14),o=n(8),c=n(9);n.d(t,"a",(function(){return c.x}));var s=n(178),u=n(275),l=n(122),g=n(342);function f(e){return e===o.a||Object(c.x)(e)}function d(e){return"string"==typeof e&&/^\d+$/.test(e)}function p(e){return e===o.s||d(e)}function m(e){return"string"==typeof e&&/^\d+$/.test(e)}function b(e){return e===o.z||m(e)}function v(e){return"string"==typeof e&&e.trim().length>0}function h(e){return"string"==typeof e&&/^G-[a-zA-Z0-9]+$/.test(e)}function y(e){return"string"==typeof e&&/^(G|GT|AW)-[a-zA-Z0-9]+$/.test(e)}function O(e){a()(Object(i.isPlainObject)(e),"options for Analytics 4 report must be an object."),a()(Object(s.a)(e),"Either date range or start/end dates must be provided for Analytics 4 report.");var t=Object(u.a)(e),n=t.metrics,r=t.dimensions,o=t.dimensionFilters,c=t.metricFilters,g=t.orderby;a()(n.length,"Requests must specify at least one metric for an Analytics 4 report."),a()(Object(l.d)(n),'metrics for an Analytics 4 report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property. Metric names must match the expression ^[a-zA-Z0-9_]+$.'),r&&a()(Object(l.b)(r),'dimensions for an Analytics 4 report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property.'),o&&a()(Object(l.a)(o),"dimensionFilters for an Analytics 4 report must be a map of dimension names as keys and dimension values as values."),c&&a()(Object(l.c)(c),"metricFilters for an Analytics 4 report must be a map of metric names as keys and filter value(s) as numeric fields, depending on the filterType."),g&&a()(Object(l.e)(g),'orderby for an Analytics 4 report must be an array of OrderBy objects where each object should have either a "metric" or "dimension" property, and an optional "desc" property.')}function j(e){a()(Object(i.isPlainObject)(e),"options for Analytics 4 pivot report must be an object."),a()(Object(s.a)(e),"Start/end dates must be provided for Analytics 4 pivot report.");var t=Object(u.a)(e),n=t.metrics,r=t.dimensions,o=t.dimensionFilters,c=t.metricFilters,f=t.pivots,d=t.orderby,p=t.limit;a()(n.length,"Requests must specify at least one metric for an Analytics 4 pivot report."),a()(Object(l.d)(n),'metrics for an Analytics 4 pivot report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property. Metric names must match the expression ^[a-zA-Z0-9_]+$.'),a()(Object(g.a)(f),'pivots for an Analytics 4 pivot report must be an array of objects. Each object must have a "fieldNames" property and a "limit".'),d&&a()(Array.isArray(d),"orderby for an Analytics 4 pivot report must be passed within a pivot."),p&&a()("number"==typeof p,"limit for an Analytics 4 pivot report must be passed within a pivot."),r&&a()(Object(l.b)(r),'dimensions for an Analytics 4 pivot report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property.'),o&&a()(Object(l.a)(o),"dimensionFilters for an Analytics 4 pivot report must be a map of dimension names as keys and dimension values as values."),c&&a()(Object(l.c)(c),"metricFilters for an Analytics 4 pivot report must be a map of metric names as keys and filter value(s) as numeric fields, depending on the filterType.")}function S(e){var t=["displayName","description","membershipDurationDays","eventTrigger","exclusionDurationMode","filterClauses"];a()(Object(i.isPlainObject)(e),"Audience must be an object."),Object.keys(e).forEach((function(e){a()(t.includes(e),'Audience object must contain only valid keys. Invalid key: "'.concat(e,'"'))})),["displayName","description","membershipDurationDays","filterClauses"].forEach((function(t){a()(e[t],'Audience object must contain required keys. Missing key: "'.concat(t,'"'))})),a()(Object(i.isArray)(e.filterClauses),"filterClauses must be an array with AudienceFilterClause objects.")}},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a})),n.d(t,"e",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return u})),n.d(t,"O",(function(){return l})),n.d(t,"K",(function(){return g})),n.d(t,"L",(function(){return f})),n.d(t,"J",(function(){return d})),n.d(t,"I",(function(){return p})),n.d(t,"N",(function(){return m})),n.d(t,"f",(function(){return b})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return y})),n.d(t,"l",(function(){return O})),n.d(t,"m",(function(){return j})),n.d(t,"n",(function(){return S})),n.d(t,"o",(function(){return _})),n.d(t,"q",(function(){return E})),n.d(t,"s",(function(){return k})),n.d(t,"r",(function(){return w})),n.d(t,"t",(function(){return A})),n.d(t,"w",(function(){return C})),n.d(t,"u",(function(){return D})),n.d(t,"v",(function(){return N})),n.d(t,"x",(function(){return x})),n.d(t,"y",(function(){return T})),n.d(t,"A",(function(){return I})),n.d(t,"B",(function(){return P})),n.d(t,"C",(function(){return R})),n.d(t,"D",(function(){return M})),n.d(t,"k",(function(){return L})),n.d(t,"F",(function(){return F})),n.d(t,"z",(function(){return G})),n.d(t,"G",(function(){return U})),n.d(t,"E",(function(){return V})),n.d(t,"i",(function(){return q})),n.d(t,"p",(function(){return B})),n.d(t,"Q",(function(){return H})),n.d(t,"P",(function(){return W}));var r="core/user",a="connected_url_mismatch",i="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",u="googlesitekit_setup",l="googlesitekit_view_dashboard",g="googlesitekit_manage_options",f="googlesitekit_read_shared_module_data",d="googlesitekit_manage_module_sharing_options",p="googlesitekit_delegate_module_sharing_management",m="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",y="kmAnalyticsNewVisitors",O="kmAnalyticsPopularAuthors",j="kmAnalyticsPopularContent",S="kmAnalyticsPopularProducts",_="kmAnalyticsReturningVisitors",E="kmAnalyticsTopCities",k="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",A="kmAnalyticsTopCitiesDrivingPurchases",C="kmAnalyticsTopDeviceDrivingPurchases",D="kmAnalyticsTopConvertingTrafficSource",N="kmAnalyticsTopCountries",x="kmAnalyticsTopPagesDrivingLeads",T="kmAnalyticsTopRecentTrendingPages",I="kmAnalyticsTopTrafficSource",P="kmAnalyticsTopTrafficSourceDrivingAddToCart",R="kmAnalyticsTopTrafficSourceDrivingLeads",M="kmAnalyticsTopTrafficSourceDrivingPurchases",L="kmAnalyticsPagesPerVisit",F="kmAnalyticsVisitLength",G="kmAnalyticsTopReturningVisitorPages",U="kmSearchConsolePopularKeywords",V="kmAnalyticsVisitsPerVisitor",q="kmAnalyticsMostEngagingPages",B="kmAnalyticsTopCategories",H=[b,v,h,y,O,j,S,_,B,E,k,w,A,C,D,N,T,I,P,L,F,G,V,q,B],W=[].concat(H,[U])},70:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},74:function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(33),a=n.n(r),i=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:i.a.sanitize(e,t)}};function c(e){var t,n="object"===a()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(1),a=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,a=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:a}},n)}IconWrapper.propTypes={children:a.a.node.isRequired,marginLeft:a.a.number,marginRight:a.a.number}}).call(this,n(4))},763:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupErrorNotice}));var r=n(3),a=n(46),i=n(19),o=n(8),c=n(141),s=n(54);function SetupErrorNotice(){var t=Object(r.useSelect)((function(e){return e(i.a).isModuleAvailable("analytics-4")})),n=[Object(r.useSelect)((function(e){return e(i.a).getErrorForAction("activateModule",["analytics-4"])})),Object(r.useSelect)((function(e){if(!t)return!1;var n=e(o.r).getSettings();return e(o.r).getErrorForAction("saveSettings",[n])}))].filter(Boolean);return n.length?n.map((function(t){var n=t.message,r=t.reconnectURL;return e.createElement(s.a,{key:n,message:n,reconnectURL:r})})):e.createElement(c.a,{moduleSlug:"tagmanager",storeName:a.g})}}).call(this,n(4))},764:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupForm}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(15),s=n.n(c),u=n(1),l=n.n(u),g=n(0),f=n(2),d=n(3),p=n(10),m=n(46),b=n(29),v=n(7),h=n(19),y=n(32),O=n(35),j=n(37),S=n(355),_=n(763),E=n(768);function SetupForm(t){var n=t.finishSetup,r=Object(d.useSelect)((function(e){return e(m.g).canSubmitChanges()})),i=Object(d.useSelect)((function(e){return e(m.g).getCurrentGTMGoogleTagID()})),c=Object(d.useSelect)((function(e){return e(h.a).isModuleAvailable("analytics-4")})),u=Object(d.useSelect)((function(e){return e(h.a).isModuleActive("analytics-4")})),l=Object(d.useSelect)((function(e){return e(v.a).hasScope(m.e)})),k=Object(d.useSelect)((function(e){return e(b.a).getValue(m.f,"autoSubmit")}),[]),w=Object(d.useSelect)((function(e){return e(b.a).getValue(m.f,"submitMode")}),[]),A=Object(d.useSelect)((function(e){return e(m.g).hasExistingTag()})),C=Object(d.useSelect)((function(e){return e(m.g).isDoingSubmitChanges()||e(y.a).isNavigating()||e(b.a).getValue(m.f,"submitInProgress")})),D=Object(g.useState)(!1),N=s()(D,2),x=N[0],T=N[1],I=Object(d.useDispatch)(b.a).setValues,P=Object(d.useDispatch)(h.a).activateModule,R=Object(d.useDispatch)(m.g).submitChanges,M=Object(g.useCallback)(o()(a.a.mark((function e(){var t,r,i,c,s,l,g=arguments;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=g.length>0&&void 0!==g[0]?g[0]:{},r=t.submitMode,i=function(){var e=o()(a.a.mark((function e(t){var n,r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t();case 2:if(e.t0=e.sent,e.t0){e.next=5;break}e.t0={};case 5:if(n=e.t0,!(r=n.error)){e.next=9;break}throw r;case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),I(m.f,{submitMode:r,submitInProgress:!0}),e.prev=3,e.next=6,i((function(){return R()}));case 6:if(I(m.f,{autoSubmit:!1}),r!==m.i||u){e.next=20;break}return e.next=10,P("analytics-4");case 10:if(c=e.sent,s=c.response,!(l=c.error)){e.next=15;break}throw l;case 15:return e.next=17,Object(j.f)("module_setup","analytics-4",{ttl:300});case 17:n(s.moduleReauthURL),e.next=21;break;case 20:n();case 21:e.next=26;break;case 23:e.prev=23,e.t0=e.catch(3),Object(O.f)(e.t0)&&I(m.f,{autoSubmit:!0});case 26:I(m.f,{submitInProgress:!1});case 27:case"end":return e.stop()}}),e,null,[[3,23]])}))),[n,u,P,R,I]);Object(g.useEffect)((function(){k&&l&&M({submitMode:w})}),[l,k,M,w]);var L=!(!i||!c||u),F=Object(g.useCallback)((function(e){e.preventDefault();var t=L?m.i:"";M({submitMode:t})}),[M,L]),G=Object(g.useCallback)((function(){return M()}),[M]);return e.createElement("form",{className:"googlesitekit-tagmanager-setup__form",onSubmit:F},e.createElement(_.a,null),e.createElement(S.e,{isSetup:!0}),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(S.c,null),e.createElement(S.g,null),e.createElement(S.a,null),e.createElement(S.f,null)),e.createElement(S.d,null),A&&e.createElement(E.a,null),e.createElement("div",{className:"googlesitekit-setup-module__action"},L&&e.createElement(g.Fragment,null,e.createElement(p.SpinnerButton,{disabled:!r,isSaving:x&&C,onClick:function(){return T(!0)}},Object(f.__)("Continue to Analytics setup","google-site-kit")),e.createElement(p.Button,{tertiary:!0,className:"googlesitekit-setup-module__sub-action",type:"button",onClick:G,disabled:!r},Object(f.__)("Complete setup without Analytics","google-site-kit"))),!L&&e.createElement(p.SpinnerButton,{disabled:!r||C,isSaving:C},Object(f.__)("Complete setup","google-site-kit"))))}SetupForm.propTypes={finishSetup:l.a.func}}).call(this,n(4))},765:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AMPContainerNameTextField}));var r=n(81),a=n(2),i=n(106),o=n(3),c=n(46),s=n(13),u=n(29),l=n(614);function AMPContainerNameTextField(){var t=Object(o.useSelect)((function(e){return e(c.g).getAMPContainerID()})),n=Object(o.useSelect)((function(e){return e(s.c).getSiteName()})),g=Object(o.useSelect)((function(e){return e(s.c).getReferenceSiteURL()})),f=Object(o.useSelect)((function(e){return e(u.a).getValue(c.f,"ampContainerName")}),[]),d=n;!d&&Object(i.a)(g)&&(d=new URL(g).hostname),d+=" AMP";var p=Object(o.useDispatch)(u.a).setValues;return Object(r.a)((function(){f||p(c.f,{ampContainerName:d})})),t!==c.b?null:e.createElement(l.a,{name:"ampContainerName",label:Object(a.__)("AMP Container Name","google-site-kit")})}}).call(this,n(4))},766:function(e,t,n){"use strict";t.a=[/<script[^>]*>[^>]+?www.googletagmanager.com\/gtm[^>]+?['|"](GTM-[0-9A-Z]+)['|"]/,/<script[^>]*src=['|"]https:\/\/www.googletagmanager.com\/gtm\.js\?id=(GTM-[0-9A-Z]+)['|"]/,/<script[^>]*src=['|"]https:\/\/www.googletagmanager.com\/ns.html\?id=(GTM-[0-9A-Z]+)['|"]/,/<amp-analytics [^>]*config=['|"]https:\/\/www.googletagmanager.com\/amp.json\?id=(GTM-[0-9A-Z]+)['|"]/]},767:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WebContainerNameTextField}));var r=n(81),a=n(2),i=n(106),o=n(3),c=n(46),s=n(13),u=n(29),l=n(614);function WebContainerNameTextField(){var t=Object(o.useSelect)((function(e){return e(c.g).getContainerID()})),n=Object(o.useSelect)((function(e){return e(s.c).getSiteName()})),g=Object(o.useSelect)((function(e){return e(s.c).isAMP()})),f=Object(o.useSelect)((function(e){return e(s.c).getReferenceSiteURL()})),d=Object(o.useSelect)((function(e){return e(u.a).getValue(c.f,"containerName")}),[]),p=n;!p&&Object(i.a)(f)&&(p=new URL(f).hostname);var m=Object(o.useDispatch)(u.a).setValues;if(Object(r.a)((function(){d||m(c.f,{containerName:p})})),t!==c.b)return null;var b=g?Object(a.__)("Web Container Name","google-site-kit"):Object(a.__)("Container Name","google-site-kit");return e.createElement(l.a,{name:"containerName",label:b})}}).call(this,n(4))},768:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupUseSnippetSwitch}));var r=n(0),a=n(2),i=n(3),o=n(46),c=n(616);function SetupUseSnippetSwitch(){var t=Object(i.useSelect)((function(e){return e(o.g).getPrimaryContainerID()})),n=Object(i.useSelect)((function(e){return e(o.g).getExistingTag()})),s=t===n?e.createElement(r.Fragment,null,e.createElement("p",null,Object(a.sprintf)(
/* translators: %s: existing tag ID */
Object(a.__)("A tag %s for the selected container already exists on the site","google-site-kit"),n)),e.createElement("p",null,Object(a.__)("Make sure you remove it if you want to place the same tag via Site Kit, otherwise they will be duplicated","google-site-kit"))):e.createElement(r.Fragment,null,e.createElement("p",null,Object(a.sprintf)(
/* translators: %s: existing tag ID */
Object(a.__)("An existing tag %s was found on the page","google-site-kit"),n)),e.createElement("p",null,Object(a.__)("If you prefer to collect data using that existing tag, please select the corresponding account and property above","google-site-kit")));return e.createElement(c.a,{description:s})}}).call(this,n(4))},769:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsForm}));var r=n(1),a=n.n(r),i=n(38),o=n(2),c=n(3),s=n(355),u=n(141),l=n(46),g=n(19),f=n(770),d=n(166),p=n(142),m=n(186);function SettingsForm(t){var n,r=t.hasModuleAccess,a=Object(c.useSelect)((function(e){return e(g.a).getModule("tagmanager")})),b=(null==a||null===(n=a.owner)||void 0===n?void 0:n.login)?"<strong>".concat(a.owner.login,"</strong>"):Object(o.__)("Another admin","google-site-kit");return e.createElement("div",{className:"googlesitekit-tagmanager-settings-fields"},e.createElement(u.a,{moduleSlug:"tagmanager",storeName:l.g}),e.createElement(s.e,null),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(s.c,{hasModuleAccess:r}),e.createElement(s.g,{hasModuleAccess:r}),e.createElement(s.a,{hasModuleAccess:r}),e.createElement(s.f,null)),!1===r&&e.createElement(d.a,{type:p.a,Icon:m.a,notice:Object(i.a)(Object(o.sprintf)(
/* translators: 1: module owner's name, 2: module name */
Object(o.__)("%1$s configured %2$s and you don’t have access to this %2$s account. Contact them to share access or change the %2$s account.","google-site-kit"),b,null==a?void 0:a.name),{strong:e.createElement("strong",null)})}),e.createElement(s.d,null),e.createElement("div",{className:"googlesitekit-setup-module__inputs googlesitekit-setup-module__inputs--multiline"},e.createElement(f.a,null)))}SettingsForm.propTypes={hasModuleAccess:a.a.bool},SettingsForm.defaultProps={hasModuleAccess:!0}}).call(this,n(4))},770:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsUseSnippetSwitch}));var r=n(0),a=n(2),i=n(3),o=n(616),c=n(46);function SettingsUseSnippetSwitch(){var t,n=Object(i.useSelect)((function(e){return e(c.g).getUseSnippet()})),s=Object(i.useSelect)((function(e){return e(c.g).getPrimaryContainerID()})),u=Object(i.useSelect)((function(e){return e(c.g).getExistingTag()}));return t=u?s===u?e.createElement(r.Fragment,null,e.createElement("p",null,Object(a.sprintf)(
/* translators: %s: existing tag ID */
Object(a.__)("A tag %s for the selected container already exists on the site","google-site-kit"),u)),e.createElement("p",null,Object(a.__)("Consider removing the existing tag to avoid loading both tags on your site","google-site-kit"))):e.createElement(r.Fragment,null,e.createElement("p",null,Object(a.sprintf)(
/* translators: %s: existing tag ID */
Object(a.__)("An existing tag %s was found on the page","google-site-kit"),u)),e.createElement("p",null,Object(a.__)("If you prefer to collect data using that existing tag, please select the corresponding account and property above","google-site-kit"))):n?e.createElement("p",null,Object(a.__)("Site Kit will add the code automatically","google-site-kit")):e.createElement("p",null,Object(a.__)("Site Kit will not add the code to your site","google-site-kit")),e.createElement(o.a,{description:t})}}).call(this,n(4))},789:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",transform:"translate(.422 .422)"},r.createElement("path",{d:"M16.091 30.725l14.461-14.543 6.817 6.856L22.908 37.58z",fill:"#8ab4f8"}),r.createElement("path",{d:"M22.91 8.496L16.09 1.64 1.63 16.182a4.867 4.867 0 000 6.854L16.09 37.58l6.817-6.855L11.856 19.61z",fill:"#4285f4"}),r.createElement("ellipse",{cx:19.5,cy:34.153,fill:"#1967d2",rx:4.82,ry:4.847}),r.createElement("path",{d:"M37.37 16.182L22.91 1.639a4.801 4.801 0 00-6.817 0 4.867 4.867 0 000 6.855l14.46 14.542a4.801 4.801 0 006.817 0 4.867 4.867 0 000-6.854z",fill:"#8ab4f8"}));t.a=function SvgTagmanager(e){return r.createElement("svg",a({viewBox:"0 0 40 40"},e),i)}},790:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),a=n(3),i=n(46);function o(){var e=Object(a.useSelect)((function(e){return e(i.g).getExistingTag()})),t=Object(a.useSelect)((function(e){return e(i.g).getPrimaryContainerID()})),n=Object(r.useRef)(!0),o=Object(a.useDispatch)(i.g).setUseSnippet;Object(r.useEffect)((function(){if(e&&void 0!==t){if(""===t||n.current)return void(n.current=!1);o(e!==t)}}),[t,e,o])}},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"s",(function(){return i})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return s})),n.d(t,"g",(function(){return u})),n.d(t,"p",(function(){return l})),n.d(t,"j",(function(){return g})),n.d(t,"i",(function(){return f})),n.d(t,"k",(function(){return d})),n.d(t,"m",(function(){return p})),n.d(t,"n",(function(){return m})),n.d(t,"h",(function(){return b})),n.d(t,"x",(function(){return v})),n.d(t,"w",(function(){return h})),n.d(t,"y",(function(){return y})),n.d(t,"u",(function(){return O})),n.d(t,"v",(function(){return j})),n.d(t,"f",(function(){return S})),n.d(t,"l",(function(){return _})),n.d(t,"e",(function(){return E})),n.d(t,"t",(function(){return k})),n.d(t,"c",(function(){return w})),n.d(t,"d",(function(){return A})),n.d(t,"b",(function(){return C}));var r="modules/analytics-4",a="account_create",i="property_create",o="webdatastream_create",c="analyticsSetup",s=10,u=1,l="https://www.googleapis.com/auth/tagmanager.readonly",g="enhanced-measurement-form",f="enhanced-measurement-enabled",d="enhanced-measurement-should-dismiss-activation-banner",p="analyticsAccountCreate",m="analyticsCustomDimensionsCreate",b="https://www.googleapis.com/auth/analytics.edit",v="dashboardAllTrafficWidgetDimensionName",h="dashboardAllTrafficWidgetDimensionColor",y="dashboardAllTrafficWidgetDimensionValue",O="dashboardAllTrafficWidgetActiveRowIndex",j="dashboardAllTrafficWidgetLoaded",S={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},_={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},E=[_.CONTACT,_.GENERATE_LEAD,_.SUBMIT_LEAD_FORM],k={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},w="audiencePermissionsSetup",A="audienceTileCustomDimensionCreate",C="audience-selection-panel-expirable-new-badge-"},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(106);function a(e){try{return new URL(e).pathname}catch(e){}return null}function i(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),a=e.replace(n.origin,"");if(a.length<t)return a;var i=a.length-Math.floor(t)+1;return"…"+a.substr(i)}},82:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return i}));var r=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},a=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function i(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return _})),n.d(t,"d",(function(){return E})),n.d(t,"e",(function(){return w})),n.d(t,"c",(function(){return A})),n.d(t,"b",(function(){return C}));var r=n(15),a=n.n(r),i=n(33),o=n.n(i),c=n(6),s=n.n(c),u=n(25),l=n.n(u),g=n(14),f=n(63),d=n.n(f),p=n(2);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=O(e,t),r=n.formatUnit,a=n.formatDecimal;try{return r()}catch(e){return a()}},h=function(e){var t=y(e),n=t.hours,r=t.minutes,a=t.seconds;return a=("0"+a).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(a):"".concat(n,":").concat(r,":").concat(a)},y=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},O=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=y(e),r=n.hours,a=n.minutes,i=n.seconds;return{hours:r,minutes:a,seconds:i,formatUnit:function(){var n=t.unitDisplay,o=b(b({unitDisplay:void 0===n?"short":n},l()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(i,b(b({},o),{},{unit:"second"})):Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?w(i,b(b({},o),{},{unit:"second"})):"",a?w(a,b(b({},o),{},{unit:"minute"})):"",r?w(r,b(b({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(p.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(p.__)("%ds","google-site-kit"),i);if(0===e)return t;var n=Object(p.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(p.__)("%dm","google-site-kit"),a),o=Object(p.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(p.__)("%dh","google-site-kit"),r);return Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?t:"",a?n:"",r?o:"").trim()}}},j=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},S=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in millions.
Object(p.__)("%sM","google-site-kit"),w(j(e),e%10==0?{}:t)):1e4<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(j(e))):1e3<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(j(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function _(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(g.isPlainObject)(e)&&(t=b({},e)),t}function E(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(g.isFinite)(e)?e:Number(e),Object(g.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=_(t),r=n.style,a=void 0===r?"metric":r;return"metric"===a?S(e):"duration"===a?v(e,n):"durationISO"===a?h(e):w(e,n)}var k=d()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?C():n,i=l()(t,["locale"]);try{return new Intl.NumberFormat(r,i).format(e)}catch(t){k("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(i)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],u={},g=0,f=Object.entries(i);g<f.length;g++){var d=a()(f[g],2),p=d[0],m=d[1];c[p]&&m===c[p]||(s.includes(p)||(u[p]=m))}try{return new Intl.NumberFormat(r,u).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},A=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?C():n,a=t.style,i=void 0===a?"long":a,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(r,{style:i,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var u=Object(p.__)(", ","google-site-kit");return e.join(u)},C=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(g.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a}));var r=n(149),a=n.n(r)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i);function ChangeArrow(t){var n=t.direction,r=t.invertColor,a=t.width,i=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:a,height:i,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:a.a.string,invertColor:a.a.bool,width:a.a.number,height:a.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(12),a=n.n(r),i=function(e,t){var n=t.dateRangeLength;a()(Array.isArray(e),"report must be an array to partition."),a()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return a.b})),n.d(t,"J",(function(){return a.c})),n.d(t,"F",(function(){return i.a})),n.d(t,"K",(function(){return i.b})),n.d(t,"H",(function(){return l})),n.d(t,"m",(function(){return g.a})),n.d(t,"B",(function(){return g.d})),n.d(t,"C",(function(){return g.e})),n.d(t,"y",(function(){return g.c})),n.d(t,"r",(function(){return g.b})),n.d(t,"z",(function(){return m})),n.d(t,"j",(function(){return b})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return _})),n.d(t,"c",(function(){return E})),n.d(t,"e",(function(){return k})),n.d(t,"b",(function(){return w})),n.d(t,"a",(function(){return A})),n.d(t,"f",(function(){return C})),n.d(t,"n",(function(){return D})),n.d(t,"w",(function(){return N})),n.d(t,"p",(function(){return x})),n.d(t,"G",(function(){return T})),n.d(t,"s",(function(){return I})),n.d(t,"v",(function(){return P})),n.d(t,"k",(function(){return R})),n.d(t,"o",(function(){return M.b})),n.d(t,"h",(function(){return M.a})),n.d(t,"t",(function(){return L.b})),n.d(t,"q",(function(){return L.a})),n.d(t,"A",(function(){return L.c})),n.d(t,"x",(function(){return F})),n.d(t,"u",(function(){return G})),n.d(t,"E",(function(){return q})),n.d(t,"D",(function(){return B.a})),n.d(t,"g",(function(){return H})),n.d(t,"L",(function(){return W})),n.d(t,"l",(function(){return K}));var r=n(14),a=n(36),i=n(75),o=n(33),c=n.n(o),s=n(96),u=n.n(s),l=function(e){return u()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var a=t[r];a&&"object"===c()(a)&&!Array.isArray(a)&&(a=e(a)),n[r]=a})),n}(e)))};n(97);var g=n(83);function f(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function d(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function p(e){return e.replace(/\n/gi,"<br>")}function m(e){for(var t=e,n=0,r=[f,d,p];n<r.length;n++){t=(0,r[n])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(15),y=n.n(h),O=n(12),j=n.n(O),S=n(2),_="Invalid dateString parameter, it must be a string.",E='Invalid date range, it must be a string with the format "last-x-days".',k=60,w=60*k,A=24*w,C=7*A;function D(){var e=function(e){return Object(S.sprintf)(
/* translators: %s: number of days */
Object(S._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function N(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function x(e){j()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function T(e){j()(N(e),_);var t=e.split("-"),n=y()(t,3),r=n[0],a=n[1],i=n[2];return new Date(r,a-1,i)}function I(e,t){return x(R(e,t*A))}function P(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function R(e,t){j()(N(e)||Object(r.isDate)(e)&&!isNaN(e),_);var n=N(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var M=n(98),L=n(80);function F(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function G(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var U=n(27),V=n.n(U),q=function(e){return Array.isArray(e)?V()(e).sort():e},B=n(89);function H(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var W=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},K=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},935:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return AccountCreate}));var a=n(0),i=n(2),o=n(3),c=n(10),s=n(46),u=n(7),l=n(141),g=n(36),f=n(18);function AccountCreate(){var t=Object(f.a)(),n=Object(o.useSelect)((function(e){return e(s.g).hasFinishedResolution("getAccounts")})),d=Object(o.useSelect)((function(e){return e(u.a).hasFinishedResolution("getUser")})),p=Object(o.useSelect)((function(e){return e(s.g).getServiceURL({path:"admin/accounts/create"})})),m=Object(o.useDispatch)(s.g).resetAccounts,b=Object(a.useCallback)((function(){m()}),[m]),v=Object(a.useCallback)((function(){Object(g.b)("".concat(t,"_tagmanager"),"create_account"),e.window.open(p,"_blank")}),[p,t]);return n&&d?r.createElement("div",null,r.createElement(l.a,{moduleSlug:"tagmanager",storeName:s.g}),r.createElement("p",null,Object(i.__)("To create a new account, click the button below which will open the Google Tag Manager account creation screen in a new window.","google-site-kit")),r.createElement("p",null,Object(i.__)("Once completed, click the link below to re-fetch your accounts to continue.","google-site-kit")),r.createElement("div",{className:"googlesitekit-setup-module__action"},r.createElement(c.Button,{onClick:v},Object(i.__)("Create an account","google-site-kit")),r.createElement("div",{className:"googlesitekit-setup-module__sub-action"},r.createElement(c.Button,{tertiary:!0,onClick:b},Object(i.__)("Re-fetch My Account","google-site-kit"))))):r.createElement(c.ProgressBar,null)}}).call(this,n(28),n(4))},936:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccountSelect}));var r=n(1),a=n.n(r),i=n(0),o=n(2),c=n(10),s=n(3),u=n(46),l=n(36),g=n(18);function AccountSelect(t){var n=t.hasModuleAccess,r=Object(g.a)(),a=Object(s.useSelect)((function(e){return e(u.g).getAccounts()})),f=Object(s.useSelect)((function(e){return e(u.g).hasFinishedResolution("getAccounts")})),d=Object(s.useSelect)((function(e){return e(u.g).getAccountID()})),p=Object(s.useDispatch)(u.g).selectAccount,m=Object(i.useCallback)((function(e,t){var n=t.dataset.value;if(d!==n){var a=n===u.a?"change_account_new":"change_account";Object(l.b)("".concat(r,"_tagmanager"),a),p(n)}}),[d,p,r]);return f?!1===n?e.createElement(c.Select,{className:"googlesitekit-tagmanager__select-account",label:Object(o.__)("Account","google-site-kit"),value:d,enhanced:!0,outlined:!0,disabled:!0},e.createElement(c.Option,{value:d},d)):e.createElement(c.Select,{className:"googlesitekit-tagmanager__select-account",label:Object(o.__)("Account","google-site-kit"),value:d,onEnhancedChange:m,enhanced:!0,outlined:!0},(a||[]).concat({accountId:u.a,name:Object(o.__)("Set up a new account","google-site-kit")}).map((function(t){var n=t.accountId,r=t.name;return e.createElement(c.Option,{key:n,value:n},r)}))):e.createElement(c.ProgressBar,{small:!0})}AccountSelect.propTypes={hasModuleAccess:a.a.bool}}).call(this,n(4))},937:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AMPContainerSelect}));var r=n(1),a=n.n(r),i=n(0),o=n(2),c=n(10),s=n(3),u=n(46),l=n(13),g=n(615),f=n(36),d=n(18);function AMPContainerSelect(t){var n=t.hasModuleAccess,r=Object(d.a)(),a=Object(s.useSelect)((function(e){return e(u.g).getAccountID()})),p=Object(s.useSelect)((function(e){return e(u.g).getAMPContainerID()})),m=Object(s.useSelect)((function(e){return!1===n?null:e(u.g).getAMPContainers(a)})),b=Object(s.useSelect)((function(e){return e(l.c).isAMP()})),v=Object(s.useDispatch)(u.g),h=v.setAMPContainerID,y=v.setInternalAMPContainerID,O=Object(i.useCallback)((function(e,t){var n=t.dataset,a=n.value,i=n.internalId;if(p!==a){var o=a===u.b?"change_amp_container_new":"change_amp_container";Object(f.b)("".concat(r,"_tagmanager"),o),h(a),y(i||"")}}),[p,h,y,r]);if(!b)return null;var j=Object(o.__)("AMP Container","google-site-kit");return!1===n?e.createElement(c.Select,{className:"googlesitekit-tagmanager__select-container--amp",label:j,value:p,enhanced:!0,outlined:!0,disabled:!0},e.createElement(c.Option,{value:p},p)):e.createElement(g.a,{className:"googlesitekit-tagmanager__select-container--amp",label:j,value:p,containers:m,onEnhancedChange:O})}AMPContainerSelect.propTypes={hasModuleAccess:a.a.bool}}).call(this,n(4))},938:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ContainerNames}));var r=n(3),a=n(765),i=n(767),o=n(46);function ContainerNames(){var t=Object(r.useSelect)((function(e){return e(o.g).getContainerID()})),n=Object(r.useSelect)((function(e){return e(o.g).getAMPContainerID()}));return t!==o.b&&n!==o.b?null:e.createElement("div",{className:"googlesitekit-setup-module__inputs googlesitekit-setup-module__inputs--collapsed"},t===o.b&&e.createElement(i.a,null),n===o.b&&e.createElement(a.a,null))}}).call(this,n(4))},939:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return FormInstructions}));var r=n(1),a=n.n(r),i=n(2),o=n(3),c=n(13),s=n(19),u=n(46);function FormInstructions(t){var n=t.isSetup,r=Object(o.useSelect)((function(e){return e(c.c).isSecondaryAMP()})),a=Object(o.useSelect)((function(e){return e(s.a).isModuleAvailable("analytics-4")})),l=Object(o.useSelect)((function(e){return e(s.a).isModuleActive("analytics-4")})),g=Object(o.useSelect)((function(e){return e(u.g).getCurrentGTMGoogleTagID()}));return a&&!l&&g?e.createElement("p",null,Object(i.__)("Looks like you may be using Google Analytics within your Google Tag Manager configuration. Activate the Google Analytics module in Site Kit to see relevant insights in your dashboard.","google-site-kit")):r?e.createElement("p",null,n?Object(i.__)("Looks like your site is using paired AMP. Please select your Tag Manager account and relevant containers below. You can change these later in your settings.","google-site-kit"):Object(i.__)("Looks like your site is using paired AMP. Please select your Tag Manager account and relevant containers below.","google-site-kit")):e.createElement("p",null,n?Object(i.__)("Please select your Tag Manager account and container below. You can change these later in your settings.","google-site-kit"):Object(i.__)("Please select your Tag Manager account and container below","google-site-kit"))}FormInstructions.propTypes={isSetup:a.a.bool}}).call(this,n(4))},940:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TagCheckProgress}));var r=n(11),a=n.n(r),i=n(1),o=n.n(i),c=n(2),s=n(3),u=n(10),l=n(46);function TagCheckProgress(t){var n=t.className;return Object(s.useSelect)((function(e){var t=e(l.g).getAccountID(),n=e(l.g).getInternalContainerID(),r=e(l.g).getInternalAMPContainerID();return e(l.g).isResolving("getLiveContainerVersion",[t,n])||e(l.g).isResolving("getLiveContainerVersion",[t,r])}))?e.createElement("div",{className:a()(n)},e.createElement("small",null,Object(c.__)("Checking tags…","google-site-kit")),e.createElement(u.ProgressBar,{small:!0,compress:!0})):null}TagCheckProgress.propTypes={className:o.a.string},TagCheckProgress.defaultProps={className:""}}).call(this,n(4))},941:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WebContainerSelect}));var r=n(1),a=n.n(r),i=n(0),o=n(2),c=n(10),s=n(3),u=n(46),l=n(13),g=n(615),f=n(36),d=n(18);function WebContainerSelect(t){var n=t.hasModuleAccess,r=Object(d.a)(),a=Object(s.useSelect)((function(e){return e(u.g).getAccountID()})),p=Object(s.useSelect)((function(e){return e(u.g).getContainerID()})),m=Object(s.useSelect)((function(e){return!1===n?null:e(u.g).getWebContainers(a)})),b=Object(s.useSelect)((function(e){return e(l.c).isAMP()})),v=Object(s.useDispatch)(u.g),h=v.setContainerID,y=v.setInternalContainerID,O=Object(i.useCallback)((function(e,t){var n=t.dataset,a=n.value,i=n.internalId;if(p!==a){var o=a===u.b?"change_container_new":"change_container";Object(f.b)("".concat(r,"_tagmanager"),o),h(a),y(i||"")}}),[p,h,y,r]),j=b?Object(o.__)("Web Container","google-site-kit"):Object(o.__)("Container","google-site-kit");return!1===n?e.createElement(c.Select,{className:"googlesitekit-tagmanager__select-container--web",label:j,value:p,enhanced:!0,outlined:!0,disabled:!0},e.createElement(c.Option,{value:p},p)):e.createElement(g.a,{className:"googlesitekit-tagmanager__select-container--web",label:j,value:p,containers:m,onEnhancedChange:O})}WebContainerSelect.propTypes={hasModuleAccess:a.a.bool}}).call(this,n(4))},942:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupMain}));var r=n(1),a=n.n(r),i=n(2),o=n(3),c=n(10),s=n(789),u=n(764),l=n(46),g=n(790),f=n(355);function SetupMain(t){var n,r=t.finishSetup,a=Object(o.useSelect)((function(e){return e(l.g).getAccounts()})),d=Object(o.useSelect)((function(e){return e(l.g).getAccountID()})),p=Object(o.useSelect)((function(e){return e(l.g).hasExistingTag()})),m=Object(o.useSelect)((function(e){return e(l.g).hasFinishedResolution("getAccounts")})),b=l.a===d;return Object(g.a)(),n=m&&void 0!==p?b||!(null==a?void 0:a.length)?e.createElement(f.b,null):e.createElement(u.a,{finishSetup:r}):e.createElement(c.ProgressBar,null),e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--tagmanager"},e.createElement("div",{className:"googlesitekit-setup-module__step"},e.createElement("div",{className:"googlesitekit-setup-module__logo"},e.createElement(s.a,{width:"40",height:"40"})),e.createElement("h2",{className:"googlesitekit-heading-3 googlesitekit-setup-module__title"},Object(i._x)("Tag Manager","Service name","google-site-kit"))),e.createElement("div",{className:"googlesitekit-setup-module__step"},n))}SetupMain.propTypes={finishSetup:a.a.func},SetupMain.defaultProps={finishSetup:function(){}}}).call(this,n(4))},943:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsEdit}));var r=n(3),a=n(10),i=n(19),o=n(46),c=n(790),s=n(355),u=n(769);function SettingsEdit(){var t,n=Object(r.useSelect)((function(e){return e(o.g).getAccounts()}))||[],l=Object(r.useSelect)((function(e){return e(o.g).getAccountID()})),g=Object(r.useSelect)((function(e){return e(o.g).hasExistingTag()})),f=Object(r.useSelect)((function(e){return e(o.g).isDoingSubmitChanges()})),d=Object(r.useSelect)((function(e){return e(o.g).hasFinishedResolution("getAccounts")})),p=Object(r.useSelect)((function(e){return e(i.a).hasModuleOwnershipOrAccess("tagmanager")})),m=o.a===l;return Object(c.a)(),t=f||!d||void 0===p||void 0===g?e.createElement(a.ProgressBar,null):m||!(null==n?void 0:n.length)?e.createElement(s.b,null):e.createElement(u.a,{hasModuleAccess:p}),e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--tagmanager"},t)}}).call(this,n(4))},944:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsView}));var r=n(304),a=n.n(r),i=n(0),o=n(38),c=n(2),s=n(3),u=n(182),l=n(20),g=n(141),f=n(105),d=n(13),p=n(46),m=n(426);function b(){var e=a()(["/container/accounts/","/containers/",""]);return b=function(){return e},e}function v(){var e=a()(["/container/accounts/","/containers/",""]);return v=function(){return e},e}function SettingsView(){var t=Object(s.useSelect)((function(e){return e(p.g).getAccountID()})),n=Object(s.useSelect)((function(e){return e(p.g).getContainerID()})),r=Object(s.useSelect)((function(e){return e(p.g).getAMPContainerID()})),a=Object(s.useSelect)((function(e){return e(p.g).getUseSnippet()})),h=Object(s.useSelect)((function(e){return e(p.g).hasExistingTag()})),y=Object(s.useSelect)((function(e){return e(d.c).isAMP()})),O=Object(s.useSelect)((function(e){return e(d.c).isSecondaryAMP()})),j=Object(s.useSelect)((function(e){return e(p.g).getInternalContainerID()})),S=Object(s.useSelect)((function(e){return e(p.g).getInternalAMPContainerID()})),_=Object(s.useSelect)((function(e){return e(p.g).getServiceURL({path:Object(m.a)(v(),t,j)})})),E=Object(s.useSelect)((function(e){return e(p.g).getServiceURL({path:Object(m.a)(b(),t,S)})}));return e.createElement(i.Fragment,null,e.createElement(g.a,{moduleSlug:"tagmanager",storeName:p.g}),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(c.__)("Account","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:t}))),(!y||O)&&e.createElement(i.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},O&&e.createElement("span",null,Object(c.__)("Web Container ID","google-site-kit")),!O&&e.createElement("span",null,Object(c.__)("Container ID","google-site-kit"))),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:n}))),_&&e.createElement("div",{className:"googlesitekit-settings-module__meta-item googlesitekit-settings-module__meta-item--data-only"},e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data googlesitekit-settings-module__meta-item-data--tiny"},e.createElement(l.a,{href:_,external:!0},Object(o.a)(Object(c.sprintf)(
/* translators: %s: Appropriate container term. */
Object(c.__)("Edit <VisuallyHidden>%s </VisuallyHidden>in Tag Manager","google-site-kit"),O?Object(c.__)("web container","google-site-kit"):Object(c.__)("container","google-site-kit")),{VisuallyHidden:e.createElement(f.a,null)}))))),y&&e.createElement(i.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},O&&e.createElement("span",null,Object(c.__)("AMP Container ID","google-site-kit")),!O&&e.createElement("span",null,Object(c.__)("Container ID","google-site-kit"))),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:r}))),E&&e.createElement("div",{className:"googlesitekit-settings-module__meta-item googlesitekit-settings-module__meta-item--data-only"},e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data googlesitekit-settings-module__meta-item-data--tiny"},e.createElement(l.a,{href:E,external:!0},Object(o.a)(Object(c.sprintf)(
/* translators: %s: Appropriate container term. */
Object(c.__)("Edit <VisuallyHidden>%s </VisuallyHidden>in Tag Manager","google-site-kit"),O?Object(c.__)("AMP container","google-site-kit"):Object(c.__)("container","google-site-kit")),{VisuallyHidden:e.createElement(f.a,null)})))))),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(c.__)("Tag Manager Code Snippet","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},a&&e.createElement("span",null,Object(c.__)("Snippet is inserted","google-site-kit")),!a&&e.createElement("span",null,Object(c.__)("Snippet is not inserted","google-site-kit"))),h&&e.createElement("p",null,Object(c.__)("Placing two tags at the same time is not recommended.","google-site-kit")))))}}).call(this,n(4))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o}));var r=n(239),a=n(85),i=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var i=n.invertColor,o=void 0!==i&&i;return Object(r.a)(e.createElement(a.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g}));var r=n(6),a=n.n(r),i=n(14),o=n(100),c=n(101);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function g(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=u(u({},l),t);a.referenceSiteURL&&(a.referenceSiteURL=a.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(a,n),g=Object(c.a)(a,n,s,r),f={},d=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);f[r]||(f[r]=Object(i.once)(g)),f[r].apply(f,t)};return{enableTracking:function(){a.trackingEnabled=!0},disableTracking:function(){a.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!a.trackingEnabled},trackEvent:g,trackEventOnce:d}}}).call(this,n(28))}},[[1293,1,0]]]);