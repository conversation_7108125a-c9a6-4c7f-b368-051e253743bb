(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[11],{100:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return a}));var n=r(59),o=r(39),i=r(57);function a(t,r){var a,c=Object(n.a)(r),s=t.activeModules,u=t.referenceSiteURL,l=t.userIDHash,f=t.userRoles,p=void 0===f?[]:f,d=t.isAuthenticated,g=t.pluginVersion;return function(){var r=e.document;if(void 0===a&&(a=!!r.querySelector("script[".concat(o.b,"]"))),!a){a=!0;var n=(null==p?void 0:p.length)?p.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:u,plugin_version:g||"",enabled_features:Array.from(i.a).join(","),active_modules:s.join(","),authenticated:d?"1":"0",user_properties:{user_roles:n,user_identifier:l}});var f=r.createElement("script");return f.setAttribute(o.b,""),f.async=!0,f.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(o.a),r.head.appendChild(f),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(o.a)}}}}}).call(this,r(28))},101:function(e,t,r){"use strict";r.d(t,"a",(function(){return p}));var n=r(5),o=r.n(n),i=r(6),a=r.n(i),c=r(16),s=r.n(c),u=r(59);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach((function(t){a()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function p(e,t,r,n){var i=Object(u.a)(t);return function(){var t=s()(o.a.mark((function t(a,c,s,u){var l;return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return r(),l={send_to:"site_kit",event_category:a,event_label:s,value:u},t.abrupt("return",new Promise((function(e){var t,r,o=setTimeout((function(){n.console.warn('Tracking event "'.concat(c,'" (category "').concat(a,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(o),e()};i("event",c,f(f({},l),{},{event_callback:s})),(null===(t=n._gaUserPrefs)||void 0===t||null===(r=t.ioo)||void 0===r?void 0:r.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,r,n,o){return t.apply(this,arguments)}}()}},1292:function(e,t,r){"use strict";r.r(t);var n,o=r(3),i=r.n(o),a=r(13),c=r(6),s=r.n(c),u=r(16),l=r.n(u),f=r(5),p=r.n(f),d=r(12),g=r.n(d),v=r(37),b={setCacheItem:p.a.mark((function e(t,r,n){return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return g()(t,"key is required"),g()(void 0!==r,"value is required"),e.next=4,{type:"CACHE_SET_ITEM",payload:{key:t,value:r,args:n}};case 4:case"end":return e.stop()}}),e)}))},y=s()({},"CACHE_SET_ITEM",(n=l()(p.a.mark((function e(t){var r,n,o,i;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.payload,n=r.key,o=r.value,i=r.args,e.next=4,Object(v.f)(n,o,i);case 4:case"end":return e.stop()}}),e)}))),function(e){return n.apply(this,arguments)})),m=Object(o.combineStores)({initialState:{},actions:b,controls:y}),O=(m.initialState,m.actions,m.controls,m.reducer,m.resolvers,m.selectors,m),h=r(45),S=r.n(h),E=r(48);function j(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function k(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?j(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var w=Object(E.a)({baseName:"getConnection",controlCallback:function(){return S.a.get("core","site","connection",void 0,{useCache:!1})},reducerCallback:function(e,t){return k(k({},e),{},{connection:t})}}),P={connection:void 0},R={getConnection:p.a.mark((function e(){var t;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o.commonActions.getRegistry();case 2:if(t=e.sent,t.select(a.c).getConnection()){e.next=7;break}return e.next=7,w.actions.fetchGetConnection();case 7:case"end":return e.stop()}}),e)}))},A={getConnection:function(e){return e.connection},getOwnerID:Object(o.createRegistrySelector)((function(e){return function(){return(e(a.c).getConnection()||{}).ownerID}})),hasConnectedAdmins:Object(o.createRegistrySelector)((function(e){return function(){return(e(a.c).getConnection()||{}).hasConnectedAdmins}})),isConnected:Object(o.createRegistrySelector)((function(e){return function(){var t=e(a.c).getConnection();return void 0!==t?t.connected:t}})),isResettable:Object(o.createRegistrySelector)((function(e){return function(){var t=e(a.c).getConnection();return void 0!==t?t.resettable:t}})),isSetupCompleted:Object(o.createRegistrySelector)((function(e){return function(){var t=e(a.c).getConnection();return void 0!==t?t.setupCompleted:t}})),hasMultipleAdmins:Object(o.createRegistrySelector)((function(e){return function(){var t;return null===(t=e(a.c).getConnection())||void 0===t?void 0:t.hasMultipleAdmins}}))},C=Object(o.combineStores)(w,{initialState:P,resolvers:R,selectors:A}),_=(C.initialState,C.actions,C.controls,C.reducer,C.resolvers,C.selectors,C),T=r(955),x=r(14),L=r(176),N=o.commonActions.getRegistry,I=Object(L.a)((function(e,t){e.conversionTracking.settings=t,e.conversionTracking.savedSettings=t})),M=Object(E.a)({baseName:"getConversionTrackingSettings",controlCallback:function(){return S.a.get("core","site","conversion-tracking",null,{useCache:!1})},reducerCallback:I}),D=Object(E.a)({baseName:"saveConversionTrackingSettings",controlCallback:function(e){var t=e.settings;return S.a.set("core","site","conversion-tracking",{settings:t})},reducerCallback:I,argsToParams:function(e){return{settings:e}},validateParams:function(e){var t=e.settings;g()(Object(x.isPlainObject)(t),"settings must be a plain object.")}}),U={conversionTracking:{settings:void 0,savedSettings:void 0}},F={saveConversionTrackingSettings:p.a.mark((function e(){var t,r,n;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,N();case 2:return t=e.sent,r=t.select,n=r(a.c).getConversionTrackingSettings(),e.next=7,D.actions.fetchSaveConversionTrackingSettings(n);case 7:return e.abrupt("return",e.sent);case 8:case"end":return e.stop()}}),e)})),setConversionTrackingEnabled:function(e){return{type:"SET_CONVERSION_TRACKING_ENABLED",payload:{enabled:e}}},resetConversionTrackingSettings:function(){return{payload:{},type:"RESET_CONVERSION_TRACKING_SETTINGS"}}},B=Object(L.a)((function(e,t){var r=t.type,n=t.payload;switch(r){case"SET_CONVERSION_TRACKING_ENABLED":e.conversionTracking.settings=e.conversionTracking.settings||{},e.conversionTracking.settings.enabled=!!n.enabled;break;case"RESET_CONVERSION_TRACKING_SETTINGS":e.conversionTracking.settings=e.conversionTracking.savedSettings}})),G={getConversionTrackingSettings:function(e){return e.conversionTracking.settings},isConversionTrackingEnabled:Object(o.createRegistrySelector)((function(e){return function(){return(e(a.c).getConversionTrackingSettings()||{}).enabled}})),haveConversionTrackingSettingsChanged:function(e){var t=e.conversionTracking,r=t.settings,n=t.savedSettings;return!Object(x.isEqual)(r,n)}},V={getConversionTrackingSettings:p.a.mark((function e(){var t,r;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,N();case 2:if(t=e.sent,r=t.select,!r(a.c).getConversionTrackingSettings()){e.next=7;break}return e.abrupt("return");case 7:return e.next=9,M.actions.fetchGetConversionTrackingSettings();case 9:case"end":return e.stop()}}),e)}))},K=Object(o.combineStores)(M,D,{initialState:U,actions:F,controls:{},reducer:B,resolvers:V,selectors:G}),H=(K.initialState,K.actions,K.controls,K.reducer,K.resolvers,K.selectors,K),q=r(25),W=r.n(q),z=r(2);function $(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function J(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?$(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Y={internalServerError:void 0},Z={setInternalServerError:function(e){g()(Object(x.isPlainObject)(e),"internalServerError must be a plain object.");var t=e.title,r=void 0===t?Object(z.__)("Internal Server Error","google-site-kit"):t,n=e.format,o=void 0===n?"small":n,i=e.type;return{type:"SET_SERVER_ERROR",payload:{internalServerError:J({title:r,format:o,type:void 0===i?"win-error":i},W()(e,["title","format","type"]))}}},clearInternalServerError:function(){return{type:"CLEAR_SERVER_ERROR"}}},Q=Object(o.combineStores)({initialState:Y,actions:Z,reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"SET_SERVER_ERROR":return J(J({},e),{},{internalServerError:n.internalServerError});case"CLEAR_SERVER_ERROR":return J(J({},e),{},{internalServerError:void 0});default:return e}},resolvers:{},selectors:{getInternalServerError:function(e){return e.internalServerError}}}),X=(Q.initialState,Q.actions,Q.controls,Q.reducer,Q.resolvers,Q.selectors,Q),ee=r(7),te=r(19),re=r(57),ne=Object(o.createReducer)((function(e,t){e.firstPartyModeSettings=t,e.firstPartyModeSavedSettings=t})),oe=Object(E.a)({baseName:"getFirstPartyModeSettings",controlCallback:function(){return S.a.get("core","site","fpm-settings",void 0,{useCache:!1})},reducerCallback:ne}),ie=Object(E.a)({baseName:"saveFirstPartyModeSettings",controlCallback:function(e){var t=e.settings;return S.a.set("core","site","fpm-settings",{settings:t})},reducerCallback:ne,argsToParams:function(e){return{settings:{isEnabled:(e||{}).isEnabled}}},validateParams:function(e){var t=e.settings;g()(Object(x.isPlainObject)(t),"settings must be a plain object."),g()("boolean"==typeof t.isEnabled,"isEnabled must be a boolean."),g()(1===Object.keys(t).length,"settings must have only the `isEnabled` property.")}}),ae=Object(E.a)({baseName:"getFPMServerRequirementStatus",controlCallback:function(){return S.a.get("core","site","fpm-server-requirement-status",void 0,{useCache:!1})},reducerCallback:ne}),ce={firstPartyModeSettings:void 0,firstPartyModeSavedSettings:void 0},se={saveFirstPartyModeSettings:p.a.mark((function e(){var t,r,n,i,c,s;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o.commonActions.getRegistry();case 2:return r=e.sent,n=r.dispatch,i=r.select,c=i(a.c).getFirstPartyModeSettings(),e.next=8,ie.actions.fetchSaveFirstPartyModeSettings(c);case 8:if(!(null==(s=e.sent)||null===(t=s.response)||void 0===t?void 0:t.isEnabled)){e.next=12;break}return e.next=12,o.commonActions.await(n(ee.a).triggerSurvey("fpm_setup_completed"));case 12:return e.abrupt("return",s);case 13:case"end":return e.stop()}}),e)})),setFirstPartyModeEnabled:function(e){return{type:"SET_FIRST_PARTY_MODE_ENABLED",payload:{isEnabled:e}}},resetFirstPartyModeSettings:function(){return{payload:{},type:"RESET_FIRST_PARTY_MODE_SETTINGS"}}},ue=Object(o.createReducer)((function(e,t){var r=t.type,n=t.payload;switch(r){case"SET_FIRST_PARTY_MODE_ENABLED":e.firstPartyModeSettings=e.firstPartyModeSettings||{},e.firstPartyModeSettings.isEnabled=!!n.isEnabled;break;case"RESET_FIRST_PARTY_MODE_SETTINGS":e.firstPartyModeSettings=e.firstPartyModeSavedSettings}})),le={getFirstPartyModeSettings:p.a.mark((function e(){var t,r;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o.commonActions.getRegistry();case 2:if(t=e.sent,r=t.select,void 0!==r(a.c).getFirstPartyModeSettings()){e.next=8;break}return e.next=8,oe.actions.fetchGetFirstPartyModeSettings();case 8:case"end":return e.stop()}}),e)}))},fe={getFirstPartyModeSettings:function(e){return e.firstPartyModeSettings},isFirstPartyModeEnabled:Object(o.createRegistrySelector)((function(e){return function(){return(e(a.c).getFirstPartyModeSettings()||{}).isEnabled}})),isFPMHealthy:Object(o.createRegistrySelector)((function(e){return function(){return(e(a.c).getFirstPartyModeSettings()||{}).isFPMHealthy}})),isScriptAccessEnabled:Object(o.createRegistrySelector)((function(e){return function(){return(e(a.c).getFirstPartyModeSettings()||{}).isScriptAccessEnabled}})),haveFirstPartyModeSettingsChanged:function(e){var t=e.firstPartyModeSettings,r=e.firstPartyModeSavedSettings;return!Object(x.isEqual)(t,r)},isAnyFirstPartyModeModuleConnected:Object(o.createRegistrySelector)((function(e){return function(){if(!Object(re.b)("firstPartyMode"))return!1;var t=e(te.a).isModuleConnected;return t("analytics-4")||t("ads")}}))},pe=Object(o.combineStores)(oe,ie,ae,{initialState:ce,actions:se,controls:{},reducer:ue,resolvers:le,selectors:fe}),de=(pe.initialState,pe.actions,pe.controls,pe.reducer,pe.resolvers,pe.selectors,pe),ge=r(106),ve=r(157),be=r(369);function ye(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function me(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ye(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ye(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Oe,he=Object(E.a)({baseName:"getHTMLForURL",argsToParams:function(e){return{url:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.url;g()(Object(ge.a)(t),"a valid url is required to fetch HTML.")},controlCallback:(Oe=l()(p.a.mark((function e(t){var r,n,o,i,a;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.url,n={credentials:"omit"},o={tagverify:1,timestamp:Date.now()},e.next=5,fetch(Object(ve.a)(r,o),n);case 5:return i=e.sent,e.prev=6,e.next=9,i.text();case 9:if(""!==(a=e.sent)&&void 0!==a){e.next=12;break}return e.abrupt("return",null);case 12:return e.abrupt("return",a);case 15:return e.prev=15,e.t0=e.catch(6),e.abrupt("return",null);case 18:case"end":return e.stop()}}),e,null,[[6,15]])}))),function(e){return Oe.apply(this,arguments)}),reducerCallback:function(e,t,r){var n=r.url;return me(me({},e),{},{htmlForURL:me(me({},e.htmlForURL),{},s()({},n,t))})}}),Se={resetHTMLForURL:p.a.mark((function e(t){var r,n;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o.commonActions.getRegistry();case 2:return r=e.sent,n=r.dispatch,e.next=6,{payload:{url:t},type:"RESET_HTML_FOR_URL"};case 6:return e.abrupt("return",n(a.c).invalidateResolutionForStoreSelector("getHTMLForURL"));case 7:case"end":return e.stop()}}),e)})),checkForSetupTag:p.a.mark((function e(){return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"CHECK_FOR_SETUP_TAG"};case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)}))},Ee=s()({},"CHECK_FOR_SETUP_TAG",Object(o.createRegistryControl)((function(e){return l()(p.a.mark((function t(){var r,n,o,i,c,s,u,l;return p.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=!1,t.prev=1,t.next=4,S.a.set("core","site","setup-tag");case 4:return c=t.sent,o=c.token,t.next=8,e.select(a.c).getHomeURL();case 8:return s=t.sent,t.next=11,e.dispatch(a.c).fetchGetHTMLForURL(s);case 11:u=t.sent,n=u.response,r=u.error,t.next=19;break;case 16:t.prev=16,t.t0=t.catch(1),r="check_fetch_failed";case 19:return r||(l=Object(be.a)(n,[/<meta name="googlesitekit-setup" content="([a-z0-9-]+)"/]),(i=o===l)||(r="setup_token_mismatch")),t.abrupt("return",{response:i,error:r});case 21:case"end":return t.stop()}}),t,null,[[1,16]])})))}))),je={getHTMLForURL:p.a.mark((function e(t){var r;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o.commonActions.getRegistry();case 2:if(r=e.sent,void 0!==r.select(a.c).getHTMLForURL(t)){e.next=7;break}return e.next=7,he.actions.fetchGetHTMLForURL(t);case 7:case"end":return e.stop()}}),e)}))},ke=Object(o.combineStores)(he,{initialState:{htmlForURL:{}},actions:Se,controls:Ee,reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"RESET_HTML_FOR_URL":var o=n.url;return me(me({},e),{},{htmlForURL:me(me({},e.htmlForURL),{},s()({},o,void 0))});default:return e}},resolvers:je,selectors:{getHTMLForURL:function(e,t){return e.htmlForURL[t]}}}),we=(ke.initialState,ke.actions,ke.controls,ke.reducer,ke.resolvers,ke.selectors,ke),Pe=r(653),Re=Object(E.a)({baseName:"reset",controlCallback:function(){return S.a.set("core","site","reset")}}),Ae={reset:p.a.mark((function e(){return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Re.actions.fetchReset();case 2:case"end":return e.stop()}}),e)}))},Ce={isDoingReset:Object(o.createRegistrySelector)((function(e){return function(){return e(a.c).isFetchingReset()}}))},_e=Object(o.combineStores)(Re,{initialState:{},actions:Ae,selectors:Ce}),Te=(_e.initialState,_e.actions,_e.controls,_e.reducer,_e.resolvers,_e.selectors,_e),xe=r(956);function Le(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ne(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Le(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Le(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Ie=Object(E.a)({baseName:"getAdminBarSettings",controlCallback:function(){return S.a.get("core","site","admin-bar-settings",void 0,{useCache:!1})},reducerCallback:function(e,t){return Ne(Ne({},e),{},{adminBarSettings:Ne(Ne({},e.adminBarSettings||{}),t)})}}),Me=Object(E.a)({baseName:"setAdminBarSettings",controlCallback:function(e){var t=e.enabled;return S.a.set("core","site","admin-bar-settings",{enabled:t})},reducerCallback:function(e,t){return Ne(Ne({},e),{},{adminBarSettings:Ne(Ne({},e.adminBarSettings||{}),t)})},argsToParams:function(e){return{enabled:e.enabled}},validateParams:function(e){var t=e.enabled;g()("boolean"==typeof t,"enabled must be of boolean type")}}),De={adminBarSettings:void 0},Ue={setShowAdminBar:p.a.mark((function e(t){var r,n,o;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Me.actions.fetchSetAdminBarSettings({enabled:t});case 2:return r=e.sent,n=r.response,o=r.error,e.abrupt("return",{response:n,error:o});case 6:case"end":return e.stop()}}),e)}))};var Fe={getAdminBarSettings:p.a.mark((function e(){var t,r;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o.commonActions.getRegistry();case 2:if(t=e.sent,r=t.select,void 0!==r(a.c).getAdminBarSettings()){e.next=8;break}return e.next=8,Ie.actions.fetchGetAdminBarSettings();case 8:case"end":return e.stop()}}),e)}))},Be={getAdminBarSettings:function(e){return e.adminBarSettings},getShowAdminBar:Object(o.createRegistrySelector)((function(e){return function(){var t;return null===(t=e(a.c).getAdminBarSettings())||void 0===t?void 0:t.enabled}}))},Ge=Object(o.combineStores)(Ie,Me,{initialState:De,actions:Ue,controls:{},reducer:function(e,t){return t.type,e},resolvers:Fe,selectors:Be}),Ve=(Ge.initialState,Ge.actions,Ge.controls,Ge.reducer,Ge.resolvers,Ge.selectors,Ge),Ke=r(9);function He(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function qe(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?He(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):He(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var We={selectors:{getGoogleLocaleAwareURL:function(e,t){var r,n=Object(Ke.r)(),o=t||{},i=o.website,a=o.path,c=o.query,s=o.hash,u=o.locale,l=void 0===u?(null===(r=n.match(/^([a-zA-Z]+[-_]?[a-zA-Z]*)/))||void 0===r?void 0:r[0])||n:u;if(!a)return null;var f=new URL(i);f.pathname=a,f.hash=s||"";var p=qe(qe({},c),{},{hl:l});for(var d in p)f.searchParams.set(d,p[d]);return f.toString()},getGoogleSupportURL:Object(o.createRegistrySelector)((function(e){return function(t,r){return e(a.c).getGoogleLocaleAwareURL(qe(qe({},r),{},{website:"https://support.google.com"}))}})),getGooglePrivacyPolicyURL:Object(o.createRegistrySelector)((function(e){return function(){return e(a.c).getGoogleLocaleAwareURL({website:"https://myaccount.google.com",path:"/privacypolicy"})}})),getDocumentationLinkURL:Object(o.createRegistrySelector)((function(e){return function(t,r){g()(r,"A slug is required.");var n=e(a.c).getProxySupportLinkURL();return"".concat(n,"?doc=").concat(encodeURIComponent(r))}})),getErrorTroubleshootingLinkURL:Object(o.createRegistrySelector)((function(e){return function(t,r){g()(r,"An error is required.");var n=e(a.c).getProxySupportLinkURL();return r.id&&!Object(Ke.u)(r.id)?"".concat(n,"?error_id=").concat(encodeURIComponent(r.id)):r.code&&!Object(Ke.u)(r.code)?"".concat(n,"?error_id=").concat(encodeURIComponent(r.code)):"".concat(n,"?error=").concat(encodeURIComponent(r.message))}})),getGoogleTermsURL:Object(o.createRegistrySelector)((function(e){return function(){return e(a.c).getGoogleLocaleAwareURL({website:"https://policies.google.com",path:"/terms"})}}))}};function ze(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function $e(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ze(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ze(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Je=Object(E.a)({baseName:"getDeveloperPluginState",controlCallback:function(){return S.a.get("core","site","developer-plugin",void 0,{useCache:!1})},reducerCallback:function(e,t){return $e($e({},e),{},{developerPluginState:t})}}),Ye={developerPluginState:void 0},Ze={getDeveloperPluginState:p.a.mark((function e(){var t;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o.commonActions.getRegistry();case 2:if(t=e.sent,t.select(a.c).getDeveloperPluginState()){e.next=7;break}return e.next=7,Je.actions.fetchGetDeveloperPluginState();case 7:case"end":return e.stop()}}),e)}))},Qe=Object(o.combineStores)(Je,{initialState:Ye,resolvers:Ze,selectors:{getDeveloperPluginState:function(e){return e.developerPluginState}}}),Xe=(Qe.initialState,Qe.actions,Qe.controls,Qe.reducer,Qe.resolvers,Qe.selectors,Qe),et=r(561),tt=r(62),rt=r(64),nt="accepted",ot="dismissed",it=function(e){return"string"==typeof e},at=Object(E.a)({baseName:"markNotification",controlCallback:function(e){var t=e.notificationID,r=e.notificationState;return S.a.set("core","site","mark-notification",{notificationID:t,notificationState:r})},argsToParams:function(e){return{notificationID:e.notificationID,notificationState:e.notificationState}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.notificationID,r=e.notificationState;g()([nt,ot].includes(r),"notificationState must be accepted or dismissed."),g()(it(t),"a valid notification ID is required to mark a notification.")}}),ct={acceptNotification:Object(tt.f)((function(e){g()(it(e),"a valid notification ID is required to accept a notification.")}),p.a.mark((function e(t){var r,n,o;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,at.actions.fetchMarkNotification({notificationID:t,notificationState:nt});case 2:if(r=e.sent,n=r.response,!(o=r.error)){e.next=8;break}return e.next=8,rt.a.receiveError(o,"acceptNotification",[t]);case 8:return e.abrupt("return",{response:n,error:o});case 9:case"end":return e.stop()}}),e)}))),dismissNotification:Object(tt.f)((function(e){g()(it(e),"a valid notification ID is required to dismiss a notification.")}),p.a.mark((function e(t){var r,n,o;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,at.actions.fetchMarkNotification({notificationID:t,notificationState:ot});case 2:if(r=e.sent,n=r.response,!(o=r.error)){e.next=8;break}return e.next=8,rt.a.receiveError(o,"dismissNotification",[t]);case 8:return e.abrupt("return",{response:n,error:o});case 9:case"end":return e.stop()}}),e)})))},st=Object(o.combineStores)(Object(et.a)("core","site","notifications",{storeName:a.c}),at,{actions:ct}),ut=Object(o.combineStores)(o.commonStore,_,T.a,H,X,de,we,Pe.b,Xe,Te,xe.a,Ve,We,st,O,Object(rt.b)(a.c));ut.initialState,ut.actions,ut.controls,ut.reducer,ut.resolvers,ut.selectors;i.a.registerStore(a.c,ut)},13:function(e,t,r){"use strict";r.d(t,"c",(function(){return n})),r.d(t,"a",(function(){return o})),r.d(t,"b",(function(){return i}));var n="core/site",o="primary",i="secondary"},176:function(e,t,r){"use strict";r.d(t,"a",(function(){return o}));var n=r(374);function o(e){return Object(n.a)(e)}},19:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return o}));var n="core/modules",o="insufficient_module_dependencies"},2:function(e,t){e.exports=googlesitekit.i18n},273:function(e,t,r){"use strict";(function(e){var n=r(55),o=r.n(n),i=r(274),a=e._googlesitekitAPIFetchData||{},c=a.nonce,s=a.nonceEndpoint,u=a.preloadedData,l=a.rootURL;o.a.nonceEndpoint=s,o.a.nonceMiddleware=o.a.createNonceMiddleware(c),o.a.rootURLMiddleware=o.a.createRootURLMiddleware(l),o.a.preloadingMiddleware=Object(i.a)(u),o.a.use(o.a.nonceMiddleware),o.a.use(o.a.mediaUploadMiddleware),o.a.use(o.a.rootURLMiddleware),o.a.use(o.a.preloadingMiddleware),t.default=o.a}).call(this,r(28))},274:function(e,t,r){"use strict";var n=r(262);t.a=function(e){var t=Object.keys(e).reduce((function(t,r){return t[Object(n.getStablePath)(r)]=e[r],t}),{}),r=!1;return function(e,o){if(r)return o(e);setTimeout((function(){r=!0}),3e3);var i=e.parse,a=void 0===i||i,c=e.path;if("string"==typeof e.path){var s,u=(null===(s=e.method)||void 0===s?void 0:s.toUpperCase())||"GET",l=Object(n.getStablePath)(c);if(a&&"GET"===u&&t[l]){var f=Promise.resolve(t[l].body);return delete t[l],f}if("OPTIONS"===u&&t[u]&&t[u][l]){var p=Promise.resolve(t[u][l]);return delete t[u][l],p}}return o(e)}}},3:function(e,t){e.exports=googlesitekit.data},36:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return h})),r.d(t,"b",(function(){return m})),r.d(t,"c",(function(){return O}));var n=r(99),o=e._googlesitekitTrackingData||{},i=o.activeModules,a=void 0===i?[]:i,c=o.isSiteKitScreen,s=o.trackingEnabled,u=o.trackingID,l=o.referenceSiteURL,f=o.userIDHash,p=o.isAuthenticated,d={activeModules:a,trackingEnabled:s,trackingID:u,referenceSiteURL:l,userIDHash:f,isSiteKitScreen:c,userRoles:o.userRoles,isAuthenticated:p,pluginVersion:"1.151.0"},g=Object(n.a)(d),v=g.enableTracking,b=g.disableTracking,y=(g.isTrackingEnabled,g.initializeSnippet),m=g.trackEvent,O=g.trackEventOnce;function h(e){e?v():b()}c&&s&&y()}).call(this,r(28))},369:function(e,t,r){"use strict";r.d(t,"a",(function(){return g})),r.d(t,"b",(function(){return v}));var n=r(5),o=r.n(n),i=r(16),a=r.n(i),c=r(12),s=r.n(c),u=r(14),l=r(273),f=r(106),p=r(157),d=r(13),g=function(e,t){var r=t.find((function(t){return t.test(e)}));return!!r&&r.exec(e)[1]},v=Object(u.memoize)(function(){var e=a()(o.a.mark((function e(t){var r,n,i,a;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.homeURL,n=t.ampMode,s()(Object(f.a)(r),"homeURL must be valid URL"),i=[r],d.b!==n){e.next=14;break}return e.prev=4,e.next=7,Object(l.default)({path:"/wp/v2/posts?per_page=1"}).then((function(e){return e.slice(0,1).map((function(e){return Object(p.a)(e.link,{amp:1})})).pop()}));case 7:(a=e.sent)&&i.push(a),e.next=14;break;case 11:return e.prev=11,e.t0=e.catch(4),e.abrupt("return",i);case 14:return e.abrupt("return",i);case 15:case"end":return e.stop()}}),e,null,[[4,11]])})));return function(t){return e.apply(this,arguments)}}())},37:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return f})),r.d(t,"d",(function(){return m})),r.d(t,"f",(function(){return O})),r.d(t,"c",(function(){return h})),r.d(t,"e",(function(){return S})),r.d(t,"b",(function(){return E}));var n=r(5),o=r.n(n),i=r(16),a=r.n(i),c=(r(27),r(9));function s(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw i}}}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var l,f="googlesitekit_",p="".concat(f).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),d=["sessionStorage","localStorage"],g=[].concat(d),v=function(){var t=a()(o.a.mark((function t(r){var n,i;return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e[r]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,i="__storage_test__",n.setItem(i,i),n.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==n.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function b(){return y.apply(this,arguments)}function y(){return(y=a()(o.a.mark((function t(){var r,n,i;return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===l){t.next=2;break}return t.abrupt("return",l);case 2:r=s(g),t.prev=3,r.s();case 5:if((n=r.n()).done){t.next=15;break}if(i=n.value,!l){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,v(i);case 11:if(!t.sent){t.next=13;break}l=e[i];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),r.e(t.t0);case 20:return t.prev=20,r.f(),t.finish(20);case 23:return void 0===l&&(l=null),t.abrupt("return",l);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var m=function(){var e=a()(o.a.mark((function e(t){var r,n,i,a,c,s,u;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!(r=e.sent)){e.next=10;break}if(!(n=r.getItem("".concat(p).concat(t)))){e.next=10;break}if(i=JSON.parse(n),a=i.timestamp,c=i.ttl,s=i.value,u=i.isError,!a||c&&!(Math.round(Date.now()/1e3)-a<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:u});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),O=function(){var t=a()(o.a.mark((function t(r,n){var i,a,s,u,l,f,d,g,v=arguments;return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=v.length>2&&void 0!==v[2]?v[2]:{},a=i.ttl,s=void 0===a?c.b:a,u=i.timestamp,l=void 0===u?Math.round(Date.now()/1e3):u,f=i.isError,d=void 0!==f&&f,t.next=3,b();case 3:if(!(g=t.sent)){t.next=14;break}return t.prev=5,g.setItem("".concat(p).concat(r),JSON.stringify({timestamp:l,ttl:s,value:n,isError:d})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,r){return t.apply(this,arguments)}}(),h=function(){var t=a()(o.a.mark((function t(r){var n,i;return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(n=t.sent)){t.next=14;break}return t.prev=4,i=r.startsWith(f)?r:"".concat(p).concat(r),n.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),S=function(){var t=a()(o.a.mark((function t(){var r,n,i,a;return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(r=t.sent)){t.next=14;break}for(t.prev=4,n=[],i=0;i<r.length;i++)0===(a=r.key(i)).indexOf(f)&&n.push(a);return t.abrupt("return",n);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),E=function(){var e=a()(o.a.mark((function e(){var t,r,n,i;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!e.sent){e.next=25;break}return e.next=6,S();case 6:t=e.sent,r=s(t),e.prev=8,r.s();case 10:if((n=r.n()).done){e.next=16;break}return i=n.value,e.next=14,h(i);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),r.e(e.t0);case 21:return e.prev=21,r.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,r(28))},39:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return o}));var n="_googlesitekitDataLayer",o="data-googlesitekit-gtag"},45:function(e,t){e.exports=googlesitekit.api},48:function(e,t,r){"use strict";r.d(t,"a",(function(){return h}));var n=r(5),o=r.n(n),i=r(6),a=r.n(i),c=r(12),s=r.n(c),u=r(14),l=r(64),f=r(82),p=r(9);function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){a()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var v=function(e){return e},b=function(){return{}},y=function(){},m=l.a.clearError,O=l.a.receiveError,h=function(e){var t,r,n=o.a.mark(I),i=e.baseName,c=e.controlCallback,l=e.reducerCallback,d=void 0===l?v:l,h=e.argsToParams,S=void 0===h?b:h,E=e.validateParams,j=void 0===E?y:E;s()(i,"baseName is required."),s()("function"==typeof c,"controlCallback is required and must be a function."),s()("function"==typeof d,"reducerCallback must be a function."),s()("function"==typeof S,"argsToParams must be a function."),s()("function"==typeof j,"validateParams must be a function.");try{j(S()),r=!1}catch(e){r=!0}var k=Object(f.b)(i),w=Object(f.a)(i),P="FETCH_".concat(w),R="START_".concat(P),A="FINISH_".concat(P),C="CATCH_".concat(P),_="RECEIVE_".concat(w),T="fetch".concat(k),x="receive".concat(k),L="isFetching".concat(k),N=a()({},L,{});function I(e,t){var r,a;return o.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,{payload:{params:e},type:R};case 2:return n.next=4,m(i,t);case 4:return n.prev=4,n.next=7,{payload:{params:e},type:P};case 7:return r=n.sent,n.next=10,M[x](r,e);case 10:return n.next=12,{payload:{params:e},type:A};case 12:n.next=21;break;case 14:return n.prev=14,n.t0=n.catch(4),a=n.t0,n.next=19,O(a,i,t);case 19:return n.next=21,{payload:{params:e},type:C};case 21:return n.abrupt("return",{response:r,error:a});case 22:case"end":return n.stop()}}),n,null,[[4,14]])}var M=(t={},a()(t,T,(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=S.apply(void 0,t);return j(n),I(n,t)})),a()(t,x,(function(e,t){return s()(void 0!==e,"response is required."),r?(s()(Object(u.isPlainObject)(t),"params is required."),j(t)):t={},{payload:{response:e,params:t},type:_}})),t),D=a()({},P,(function(e){var t=e.payload;return c(t.params)})),U=a()({},L,(function(e){if(void 0===e[L])return!1;var t;try{for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];t=S.apply(void 0,n),j(t)}catch(e){return!1}return!!e[L][Object(p.H)(t)]}));return{initialState:N,actions:M,controls:D,reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case R:var o=n.params;return g(g({},e),{},a()({},L,g(g({},e[L]),{},a()({},Object(p.H)(o),!0))));case _:var i=n.response,c=n.params;return d(e,i,c);case A:var s=n.params;return g(g({},e),{},a()({},L,g(g({},e[L]),{},a()({},Object(p.H)(s),!1))));case C:var u=n.params;return g(g({},e),{},a()({},L,g(g({},e[L]),{},a()({},Object(p.H)(u),!1))));default:return e}},resolvers:{},selectors:U}}},561:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return v}));var n=r(5),o=r.n(n),i=r(6),a=r.n(i),c=r(12),s=r.n(c),u=r(45),l=r.n(u),f=r(3),p=r(48);function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){a()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var v=function(t,r,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},c=i.client,u=void 0===c||c,d=i.server,v=void 0===d||d,b=i.storeName,y=void 0===b?void 0:b;s()(t,"type is required."),s()(r,"identifier is required."),s()(n,"datapoint is required.");var m=y||"".concat(t,"/").concat(r),O={serverNotifications:v?void 0:{},clientNotifications:u?void 0:{}},h=Object(p.a)({baseName:"getNotifications",controlCallback:function(){return l.a.get(t,r,n)},reducerCallback:function(e,t){return g(g({},e),{},{serverNotifications:t.reduce((function(e,t){return g(g({},e),{},a()({},t.id,t))}),{})})}}),S={addNotification:function(e){return s()(e,"notification is required."),{payload:{notification:e},type:"ADD_NOTIFICATION"}},removeNotification:function(e){return s()(e,"id is required."),{payload:{id:e},type:"REMOVE_NOTIFICATION"}}},E={},j=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:O,r=arguments.length>1?arguments[1]:void 0,n=r.type,o=r.payload;switch(n){case"ADD_NOTIFICATION":var i=o.notification;return g(g({},t),{},{clientNotifications:g(g({},t.clientNotifications||{}),{},a()({},i.id,i))});case"REMOVE_NOTIFICATION":var c=o.id;if(void 0===t.clientNotifications||void 0===t.clientNotifications[c])return void 0!==t.serverNotifications&&void 0!==t.serverNotifications[c]&&e.console.warn('Cannot remove server-side notification with ID "'.concat(c,'"; this may be changed in a future release.')),t;var s=g({},t.clientNotifications);return delete s[c],g(g({},t),{},{clientNotifications:s});default:return t}},k={getNotifications:o.a.mark((function e(){var t;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.commonActions.getRegistry();case 2:if(t=e.sent,t.select(m).getNotifications()){e.next=7;break}return e.next=7,h.actions.fetchGetNotifications();case 7:case"end":return e.stop()}}),e)}))};v||delete k.getNotifications;var w={getNotifications:function(e){var t=e.serverNotifications,r=e.clientNotifications;return void 0===t&&void 0===r?t:Object.values(g(g({},t||{}),r||{}))}},P=Object(f.combineStores)(h,{initialState:O,actions:S,controls:E,reducer:j,resolvers:k,selectors:w});return g(g({},P),{},{STORE_NAME:m})}}).call(this,r(28))},57:function(e,t,r){"use strict";(function(e){var n,o;r.d(t,"a",(function(){return i})),r.d(t,"b",(function(){return a}));var i=new Set((null===(n=e)||void 0===n||null===(o=n._googlesitekitBaseData)||void 0===o?void 0:o.enabledFeatures)||[]),a=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;return t instanceof Set&&t.has(e)}}).call(this,r(28))},59:function(e,t,r){"use strict";r.d(t,"a",(function(){return o}));var n=r(39);function o(e){return function(){e[n.a]=e[n.a]||[],e[n.a].push(arguments)}}},598:function(e,t,r){"use strict";function n(e){if(void 0!==e)return!e}r.d(t,"a",(function(){return n}))},62:function(e,t,r){"use strict";r.d(t,"a",(function(){return P})),r.d(t,"b",(function(){return R})),r.d(t,"c",(function(){return A})),r.d(t,"d",(function(){return _})),r.d(t,"e",(function(){return T})),r.d(t,"g",(function(){return L})),r.d(t,"f",(function(){return N}));var n,o=r(5),i=r.n(o),a=r(27),c=r.n(a),s=r(6),u=r.n(s),l=r(12),f=r.n(l),p=r(63),d=r.n(p),g=r(14),v=r(116);function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){u()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var m=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.reduce((function(e,t){return y(y({},e),t)}),{}),o=t.reduce((function(e,t){return[].concat(c()(e),c()(Object.keys(t)))}),[]),i=C(o);return f()(0===i.length,"collect() cannot accept collections with duplicate keys. Your call to collect() contains the following duplicated functions: ".concat(i.join(", "),". Check your data stores for duplicates.")),n},O=m,h=m,S=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n,o=[].concat(t);return"function"!=typeof o[0]&&(n=o.shift()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return o.reduce((function(e,r){return r(e,t)}),e)}},E=m,j=m,k=m,w=function(e){return e},P=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=k.apply(void 0,c()(t.map((function(e){return e.initialState||{}}))));return{initialState:n,controls:h.apply(void 0,c()(t.map((function(e){return e.controls||{}})))),actions:O.apply(void 0,c()(t.map((function(e){return e.actions||{}})))),reducer:S.apply(void 0,[n].concat(c()(t.map((function(e){return e.reducer||w}))))),resolvers:E.apply(void 0,c()(t.map((function(e){return e.resolvers||{}})))),selectors:j.apply(void 0,c()(t.map((function(e){return e.selectors||{}}))))}},R={getRegistry:function(){return{payload:{},type:"GET_REGISTRY"}},await:i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{payload:{value:t},type:"AWAIT"});case 1:case"end":return e.stop()}}),e)}))},A=(n={},u()(n,"GET_REGISTRY",Object(v.a)((function(e){return function(){return e}}))),u()(n,"AWAIT",(function(e){return e.payload.value})),n),C=function(e){for(var t=[],r={},n=0;n<e.length;n++){var o=e[n];r[o]=r[o]>=1?r[o]+1:1,r[o]>1&&t.push(o)}return t},_={actions:R,controls:A,reducer:w},T=function(e){return function(t){return x(e(t))}},x=d()((function(e){return Object(g.mapValues)(e,(function(e,t){return function(){var r=e.apply(void 0,arguments);return f()(void 0!==r,"".concat(t,"(...) is not resolved")),r}}))}));function L(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.negate,n=void 0!==r&&r,o=Object(v.b)((function(t){return function(r){var o=!n,i=!!n;try{for(var a=arguments.length,c=new Array(a>1?a-1:0),s=1;s<a;s++)c[s-1]=arguments[s];return e.apply(void 0,[t,r].concat(c)),o}catch(e){return i}}})),i=Object(v.b)((function(t){return function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];e.apply(void 0,[t,r].concat(o))}}));return{safeSelector:o,dangerousSelector:i}}function N(e,t){return f()("function"==typeof e,"a validator function is required."),f()("function"==typeof t,"an action creator function is required."),f()("Generator"!==e[Symbol.toStringTag]&&"GeneratorFunction"!==e[Symbol.toStringTag],"an action’s validator function must not be a generator."),function(){return e.apply(void 0,arguments),t.apply(void 0,arguments)}}},64:function(e,t,r){"use strict";r.d(t,"a",(function(){return b})),r.d(t,"b",(function(){return y}));var n=r(6),o=r.n(n),i=r(33),a=r.n(i),c=r(116),s=r(12),u=r.n(s),l=r(96),f=r.n(l),p=r(9);function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){o()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function v(e,t){if(t&&Array.isArray(t)){var r=t.map((function(e){return"object"===a()(e)?Object(p.H)(e):e}));return"".concat(e,"::").concat(f()(JSON.stringify(r)))}return e}var b={receiveError:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(e,"error is required."),u()(t,"baseName is required."),u()(r&&Array.isArray(r),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:r}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return u()(e,"baseName is required."),u()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function y(e){u()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(r,"selectorName is required."),t.getError(e,r,n)},getErrorForAction:function(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(r,"actionName is required."),t.getError(e,r,n)},getError:function(e,t,r){var n=e.errors;return u()(t,"baseName is required."),n[v(t,r)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var r=Object.keys(e.errors).find((function(r){return e.errors[r]===t}));return r?{baseName:r.substring(0,r.indexOf("::")),args:e.errorArgs[r]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(r,n){var o=t(e).getMetaDataForError(n);if(o){var i=o.baseName,a=o.args;if(!!t(e)[i])return{storeName:e,name:i,args:a}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:b,controls:{},reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"RECEIVE_ERROR":var i=n.baseName,a=n.args,c=n.error,s=v(i,a);return g(g({},e),{},{errors:g(g({},e.errors||{}),{},o()({},s,c)),errorArgs:g(g({},e.errorArgs||{}),{},o()({},s,a))});case"CLEAR_ERROR":var u=n.baseName,l=n.args,f=g({},e),p=v(u,l);return f.errors=g({},e.errors||{}),f.errorArgs=g({},e.errorArgs||{}),delete f.errors[p],delete f.errorArgs[p],f;case"CLEAR_ERRORS":var d=n.baseName,b=g({},e);if(d)for(var y in b.errors=g({},e.errors||{}),b.errorArgs=g({},e.errorArgs||{}),b.errors)(y===d||y.startsWith("".concat(d,"::")))&&(delete b.errors[y],delete b.errorArgs[y]);else b.errors={},b.errorArgs={};return b;default:return e}},resolvers:{},selectors:t}}},653:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return P}));var n=r(15),o=r.n(n),i=r(25),a=r.n(i),c=r(5),s=r.n(c),u=r(6),l=r.n(u),f=r(12),p=r.n(f),d=r(775),g=r.n(d),v=r(157),b=r(409),y=r(3),m=r(13),O=r(9),h=r(598);function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach((function(t){l()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function j(e){return Object(y.createRegistrySelector)((function(t){return function(){return(t(m.c).getSiteInfo()||{})[e]}}))}var k={siteInfo:void 0,permaLink:!1},w={receiveSiteInfo:function(e){return p()(e,"siteInfo is required."),{payload:{siteInfo:e},type:"RECEIVE_SITE_INFO"}},receivePermaLinkParam:function(e){return p()(e,"permaLink is required."),{payload:{permaLink:e},type:"RECEIVE_PERMALINK_PARAM"}},setSiteKitAutoUpdatesEnabled:function(e){return p()("boolean"==typeof e,"siteKitAutoUpdatesEnabled must be a boolean."),{payload:{siteKitAutoUpdatesEnabled:e},type:"SET_SITE_KIT_AUTO_UPDATES_ENABLED"}},setKeyMetricsSetupCompletedBy:function(e){return p()("number"==typeof e,"keyMetricsSetupCompletedBy must be a number."),{payload:{keyMetricsSetupCompletedBy:e},type:"SET_KEY_METRICS_SETUP_COMPLETED_BY"}},setSetupErrorCode:function(e){return p()("string"==typeof e||null===e,"setupErrorCode must be a string or null."),{payload:{setupErrorCode:e},type:"SET_SETUP_ERROR_CODE"}}},P={},R={getSiteInfo:s.a.mark((function t(){var r,n,o,i,a,c,u,l,f,p,d,g,v,b,O,h,S,E,j,k,P,R,A,C,_,T,x,L,N,I,M,D,U,F;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,y.commonActions.getRegistry();case 2:if(!t.sent.select(m.c).getSiteInfo()){t.next=5;break}return t.abrupt("return");case 5:if(e._googlesitekitBaseData&&e._googlesitekitEntityData){t.next=8;break}return e.console.error("Could not load core/site info."),t.abrupt("return");case 8:return r=e._googlesitekitBaseData,n=r.adminURL,o=r.ampMode,i=r.homeURL,a=r.proxyPermissionsURL,c=r.proxySetupURL,u=r.referenceSiteURL,l=r.setupErrorCode,f=r.setupErrorMessage,p=r.setupErrorRedoURL,d=r.siteName,g=r.siteLocale,v=r.timezone,b=r.usingProxy,O=r.webStoriesActive,h=r.proxySupportLinkURL,S=r.widgetsAdminURL,E=r.postTypes,j=r.wpVersion,k=r.updateCoreURL,P=r.changePluginAutoUpdatesCapacity,R=r.siteKitAutoUpdatesEnabled,A=r.pluginBasename,C=r.productPostType,_=r.keyMetricsSetupCompletedBy,T=r.keyMetricsSetupNew,x=r.consentModeRegions,L=r.anyoneCanRegister,N=r.isMultisite,I=e._googlesitekitEntityData,M=I.currentEntityID,D=I.currentEntityTitle,U=I.currentEntityType,F=I.currentEntityURL,t.next=12,w.receiveSiteInfo({adminURL:n,ampMode:o,currentEntityID:M,currentEntityTitle:D,currentEntityType:U,currentEntityURL:F,homeURL:i,proxyPermissionsURL:a,proxySetupURL:c,referenceSiteURL:u,setupErrorCode:l,setupErrorMessage:f,setupErrorRedoURL:p,siteName:d,siteLocale:g,timezone:v,postTypes:E,usingProxy:!!b,webStoriesActive:O,proxySupportLinkURL:h,widgetsAdminURL:S,wpVersion:j,updateCoreURL:k,changePluginAutoUpdatesCapacity:P,siteKitAutoUpdatesEnabled:R,pluginBasename:A,productPostType:C,keyMetricsSetupCompletedBy:_,keyMetricsSetupNew:T,consentModeRegions:x,anyoneCanRegister:L,isMultisite:N});case 12:case"end":return t.stop()}}),t)}))},A={getSiteInfo:function(e){return e.siteInfo},getAdminURL:Object(y.createRegistrySelector)((function(e){return function(t,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=e(m.c).getSiteInfo()||{},i=o.adminURL;if(void 0===i||void 0===r)return i;var c="/"===i[i.length-1]?i:"".concat(i,"/"),s=r,u="admin.php";if(-1!==r.indexOf(".php?")){var l=r.split("?");if(!(s=g.a.parse(l.pop()).page))return i;u=l.shift()}n.page;var f=a()(n,["page"]);return Object(v.a)("".concat(c).concat(u),E({page:s},f))}})),getAMPMode:j("ampMode"),getCurrentEntityID:j("currentEntityID"),getCurrentEntityTitle:j("currentEntityTitle"),getCurrentEntityType:j("currentEntityType"),getCurrentEntityURL:j("currentEntityURL"),getHomeURL:j("homeURL"),getReferenceSiteURL:j("referenceSiteURL"),getProxySetupURL:j("proxySetupURL"),getProxyPermissionsURL:j("proxyPermissionsURL"),getCurrentReferenceURL:Object(y.createRegistrySelector)((function(e){return function(){var t=e(m.c).getCurrentEntityURL();return null!==t?t:e(m.c).getReferenceSiteURL()}})),isAMP:Object(y.createRegistrySelector)((function(e){return function(){var t=e(m.c).getAMPMode();if(void 0!==t)return!!t}})),isPrimaryAMP:Object(y.createRegistrySelector)((function(e){return function(){var t=e(m.c).getAMPMode();if(void 0!==t)return t===m.a}})),isSecondaryAMP:Object(y.createRegistrySelector)((function(e){return function(){var t=e(m.c).getAMPMode();if(void 0!==t)return t===m.b}})),getAdminSettingsURL:Object(y.createRegistrySelector)((function(e){return function(){var t=e(m.c).getAdminURL(),r=e(m.c).isMultisite();if(void 0!==t&&void 0!==r)return new URL(!0===r?"network/settings.php":"options-general.php",t).href}})),getTimezone:j("timezone"),isUsingProxy:j("usingProxy"),getSiteName:j("siteName"),getSiteLocale:Object(y.createRegistrySelector)((function(e){return function(){var t,r;return null===(t=e(m.c).getSiteInfo())||void 0===t||null===(r=t.siteLocale)||void 0===r?void 0:r.replace("_","-")}})),getSetupErrorCode:j("setupErrorCode"),getSetupErrorMessage:j("setupErrorMessage"),getSetupErrorRedoURL:j("setupErrorRedoURL"),getProxySupportLinkURL:j("proxySupportLinkURL"),getWidgetsAdminURL:j("widgetsAdminURL"),getPostTypes:j("postTypes"),getPermaLinkParam:function(t){if(t.permaLink)return t.permaLink;var r=Object(b.a)(e.location.href,"permaLink");return r||!1},isWebStoriesActive:j("webStoriesActive"),isSiteURLMatch:Object(y.createRegistrySelector)((function(e){return function(t,r){var n=e(m.c).getReferenceSiteURL();return Object(O.A)(n)===Object(O.A)(r)}})),getSiteURLPermutations:Object(y.createRegistrySelector)((function(e){return function(){var t=e(m.c).getReferenceSiteURL(),r=[],n=new URL(t);return n.hostname=n.hostname.replace(/^www\./i,""),n.protocol="http",r.push(Object(O.K)(n)),n.protocol="https",r.push(Object(O.K)(n)),n.hostname="www."+n.hostname,r.push(Object(O.K)(n)),n.protocol="http",r.push(Object(O.K)(n)),r}})),getWPVersion:j("wpVersion"),getUpdateCoreURL:j("updateCoreURL"),hasChangePluginAutoUpdatesCapacity:j("changePluginAutoUpdatesCapacity"),getSiteKitAutoUpdatesEnabled:j("siteKitAutoUpdatesEnabled"),getPluginBasename:j("pluginBasename"),getKeyMetricsSetupCompletedBy:j("keyMetricsSetupCompletedBy"),getKeyMetricsSetupNew:j("keyMetricsSetupNew"),hasMinimumWordPressVersion:Object(y.createRegistrySelector)((function(e){return function(t,r){p()(r,"minimumWPVersion is required.");var n=e(m.c).getWPVersion()||{},i=n.major,a=n.minor;if(void 0!==i&&void 0!==a){var c=r.split(".").map((function(e){return parseInt(e,10)})),s=o()(c,2),u=s[0],l=s[1];return u<i||u===i&&(void 0===l?0:l)<=a}}})),getProductPostType:j("productPostType"),isKeyMetricsSetupCompleted:function(e){return Object(h.a)(Object(h.a)(A.getKeyMetricsSetupCompletedBy(e)))},getConsentModeRegions:j("consentModeRegions"),getAnyoneCanRegister:j("anyoneCanRegister"),isMultisite:j("isMultisite")};t.b={initialState:k,actions:w,controls:P,reducer:function(e,t){var r=t.payload;switch(t.type){case"RECEIVE_SITE_INFO":var n=r.siteInfo,o=n.adminURL,i=n.ampMode,a=n.currentEntityID,c=n.currentEntityTitle,s=n.currentEntityType,u=n.currentEntityURL,l=n.homeURL,f=n.proxyPermissionsURL,p=n.proxySetupURL,d=n.referenceSiteURL,g=n.setupErrorCode,v=n.setupErrorMessage,b=n.setupErrorRedoURL,y=n.siteName,m=n.siteLocale,O=n.timezone,h=n.usingProxy,S=n.webStoriesActive,j=n.proxySupportLinkURL,k=n.widgetsAdminURL,w=n.postTypes,P=n.wpVersion,R=n.updateCoreURL,A=n.changePluginAutoUpdatesCapacity,C=n.siteKitAutoUpdatesEnabled,_=n.pluginBasename,T=n.productPostType,x=n.keyMetricsSetupCompletedBy,L=n.keyMetricsSetupNew,N=n.consentModeRegions,I=n.anyoneCanRegister,M=n.isMultisite;return E(E({},e),{},{siteInfo:{adminURL:o,ampMode:i,currentEntityID:parseInt(a,10),currentEntityTitle:c,currentEntityType:s,currentEntityURL:u,homeURL:l,proxyPermissionsURL:f,proxySetupURL:p,referenceSiteURL:d,setupErrorCode:g,setupErrorMessage:v,setupErrorRedoURL:b,siteName:y,siteLocale:m,timezone:O,usingProxy:h,webStoriesActive:S,proxySupportLinkURL:j,widgetsAdminURL:k,postTypes:w,wpVersion:P,updateCoreURL:R,changePluginAutoUpdatesCapacity:A,siteKitAutoUpdatesEnabled:C,pluginBasename:_,productPostType:T,keyMetricsSetupCompletedBy:x,keyMetricsSetupNew:L,consentModeRegions:N,anyoneCanRegister:I,isMultisite:M}});case"RECEIVE_PERMALINK_PARAM":var D=r.permaLink;return E(E({},e),{},{permaLink:D});case"SET_SITE_KIT_AUTO_UPDATES_ENABLED":var U=r.siteKitAutoUpdatesEnabled;return E(E({},e),{},{siteInfo:E(E({},e.siteInfo),{},{siteKitAutoUpdatesEnabled:U})});case"SET_KEY_METRICS_SETUP_COMPLETED_BY":var F=r.keyMetricsSetupCompletedBy;return E(E({},e),{},{siteInfo:E(E({},e.siteInfo),{},{keyMetricsSetupCompletedBy:F})});case"SET_SETUP_ERROR_CODE":var B=r.setupErrorCode;return E(E({},e),{},{siteInfo:E(E({},e.siteInfo),{},{setupErrorCode:B})});default:return e}},resolvers:R,selectors:A}}).call(this,r(28))},7:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return o})),r.d(t,"e",(function(){return i})),r.d(t,"d",(function(){return a})),r.d(t,"c",(function(){return c})),r.d(t,"H",(function(){return s})),r.d(t,"M",(function(){return u})),r.d(t,"O",(function(){return l})),r.d(t,"K",(function(){return f})),r.d(t,"L",(function(){return p})),r.d(t,"J",(function(){return d})),r.d(t,"I",(function(){return g})),r.d(t,"N",(function(){return v})),r.d(t,"f",(function(){return b})),r.d(t,"g",(function(){return y})),r.d(t,"h",(function(){return m})),r.d(t,"j",(function(){return O})),r.d(t,"l",(function(){return h})),r.d(t,"m",(function(){return S})),r.d(t,"n",(function(){return E})),r.d(t,"o",(function(){return j})),r.d(t,"q",(function(){return k})),r.d(t,"s",(function(){return w})),r.d(t,"r",(function(){return P})),r.d(t,"t",(function(){return R})),r.d(t,"w",(function(){return A})),r.d(t,"u",(function(){return C})),r.d(t,"v",(function(){return _})),r.d(t,"x",(function(){return T})),r.d(t,"y",(function(){return x})),r.d(t,"A",(function(){return L})),r.d(t,"B",(function(){return N})),r.d(t,"C",(function(){return I})),r.d(t,"D",(function(){return M})),r.d(t,"k",(function(){return D})),r.d(t,"F",(function(){return U})),r.d(t,"z",(function(){return F})),r.d(t,"G",(function(){return B})),r.d(t,"E",(function(){return G})),r.d(t,"i",(function(){return V})),r.d(t,"p",(function(){return K})),r.d(t,"Q",(function(){return H})),r.d(t,"P",(function(){return q}));var n="core/user",o="connected_url_mismatch",i="__global",a="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",u="googlesitekit_setup",l="googlesitekit_view_dashboard",f="googlesitekit_manage_options",p="googlesitekit_read_shared_module_data",d="googlesitekit_manage_module_sharing_options",g="googlesitekit_delegate_module_sharing_management",v="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",y="kmAnalyticsEngagedTrafficSource",m="kmAnalyticsLeastEngagingPages",O="kmAnalyticsNewVisitors",h="kmAnalyticsPopularAuthors",S="kmAnalyticsPopularContent",E="kmAnalyticsPopularProducts",j="kmAnalyticsReturningVisitors",k="kmAnalyticsTopCities",w="kmAnalyticsTopCitiesDrivingLeads",P="kmAnalyticsTopCitiesDrivingAddToCart",R="kmAnalyticsTopCitiesDrivingPurchases",A="kmAnalyticsTopDeviceDrivingPurchases",C="kmAnalyticsTopConvertingTrafficSource",_="kmAnalyticsTopCountries",T="kmAnalyticsTopPagesDrivingLeads",x="kmAnalyticsTopRecentTrendingPages",L="kmAnalyticsTopTrafficSource",N="kmAnalyticsTopTrafficSourceDrivingAddToCart",I="kmAnalyticsTopTrafficSourceDrivingLeads",M="kmAnalyticsTopTrafficSourceDrivingPurchases",D="kmAnalyticsPagesPerVisit",U="kmAnalyticsVisitLength",F="kmAnalyticsTopReturningVisitorPages",B="kmSearchConsolePopularKeywords",G="kmAnalyticsVisitsPerVisitor",V="kmAnalyticsMostEngagingPages",K="kmAnalyticsTopCategories",H=[b,y,m,O,h,S,E,j,K,k,w,P,R,A,C,_,x,L,N,D,U,F,G,V,K],q=[].concat(H,[B])},75:function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return c}));var n=r(33),o=r.n(n),i=r(84),a=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:i.a.sanitize(e,t)}};function c(e){var t,r="object"===o()(e)?e.toString():e;return null==r||null===(t=r.replace)||void 0===t?void 0:t.call(r,/\/+$/,"")}},80:function(e,t,r){"use strict";r.d(t,"b",(function(){return o})),r.d(t,"a",(function(){return i})),r.d(t,"c",(function(){return a})),r.d(t,"d",(function(){return c}));var n=r(106);function o(e){try{return new URL(e).pathname}catch(e){}return null}function i(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function a(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(n.a)(e))return e;if(e.length<=t)return e;var r=new URL(e),o=e.replace(r.origin,"");if(o.length<t)return o;var i=o.length-Math.floor(t)+1;return"…"+o.substr(i)}},82:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"a",(function(){return o})),r.d(t,"c",(function(){return i}));var n=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},o=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function i(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},83:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return j})),r.d(t,"d",(function(){return k})),r.d(t,"e",(function(){return P})),r.d(t,"c",(function(){return R})),r.d(t,"b",(function(){return A}));var n=r(15),o=r.n(n),i=r(33),a=r.n(i),c=r(6),s=r.n(c),u=r(25),l=r.n(u),f=r(14),p=r(63),d=r.n(p),g=r(2);function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var y=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=h(e,t),n=r.formatUnit,o=r.formatDecimal;try{return n()}catch(e){return o()}},m=function(e){var t=O(e),r=t.hours,n=t.minutes,o=t.seconds;return o=("0"+o).slice(-2),n=("0"+n).slice(-2),"00"===(r=("0"+r).slice(-2))?"".concat(n,":").concat(o):"".concat(r,":").concat(n,":").concat(o)},O=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},h=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=O(e),n=r.hours,o=r.minutes,i=r.seconds;return{hours:n,minutes:o,seconds:i,formatUnit:function(){var r=t.unitDisplay,a=b(b({unitDisplay:void 0===r?"short":r},l()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?P(i,b(b({},a),{},{unit:"second"})):Object(g.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(g._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?P(i,b(b({},a),{},{unit:"second"})):"",o?P(o,b(b({},a),{},{unit:"minute"})):"",n?P(n,b(b({},a),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(g.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(g.__)("%ds","google-site-kit"),i);if(0===e)return t;var r=Object(g.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(g.__)("%dm","google-site-kit"),o),a=Object(g.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(g.__)("%dh","google-site-kit"),n);return Object(g.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(g._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?t:"",o?r:"",n?a:"").trim()}}},S=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},E=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(g.sprintf)(// translators: %s: an abbreviated number in millions.
Object(g.__)("%sM","google-site-kit"),P(S(e),e%10==0?{}:t)):1e4<=e?Object(g.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(g.__)("%sK","google-site-kit"),P(S(e))):1e3<=e?Object(g.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(g.__)("%sK","google-site-kit"),P(S(e),e%10==0?{}:t)):P(e,{signDisplay:"never",maximumFractionDigits:1})};function j(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(f.isPlainObject)(e)&&(t=b({},e)),t}function k(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(f.isFinite)(e)?e:Number(e),Object(f.isFinite)(e)||(console.warn("Invalid number",e,a()(e)),e=0);var r=j(t),n=r.style,o=void 0===n?"metric":n;return"metric"===o?E(e):"duration"===o?y(e,r):"durationISO"===o?m(e):P(e,r)}var w=d()(console.warn),P=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.locale,n=void 0===r?A():r,i=l()(t,["locale"]);try{return new Intl.NumberFormat(n,i).format(e)}catch(t){w("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(n),", ").concat(JSON.stringify(i)," ).format( ").concat(a()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],u={},f=0,p=Object.entries(i);f<p.length;f++){var d=o()(p[f],2),g=d[0],v=d[1];c[g]&&v===c[g]||(s.includes(g)||(u[g]=v))}try{return new Intl.NumberFormat(n,u).format(e)}catch(t){return new Intl.NumberFormat(n).format(e)}},R=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.locale,n=void 0===r?A():r,o=t.style,i=void 0===o?"long":o,a=t.type,c=void 0===a?"conjunction":a;if(Intl.ListFormat){var s=new Intl.ListFormat(n,{style:i,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var u=Object(g.__)(", ","google-site-kit");return e.join(u)},A=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,r=Object(f.get)(t,["_googlesitekitLegacyData","locale"]);if(r){var n=r.match(/^(\w{2})?(_)?(\w{2})/);if(n&&n[0])return n[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,r(28))},84:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return o}));var n=r(149),o=r.n(n)()(e)}).call(this,r(28))},85:function(e,t,r){"use strict";(function(e){var n=r(1),o=r.n(n),i=r(11),a=r.n(i);function ChangeArrow(t){var r=t.direction,n=t.invertColor,o=t.width,i=t.height;return e.createElement("svg",{className:a()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(r),{"googlesitekit-change-arrow--inverted-color":n}),width:o,height:i,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:o.a.string,invertColor:o.a.bool,width:o.a.number,height:o.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,r(4))},89:function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));var n=r(12),o=r.n(n),i=function(e,t){var r=t.dateRangeLength;o()(Array.isArray(e),"report must be an array to partition."),o()(Number.isInteger(r)&&r>0,"dateRangeLength must be a positive integer.");var n=-1*r;return{currentRange:e.slice(n),compareRange:e.slice(2*n,n)}}},9:function(e,t,r){"use strict";r.d(t,"I",(function(){return o.b})),r.d(t,"J",(function(){return o.c})),r.d(t,"F",(function(){return i.a})),r.d(t,"K",(function(){return i.b})),r.d(t,"H",(function(){return l})),r.d(t,"m",(function(){return f.a})),r.d(t,"B",(function(){return f.d})),r.d(t,"C",(function(){return f.e})),r.d(t,"y",(function(){return f.c})),r.d(t,"r",(function(){return f.b})),r.d(t,"z",(function(){return v})),r.d(t,"j",(function(){return b})),r.d(t,"i",(function(){return y})),r.d(t,"d",(function(){return j})),r.d(t,"c",(function(){return k})),r.d(t,"e",(function(){return w})),r.d(t,"b",(function(){return P})),r.d(t,"a",(function(){return R})),r.d(t,"f",(function(){return A})),r.d(t,"n",(function(){return C})),r.d(t,"w",(function(){return _})),r.d(t,"p",(function(){return T})),r.d(t,"G",(function(){return x})),r.d(t,"s",(function(){return L})),r.d(t,"v",(function(){return N})),r.d(t,"k",(function(){return I})),r.d(t,"o",(function(){return M.b})),r.d(t,"h",(function(){return M.a})),r.d(t,"t",(function(){return D.b})),r.d(t,"q",(function(){return D.a})),r.d(t,"A",(function(){return D.c})),r.d(t,"x",(function(){return U})),r.d(t,"u",(function(){return F})),r.d(t,"E",(function(){return V})),r.d(t,"D",(function(){return K.a})),r.d(t,"g",(function(){return H})),r.d(t,"L",(function(){return q})),r.d(t,"l",(function(){return W}));var n=r(14),o=r(36),i=r(75),a=r(33),c=r.n(a),s=r(96),u=r.n(s),l=function(e){return u()(JSON.stringify(function e(t){var r={};return Object.keys(t).sort().forEach((function(n){var o=t[n];o&&"object"===c()(o)&&!Array.isArray(o)&&(o=e(o)),r[n]=o})),r}(e)))};r(97);var f=r(83);function p(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function d(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function g(e){return e.replace(/\n/gi,"<br>")}function v(e){for(var t=e,r=0,n=[p,d,g];r<n.length;r++){t=(0,n[r])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},y=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},m=r(15),O=r.n(m),h=r(12),S=r.n(h),E=r(2),j="Invalid dateString parameter, it must be a string.",k='Invalid date range, it must be a string with the format "last-x-days".',w=60,P=60*w,R=24*P,A=7*R;function C(){var e=function(e){return Object(E.sprintf)(
/* translators: %s: number of days */
Object(E._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function _(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(n.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var r=new Date(e);return Object(n.isDate)(r)&&!isNaN(r)}function T(e){S()(Object(n.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),r="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,r.length<2?"0".concat(r):r].join("-")}function x(e){S()(_(e),j);var t=e.split("-"),r=O()(t,3),n=r[0],o=r[1],i=r[2];return new Date(n,o-1,i)}function L(e,t){return T(I(e,t*R))}function N(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function I(e,t){S()(_(e)||Object(n.isDate)(e)&&!isNaN(e),j);var r=_(e)?Date.parse(e):e.getTime();return new Date(r-1e3*t)}var M=r(98),D=r(80);function U(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function F(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var B=r(27),G=r.n(B),V=function(e){return Array.isArray(e)?G()(e).sort():e},K=r(89);function H(e,t){var r=function(e){return"0"===e||0===e};if(r(e)&&r(t))return 0;if(r(e)||Number.isNaN(e))return null;var n=(t-e)/e;return Number.isNaN(n)||!Number.isFinite(n)?null:n}var q=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},W=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(n.unescape)(t)}},955:function(e,t,r){"use strict";(function(e){var n,o=r(5),i=r.n(o),a=r(16),c=r.n(a),s=r(12),u=r.n(s),l=r(14),f=r(45),p=r.n(f),d=r(3),g=r(48),v=r(176),b=r(13),y=r(7),m=r(64),O=m.a.clearError,h=m.a.receiveError,S=d.commonActions.getRegistry,E=Object(v.a)((function(e,t){e.consentMode.settings=t})),j=Object(g.a)({baseName:"getConsentModeSettings",controlCallback:function(){return p.a.get("core","site","consent-mode",null,{useCache:!1})},reducerCallback:E}),k=Object(g.a)({baseName:"saveConsentModeSettings",controlCallback:function(e){var t=e.settings;return p.a.set("core","site","consent-mode",{settings:t})},reducerCallback:E,argsToParams:function(e){return{settings:e}},validateParams:function(e){var t=e.settings;u()(Object(l.isPlainObject)(t),"settings must be a plain object.")}}),w=Object(g.a)({baseName:"getConsentAPIInfo",controlCallback:function(){return p.a.get("core","site","consent-api-info",null,{useCache:!1})},reducerCallback:Object(v.a)((function(e,t){e.consentMode.apiInfo=t}))}),P=Object(g.a)({baseName:"installActivateWPConsentAPI",controlCallback:(n=c()(i.a.mark((function t(r){var n,o,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=r.nonce,(o=new FormData).append("action","install_activate_wp_consent_api"),o.append("_ajax_nonce",n),t.next=6,fetch(e.ajaxurl,{method:"POST",credentials:"same-origin",body:o});case 6:return a=t.sent,t.abrupt("return",a.json());case 8:case"end":return t.stop()}}),t)}))),function(e){return n.apply(this,arguments)}),argsToParams:function(e){return{nonce:e.nonce}},validateParams:function(e){var t=e.nonce;u()("string"==typeof t,"nonce must be a string.")}}),R=Object(g.a)({baseName:"activateConsentAPI",controlCallback:function(){return p.a.set("core","site","consent-api-activate",null,{useCache:!1})}}),A=Object(g.a)({baseName:"getAdsMeasurementStatus",controlCallback:function(e){var t=e.useCache;return p.a.get("core","site","ads-measurement-status",null,{useCache:t})},reducerCallback:Object(v.a)((function(e,t,r){var n=r.useCache;e.consentMode.adsConnected=t.connected,n||(e.consentMode.adsConnectedUncached=t.connected)})),argsToParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.useCache;return{useCache:t}},validateParams:function(e){var t=e.useCache;u()("boolean"==typeof t,"useCache must be a boolean.")}}),C={consentMode:{settings:void 0,apiInfo:void 0,apiInstallResponse:void 0,isApiFetching:void 0,adsConnected:void 0,adsConnectedUncached:void 0}},_={saveConsentModeSettings:i.a.mark((function e(){var t,r,n;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S();case 2:return t=e.sent,r=t.select,n=r(b.c).getConsentModeSettings(),e.next=7,k.actions.fetchSaveConsentModeSettings(n);case 7:return e.abrupt("return",e.sent);case 8:case"end":return e.stop()}}),e)})),setConsentModeEnabled:function(e){return{type:"SET_CONSENT_MODE_ENABLED",payload:{enabled:e}}},installActivateWPConsentAPI:i.a.mark((function e(){var t,r,n,o,a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S();case 2:return t=e.sent,e.next=5,O("installActivateWPConsentAPI",[]);case 5:return e.next=7,{type:"INSTALL_ACTIVATE_WP_CONSENT_API_FETCHING",payload:!0};case 7:return e.next=9,d.commonActions.await(t.resolveSelect(y.a).getNonces());case 9:if(void 0!==(r=t.select(y.a).getNonce("updates"))){e.next=18;break}return n=t.select(y.a).getErrorForSelector("getNonces"),e.next=14,h(n,"installActivateWPConsentAPI",[]);case 14:return e.next=16,{type:"INSTALL_ACTIVATE_WP_CONSENT_API_FETCHING",payload:!1};case 16:return t.dispatch(y.a).invalidateResolution("getNonces",[]),e.abrupt("return");case 18:return e.next=20,P.actions.fetchInstallActivateWPConsentAPI({nonce:r});case 20:return o=e.sent,a=o.response,e.next=24,{type:"INSTALL_ACTIVATE_WP_CONSENT_API_RESPONSE",payload:a};case 24:return e.next=26,{type:"INSTALL_ACTIVATE_WP_CONSENT_API_FETCHING",payload:!1};case 26:return e.next=28,w.actions.fetchGetConsentAPIInfo();case 28:case"end":return e.stop()}}),e)})),activateConsentAPI:i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,R.actions.fetchActivateConsentAPI();case 2:return t=e.sent,e.next=5,{type:"INSTALL_ACTIVATE_WP_CONSENT_API_RESPONSE",payload:t};case 5:return e.next=7,w.actions.fetchGetConsentAPIInfo();case 7:case"end":return e.stop()}}),e)}))},T=Object(v.a)((function(e,t){var r=t.type,n=t.payload;switch(r){case"SET_CONSENT_MODE_ENABLED":e.consentMode.settings=e.consentMode.settings||{},e.consentMode.settings.enabled=!!n.enabled;break;case"INSTALL_ACTIVATE_WP_CONSENT_API_RESPONSE":e.consentMode.apiInstallResponse=n;break;case"INSTALL_ACTIVATE_WP_CONSENT_API_FETCHING":e.consentMode.isApiFetching=n}})),x={getConsentModeSettings:function(e){return e.consentMode.settings},isConsentModeEnabled:Object(d.createRegistrySelector)((function(e){return function(){return(e(b.c).getConsentModeSettings()||{}).enabled}})),getConsentAPIInfo:function(e){return e.consentMode.apiInfo},getApiInstallResponse:function(e){return e.consentMode.apiInstallResponse},isApiFetching:function(e){return e.consentMode.isApiFetching},isAdsConnected:function(e){return e.consentMode.adsConnected},isAdsConnectedUncached:function(e){return e.consentMode.adsConnectedUncached}},L={getConsentModeSettings:i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S();case 2:if(t=e.sent,!(0,t.select)(b.c).getConsentModeSettings()){e.next=6;break}return e.abrupt("return");case 6:return e.next=8,j.actions.fetchGetConsentModeSettings();case 8:case"end":return e.stop()}}),e)})),getConsentAPIInfo:i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S();case 2:if(t=e.sent,!(0,t.select)(b.c).getConsentAPIInfo()){e.next=6;break}return e.abrupt("return");case 6:return e.next=8,w.actions.fetchGetConsentAPIInfo();case 8:case"end":return e.stop()}}),e)})),isAdsConnected:i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S();case 2:if(t=e.sent,void 0===(0,t.select)(b.c).isAdsConnected()){e.next=6;break}return e.abrupt("return");case 6:return e.next=8,A.actions.fetchGetAdsMeasurementStatus({useCache:!0});case 8:case"end":return e.stop()}}),e)})),isAdsConnectedUncached:i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S();case 2:if(t=e.sent,void 0===(0,t.select)(b.c).isAdsConnectedUncached()){e.next=6;break}return e.abrupt("return");case 6:return e.next=8,A.actions.fetchGetAdsMeasurementStatus({useCache:!1});case 8:case"end":return e.stop()}}),e)}))},N=Object(d.combineStores)(j,k,w,P,R,A,{initialState:C,actions:_,controls:{},reducer:T,resolvers:L,selectors:x});N.initialState,N.actions,N.controls,N.reducer,N.resolvers,N.selectors;t.a=N}).call(this,r(28))},956:function(e,t,r){"use strict";(function(e){var n,o=r(5),i=r.n(o),a=r(16),c=r.n(a),s=r(12),u=r.n(s),l=r(3),f=r(13),p=r(48),d=r(64),g=r(7),v=d.a.receiveError,b=d.a.clearError,y=Object(p.a)({baseName:"enableAutoUpdate",controlCallback:(n=c()(i.a.mark((function t(r){var n,o,a,c;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=r.nonce,o=r.pluginBasename,(a=new FormData).append("action","toggle-auto-updates"),a.append("_ajax_nonce",n),a.append("state","enable"),a.append("type","plugin"),a.append("asset",o),t.next=9,fetch(e.ajaxurl,{method:"POST",credentials:"same-origin",body:a});case 9:return c=t.sent,t.abrupt("return",c.json());case 11:case"end":return t.stop()}}),t)}))),function(e){return n.apply(this,arguments)}),argsToParams:function(e){return{nonce:e.nonce,pluginBasename:e.pluginBasename}},validateParams:function(e){var t=e.nonce,r=e.pluginBasename;u()("string"==typeof t,"nonce must be a string."),u()("string"==typeof r,"pluginBasename must be a string.")}}),m={enableAutoUpdate:i.a.mark((function e(){var t,r,n,o,a,c;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b("enableAutoUpdate",[]);case 2:return e.next=4,l.commonActions.getRegistry();case 4:return t=e.sent,e.next=7,l.commonActions.await(t.resolveSelect(g.a).getNonces());case 7:return e.next=9,l.commonActions.await(t.resolveSelect(f.c).getSiteInfo());case 9:return r=t.select(g.a).getNonce("updates"),n=t.select(f.c).getPluginBasename(),e.next=13,y.actions.fetchEnableAutoUpdate({nonce:r,pluginBasename:n});case 13:if(o=e.sent,a=o.response,c=o.error,(null==a?void 0:a.success)&&t.dispatch(f.c).setSiteKitAutoUpdatesEnabled(!0),!c){e.next=20;break}return e.next=20,v(c,"enableAutoUpdate",[]);case 20:case"end":return e.stop()}}),e)}))},O={isDoingEnableAutoUpdate:Object(l.createRegistrySelector)((function(e){return function(){var t=e(g.a).getNonce("updates"),r=e(f.c).getPluginBasename();return void 0!==t&&void 0!==r&&e(f.c).isFetchingEnableAutoUpdate({nonce:t,pluginBasename:r})}}))},h=Object(l.combineStores)(y,{initialState:{},actions:m,selectors:O});h.initialState,h.actions,h.controls,h.reducer,h.resolvers,h.selectors;t.a=h}).call(this,r(28))},97:function(e,t,r){"use strict";(function(e){r(51),r(53)}).call(this,r(28))},98:function(e,t,r){"use strict";(function(e){r.d(t,"b",(function(){return i})),r.d(t,"a",(function(){return a}));var n=r(239),o=r(85),i=function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var i=r.invertColor,a=void 0!==i&&i;return Object(n.a)(e.createElement(o.a,{direction:t>0?"up":"down",invertColor:a}))},a=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,r(4))},99:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return f}));var n=r(6),o=r.n(n),i=r(14),a=r(100),c=r(101);function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach((function(t){o()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var l={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function f(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,o=u(u({},l),t);o.referenceSiteURL&&(o.referenceSiteURL=o.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(a.a)(o,r),f=Object(c.a)(o,r,s,n),p={},d=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=JSON.stringify(t);p[n]||(p[n]=Object(i.once)(f)),p[n].apply(p,t)};return{enableTracking:function(){o.trackingEnabled=!0},disableTracking:function(){o.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!o.trackingEnabled},trackEvent:f,trackEventOnce:d}}}).call(this,r(28))}},[[1292,1,0]]]);