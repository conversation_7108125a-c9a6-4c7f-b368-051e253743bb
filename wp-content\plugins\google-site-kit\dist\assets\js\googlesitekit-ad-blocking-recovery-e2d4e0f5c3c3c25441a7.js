(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[3],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(59),a=n(39),i=n(57);function o(t,n){var o,c=Object(r.a)(n),s=t.activeModules,l=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,g=void 0===d?[]:d,f=t.isAuthenticated,m=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(a.b,"]"))),!o){o=!0;var r=(null==g?void 0:g.length)?g.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:l,plugin_version:m||"",enabled_features:Array.from(i.a).join(","),active_modules:s.join(","),authenticated:f?"1":"0",user_properties:{user_roles:r,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(a.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(a.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(a.a)}}}}}).call(this,n(28))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var r=n(5),a=n.n(r),i=n(6),o=n.n(i),c=n(16),s=n.n(c),l=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n,r){var i=Object(l.a)(t);return function(){var t=s()(a.a.mark((function t(o,c,s,l){var u;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:s,value:l},t.abrupt("return",new Promise((function(e){var t,n,a=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(a),e()};i("event",c,d(d({},u),{},{event_callback:s})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,a){return t.apply(this,arguments)}}()}},102:function(e,t,n){"use strict";var r=n(123);n.d(t,"a",(function(){return r.a}));var a=n(124);n.d(t,"c",(function(){return a.a}));var i=n(125);n.d(t,"b",(function(){return i.a}))},104:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return r.createElement("svg",a({viewBox:"0 0 14 14",fill:"none"},e),i)}},105:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(11),u=n.n(l);function VisuallyHidden(t){var n=t.className,r=t.children,i=o()(t,["className","children"]);return r?e.createElement("span",a()({},i,{className:u()("screen-reader-text",n)}),r):null}VisuallyHidden.propTypes={className:s.a.string,children:s.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(4))},107:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var r=n(21),a=n.n(r),i=n(152),o=n.n(i),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(2),g=n(10),f=n(154),m=n(104);function TourTooltip(t){var n=t.backProps,r=t.closeProps,c=t.index,l=t.primaryProps,u=t.size,p=t.step,b=t.tooltipProps,v=u>1?Object(f.a)(u):[],h=function(e){return s()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",a()({className:s()("googlesitekit-tour-tooltip",p.className)},b),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},p.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},p.content)),e.createElement(i.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},v.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:h(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(g.Button,a()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),p.cta,l.title&&e.createElement(g.Button,a()({className:"googlesitekit-tooltip-button",text:!0},l),l.title))),e.createElement(g.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(m.a,{width:"14",height:"14"}),onClick:r.onClick,"aria-label":Object(d.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(4))},109:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(208),s=n(38),l=n(2),u=n(10),d=n(58);function ModalDialog(t){var n=t.className,r=void 0===n?"":n,a=t.dialogActive,i=void 0!==a&&a,g=t.handleDialog,f=void 0===g?null:g,m=t.onOpen,p=void 0===m?null:m,b=t.onClose,v=void 0===b?null:b,h=t.title,_=void 0===h?null:h,E=t.provides,O=t.handleConfirm,k=t.subtitle,y=t.confirmButton,S=void 0===y?null:y,j=t.dependentModules,w=t.danger,N=void 0!==w&&w,A=t.inProgress,C=void 0!==A&&A,T=t.small,x=void 0!==T&&T,R=t.medium,L=void 0!==R&&R,D=t.buttonLink,I=void 0===D?null:D,M=Object(c.a)(ModalDialog),P="googlesitekit-dialog-description-".concat(M),B=!(!E||!E.length);return e.createElement(u.Dialog,{open:i,onOpen:p,onClose:v,"aria-describedby":B?P:void 0,tabIndex:"-1",className:o()(r,{"googlesitekit-dialog-sm":x,"googlesitekit-dialog-md":L})},e.createElement(u.DialogTitle,null,N&&e.createElement(d.a,{width:28,height:28}),_),k?e.createElement("p",{className:"mdc-dialog__lead"},k):[],e.createElement(u.DialogContent,null,B&&e.createElement("section",{id:P,className:"mdc-dialog__provides"},e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},E.map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t},e.createElement("span",{className:"mdc-list-item__text"},t))})))),j&&e.createElement("p",{className:"mdc-dialog__dependencies"},Object(s.a)(Object(l.sprintf)(
/* translators: %s is replaced with the dependent modules. */
Object(l.__)("<strong>Note:</strong> %s","google-site-kit"),j),{strong:e.createElement("strong",null)}))),e.createElement(u.DialogFooter,null,e.createElement(u.Button,{className:"mdc-dialog__cancel-button",tertiary:!0,onClick:f,disabled:C},Object(l.__)("Cancel","google-site-kit")),I?e.createElement(u.Button,{href:I,onClick:O,target:"_blank",danger:N},S):e.createElement(u.SpinnerButton,{onClick:O,danger:N,disabled:C,isSaving:C},S||Object(l.__)("Disconnect","google-site-kit"))))}ModalDialog.displayName="Dialog",ModalDialog.propTypes={className:a.a.string,dialogActive:a.a.bool,handleDialog:a.a.func,handleConfirm:a.a.func.isRequired,onOpen:a.a.func,onClose:a.a.func,title:a.a.string,confirmButton:a.a.string,danger:a.a.bool,small:a.a.bool,medium:a.a.bool,buttonLink:a.a.string},t.a=ModalDialog}).call(this,n(4))},111:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(1),a=n.n(r),i=n(0),o=n(9),c=n(54);function Description(t){var n=t.className,r=void 0===n?"googlesitekit-publisher-win__desc":n,a=t.text,s=t.learnMoreLink,l=t.errorText,u=t.children;return e.createElement(i.Fragment,null,e.createElement("div",{className:r},e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.F)(a,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",s)),l&&e.createElement(c.a,{message:l}),u)}Description.propTypes={className:a.a.string,text:a.a.string,learnMoreLink:a.a.node,errorText:a.a.string,children:a.a.node}}).call(this,n(4))},112:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(392),a=function(e,t,n){Object(r.a)((function(n){return e.includes(n.keyCode)&&t.current.contains(n.target)}),n)}},120:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var r=n(1),a=n.n(r),i=n(0),o=n(2),c=n(3),s=n(10),l=n(35),u=n(54);function ErrorNotice(t){var n,r=t.error,a=t.hasButton,d=void 0!==a&&a,g=t.storeName,f=t.message,m=void 0===f?r.message:f,p=t.noPrefix,b=void 0!==p&&p,v=t.skipRetryMessage,h=t.Icon,_=Object(c.useDispatch)(),E=Object(c.useSelect)((function(e){return g?e(g).getSelectorDataForError(r):null})),O=Object(i.useCallback)((function(){_(E.storeName).invalidateResolution(E.name,E.args)}),[_,E]);if(!r||Object(l.f)(r))return null;var k=d&&Object(l.d)(r,E);return d||v||(m=Object(o.sprintf)(
/* translators: %1$s: Error message from Google API. */
Object(o.__)("%1$s%2$s Please try again.","google-site-kit"),m,m.endsWith(".")?"":".")),e.createElement(i.Fragment,null,h&&e.createElement("div",{className:"googlesitekit-error-notice__icon"},e.createElement(h,{width:"24",height:"24"})),e.createElement(u.a,{message:m,reconnectURL:null===(n=r.data)||void 0===n?void 0:n.reconnectURL,noPrefix:b}),k&&e.createElement(s.Button,{className:"googlesitekit-error-notice__retry-button",onClick:O},Object(o.__)("Retry","google-site-kit")))}ErrorNotice.propTypes={error:a.a.shape({message:a.a.string}),hasButton:a.a.bool,storeName:a.a.string,message:a.a.string,noPrefix:a.a.bool,skipRetryMessage:a.a.bool,Icon:a.a.elementType}}).call(this,n(4))},123:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(21),a=n.n(r),i=n(6),o=n.n(i),c=n(25),s=n.n(c),l=n(1),u=n.n(l),d=n(11),g=n.n(d);function Cell(t){var n,r=t.className,i=t.alignTop,c=t.alignMiddle,l=t.alignBottom,u=t.alignRight,d=t.alignLeft,f=t.smAlignRight,m=t.mdAlignRight,p=t.lgAlignRight,b=t.smSize,v=t.smStart,h=t.smOrder,_=t.mdSize,E=t.mdStart,O=t.mdOrder,k=t.lgSize,y=t.lgStart,S=t.lgOrder,j=t.size,w=t.children,N=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",a()({},N,{className:g()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":i,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":l,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":f,"mdc-layout-grid__cell--align-right-tablet":m,"mdc-layout-grid__cell--align-right-desktop":p},o()(n,"mdc-layout-grid__cell--span-".concat(j),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--start-".concat(y,"-desktop"),12>=y&&y>0),o()(n,"mdc-layout-grid__cell--order-".concat(S,"-desktop"),12>=S&&S>0),o()(n,"mdc-layout-grid__cell--span-".concat(_,"-tablet"),8>=_&&_>0),o()(n,"mdc-layout-grid__cell--start-".concat(E,"-tablet"),8>=E&&E>0),o()(n,"mdc-layout-grid__cell--order-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--span-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--start-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--order-".concat(h,"-phone"),4>=h&&h>0),n))}),w)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},124:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),g=Object(d.forwardRef)((function(t,n){var r=t.className,i=t.children,c=o()(t,["className","children"]);return e.createElement("div",a()({ref:n,className:u()("mdc-layout-grid__inner",r)},c),i)}));g.displayName="Row",g.propTypes={className:s.a.string,children:s.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(4))},125:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),g=Object(d.forwardRef)((function(t,n){var r=t.alignLeft,i=t.fill,c=t.className,s=t.children,l=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",a()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":l,"mdc-layout-grid--fill":i})},d,{ref:n}),s)}));g.displayName="Grid",g.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(4))},126:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",a({viewBox:"0 0 13 13"},e),i)}},127:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",a({viewBox:"0 0 13 13"},e),i)}},1272:function(e,t,n){"use strict";n.r(t),function(e){var t=n(332),r=n(144),a=n(22),i=n(661),o=n(224);Object(t.a)((function(){var t=document.getElementById("js-googlesitekit-ad-blocking-recovery");t&&Object(r.render)(e.createElement(o.a,{viewContext:a.k},e.createElement(i.a,null)),t)}))}.call(this,n(4))},128:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},129:function(e,t,n){"use strict";n.d(t,"a",(function(){return p})),n.d(t,"c",(function(){return v})),n.d(t,"b",(function(){return h}));var r=n(25),a=n.n(r),i=n(6),o=n.n(i),c=n(5),s=n.n(c),l=n(12),u=n.n(l),d=n(3),g=n.n(d),f=n(37),m=n(9),p=function(e){var t;u()(e,"storeName is required to create a snapshot store.");var n={},r={deleteSnapshot:s.a.mark((function e(){var t;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"DELETE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})),restoreSnapshot:s.a.mark((function e(){var t,n,r,a,i,o,c=arguments;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},n=t.clearAfterRestore,r=void 0===n||n,e.next=4,{payload:{},type:"RESTORE_SNAPSHOT"};case 4:if(a=e.sent,i=a.cacheHit,o=a.value,!i){e.next=13;break}return e.next=10,{payload:{snapshot:o},type:"SET_STATE_FROM_SNAPSHOT"};case 10:if(!r){e.next=13;break}return e.next=13,{payload:{},type:"DELETE_SNAPSHOT"};case 13:return e.abrupt("return",i);case 14:case"end":return e.stop()}}),e)})),createSnapshot:s.a.mark((function e(){var t;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"CREATE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))},i=(t={},o()(t,"DELETE_SNAPSHOT",(function(){return Object(f.c)("datastore::cache::".concat(e))})),o()(t,"CREATE_SNAPSHOT",Object(d.createRegistryControl)((function(t){return function(){return Object(f.f)("datastore::cache::".concat(e),t.stores[e].store.getState())}}))),o()(t,"RESTORE_SNAPSHOT",(function(){return Object(f.d)("datastore::cache::".concat(e),m.b)})),t);return{initialState:n,actions:r,controls:i,reducer:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1?arguments[1]:void 0,r=t.type,i=t.payload;switch(r){case"SET_STATE_FROM_SNAPSHOT":var o=i.snapshot,c=(o.error,a()(o,["error"]));return c;default:return e}}}},b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Object.values(e.stores).filter((function(e){return Object.keys(e.getActions()).includes("restoreSnapshot")}))},v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Promise.all(b(e).map((function(e){return e.getActions().createSnapshot()})))},h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Promise.all(b(e).map((function(e){return e.getActions().restoreSnapshot()})))}},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var r="core/site",a="primary",i="secondary"},130:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(14),a=function(e){return Object(r.isFinite)(e)?e:0}},134:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(38),s=n(2),l=n(20),u=n(34);function SourceLink(t){var n=t.name,r=t.href,a=t.className,i=t.external;return Object(u.a)()?null:e.createElement("div",{className:o()("googlesitekit-source-link",a)},Object(c.a)(Object(s.sprintf)(
/* translators: %s: source link */
Object(s.__)("Source: %s","google-site-kit"),"<a>".concat(n,"</a>")),{a:e.createElement(l.a,{key:"link",href:r,external:i})}))}SourceLink.propTypes={name:a.a.string,href:a.a.string,className:a.a.string,external:a.a.bool},SourceLink.defaultProps={name:"",href:"",className:"",external:!1},t.a=SourceLink}).call(this,n(4))},136:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"d",(function(){return i})),n.d(t,"c",(function(){return o}));function r(e){var t=e.format,n=void 0===t?"small":t,r=e.hasErrorOrWarning,a=e.hasSmallImageSVG,o=e.hasWinImageSVG,c={smSize:4,mdSize:8,lgSize:12},s=i(n);return Object.keys(c).forEach((function(e){var t=c[e];r&&(t-=1),a&&(t-=1),o&&0<t-s[e]&&(t-=s[e]),c[e]=t})),c}var a=function(e){switch(e){case"small":return{};case"larger":return{smOrder:2,mdOrder:2,lgOrder:1};default:return{smOrder:2,mdOrder:1}}},i=function(e){switch(e){case"smaller":return{smSize:4,mdSize:2,lgSize:2};case"larger":return{smSize:4,mdSize:8,lgSize:7};default:return{smSize:4,mdSize:2,lgSize:4}}},o=function(e){switch(e){case"larger":return{smOrder:1,mdOrder:1,lgOrder:2};default:return{smOrder:1,mdOrder:2}}}},139:function(e,t,n){"use strict";var r=n(0),a=Object(r.createContext)(!1);t.a=a},140:function(e,t,n){"use strict";n.d(t,"d",(function(){return r})),n.d(t,"e",(function(){return a})),n.d(t,"j",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"g",(function(){return c})),n.d(t,"k",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"b",(function(){return u})),n.d(t,"h",(function(){return d})),n.d(t,"f",(function(){return g})),n.d(t,"i",(function(){return f})),n.d(t,"l",(function(){return m})),n.d(t,"n",(function(){return p})),n.d(t,"r",(function(){return b})),n.d(t,"m",(function(){return v})),n.d(t,"p",(function(){return h})),n.d(t,"q",(function(){return _})),n.d(t,"o",(function(){return E})),n.d(t,"t",(function(){return O})),n.d(t,"s",(function(){return k}));var r="disapproved",a="graylisted",i="pending",o="approved",c="needs-attention",s="ready",l="client-requires-review",u="client-getting-ready",d="none",g="multiple",f="no-client",m="added",p="needs-attention",b="requires-review",v="getting-ready",h="ready",_="ready-no-auto-ads",E="none",O=[r,a,i,o],k=function(e){return e===a||e===i}},141:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return StoreErrorNotices}));var r=n(1),a=n.n(r),i=n(3),o=n(120),c=n(19),s=n(35),l=n(169);function StoreErrorNotices(t){var n=t.hasButton,r=void 0!==n&&n,a=t.moduleSlug,u=t.storeName,d=Object(i.useSelect)((function(e){return e(u).getErrors()})),g=Object(i.useSelect)((function(e){return e(c.a).getModule(a)})),f=[];return d.filter((function(e){return!(!(null==e?void 0:e.message)||f.includes(e.message))&&(f.push(e.message),!0)})).map((function(t,n){var a=t.message;return Object(s.e)(t)&&(a=Object(l.a)(a,g)),e.createElement(o.a,{key:n,error:t,hasButton:r,storeName:u,message:a})}))}StoreErrorNotices.propTypes={hasButton:a.a.bool,storeName:a.a.string.isRequired,moduleSlug:a.a.string}}).call(this,n(4))},142:function(e,t,n){"use strict";var r=n(166);n.d(t,"c",(function(){return r.a}));var a=n(65);n.d(t,"b",(function(){return a.c})),n.d(t,"a",(function(){return a.a}))},148:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M3.332 24.75h21.335c2.182 0 3.542-2.366 2.451-4.25L16.451 2.07C15.36.184 12.64.184 11.549 2.07L.882 20.5c-1.091 1.884.269 4.25 2.45 4.25zM14 14.833a1.42 1.42 0 01-1.417-1.416v-2.834c0-.779.638-1.416 1.417-1.416.78 0 1.417.637 1.417 1.416v2.834A1.42 1.42 0 0114 14.833zm1.417 5.667h-2.834v-2.833h2.834V20.5z",fill:"currentColor",fillRule:"nonzero"});t.a=function SvgError(e){return r.createElement("svg",a({viewBox:"0 0 28 25"},e),i)}},151:function(e,t,n){"use strict";(function(e,r){var a=n(51),i=n.n(a),o=n(53),c=n.n(o),s=n(68),l=n.n(s),u=n(69),d=n.n(u),g=n(49),f=n.n(g),m=n(1),p=n.n(m),b=n(0),v=n(2),h=n(54);function _(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var a=f()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return d()(this,n)}}var E=function(t){l()(MediaErrorHandler,t);var n=_(MediaErrorHandler);function MediaErrorHandler(e){var t;return i()(this,MediaErrorHandler),(t=n.call(this,e)).state={error:null},t}return c()(MediaErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t})}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.errorMessage;return this.state.error?r.createElement(h.a,{message:n}):t}}]),MediaErrorHandler}(b.Component);E.defaultProps={errorMessage:Object(v.__)("Failed to load media","google-site-kit")},E.propTypes={children:p.a.node.isRequired,errorMessage:p.a.string.isRequired},t.a=E}).call(this,n(28),n(4))},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},155:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("path",{d:"M2.253 12.252l7.399 5.658A13.055 13.055 0 009 22c0 1.43.229 2.805.652 4.09l-7.4 5.658A22.02 22.02 0 010 22c0-3.506.81-6.814 2.253-9.748z",fill:"#FBBC05"}),r.createElement("path",{d:"M9.652 17.91l-7.4-5.658A21.935 21.935 0 0122 0c5.6 0 10.6 2.1 14.5 5.5l-6.4 6.4C27.9 10.1 25.1 9 22 9c-5.77 0-10.64 3.725-12.348 8.91z",fill:"#EA4335"}),r.createElement("path",{d:"M2.25 31.742l7.396-5.67A12.975 12.975 0 0022 35c6.1 0 10.7-3.1 11.8-8.5H22V18h20.5c.3 1.3.5 2.7.5 4 0 14-10 22-21 22A21.935 21.935 0 012.25 31.742z",fill:"#34A853"}),r.createElement("path",{d:"M36.34 38.52l-7.025-5.437c2.297-1.45 3.895-3.685 4.485-6.583H22V18h20.5c.3 1.3.5 2.7.5 4 0 7.17-2.623 12.767-6.66 16.52z",fill:"#4285F4"}));t.a=function SvgLogoG(e){return r.createElement("svg",a({viewBox:"0 0 43 44"},e),i)}},158:function(e,t,n){"use strict";var r=n(0),a=n(57),i=Object(r.createContext)(a.a);t.a=i},160:function(e,t,n){"use strict";var r=n(139),a=(r.a.Consumer,r.a.Provider);t.a=a},161:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(3),a=n(23),i=function(e){return"notification/".concat(e,"/viewed")};function o(e){return Object(r.useSelect)((function(t){return!!t(a.b).getValue(i(e))}),[e])}o.getKey=i},162:function(e,t,n){"use strict";var r=n(646);n.d(t,"a",(function(){return r.a}));var a=n(647);n.d(t,"b",(function(){return a.a}));var i=n(648);n.d(t,"d",(function(){return i.a}));var o=n(649);n.d(t,"f",(function(){return o.a}));var c=n(650);n.d(t,"e",(function(){return c.a}));n(595);var s=n(407);n.d(t,"c",(function(){return s.c}));n(596)},164:function(e,t,n){"use strict";(function(e){var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(1),s=n.n(c),l=n(0),u=n(20),d=n(9),g=n(18);function HelpMenuLink(t){var n=t.children,r=t.href,i=t.gaEventLabel,c=Object(g.a)(),s=Object(l.useCallback)(o()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!i){e.next=3;break}return e.next=3,Object(d.I)("".concat(c,"_headerbar_helpmenu"),"click_outgoing_link",i);case 3:case"end":return e.stop()}}),e)}))),[i,c]);return e.createElement("li",{className:"googlesitekit-help-menu-link mdc-list-item",role:"none"},e.createElement(u.a,{className:"mdc-list-item__text",href:r,external:!0,hideExternalIndicator:!0,role:"menuitem",onClick:s},n))}HelpMenuLink.propTypes={children:s.a.node.isRequired,href:s.a.string.isRequired,gaEventLabel:s.a.string},t.a=HelpMenuLink}).call(this,n(4))},166:function(e,t,n){"use strict";(function(e){var r=n(11),a=n.n(r),i=n(1),o=n.n(i),c=n(2),s=n(3),l=n(201),u=n(210),d=n(65),g=n(7),f=n(10),m=n(0),p=Object(m.forwardRef)((function(t,n){var r=t.className,i=t.children,o=t.type,m=t.dismiss,p=void 0===m?"":m,b=t.dismissCallback,v=t.dismissLabel,h=void 0===v?Object(c.__)("OK, Got it!","google-site-kit"):v,_=t.Icon,E=void 0===_?Object(d.d)(o):_,O=t.OuterCTA,k=Object(s.useDispatch)(g.a).dismissItem,y=Object(s.useSelect)((function(e){return p?e(g.a).isItemDismissed(p):void 0}));if(p&&y)return null;var S=i?u.a:l.a;return e.createElement("div",{ref:n,className:a()(r,"googlesitekit-settings-notice","googlesitekit-settings-notice--".concat(o),{"googlesitekit-settings-notice--single-row":!i,"googlesitekit-settings-notice--multi-row":i})},e.createElement("div",{className:"googlesitekit-settings-notice__icon"},e.createElement(E,{width:"20",height:"20"})),e.createElement("div",{className:"googlesitekit-settings-notice__body"},e.createElement(S,t)),p&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(f.Button,{tertiary:!0,onClick:function(){"string"==typeof p&&k(p),null==b||b()}},h)),O&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(O,null)))}));p.propTypes={className:o.a.string,children:o.a.node,notice:o.a.node.isRequired,type:o.a.oneOf([d.a,d.c,d.b]),Icon:o.a.elementType,LearnMore:o.a.elementType,CTA:o.a.elementType,OuterCTA:o.a.elementType,dismiss:o.a.string,dismissLabel:o.a.string,dismissCallback:o.a.func},p.defaultProps={type:d.a},t.a=p}).call(this,n(4))},167:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notifications}));var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(3),s=n(18),l=n(41),u=n(283);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Notifications(t){var n,r=t.areaSlug,i=t.groupID,o=void 0===i?l.c.DEFAULT:i,g=Object(s.a)(),f=Object(c.useSelect)((function(e){return e(l.a).getQueuedNotifications(g,o)}));if(void 0===(null==f?void 0:f[0])||(null==f||null===(n=f[0])||void 0===n?void 0:n.areaSlug)!==r)return null;var m=f[0],p=m.id,b=m.Component,v=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},Object(u.a)(p));return e.createElement(b,v)}Notifications.propTypes={viewContext:o.a.string,areaSlug:o.a.string}}).call(this,n(4))},169:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(2);function a(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},a=n.slug,i=void 0===a?"":a,o=n.name,c=void 0===o?"":o,s=n.owner,l=void 0===s?{}:s;if(!i||!c)return e;var u="",d="";return"analytics-4"===i?e.match(/account/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):"search-console"===i&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),u||(u=Object(r.sprintf)(
/* translators: %s: module name */
Object(r.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),c)),l&&l.login&&(d=Object(r.sprintf)(
/* translators: %s: owner name */
Object(r.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),l.login)),d||(d=Object(r.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(u," ").concat(d)}},17:function(e,t,n){"use strict";var r=n(254);n.d(t,"i",(function(){return r.a}));var a=n(319);n.d(t,"f",(function(){return a.a}));var i=n(320);n.d(t,"h",(function(){return i.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var s=n(91),l=n.n(s);n.d(t,"b",(function(){return l.a})),n.d(t,"c",(function(){return s.DialogContent})),n.d(t,"d",(function(){return s.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},172:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GenericErrorHandlerActions}));var r=n(1),a=n.n(r),i=n(2),o=n(20),c=n(194);function GenericErrorHandlerActions(t){var n=t.message,r=t.componentStack;return e.createElement("div",{className:"googlesitekit-generic-error-handler-actions"},e.createElement(c.a,{message:n,componentStack:r}),e.createElement(o.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0},Object(i.__)("Report this problem","google-site-kit")))}GenericErrorHandlerActions.propTypes={message:a.a.string,componentStack:a.a.string}}).call(this,n(4))},173:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(22),a=function(e){return r.f.includes(e)}},18:function(e,t,n){"use strict";var r=n(0),a=n(61);t.a=function(){return Object(r.useContext)(a.b)}},183:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LoadingWrapper}));var r=n(6),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(44);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function LoadingWrapper(t){var n=t.loading,r=t.children,a=o()(t,["loading","children"]);return n?e.createElement(l.a,a):r}LoadingWrapper.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({loading:s.a.bool,children:s.a.node},l.a.propTypes)}).call(this,n(4))},186:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M0 0h2v7H0zm0 10h2v2H0z",fill:"currentColor",fillRule:"evenodd"});t.a=function SvgWarningIcon(e){return r.createElement("svg",a({viewBox:"0 0 2 12"},e),i)}},189:function(e,t,n){"use strict";(function(e){function Title(t){var n=t.title;return e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n)}n.d(t,"a",(function(){return Title}))}).call(this,n(4))},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r="core/modules",a="insufficient_module_dependencies"},191:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notification}));var r=n(15),a=n.n(r),i=n(1),o=n.n(i),c=n(0),s=n(282),l=n(161),u=n(73);function Notification(t){var n=t.id,r=t.className,i=t.gaTrackingEventArgs,o=t.children,d=t.onView,g=Object(c.useRef)(),f=Object(l.a)(n),m=Object(u.a)(n,null==i?void 0:i.category),p=Object(c.useState)(!1),b=a()(p,2),v=b[0],h=b[1];return Object(c.useEffect)((function(){!v&&f&&(m.view(null==i?void 0:i.label,null==i?void 0:i.value),null==d||d(),h(!0))}),[f,m,v,i,d]),e.createElement("section",{id:n,ref:g,className:r},o,!f&&e.createElement(s.a,{id:n,observeRef:g,threshold:.5}))}Notification.propTypes={id:o.a.string,className:o.a.string,gaTrackingEventArgs:o.a.shape({category:o.a.string,label:o.a.string,value:o.a.string}),children:o.a.node,onView:o.a.func}}).call(this,n(4))},192:function(e,t,n){"use strict";(function(e){var r=n(51),a=n.n(r),i=n(53),o=n.n(i),c=n(68),s=n.n(c),l=n(69),u=n.n(l),d=n(49),g=n.n(d),f=n(1),m=n.n(f),p=n(11),b=n.n(p),v=n(0),h=n(329),_=n(330);function E(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=g()(e);if(t){var a=g()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return u()(this,n)}}var O=function(t){s()(Layout,t);var n=E(Layout);function Layout(){return a()(this,Layout),n.apply(this,arguments)}return o()(Layout,[{key:"render",value:function(){var t=this.props,n=t.header,r=t.footer,a=t.children,i=t.title,o=t.badge,c=t.headerCTALabel,s=t.headerCTALink,l=t.footerCTALabel,u=t.footerCTALink,d=t.footerContent,g=t.className,f=t.fill,m=t.relative,p=t.rounded,v=void 0!==p&&p,E=t.transparent,O=void 0!==E&&E;return e.createElement("div",{className:b()("googlesitekit-layout",g,{"googlesitekit-layout--fill":f,"googlesitekit-layout--relative":m,"googlesitekit-layout--rounded":v,"googlesitekit-layout--transparent":O})},n&&e.createElement(h.a,{title:i,badge:o,ctaLabel:c,ctaLink:s}),a,r&&e.createElement(_.a,{ctaLabel:l,ctaLink:u,footerContent:d}))}}]),Layout}(v.Component);O.propTypes={header:m.a.bool,footer:m.a.bool,children:m.a.node.isRequired,title:m.a.string,badge:m.a.node,headerCTALabel:m.a.string,headerCTALink:m.a.string,footerCTALabel:m.a.string,footerCTALink:m.a.string,footerContent:m.a.node,className:m.a.string,fill:m.a.bool,relative:m.a.bool,rounded:m.a.bool,transparent:m.a.bool},O.defaultProps={header:!1,footer:!1,title:"",badge:null,headerCTALabel:"",headerCTALink:"",footerCTALabel:"",footerCTALink:"",footerContent:null,className:"",fill:!1,relative:!1},t.a=O}).call(this,n(4))},194:function(e,t,n){"use strict";(function(e){var r=n(15),a=n.n(r),i=n(195),o=n.n(i),c=n(1),s=n.n(c),l=n(0),u=n(2),d=n(266),g=n(423),f=n(424),m=n(10);function ReportErrorButton(t){var n=t.message,r=t.componentStack,i=Object(l.useState)(!1),c=a()(i,2),s=c[0],p=c[1];return e.createElement(m.Button,{"aria-label":s?Object(u.__)("Error message copied to clipboard. Click to copy the error message again.","google-site-kit"):void 0,onClick:function(){o()("`".concat(n,"\n").concat(r,"`")),p(!0)},trailingIcon:e.createElement(d.a,{className:"mdc-button__icon",icon:s?g.a:f.a})},s?Object(u.__)("Copied to clipboard","google-site-kit"):Object(u.__)("Copy error contents","google-site-kit"))}ReportErrorButton.propTypes={message:s.a.string,componentStack:s.a.string},t.a=ReportErrorButton}).call(this,n(4))},196:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationError}));var r=n(17),a=n(222),i=n(189);function NotificationError(t){var n=t.title,o=t.description,c=t.actions;return e.createElement(r.e,null,e.createElement(r.k,null,e.createElement(r.a,{smSize:3,mdSize:7,lgSize:11,className:"googlesitekit-publisher-win__content"},e.createElement(i.a,{title:n}),o,c),e.createElement(a.a,{type:"win-error"})))}}).call(this,n(4))},197:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerNotification}));var r=n(21),a=n.n(r),i=n(6),o=n.n(i),c=n(5),s=n.n(c),l=n(16),u=n.n(l),d=n(15),g=n.n(d),f=n(1),m=n.n(f),p=n(11),b=n.n(p),v=n(206),h=n(240),_=n(81),E=n(0),O=n(106),k=n(3),y=n(17),S=n(93),j=n(37),w=n(24),N=n(211),A=n(213),C=n(212),T=n(226),x=n(227),R=n(86),L=n(136),D=n(130),I=n(32),M=n(228),P=n(79);function BannerNotification(t){var n,r=t.badgeLabel,i=t.children,c=t.className,l=void 0===c?"":c,d=t.ctaLabel,f=t.ctaLink,m=t.ctaTarget,p=t.description,B=t.dismiss,z=t.dismissExpires,H=void 0===z?0:z,F=t.format,V=void 0===F?"":F,U=t.id,W=t.isDismissible,G=void 0===W||W,q=t.learnMoreDescription,K=t.learnMoreLabel,X=t.learnMoreURL,Y=t.learnMoreTarget,$=void 0===Y?R.a.EXTERNAL:Y,J=t.logo,Z=t.module,Q=t.moduleName,ee=t.onCTAClick,te=t.onView,ne=t.onDismiss,re=t.onLearnMoreClick,ae=t.showOnce,ie=void 0!==ae&&ae,oe=t.SmallImageSVG,ce=t.title,se=t.type,le=t.WinImageSVG,ue=t.showSmallWinImage,de=void 0===ue||ue,ge=t.smallWinImageSVGWidth,fe=void 0===ge?75:ge,me=t.smallWinImageSVGHeight,pe=void 0===me?75:me,be=t.mediumWinImageSVGWidth,ve=void 0===be?105:be,he=t.mediumWinImageSVGHeight,_e=void 0===he?105:he,Ee=t.rounded,Oe=void 0!==Ee&&Ee,ke=t.footer,ye=t.secondaryPane,Se=t.ctaComponent,je=Object(E.useState)(!1),we=g()(je,2),Ne=we[0],Ae=we[1],Ce=Object(E.useState)(!1),Te=g()(Ce,2),xe=Te[0],Re=Te[1],Le="notification::dismissed::".concat(U),De=function(){return Object(j.f)(Le,new Date,{ttl:null})},Ie=Object(P.a)(),Me=Object(w.e)(),Pe=Object(v.a)(),Be=Object(E.useState)(!1),ze=g()(Be,2),He=ze[0],Fe=ze[1],Ve=Object(E.useRef)(),Ue=Object(h.a)(Ve,{rootMargin:"".concat(-Object(D.a)(Object(S.c)(Me)),"px 0px 0px 0px"),threshold:0});Object(E.useEffect)((function(){!He&&(null==Ue?void 0:Ue.isIntersecting)&&("function"==typeof te&&te(),Fe(!0))}),[U,te,He,Ue]);var We=Ie>=600;Object(_.a)(u()(s.a.mark((function e(){var t,n;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(H>0)){e.next=3;break}return e.next=3,Je();case 3:if(!G){e.next=9;break}return e.next=6,Object(j.d)(Le);case 6:t=e.sent,n=t.cacheHit,Re(n);case 9:if(!ie){e.next=12;break}return e.next=12,De();case 12:case"end":return e.stop()}}),e)}))));var Ge=function(){var e=u()(s.a.mark((function e(t){return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),t.preventDefault(),!ne){e.next=5;break}return e.next=5,ne(t);case 5:Ke();case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),qe=Object(O.a)(f)&&"_blank"!==m,Ke=function(){return qe||Ae(!0),new Promise((function(e){setTimeout(u()(s.a.mark((function t(){var n;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,De();case 2:Pe()&&Re(!0),n=new Event("notificationDismissed"),document.dispatchEvent(n),e();case 6:case"end":return t.stop()}}),t)}))),350)}))},Xe=Object(k.useSelect)((function(e){return!!f&&e(I.a).isNavigatingTo(f)})),Ye=Object(k.useDispatch)(I.a).navigateTo,$e=function(){var e=u()(s.a.mark((function e(t){var n,r,a;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),qe&&!t.defaultPrevented&&t.preventDefault(),n=!0,!ee){e.next=12;break}return e.next=6,ee(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:r=e.t0,a=r.dismissOnCTAClick,n=void 0===a||a;case 12:if(!G||!n){e.next=15;break}return e.next=15,Ke();case 15:qe&&Ye(f);case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Je=function(){var e=u()(s.a.mark((function e(){var t,n,r;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(j.d)(Le);case 2:if(t=e.sent,!(n=t.value)){e.next=10;break}if((r=new Date(n)).setSeconds(r.getSeconds()+parseInt(H,10)),!(r<new Date)){e.next=10;break}return e.next=10,Object(j.c)(Le);case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();if(!Xe&&G&&(void 0===xe||xe))return null;var Ze=!Xe&&Ne?"is-closed":"is-open",Qe=Object(L.d)(V),et=Object(L.c)(V),tt=Object(L.a)(V),nt=Object(L.b)({format:V,hasErrorOrWarning:"win-error"===se||"win-warning"===se,hasSmallImageSVG:!!oe,hasWinImageSVG:!!le});return e.createElement(N.a,{id:U,className:b()(l,(n={},o()(n,"googlesitekit-publisher-win--".concat(V),V),o()(n,"googlesitekit-publisher-win--".concat(se),se),o()(n,"googlesitekit-publisher-win--".concat(Ze),Ze),o()(n,"googlesitekit-publisher-win--rounded",Oe),n)),secondaryPane:ye,ref:Ve},J&&e.createElement(x.a,{module:Z,moduleName:Q}),oe&&e.createElement(y.a,{size:1,className:"googlesitekit-publisher-win__small-media"},e.createElement(oe,null)),e.createElement(y.a,a()({},nt,tt,{className:"googlesitekit-publisher-win__content"}),e.createElement(A.a,{title:ce,badgeLabel:r,smallWinImageSVGHeight:pe,smallWinImageSVGWidth:fe,winImageFormat:V,WinImageSVG:!We&&de?le:void 0}),e.createElement(M.a,{description:p,learnMoreURL:X,learnMoreLabel:K,learnMoreTarget:$,learnMoreDescription:q,onLearnMoreClick:re}),i,e.createElement(C.a,{ctaLink:f,ctaLabel:d,ctaComponent:Se,ctaTarget:m,ctaCallback:$e,dismissLabel:G?B:void 0,dismissCallback:Ge}),ke&&e.createElement("div",{className:"googlesitekit-publisher-win__footer"},ke)),le&&(We||!de)&&e.createElement(y.a,a()({},Qe,et,{alignBottom:"larger"===V,className:"googlesitekit-publisher-win__image"}),e.createElement("div",{className:"googlesitekit-publisher-win__image-".concat(V)},e.createElement(le,{style:{maxWidth:ve,maxHeight:_e}}))),e.createElement(T.a,{type:se}))}BannerNotification.propTypes={id:m.a.string.isRequired,className:m.a.string,title:m.a.string.isRequired,description:m.a.node,learnMoreURL:m.a.string,learnMoreDescription:m.a.string,learnMoreLabel:m.a.string,learnMoreTarget:m.a.oneOf(Object.values(R.a)),WinImageSVG:m.a.elementType,SmallImageSVG:m.a.elementType,format:m.a.string,ctaLink:m.a.string,ctaLabel:m.a.string,type:m.a.string,dismiss:m.a.string,isDismissible:m.a.bool,logo:m.a.bool,module:m.a.string,moduleName:m.a.string,dismissExpires:m.a.number,showOnce:m.a.bool,onCTAClick:m.a.func,onView:m.a.func,onDismiss:m.a.func,onLearnMoreClick:m.a.func,badgeLabel:m.a.string,rounded:m.a.bool,footer:m.a.node,secondaryPane:m.a.node,showSmallWinImage:m.a.bool,smallWinImageSVGWidth:m.a.number,smallWinImageSVGHeight:m.a.number,mediumWinImageSVGWidth:m.a.number,mediumWinImageSVGHeight:m.a.number}}).call(this,n(4))},198:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleIcon}));var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(3),u=n(19);function ModuleIcon(t){var n=t.slug,r=t.size,i=o()(t,["slug","size"]),c=Object(l.useSelect)((function(e){return e(u.a).getModuleIcon(n)}));return c?e.createElement(c,a()({width:r,height:r},i)):null}ModuleIcon.propTypes={slug:s.a.string.isRequired,size:s.a.number},ModuleIcon.defaultProps={size:33}}).call(this,n(4))},199:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SupportLink}));var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(3),u=n(13),d=n(20);function SupportLink(t){var n=t.path,r=t.query,i=t.hash,c=o()(t,["path","query","hash"]),s=Object(l.useSelect)((function(e){return e(u.c).getGoogleSupportURL({path:n,query:r,hash:i})}));return e.createElement(d.a,a()({},c,{href:s}))}SupportLink.propTypes={path:s.a.string.isRequired,query:s.a.object,hash:s.a.string}}).call(this,n(4))},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(146),g=n(0),f=n(2),m=n(126),p=n(127),b=n(128),v=n(70),h=n(76),_=Object(g.forwardRef)((function(t,n){var r,i=t["aria-label"],c=t.secondary,l=void 0!==c&&c,u=t.arrow,g=void 0!==u&&u,_=t.back,E=void 0!==_&&_,O=t.caps,k=void 0!==O&&O,y=t.children,S=t.className,j=void 0===S?"":S,w=t.danger,N=void 0!==w&&w,A=t.disabled,C=void 0!==A&&A,T=t.external,x=void 0!==T&&T,R=t.hideExternalIndicator,L=void 0!==R&&R,D=t.href,I=void 0===D?"":D,M=t.inverse,P=void 0!==M&&M,B=t.noFlex,z=void 0!==B&&B,H=t.onClick,F=t.small,V=void 0!==F&&F,U=t.standalone,W=void 0!==U&&U,G=t.linkButton,q=void 0!==G&&G,K=t.to,X=t.leadingIcon,Y=t.trailingIcon,$=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),J=I||K||!H?K?"ROUTER_LINK":x?"EXTERNAL_LINK":"LINK":C?"BUTTON_DISABLED":"BUTTON",Z="BUTTON"===J||"BUTTON_DISABLED"===J?"button":"ROUTER_LINK"===J?d.b:"a",Q=("EXTERNAL_LINK"===J&&(r=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===J&&(r=Object(f._x)("(disabled)","screen reader text","google-site-kit")),r?i?"".concat(i," ").concat(r):"string"==typeof y?"".concat(y," ").concat(r):void 0:i),ee=X,te=Y;return E&&(ee=e.createElement(b.a,{width:14,height:14})),x&&!L&&(te=e.createElement(v.a,{width:14,height:14})),g&&!P&&(te=e.createElement(m.a,{width:14,height:14})),g&&P&&(te=e.createElement(p.a,{width:14,height:14})),e.createElement(Z,a()({"aria-label":Q,className:s()("googlesitekit-cta-link",j,{"googlesitekit-cta-link--secondary":l,"googlesitekit-cta-link--inverse":P,"googlesitekit-cta-link--small":V,"googlesitekit-cta-link--caps":k,"googlesitekit-cta-link--danger":N,"googlesitekit-cta-link--disabled":C,"googlesitekit-cta-link--standalone":W,"googlesitekit-cta-link--link-button":q,"googlesitekit-cta-link--no-flex":!!z}),disabled:C,href:"LINK"!==J&&"EXTERNAL_LINK"!==J||C?void 0:I,onClick:H,rel:"EXTERNAL_LINK"===J?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===J?"_blank":void 0,to:K},$),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},y),!!te&&e.createElement(h.a,{marginLeft:5},te))}));_.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=_}).call(this,n(4))},201:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeSingleRow}));var r=n(1),a=n.n(r),i=n(0);function SettingsNoticeSingleRow(t){var n=t.notice,r=t.LearnMore,a=t.CTA;return e.createElement(i.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),a&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(a,null)))}SettingsNoticeSingleRow.propTypes={notice:a.a.node.isRequired,LearnMore:a.a.elementType,CTA:a.a.elementType}}).call(this,n(4))},210:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeMultiRow}));var r=n(1),a=n.n(r),i=n(0);function SettingsNoticeMultiRow(t){var n=t.notice,r=t.LearnMore,a=t.CTA,o=t.children;return e.createElement(i.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),e.createElement("div",{className:"googlesitekit-settings-notice__inner-row"},e.createElement("div",{className:"googlesitekit-settings-notice__children-container"},o),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),a&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(a,null))))}SettingsNoticeMultiRow.propTypes={children:a.a.node.isRequired,notice:a.a.node.isRequired,LearnMore:a.a.elementType,CTA:a.a.elementType}}).call(this,n(4))},211:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(0),s=n(17),l=Object(c.forwardRef)((function(t,n){var r=t.id,a=t.className,i=t.children,l=t.secondaryPane;return e.createElement("section",{id:r,className:o()(a,"googlesitekit-publisher-win"),ref:n},e.createElement(s.e,null,e.createElement(s.k,null,i)),l&&e.createElement(c.Fragment,null,e.createElement("div",{className:"googlesitekit-publisher-win__secondary-pane-divider"}),e.createElement(s.e,{className:"googlesitekit-publisher-win__secondary-pane"},e.createElement(s.k,null,e.createElement(s.a,{className:"googlesitekit-publisher-win__secondary-pane",size:12},l)))))}));l.displayName="Banner",l.propTypes={id:a.a.string,className:a.a.string,secondaryPane:a.a.node},t.a=l}).call(this,n(4))},212:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerActions}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(15),s=n.n(c),l=n(1),u=n.n(l),d=n(206),g=n(0),f=n(3),m=n(10),p=n(32);function BannerActions(t){var n=t.ctaLink,r=t.ctaLabel,i=t.ctaComponent,c=t.ctaTarget,l=t.ctaCallback,u=t.dismissLabel,b=t.dismissCallback,v=Object(g.useState)(!1),h=s()(v,2),_=h[0],E=h[1],O=Object(d.a)(),k=Object(f.useSelect)((function(e){return!!n&&e(p.a).isNavigatingTo(n)})),y=function(){var e=o()(a.a.mark((function e(){var t,n,r,i=arguments;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(E(!0),t=i.length,n=new Array(t),r=0;r<t;r++)n[r]=i[r];return e.next=4,null==l?void 0:l.apply(void 0,n);case 4:O()&&E(!1);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return n||u||i?e.createElement("div",{className:"googlesitekit-publisher-win__actions"},i,r&&e.createElement(m.SpinnerButton,{className:"googlesitekit-notification__cta",href:n,target:c,onClick:y,disabled:_||k,isSaving:_||k},r),u&&e.createElement(m.Button,{tertiary:n||i,onClick:b,disabled:_||k},u)):null}BannerActions.propTypes={ctaLink:u.a.string,ctaLabel:u.a.string,ctaComponent:u.a.element,ctaTarget:u.a.string,ctaCallback:u.a.func,dismissLabel:u.a.string,dismissCallback:u.a.func}}).call(this,n(4))},213:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerTitle}));var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(11),s=n.n(c),l=n(77);function BannerTitle(t){var n=t.title,r=t.badgeLabel,i=t.WinImageSVG,o=t.winImageFormat,c=void 0===o?"":o,u=t.smallWinImageSVGWidth,d=void 0===u?75:u,g=t.smallWinImageSVGHeight,f=void 0===g?75:g;return n?e.createElement("div",{className:"googlesitekit-publisher-win__title-image-wrapper"},e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n,r&&e.createElement(l.a,{label:r})),i&&e.createElement("div",{className:s()(a()({},"googlesitekit-publisher-win__image-".concat(c),c))},e.createElement(i,{width:d,height:f}))):null}BannerTitle.propTypes={title:o.a.string,badgeLabel:o.a.string,WinImageSVG:o.a.elementType,winImageFormat:o.a.string,smallWinImageSVGWidth:o.a.number,smallWinImageSVGHeight:o.a.number}}).call(this,n(4))},217:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WarningNotice}));var r=n(11),a=n.n(r),i=n(1),o=n.n(i);function WarningNotice(t){var n=t.children,r=t.className;return e.createElement("div",{className:a()("googlesitekit-warning-notice",r)},n)}WarningNotice.propTypes={children:o.a.node.isRequired,className:o.a.string}}).call(this,n(4))},218:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OptIn}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),g=n(38),f=n(2),m=n(3),p=n(10),b=n(7),v=n(36),h=n(20),_=n(18);function OptIn(t){var n=t.id,r=void 0===n?"googlesitekit-opt-in":n,i=t.name,c=void 0===i?"optIn":i,s=t.className,l=t.trackEventCategory,E=t.alignLeftCheckbox,O=void 0!==E&&E,k=Object(m.useSelect)((function(e){return e(b.a).isTrackingEnabled()})),y=Object(m.useSelect)((function(e){return e(b.a).isSavingTrackingEnabled()})),S=Object(m.useSelect)((function(e){return e(b.a).getErrorForAction("setTrackingEnabled",[!k])})),j=Object(m.useDispatch)(b.a).setTrackingEnabled,w=Object(_.a)(),N=Object(d.useCallback)(function(){var e=o()(a.a.mark((function e(t){var n,r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,j(!!t.target.checked);case 2:n=e.sent,r=n.response,n.error||(Object(v.a)(r.enabled),r.enabled&&Object(v.b)(l||w,"tracking_optin"));case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[j,l,w]);return e.createElement("div",{className:u()("googlesitekit-opt-in",s)},e.createElement(p.Checkbox,{id:r,name:c,value:"1",checked:k,disabled:y,onChange:N,loading:void 0===k,alignLeft:O},Object(g.a)(Object(f.__)("<span>Help us improve Site Kit by sharing anonymous usage data.</span> <span>All collected data is treated in accordance with the <a>Google Privacy Policy.</a></span>","google-site-kit"),{a:e.createElement(h.a,{key:"link",href:"https://policies.google.com/privacy",external:!0}),span:e.createElement("span",null)})),(null==S?void 0:S.message)&&e.createElement("div",{className:"googlesitekit-error-text"},null==S?void 0:S.message))}OptIn.propTypes={id:s.a.string,name:s.a.string,className:s.a.string,trackEventCategory:s.a.string,alignLeftCheckbox:s.a.bool}}).call(this,n(4))},22:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return a})),n.d(t,"o",(function(){return i})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return s})),n.d(t,"s",(function(){return l})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"u",(function(){return m})),n.d(t,"v",(function(){return p})),n.d(t,"q",(function(){return b})),n.d(t,"p",(function(){return v})),n.d(t,"b",(function(){return h})),n.d(t,"e",(function(){return _})),n.d(t,"a",(function(){return E})),n.d(t,"d",(function(){return O})),n.d(t,"c",(function(){return k})),n.d(t,"f",(function(){return y})),n.d(t,"g",(function(){return S}));var r="mainDashboard",a="entityDashboard",i="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",s="activation",l="splash",u="adminBar",d="adminBarViewOnly",g="settings",f="adBlockingRecovery",m="wpDashboard",p="wpDashboardViewOnly",b="moduleSetup",v="metricSelection",h="key-metrics",_="traffic",E="content",O="speed",k="monetization",y=[r,a,i,o,c,l,g,b,v],S=[i,o,d,p]},220:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Item}));var r=n(1),a=n.n(r);function Item(t){var n=t.icon,r=t.label;return e.createElement("div",{className:"googlesitekit-user-menu__item"},e.createElement("div",{className:"googlesitekit-user-menu__item-icon"},n),e.createElement("span",{className:"googlesitekit-user-menu__item-label"},r))}Item.propTypes={icon:a.a.node,label:a.a.string}}).call(this,n(4))},222:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var r=n(1),a=n.n(r),i=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var r="win-warning"===n?e.createElement(i.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},r))}BannerIcon.propTypes={type:a.a.string}}).call(this,n(4))},224:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Root}));var r=n(15),a=n.n(r),i=n(1),o=n.n(i),c=n(432),s=n(534),l=n(0),u=n(3),d=n.n(u),g=n(225),f=n(229),m=n(57),p=n(230),b=n(232),v=n(233),h=n(61),_=n(160),E=n(173);function Root(t){var n=t.children,r=t.registry,i=t.viewContext,o=void 0===i?null:i,d=c.a,O=Object(l.useState)({key:"Root",value:!0}),k=a()(O,1)[0];return e.createElement(l.StrictMode,null,e.createElement(_.a,{value:k},e.createElement(u.RegistryProvider,{value:r},e.createElement(f.a,{value:m.a},e.createElement(h.a,{value:o},e.createElement(s.a,{theme:d()},e.createElement(g.a,null,e.createElement(b.a,null,n,o&&e.createElement(v.a,null)),Object(E.a)(o)&&e.createElement(p.a,null))))))))}Root.propTypes={children:o.a.node,registry:o.a.object,viewContext:o.a.string.isRequired},Root.defaultProps={registry:d.a}}).call(this,n(4))},225:function(e,t,n){"use strict";(function(e,r){var a=n(51),i=n.n(a),o=n(53),c=n.n(o),s=n(68),l=n.n(s),u=n(69),d=n.n(u),g=n(49),f=n.n(g),m=n(1),p=n.n(m),b=n(0),v=n(2),h=n(172),_=n(61),E=n(197),O=n(9);function k(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var a=f()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return d()(this,n)}}var y=function(t){l()(ErrorHandler,t);var n=k(ErrorHandler);function ErrorHandler(e){var t;return i()(this,ErrorHandler),(t=n.call(this,e)).state={error:null,info:null,copied:!1},t}return c()(ErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t,info:n}),Object(O.I)("react_error","handle_".concat(this.context||"unknown","_error"),"".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,a=t.info;return n?r.createElement(E.a,{id:"googlesitekit-error",className:"googlesitekit-error-handler",title:Object(v.__)("Site Kit encountered an error","google-site-kit"),description:r.createElement(h.a,{message:n.message,componentStack:a.componentStack}),isDismissible:!1,format:"small",type:"win-error"},r.createElement("pre",{className:"googlesitekit-overflow-auto"},n.message,a.componentStack)):e}}]),ErrorHandler}(b.Component);y.contextType=_.b,y.propTypes={children:p.a.node.isRequired},t.a=y}).call(this,n(28),n(4))},226:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var r=n(1),a=n.n(r),i=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var r="win-warning"===n?e.createElement(i.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},r))}BannerIcon.propTypes={type:a.a.string}}).call(this,n(4))},227:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerLogo}));var r=n(1),a=n.n(r),i=n(17),o=n(155),c=n(198);function BannerLogo(t){var n=t.module,r=t.moduleName;return e.createElement(i.a,{size:12},e.createElement("div",{className:"googlesitekit-publisher-win__logo"},n&&e.createElement(c.a,{slug:n,size:19}),!n&&e.createElement(o.a,{height:"34",width:"32"})),r&&e.createElement("div",{className:"googlesitekit-publisher-win__module-name"},r))}BannerLogo.propTypes={module:a.a.string,moduleName:a.a.string}}).call(this,n(4))},228:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerDescription}));var r=n(1),a=n.n(r),i=n(0),o=n(75),c=n(20),s=n(86);function BannerDescription(t){var n=t.description,r=t.learnMoreLabel,a=t.learnMoreURL,l=t.learnMoreTarget,u=t.learnMoreDescription,d=t.onLearnMoreClick;if(!n)return null;var g;return r&&(g=e.createElement(i.Fragment,null,e.createElement(c.a,{onClick:function(e){e.persist(),null==d||d()},href:a,external:l===s.a.EXTERNAL},r),u)),e.createElement("div",{className:"googlesitekit-publisher-win__desc"},Object(i.isValidElement)(n)?e.createElement(i.Fragment,null,n,g&&e.createElement("p",null,g)):e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.a)(n,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",g))}BannerDescription.propTypes={description:a.a.node,learnMoreURL:a.a.string,learnMoreDescription:a.a.string,learnMoreLabel:a.a.string,learnMoreTarget:a.a.oneOf(Object.values(s.a)),onLearnMoreClick:a.a.func}}).call(this,n(4))},229:function(e,t,n){"use strict";var r=n(158),a=(r.a.Consumer,r.a.Provider);t.a=a},23:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a}));var r="core/ui",a="activeContextID"},230:function(e,t,n){"use strict";(function(e){var r=n(3),a=n(231),i=n(7);t.a=function PermissionsModal(){return Object(r.useSelect)((function(e){return e(i.a).isAuthenticated()}))?e.createElement(a.a,null):null}}).call(this,n(4))},231:function(e,t,n){"use strict";(function(e,r){var a=n(5),i=n.n(a),o=n(16),c=n.n(o),s=n(2),l=n(0),u=n(3),d=n(109),g=n(29),f=n(32),m=n(7),p=n(129),b=n(72);t.a=function AuthenticatedPermissionsModal(){var t,n,a,o,v=Object(u.useRegistry)(),h=Object(u.useSelect)((function(e){return e(m.a).getPermissionScopeError()})),_=Object(u.useSelect)((function(e){return e(m.a).getUnsatisfiedScopes()})),E=Object(u.useSelect)((function(t){var n,r,a;return t(m.a).getConnectURL({additionalScopes:null==h||null===(n=h.data)||void 0===n?void 0:n.scopes,redirectURL:(null==h||null===(r=h.data)||void 0===r?void 0:r.redirectURL)||e.location.href,errorRedirectURL:null==h||null===(a=h.data)||void 0===a?void 0:a.errorRedirectURL})})),O=Object(u.useDispatch)(m.a).clearPermissionScopeError,k=Object(u.useDispatch)(f.a).navigateTo,y=Object(u.useDispatch)(g.a).setValues,S=Object(l.useCallback)((function(){O()}),[O]),j=Object(l.useCallback)(c()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return y(m.d,{permissionsError:h}),e.next=3,Object(p.c)(v);case 3:k(E);case 4:case"end":return e.stop()}}),e)}))),[v,E,k,h,y]);return Object(l.useEffect)((function(){(function(){var e=c()(i.a.mark((function e(){var t,n,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(null==h||null===(t=h.data)||void 0===t?void 0:t.skipModal)||!(null==h||null===(n=h.data)||void 0===n||null===(r=n.scopes)||void 0===r?void 0:r.length)){e.next=3;break}return e.next=3,j();case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[j,h]),h?(null==h||null===(t=h.data)||void 0===t||null===(n=t.scopes)||void 0===n?void 0:n.length)?(null==h||null===(a=h.data)||void 0===a?void 0:a.skipModal)||_&&(null==h||null===(o=h.data)||void 0===o?void 0:o.scopes.every((function(e){return _.includes(e)})))?null:r.createElement(b.a,null,r.createElement(d.a,{title:Object(s.__)("Additional Permissions Required","google-site-kit"),subtitle:h.message,confirmButton:Object(s.__)("Proceed","google-site-kit"),dialogActive:!0,handleConfirm:j,handleDialog:S,medium:!0})):(e.console.warn("permissionsError lacks scopes array to use for redirect, so not showing the PermissionsModal. permissionsError was:",h),null):null}}).call(this,n(28),n(4))},232:function(e,t,n){"use strict";var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(15),s=n.n(c),l=n(0),u=n(3),d=n(129);t.a=function RestoreSnapshots(e){var t=e.children,n=Object(u.useRegistry)(),r=Object(l.useState)(!1),i=s()(r,2),c=i[0],g=i[1];return Object(l.useEffect)((function(){c||o()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(d.b)(n);case 2:g(!0);case 3:case"end":return e.stop()}}),e)})))()}),[n,c]),c?t:null}},233:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return FeatureTours}));var a=n(81),i=n(0),o=n(3),c=n(7),s=n(18),l=n(90);function FeatureTours(){var t=Object(s.a)(),n=Object(o.useDispatch)(c.a).triggerTourForView;Object(a.a)((function(){n(t)}));var u=Object(o.useSelect)((function(e){return e(c.a).getCurrentTour()}));return Object(i.useEffect)((function(){if(u){var t=document.getElementById("js-googlesitekit-main-dashboard");if(t){var n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}}),[u]),u?r.createElement(l.a,{tourID:u.slug,steps:u.steps,gaEventCategory:u.gaEventCategory,callback:u.callback}):null}}).call(this,n(28),n(4))},234:function(e,t,n){"use strict";(function(e){var r=n(15),a=n.n(r),i=n(1),o=n.n(i),c=n(11),s=n.n(c),l=n(590),u=n(2),d=n(0),g=n(3),f=n(256),m=n(276),p=n(280),b=n(7),v=n(17),h=n(284),_=n(291),E=n(293),O=n(34),k=n(52),y=n(20),S=n(299),j=n(13),w=n(300);function Header(t){var n,r=t.children,i=t.subHeader,o=t.showNavigation,c=!!Object(k.c)(),N=Object(O.a)();Object(w.a)();var A=Object(g.useSelect)((function(e){return e(j.c).getAdminURL("googlesitekit-dashboard")})),C=Object(g.useSelect)((function(e){return e(b.a).isAuthenticated()})),T=Object(l.a)({childList:!0}),x=a()(T,2),R=x[0],L=!!(null===(n=x[1].target)||void 0===n?void 0:n.childElementCount);return e.createElement(d.Fragment,null,e.createElement("header",{className:s()("googlesitekit-header",{"googlesitekit-header--has-subheader":L,"googlesitekit-header--has-navigation":o})},e.createElement(v.e,null,e.createElement(v.k,null,e.createElement(v.a,{smSize:1,mdSize:2,lgSize:4,className:"googlesitekit-header__logo",alignMiddle:!0},e.createElement(y.a,{"aria-label":Object(u.__)("Go to dashboard","google-site-kit"),className:"googlesitekit-header__logo-link",href:A},e.createElement(f.a,null))),e.createElement(v.a,{smSize:3,mdSize:6,lgSize:8,className:"googlesitekit-header__children",alignMiddle:!0},r,!C&&c&&N&&e.createElement(E.a,null),C&&!N&&e.createElement(m.a,null))))),e.createElement("div",{className:"googlesitekit-subheader",ref:R},e.createElement(p.a,null),i),o&&e.createElement(h.a,null),c&&e.createElement(S.a,null),e.createElement(_.a,null))}Header.displayName="Header",Header.propTypes={children:o.a.node,subHeader:o.a.element,showNavigation:o.a.bool},Header.defaultProps={children:null,subHeader:null},t.a=Header}).call(this,n(4))},235:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return HelpMenu}));var r=n(15),a=n.n(r),i=n(1),o=n.n(i),c=n(209),s=n(0),l=n(56),u=n(2),d=n(3),g=n(10),f=n(301),m=n(112),p=n(9),b=n(164),v=n(19),h=n(18),_=n(13);function HelpMenu(t){var n=t.children,r=Object(s.useState)(!1),i=a()(r,2),o=i[0],E=i[1],O=Object(s.useRef)(),k=Object(h.a)();Object(c.a)(O,(function(){return E(!1)})),Object(m.a)([l.c,l.f],O,(function(){return E(!1)}));var y=Object(d.useSelect)((function(e){return e(v.a).isModuleActive("adsense")})),S=Object(s.useCallback)((function(){o||Object(p.I)("".concat(k,"_headerbar"),"open_helpmenu"),E(!o)}),[o,k]),j=Object(s.useCallback)((function(){E(!1)}),[]),w=Object(d.useSelect)((function(e){return e(_.c).getDocumentationLinkURL("fix-common-issues")}));return e.createElement("div",{ref:O,className:"googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},e.createElement(g.Button,{"aria-controls":"googlesitekit-help-menu","aria-expanded":o,"aria-label":Object(u.__)("Help","google-site-kit"),"aria-haspopup":"menu",className:"googlesitekit-header__dropdown googlesitekit-border-radius-round googlesitekit-button-icon googlesitekit-help-menu__button mdc-button--dropdown",icon:e.createElement(f.a,{width:"20",height:"20"}),onClick:S,text:!0,tooltipEnterDelayInMS:500}),e.createElement(g.Menu,{className:"googlesitekit-width-auto",menuOpen:o,id:"googlesitekit-help-menu",onSelected:j},n,e.createElement(b.a,{gaEventLabel:"fix_common_issues",href:w},Object(u.__)("Fix common issues","google-site-kit")),e.createElement(b.a,{gaEventLabel:"documentation",href:"https://sitekit.withgoogle.com/documentation/"},Object(u.__)("Read help docs","google-site-kit")),e.createElement(b.a,{gaEventLabel:"support_forum",href:"https://wordpress.org/support/plugin/google-site-kit/"},Object(u.__)("Get support","google-site-kit")),y&&e.createElement(b.a,{gaEventLabel:"adsense_help",href:"https://support.google.com/adsense/"},Object(u.__)("Get help with AdSense","google-site-kit"))))}HelpMenu.propTypes={children:o.a.node}}).call(this,n(4))},24:function(e,t,n){"use strict";n.d(t,"d",(function(){return a})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return s}));var r=n(79),a="xlarge",i="desktop",o="tablet",c="small";function s(){var e=Object(r.a)();return e>1280?a:e>960?i:e>600?o:c}},245:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdBlockerWarning}));var r=n(1),a=n.n(r),i=n(3),o=n(13),c=n(19),s=n(395);function AdBlockerWarning(t){var n=t.moduleSlug,r=t.className,a=Object(i.useSelect)((function(e){return e(c.a).getModuleStoreName(n)})),l=Object(i.useSelect)((function(e){var t;return null===(t=e(a))||void 0===t?void 0:t.getAdBlockerWarningMessage()})),u=Object(i.useSelect)((function(e){return e(o.c).getDocumentationLinkURL("".concat(n,"-ad-blocker-detected"))}));return e.createElement(s.a,{className:r,getHelpLink:u,warningMessage:l})}AdBlockerWarning.propTypes={className:a.a.string,moduleSlug:a.a.string.isRequired}}).call(this,n(4))},249:function(e,t,n){"use strict";(function(e){var r=n(15),a=n.n(r),i=n(0);t.a=function(t,n){var r=Object(i.useState)(null),o=a()(r,2),c=o[0],s=o[1];return Object(i.useEffect)((function(){if(t.current&&"function"==typeof e.IntersectionObserver){var r=new e.IntersectionObserver((function(e){s(e[e.length-1])}),n);return r.observe(t.current),function(){s(null),r.disconnect()}}return function(){}}),[t.current,n.threshold,n.root,n.rootMargin]),c}}).call(this,n(28))},256:function(e,t,n){"use strict";(function(e){var r=n(2),a=n(155),i=n(257),o=n(105);t.a=function Logo(){return e.createElement("div",{className:"googlesitekit-logo","aria-hidden":"true"},e.createElement(a.a,{className:"googlesitekit-logo__logo-g",height:"34",width:"32"}),e.createElement(i.a,{className:"googlesitekit-logo__logo-sitekit",height:"26",width:"99"}),e.createElement(o.a,null,Object(r.__)("Site Kit by Google Logo","google-site-kit")))}}).call(this,n(4))},257:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M62.09 1.664h3.038v.1L58.34 9.593l7.241 10.224v.1H62.7L56.755 11.4 53.95 14.64v5.278h-2.351V1.664h2.35v9.415h.1l8.04-9.415zM69.984 3.117c0 .454-.166.853-.487 1.175-.322.322-.71.488-1.176.488-.455 0-.854-.166-1.175-.488a1.599 1.599 0 01-.488-1.175c0-.466.166-.854.488-1.176.321-.322.71-.488 1.175-.488.455 0 .854.166 1.176.488.332.333.487.72.487 1.176zm-.476 4.313v12.498h-2.351V7.43h2.35zM77.016 20.128c-1.02 0-1.864-.31-2.54-.943-.676-.632-1.02-1.508-1.031-2.628V9.57h-2.196V7.43h2.196V3.603h2.35V7.43h3.061v2.14h-3.06v6.222c0 .831.166 1.397.488 1.696.321.3.687.444 1.097.444.189 0 .366-.022.555-.067.188-.044.344-.1.499-.166l.743 2.096c-.632.222-1.342.333-2.162.333zM2.673 18.952C1.375 18.009.488 16.678 0 14.97l2.883-1.176c.289 1.076.799 1.94 1.542 2.628.732.677 1.619 1.02 2.65 1.02.965 0 1.774-.244 2.45-.742.677-.5 1.01-1.187 1.01-2.052 0-.798-.3-1.453-.887-1.974-.588-.521-1.62-1.042-3.094-1.564l-1.22-.432C4.025 10.224 2.928 9.57 2.04 8.716 1.153 7.862.71 6.742.71 5.346c0-.966.266-1.853.787-2.673C2.018 1.852 2.75 1.209 3.693.72 4.624.244 5.678 0 6.864 0c1.708 0 3.072.41 4.081 1.242 1.02.832 1.697 1.752 2.04 2.795L10.236 5.2c-.2-.621-.576-1.164-1.142-1.63-.565-.477-1.286-.71-2.173-.71s-1.641.222-2.251.676c-.61.455-.91 1.032-.91 1.742 0 .676.278 1.22.82 1.663.544.432 1.398.854 2.563 1.253l1.22.41c1.674.577 2.96 1.342 3.88 2.274.921.931 1.376 2.184 1.376 3.748 0 1.275-.322 2.34-.976 3.193a6.01 6.01 0 01-2.495 1.919 8.014 8.014 0 01-3.116.621c-1.62 0-3.072-.466-4.358-1.408zM15.969 3.449a1.95 1.95 0 01-.588-1.43c0-.566.2-1.043.588-1.431A1.95 1.95 0 0117.399 0c.566 0 1.043.2 1.43.588.389.388.588.865.588 1.43 0 .566-.2 1.043-.587 1.43a1.95 1.95 0 01-1.43.589c-.566-.012-1.043-.2-1.431-.588zm-.067 2.595h2.994v13.883h-2.994V6.044zM25.405 19.85c-.543-.2-.986-.466-1.33-.788-.776-.776-1.176-1.84-1.176-3.182V8.683h-2.428v-2.64h2.428V2.13h2.994v3.926h3.372v2.639h-3.372v6.531c0 .743.145 1.276.433 1.575.277.366.743.543 1.42.543.31 0 .576-.044.82-.122.233-.077.488-.21.765-.399v2.917c-.599.277-1.32.41-2.173.41a5.01 5.01 0 01-1.753-.3zM33.623 19.407a6.63 6.63 0 01-2.529-2.628c-.61-1.12-.909-2.373-.909-3.77 0-1.332.3-2.551.887-3.693.588-1.132 1.409-2.04 2.462-2.706 1.053-.666 2.251-1.01 3.593-1.01 1.397 0 2.606.311 3.637.921a6.123 6.123 0 012.34 2.528c.532 1.076.799 2.274.799 3.627 0 .255-.023.576-.078.953H33.179c.111 1.287.566 2.285 1.375 2.983a4.162 4.162 0 002.817 1.043c.854 0 1.597-.189 2.218-.588a4.266 4.266 0 001.508-1.597l2.528 1.198c-.654 1.142-1.508 2.04-2.561 2.694-1.054.655-2.318.976-3.782.976-1.364.022-2.584-.288-3.66-.931zm7.23-8.051a3.332 3.332 0 00-.466-1.453c-.277-.477-.687-.887-1.242-1.208-.554-.322-1.23-.488-2.03-.488-.964 0-1.773.288-2.439.853-.665.566-1.12 1.342-1.375 2.296h7.552z",fill:"#5F6368"});t.a=function SvgLogoSitekit(e){return r.createElement("svg",a({viewBox:"0 0 80 21",fill:"none"},e),i)}},276:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return UserMenu}));var a=n(5),i=n.n(a),o=n(16),c=n.n(o),s=n(15),l=n.n(s),u=n(209),d=n(0),g=n(2),f=n(56),m=n(3),p=n(10),b=n(109),v=n(9),h=n(37),_=n(72),E=n(277),O=n(220),k=n(278),y=n(279),S=n(29),j=n(13),w=n(7),N=n(32),A=n(8),C=n(112),T=n(18);function UserMenu(){var t=Object(m.useSelect)((function(e){return e(j.c).getProxyPermissionsURL()})),n=Object(m.useSelect)((function(e){return e(w.a).getEmail()})),a=Object(m.useSelect)((function(e){return e(w.a).getPicture()})),o=Object(m.useSelect)((function(e){return e(w.a).getFullName()})),s=Object(m.useSelect)((function(e){return e(j.c).getAdminURL("googlesitekit-splash",{googlesitekit_context:"revoked"})})),x=Object(m.useSelect)((function(e){return e(S.a).getValue(A.d,"isAutoCreatingCustomDimensionsForAudience")})),R=Object(d.useState)(!1),L=l()(R,2),D=L[0],I=L[1],M=Object(d.useState)(!1),P=l()(M,2),B=P[0],z=P[1],H=Object(d.useRef)(),F=Object(d.useRef)(),V=Object(T.a)(),U=Object(m.useDispatch)(N.a).navigateTo;Object(u.a)(H,(function(){return z(!1)})),Object(C.a)([f.c,f.f],H,(function(){var e;z(!1),null===(e=F.current)||void 0===e||e.focus()})),Object(d.useEffect)((function(){var t=function(e){f.c===e.keyCode&&(I(!1),z(!1))};return e.addEventListener("keyup",t),function(){e.removeEventListener("keyup",t)}}),[]);var W,G=Object(d.useCallback)((function(){B||Object(v.I)("".concat(V,"_headerbar"),"open_usermenu"),z(!B)}),[B,V]),q=Object(d.useCallback)((function(){I(!D),z(!1)}),[D]),K=Object(d.useCallback)(function(){var e=c()(i.a.mark((function e(n,r){var a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:a=r.detail.item,e.t0=null==a?void 0:a.id,e.next="manage-sites"===e.t0?4:"disconnect"===e.t0?9:11;break;case 4:if(!t){e.next=8;break}return e.next=7,Object(v.I)("".concat(V,"_headerbar_usermenu"),"manage_sites");case 7:U(t);case 8:return e.abrupt("break",12);case 9:return q(),e.abrupt("break",12);case 11:G();case 12:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),[t,G,q,U,V]),X=Object(d.useCallback)(c()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return I(!1),e.next=3,Object(h.b)();case 3:return e.next=5,Object(v.I)("".concat(V,"_headerbar_usermenu"),"disconnect_user");case 5:U(s);case 6:case"end":return e.stop()}}),e)}))),[s,U,V]);return n?(o&&n&&(W=Object(g.sprintf)(
/* translators: Account info text. 1: User's (full) name 2: User's email address. */
Object(g.__)("Google Account for %1$s (Email: %2$s)","google-site-kit"),o,n)),o&&!n&&(W=Object(g.sprintf)(
/* translators: Account info text. 1: User's (full) name. */
Object(g.__)("Google Account for %1$s","google-site-kit"),o)),!o&&n&&(W=Object(g.sprintf)(
/* translators: Account info text. 1: User's email address. */
Object(g.__)("Google Account (Email: %1$s)","google-site-kit"),n)),r.createElement(d.Fragment,null,r.createElement("div",{ref:H,className:"googlesitekit-user-selector googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},r.createElement(p.Button,{disabled:x,ref:F,className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--tablet googlesitekit-border-radius-round--phone googlesitekit-border-radius-round googlesitekit-button-icon",text:!0,onClick:G,icon:!!a&&r.createElement("i",{className:"mdc-button__icon mdc-button__account","aria-hidden":"true"},r.createElement("img",{className:"mdc-button__icon--image",src:a,alt:Object(g.__)("User Avatar","google-site-kit")})),"aria-haspopup":"menu","aria-expanded":B,"aria-controls":"user-menu","aria-label":x?void 0:Object(g.__)("Account","google-site-kit"),tooltip:!0,tooltipEnterDelayInMS:500,customizedTooltip:x?null:r.createElement("span",{"aria-label":W},r.createElement("strong",null,Object(g.__)("Google Account","google-site-kit")),r.createElement("br",null),r.createElement("br",null),o,o&&r.createElement("br",null),n)}),r.createElement(p.Menu,{className:"googlesitekit-user-menu",menuOpen:B,onSelected:K,id:"user-menu"},r.createElement("li",null,r.createElement(E.a,null)),!!t&&r.createElement("li",{id:"manage-sites",className:"mdc-list-item",role:"menuitem"},r.createElement(O.a,{icon:r.createElement(y.a,{width:"22"}),label:Object(g.__)("Manage Sites","google-site-kit")})),r.createElement("li",{id:"disconnect",className:"mdc-list-item",role:"menuitem"},r.createElement(O.a,{icon:r.createElement(k.a,{width:"22"}),label:Object(g.__)("Disconnect","google-site-kit")})))),r.createElement(_.a,null,r.createElement(b.a,{dialogActive:D,handleConfirm:X,handleDialog:q,title:Object(g.__)("Disconnect","google-site-kit"),subtitle:Object(g.__)("Disconnecting Site Kit by Google will remove your access to all services. After disconnecting, you will need to re-authorize to restore service.","google-site-kit"),confirmButton:Object(g.__)("Disconnect","google-site-kit"),danger:!0,small:!0})))):null}}).call(this,n(28),n(4))},277:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Details}));var r=n(2),a=n(3),i=n(7);function Details(){var t=Object(a.useSelect)((function(e){return e(i.a).getPicture()})),n=Object(a.useSelect)((function(e){return e(i.a).getFullName()})),o=Object(a.useSelect)((function(e){return e(i.a).getEmail()}));return e.createElement("div",{className:"googlesitekit-user-menu__details","aria-label":Object(r.__)("Google account","google-site-kit")},!!t&&e.createElement("img",{className:"googlesitekit-user-menu__details-avatar",src:t,alt:""}),e.createElement("div",{className:"googlesitekit-user-menu__details-info"},e.createElement("p",{className:"googlesitekit-user-menu__details-info__name"},n),e.createElement("p",{className:"googlesitekit-user-menu__details-info__email","aria-label":Object(r.__)("Email","google-site-kit")},o)))}}).call(this,n(4))},278:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M6.83 2H20a2 2 0 012 2v12c0 .34-.09.66-.23.94L20 15.17V6h-9.17l-4-4zm13.66 19.31L17.17 18H4a2 2 0 01-2-2V4c0-.34.08-.66.23-.94L.69 1.51 2.1.1l19.8 19.8-1.41 1.41zM15.17 16l-10-10H4v10h11.17z",fill:"currentColor"});t.a=function SvgDisconnect(e){return r.createElement("svg",a({viewBox:"0 0 22 22",fill:"none"},e),i)}},279:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M20 0H2C.9 0 0 .9 0 2v12c0 1.1.9 2 2 2h5v2h8v-2h5c1.1 0 2-.9 2-2V2c0-1.1-.9-2-2-2zm0 14H2V2h18v12zm-2-9H7v2h11V5zm0 4H7v2h11V9zM6 5H4v2h2V5zm0 4H4v2h2V9z",fill:"currentColor"});t.a=function SvgManageSites(e){return r.createElement("svg",a({viewBox:"0 0 22 18",fill:"none"},e),i)}},280:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotifications}));var r=n(0),a=n(281),i=n(167),o=n(41);function ErrorNotifications(){return e.createElement(r.Fragment,null,e.createElement(a.a,null),e.createElement(i.a,{areaSlug:o.b.ERRORS}))}}).call(this,n(4))},281:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InternalServerError}));var r=n(3),a=n(13),i=n(196),o=n(191),c=n(111);function InternalServerError(){var t=Object(r.useSelect)((function(e){return e(a.c).getInternalServerError()}));return t?e.createElement(o.a,{className:"googlesitekit-publisher-win googlesitekit-publisher-win--win-error"},e.createElement(i.a,{title:t.title,description:e.createElement(c.a,{text:t.description})})):null}}).call(this,n(4))},282:function(e,t,n){"use strict";n.d(t,"a",(function(){return ViewedStateObserver}));var r=n(1),a=n.n(r),i=n(0),o=n(3),c=n(23),s=n(249),l=n(161);function ViewedStateObserver(e){var t=e.id,n=e.observeRef,r=e.threshold,a=Object(s.a)(n,{threshold:r}),u=Object(o.useDispatch)(c.b).setValue,d=!!(null==a?void 0:a.isIntersecting),g=Object(l.a)(t);return Object(i.useEffect)((function(){!g&&d&&u(l.a.getKey(t),!0)}),[g,d,u,t]),null}ViewedStateObserver.propTypes={id:a.a.string,observeRef:a.a.object,threshold:a.a.number}},283:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return s}));var r=n(21),a=n.n(r),i=n(63),o=n.n(i),c=n(191),s=o()((function(e){return{id:e,Notification:l(e)(c.a)}}));function l(t){return function(n){function WithNotificationID(r){return e.createElement(n,a()({},r,{id:t}))}return WithNotificationID.displayName="WithNotificationID",(n.displayName||n.name)&&(WithNotificationID.displayName+="(".concat(n.displayName||n.name,")")),WithNotificationID}}}).call(this,n(4))},284:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardNavigation}));var r=n(3),a=n(7),i=n(34),o=n(183),c=n(285);function DashboardNavigation(){var t=Object(i.a)(),n=Object(r.useSelect)((function(e){return t?e(a.a).getViewableModules():null})),s=Object(r.useSelect)((function(e){return e(a.a).getKeyMetrics()}));return e.createElement(o.a,{loading:void 0===n||void 0===s,width:"100%",smallHeight:"59px",height:"71px"},e.createElement(c.a,null))}}).call(this,n(4))},285:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return Navigation}));var a=n(27),i=n.n(a),o=n(15),c=n.n(o),s=n(11),l=n.n(s),u=n(14),d=n(81),g=n(153),f=n(0),m=n(2),p=n(3),b=n(286),v=n(287),h=n(288),_=n(289),E=n(290),O=n(22),k=n(7),y=n(47),S=n(23),j=n(71),w=n(52),N=n(24),A=n(93),C=n(9),T=n(18),x=n(34);function R(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return L(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return L(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function L(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Navigation(){var t,n=Object(w.c)(),a=Object(f.useRef)(),o=Object(N.e)(),s=null===(t=e.location.hash)||void 0===t?void 0:t.substring(1),L=Object(f.useState)(s),D=c()(L,2),I=D[0],M=D[1],P=Object(f.useState)(s||void 0),B=c()(P,2),z=B[0],H=B[1],F=Object(f.useState)(!1),V=c()(F,2),U=V[0],W=V[1],G=Object(T.a)(),q=Object(x.a)(),K=Object(p.useDispatch)(S.b).setValue,X=Object(p.useSelect)((function(e){return q?e(k.a).getViewableModules():null})),Y=Object(p.useSelect)((function(e){return e(k.a).isKeyMetricsWidgetHidden()})),$={modules:X||void 0},J=Object(p.useSelect)((function(e){return n===w.b&&!0!==Y&&e(y.a).isWidgetContextActive(j.CONTEXT_MAIN_DASHBOARD_KEY_METRICS,$)})),Z=Object(p.useSelect)((function(e){return e(y.a).isWidgetContextActive(n===w.b?j.CONTEXT_MAIN_DASHBOARD_TRAFFIC:j.CONTEXT_ENTITY_DASHBOARD_TRAFFIC,$)})),Q=Object(p.useSelect)((function(e){return e(y.a).isWidgetContextActive(n===w.b?j.CONTEXT_MAIN_DASHBOARD_CONTENT:j.CONTEXT_ENTITY_DASHBOARD_CONTENT,$)})),ee=Object(p.useSelect)((function(e){return e(y.a).isWidgetContextActive(n===w.b?j.CONTEXT_MAIN_DASHBOARD_SPEED:j.CONTEXT_ENTITY_DASHBOARD_SPEED,$)})),te=Object(p.useSelect)((function(e){return e(y.a).isWidgetContextActive(n===w.b?j.CONTEXT_MAIN_DASHBOARD_MONETIZATION:j.CONTEXT_ENTITY_DASHBOARD_MONETIZATION,$)})),ne=Object(f.useCallback)((function(){return J?O.b:q?Z?O.e:Q?O.a:ee?O.d:te?O.c:"":O.e}),[J,Z,Q,ee,te,q]),re=Object(f.useCallback)((function(t){var n,r=t.target.closest(".mdc-chip"),a=null==r||null===(n=r.dataset)||void 0===n?void 0:n.contextId;e.history.replaceState({},"","#".concat(a)),H(a),Object(C.I)("".concat(G,"_navigation"),"tab_select",a),e.scrollTo({top:a!==ne()?Object(A.a)("#".concat(a),o):0,behavior:"smooth"}),setTimeout((function(){K(S.a,a)}),50)}),[o,G,K,ne]);return Object(d.a)((function(){var t=ne();if(!s)return M(t),void setTimeout((function(){return e.history.replaceState({},"","#".concat(t))}));var n=s;(function(e){return!(!J||e!==O.b)||(!(!Z||e!==O.e)||(!(!Q||e!==O.a)||(!(!ee||e!==O.d)||!(!te||e!==O.c))))})(n)||(n=t),K(S.a,n),M(n),setTimeout((function(){var r=n!==t?Object(A.a)("#".concat(n),o):0;e.scrollY!==r?e.scrollTo({top:r,behavior:"smooth"}):K(S.a,void 0)}),50)})),Object(f.useEffect)((function(){var t=function(e){K(S.a,void 0),M(e),H(void 0)},n=Object(u.throttle)((function(n){var r,o,c,s,l=e.scrollY,u=null===(r=document.querySelector(".googlesitekit-entity-header"))||void 0===r||null===(o=r.getBoundingClientRect())||void 0===o?void 0:o.bottom,d=null==a||null===(c=a.current)||void 0===c?void 0:c.getBoundingClientRect(),g=d.bottom,f=d.top,m=[].concat(i()(J?[O.b]:[]),i()(Z?[O.e]:[]),i()(Q?[O.a]:[]),i()(ee?[O.d]:[]),i()(te?[O.c]:[])),p=ne();if(0===l)W(!1);else{var b,v=null===(b=document.querySelector(".googlesitekit-header"))||void 0===b?void 0:b.getBoundingClientRect().bottom;W(f===v)}var h,_=R(m);try{for(_.s();!(h=_.n()).done;){var E=h.value,k=document.getElementById(E);if(k){var y=k.getBoundingClientRect().top-20-(u||g||0);y<0&&(void 0===s||s<y)&&(s=y,p=E)}}}catch(e){_.e(e)}finally{_.f()}if(z)z===p&&t(p);else{var S=e.location.hash;p!==(null==S?void 0:S.substring(1))&&(n&&Object(C.I)("".concat(G,"_navigation"),"tab_scroll",p),e.history.replaceState({},"","#".concat(p)),t(p))}}),150);return e.addEventListener("scroll",n),function(){e.removeEventListener("scroll",n)}}),[z,J,Z,Q,ee,te,G,K,ne]),r.createElement("nav",{className:l()("mdc-chip-set","googlesitekit-navigation","googlesitekit-navigation--".concat(n),{"googlesitekit-navigation--is-sticky":U}),ref:a},J&&r.createElement(g.Chip,{id:O.b,label:Object(m.__)("Key metrics","google-site-kit"),leadingIcon:r.createElement(b.a,{width:"18",height:"16"}),onClick:re,selected:I===O.b,"data-context-id":O.b}),Z&&r.createElement(g.Chip,{id:O.e,label:Object(m.__)("Traffic","google-site-kit"),leadingIcon:r.createElement(v.a,{width:"18",height:"16"}),onClick:re,selected:I===O.e,"data-context-id":O.e}),Q&&r.createElement(g.Chip,{id:O.a,label:Object(m.__)("Content","google-site-kit"),leadingIcon:r.createElement(h.a,{width:"18",height:"18"}),onClick:re,selected:I===O.a,"data-context-id":O.a}),ee&&r.createElement(g.Chip,{id:O.d,label:Object(m.__)("Speed","google-site-kit"),leadingIcon:r.createElement(_.a,{width:"20",height:"16"}),onClick:re,selected:I===O.d,"data-context-id":O.d}),te&&r.createElement(g.Chip,{id:O.c,label:Object(m.__)("Monetization","google-site-kit"),leadingIcon:r.createElement(E.a,{width:"18",height:"16"}),onClick:re,selected:I===O.c,"data-context-id":O.c}))}}).call(this,n(28),n(4))},286:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("rect",{x:.5,width:5,height:5,rx:1,fill:"currentColor"}),o=r.createElement("rect",{x:7.5,width:5,height:5,rx:1,fill:"currentColor"}),c=r.createElement("rect",{x:.5,y:7,width:5,height:5,rx:1,fill:"currentColor"}),s=r.createElement("rect",{x:7.5,y:7,width:5,height:5,rx:1,fill:"currentColor"});t.a=function SvgNavKeyMetricsIcon(e){return r.createElement("svg",a({viewBox:"0 0 13 12",fill:"none"},e),i,o,c,s)}},287:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 0h3.971v16H7V0zM0 8h4v8H0V8zm18-3h-4v11h4V5z",fill:"currentColor"});t.a=function SvgNavTrafficIcon(e){return r.createElement("svg",a({viewBox:"0 0 18 16",fill:"none"},e),i)}},288:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 16V2c0-1.1-1-2-2.222-2H2.222C1 0 0 .9 0 2v14c0 1.1 1 2 2.222 2h13.556C17 18 18 17.1 18 16zM9 7h5V5H9v2zm7-5H2v14h14V2zM4 4h4v4H4V4zm10 7H9v2h5v-2zM4 10h4v4H4v-4z",fill:"currentColor"});t.a=function SvgNavContentIcon(e){return r.createElement("svg",a({viewBox:"0 0 18 18",fill:"none"},e),i)}},289:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M18.378 4.543l-1.232 1.854a8.024 8.024 0 01-.22 7.598H3.043A8.024 8.024 0 014.154 4.49 8.011 8.011 0 0113.57 2.82l1.853-1.233A10.01 10.01 0 003.117 2.758a10.026 10.026 0 00-1.797 12.24A2.004 2.004 0 003.043 16h13.873a2.003 2.003 0 001.742-1.002 10.03 10.03 0 00-.27-10.465l-.01.01z",fill:"currentColor"}),o=r.createElement("path",{d:"M8.572 11.399a2.003 2.003 0 002.835 0l5.669-8.51-8.504 5.673a2.005 2.005 0 000 2.837z",fill:"currentColor"});t.a=function SvgNavSpeedIcon(e){return r.createElement("svg",a({viewBox:"0 0 20 16",fill:"none"},e),i,o)}},29:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/forms"},290:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M16.1 0v2h2.967l-5.946 5.17-4.6-4L0 10.59 1.621 12l6.9-6 4.6 4L20.7 3.42V6H23V0h-6.9z",fill:"currentColor"});t.a=function SvgNavMonetizationIcon(e){return r.createElement("svg",a({viewBox:"0 0 23 12",fill:"none"},e),i)}},291:function(e,t,n){"use strict";(function(e,r){var a=n(15),i=n.n(a),o=n(14),c=n(2),s=n(0),l=n(3),u=n(10),d=n(13),g=n(292),f=n(32),m=n(20),p=n(80),b=n(9),v=n(52),h=n(18);t.a=function EntityHeader(){var t=Object(h.a)(),n=Object(v.c)(),a=Object(l.useSelect)((function(e){return e(d.c).getCurrentEntityTitle()})),_=Object(l.useSelect)((function(e){return e(d.c).getCurrentEntityURL()})),E=Object(s.useRef)(),O=Object(s.useState)(_),k=i()(O,2),y=k[0],S=k[1];Object(s.useEffect)((function(){var t=function(){if(E.current){var t=E.current.clientWidth-40,n=e.getComputedStyle(E.current.lastChild,null).getPropertyValue("font-size"),r=2*t/parseFloat(n);S(Object(p.d)(_,r))}},n=Object(o.throttle)(t,100);return t(),e.addEventListener("resize",n),function(){e.removeEventListener("resize",n)}}),[_,E,S]);var j=Object(l.useDispatch)(f.a).navigateTo,w=Object(l.useSelect)((function(e){return e(d.c).getAdminURL("googlesitekit-dashboard")})),N=Object(s.useCallback)((function(){Object(b.I)("".concat(t,"_navigation"),"return_to_dashboard"),j(w)}),[w,j,t]);return v.a!==n||null===_||null===a?null:r.createElement("div",{className:"googlesitekit-entity-header"},r.createElement("div",{className:"googlesitekit-entity-header__back"},r.createElement(u.Button,{icon:r.createElement(g.a,{width:24,height:24}),"aria-label":Object(c.__)("Back to dashboard","google-site-kit"),onClick:N,text:!0,tertiary:!0},Object(c.__)("Back to dashboard","google-site-kit"))),r.createElement("div",{ref:E,className:"googlesitekit-entity-header__details"},r.createElement("p",null,a),r.createElement(m.a,{secondary:!0,href:_,"aria-label":_,external:!0},y)))}}).call(this,n(28),n(4))},292:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M21 11H6.83l3.58-3.59L9 6l-6 6 6 6 1.41-1.41L6.83 13H21z",fill:"currentColor"});t.a=function SvgKeyboardBackspace(e){return r.createElement("svg",a({viewBox:"0 0 24 24"},e),i,o)}},293:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ViewOnlyMenu}));var r=n(15),a=n.n(r),i=n(209),o=n(11),c=n.n(o),s=n(0),l=n(2),u=n(56),d=n(10),g=n(18),f=n(112),m=n(9),p=n(294),b=n(295),v=n(296),h=n(298),_=n(3),E=n(7);function ViewOnlyMenu(){var t=Object(s.useState)(!1),n=a()(t,2),r=n[0],o=n[1],O=Object(s.useRef)(),k=Object(g.a)();Object(i.a)(O,(function(){return o(!1)})),Object(f.a)([u.c,u.f],O,(function(){return o(!1)}));var y=Object(s.useCallback)((function(){r||Object(m.I)("".concat(k,"_headerbar"),"open_viewonly"),o(!r)}),[r,k]),S=Object(_.useSelect)((function(e){return e(E.a).hasCapability(E.H)}));return e.createElement("div",{ref:O,className:c()("googlesitekit-view-only-menu","googlesitekit-dropdown-menu","googlesitekit-dropdown-menu__icon-menu","mdc-menu-surface--anchor",{"googlesitekit-view-only-menu--user-can-authenticate":S})},e.createElement(d.Button,{className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--phone googlesitekit-button-icon",text:!0,onClick:y,icon:e.createElement("span",{className:"mdc-button__icon","aria-hidden":"true"},e.createElement(p.a,{className:"mdc-button__icon--image"})),"aria-haspopup":"menu","aria-expanded":r,"aria-controls":"view-only-menu","aria-label":Object(l.__)("View only","google-site-kit"),tooltip:!0,tooltipEnterDelayInMS:500},Object(l.__)("View only","google-site-kit")),e.createElement(d.Menu,{menuOpen:r,nonInteractive:!0,onSelected:y,id:"view-only-menu"},e.createElement(b.a,null),e.createElement(v.a,null),e.createElement("li",{className:"mdc-list-divider",role:"separator"}),e.createElement(h.a,null)))}}).call(this,n(4))},294:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M8 1.333c2.756 0 5.214 1.42 6.415 3.667-1.2 2.247-3.659 3.667-6.415 3.667-2.756 0-5.215-1.42-6.415-3.667C2.785 2.753 5.244 1.333 8 1.333zM8 0C4.364 0 1.258 2.073 0 5c1.258 2.927 4.364 5 8 5s6.742-2.073 8-5c-1.258-2.927-4.364-5-8-5zm0 3.333c1.004 0 1.818.747 1.818 1.667S9.004 6.667 8 6.667 6.182 5.92 6.182 5 6.996 3.333 8 3.333zM8 2C6.196 2 4.727 3.347 4.727 5S6.197 8 8 8c1.804 0 3.273-1.347 3.273-3S9.803 2 8 2z",fill:"currentColor"});t.a=function SvgView(e){return r.createElement("svg",a({viewBox:"0 0 16 10",fill:"none"},e),i)}},295:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(0),s=n(38),l=n(2),u=n(3),d=n(10),g=n(32),f=n(13),m=n(7),p=n(9),b=n(20),v=n(18),h=n(37);function Description(){var t=Object(v.a)(),n=Object(u.useSelect)((function(e){return e(m.a).hasCapability(m.H)})),r=Object(u.useSelect)((function(e){return e(f.c).getProxySetupURL()})),i=Object(u.useSelect)((function(e){return e(f.c).getDocumentationLinkURL("dashboard-sharing")})),_=Object(u.useDispatch)(g.a).navigateTo,E=Object(c.useCallback)(function(){var e=o()(a.a.mark((function e(n){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.preventDefault(),e.next=3,Promise.all([Object(h.f)("start_user_setup",!0),Object(p.I)("".concat(t,"_headerbar_viewonly"),"start_user_setup",r?"proxy":"custom-oauth")]);case 3:_(r);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[r,_,t]),O=Object(c.useCallback)((function(){Object(p.I)("".concat(t,"_headerbar_viewonly"),"click_learn_more_link")}),[t]),k=n?Object(s.a)(Object(l.__)("You can see stats from all shared Google services, but you can't make any changes. <strong>Sign in to connect more services and control sharing access.</strong>","google-site-kit"),{strong:e.createElement("strong",null)}):Object(s.a)(Object(l.__)("You can see stats from all shared Google services, but you can't make any changes. <a>Learn more</a>","google-site-kit"),{a:e.createElement(b.a,{href:i,external:!0,onClick:O,"aria-label":Object(l.__)("Learn more about dashboard sharing","google-site-kit")})});return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item googlesitekit-view-only-menu__description"},e.createElement("p",null,k),n&&e.createElement(d.Button,{onClick:E},Object(l._x)("Sign in with Google","Service name","google-site-kit")))}}).call(this,n(4))},296:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SharedServices}));var r=n(2),a=n(3),i=n(7),o=n(297);function SharedServices(){var t=Object(a.useSelect)((function(e){return e(i.a).getViewableModules()}));return void 0===t?null:e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("h4",null,Object(r.__)("Shared services","google-site-kit")),e.createElement("ul",null,t.map((function(t){return e.createElement(o.a,{key:t,module:t})}))))}}).call(this,n(4))},297:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Service}));var r=n(1),a=n.n(r),i=n(38),o=n(2),c=n(3),s=n(19),l=n(7);function Service(t){var n=t.module,r=Object(c.useSelect)((function(e){return e(l.a).hasCapability(l.H)})),a=Object(c.useSelect)((function(e){return e(s.a).getModule(n)||{}})),u=a.name,d=a.owner,g=Object(c.useSelect)((function(e){return e(s.a).getModuleIcon(n)}));return e.createElement("li",{className:"googlesitekit-view-only-menu__service"},e.createElement("span",{className:"googlesitekit-view-only-menu__service--icon"},e.createElement(g,{height:26})),e.createElement("span",{className:"googlesitekit-view-only-menu__service--name"},u),r&&(null==d?void 0:d.login)&&e.createElement("span",{className:"googlesitekit-view-only-menu__service--owner"},Object(i.a)(Object(o.sprintf)(
/* translators: %s: module owner Google Account email address */
Object(o.__)("Shared by <strong>%s</strong>","google-site-kit"),d.login),{strong:e.createElement("strong",{title:d.login})})))}Service.propTypes={module:a.a.string.isRequired}}).call(this,n(4))},298:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Tracking}));var r=n(38),a=n(2),i=n(218),o=n(18);function Tracking(){var t=Object(o.a)();return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("p",null,Object(r.a)(Object(a.__)("Thanks for using Site Kit!<br />Help us make it even better","google-site-kit"),{br:e.createElement("br",null)})),e.createElement(i.a,{trackEventCategory:"".concat(t,"_headerbar_viewonly"),alignCheckboxLeft:!0}))}}).call(this,n(4))},299:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SubtleNotifications}));var r=n(167),a=n(41);function SubtleNotifications(){return e.createElement(r.a,{areaSlug:a.b.BANNERS_BELOW_NAV})}}).call(this,n(4))},3:function(e,t){e.exports=googlesitekit.data},300:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(0),s=n(3),l=n(13),u=n(18),d=n(37),g=n(9),f=function(){var e=Object(u.a)(),t=Object(s.useSelect)((function(e){return e(l.c).isUsingProxy()})),n=Object(s.useSelect)((function(e){return e(l.c).getSetupErrorMessage()}));Object(c.useEffect)((function(){n||void 0===t||function(){var n=o()(a.a.mark((function n(){var r,i;return a.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,Object(d.d)("start_user_setup");case 2:return r=n.sent,n.next=5,Object(d.d)("start_site_setup");case 5:if(i=n.sent,!r.cacheHit){n.next=10;break}return n.next=9,Object(d.c)("start_user_setup");case 9:Object(g.I)("".concat(e,"_setup"),"complete_user_setup",t?"proxy":"custom-oauth");case 10:if(!i.cacheHit){n.next=14;break}return n.next=13,Object(d.c)("start_site_setup");case 13:Object(g.I)("".concat(e,"_setup"),"complete_site_setup",t?"proxy":"custom-oauth");case 14:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}()()}),[e,t,n])}},301:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M9 16h2v-2H9v2zm1-16C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14C7.79 4 6 5.79 6 8h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z",fill:"currentColor"});t.a=function SvgHelp(e){return r.createElement("svg",a({viewBox:"0 0 20 20",fill:"none"},e),i)}},307:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={UPCOMING:"upcoming",ACTIVE:"active",COMPLETED:"completed"}},31:function(e,t,n){"use strict";n.d(t,"l",(function(){return r})),n.d(t,"i",(function(){return a})),n.d(t,"f",(function(){return i})),n.d(t,"e",(function(){return o})),n.d(t,"g",(function(){return c})),n.d(t,"d",(function(){return s})),n.d(t,"h",(function(){return l})),n.d(t,"a",(function(){return u})),n.d(t,"c",(function(){return d})),n.d(t,"b",(function(){return g})),n.d(t,"j",(function(){return f})),n.d(t,"k",(function(){return m}));var r="modules/adsense",a=1,i="READY",o="NEEDS_ATTENTION",c="REQUIRES_REVIEW",s="GETTING_READY",l="background-submit-suspended",u="adsenseAdBlockingFormSettings",d="googlesitekit-ad-blocking-recovery-setup-create-message-cta-clicked",g="ad-blocking-recovery-notification",f={TAG_PLACED:"tag-placed",SETUP_CONFIRMED:"setup-confirmed"},m={PLACE_TAGS:0,CREATE_MESSAGE:1,COMPLETE:2}},311:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"currentColor",fillRule:"evenodd"},r.createElement("path",{d:"M0 6.414L1.415 5l5.292 5.292-1.414 1.415z"}),r.createElement("path",{d:"M14.146.146l1.415 1.414L5.414 11.707 4 10.292z"}));t.a=function SvgConnected(e){return r.createElement("svg",a({viewBox:"0 0 16 12"},e),i)}},312:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"currentColor",fillRule:"evenodd"},r.createElement("path",{d:"M0 0h2v7H0zM0 10h2v2H0z"}));t.a=function SvgExclamation(e){return r.createElement("svg",a({viewBox:"0 0 2 12"},e),i)}},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},329:function(e,t,n){"use strict";(function(e){var r=n(51),a=n.n(r),i=n(53),o=n.n(i),c=n(68),s=n.n(c),l=n(69),u=n.n(l),d=n(49),g=n.n(d),f=n(1),m=n.n(f),p=n(0),b=n(17),v=n(20);function h(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=g()(e);if(t){var a=g()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return u()(this,n)}}var _=function(t){s()(LayoutHeader,t);var n=h(LayoutHeader);function LayoutHeader(){return a()(this,LayoutHeader),n.apply(this,arguments)}return o()(LayoutHeader,[{key:"render",value:function(){var t=this.props,n=t.title,r=t.badge,a=t.ctaLabel,i=t.ctaLink,o=i?{alignMiddle:!0,smSize:4,lgSize:6}:{alignMiddle:!0,smSize:4,mdSize:8,lgSize:12};return e.createElement("header",{className:"googlesitekit-layout__header"},e.createElement(b.e,null,e.createElement(b.k,null,n&&e.createElement(b.a,o,e.createElement("h3",{className:"googlesitekit-subheading-1 googlesitekit-layout__header-title"},n,r)),i&&e.createElement(b.a,{alignMiddle:!0,mdAlignRight:!0,smSize:4,lgSize:6},e.createElement(v.a,{href:i,external:!0},a)))))}}]),LayoutHeader}(p.Component);_.propTypes={title:m.a.string,badge:m.a.node,ctaLabel:m.a.string,ctaLink:m.a.string},_.defaultProps={title:"",badge:null,ctaLabel:"",ctaLink:""},t.a=_}).call(this,n(4))},330:function(e,t,n){"use strict";(function(e){var r=n(51),a=n.n(r),i=n(53),o=n.n(i),c=n(68),s=n.n(c),l=n(69),u=n.n(l),d=n(49),g=n.n(d),f=n(1),m=n.n(f),p=n(0),b=n(17),v=n(134);function h(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=g()(e);if(t){var a=g()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return u()(this,n)}}var _=function(t){s()(LayoutFooter,t);var n=h(LayoutFooter);function LayoutFooter(){return a()(this,LayoutFooter),n.apply(this,arguments)}return o()(LayoutFooter,[{key:"render",value:function(){var t=this.props,n=t.ctaLabel,r=t.ctaLink,a=t.footerContent;return e.createElement("footer",{className:"googlesitekit-layout__footer"},e.createElement(b.e,null,e.createElement(b.k,null,e.createElement(b.a,{size:12},r&&n&&e.createElement(v.a,{className:"googlesitekit-data-block__source",name:n,href:r,external:!0}),a))))}}]),LayoutFooter}(p.Component);_.propTypes={ctaLabel:m.a.string,ctaLink:m.a.string},t.a=_}).call(this,n(4))},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(22),a=n(18);function i(){var e=Object(a.a)();return r.g.includes(e)}},35:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return d}));n(14);var r=n(2),a="missing_required_scopes",i="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===a}function s(e){var t;return[i,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function l(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||s(e)||c(e)||l(e))}function d(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(r.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(r.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},353:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a}));var r=n(0);function a(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;Object(r.useEffect)((function(){var r,a=!1,i=function(){r=e.setTimeout((function(){a=!0}),n)},o=function(){e.clearTimeout(r),a&&(a=!1,t())};return e.addEventListener("focus",o),e.addEventListener("blur",i),function(){e.removeEventListener("focus",o),e.removeEventListener("blur",i),e.clearTimeout(r)}}),[n,t])}}).call(this,n(28))},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return E})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return _}));var r=n(99),a=e._googlesitekitTrackingData||{},i=a.activeModules,o=void 0===i?[]:i,c=a.isSiteKitScreen,s=a.trackingEnabled,l=a.trackingID,u=a.referenceSiteURL,d=a.userIDHash,g=a.isAuthenticated,f={activeModules:o,trackingEnabled:s,trackingID:l,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:a.userRoles,isAuthenticated:g,pluginVersion:"1.151.0"},m=Object(r.a)(f),p=m.enableTracking,b=m.disableTracking,v=(m.isTrackingEnabled,m.initializeSnippet),h=m.trackEvent,_=m.trackEventOnce;function E(e){e?p():b()}c&&s&&v()}).call(this,n(28))},365:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PageHeader}));var r=n(11),a=n.n(r),i=n(1),o=n.n(i),c=n(17),s=n(311),l=n(312),u=n(76);function PageHeader(t){var n=t.title,r=t.icon,i=t.className,o=t.status,d=t.statusText,g=t.fullWidth,f=t.children,m=g?{size:12}:{smSize:4,mdSize:4,lgSize:6},p=""!==o||Boolean(f);return e.createElement("header",{className:"googlesitekit-page-header"},e.createElement(c.k,null,n&&e.createElement(c.a,m,r,e.createElement("h1",{className:a()("googlesitekit-page-header__title",i)},n)),p&&e.createElement(c.a,{alignBottom:!0,mdAlignRight:!0,smSize:4,mdSize:4,lgSize:6},e.createElement("div",{className:"googlesitekit-page-header__details"},o&&e.createElement("span",{className:a()("googlesitekit-page-header__status","googlesitekit-page-header__status--".concat(o))},d,e.createElement(u.a,null,"connected"===o?e.createElement(s.a,{width:10,height:8}):e.createElement(l.a,{width:2,height:12}))),f))))}PageHeader.propTypes={title:o.a.string,icon:o.a.node,className:o.a.string,status:o.a.string,statusText:o.a.string,fullWidth:o.a.bool},PageHeader.defaultProps={title:"",icon:null,className:"googlesitekit-heading-3",status:"",statusText:"",fullWidth:!1}}).call(this,n(4))},368:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupAccountSiteUI}));var r=n(1),a=n.n(r),i=n(0),o=n(3),c=n(10),s=n(32),l=n(162),u=n(410),d=n(31);function SetupAccountSiteUI(t){var n=t.heading,r=t.description,a=t.primaryButton,g=t.secondaryButton,f=Object(o.useSelect)((function(e){return e(d.l).isDoingSubmitChanges()||e(s.a).isNavigating()}));return e.createElement(i.Fragment,null,e.createElement("h3",{className:"googlesitekit-heading-4 googlesitekit-setup-module__title"},n),e.createElement(l.d,null),e.createElement("p",null,r),e.createElement(u.a,null),e.createElement("div",{className:"googlesitekit-setup-module__action"},e.createElement(c.SpinnerButton,{onClick:a.onClick,href:a.href,disabled:f,isSaving:f},a.label),g&&e.createElement("div",{className:"googlesitekit-setup-module__sub-action"},e.createElement(c.Button,{tertiary:!0,onClick:g.onClick},g.label))))}SetupAccountSiteUI.propTypes={heading:a.a.string.isRequired,description:a.a.string.isRequired,primaryButton:a.a.shape({label:a.a.string,href:a.a.string,onClick:a.a.func}).isRequired,secondaryButton:a.a.shape({label:a.a.string,onClick:a.a.func})}}).call(this,n(4))},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return _})),n.d(t,"c",(function(){return E})),n.d(t,"e",(function(){return O})),n.d(t,"b",(function(){return k}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u,d="googlesitekit_",g="".concat(d).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),f=["sessionStorage","localStorage"],m=[].concat(f),p=function(){var t=o()(a.a.mark((function t(n){var r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,i="__storage_test__",r.setItem(i,i),r.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function b(){return v.apply(this,arguments)}function v(){return(v=o()(a.a.mark((function t(){var n,r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=s(m),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(i=r.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,p(i);case 11:if(!t.sent){t.next=13;break}u=e[i];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=o()(a.a.mark((function e(t){var n,r,i,o,c,s,l;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(g).concat(t)))){e.next=10;break}if(i=JSON.parse(r),o=i.timestamp,c=i.ttl,s=i.value,l=i.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:l});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),_=function(){var t=o()(a.a.mark((function t(n,r){var i,o,s,l,u,d,f,m,p=arguments;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=p.length>2&&void 0!==p[2]?p[2]:{},o=i.ttl,s=void 0===o?c.b:o,l=i.timestamp,u=void 0===l?Math.round(Date.now()/1e3):l,d=i.isError,f=void 0!==d&&d,t.next=3,b();case 3:if(!(m=t.sent)){t.next=14;break}return t.prev=5,m.setItem("".concat(g).concat(n),JSON.stringify({timestamp:u,ttl:s,value:r,isError:f})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),E=function(){var t=o()(a.a.mark((function t(n){var r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,i=n.startsWith(d)?n:"".concat(g).concat(n),r.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),O=function(){var t=o()(a.a.mark((function t(){var n,r,i,o;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],i=0;i<n.length;i++)0===(o=n.key(i)).indexOf(d)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),k=function(){var e=o()(a.a.mark((function e(){var t,n,r,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!e.sent){e.next=25;break}return e.next=6,O();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return i=r.value,e.next=14,E(i);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},372:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M1 3.838L4.106 7 10 1",stroke:"currentColor",strokeWidth:1.5});t.a=function SvgTick(e){return r.createElement("svg",a({viewBox:"0 0 11 9",fill:"none"},e),i)}},383:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var r=n(545);function a(e){if(Object(r.b)(e))return e.match(/pub-\d+$/)[0]}function i(e){if(Object(r.a)(e))return e.match(/pub-\d+$/)[0]}},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r="_googlesitekitDataLayer",a="data-googlesitekit-gtag"},395:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdBlockerWarningMessage}));var r=n(1),a=n.n(r),i=n(2),o=n(38),c=n(20),s=n(217),l=n(396);function AdBlockerWarningMessage(t){var n=t.className,r=void 0===n?"":n,a=t.getHelpLink,u=void 0===a?"":a,d=t.warningMessage,g=void 0===d?null:d;return g?e.createElement(s.a,{className:r},Object(o.a)(Object(i.sprintf)(
/* translators: 1: The warning message. 2: "Get help" text. */
Object(i.__)("%1$s. <Link>%2$s</Link>","google-site-kit"),g,Object(i.__)("Get help","google-site-kit")),{Link:e.createElement(c.a,{href:u,external:!0,hideExternalIndicator:!0,trailingIcon:e.createElement(l.a,{width:15,height:15})})})):null}AdBlockerWarningMessage.propTypes={className:a.a.string,getHelpLink:a.a.string,warningMessage:a.a.string}}).call(this,n(4))},396:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M4.5 1.5H3a2 2 0 00-2 2v7a2 2 0 002 2h7a2 2 0 002-2V9M7 1.5h5v5M5 8.5L11.5 2",stroke:"currentColor",strokeWidth:1.5});t.a=function SvgExternalRounded(e){return r.createElement("svg",a({viewBox:"0 0 13 14",fill:"none"},e),i)}},407:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return d})),n.d(t,"a",(function(){return g})),n.d(t,"d",(function(){return f})),n.d(t,"c",(function(){return AutoAdExclusionSwitches}));var r,a=n(6),i=n.n(a),o=n(0),c=n(2),s=n(3),l=n(10),u=n(31),d="loggedinUsers",g="contentCreators",f=(r={},i()(r,d,Object(c.__)("All logged-in users","google-site-kit")),i()(r,g,Object(c.__)("Users who can write posts","google-site-kit")),r);function AutoAdExclusionSwitches(){var t,n=Object(s.useSelect)((function(e){return e(u.l).getAutoAdsDisabled()})),r=Object(s.useDispatch)(u.l).setAutoAdsDisabled;t=n&&n.includes(d)?Object(c.__)("Ads will not be displayed for all logged-in users","google-site-kit"):n&&n.includes(g)?Object(c.__)("Ads will not be displayed for users that can write posts","google-site-kit"):Object(c.__)("Ads will be displayed for all users","google-site-kit");var a=Object(o.useCallback)((function(e,t){var a=t?n.concat(e):n.filter((function(t){return t!==e}));r(a)}),[n,r]),i=Object(o.useCallback)((function(e){var t=e.target.checked;a(g,t)}),[a]),m=Object(o.useCallback)((function(e){var t=e.target.checked;a(d,t)}),[a]);return Array.isArray(n)?e.createElement("fieldset",{className:"googlesitekit-analytics-auto-ads-disabled"},e.createElement("legend",{className:"googlesitekit-setup-module__text"},Object(c.__)("Exclude from Ads","google-site-kit")),e.createElement("div",{className:"googlesitekit-settings-module__inline-items"},e.createElement("div",{className:"googlesitekit-settings-module__inline-item"},e.createElement(l.Switch,{label:f[d],checked:n.includes(d),onClick:m,hideLabel:!1})),!n.includes(d)&&e.createElement("div",{className:"googlesitekit-settings-module__inline-item"},e.createElement(l.Switch,{label:f[g],checked:n.includes(g),onClick:i,hideLabel:!1}))),e.createElement("p",null,t)):null}}).call(this,n(4))},41:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(22),a="core/notifications",i={ERRORS:"notification-area-errors",BANNERS_ABOVE_NAV:"notification-area-banners-above-nav",BANNERS_BELOW_NAV:"notification-area-banners-below-nav"},o={DEFAULT:"default",SETUP_CTAS:"setup-ctas"},c=[r.s,r.n,r.l,r.o,r.m]},410:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupUseSnippetSwitch}));var r=n(0),a=n(2),i=n(3),o=n(31),c=n(383),s=n(162);function SetupUseSnippetSwitch(){var t,n,l=Object(i.useSelect)((function(e){return e(o.l).getOriginalUseSnippet()})),u=Object(i.useSelect)((function(e){return e(o.l).getExistingTag()})),d=Object(i.useSelect)((function(e){return e(o.l).getClientID()})),g=Object(i.useDispatch)(o.l),f=g.setUseSnippet,m=g.saveSettings,p=Boolean(u);if(Object(r.useEffect)((function(){p&&(f(!1),m())}),[p,m,f]),l&&!u||void 0===u||void 0===l)return null;var b=Object(a.__)("Make sure to remove the existing AdSense code to avoid conflicts with the code placed by Site Kit","google-site-kit");return u===d?(n=Object(a.__)("You’ve already got an AdSense code on your site for this account. We recommend you use Site Kit to place the code to get the most out of AdSense.","google-site-kit"),t="".concat(n," ").concat(b)):u?(n=Object(a.sprintf)(
/* translators: 1: existing account ID, 2: current account ID */
Object(a.__)("Site Kit detected AdSense code for a different account %1$s on your site. In order to configure AdSense for your current account %2$s, we recommend you use Site Kit to place the code instead.","google-site-kit"),Object(c.a)(u),Object(c.a)(d)),t="".concat(n," ").concat(b)):t=b,e.createElement(s.e,{checkedMessage:t,uncheckedMessage:n,saveOnChange:!0})}}).call(this,n(4))},44:function(e,t,n){"use strict";(function(e){var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(11),s=n.n(c),l=n(24);function PreviewBlock(t){var n,r,i=t.className,o=t.width,c=t.height,u=t.shape,d=t.padding,g=t.smallWidth,f=t.smallHeight,m=t.tabletWidth,p=t.tabletHeight,b=t.desktopWidth,v=t.desktopHeight,h=Object(l.e)(),_={width:(n={},a()(n,l.b,g),a()(n,l.c,m),a()(n,l.a,b),a()(n,l.d,b),n),height:(r={},a()(r,l.b,f),a()(r,l.c,p),a()(r,l.a,v),a()(r,l.d,b),r)};return e.createElement("div",{className:s()("googlesitekit-preview-block",i,{"googlesitekit-preview-block--padding":d}),style:{width:_.width[h]||o,height:_.height[h]||c}},e.createElement("div",{className:s()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(4))},47:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return i}));var r={BOXES:"boxes",COMPOSITE:"composite"},a={QUARTER:"quarter",HALF:"half",FULL:"full"},i="core/widgets"},512:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("path",{d:"M107.91 41.72c7.73-13.22 3.14-30.12-10.24-37.75C84.29-3.66 67.18.87 59.45 14.09c-.35.59-.66 1.2-.96 1.81l-26.1 44.66a27.172 27.172 0 00-1.6 2.75L3.67 110.1l48.45 27.16 26.98-46.4c.29-.44.57-.89.84-1.35.27-.46.52-.93.76-1.39l26.11-44.67c.38-.57.76-1.14 1.1-1.73z",fill:"#FBBC04"}),r.createElement("path",{d:"M52.34 137.11c-7.68 13.43-25 18.38-38.31 10.62-13.31-7.76-18.02-24.57-10.34-38s24.86-18.39 38.16-10.64c13.3 7.75 18.18 24.59 10.49 38.02z",fill:"#34A853"}),r.createElement("path",{d:"M158.79 51.86c-13.23-7.62-30.15-3.1-37.79 10.1l-27.66 47.8c-7.64 13.2-3.11 30.08 10.13 37.7 13.23 7.62 30.15 3.1 37.79-10.1l27.66-47.8c7.63-13.2 3.1-30.08-10.13-37.7z",fill:"#4285F4"}));t.a=function SvgAdsense(e){return r.createElement("svg",a({viewBox:"0 0 173 152"},e),i)}},52:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(22),a=n(18),i=r.n,o=r.l;function c(){var e=Object(a.a)();return e===r.n||e===r.o?i:e===r.l||e===r.m?o:null}},537:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupAccount}));var r=n(1),a=n.n(r),i=n(0),o=n(3),c=n(10),s=n(31),l=n(140),u=n(538),d=n(539),g=n(540),f=n(541);function SetupAccount(t){var n=t.account,r=t.finishSetup,a=n._id,m=n.state,p=Object(o.useSelect)((function(e){return e(s.l).getClientID()})),b=Object(o.useSelect)((function(e){return e(s.l).getCurrentSite(a)})),v=Object(o.useSelect)((function(e){return e(s.l).getAFCClient(a)})),h=Object(o.useDispatch)(s.l),_=h.setClientID,E=h.setAccountStatus,O=h.setSiteStatus;return Object(i.useEffect)((function(){(null==v?void 0:v._id)&&p!==v._id?_(v._id):null===v&&p&&_("")}),[v,p,_]),Object(i.useEffect)((function(){null===b&&O(l.o)}),[O,b]),Object(i.useEffect)((function(){void 0!==b&&(p?m===s.e?E(l.g):(null==v?void 0:v.state)===s.g?E(l.c):(null==v?void 0:v.state)===s.d?E(l.b):E(l.k):E(l.i))}),[m,v,p,E,b]),void 0===b?e.createElement(c.ProgressBar,null):p?null===b?e.createElement(g.a,null):m===s.e||(null==v?void 0:v.state)===s.g||(null==v?void 0:v.state)===s.d?e.createElement(f.a,null):e.createElement(u.a,{site:b,finishSetup:r}):e.createElement(d.a,null)}SetupAccount.propTypes={account:a.a.shape({_id:a.a.string,state:a.a.string}),finishSetup:a.a.func}}).call(this,n(4))},538:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupAccountSite}));var r=n(1),a=n.n(r),i=n(0),o=n(2),c=n(3),s=n(31),l=n(140),u=n(671),d=n(672),g=n(673),f=n(674),m=n(54);function SetupAccountSite(t){var n=t.site,r=t.finishSetup,a=n.autoAdsEnabled,p=n.state,b=Object(c.useDispatch)(s.l).setSiteStatus;switch(Object(i.useEffect)((function(){var e;switch(p){case s.e:e=l.n;break;case s.g:e=l.r;break;case s.d:e=l.m;break;case s.f:e=a?l.p:l.q}e&&b(e)}),[a,b,p]),p){case s.e:return e.createElement(u.a,null);case s.g:return e.createElement(g.a,null);case s.d:return e.createElement(d.a,null);case s.f:return e.createElement(f.a,{site:n,finishSetup:r});default:return e.createElement(m.a,{message:Object(o.sprintf)(
/* translators: %s: invalid site state identifier */
Object(o.__)("Invalid site state %s","google-site-kit"),p)})}}SetupAccountSite.propTypes={site:a.a.shape({autoAdsEnabled:a.a.bool,state:a.a.string}).isRequired,finishSetup:a.a.func}}).call(this,n(4))},539:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupAccountNoClient}));var r=n(0),a=n(38),i=n(2),o=n(10),c=n(199),s=n(162),l=n(9),u=n(18);function SetupAccountNoClient(){var t=Object(u.a)(),n=Object(r.useCallback)((function(){Object(l.I)("".concat(t,"_adsense"),"apply_afc")}),[t]);return e.createElement(r.Fragment,null,e.createElement("h3",{className:"googlesitekit-heading-4 googlesitekit-setup-module__title"},Object(i.__)("Looks like you need to upgrade your AdSense account","google-site-kit")),e.createElement(s.d,null),e.createElement("p",null,Object(a.a)(Object(i.__)("To start using AdSense on your website, you need to upgrade your account to add “AdSense for content”. <a>Learn more</a>","google-site-kit"),{a:e.createElement(c.a,{path:"/adsense/answer/6023158",external:!0,"aria-label":Object(i.__)("Learn more about updating your AdSense account","google-site-kit")})})),e.createElement("div",{className:"googlesitekit-setup-module__action"},e.createElement(o.Button,{href:"https://www.google.com/adsense",target:"_blank","aria-label":Object(i.__)("Learn more about updating your AdSense account","google-site-kit"),onClick:n},Object(i.__)("Apply now","google-site-kit"))))}}).call(this,n(4))},54:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,r=t.reconnectURL,a=t.noPrefix;if(!n)return null;var s=n;void 0!==a&&a||(s=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),r&&Object(i.a)(r)&&(s=s+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),r));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(s,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:a.a.string.isRequired,reconnectURL:a.a.string,noPrefix:a.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},540:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return SetupAccountCreateSite}));var a=n(0),i=n(2),o=n(3),c=n(10),s=n(31),l=n(162),u=n(9),d=n(18);function SetupAccountCreateSite(){var t=Object(d.a)(),n=Object(o.useSelect)((function(e){return e(s.l).getServiceAccountManageSiteURL()})),g=Object(a.useCallback)((function(r){r.preventDefault(),Object(u.I)("".concat(t,"_adsense"),"create_site"),e.open(n,"_blank")}),[n,t]);return r.createElement(a.Fragment,null,r.createElement("h3",{className:"googlesitekit-heading-4 googlesitekit-setup-module__title"},Object(i.__)("Add this site to your AdSense account","google-site-kit")),r.createElement(l.d,null),r.createElement("p",null,Object(i.__)("We’ve detected that you haven’t added this site to your AdSense account yet","google-site-kit")),r.createElement("div",{className:"googlesitekit-setup-module__action"},r.createElement(c.Button,{onClick:g,href:n},Object(i.__)("Add site to AdSense","google-site-kit"))))}}).call(this,n(28),n(4))},541:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupAccountPendingTasks}));var r=n(0),a=n(2),i=n(3),o=n(10),c=n(410),s=n(31),l=n(162),u=n(9),d=n(18);function SetupAccountPendingTasks(){var t=Object(d.a)(),n=Object(r.useCallback)((function(){Object(u.I)("".concat(t,"_adsense"),"review_tasks")}),[t]),g=Object(i.useSelect)((function(e){return e(s.l).getServiceAccountURL()}));return e.createElement(r.Fragment,null,e.createElement("h3",{className:"googlesitekit-heading-4 googlesitekit-setup-module__title"},Object(a.__)("Your account isn’t ready to show ads yet","google-site-kit")),e.createElement(l.d,null),e.createElement("p",null,Object(a.__)("You need to fix some things before we can connect Site Kit to your AdSense account","google-site-kit")),e.createElement(c.a,null),e.createElement("div",{className:"googlesitekit-setup-module__action"},e.createElement(o.Button,{onClick:n,href:g},Object(a.__)("Review AdSense account","google-site-kit"))))}}).call(this,n(4))},542:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return SetupCreateAccount}));var a=n(5),i=n.n(a),o=n(16),c=n.n(o),s=n(0),l=n(38),u=n(2),d=n(3),g=n(10),f=n(199),m=n(9),p=n(383),b=n(31),v=n(7),h=n(162),_=n(18);function SetupCreateAccount(){var t=Object(_.a)(),n="".concat(t,"_adsense"),a=Object(d.useSelect)((function(e){return e(v.a).getEmail()})),o=Object(d.useSelect)((function(e){return e(b.l).getExistingTag()})),E=Object(d.useSelect)((function(e){return e(b.l).getServiceCreateAccountURL()})),O=Object(s.useCallback)(function(){var t=c()(i.a.mark((function t(r){return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r.preventDefault(),t.next=3,Object(m.I)(n,"create_account");case 3:e.open(E,"_blank");case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),[E,n]);return r.createElement(s.Fragment,null,r.createElement("h3",{className:"googlesitekit-heading-4 googlesitekit-setup-module__title"},Object(u.__)("Create your AdSense account","google-site-kit")),r.createElement(h.d,null),r.createElement("p",null,Object(u.__)("Once you create your account, Site Kit will place AdSense code on every page across your site. This means your site will be automatically optimized to help you earn money from your content.","google-site-kit")),r.createElement(h.f,null),r.createElement("div",{className:"googlesitekit-setup-module__action"},r.createElement(g.Button,{onClick:O,href:E},Object(u.__)("Create AdSense account","google-site-kit"))),r.createElement("p",{className:"googlesitekit-setup-module__footer-text"},o&&Object(u.sprintf)(
/* translators: 1: client ID, 2: user email address, 3: account ID */
Object(u.__)("Site Kit detected AdSense code %1$s on your page. We recommend you remove that code or add %2$s as a user to the AdSense account %3$s.","google-site-kit"),o,a,Object(p.a)(o)),!o&&Object(l.a)(Object(u.sprintf)(
/* translators: %s: user email address */
Object(u.__)("Already use AdSense? Add %s as a user to an existing AdSense account. <a>Learn more</a>","google-site-kit"),a),{a:r.createElement(f.a,{path:"/adsense/answer/2659101",external:!0,"aria-label":Object(u.__)("Learn more about adding a user to an existing AdSense account","google-site-kit")})})))}}).call(this,n(28),n(4))},543:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupSelectAccount}));var r=n(0),a=n(2),i=n(162);function SetupSelectAccount(){return e.createElement(r.Fragment,null,e.createElement("h3",{className:"googlesitekit-heading-4 googlesitekit-setup-module__title"},Object(a.__)("Select your AdSense account","google-site-kit")),e.createElement(i.d,null),e.createElement("p",null,Object(a.__)("Looks like you have multiple AdSense accounts associated with your Google account. Select the account to use with Site Kit below.","google-site-kit")),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(i.a,null)))}}).call(this,n(4))},545:function(e,t,n){"use strict";function r(e){return"string"==typeof e&&/^pub-\d+$/.test(e)}function a(e){return"string"==typeof e&&/^ca-pub-\d+$/.test(e)}n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}))},57:function(e,t,n){"use strict";(function(e){var r,a;n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var i=new Set((null===(r=e)||void 0===r||null===(a=r._googlesitekitBaseData)||void 0===a?void 0:a.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;return t instanceof Set&&t.has(e)}}).call(this,n(28))},58:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return r.createElement("svg",a({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),i,o)}},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(39);function a(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},595:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WebStoriesAdUnitSelect}));var r=n(0),a=n(2),i=n(10),o=n(3),c=n(31);function WebStoriesAdUnitSelect(){var t=Object(o.useSelect)((function(e){return e(c.l).getAccountID()})),n=Object(o.useSelect)((function(e){return e(c.l).getClientID()})),s=Object(o.useSelect)((function(e){return e(c.l).getWebStoriesAdUnit()})),l=Object(o.useSelect)((function(e){return e(c.l).getAdUnits(t,n)})),u=Object(o.useSelect)((function(e){return e(c.l).hasFinishedResolution("getAdUnits",[t,n])})),d=Object(o.useDispatch)(c.l).setWebStoriesAdUnit,g=Object(r.useCallback)((function(e,t){var n=t.dataset.value;s!==n&&d(n)}),[s,d]);return u?e.createElement(i.Select,{className:"googlesitekit-adsense__select-field",label:Object(a.__)("Web Stories Ad Unit","google-site-kit"),value:s,onEnhancedChange:g,enhanced:!0,outlined:!0},e.createElement(i.Option,{value:""},Object(a.__)("Select ad unit","google-site-kit")),(l||[]).map((function(t){var n=t._id,r=t.displayName;return e.createElement(i.Option,{key:n,value:n},r)}))):e.createElement(i.ProgressBar,{small:!0})}}).call(this,n(4))},596:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdSenseConnectCTA}));var r=n(21),a=n.n(r),i=n(5),o=n.n(i),c=n(16),s=n.n(c),l=n(15),u=n.n(l),d=n(240),g=n(1),f=n.n(g),m=n(2),p=n(0),b=n(38),v=n(3),h=n(10),_=n(31),E=n(17),O=n(13),k=n(19),y=n(32),S=n(37),j=n(9),w=n(668),N=n(199),A=n(18);function AdSenseConnectCTA(t){var n=t.onDismissModule,r=Object(v.useDispatch)(y.a).navigateTo,i=Object(v.useDispatch)(k.a).activateModule,c=Object(v.useDispatch)(O.c).setInternalServerError,l=Object(A.a)(),g=Object(p.useRef)(),f=Object(p.useState)(!1),C=u()(f,2),T=C[0],x=C[1],R=Object(d.a)(g,{threshold:.25}),L=!!(null==R?void 0:R.intersectionRatio);Object(p.useEffect)((function(){L&&!T&&(Object(j.I)("".concat(l,"_adsense-cta-widget"),"widget_view"),x(!0))}),[L,l,T]);var D=Object(v.useSelect)((function(e){return e(_.l).getAdminReauthURL()})),I=Object(v.useSelect)((function(e){return e(k.a).isModuleActive("adsense")})),M=Object(v.useSelect)((function(e){return e(k.a).isModuleConnected("adsense")})),P=Object(v.useSelect)((function(e){return!!e(k.a).isFetchingSetModuleActivation("adsense",!0)||!!D&&e(y.a).isNavigatingTo(D)})),B=Object(p.useCallback)(s()(o.a.mark((function e(){var t,n,a;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,i("adsense");case 2:if(t=e.sent,n=t.response,!(a=t.error)){e.next=8;break}return c({id:"setup-module-error",description:a.message}),e.abrupt("return",null);case 8:return e.next=10,Object(j.I)("".concat(l,"_adsense-cta-widget"),"activate_module","adsense");case 10:return e.next=12,Object(S.f)("module_setup","adsense",{ttl:300});case 12:r(n.moduleReauthURL);case 13:case"end":return e.stop()}}),e)}))),[i,r,c,l]),z=Object(p.useCallback)((function(){return r(D)}),[D,r]),H=Object(p.useCallback)((function(){Object(j.I)("".concat(l,"_adsense-cta-widget"),"dismiss_widget"),n()}),[n,l]),F={smSize:4,mdSize:4,lgSize:6};return e.createElement("section",{ref:g,className:"googlesitekit-setup__wrapper googlesitekit-setup__wrapper--adsense-connect"},e.createElement(E.e,null,e.createElement(w.a,{hasBeenInView:T}),e.createElement(E.k,null,e.createElement(E.a,F,e.createElement("div",{className:"googlesitekit-setup-module__action"},!I&&e.createElement(h.SpinnerButton,{onClick:B,isSaving:P},Object(m.__)("Connect now","google-site-kit")),I&&!M&&e.createElement(h.SpinnerButton,{onClick:z,isSaving:P},Object(m.__)("Complete setup","google-site-kit")),e.createElement(h.Button,{tertiary:!0,onClick:H},Object(m.__)("Maybe later","google-site-kit")))),e.createElement(E.a,a()({},F,{className:"googlesitekit-setup-module__footer-text"}),e.createElement("p",null,Object(b.a)(Object(m.__)("AdSense accounts are <a>subject to review and approval</a> by the Google AdSense team","google-site-kit"),{a:e.createElement(N.a,{path:"/adsense/answer/9724",external:!0,hideExternalIndicator:!0})}))))))}AdSenseConnectCTA.propTypes={onDismissModule:f.a.func.isRequired}}).call(this,n(4))},600:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Step}));var r=n(1),a=n.n(r),i=n(307);function Step(t){var n=t.children,r=t.title,a=t.stepStatus;return e.createElement("div",{className:"googlesitekit-stepper__step-info"},e.createElement("h2",{className:"googlesitekit-stepper__step-title"},r),e.createElement("div",{className:"googlesitekit-stepper__step-content-container"},a===i.a.ACTIVE&&e.createElement("div",{className:"googlesitekit-stepper__step-content"},n)))}Step.propTypes={children:a.a.node.isRequired,title:a.a.string.isRequired,stepStatus:a.a.oneOf(Object.values(i.a))}}).call(this,n(4))},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(0),a=Object(r.createContext)(""),i=(a.Consumer,a.Provider);t.b=a},645:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdBlockingRecoveryApp}));var r=n(0),a=n(234),i=n(235),o=n(17),c=n(662);function AdBlockingRecoveryApp(){return e.createElement(r.Fragment,null,e.createElement(a.a,null,e.createElement(i.a,null)),e.createElement("div",{className:"googlesitekit-ad-blocking-recovery googlesitekit-module-page"},e.createElement(o.e,null,e.createElement(o.k,null,e.createElement(o.a,{size:12},e.createElement(c.a,null))))))}}).call(this,n(4))},646:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccountSelect}));var r=n(0),a=n(2),i=n(10),o=n(3),c=n(9),s=n(31),l=n(18);function AccountSelect(){var t=Object(l.a)(),n="".concat(t,"_adsense"),u=Object(o.useSelect)((function(e){return e(s.l).getAccountID()})),d=Object(o.useSelect)((function(e){return e(s.l).getAccounts()})),g=Object(o.useSelect)((function(e){return e(s.l).hasFinishedResolution("getAccounts")})),f=Object(o.useDispatch)(s.l).setAccountID,m=Object(r.useCallback)((function(e,t){var r=t.dataset.value;u!==r&&(f(r),Object(c.I)(n,"change_account"))}),[u,n,f]);return g?e.createElement(i.Select,{className:"googlesitekit-adsense__select-account",label:Object(a.__)("Account","google-site-kit"),value:u,onEnhancedChange:m,enhanced:!0,outlined:!0},(d||[]).map((function(t,n){var r=t._id,a=t.displayName;return e.createElement(i.Option,{key:n,value:r},a)}))):e.createElement(i.ProgressBar,{small:!0})}}).call(this,n(4))},647:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdSenseLinkCTA}));var r=n(2),a=n(3),i=n(95),o=n(13);function AdSenseLinkCTA(t){var n=t.onClick,c=void 0===n?function(){}:n,s=Object(a.useSelect)((function(e){return e(o.c).getGoogleSupportURL({path:"/adsense/answer/6084409"})}));return e.createElement(i.a,{title:Object(r.__)("Link Analytics and AdSense","google-site-kit"),description:Object(r.__)("Get reports for your top earning pages by linking your Analytics and AdSense accounts","google-site-kit"),ctaLink:s,ctaLabel:Object(r.__)("Learn more","google-site-kit"),ctaLinkExternal:!0,onClick:c})}}).call(this,n(4))},648:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotices}));var r=n(31),a=n(141),i=n(1),o=n.n(i);function ErrorNotices(t){var n=t.hasButton,i=void 0!==n&&n;return e.createElement(a.a,{hasButton:i,moduleSlug:"adsense",storeName:r.l})}ErrorNotices.propTypes={hasButton:o.a.bool}}).call(this,n(4))},649:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UserProfile}));var r=n(3),a=n(10),i=n(7);function UserProfile(){var t=Object(r.useSelect)((function(e){return e(i.a).getEmail()})),n=Object(r.useSelect)((function(e){return e(i.a).getPicture()}));return Object(r.useSelect)((function(e){return e(i.a).hasFinishedResolution("getUser")}))?e.createElement("p",{className:"googlesitekit-setup-module__user"},e.createElement("img",{className:"googlesitekit-setup-module__user-image",src:n,alt:""}),e.createElement("span",{className:"googlesitekit-setup-module__user-email"},t)):e.createElement(a.ProgressBar,{small:!0})}}).call(this,n(4))},65:function(e,t,n){"use strict";n.d(t,"c",(function(){return p})),n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return v})),n.d(t,"d",(function(){return _}));var r=n(6),a=n.n(r),i=n(0);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var c=i.createElement("path",{d:"M10 15.27L16.18 19l-1.64-7.03L20 7.24l-7.19-.61L10 0 7.19 6.63 0 7.24l5.46 4.73L3.82 19 10 15.27z"});var s=function SvgInfoIcon(e){return i.createElement("svg",o({viewBox:"0 0 20 20",fill:"currentColor"},e),c)};function l(){return(l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var u=i.createElement("path",{d:"M0 4h2v7H0zm0-4h2v2H0z",fill:"currentColor",fillRule:"evenodd"});var d,g=function SvgSuggestionIcon(e){return i.createElement("svg",l({viewBox:"0 0 2 11"},e),u)},f=n(186),m=n(74),p="warning",b="info",v="suggestion",h=(d={},a()(d,b,s),a()(d,p,f.a),a()(d,v,g),d),_=function(e){return h[e]||m.a}},650:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UseSnippetSwitch}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(1),s=n.n(c),l=n(535),u=n(0),d=n(2),g=n(3),f=n(10),m=n(142),p=n(9),b=n(31),v=n(18),h=n(77);function UseSnippetSwitch(t){var n=t.label,r=void 0===n?Object(d.__)("Let Site Kit place AdSense code on your site","google-site-kit"):n,i=t.checkedMessage,c=t.uncheckedMessage,s=t.saveOnChange,_=Object(v.a)(),E="".concat(_,"_adsense"),O=Object(g.useSelect)((function(e){return e(b.l).getUseSnippet()})),k=Object(g.useSelect)((function(e){return e(b.l).isDoingSubmitChanges()})),y=Object(g.useDispatch)(b.l),S=y.setUseSnippet,j=y.saveSettings,w=Object(u.useCallback)(o()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(S(!O),!s){e.next=4;break}return e.next=4,j();case 4:case"end":return e.stop()}}),e)}))),[O,s,S,j]);return Object(l.a)((function(){Object(p.I)(E,O?"enable_tag":"disable_tag")}),[E,O]),void 0===O?null:e.createElement(u.Fragment,null,e.createElement("div",{className:"googlesitekit-setup-module__switch"},e.createElement(f.Switch,{label:r,onClick:w,checked:O,disabled:k,hideLabel:!1})," ",e.createElement(h.a,{className:"googlesitekit-badge--primary",label:Object(d.__)("Recommended","google-site-kit")})),O&&i&&e.createElement(m.c,{notice:i}),!O&&c&&e.createElement(m.c,{notice:c}))}UseSnippetSwitch.propTypes={label:s.a.string,checkedMessage:s.a.string,uncheckedMessage:s.a.string,saveOnChange:s.a.bool},UseSnippetSwitch.defaultProps={saveOnChange:!1}}).call(this,n(4))},651:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupMain}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(15),s=n.n(c),l=n(535),u=n(1),d=n.n(u),g=n(2),f=n(0),m=n(3),p=n(10),b=n(512),v=n(537),h=n(542),_=n(543),E=n(9),O=n(162),k=n(245),y=n(31),S=n(7),j=n(13),w=n(140),N=n(18),A=n(353),C=n(23);function SetupMain(t){var n=t.finishSetup,r=Object(N.a)(),i="".concat(r,"_adsense"),c=Object(m.useDispatch)(y.l),u=c.resetAccounts,d=c.resetClients,T=c.resetSites,x=c.setAccountID,R=c.setAccountStatus,L=c.submitChanges,D=Object(f.useState)(!1),I=s()(D,2),M=I[0],P=I[1],B=Object(f.useState)(!1),z=s()(B,2),H=z[0],F=z[1],V=Object(m.useSelect)((function(e){return!!e(C.b).getValue(y.h)})),U=Object(m.useSelect)((function(e){return e(S.a).isAdBlockerActive()})),W=Object(m.useSelect)((function(e){return e(y.l).getAccounts()})),G=Object(m.useSelect)((function(e){return e(y.l).getAccountID()})),q=Object(m.useSelect)((function(e){return e(y.l).hasSettingChanged("accountID")})),K=Object(m.useSelect)((function(e){return e(y.l).hasSettingChanged("clientID")})),X=Object(m.useSelect)((function(e){return e(y.l).canSubmitChanges()})),Y=Object(m.useSelect)((function(e){return e(y.l).getClientID()})),$=Object(m.useSelect)((function(e){return e(y.l).getAccountStatus()})),J=Object(m.useSelect)((function(e){return e(y.l).hasSettingChanged("accountStatus")})),Z=Object(m.useSelect)((function(e){return e(y.l).getSiteStatus()})),Q=Object(m.useSelect)((function(e){return e(y.l).hasSettingChanged("siteStatus")})),ee=Object(m.useSelect)((function(e){return e(y.l).hasErrors()})),te=Object(m.useSelect)((function(e){return e(y.l).hasFinishedResolution("getAccounts")})),ne=Object(m.useSelect)((function(e){return e(S.a).getEmail()})),re=Object(m.useSelect)((function(e){return e(j.c).getReferenceSiteURL()})),ae=Object(m.useSelect)((function(e){return e(y.l).getExistingTag()})),ie=null==W?void 0:W.find((function(e){return e._id===G}));Object(l.a)((function(){(q&&void 0!==G||K&&void 0!==Y||J&&void 0!==$||Q&&void 0!==Z)&&P(!0)}),[G,q,Y,K,$,J,Z,Q]),Object(f.useEffect)((function(){var e;Array.isArray(W)&&(1!==W.length||G&&W[0]._id===G?0===W.length&&G&&(e=""):e=W[0]._id,void 0!==e&&(x(e),P(!0)))}),[W,G,x]),Object(f.useEffect)((function(){0===(null==W?void 0:W.length)?R(w.h):(null==W?void 0:W.length)>1&&!G&&R(w.f)}),[R,G,W]),Object(f.useEffect)((function(){M&&!H&&X&&!V&&(P(!1),o()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return F(!0),e.next=3,L();case 3:F(!1);case 4:case"end":return e.stop()}}),e)})))())}),[M,H,X,L,V]);var oe,ce=Object(f.useCallback)((function(){void 0!==$&&w.k!==$&&(u(),d(),T())}),[$,u,d,T]);return Object(A.a)(ce,15e3),Object(f.useEffect)((function(){void 0!==$&&Object(E.I)(i,"receive_account_state",$)}),[i,$]),Object(f.useEffect)((function(){void 0!==Z&&Object(E.I)(i,"receive_site_state",Z)}),[i,Z]),oe=te&&void 0!==G&&void 0!==ne&&void 0!==re&&void 0!==ae?ee?e.createElement(O.d,{hasButton:!0}):(null==W?void 0:W.length)?G?e.createElement(v.a,{account:ie,finishSetup:n}):e.createElement(_.a,null):e.createElement(h.a,null):e.createElement(p.ProgressBar,null),e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--adsense"},e.createElement("div",{className:"googlesitekit-setup-module__step"},e.createElement("div",{className:"googlesitekit-setup-module__logo"},e.createElement(b.a,{width:"40",height:"40"})),e.createElement("h2",{className:"googlesitekit-heading-3 googlesitekit-setup-module__title"},Object(g._x)("AdSense","Service name","google-site-kit"))),e.createElement("div",{className:"googlesitekit-setup-module__step"},e.createElement(k.a,{moduleSlug:"adsense"}),!U&&oe))}SetupMain.propTypes={finishSetup:d.a.func}}).call(this,n(4))},661:function(e,t,n){"use strict";var r=n(645);n.d(t,"a",(function(){return r.a}));n(410);var a=n(651);n.d(t,"b",(function(){return a.a}));n(537),n(542),n(543),n(539),n(540),n(541),n(538)},662:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupMain}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(15),s=n.n(c),l=n(6),u=n.n(l),d=n(157),g=n(0),f=n(2),m=n(3),p=n(10),b=n(20),v=n(365),h=n(663),_=n(600),E=n(192),O=n(32),k=n(13),y=n(23),S=n(18),j=n(17),w=n(9),N=n(31),A=n(664),C=n(666),T=n(667);function SetupMain(){var t=Object(S.a)(),n=Object(m.useSelect)((function(e){return e(k.c).getAdminURL("googlesitekit-settings")})),r="".concat(n,"#/connected-services/adsense"),i=Object(m.useSelect)((function(e){return!!e(y.b).getValue(N.c)})),c=Object(m.useSelect)((function(e){return e(k.c).getAdminURL("googlesitekit-dashboard")})),l=Object(m.useSelect)((function(e){var t,n=(t={},u()(t,N.j.TAG_PLACED,N.k.CREATE_MESSAGE),u()(t,N.j.SETUP_CONFIRMED,N.k.COMPLETE),t),r=e(N.l).getAdBlockingRecoverySetupStatus();if(void 0!==r)return n[r]||N.k.PLACE_TAGS})),x=Object(d.a)(c,{notification:"ad_blocking_recovery_setup_success"}),R=Object(m.useSelect)((function(e){return e(N.l).getAccountID()})),L=Object(m.useSelect)((function(e){return e(N.l).getServiceURL({path:"/".concat(R,"/privacymessaging/ad_blocking")})})),D=Object(m.useDispatch)(N.l),I=D.saveSettings,M=D.setAdBlockingRecoverySetupStatus,P=D.setUseAdBlockingRecoverySnippet,B=D.setUseAdBlockingRecoveryErrorSnippet,z=Object(m.useDispatch)(O.a).navigateTo,H=Object(g.useState)(l),F=s()(H,2),V=F[0],U=F[1],W=Object(g.useCallback)(o()(a.a.mark((function e(){var o,s;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==V){e.next=6;break}return e.next=3,Object(w.I)("".concat(t,"_adsense-abr"),"cancel_setup","on_place_tag_step");case 3:if(!document.referrer.includes(n)){e.next=5;break}return e.abrupt("return",z(r));case 5:return e.abrupt("return",z(c));case 6:if(!i){e.next=10;break}return e.next=9,Object(w.I)("".concat(t,"_adsense-abr"),"cancel_setup","on_final_step");case 9:return e.abrupt("return",z(r));case 10:return M(""),P(!1),B(!1),e.next=15,I();case 15:return o=e.sent,s=o.error,e.next=19,Object(w.I)("".concat(t,"_adsense-abr"),"cancel_setup","on_create_message_step");case 19:s||(document.referrer.includes(n)?z(r):z(c));case 20:case"end":return e.stop()}}),e)}))),[V,r,i,c,z,I,M,B,P,n,t]);return Object(g.useEffect)((function(){void 0===V&&void 0!==l&&U(l)}),[V,l]),e.createElement(E.a,{rounded:!0},e.createElement(j.e,null,e.createElement(j.k,null,e.createElement(j.a,{lgSize:6,mdSize:8,smSize:4},e.createElement(v.a,{className:"googlesitekit-heading-3 googlesitekit-ad-blocking-recovery__heading",title:Object(f.__)("Ad Blocking Recovery","google-site-kit"),fullWidth:!0})))),e.createElement(A.a,null,e.createElement(h.a,{activeStep:V,className:"googlesitekit-ad-blocking-recovery__steps"},e.createElement(_.a,{title:Object(f.__)("Enable ad blocking recovery message (required)","google-site-kit"),className:"googlesitekit-ad-blocking-recovery__step googlesitekit-ad-blocking-recovery__step-place-tags"},e.createElement(T.a,{setActiveStep:U})),e.createElement(_.a,{title:Object(f.__)("Create your site’s ad blocking recovery message (required)","google-site-kit"),className:"googlesitekit-ad-blocking-recovery__step googlesitekit-ad-blocking-recovery__step-create-message"},e.createElement(C.a,null))),N.k.COMPLETE===V&&e.createElement("div",{className:"googlesitekit-ad-blocking-recovery__complete-content"},e.createElement("p",null,Object(f.__)("Create and publish an ad blocking recovery message in AdSense","google-site-kit")),e.createElement("p",null,Object(f.__)("Site visitors will be given the option to allow ads on your site. You can also present them with other options to fund your site (optional)","google-site-kit")))),e.createElement("div",{className:"googlesitekit-ad-blocking-recovery__footer googlesitekit-ad-blocking-recovery__buttons"},e.createElement("div",{className:"googlesitekit-ad-blocking-recovery__footer-cancel"},N.k.COMPLETE===V?e.createElement("div",{className:"googlesitekit-ad-blocking-recovery__complete-actions"},e.createElement(p.SpinnerButton,{href:x},Object(f.__)("My message is ready","google-site-kit")),e.createElement(b.a,{href:L,external:!0,hideExternalIndicator:!0},Object(f.__)("Create message","google-site-kit"))):e.createElement(b.a,{onClick:W},Object(f.__)("Cancel","google-site-kit")))))}}).call(this,n(4))},663:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Stepper}));var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(0),s=n(2),l=n(372),u=n(307);function Stepper(t){var n=t.children,r=t.activeStep,a=t.className,i=c.Children.count(n);function d(e,t){switch(t){case u.a.UPCOMING:return Object(s.sprintf)(
/* translators: 1: The number of the current step. 2: The total number of steps. */
Object(s.__)("Step %1$s of %2$s (upcoming).","google-site-kit"),e,i);case u.a.ACTIVE:return Object(s.sprintf)(
/* translators: 1: The number of the current step. 2: The total number of steps. */
Object(s.__)("Step %1$s of %2$s (active).","google-site-kit"),e,i);case u.a.COMPLETED:return Object(s.sprintf)(
/* translators: 1: The number of the current step. 2: The total number of steps. */
Object(s.__)("Step %1$s of %2$s (completed).","google-site-kit"),e,i)}}return e.createElement("ol",{className:o()("googlesitekit-stepper",a)},c.Children.map(n,(function(t,n){var a=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1;return e<r?u.a.COMPLETED:e===r?u.a.ACTIVE:u.a.UPCOMING}(n,r),s=n+1;return e.createElement("li",{className:o()("googlesitekit-stepper__step","googlesitekit-stepper__step--".concat(a),t.props.className)},e.createElement("div",{className:"googlesitekit-stepper__step-progress"},e.createElement("span",{className:"googlesitekit-stepper__step-number",title:d(s,a)},a===u.a.COMPLETED?e.createElement(l.a,null):s),s<i&&e.createElement("div",{className:"googlesitekit-stepper__step-progress-line"})),Object(c.cloneElement)(t,{stepStatus:a}))})))}Stepper.propTypes={children:a.a.node.isRequired,activeStep:a.a.number,className:a.a.string}}).call(this,n(4))},664:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Content}));var r=n(1),a=n.n(r),i=n(17),o=n(665),c=n(24);function Content(t){var n=t.children,r=Object(c.e)(),a=![c.c,c.b].includes(r);return e.createElement(i.e,{className:"googlesitekit-ad-blocking-recovery__content"},e.createElement(i.k,null,e.createElement(i.a,{mdSize:8,lgSize:8},n),a&&e.createElement(i.a,{className:"googlesitekit-ad-blocking-recovery__hero-graphic",lgSize:4},e.createElement(o.a,null))))}Content.propTypes={children:a.a.node}}).call(this,n(4))},665:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M22.56 28.885a85.319 85.319 0 018.606-7.899C60.044-2.097 83.903-2.06 116.934 2.153 139.265 5 158.642 18.5 182.13 17.185 205.617 15.871 238.088 7.743 264 9.5c29.5 2 37 5.5 63.227 19.385 24.273 12.85 46.312 35.426 57.388 65.284 18.985 51.177-14.865 143.285-60.259 147.628-32.893 3.148-60.039-33.973-93.239-25.376-20.452 5.297-32.583 27.515-48.362 42.177-18.409 17.107-48.199 16.032-69.755 6.902-20.542-8.699-35.63-25.926-42.338-51.32-5.107-19.338-4.595-38.709-16.86-53.857C36.497 138.947 9.432 134 1.281 94.168c-5.16-25.213 5.942-49.13 21.279-65.283z",fill:"#F3F5F7"}),o=r.createElement("g",{filter:"url(#ad-blocking-recovery-setup_svg__filter0_d_149_3117)"},r.createElement("rect",{x:50.837,y:42.913,width:257.906,height:176.087,rx:14.086,fill:"#fff"}),r.createElement("rect",{x:50.139,y:42.214,width:259.302,height:177.484,rx:14.785,stroke:"#CBD0D3",strokeWidth:1.396})),c=r.createElement("rect",{x:65,y:78.486,width:78,height:126.285,rx:5.282,fill:"#EE92DA"}),s=r.createElement("circle",{cx:104.5,cy:139.528,r:11,stroke:"#fff",strokeWidth:3}),l=r.createElement("path",{d:"M112 132.028l-15.5 15.5",stroke:"#fff",strokeWidth:3}),u=r.createElement("path",{d:"M50.837 56.999c0-7.78 6.307-14.086 14.087-14.086h229.733c7.78 0 14.086 6.306 14.086 14.086v7.258H50.837v-7.258z",fill:"#EBEEF0"}),d=r.createElement("rect",{x:61.509,y:50.027,width:7.115,height:7.115,rx:3.557,fill:"#CBD0D3"}),g=r.createElement("rect",{x:72.181,y:50.027,width:7.115,height:7.115,rx:3.557,fill:"#CBD0D3"}),f=r.createElement("rect",{x:164,y:78.486,width:125.396,height:42.688,rx:5.585,fill:"#EBEEF0"}),m=r.createElement("rect",{x:164,y:129,width:92.49,height:14.229,rx:7.115,fill:"#EBEEF0"}),p=r.createElement("rect",{x:164,y:151,width:108.498,height:5.585,rx:2.793,fill:"#EBEEF0"}),b=r.createElement("rect",{x:164,y:165,width:92.49,height:5.585,rx:2.793,fill:"#EBEEF0"}),v=r.createElement("rect",{x:164,y:179,width:125.396,height:5.585,rx:2.793,fill:"#EBEEF0"}),h=r.createElement("g",{filter:"url(#ad-blocking-recovery-setup_svg__filter1_d_149_3117)"},r.createElement("path",{d:"M199 163c0-6.075 4.925-11 11-11h126c6.075 0 11 4.925 11 11v83.038c0 6.076-4.925 11-11 11H210c-6.075 0-11-4.924-11-11V163z",fill:"#fff"}),r.createElement("path",{d:"M199.5 163c0-5.799 4.701-10.5 10.5-10.5h126c5.799 0 10.5 4.701 10.5 10.5v83.038c0 5.799-4.701 10.5-10.5 10.5H210c-5.799 0-10.5-4.701-10.5-10.5V163z",stroke:"#CBD0D3"})),_=r.createElement("rect",{x:224.247,y:192.918,width:96.635,height:5.224,rx:2.612,fill:"#EBEEF0"}),E=r.createElement("rect",{x:246.882,y:222.518,width:53.106,height:22.635,rx:11.318,fill:"#77AD8C"}),O=r.createElement("circle",{cx:273,cy:172.023,r:9.576,fill:"#EBEEF0"}),k=r.createElement("path",{d:"M266.906 233.539l5.003 5.003 9.495-9.495",stroke:"#fff",strokeWidth:3}),y=r.createElement("rect",{x:222.506,y:205.106,width:98.376,height:5.224,rx:2.612,fill:"#EBEEF0"}),S=r.createElement("defs",null,r.createElement("filter",{id:"ad-blocking-recovery-setup_svg__filter0_d_149_3117",x:49.441,y:41.516,width:264.699,height:183.88,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dx:4,dy:5}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0.796078 0 0 0 0 0.815686 0 0 0 0 0.827451 0 0 0 1 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_149_3117"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_149_3117",result:"shape"})),r.createElement("filter",{id:"ad-blocking-recovery-setup_svg__filter1_d_149_3117",x:199,y:152,width:152,height:110.038,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dx:4,dy:5}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0.796078 0 0 0 0 0.815686 0 0 0 0 0.827451 0 0 0 1 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_149_3117"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_149_3117",result:"shape"})));t.a=function SvgAdBlockingRecoverySetup(e){return r.createElement("svg",a({viewBox:"0 0 390 273",fill:"none"},e),i,o,c,s,l,u,d,g,f,m,p,b,v,h,_,E,O,k,y,S)}},666:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CreateMessageStep}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(81),s=n(0),l=n(2),u=n(157),d=n(10),g=n(3),f=n(120),m=n(20),p=n(32),b=n(13),v=n(23),h=n(18),_=n(9),E=n(31);function CreateMessageStep(){var t=Object(h.a)(),n=Object(g.useSelect)((function(e){return e(E.l).getAccountID()})),r=Object(g.useSelect)((function(e){return e(E.l).getServiceURL({path:"/".concat(n,"/privacymessaging/ad_blocking")})})),i=Object(g.useSelect)((function(e){return e(b.c).getAdminURL("googlesitekit-dashboard")})),O=Object(u.a)(i,{notification:"ad_blocking_recovery_setup_success"}),k=Object(g.useSelect)((function(e){return e(E.l).isDoingSaveSettings()||e(p.a).isNavigatingTo(O)})),y=Object(g.useSelect)((function(e){return!!e(v.b).getValue(E.c)})),S=Object(g.useSelect)((function(e){return e(E.l).getErrorForAction("saveSettings")})),j=Object(g.useDispatch)(E.l),w=j.saveSettings,N=j.setAdBlockingRecoverySetupStatus,A=Object(g.useDispatch)(p.a).navigateTo,C=Object(g.useDispatch)(v.b).setValue,T=Object(s.useCallback)(o()(a.a.mark((function e(){var n;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(y){e.next=5;break}return e.next=3,Object(_.I)("".concat(t,"_adsense-abr"),"create_message","primary_cta");case 3:return C(E.c,!0),e.abrupt("return");case 5:return N(E.j.SETUP_CONFIRMED),e.next=8,w();case 8:if(n=e.sent,n.error){e.next=14;break}return e.next=13,Object(_.I)("".concat(t,"_adsense-abr"),"confirm_message_ready");case 13:A(O);case 14:case"end":return e.stop()}}),e)}))),[y,A,w,N,C,O,t]),x=Object(s.useCallback)(o()(a.a.mark((function e(){var n;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return N(E.j.SETUP_CONFIRMED),e.next=3,w();case 3:if(n=e.sent,n.error){e.next=9;break}return e.next=8,Object(_.I)("".concat(t,"_adsense-abr"),"confirm_message_ready_secondary_cta");case 8:A(O);case 9:case"end":return e.stop()}}),e)}))),[N,w,A,O,t]);Object(c.a)((function(){Object(_.I)("".concat(t,"_adsense-abr"),"setup_create_message")})),Object(s.useEffect)((function(){y&&Object(_.I)("".concat(t,"_adsense-abr"),"setup_final_step")}),[y,t]);return e.createElement(s.Fragment,null,e.createElement("p",null,Object(l.__)("Create and publish an ad blocking recovery message in AdSense","google-site-kit")),e.createElement("p",null,Object(l.__)("Site visitors will be given the option to allow ads on your site. You can also present them with other options to fund your site (optional)","google-site-kit")),S&&e.createElement(f.a,{error:S}),e.createElement("div",{className:"googlesitekit-ad-blocking-recovery__create-message-footer"},e.createElement("div",{className:"googlesitekit-ad-blocking-recovery__create-message-footer-actions"},y?e.createElement(s.Fragment,null,e.createElement(d.SpinnerButton,{onClick:T,isSaving:k,disabled:k},Object(l.__)("My message is ready","google-site-kit")),e.createElement(m.a,{onClick:function(){Object(_.I)("".concat(t,"_adsense-abr"),"create_message","secondary_cta")},href:r,external:!0,hideExternalIndicator:!0},Object(l.__)("Create message","google-site-kit"))):e.createElement(s.Fragment,null,e.createElement(d.Button,{href:r,target:"_blank",onClick:T},Object(l.__)("Create message","google-site-kit")),e.createElement(m.a,{onClick:x,disabled:k},Object(l.__)("I published my message","google-site-kit")))),y&&e.createElement("p",{className:"googlesitekit-ad-blocking-recovery__create-message-footer-note"},Object(l.__)("Ad blocking recovery only works if you’ve created and published your message in AdSense","google-site-kit"))))}}).call(this,n(4))},667:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PlaceTagsStep}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(1),s=n.n(c),l=n(81),u=n(0),d=n(38),g=n(2),f=n(10),m=n(3),p=n(120),b=n(20),v=n(13),h=n(18),_=n(9),E=n(31);function PlaceTagsStep(t){var n=t.setActiveStep,r=Object(h.a)(),i=Object(m.useSelect)((function(e){return e(E.l).getUseAdBlockingRecoveryErrorSnippet()})),c=Object(m.useSelect)((function(e){return e(E.l).isDoingSaveSettings()||e(E.l).isFetchingSyncAdBlockingRecoveryTags()})),s=Object(m.useSelect)((function(e){return e(E.l).getErrorForAction("syncAdBlockingRecoveryTags")||e(E.l).getErrorForAction("saveSettings")})),O=Object(m.useSelect)((function(e){return e(v.c).getDocumentationLinkURL("ad-blocking-recovery")})),k=Object(m.useDispatch)(E.l),y=k.saveSettings,S=k.setAdBlockingRecoverySetupStatus,j=k.setUseAdBlockingRecoverySnippet,w=k.setUseAdBlockingRecoveryErrorSnippet,N=k.syncAdBlockingRecoveryTags,A=Object(u.useCallback)((function(e){var t=!!e.target.checked;w(t),Object(_.I)("".concat(r,"_adsense-abr"),t?"check_box":"uncheck_box")}),[w,r]),C=Object(u.useCallback)(o()(a.a.mark((function e(){var t,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,N();case 2:if(t=e.sent,!t.error){e.next=6;break}return e.abrupt("return");case 6:return S(E.j.TAG_PLACED),j(!0),e.next=10,y();case 10:if(i=e.sent,!i.error){e.next=14;break}return e.abrupt("return");case 14:return e.next=16,Object(_.I)("".concat(r,"_adsense-abr"),"setup_enable_tag");case 16:n(E.k.CREATE_MESSAGE);case 17:case"end":return e.stop()}}),e)}))),[y,n,S,j,N,r]);return Object(l.a)((function(){i||w(!0),Object(_.I)("".concat(r,"_adsense-abr"),"setup_place_tag")})),e.createElement(u.Fragment,null,e.createElement("p",null,Object(g.__)("Identify site visitors that have an ad blocker browser extension installed. These site visitors will see the ad blocking recovery message created in AdSense.","google-site-kit")),e.createElement(f.Checkbox,{checked:i,id:"ad-blocking-recovery-error-protection-tag-checkbox",name:"ad-blocking-recovery-error-protection-tag-checkbox",value:"1",onChange:A,alignLeft:!0},Object(g.__)("Enable error protection code (optional)","google-site-kit")),e.createElement("p",{className:"googlesitekit-ad-blocking-recovery__error-protection-tag-info"},Object(d.a)(Object(g.__)("If a site visitor’s ad blocker browser extension blocks the message you create in AdSense, a default, non-customizable ad blocking recovery message will display instead. <a>Learn more</a>","google-site-kit"),{a:e.createElement(b.a,{href:O,external:!0})})),s&&e.createElement(p.a,{error:s}),e.createElement(f.SpinnerButton,{onClick:C,isSaving:c,disabled:c},Object(g.__)("Enable message","google-site-kit")))}PlaceTagsStep.propTypes={setActiveStep:s.a.func}}).call(this,n(4))},668:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ContentAutoUpdate}));var r=n(15),a=n.n(r),i=n(1),o=n.n(i),c=n(0),s=n(669);function ContentAutoUpdate(t){var n=t.hasBeenInView,r=Object(c.useState)({stage:0,mode:"static"}),i=a()(r,2),o=i[0],l=o.stage,u=o.mode,d=i[1];return Object(c.useEffect)((function(){if(n){var e=setTimeout((function(){d({stage:0,mode:"leave"})}),7e3);return function(){clearTimeout(e)}}}),[n]),e.createElement(s.a,{stage:l,mode:u,onAnimationEnd:function(){"enter"===u?d({stage:l,mode:"leave"}):"leave"===u&&d({stage:2===l?0:l+1,mode:"enter"})}})}ContentAutoUpdate.propTypes={hasBeenInView:o.a.bool.isRequired}}).call(this,n(4))},669:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(6),o=n.n(i),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(2),g=n(0),f=n(17),m=n(670),p=n(512),b=Object(g.forwardRef)((function(t,n){var r=t.stage,i=t.mode,c=t.onAnimationEnd,s=[{title:Object(d.__)("Earn money from your site","google-site-kit"),description:Object(d.__)("Focus on writing good content and let AdSense help you make it profitable","google-site-kit")},{title:Object(d.__)("Save time with automated ads","google-site-kit"),description:Object(d.__)("Auto ads automatically place and optimize your ads for you so you don't have to spend time doing it yourself","google-site-kit")},{title:Object(d.__)("You’re in control","google-site-kit"),description:Object(d.__)("Block ads you don't like, customize where ads appear, and choose which types fit your site best","google-site-kit")}],l={smSize:4,mdSize:4,lgSize:6};return e.createElement(g.Fragment,null,e.createElement(f.k,null,e.createElement(f.a,{size:12},e.createElement("p",{className:"googlesitekit-setup__intro-title"},Object(d.__)("Connect Service","google-site-kit")),e.createElement("div",{className:"googlesitekit-setup-module"},e.createElement("div",{className:"googlesitekit-setup-module__logo"},e.createElement(p.a,{width:"33",height:"33"})),e.createElement("h2",{className:"googlesitekit-heading-3 googlesitekit-setup-module__title"},Object(d._x)("AdSense","Service name","google-site-kit"))))),e.createElement(f.k,{ref:n},e.createElement(f.a,a()({},l,{smOrder:2,mdOrder:1,className:"googlesitekit-setup-module--adsense__stage-captions"}),e.createElement("ul",{className:"googlesitekit-setup-module--adsense__stage-caption-container"},s.map((function(t,n){var a=t.title,c=t.description;return e.createElement("li",{key:n,className:u()("googlesitekit-setup-module--adsense__stage-caption",o()({},"googlesitekit-setup-module--adsense__stage-caption--current--".concat(i),r===n))},e.createElement("div",{className:"googlesitekit-setup-module--adsense__stage-caption-indicator"}),e.createElement("div",null,e.createElement("h4",null,a),e.createElement("p",null,c)))}))),e.createElement("ul",{className:"googlesitekit-setup-module--adsense__stage-indicator"},s.map((function(t,n){return e.createElement("li",{key:n,className:u()(o()({},"googlesitekit-setup-module--adsense__stage-indicator--current--".concat(i),r===n))})})))),e.createElement(f.a,a()({},l,{smOrder:1,mdOrder:2,className:"googlesitekit-setup-module--adsense__stage-images"}),e.createElement("div",{className:"googlesitekit-setup-module--adsense__stage-image-container"},s.map((function(t,n){return e.createElement("div",{key:n,className:u()("googlesitekit-setup-module--adsense__stage-image",o()({},"googlesitekit-setup-module--adsense__stage-image--current--".concat(i),r===n)),onAnimationEnd:r===n?c:void 0},e.createElement(m.a,{stage:n}))}))))))}));b.propTypes={stage:s.a.oneOf([0,1,2]),mode:s.a.oneOf(["static","enter","leave"]),onAnimationEnd:s.a.func},t.a=b}).call(this,n(4))},670:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ContentSVG}));var r=n(1),a=n(0),i=n(2),o=n(44),c=n(151),s=Object(a.lazy)((function(){return n.e(33).then(n.bind(null,1217))})),l=Object(a.lazy)((function(){return n.e(34).then(n.bind(null,1218))})),u=Object(a.lazy)((function(){return n.e(35).then(n.bind(null,1219))}));function LazyContentSVG(t){var n=t.stage,r={0:e.createElement(s,null),1:e.createElement(l,null),2:e.createElement(u,null)};return r[n]?e.createElement(c.a,{errorMessage:Object(i.__)("Failed to load graphic","google-site-kit")},r[n]):null}function ContentSVG(t){var n=t.stage;return e.createElement(a.Suspense,{fallback:e.createElement(o.a,{width:"100%",height:"100%"})},e.createElement(LazyContentSVG,{stage:n}))}ContentSVG.propTypes={stage:r.PropTypes.oneOf([0,1,2]).isRequired}}).call(this,n(4))},671:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return NeedsAttention}));var a=n(0),i=n(2),o=n(3),c=n(61),s=n(9),l=n(31),u=n(368);function NeedsAttention(){var t=Object(a.useContext)(c.b),n=Object(o.useSelect)((function(e){return e(l.l).getServiceAccountManageSitesURL()})),d=Object(a.useCallback)((function(r){r.preventDefault(),Object(s.I)("".concat(t,"_adsense"),"review_site_state","needs_attention"),e.open(n,"_blank")}),[n,t]),g=Object(i.__)("Your site isn’t ready to show ads yet","google-site-kit"),f=Object(i.__)("You need to fix some things with this site before we can connect Site Kit to your AdSense account","google-site-kit"),m={label:Object(i.__)("Review site in AdSense","google-site-kit"),href:n,onClick:d};return r.createElement(u.a,{heading:g,description:f,primaryButton:m})}}).call(this,n(28),n(4))},672:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return GettingReady}));var a=n(0),i=n(2),o=n(3),c=n(61),s=n(9),l=n(31),u=n(368);function GettingReady(){var t=Object(a.useContext)(c.b),n=Object(o.useSelect)((function(e){return e(l.l).getServiceAccountManageSitesURL()})),d=Object(a.useCallback)((function(r){r.preventDefault(),Object(s.I)("".concat(t,"_adsense"),"review_site_state","getting_ready"),e.open(n,"_blank")}),[n,t]),g=Object(i.__)("Your site is getting ready","google-site-kit"),f=Object(i.__)("This usually takes a few days, but in some cases can take a few weeks. You’ll get an email from AdSense as soon as they have run some checks on your site.","google-site-kit"),m={label:Object(i.__)("Review site in AdSense","google-site-kit"),href:n,onClick:d};return r.createElement(u.a,{heading:g,description:f,primaryButton:m})}}).call(this,n(28),n(4))},673:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return RequiresReview}));var a=n(0),i=n(2),o=n(3),c=n(61),s=n(9),l=n(31),u=n(368);function RequiresReview(){var t=Object(a.useContext)(c.b),n=Object(o.useSelect)((function(e){return e(l.l).getServiceAccountManageSitesURL()})),d=Object(a.useCallback)((function(r){r.preventDefault(),Object(s.I)("".concat(t,"_adsense"),"review_site_state","requires_review"),e.open(n,"_blank")}),[n,t]),g=Object(i.__)("Your site requires review","google-site-kit"),f=Object(i.__)("To start serving ads, your site needs to be approved first. Go to AdSense to request the review.","google-site-kit"),m={label:Object(i.__)("Request review in AdSense","google-site-kit"),href:n,onClick:d};return r.createElement(u.a,{heading:g,description:f,primaryButton:m})}}).call(this,n(28),n(4))},674:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return Ready}));var a=n(5),i=n.n(a),o=n(16),c=n.n(o),s=n(15),l=n.n(s),u=n(0),d=n(2),g=n(3),f=n(61),m=n(9),p=n(31),b=n(23),v=n(368);function Ready(t){var n=t.site,a=t.finishSetup,o=Object(u.useState)(!1),s=l()(o,2),h=s[0],_=s[1],E=Object(u.useContext)(f.b),O=Object(g.useSelect)((function(e){return e(p.l).getExistingTag()})),k=Object(g.useSelect)((function(e){return e(p.l).getServiceAccountSiteAdsPreviewURL()})),y=Object(g.useSelect)((function(e){return e(p.l).isDoingSubmitChanges()})),S=Object(g.useDispatch)(p.l),j=S.completeSiteSetup,w=S.completeAccountSetup,N=Object(g.useDispatch)(b.b).setValue,A=Object(u.useCallback)((function(t){t.preventDefault(),Object(m.I)("".concat(E,"_adsense"),"enable_auto_ads"),e.open(k,"_blank")}),[k,E]),C=Object(u.useCallback)((function(e){e.preventDefault(),Object(m.I)("".concat(E,"_adsense"),"disable_auto_ads"),_(!0)}),[E]),T=Object(u.useCallback)(c()(i.a.mark((function e(){var t,n;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!y){e.next=2;break}return e.abrupt("return");case 2:return N(p.h,!0),e.next=5,j();case 5:return t=e.sent,e.next=8,w();case 8:n=e.sent,N(p.h,!1),t&&n&&"function"==typeof a&&a();case 11:case"end":return e.stop()}}),e)}))),[y,N,j,w,a]),x={};return n.autoAdsEnabled||h?(x.heading=O?Object(d.__)("Your AdSense account is ready to connect to Site Kit","google-site-kit"):Object(d.__)("Your site is ready to use AdSense","google-site-kit"),x.description=O?Object(d.__)("Connect your AdSense account to see stats on your overall earnings, page CTR, and top earning pages","google-site-kit"):Object(d.__)("Site Kit has placed AdSense code on your site to connect your site to AdSense and help you get the most out of ads","google-site-kit"),x.primaryButton={label:Object(d.__)("Complete setup","google-site-kit"),onClick:T}):(x.heading=Object(d.__)("Enable auto ads for your site","google-site-kit"),x.description=Object(d.__)("To start serving ads via Site Kit, you need to activate auto ads first. Go to AdSense and enable auto ads for your site.","google-site-kit"),x.primaryButton={label:Object(d.__)("Enable auto ads","google-site-kit"),href:k,onClick:A},O&&(x.description=Object(d.__)("Site Kit recommends enabling auto ads. If your existing AdSense setup relies on individual ad units, you can proceed without enabling auto ads.","google-site-kit"),x.secondaryButton={label:Object(d.__)("Proceed without enabling auto ads","google-site-kit"),onClick:C})),r.createElement(v.a,x)}}).call(this,n(28),n(4))},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a})),n.d(t,"e",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return l})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return g})),n.d(t,"J",(function(){return f})),n.d(t,"I",(function(){return m})),n.d(t,"N",(function(){return p})),n.d(t,"f",(function(){return b})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return _})),n.d(t,"l",(function(){return E})),n.d(t,"m",(function(){return O})),n.d(t,"n",(function(){return k})),n.d(t,"o",(function(){return y})),n.d(t,"q",(function(){return S})),n.d(t,"s",(function(){return j})),n.d(t,"r",(function(){return w})),n.d(t,"t",(function(){return N})),n.d(t,"w",(function(){return A})),n.d(t,"u",(function(){return C})),n.d(t,"v",(function(){return T})),n.d(t,"x",(function(){return x})),n.d(t,"y",(function(){return R})),n.d(t,"A",(function(){return L})),n.d(t,"B",(function(){return D})),n.d(t,"C",(function(){return I})),n.d(t,"D",(function(){return M})),n.d(t,"k",(function(){return P})),n.d(t,"F",(function(){return B})),n.d(t,"z",(function(){return z})),n.d(t,"G",(function(){return H})),n.d(t,"E",(function(){return F})),n.d(t,"i",(function(){return V})),n.d(t,"p",(function(){return U})),n.d(t,"Q",(function(){return W})),n.d(t,"P",(function(){return G}));var r="core/user",a="connected_url_mismatch",i="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",l="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",g="googlesitekit_read_shared_module_data",f="googlesitekit_manage_module_sharing_options",m="googlesitekit_delegate_module_sharing_management",p="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",_="kmAnalyticsNewVisitors",E="kmAnalyticsPopularAuthors",O="kmAnalyticsPopularContent",k="kmAnalyticsPopularProducts",y="kmAnalyticsReturningVisitors",S="kmAnalyticsTopCities",j="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",N="kmAnalyticsTopCitiesDrivingPurchases",A="kmAnalyticsTopDeviceDrivingPurchases",C="kmAnalyticsTopConvertingTrafficSource",T="kmAnalyticsTopCountries",x="kmAnalyticsTopPagesDrivingLeads",R="kmAnalyticsTopRecentTrendingPages",L="kmAnalyticsTopTrafficSource",D="kmAnalyticsTopTrafficSourceDrivingAddToCart",I="kmAnalyticsTopTrafficSourceDrivingLeads",M="kmAnalyticsTopTrafficSourceDrivingPurchases",P="kmAnalyticsPagesPerVisit",B="kmAnalyticsVisitLength",z="kmAnalyticsTopReturningVisitorPages",H="kmSearchConsolePopularKeywords",F="kmAnalyticsVisitsPerVisitor",V="kmAnalyticsMostEngagingPages",U="kmAnalyticsTopCategories",W=[b,v,h,_,E,O,k,y,U,S,j,w,N,A,C,T,R,L,D,P,B,z,F,V,U],G=[].concat(W,[H])},70:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},71:function(e,t,n){"use strict";n.r(t),n.d(t,"CONTEXT_MAIN_DASHBOARD_KEY_METRICS",(function(){return r})),n.d(t,"CONTEXT_MAIN_DASHBOARD_TRAFFIC",(function(){return a})),n.d(t,"CONTEXT_MAIN_DASHBOARD_CONTENT",(function(){return i})),n.d(t,"CONTEXT_MAIN_DASHBOARD_SPEED",(function(){return o})),n.d(t,"CONTEXT_MAIN_DASHBOARD_MONETIZATION",(function(){return c})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_TRAFFIC",(function(){return s})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_CONTENT",(function(){return l})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_SPEED",(function(){return u})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_MONETIZATION",(function(){return d}));var r="mainDashboardKeyMetrics",a="mainDashboardTraffic",i="mainDashboardContent",o="mainDashboardSpeed",c="mainDashboardMonetization",s="entityDashboardTraffic",l="entityDashboardContent",u="entityDashboardSpeed",d="entityDashboardMonetization";t.default={CONTEXT_MAIN_DASHBOARD_KEY_METRICS:r,CONTEXT_MAIN_DASHBOARD_TRAFFIC:a,CONTEXT_MAIN_DASHBOARD_CONTENT:i,CONTEXT_MAIN_DASHBOARD_SPEED:o,CONTEXT_MAIN_DASHBOARD_MONETIZATION:c,CONTEXT_ENTITY_DASHBOARD_TRAFFIC:s,CONTEXT_ENTITY_DASHBOARD_CONTENT:l,CONTEXT_ENTITY_DASHBOARD_SPEED:u,CONTEXT_ENTITY_DASHBOARD_MONETIZATION:d}},72:function(e,t,n){"use strict";var r=n(15),a=n.n(r),i=n(265),o=n(1),c=n.n(o),s=n(0),l=n(144);function Portal(e){var t=e.children,n=e.slug,r=Object(s.useState)(document.createElement("div")),o=a()(r,1)[0];return Object(i.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(l.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},73:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),a=n(18),i=n(9);function o(e,t){var n=Object(a.a)(),o=null!=t?t:"".concat(n,"_").concat(e);return{view:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.I.apply(void 0,[o,"view_notification"].concat(t))}),[o]),confirm:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.I.apply(void 0,[o,"confirm_notification"].concat(t))}),[o]),dismiss:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.I.apply(void 0,[o,"dismiss_notification"].concat(t))}),[o]),clickLearnMore:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.I.apply(void 0,[o,"click_learn_more_link"].concat(t))}),[o])}}},74:function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(33),a=n.n(r),i=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:i.a.sanitize(e,t)}};function c(e){var t,n="object"===a()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(1),a=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,a=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:a}},n)}IconWrapper.propTypes={children:a.a.node.isRequired,marginLeft:a.a.number,marginRight:a.a.number}}).call(this,n(4))},77:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(0),g=Object(d.forwardRef)((function(t,n){var r=t.label,i=t.className,c=t.hasLeftSpacing,l=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",a()({ref:n},u,{className:s()("googlesitekit-badge",i,{"googlesitekit-badge--has-left-spacing":l})}),r)}));g.displayName="Badge",g.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=g}).call(this,n(4))},79:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(15),a=n.n(r),i=n(188),o=n(133),c={},s=void 0===e?null:e,l=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,u=void 0===r?0:r,d=e.initialHeight,g=void 0===d?0:d,f=Object(i.a)("undefined"==typeof document?[u,g]:l,t,n),m=a()(f,2),p=m[0],b=m[1],v=function(){return b(l)};return Object(o.a)(s,"resize",v),Object(o.a)(s,"orientationchange",v),p},d=function(e){return u(e)[0]}}).call(this,n(28))},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"s",(function(){return i})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return s})),n.d(t,"g",(function(){return l})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"m",(function(){return m})),n.d(t,"n",(function(){return p})),n.d(t,"h",(function(){return b})),n.d(t,"x",(function(){return v})),n.d(t,"w",(function(){return h})),n.d(t,"y",(function(){return _})),n.d(t,"u",(function(){return E})),n.d(t,"v",(function(){return O})),n.d(t,"f",(function(){return k})),n.d(t,"l",(function(){return y})),n.d(t,"e",(function(){return S})),n.d(t,"t",(function(){return j})),n.d(t,"c",(function(){return w})),n.d(t,"d",(function(){return N})),n.d(t,"b",(function(){return A}));var r="modules/analytics-4",a="account_create",i="property_create",o="webdatastream_create",c="analyticsSetup",s=10,l=1,u="https://www.googleapis.com/auth/tagmanager.readonly",d="enhanced-measurement-form",g="enhanced-measurement-enabled",f="enhanced-measurement-should-dismiss-activation-banner",m="analyticsAccountCreate",p="analyticsCustomDimensionsCreate",b="https://www.googleapis.com/auth/analytics.edit",v="dashboardAllTrafficWidgetDimensionName",h="dashboardAllTrafficWidgetDimensionColor",_="dashboardAllTrafficWidgetDimensionValue",E="dashboardAllTrafficWidgetActiveRowIndex",O="dashboardAllTrafficWidgetLoaded",k={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},y={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},S=[y.CONTACT,y.GENERATE_LEAD,y.SUBMIT_LEAD_FORM],j={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},w="audiencePermissionsSetup",N="audienceTileCustomDimensionCreate",A="audience-selection-panel-expirable-new-badge-"},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(106);function a(e){try{return new URL(e).pathname}catch(e){}return null}function i(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),a=e.replace(n.origin,"");if(a.length<t)return a;var i=a.length-Math.floor(t)+1;return"…"+a.substr(i)}},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return y})),n.d(t,"d",(function(){return S})),n.d(t,"e",(function(){return w})),n.d(t,"c",(function(){return N})),n.d(t,"b",(function(){return A}));var r=n(15),a=n.n(r),i=n(33),o=n.n(i),c=n(6),s=n.n(c),l=n(25),u=n.n(l),d=n(14),g=n(63),f=n.n(g),m=n(2);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=E(e,t),r=n.formatUnit,a=n.formatDecimal;try{return r()}catch(e){return a()}},h=function(e){var t=_(e),n=t.hours,r=t.minutes,a=t.seconds;return a=("0"+a).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(a):"".concat(n,":").concat(r,":").concat(a)},_=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},E=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=_(e),r=n.hours,a=n.minutes,i=n.seconds;return{hours:r,minutes:a,seconds:i,formatUnit:function(){var n=t.unitDisplay,o=b(b({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(i,b(b({},o),{},{unit:"second"})):Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?w(i,b(b({},o),{},{unit:"second"})):"",a?w(a,b(b({},o),{},{unit:"minute"})):"",r?w(r,b(b({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(m.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(m.__)("%ds","google-site-kit"),i);if(0===e)return t;var n=Object(m.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(m.__)("%dm","google-site-kit"),a),o=Object(m.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(m.__)("%dh","google-site-kit"),r);return Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?t:"",a?n:"",r?o:"").trim()}}},O=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},k=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in millions.
Object(m.__)("%sM","google-site-kit"),w(O(e),e%10==0?{}:t)):1e4<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),w(O(e))):1e3<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),w(O(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function y(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=b({},e)),t}function S(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=y(t),r=n.style,a=void 0===r?"metric":r;return"metric"===a?k(e):"duration"===a?v(e,n):"durationISO"===a?h(e):w(e,n)}var j=f()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?A():n,i=u()(t,["locale"]);try{return new Intl.NumberFormat(r,i).format(e)}catch(t){j("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(i)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],l={},d=0,g=Object.entries(i);d<g.length;d++){var f=a()(g[d],2),m=f[0],p=f[1];c[m]&&p===c[m]||(s.includes(m)||(l[m]=p))}try{return new Intl.NumberFormat(r,l).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},N=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?A():n,a=t.style,i=void 0===a?"long":a,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(r,{style:i,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var l=Object(m.__)(", ","google-site-kit");return e.join(l)},A=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a}));var r=n(149),a=n.n(r)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i);function ChangeArrow(t){var n=t.direction,r=t.invertColor,a=t.width,i=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:a,height:i,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:a.a.string,invertColor:a.a.bool,width:a.a.number,height:a.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},86:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={EXTERNAL:"external",INTERNAL:"internal"}},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(12),a=n.n(r),i=function(e,t){var n=t.dateRangeLength;a()(Array.isArray(e),"report must be an array to partition."),a()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return a.b})),n.d(t,"J",(function(){return a.c})),n.d(t,"F",(function(){return i.a})),n.d(t,"K",(function(){return i.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return p})),n.d(t,"j",(function(){return b})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return y})),n.d(t,"c",(function(){return S})),n.d(t,"e",(function(){return j})),n.d(t,"b",(function(){return w})),n.d(t,"a",(function(){return N})),n.d(t,"f",(function(){return A})),n.d(t,"n",(function(){return C})),n.d(t,"w",(function(){return T})),n.d(t,"p",(function(){return x})),n.d(t,"G",(function(){return R})),n.d(t,"s",(function(){return L})),n.d(t,"v",(function(){return D})),n.d(t,"k",(function(){return I})),n.d(t,"o",(function(){return M.b})),n.d(t,"h",(function(){return M.a})),n.d(t,"t",(function(){return P.b})),n.d(t,"q",(function(){return P.a})),n.d(t,"A",(function(){return P.c})),n.d(t,"x",(function(){return B})),n.d(t,"u",(function(){return z})),n.d(t,"E",(function(){return V})),n.d(t,"D",(function(){return U.a})),n.d(t,"g",(function(){return W})),n.d(t,"L",(function(){return G})),n.d(t,"l",(function(){return q}));var r=n(14),a=n(36),i=n(75),o=n(33),c=n.n(o),s=n(96),l=n.n(s),u=function(e){return l()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var a=t[r];a&&"object"===c()(a)&&!Array.isArray(a)&&(a=e(a)),n[r]=a})),n}(e)))};n(97);var d=n(83);function g(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function f(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function m(e){return e.replace(/\n/gi,"<br>")}function p(e){for(var t=e,n=0,r=[g,f,m];n<r.length;n++){t=(0,r[n])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(15),_=n.n(h),E=n(12),O=n.n(E),k=n(2),y="Invalid dateString parameter, it must be a string.",S='Invalid date range, it must be a string with the format "last-x-days".',j=60,w=60*j,N=24*w,A=7*N;function C(){var e=function(e){return Object(k.sprintf)(
/* translators: %s: number of days */
Object(k._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function T(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function x(e){O()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function R(e){O()(T(e),y);var t=e.split("-"),n=_()(t,3),r=n[0],a=n[1],i=n[2];return new Date(r,a-1,i)}function L(e,t){return x(I(e,t*N))}function D(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function I(e,t){O()(T(e)||Object(r.isDate)(e)&&!isNaN(e),y);var n=T(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var M=n(98),P=n(80);function B(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function z(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var H=n(27),F=n.n(H),V=function(e){return Array.isArray(e)?F()(e).sort():e},U=n(89);function W(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var G=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},q=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},90:function(e,t,n){"use strict";(function(e,r){n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return E})),n.d(t,"a",(function(){return TourTooltips}));var a=n(6),i=n.n(a),o=n(81),c=n(30),s=n(1),l=n.n(s),u=n(2),d=n(3),g=n(23),f=n(7),m=n(36),p=n(107),b=n(18);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var h={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},_={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},E={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},O="feature_tooltip_view",k="feature_tooltip_advance",y="feature_tooltip_return",S="feature_tooltip_dismiss",j="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,a=t.tourID,s=t.gaEventCategory,l=t.callback,u="".concat(a,"-step"),w="".concat(a,"-run"),N=Object(d.useDispatch)(g.b).setValue,A=Object(d.useDispatch)(f.a).dismissTour,C=Object(d.useRegistry)(),T=Object(b.a)(),x=Object(d.useSelect)((function(e){return e(g.b).getValue(u)})),R=Object(d.useSelect)((function(e){return e(g.b).getValue(w)&&!1===e(f.a).isTourDismissed(a)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(a)),N(w,!0)}));var L=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return r.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,r=e.lifecycle,a=e.size,i=e.status,o=e.type,l=t+1,u="function"==typeof s?s(T):s;o===c.b.TOOLTIP&&r===c.c.TOOLTIP?Object(m.b)(u,O,l):n===c.a.CLOSE&&r===c.c.COMPLETE?Object(m.b)(u,S,l):n===c.a.NEXT&&i===c.d.FINISHED&&o===c.b.TOUR_END&&a===l&&Object(m.b)(u,j,l),r===c.c.COMPLETE&&i!==c.d.FINISHED&&(n===c.a.PREV&&Object(m.b)(u,y,l),n===c.a.NEXT&&Object(m.b)(u,k,l))}(t);var n=t.action,r=t.index,i=t.status,o=t.step,d=t.type,g=n===c.a.CLOSE,f=!g&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),p=[c.d.FINISHED,c.d.SKIPPED].includes(i),b=g&&d===c.b.STEP_AFTER,v=p||b;if(c.b.STEP_BEFORE===d){var h,_,E=o.target;"string"==typeof o.target&&(E=e.document.querySelector(o.target)),null===(h=E)||void 0===h||null===(_=h.scrollIntoView)||void 0===_||_.call(h,{block:"center"})}f?function(e,t){N(u,e+(t===c.a.PREV?-1:1))}(r,n):v&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(a)),A(a)),l&&l(t,C)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:E,locale:_,run:R,showProgress:!0,stepIndex:x,steps:L,styles:h,tooltipComponent:p.a})}TourTooltips.propTypes={steps:l.a.arrayOf(l.a.object).isRequired,tourID:l.a.string.isRequired,gaEventCategory:l.a.oneOfType([l.a.string,l.a.func]).isRequired,callback:l.a.func}}).call(this,n(28),n(4))},93:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(24),a=n(130);function i(t,n){var r=document.querySelector(t);if(!r)return 0;var a=r.getBoundingClientRect().top,i=o(n);return a+e.scrollY-i}function o(e){var t=c(e),n=document.querySelectorAll(".googlesitekit-navigation, .googlesitekit-entity-header");return t+=Array.from(n).reduce((function(e,t){return e+t.offsetHeight}),0)}function c(t){var n=0,i=document.querySelector(".googlesitekit-header");return n=!!i&&"sticky"===e.getComputedStyle(i).position?function(e){var t=document.querySelector(".googlesitekit-header");if(t){if(e===r.b)return t.offsetHeight;var n=t.getBoundingClientRect().bottom;return n<0?0:n}return 0}(t):function(e){var t=document.querySelector("#wpadminbar");return t&&e!==r.b?t.offsetHeight:0}(t),(n=Object(a.a)(n))<0?0:n}}).call(this,n(28))},95:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(10),s=n(20);function CTA(t){var n=t.title,r=t.headerText,a=t.headerContent,i=t.description,l=t.ctaLink,u=t.ctaLabel,d=t.ctaLinkExternal,g=t.ctaType,f=t.error,m=t.onClick,p=t["aria-label"],b=t.children;return e.createElement("div",{className:o()("googlesitekit-cta",{"googlesitekit-cta--error":f})},(r||a)&&e.createElement("div",{className:"googlesitekit-cta__header"},r&&e.createElement("h2",{className:"googlesitekit-cta__header_text"},r),a),e.createElement("div",{className:"googlesitekit-cta__body"},n&&e.createElement("h3",{className:"googlesitekit-cta__title"},n),i&&"string"==typeof i&&e.createElement("p",{className:"googlesitekit-cta__description"},i),i&&"string"!=typeof i&&e.createElement("div",{className:"googlesitekit-cta__description"},i),u&&"button"===g&&e.createElement(c.Button,{"aria-label":p,href:l,onClick:m},u),u&&"link"===g&&e.createElement(s.a,{href:l,onClick:m,"aria-label":p,external:d,hideExternalIndicator:d,arrow:!0},u),b))}CTA.propTypes={title:a.a.string.isRequired,headerText:a.a.string,description:a.a.oneOfType([a.a.string,a.a.node]),ctaLink:a.a.string,ctaLinkExternal:a.a.bool,ctaLabel:a.a.string,ctaType:a.a.string,"aria-label":a.a.string,error:a.a.bool,onClick:a.a.func,children:a.a.node,headerContent:a.a.node},CTA.defaultProps={title:"",headerText:"",headerContent:"",description:"",ctaLink:"",ctaLabel:"",ctaType:"link",error:!1,onClick:function(){}},t.a=CTA}).call(this,n(4))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o}));var r=n(239),a=n(85),i=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var i=n.invertColor,o=void 0!==i&&i;return Object(r.a)(e.createElement(a.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(6),a=n.n(r),i=n(14),o=n(100),c=n(101);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=l(l({},u),t);a.referenceSiteURL&&(a.referenceSiteURL=a.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(a,n),d=Object(c.a)(a,n,s,r),g={},f=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);g[r]||(g[r]=Object(i.once)(d)),g[r].apply(g,t)};return{enableTracking:function(){a.trackingEnabled=!0},disableTracking:function(){a.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!a.trackingEnabled},trackEvent:d,trackEventOnce:f}}}).call(this,n(28))}},[[1272,1,0]]]);