(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[38],{814:function(e,t,r){"use strict";r.r(t);var l=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var l in r)Object.prototype.hasOwnProperty.call(r,l)&&(e[l]=r[l])}return e}).apply(this,arguments)}var a=l.createElement("path",{d:"M533.99 214.243c-25.708 53.735-94.402 54.313-132.547 46.128-49.764-10.678-82.795-49.737-126.242-52.211-43.446-2.475-67.668 20.128-114.486 25.776-46.819 5.649-104.24.514-134.022-57.511C-3.089 118.401 17.45 56.976 70.34 26.192c52.89-30.784 84.978.156 137.193 0 52.215-.156 73.322-46.783 121.899-50.562 24.113-1.876 48.411 3.084 72.53 19.778 23.855 16.511 27.351 46.73 86.738 74.847 59.387 28.117 70.998 90.253 45.29 143.988z",fill:"#B8E6CA"}),n=l.createElement("g",{filter:"url(#key-metrics-setup-cta-desktop_svg__filter0_d_2575_27038)"},l.createElement("rect",{x:267,y:56.876,width:136.122,height:74.248,rx:9.075,fill:"#fff"})),o=l.createElement("rect",{x:280.2,y:70.075,width:20.625,height:6.6,rx:3.3,fill:"#EBEEF0"}),c=l.createElement("rect",{x:280.2,y:84.101,width:41.249,height:16.5,rx:8.25,fill:"#FFDED3"}),f=l.createElement("rect",{x:280.2,y:112.975,width:109.723,height:6.6,rx:3.3,fill:"#EBEEF0"}),s=l.createElement("path",{d:"M287.622 89.875l5.44 5.44m0 0l.06-4.292m-.06 4.291l-4.291.06",stroke:"#fff",strokeWidth:1.237,strokeLinecap:"round",strokeLinejoin:"round"}),d=l.createElement("rect",{x:298.35,y:89.875,width:16.5,height:4.95,rx:1.65,fill:"#fff"}),h=l.createElement("g",{filter:"url(#key-metrics-setup-cta-desktop_svg__filter1_d_2575_27038)"},l.createElement("rect",{x:117,y:143,width:136.122,height:74.248,rx:9.075,fill:"#fff"})),E=l.createElement("rect",{x:130,y:156,width:54,height:7,rx:3.3,fill:"#EBEEF0"}),m=l.createElement("rect",{x:130.2,y:170.225,width:41.249,height:16.5,rx:8.25,fill:"#FFDED3"}),u=l.createElement("rect",{x:130.2,y:199.099,width:109.723,height:6.6,rx:3.3,fill:"#EBEEF0"}),p=l.createElement("path",{d:"M137.622 175.999l5.44 5.439m0 0l.06-4.291m-.06 4.291l-4.291.061",stroke:"#fff",strokeWidth:1.237,strokeLinecap:"round",strokeLinejoin:"round"}),_=l.createElement("rect",{x:148.35,y:175.999,width:16.5,height:4.95,rx:1.65,fill:"#fff"}),g=l.createElement("g",{filter:"url(#key-metrics-setup-cta-desktop_svg__filter2_d_2575_27038)"},l.createElement("rect",{x:117,y:56.876,width:136.122,height:74.248,rx:9.075,fill:"#fff"})),x=l.createElement("rect",{x:130.199,y:70.075,width:86.623,height:6.6,rx:3.3,fill:"#EBEEF0"}),k=l.createElement("rect",{x:130.199,y:84.101,width:41.249,height:16.5,rx:8.25,fill:"#B8E6CA"}),y=l.createElement("rect",{x:130.199,y:112.975,width:109.723,height:6.6,rx:3.3,fill:"#EBEEF0"}),w=l.createElement("path",{d:"M137.625 95.375l5.439-5.44m0 0l-4.291-.06m4.291.06l.061 4.292",stroke:"#fff",strokeWidth:1.237,strokeLinecap:"round",strokeLinejoin:"round"}),B=l.createElement("rect",{x:148.35,y:89.875,width:16.5,height:4.95,rx:1.65,fill:"#fff"}),v=l.createElement("g",{filter:"url(#key-metrics-setup-cta-desktop_svg__filter3_d_2575_27038)"},l.createElement("rect",{x:267,y:143,width:136.122,height:74.248,rx:9.075,fill:"#fff"})),F=l.createElement("rect",{x:280.199,y:156.199,width:52.799,height:6.6,rx:3.3,fill:"#EBEEF0"}),S=l.createElement("path",{d:"M280.199 178.474a8.25 8.25 0 018.25-8.249h24.75a8.249 8.249 0 018.249 8.249 8.249 8.249 0 01-8.249 8.25h-24.75a8.25 8.25 0 01-8.25-8.25z",fill:"#B8E6CA"}),C=l.createElement("rect",{x:280.199,y:199.099,width:109.723,height:6.6,rx:3.3,fill:"#EBEEF0"}),A=l.createElement("path",{d:"M287.624 181.499l5.439-5.44m0 0l-4.291-.06m4.291.06l.061 4.292",stroke:"#fff",strokeWidth:1.237,strokeLinecap:"round",strokeLinejoin:"round"}),M=l.createElement("rect",{x:298.35,y:175.999,width:16.5,height:4.95,rx:1.65,fill:"#fff"}),O=l.createElement("defs",null,l.createElement("filter",{id:"key-metrics-setup-cta-desktop_svg__filter0_d_2575_27038",x:253.8,y:46.976,width:162.522,height:100.647,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},l.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),l.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),l.createElement("feOffset",{dy:3.3}),l.createElement("feGaussianBlur",{stdDeviation:6.6}),l.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),l.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),l.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2575_27038"}),l.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2575_27038",result:"shape"})),l.createElement("filter",{id:"key-metrics-setup-cta-desktop_svg__filter1_d_2575_27038",x:103.8,y:133.1,width:162.522,height:100.647,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},l.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),l.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),l.createElement("feOffset",{dy:3.3}),l.createElement("feGaussianBlur",{stdDeviation:6.6}),l.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),l.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),l.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2575_27038"}),l.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2575_27038",result:"shape"})),l.createElement("filter",{id:"key-metrics-setup-cta-desktop_svg__filter2_d_2575_27038",x:103.8,y:46.976,width:162.522,height:100.647,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},l.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),l.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),l.createElement("feOffset",{dy:3.3}),l.createElement("feGaussianBlur",{stdDeviation:6.6}),l.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),l.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),l.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2575_27038"}),l.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2575_27038",result:"shape"})),l.createElement("filter",{id:"key-metrics-setup-cta-desktop_svg__filter3_d_2575_27038",x:253.8,y:133.1,width:162.522,height:100.647,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},l.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),l.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),l.createElement("feOffset",{dy:3.3}),l.createElement("feGaussianBlur",{stdDeviation:6.6}),l.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),l.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),l.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2575_27038"}),l.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2575_27038",result:"shape"})));t.default=function SvgKeyMetricsSetupCtaDesktop(e){return l.createElement("svg",i({viewBox:"0 0 468 277",fill:"none"},e),a,n,o,c,f,s,d,h,E,m,u,p,_,g,x,k,y,w,B,v,F,S,C,A,M,O)}}}]);