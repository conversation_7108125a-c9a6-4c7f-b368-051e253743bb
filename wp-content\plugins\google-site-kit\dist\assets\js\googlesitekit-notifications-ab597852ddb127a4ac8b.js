(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[26],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(59),i=n(39),a=n(57);function o(t,n){var o,c=Object(r.a)(n),s=t.activeModules,l=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,f=void 0===d?[]:d,p=t.isAuthenticated,g=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(i.b,"]"))),!o){o=!0;var r=(null==f?void 0:f.length)?f.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:l,plugin_version:g||"",enabled_features:Array.from(a.a).join(","),active_modules:s.join(","),authenticated:p?"1":"0",user_properties:{user_roles:r,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(i.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a)}}}}}).call(this,n(28))},1001:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M434.125 308.505c-30.473 31.409-70.568 36.424-112.295 31.203C250.558 330.793 253.038 265.5 203.5 231c-31.111-21.667-82.978 24.804-137 4.5C6.071 212.788-32 130.003 40 67.5 94.483 20.203 131.83 51.98 173.5 49c41.67-2.98 56.763-13.256 75.538-30.37C270.942-1.34 309.5-3.913 338.252 4.574c29.338 8.66 50.219 29.28 58.202 58.922 6.077 22.573 3.427 65.335 19.583 86.632 22 29 37.45 37.613 44.52 71.5 6.139 29.431-8.182 68.021-26.432 86.876z",fill:"#B8E6CA"}),o=r.createElement("g",{filter:"url(#first-party-mode-setup-banner-desktop_svg__filter0_d_34_998)"},r.createElement("path",{d:"M118 45.764C118 38.162 124.162 32 131.764 32h167.472C306.838 32 313 38.162 313 45.764v216.472c0 7.602-6.162 13.764-13.764 13.764H131.764c-7.602 0-13.764-6.162-13.764-13.764V45.764z",fill:"#fff"})),c=r.createElement("path",{d:"M48.692 87.681c16.818 0 30.372-14.004 30.372-31.181 0-17.176-13.554-31.181-30.372-31.181-16.819 0-30.373 14.005-30.373 31.181 0 17.177 13.555 31.181 30.373 31.181z",fill:"#895A00",stroke:"#fff",strokeWidth:3.362}),s=r.createElement("ellipse",{cx:48.692,cy:56.5,rx:28.692,ry:29.5,fill:"#D9D9D9"}),l=r.createElement("g",{mask:"url(#first-party-mode-setup-banner-desktop_svg__a)",fill:"#FECE72"},r.createElement("path",{d:"M60.73 50.809c0 6.366-5.16 11.527-11.526 11.527s-11.527-5.161-11.527-11.527 5.161-11.527 11.527-11.527S60.73 44.443 60.73 50.81z"}),r.createElement("ellipse",{cx:49.204,cy:84.868,rx:24.494,ry:20.147})),u=r.createElement("path",{d:"M403.087 162.667c23.599 0 42.619-19.652 42.619-43.754 0-24.103-19.02-43.754-42.619-43.754-23.6 0-42.62 19.651-42.62 43.754 0 24.102 19.02 43.754 42.62 43.754z",fill:"#895A00",stroke:"#fff",strokeWidth:4.718}),d=r.createElement("ellipse",{cx:403.261,cy:118.913,rx:40.261,ry:41.395,fill:"#D9D9D9"}),f=r.createElement("g",{mask:"url(#first-party-mode-setup-banner-desktop_svg__b)"},r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M387.922 113.637a6.29 6.29 0 00-1.922 4.526v40.393a6.29 6.29 0 006.29 6.291h22.017a6.29 6.29 0 006.29-6.291v-40.393a6.29 6.29 0 00-1.922-4.526l-13.192-12.731a3.145 3.145 0 00-4.369 0l-13.192 12.731zm15.377 15.04a4.717 4.717 0 100-9.435 4.717 4.717 0 000 9.435z",fill:"#FECE72"})),p=r.createElement("path",{d:"M133.363 63.061a4 4 0 014-4h157a4 4 0 014 4v38a4 4 0 01-4 4h-157a4 4 0 01-4-4v-38z",fill:"#FFE4B1"}),g=r.createElement("rect",{x:133,y:115,width:165,height:8,rx:4,fill:"#FFE4B1"}),m=r.createElement("rect",{x:225,y:209,width:73,height:8,rx:4,fill:"#FFE4B1"}),h=r.createElement("rect",{opacity:.6,x:225,y:225,width:73,height:8,rx:4,fill:"#FFE4B1"}),b=r.createElement("rect",{opacity:.25,x:225,y:241,width:73,height:8,rx:4,fill:"#FFE4B1"}),v=r.createElement("rect",{opacity:.6,x:133,y:130,width:165,height:8,rx:4,fill:"#FFE4B1"}),y=r.createElement("rect",{opacity:.25,x:133,y:145,width:75,height:8,rx:4,fill:"#FFE4B1"}),E=r.createElement("rect",{opacity:.25,x:133,y:158,width:75,height:8,rx:4,fill:"#FFE4B1"}),_=r.createElement("rect",{x:225,y:146,width:73,height:50,rx:4,fill:"#FFE4B1"}),O=r.createElement("circle",{cx:241,cy:167,r:5,fill:"#E1B155"}),k=r.createElement("path",{d:"M252.37 173.768L243 185h39l-14.182-17-10.13 12.143-5.318-6.375z",fill:"#E1B155"}),w=r.createElement("rect",{x:133,y:177,width:73,height:99,rx:4,fill:"#FFE4B1"}),j=r.createElement("path",{d:"M321.909 241.145c15.006 0 27.101-12.496 27.101-27.822 0-15.327-12.095-27.823-27.101-27.823-15.007 0-27.102 12.496-27.102 27.823 0 15.326 12.095 27.822 27.102 27.822z",fill:"#895A00",stroke:"#fff",strokeWidth:3}),S=r.createElement("path",{d:"M347.01 213.323c0 14.274-11.251 25.822-25.101 25.822s-25.102-11.548-25.102-25.822c0-14.275 11.252-25.823 25.102-25.823s25.101 11.548 25.101 25.823z",fill:"#D9D9D9",stroke:"#000"}),x=r.createElement("g",{mask:"url(#first-party-mode-setup-banner-desktop_svg__c)"},r.createElement("path",{d:"M303.665 223.808L317 240h14c4.8 0 6-4 6-6v-14c0-.903-.849-2.36-2.187-3-.839-.401-1.869-.061-3.003-.368-.823-.222-.81-.762-2.31-1.055-1.219-.239-2.097.131-3 0-.949-.138-.909-.91-2.5-1.077-1.807-.189-2.193.55-3 .5v-11c0-1-.6-3-3-3s-3 2-3 3v21.5c-2-1-7.2-4.5-8-4.5-.639 0-1.957.637-3.141 1.403-.479.309-.556.965-.194 1.405z",fill:"#FECE72"})),A=r.createElement("defs",null,r.createElement("filter",{id:"first-party-mode-setup-banner-desktop_svg__filter0_d_34_998",x:102,y:20,width:227,height:276,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_34_998"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_34_998",result:"shape"})));t.a=function SvgFirstPartyModeSetupBannerDesktop(e){return r.createElement("svg",i({viewBox:"0 0 462 258",fill:"none"},e),a,o,c,r.createElement("mask",{id:"first-party-mode-setup-banner-desktop_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:20,y:27,width:58,height:59},s),l,u,r.createElement("mask",{id:"first-party-mode-setup-banner-desktop_svg__b",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:363,y:77,width:81,height:84},d),f,p,g,m,h,b,v,y,E,_,O,k,w,j,r.createElement("mask",{id:"first-party-mode-setup-banner-desktop_svg__c",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:296,y:187,width:52,height:53},S),x,A)}},1002:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M451.956 266.532c-26.296 27.103-60.895 31.431-96.901 26.925-61.502-7.692-59.363-64.035-102.11-93.806-26.846-18.697-71.603 21.404-118.22 3.883-52.145-19.598-84.997-91.035-22.867-144.97C158.873 17.75 191.1 45.17 227.058 42.6c35.958-2.571 48.981-11.44 65.183-26.208 18.901-17.23 52.174-19.45 76.984-12.127 25.316 7.473 43.335 25.267 50.224 50.845 5.244 19.478 2.957 56.378 16.899 74.756 18.984 25.025 32.316 32.457 38.416 61.699 5.298 25.396-7.06 58.696-22.808 74.967z",fill:"#B8E6CA"}),o=r.createElement("g",{filter:"url(#first-party-mode-setup-banner-tablet_svg__filter0_d_712_3365)"},r.createElement("path",{d:"M208.164 28.782a8.356 8.356 0 018.356-8.355h101.67a8.356 8.356 0 018.355 8.355V160.2a8.356 8.356 0 01-8.355 8.356H216.52a8.356 8.356 0 01-8.356-8.356V28.782z",fill:"#fff"})),c=r.createElement("path",{d:"M166.088 54.23c10.21 0 18.439-8.502 18.439-18.93 0-10.427-8.229-18.93-18.439-18.93s-18.439 8.503-18.439 18.93c0 10.428 8.229 18.93 18.439 18.93z",fill:"#895A00",stroke:"#fff",strokeWidth:2.041}),s=r.createElement("ellipse",{cx:166.088,cy:35.3,rx:17.418,ry:17.909,fill:"#D9D9D9"}),l=r.createElement("g",{mask:"url(#first-party-mode-setup-banner-tablet_svg__a)",fill:"#FECE72"},r.createElement("path",{d:"M173.396 31.845a6.998 6.998 0 11-13.995 0 6.998 6.998 0 0113.995 0z"}),r.createElement("ellipse",{cx:166.399,cy:52.522,rx:14.87,ry:12.231})),u=r.createElement("path",{d:"M381.235 99.752c14.326 0 25.873-11.93 25.873-26.562 0-14.632-11.547-26.562-25.873-26.562-14.327 0-25.874 11.93-25.874 26.562 0 14.632 11.547 26.562 25.874 26.562z",fill:"#895A00",stroke:"#fff",strokeWidth:2.864}),d=r.createElement("ellipse",{cx:381.34,cy:73.19,rx:24.442,ry:25.13,fill:"#D9D9D9"}),f=r.createElement("g",{mask:"url(#first-party-mode-setup-banner-tablet_svg__b)"},r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M372.028 69.987a3.822 3.822 0 00-1.167 2.748v24.522a3.82 3.82 0 003.819 3.819h13.366a3.82 3.82 0 003.819-3.819V72.735a3.822 3.822 0 00-1.167-2.748l-8.009-7.729a1.91 1.91 0 00-2.652 0l-8.009 7.729zm9.335 9.131a2.865 2.865 0 10-.001-5.73 2.865 2.865 0 00.001 5.73z",fill:"#FECE72"})),p=r.createElement("path",{d:"M217.49 39.283a2.428 2.428 0 012.429-2.428h95.312a2.428 2.428 0 012.428 2.428v23.07a2.428 2.428 0 01-2.428 2.428h-95.312a2.428 2.428 0 01-2.429-2.428v-23.07z",fill:"#FFE4B1"}),g=r.createElement("rect",{x:217.27,y:70.815,width:100.169,height:4.857,rx:2.428,fill:"#FFE4B1"}),m=r.createElement("rect",{x:273.121,y:127.881,width:44.317,height:4.857,rx:2.428,fill:"#FFE4B1"}),h=r.createElement("rect",{opacity:.6,x:273.121,y:137.594,width:44.317,height:4.857,rx:2.428,fill:"#FFE4B1"}),b=r.createElement("rect",{opacity:.6,x:217.27,y:79.921,width:100.169,height:4.857,rx:2.428,fill:"#FFE4B1"}),v=r.createElement("rect",{opacity:.25,x:217.27,y:89.027,width:45.531,height:4.857,rx:2.428,fill:"#FFE4B1"}),y=r.createElement("rect",{opacity:.25,x:217.27,y:96.919,width:45.531,height:4.857,rx:2.428,fill:"#FFE4B1"}),E=r.createElement("rect",{x:273.121,y:89.634,width:44.317,height:30.354,rx:2.428,fill:"#FFE4B1"}),_=r.createElement("circle",{cx:282.834,cy:102.383,r:3.035,fill:"#E1B155"}),O=r.createElement("path",{d:"M289.737 106.492l-5.688 6.819h23.676l-8.609-10.321-6.15 7.372-3.229-3.87z",fill:"#E1B155"}),k=r.createElement("rect",{x:217.27,y:108.454,width:44.317,height:60.101,rx:2.428,fill:"#FFE4B1"}),w=r.createElement("path",{d:"M331.952 147.396c9.111 0 16.453-7.587 16.453-16.891 0-9.305-7.342-16.891-16.453-16.891-9.11 0-16.452 7.586-16.452 16.891 0 9.304 7.342 16.891 16.452 16.891z",fill:"#895A00",stroke:"#fff",strokeWidth:1.821}),j=r.createElement("path",{d:"M347.191 130.505c0 8.666-6.83 15.676-15.239 15.676-8.408 0-15.238-7.01-15.238-15.676s6.83-15.677 15.238-15.677c8.409 0 15.239 7.011 15.239 15.677z",fill:"#D9D9D9",stroke:"#000",strokeWidth:.607}),S=r.createElement("g",{mask:"url(#first-party-mode-setup-banner-tablet_svg__c)"},r.createElement("path",{d:"M320.878 136.87l8.096 9.83h8.499c2.914 0 3.642-2.428 3.642-3.642v-8.499c0-.548-.515-1.433-1.327-1.822-.509-.243-1.135-.037-1.823-.223-.5-.135-.492-.463-1.403-.641-.74-.145-1.273.08-1.821 0-.576-.084-.552-.552-1.518-.653-1.097-.115-1.331.334-1.821.303v-6.678c0-.607-.364-1.821-1.821-1.821-1.457 0-1.821 1.214-1.821 1.821v13.053c-1.215-.608-4.371-2.732-4.857-2.732-.388 0-1.188.386-1.907.851a.576.576 0 00-.118.853z",fill:"#FECE72"})),x=r.createElement("defs",null,r.createElement("filter",{id:"first-party-mode-setup-banner-tablet_svg__filter0_d_712_3365",x:198.451,y:13.142,width:137.808,height:167.555,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:2.428}),r.createElement("feGaussianBlur",{stdDeviation:4.857}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_712_3365"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_712_3365",result:"shape"})));t.a=function SvgFirstPartyModeSetupBannerTablet(e){return r.createElement("svg",i({viewBox:"0 0 553 140",fill:"none"},e),a,o,c,r.createElement("mask",{id:"first-party-mode-setup-banner-tablet_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:148,y:17,width:36,height:37},s),l,u,r.createElement("mask",{id:"first-party-mode-setup-banner-tablet_svg__b",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:356,y:48,width:50,height:51},d),f,p,g,m,h,b,v,y,E,_,O,k,w,r.createElement("mask",{id:"first-party-mode-setup-banner-tablet_svg__c",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:316,y:114,width:32,height:33},j),S,x)}},1003:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M300.089 187.289c-18.5 19.067-42.841 22.112-68.172 18.943-43.268-5.413-41.763-45.051-71.836-65.996-18.888-13.153-50.375 15.058-83.171 2.732-36.685-13.788-59.798-64.045-16.088-101.99 33.076-28.713 55.749-9.422 81.046-11.23 25.297-1.81 34.46-8.048 45.858-18.439 13.298-12.122 36.705-13.684 54.16-8.531 17.811 5.257 30.488 17.775 35.334 35.77 3.689 13.704 2.08 39.664 11.889 52.593 13.356 17.606 22.735 22.834 27.026 43.406 3.728 17.867-4.967 41.295-16.046 52.742z",fill:"#B8E6CA"}),o=r.createElement("g",{filter:"url(#first-party-mode-setup-banner-mobile_svg__filter0_d_712_3088)"},r.createElement("path",{d:"M108.176 27.782a8.356 8.356 0 018.356-8.355h101.669a8.356 8.356 0 018.356 8.355V159.2a8.356 8.356 0 01-8.356 8.356H116.532a8.356 8.356 0 01-8.356-8.356V27.782z",fill:"#fff"})),c=r.createElement("path",{d:"M66.1 53.23c10.21 0 18.439-8.502 18.439-18.93 0-10.427-8.23-18.93-18.44-18.93S47.662 23.874 47.662 34.3c0 10.428 8.229 18.93 18.439 18.93z",fill:"#895A00",stroke:"#fff",strokeWidth:2.041}),s=r.createElement("ellipse",{cx:66.1,cy:34.3,rx:17.418,ry:17.909,fill:"#D9D9D9"}),l=r.createElement("g",{mask:"url(#first-party-mode-setup-banner-mobile_svg__a)",fill:"#FECE72"},r.createElement("path",{d:"M73.407 30.845a6.998 6.998 0 11-13.995 0 6.998 6.998 0 0113.995 0z"}),r.createElement("ellipse",{cx:66.411,cy:51.522,rx:14.87,ry:12.231})),u=r.createElement("path",{d:"M281.246 98.752c14.327 0 25.874-11.93 25.874-26.562 0-14.632-11.547-26.562-25.874-26.562-14.327 0-25.873 11.93-25.873 26.562 0 14.632 11.546 26.562 25.873 26.562z",fill:"#895A00",stroke:"#fff",strokeWidth:2.864}),d=r.createElement("ellipse",{cx:281.352,cy:72.19,rx:24.442,ry:25.13,fill:"#D9D9D9"}),f=r.createElement("g",{mask:"url(#first-party-mode-setup-banner-mobile_svg__b)"},r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M272.04 68.987a3.818 3.818 0 00-1.167 2.748v24.522a3.82 3.82 0 003.819 3.819h13.366a3.818 3.818 0 003.818-3.819V71.735a3.818 3.818 0 00-1.167-2.748l-8.008-7.729a1.91 1.91 0 00-2.652 0l-8.009 7.729zm9.334 9.13a2.864 2.864 0 100-5.727 2.864 2.864 0 000 5.728z",fill:"#FECE72"})),p=r.createElement("path",{d:"M117.502 38.283a2.428 2.428 0 012.428-2.428h95.313a2.428 2.428 0 012.428 2.428v23.07a2.428 2.428 0 01-2.428 2.428H119.93a2.428 2.428 0 01-2.428-2.428v-23.07z",fill:"#FFE4B1"}),g=r.createElement("rect",{x:117.281,y:69.815,width:100.169,height:4.857,rx:2.428,fill:"#FFE4B1"}),m=r.createElement("rect",{x:173.133,y:126.88,width:44.317,height:4.857,rx:2.428,fill:"#FFE4B1"}),h=r.createElement("rect",{opacity:.6,x:173.133,y:136.594,width:44.317,height:4.857,rx:2.428,fill:"#FFE4B1"}),b=r.createElement("rect",{opacity:.6,x:117.281,y:78.921,width:100.169,height:4.857,rx:2.428,fill:"#FFE4B1"}),v=r.createElement("rect",{opacity:.25,x:117.281,y:88.027,width:45.531,height:4.857,rx:2.428,fill:"#FFE4B1"}),y=r.createElement("rect",{opacity:.25,x:117.281,y:95.919,width:45.531,height:4.857,rx:2.428,fill:"#FFE4B1"}),E=r.createElement("rect",{x:173.133,y:88.634,width:44.317,height:30.354,rx:2.428,fill:"#FFE4B1"}),_=r.createElement("circle",{cx:182.846,cy:101.383,r:3.035,fill:"#E1B155"}),O=r.createElement("path",{d:"M189.749 105.492l-5.688 6.818h23.676l-8.61-10.32-6.149 7.372-3.229-3.87z",fill:"#E1B155"}),k=r.createElement("rect",{x:117.281,y:107.454,width:44.317,height:60.101,rx:2.428,fill:"#FFE4B1"}),w=r.createElement("path",{d:"M231.964 146.395c9.11 0 16.453-7.586 16.453-16.89 0-9.305-7.343-16.891-16.453-16.891s-16.453 7.586-16.453 16.891c0 9.304 7.343 16.89 16.453 16.89z",fill:"#895A00",stroke:"#fff",strokeWidth:1.821}),j=r.createElement("path",{d:"M247.203 129.505c0 8.666-6.831 15.676-15.239 15.676-8.408 0-15.239-7.01-15.239-15.676s6.831-15.677 15.239-15.677c8.408 0 15.239 7.011 15.239 15.677z",fill:"#D9D9D9",stroke:"#000",strokeWidth:.607}),S=r.createElement("g",{mask:"url(#first-party-mode-setup-banner-mobile_svg__c)"},r.createElement("path",{d:"M220.89 135.87l8.096 9.83h8.499c2.914 0 3.642-2.428 3.642-3.642v-8.5c0-.548-.515-1.432-1.328-1.821-.509-.243-1.134-.037-1.823-.223-.499-.135-.491-.463-1.402-.641-.74-.145-1.273.08-1.821 0-.576-.084-.552-.552-1.518-.653-1.097-.116-1.331.334-1.821.303v-6.678c0-.607-.364-1.821-1.821-1.821-1.457 0-1.822 1.214-1.822 1.821v13.052c-1.214-.607-4.371-2.731-4.856-2.731-.388 0-1.188.386-1.907.851a.575.575 0 00-.118.853z",fill:"#FECE72"})),x=r.createElement("defs",null,r.createElement("filter",{id:"first-party-mode-setup-banner-mobile_svg__filter0_d_712_3088",x:98.462,y:12.142,width:137.808,height:167.555,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:2.428}),r.createElement("feGaussianBlur",{stdDeviation:4.857}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_712_3088"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_712_3088",result:"shape"})));t.a=function SvgFirstPartyModeSetupBannerMobile(e){return r.createElement("svg",i({viewBox:"0 0 343 140",fill:"none"},e),a,o,c,r.createElement("mask",{id:"first-party-mode-setup-banner-mobile_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:48,y:16,width:36,height:37},s),l,u,r.createElement("mask",{id:"first-party-mode-setup-banner-mobile_svg__b",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:256,y:47,width:50,height:51},d),f,p,g,m,h,b,v,y,E,_,O,k,w,r.createElement("mask",{id:"first-party-mode-setup-banner-mobile_svg__c",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:216,y:113,width:32,height:33},j),S,x)}},1004:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return FirstPartyModeSetupSuccessSubtleNotification}));var r=n(1),i=n.n(r),a=n(2),o=n(115),c=n(92);function FirstPartyModeSetupSuccessSubtleNotification(t){var n=t.id,r=t.Notification;return e.createElement(r,null,e.createElement(o.a,{title:Object(a.__)("You successfully enabled First-party mode!","google-site-kit"),description:Object(a.__)("You can always disable it in Analytics or Ads settings","google-site-kit"),dismissCTA:e.createElement(c.a,{id:n,primary:!1,dismissLabel:Object(a.__)("Got it","google-site-kit")})}))}FirstPartyModeSetupSuccessSubtleNotification.propTypes={id:i.a.string.isRequired,Notification:i.a.elementType.isRequired}}).call(this,n(4))},1005:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConsentModeSetupCTAWidget}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(15),s=n.n(c),l=n(1),u=n.n(l),d=n(2),f=n(0),p=n(3),g=n(13),m=n(7),h=n(41),b=n(1006),v=n(1007),y=n(175),E=n(9),_=n(580),O=n(24),k=n(1008),w=n(111),j=n(180),S=n(179),x=n(18);function ConsentModeSetupCTAWidget(t){var n=t.id,r=t.Notification,a=Object(f.useState)(null),c=s()(a,2),l=c[0],u=c[1],A=Object(O.e)(),N=Object(x.a)(),T={category:"".concat(N,"_CoMo-ads-setup-notification")},C=Object(p.useSelect)((function(e){return e(g.c).getAdminURL("googlesitekit-settings")})),D=Object(p.useSelect)((function(e){return e(g.c).getDocumentationLinkURL("consent-mode")})),R={tooltipSlug:_.a,content:Object(d.__)("You can always enable consent mode in Settings later","google-site-kit"),dismissLabel:Object(d.__)("Got it","google-site-kit")},M=Object(y.b)(R),L=Object(p.useSelect)((function(e){return e(h.a).isNotificationDismissalFinal(n)})),P=Object(p.useSelect)((function(e){return e(g.c).isUsingProxy()})),F=Object(p.useDispatch)(g.c),I=F.setConsentModeEnabled,B=F.saveConsentModeSettings,U=Object(p.useDispatch)(m.a).triggerSurvey;Object(f.useEffect)((function(){P&&U("view_como_setup_cta",{ttl:E.a})}),[U,P]);var V=function(){var e=o()(i.a.mark((function e(){var t,n,r,a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return u(null),I(!0),t=[B()],P&&t.push(U("enable_como",{ttl:E.a})),e.next=6,Promise.all(t);case 6:n=e.sent,r=s()(n,1),(a=r[0].error)&&(u(a),I(!1));case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return e.createElement(r,{gaTrackingEventArgs:T},e.createElement(k.a,{id:n,title:Object(d.__)("Enable Consent Mode to preserve tracking for your Ads campaigns","google-site-kit"),description:e.createElement(w.a,{className:"googlesitekit-setup-cta-banner__description",text:Object(d.__)("Consent mode interacts with your Consent Management Platform (CMP) or custom implementation for obtaining visitor consent, such as a cookie consent banner.","google-site-kit"),learnMoreLink:e.createElement(j.a,{id:n,label:Object(d.__)("Learn more","google-site-kit"),url:D,ariaLabel:Object(d.__)("Learn more about consent mode","google-site-kit")}),errorText:null==l?void 0:l.message}),actions:e.createElement(S.a,{id:n,className:"googlesitekit-setup-cta-banner__actions-wrapper",ctaLabel:Object(d.__)("Enable consent mode","google-site-kit"),ctaLink:"".concat(C,"#/admin-settings"),onCTAClick:V,dismissLabel:L?Object(d.__)("Don’t show again","google-site-kit"):Object(d.__)("Maybe later","google-site-kit"),onDismiss:M,dismissExpires:2*E.f,gaTrackingEventArgs:T}),SVG:A!==O.b&&A!==O.d?v.a:b.a}))}ConsentModeSetupCTAWidget.propTypes={id:u.a.string,Notification:u.a.elementType}}).call(this,n(4))},1006:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M457.125 293.876a100.692 100.692 0 01-10.24 9.22c-34.362 26.944-62.751 26.901-102.055 21.983-26.57-3.323-42.093-15.97-78.319-13.981-36.226 1.989-45.507 11.196-89.85 8.247-35.101-2.334-77.689-.546-108.896-16.753C38.883 287.593 7.271 257.447 1.287 187.48c-5.984-69.967 10.507-112.4 55.75-144 55.679-38.889 109.66-3.521 151.33-6.5C250.038 34 253.263 21.115 272.038 4c21.904-19.968 63.565-24.71 89.214-14.054 27.786 6.054 50.219 29.28 58.202 58.922 6.077 22.573 3.427 65.335 19.584 86.632 22 29 37.449 37.613 44.519 71.5 6.139 29.431-8.182 68.021-26.432 86.876z",fill:"#b8e6ca"}),o=r.createElement("g",{filter:"url(#consent-mode-setup_svg__filter0_d_162_2908)"},r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M303.594 56.932H105.081c-7.657 0-13.865 6.207-13.865 13.865V216.39c0 7.658 6.208 13.865 13.865 13.865h226.126c7.657 0 13.865-6.207 13.865-13.865V112.838c-23.762-3.629-41.965-24.154-41.965-48.93a50 50 0 01.487-6.976z",fill:"#fff"}),r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M303.594 56.932H105.081c-7.657 0-13.865 6.207-13.865 13.865v7.144h213.908a49.502 49.502 0 01-2.017-14.033 50 50 0 01.487-6.976z",fill:"#ebeef0"}),r.createElement("rect",{x:101.72,y:63.934,width:7.003,height:7.003,rx:3.501,fill:"#cbd0d3"}),r.createElement("rect",{x:112.224,y:63.934,width:7.003,height:7.003,rx:3.501,fill:"#cbd0d3"}),r.createElement("rect",{x:105.318,y:92.19,width:25.184,height:25.184,rx:5.199,fill:"#ebeef0"}),r.createElement("rect",{x:105.318,y:131.477,width:25.184,height:25.184,rx:5.199,fill:"#ebeef0"}),r.createElement("rect",{x:105.318,y:170.764,width:25.184,height:25.184,rx:5.199,fill:"#ebeef0"}),r.createElement("rect",{x:138.562,y:94.204,width:41.302,height:10.074,rx:5.037,fill:"#ebeef0"}),r.createElement("rect",{x:138.562,y:133.492,width:41.302,height:10.074,rx:5.037,fill:"#ebeef0"}),r.createElement("rect",{x:138.562,y:172.779,width:41.302,height:10.074,rx:5.037,fill:"#ebeef0"}),r.createElement("rect",{x:138.562,y:109.315,width:88.648,height:6.044,rx:3.022,fill:"#ebeef0"}),r.createElement("rect",{x:138.562,y:148.602,width:88.648,height:6.044,rx:3.022,fill:"#ebeef0"}),r.createElement("rect",{x:138.562,y:187.889,width:88.648,height:6.044,rx:3.022,fill:"#ebeef0"}),r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M330.969 108.441a49.72 49.72 0 01-18.992-16.251h-30.916a5.498 5.498 0 00-5.498 5.497v101.83a5.499 5.499 0 005.498 5.498h44.41a5.499 5.499 0 005.498-5.498z",fill:"#ebeef0"}),r.createElement("circle",{cx:352.606,cy:63.908,r:42.5,fill:"#fff"}),r.createElement("path",{d:"M353.31 39.13s-12.187 5.606-21.937 7.556c0 30.712 16.575 41.68 21.937 41.68 5.362 0 22.181-13.406 21.206-41.68-9.994-2.194-21.206-7.556-21.206-7.556z",fill:"#8e68cb"}),r.createElement("path",{d:"M353.31 39.13s-12.187 5.606-21.937 7.556c0 30.712 16.575 41.68 21.937 41.68 5.362 0 22.181-13.406 21.206-41.68-9.994-2.194-21.206-7.556-21.206-7.556z",fill:"#a983e6"}),r.createElement("path",{d:"M353.31 39.13s-12.187 5.606-21.937 7.556c0 30.712 16.575 41.68 21.937 41.68z",fill:"#8e68cb"}),r.createElement("circle",{cx:353.553,cy:62.773,r:10.237,fill:"#462083"}),r.createElement("circle",{opacity:.5,cx:353.354,cy:62.869,r:13.068,stroke:"#6e48ab",strokeWidth:2}),r.createElement("mask",{id:"consent-mode-setup_svg__a",maskUnits:"userSpaceOnUse",x:343,y:52,width:21,height:22},r.createElement("circle",{cx:353.553,cy:62.773,r:10.237,fill:"#d9d9d9"})),r.createElement("g",{mask:"url(#consent-mode-setup_svg__a)",fill:"#fff"},r.createElement("circle",{cx:353.554,cy:58.874,r:4.387}),r.createElement("ellipse",{cx:353.553,cy:72.036,rx:9.75,ry:7.8}))),c=r.createElement("g",{filter:"url(#consent-mode-setup_svg__filter1_d_162_2908)"},r.createElement("rect",{x:133.078,y:190.932,width:170.133,height:60.256,rx:6.098,fill:"#8e68cb"}),r.createElement("rect",{x:179.156,y:202.746,width:77.978,height:4.726,rx:2.363,fill:"#fff"}),r.createElement("rect",{x:166.16,y:213.38,width:103.97,height:4.726,rx:2.363,fill:"#fff"}),r.createElement("rect",{x:220.507,y:227.557,width:36.626,height:12.996,rx:6.498,fill:"#fff"}),r.createElement("rect",{x:180.156,y:228.557,width:34.626,height:10.996,rx:5.498,stroke:"#fff",strokeWidth:2})),s=r.createElement("defs",{id:"consent-mode-setup_svg__defs2017"},r.createElement("filter",{id:"consent-mode-setup_svg__filter0_d_162_2908",x:75.216,y:9.408,width:335.891,height:240.846,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix",id:"consent-mode-setup_svg__feFlood1983"}),r.createElement("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha",id:"consent-mode-setup_svg__feColorMatrix1985"}),r.createElement("feOffset",{dy:4,id:"consent-mode-setup_svg__feOffset1987"}),r.createElement("feGaussianBlur",{stdDeviation:8,id:"consent-mode-setup_svg__feGaussianBlur1989"}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out",id:"consent-mode-setup_svg__feComposite1991"}),r.createElement("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0",id:"consent-mode-setup_svg__feColorMatrix1993"}),r.createElement("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_162_2908",id:"consent-mode-setup_svg__feBlend1995"}),r.createElement("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_162_2908",result:"shape",id:"consent-mode-setup_svg__feBlend1997"})),r.createElement("filter",{id:"consent-mode-setup_svg__filter1_d_162_2908",x:117.078,y:182.932,width:202.133,height:92.256,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix",id:"consent-mode-setup_svg__feFlood2000"}),r.createElement("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha",id:"consent-mode-setup_svg__feColorMatrix2002"}),r.createElement("feOffset",{dy:8,id:"consent-mode-setup_svg__feOffset2004"}),r.createElement("feGaussianBlur",{stdDeviation:8,id:"consent-mode-setup_svg__feGaussianBlur2006"}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out",id:"consent-mode-setup_svg__feComposite2008"}),r.createElement("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0",id:"consent-mode-setup_svg__feColorMatrix2010"}),r.createElement("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_162_2908",id:"consent-mode-setup_svg__feBlend2012"}),r.createElement("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_162_2908",result:"shape",id:"consent-mode-setup_svg__feBlend2014"})));t.a=function SvgConsentModeSetup(e){return r.createElement("svg",i({viewBox:"0 -16 485 344",fill:"none"},e),a,o,c,s)}},1007:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M83.98 159.764c-4.962-58.006 8.71-93.185 46.219-119.383 46.16-32.24 93.301 3 123.801 3S297.5 29.5 318.5 16.5s41.907-19.5 65-13c23.093 6.5 41.382 24.926 48 49.5 5.039 18.714 8.966 41.618 27 54.5 28 20 53 30.5 47 57l-421.52-4.736z",fill:"#B8E6CA"}),o=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M371.208 75.834C353.685 73.158 340.26 58.02 340.26 39.75c0-1.746.123-3.464.36-5.145H194.225c-5.647 0-10.225 4.578-10.225 10.225v107.368c0 5.647 4.578 10.225 10.225 10.225h166.758c5.647 0 10.225-4.578 10.225-10.225V75.834z",fill:"#fff"}),c=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M340.62 34.605H194.225c-5.647 0-10.225 4.578-10.225 10.225v5.268h157.748a36.832 36.832 0 01-1.128-15.493z",fill:"#EBEEF0"}),s=r.createElement("rect",{x:191.746,y:39.769,width:5.164,height:5.164,rx:2.582,fill:"#CBD0D3"}),l=r.createElement("rect",{x:199.493,y:39.769,width:5.164,height:5.164,rx:2.582,fill:"#CBD0D3"}),u=r.createElement("rect",{x:194.4,y:60.606,width:18.572,height:18.572,rx:3.834,fill:"#EBEEF0"}),d=r.createElement("rect",{x:194.4,y:89.579,width:18.572,height:18.572,rx:3.834,fill:"#EBEEF0"}),f=r.createElement("rect",{x:194.4,y:118.552,width:18.572,height:18.572,rx:3.834,fill:"#EBEEF0"}),p=r.createElement("rect",{x:218.916,y:62.092,width:30.459,height:7.429,rx:3.714,fill:"#EBEEF0"}),g=r.createElement("rect",{x:218.916,y:91.065,width:30.459,height:7.429,rx:3.714,fill:"#EBEEF0"}),m=r.createElement("rect",{x:218.916,y:120.038,width:30.459,height:7.429,rx:3.714,fill:"#EBEEF0"}),h=r.createElement("rect",{x:218.916,y:73.236,width:65.374,height:4.457,rx:2.229,fill:"#EBEEF0"}),b=r.createElement("rect",{x:218.916,y:102.208,width:65.374,height:4.457,rx:2.229,fill:"#EBEEF0"}),v=r.createElement("rect",{x:218.916,y:131.181,width:65.374,height:4.457,rx:2.229,fill:"#EBEEF0"}),y=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M360.808 72.59a36.656 36.656 0 01-14.006-11.984h-22.799a4.054 4.054 0 00-4.054 4.055v75.095a4.054 4.054 0 004.054 4.054h32.75a4.055 4.055 0 004.055-4.054V72.591z",fill:"#EBEEF0"}),E=r.createElement("circle",{cx:376.764,cy:39.75,r:31.342,fill:"#fff"}),_=r.createElement("path",{d:"M377.284 21.477s-8.988 4.135-16.178 5.573c0 22.648 12.223 30.737 16.178 30.737 3.954 0 16.357-9.886 15.638-30.737-7.37-1.618-15.638-5.573-15.638-5.573z",fill:"#8E68CB"}),O=r.createElement("path",{d:"M377.284 21.477s-8.988 4.135-16.178 5.573c0 22.648 12.223 30.737 16.178 30.737 3.954 0 16.357-9.886 15.638-30.737-7.37-1.618-15.638-5.573-15.638-5.573z",fill:"#A983E6"}),k=r.createElement("path",{d:"M377.284 21.477s-8.988 4.135-16.178 5.573c0 22.648 12.223 30.737 16.178 30.737v-36.31z",fill:"#8E68CB"}),w=r.createElement("circle",{cx:377.463,cy:38.913,r:7.55,fill:"#462083"}),j=r.createElement("circle",{opacity:.5,cx:377.316,cy:38.984,r:9.637,stroke:"#6E48AB",strokeWidth:1.475}),S=r.createElement("circle",{cx:377.463,cy:38.913,r:7.55,fill:"#D9D9D9"}),x=r.createElement("g",{mask:"url(#consent-mode-setup-tablet_svg__a)",fill:"#fff"},r.createElement("circle",{cx:377.464,cy:36.037,r:3.236}),r.createElement("ellipse",{cx:377.463,cy:45.744,rx:7.19,ry:5.752})),A=r.createElement("g",{filter:"url(#consent-mode-setup-tablet_svg__filter1_d_630_6357)"},r.createElement("rect",{x:214.872,y:86.424,width:125.466,height:44.436,rx:4.497,fill:"#8E68CB"}),r.createElement("rect",{x:248.852,y:95.137,width:57.505,height:3.485,rx:1.743,fill:"#fff"}),r.createElement("rect",{x:239.268,y:102.979,width:76.674,height:3.485,rx:1.743,fill:"#fff"}),r.createElement("rect",{x:279.347,y:113.434,width:27.01,height:9.584,rx:4.792,fill:"#fff"}),r.createElement("rect",{x:249.59,y:114.172,width:25.535,height:8.109,rx:4.055,stroke:"#fff",strokeWidth:1.475})),N=r.createElement("defs",null,r.createElement("filter",{id:"consent-mode-setup-tablet_svg__filter0_d_630_6357",x:172.201,y:-.442,width:247.705,height:177.614,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:2.95}),r.createElement("feGaussianBlur",{stdDeviation:5.9}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_630_6357"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_630_6357",result:"shape"})),r.createElement("filter",{id:"consent-mode-setup-tablet_svg__filter1_d_630_6357",x:203.072,y:80.525,width:149.065,height:68.035,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:5.9}),r.createElement("feGaussianBlur",{stdDeviation:5.9}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_630_6357"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_630_6357",result:"shape"})),r.createElement("clipPath",{id:"consent-mode-setup-tablet_svg__clip0_630_6357"},r.createElement("path",{fill:"#fff",d:"M0 0h553v158H0z"})));t.a=function SvgConsentModeSetupTablet(e){return r.createElement("svg",i({viewBox:"0 0 553 146",fill:"none"},e),r.createElement("g",{clipPath:"url(#consent-mode-setup-tablet_svg__clip0_630_6357)"},a,r.createElement("g",{filter:"url(#consent-mode-setup-tablet_svg__filter0_d_630_6357)"},o,c,s,l,u,d,f,p,g,m,h,b,v,y,E,_,O,k,w,j,r.createElement("mask",{id:"consent-mode-setup-tablet_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:369,y:31,width:17,height:16},S),x),A),N)}},1008:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SingleColumnNotificationWithSVG}));var r=n(11),i=n.n(r),a=n(17);function SingleColumnNotificationWithSVG(t){var n=t.id,r=t.title,o=t.description,c=t.actions,s=t.SVG;return e.createElement("div",{className:"googlesitekit-widget-context"},e.createElement(a.e,{className:"googlesitekit-widget-area"},e.createElement(a.k,null,e.createElement(a.a,{size:12},e.createElement("div",{className:i()("googlesitekit-widget","googlesitekit-widget--no-padding","googlesitekit-setup-cta-banner","googlesitekit-setup-cta-banner--single-column","googlesitekit-setup-cta-banner--".concat(n))},e.createElement("div",{className:"googlesitekit-setup-cta-banner__cells"},e.createElement("div",{className:"googlesitekit-setup-cta-banner__primary-cell"},e.createElement("h3",{className:"googlesitekit-setup-cta-banner__title"},r),o,c),e.createElement("div",{className:i()("googlesitekit-setup-cta-banner__svg-wrapper--".concat(n))},e.createElement(s,null))))))))}}).call(this,n(4))},1009:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleRecoveryAlert}));var r=n(15),i=n.n(r),a=n(2),o=n(3),c=n(19),s=n(547),l=n(654),u=n(1010),d=n(1011),f=n(1013);function ModuleRecoveryAlert(t){var n=t.id,r=t.Notification,p=Object(o.useSelect)((function(e){return e(c.a).getRecoverableModules()})),g=Object(o.useSelect)((function(e){var t=e(c.a),n=t.getRecoverableModules,r=t.hasModuleAccess,a=n();if(void 0!==a){var o=Object.keys(a).map((function(e){return[e,r(e)]}));if(!o.some((function(e){return void 0===i()(e,2)[1]})))return o.filter((function(e){return i()(e,2)[1]})).map((function(e){return i()(e,1)[0]}))}})),m=Object.keys(p||{}).length>1,h=!!(null==g?void 0:g.length),b=void 0===p||void 0===g;return e.createElement(r,{className:"googlesitekit-publisher-win"},e.createElement(s.a,{title:Object(a.__)("Dashboard data for some services has been interrupted","google-site-kit"),description:b?e.createElement(l.a,null):e.createElement(u.a,{id:n,recoverableModules:p,userRecoverableModuleSlugs:g,hasUserRecoverableModules:h,hasMultipleRecoverableModules:m}),actions:!b&&(h?e.createElement(d.a,{id:n,recoverableModules:p,userRecoverableModuleSlugs:g,hasMultipleRecoverableModules:m}):e.createElement(f.a,{id:n,recoverableModules:p,userRecoverableModuleSlugs:g,hasMultipleRecoverableModules:m}))}))}}).call(this,n(4))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function f(e,t,n,r){var a=Object(l.a)(t);return function(){var t=s()(i.a.mark((function t(o,c,s,l){var u;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:s,value:l},t.abrupt("return",new Promise((function(e){var t,n,i=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(i),e()};a("event",c,d(d({},u),{},{event_callback:s})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,i){return t.apply(this,arguments)}}()}},1010:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(2),i=n(3),a=n(13),o=n(111),c=n(180);function Description(t){var n,s,l=t.id,u=t.recoverableModules,d=t.userRecoverableModuleSlugs,f=t.hasUserRecoverableModules,p=t.hasMultipleRecoverableModules,g=Object(i.useSelect)((function(e){return e(a.c).getDocumentationLinkURL("dashboard-sharing")}));return e.createElement(o.a,{text:!p&&f?Object(r.sprintf)(
/* translators: %s: module name. */
Object(r.__)("%s data was previously shared with other users on the site by another admin who no longer has access. To restore access, you may recover the module as the new owner.","google-site-kit"),null===(n=u[d[0]])||void 0===n?void 0:n.name):p&&f?Object(r.__)("The data for the following modules was previously shared with other users on the site by another admin who no longer has access. To restore access, you may recover the module as the new owner.","google-site-kit"):p||f||!u?p&&!f?Object(r.__)("The data for the following modules was previously shared with other users on the site by another admin who no longer has access. To restore access, the module must be recovered by another admin who has access.","google-site-kit"):void 0:Object(r.sprintf)(
/* translators: %s: module name. */
Object(r.__)("%s data was previously shared with other users on the site by another admin who no longer has access. To restore access, the module must be recovered by another admin who has access.","google-site-kit"),null===(s=Object.values(u)[0])||void 0===s?void 0:s.name),learnMoreLink:e.createElement(c.a,{id:l,label:Object(r.__)("Learn more","google-site-kit"),url:g})})}}).call(this,n(4))},1011:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RecoverableActions}));var r=n(27),i=n.n(r),a=n(5),o=n.n(a),c=n(16),s=n.n(c),l=n(6),u=n.n(l),d=n(15),f=n.n(d),p=n(14),g=n(206),m=n(0),h=n(2),b=n(3),v=n(10),y=n(19),E=n(41),_=n(1012),O=n(179);function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){u()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function RecoverableActions(t){var n=t.id,r=t.recoverableModules,a=t.userRecoverableModuleSlugs,c=t.hasMultipleRecoverableModules,l=Object(m.useState)(null),d=f()(l,2),k=d[0],j=d[1],S=Object(m.useState)(!1),x=f()(S,2),A=x[0],N=x[1],T=Object(g.a)(),C=Object(b.useSelect)((function(e){if(r){var t=e(y.a).getRecoveredModules();if(!t)return{};var n=Object.keys(r),i=function(e){var n;return null==t||null===(n=t.error)||void 0===n?void 0:n[e]};return n.filter((function(e){return!!i(e)})).reduce((function(e,t){return w(w({},e),{},u()({},t,w({name:r[t].name},i(t))))}),{})}})),D=Object(b.useDispatch)(y.a),R=D.recoverModules,M=D.clearRecoveredModules,L=Object(b.useDispatch)(E.a).dismissNotification,P=Object(m.useCallback)(s()(o.a.mark((function e(){var t,r,i;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return N(!0),e.next=3,M();case 3:return e.next=5,R(k);case 5:r=e.sent,i=Object.keys((null==r||null===(t=r.response)||void 0===t?void 0:t.success)||{}).filter((function(e){return r.response.success[e]})),a.length===i.length&&L(n,{skipHidingFromQueue:!1}),T()&&(j(null),N(!1));case 9:case"end":return e.stop()}}),e)}))),[n,T,M,L,R,k,a]);Object(m.useEffect)((function(){null===k&&Array.isArray(a)&&j(a)}),[k,a]);var F=!(null==k?void 0:k.length);return e.createElement(m.Fragment,null,c&&e.createElement(m.Fragment,null,k&&a.map((function(t){return e.createElement("div",{key:t},e.createElement(v.Checkbox,{checked:k.includes(t),name:"module-recovery-alert-checkbox",id:"module-recovery-alert-checkbox-".concat(t),onChange:function(){k.includes(t)?j(Object(p.without)(k,t)):j([].concat(i()(k),[t]))},disabled:A,value:t},r[t].name))})),e.createElement("p",{className:"googlesitekit-publisher-win__desc"},Object(h.__)("By recovering the selected modules, you will restore access for other users by sharing access via your Google account. This does not make any changes to external services and can be managed at any time via the dashboard sharing settings.","google-site-kit"))),!c&&e.createElement("p",{className:"googlesitekit-publisher-win__desc"},Object(h.__)("By recovering the module, you will restore access for other users by sharing access via your Google account. This does not make any changes to external services and can be managed at any time via the dashboard sharing settings.","google-site-kit")),Object.keys(C).length>0&&e.createElement(_.a,{recoveryErrors:C}),e.createElement(O.a,{id:n,ctaLabel:Object(h.__)("Recover","google-site-kit"),isSaving:A,onCTAClick:P,dismissOnCTAClick:!1,dismissLabel:Object(h.__)("Remind me later","google-site-kit"),ctaDismissOptions:{skipHidingFromQueue:!1},ctaDisabled:F}))}}).call(this,n(4))},1012:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Errors}));var r=n(1),i=n.n(r),a=n(0),o=n(2);function Errors(t){var n=t.recoveryErrors;return e.createElement("div",{className:"googlesitekit-module-recovery-errors"},1===Object.keys(n).length&&e.createElement("p",null,Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),Object.values(n)[0].message)),Object.keys(n).length>1&&e.createElement(a.Fragment,null,e.createElement("p",null,Object(o.__)("The following modules failed to be recovered:","google-site-kit")),e.createElement("ul",null,Object.keys(n).map((function(t){return e.createElement("li",{key:t},Object(o.sprintf)(
/* translators: 1: Module name, 2: Error message */
Object(o.__)("%1$s: %2$s","google-site-kit"),n[t].name,n[t].message))})))))}Errors.propTypes={recoveryErrors:i.a.object.isRequired}}).call(this,n(4))},1013:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UnrecoverableActions}));var r=n(0),i=n(2),a=n(9),o=n(92);function UnrecoverableActions(t){var n=t.id,c=t.recoverableModules,s=t.hasMultipleRecoverableModules;return e.createElement(r.Fragment,null,s&&e.createElement("ul",{className:"mdc-list mdc-list--non-interactive"},Object.values(c||{}).map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t.slug},e.createElement("span",{className:"mdc-list-item__text"},t.name))}))),e.createElement(o.a,{id:n,dismissLabel:Object(i.__)("Remind me later","google-site-kit"),dismissExpires:a.a}))}}).call(this,n(4))},1014:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SiteKitSetupSuccessNotification}));var r=n(15),i=n.n(r),a=n(1),o=n.n(a),c=n(2),s=n(0),l=n(3),u=n(13),d=n(1015),f=n(1016),p=n(111),g=n(180),m=n(92),h=n(147);function SiteKitSetupSuccessNotification(t){var n=t.id,r=t.Notification,a=Object(l.useSelect)((function(e){return e(u.c).getAdminURL("googlesitekit-settings")})),o=Object(h.a)("notification"),b=i()(o,2)[1],v=Object(s.useCallback)((function(){b(void 0)}),[b]);return e.createElement(r,{className:"googlesitekit-publisher-win"},e.createElement(f.a,{title:Object(c.__)("Congrats on completing the setup for Site Kit!","google-site-kit"),description:e.createElement(p.a,{text:Object(c.__)("Connect more services to see more stats.","google-site-kit"),learnMoreLink:e.createElement(g.a,{id:n,label:Object(c.__)("Go to Settings","google-site-kit"),url:"".concat(a,"#/connect-more-services"),external:!1})}),actions:e.createElement(m.a,{id:n,dismissLabel:Object(c.__)("OK, Got it!","google-site-kit"),onDismiss:v}),SVG:d.a}))}SiteKitSetupSuccessNotification.propTypes={id:o.a.string.isRequired,Notification:o.a.elementType}}).call(this,n(4))},1015:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{stroke:"#E8EAED",strokeLinecap:"round",strokeMiterlimit:10,strokeWidth:6,d:"M200 10v31.42M105 35.46l15.71 27.2M35.46 105l27.2 15.71M10 200h31.42M35.46 295l27.2-15.71M364.54 295l-27.2-15.71M390 200h-31.42M364.54 105l-27.2 15.71M295 35.46l-15.71 27.2"}),o=r.createElement("path",{d:"M251.2 51.21c-.67-1 10.33-9.56 11.16-10.16 5.81-4.22 12.64-8 19.94-8.46 7.91-.53 15.45 2.58 23 4.58 10.31 2.77 21.44 3.8 32.09 1.69 9.59-1.9 20.83-5.9 27.87-12.93-.24.24.3 2.83.33 3.18a65.31 65.31 0 010 8.51 59.1 59.1 0 01-7 25.06 43.41 43.41 0 01-18.94 18.13C329.14 86 317.78 88.93 307 93.41c-6.7 2.79-18.11 7.23-18.11 16.07z",fill:"#F439A0"}),c=r.createElement("path",{stroke:"#DADCE0",strokeMiterlimit:10,strokeWidth:7,d:"M248.83 47.55l93.63 144.69"}),s=r.createElement("path",{d:"M279.89 172.88c18.52 6.68 53.35 9.87 64.53-10.87 14.77-27.39-23.47-38.52-43.87-34.53M88.94 145C13 233.15 70 296.93 119 267.62",stroke:"#34A853",strokeLinejoin:"round",strokeWidth:9}),l=r.createElement("path",{d:"M234.54 260.65c59.23 111 7.37 98-23.53 93.23l2 20.19M186.58 274.6c31.93 144.76-28.4 65.93-35.58 53.33l-12.25 14.36",stroke:"#188038",strokeLinejoin:"round",strokeWidth:9}),u=r.createElement("path",{d:"M234.54 260.65q3.92 7.32 7.19 14M186.58 274.6q2.19 9.88 3.81 18.42",stroke:"#137333",strokeLinejoin:"round",strokeWidth:9}),d=r.createElement("path",{fill:"#1E8E3E",d:"M170.71 172.85l-98 39.35 39.35 98 98-39.35 98-39.35-39.35-98-98 39.35z"}),f=r.createElement("path",{d:"M158.45 268.63c-16.25 2.37-32.52-5.2-43.75-18.63l83.92-22.8",stroke:"#FFF",strokeMiterlimit:10,strokeWidth:6}),p=r.createElement("path",{stroke:"#DADCE0",strokeMiterlimit:10,strokeWidth:7,d:"M316.62 152.31l25.84 39.93"});t.a=function SvgSuccessGreen(e){return r.createElement("svg",i({viewBox:"0 0 400 400",fill:"none"},e),a,o,c,s,l,u,d,f,p)}},1016:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationWithSmallerRightSVG}));var r=n(17),i=n(189);function NotificationWithSmallerRightSVG(t){var n=t.actions,a=t.description,o=t.title,c=t.SVG;return e.createElement(r.e,null,e.createElement(r.k,null,e.createElement(r.a,{smSize:4,mdSize:6,lgSize:8,className:"googlesitekit-publisher-win__content"},e.createElement(i.a,{title:o}),a,n),e.createElement(r.a,{smSize:4,mdSize:2,lgSize:4,className:"googlesitekit-publisher-win__image"},e.createElement("div",{className:"googlesitekit-publisher-win__image-smaller"},e.createElement(c,null)))))}}).call(this,n(4))},1017:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleSetupSuccessNotification}));var r=n(15),i=n.n(r),a=n(1),o=n.n(a),c=n(2),s=n(3),l=n(19),u=n(13),d=n(115),f=n(168),p=n(92),g=n(147),m=n(18);function ModuleSetupSuccessNotification(t){var n=t.id,r=t.Notification,a=Object(g.a)("notification"),o=i()(a,2)[1],h=Object(g.a)("slug"),b=i()(h,2),v=b[0],y=b[1],E=Object(s.useSelect)((function(e){return e(l.a).getModule(v)})),_=Object(s.useSelect)((function(e){return e(u.c).getAdminURL("googlesitekit-settings")})),O=Object(m.a)(),k={category:"".concat(O,"_setup-success-notification-").concat(null==E?void 0:E.slug)};return e.createElement(r,{gaTrackingEventArgs:k},e.createElement(d.a,{title:Object(c.sprintf)(
/* translators: %s: module name */
Object(c.__)("Congrats on completing the setup for %s!","google-site-kit"),null==E?void 0:E.name),description:Object(c.__)("Connect more services to see more stats.","google-site-kit"),dismissCTA:e.createElement(p.a,{id:n,primary:!1,dismissLabel:Object(c.__)("Got it","google-site-kit"),onDismiss:function(){o(void 0),y(void 0)},gaTrackingEventArgs:k}),additionalCTA:e.createElement(f.a,{id:n,ctaLabel:Object(c.__)("Go to Settings","google-site-kit"),ctaLink:"".concat(_,"#/connect-more-services"),gaTrackingEventArgs:k})}))}ModuleSetupSuccessNotification.propTypes={id:o.a.string.isRequired,Notification:o.a.elementType.isRequired}}).call(this,n(4))},102:function(e,t,n){"use strict";var r=n(123);n.d(t,"a",(function(){return r.a}));var i=n(124);n.d(t,"c",(function(){return i.a}));var a=n(125);n.d(t,"b",(function(){return a.a}))},104:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return r.createElement("svg",i({viewBox:"0 0 14 14",fill:"none"},e),a)}},107:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var r=n(21),i=n.n(r),a=n(152),o=n.n(a),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(2),f=n(10),p=n(154),g=n(104);function TourTooltip(t){var n=t.backProps,r=t.closeProps,c=t.index,l=t.primaryProps,u=t.size,m=t.step,h=t.tooltipProps,b=u>1?Object(p.a)(u):[],v=function(e){return s()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",i()({className:s()("googlesitekit-tour-tooltip",m.className)},h),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},m.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},m.content)),e.createElement(a.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},b.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:v(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(f.Button,i()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),m.cta,l.title&&e.createElement(f.Button,i()({className:"googlesitekit-tooltip-button",text:!0},l),l.title))),e.createElement(f.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(g.a,{width:"14",height:"14"}),onClick:r.onClick,"aria-label":Object(d.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(4))},111:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(1),i=n.n(r),a=n(0),o=n(9),c=n(54);function Description(t){var n=t.className,r=void 0===n?"googlesitekit-publisher-win__desc":n,i=t.text,s=t.learnMoreLink,l=t.errorText,u=t.children;return e.createElement(a.Fragment,null,e.createElement("div",{className:r},e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.F)(i,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",s)),l&&e.createElement(c.a,{message:l}),u)}Description.propTypes={className:i.a.string,text:i.a.string,learnMoreLink:i.a.node,errorText:i.a.string,children:i.a.node}}).call(this,n(4))},115:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(0),s=n(137),l=n(58),u=n(131),d=n(17),f=Object(c.forwardRef)((function(t,n){var r=t.className,i=t.title,a=t.description,c=t.dismissCTA,f=t.additionalCTA,p=t.reverseCTAs,g=void 0!==p&&p,m=t.type,h=void 0===m?"success":m,b=t.icon;return e.createElement(d.e,{ref:n},e.createElement(d.k,null,e.createElement(d.a,{alignMiddle:!0,size:12,className:o()("googlesitekit-subtle-notification",r,{"googlesitekit-subtle-notification--success":"success"===h,"googlesitekit-subtle-notification--warning":"warning"===h,"googlesitekit-subtle-notification--new-feature":"new-feature"===h})},e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},b,"success"===h&&!b&&e.createElement(s.a,{width:24,height:24}),"warning"===h&&!b&&e.createElement(l.a,{width:24,height:24}),"new-feature"===h&&!b&&e.createElement(u.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,i),e.createElement("p",{className:"googlesitekit-subtle-notification__secondary_description"},a)),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},!g&&c,g&&f,!g&&f,g&&c))))}));f.propTypes={className:i.a.string,title:i.a.node,description:i.a.node,dismissCTA:i.a.node,additionalCTA:i.a.node,reverseCTAs:i.a.bool,type:i.a.oneOf(["success","warning","new-feature"]),icon:i.a.object},t.a=f}).call(this,n(4))},122:function(e,t,n){"use strict";n.d(t,"d",(function(){return c})),n.d(t,"b",(function(){return s})),n.d(t,"a",(function(){return l})),n.d(t,"c",(function(){return u})),n.d(t,"e",(function(){return d}));var r=n(33),i=n.n(r),a=n(14),o=n(178);function c(e){var t=function(e){return"string"==typeof e&&/^[a-zA-Z0-9_]+$/.test(e)};return"string"==typeof e?e.split(",").every(t):Object(o.c)(e,(function(e){var n=e.hasOwnProperty("name")&&t(e.name);if(!e.hasOwnProperty("expression"))return n;var r="string"==typeof e.expression;return n&&r}),t)}function s(e){return Object(o.c)(e,(function(e){return e.hasOwnProperty("name")&&"string"==typeof e.name}))}function l(e){var t=["string"];return Object.keys(e).every((function(n){if(t.includes(i()(e[n])))return!0;if(Array.isArray(e[n]))return e[n].every((function(e){return t.includes(i()(e))}));if(Object(a.isPlainObject)(e[n])){var r=Object.keys(e[n]);return!!r.includes("filterType")&&!("emptyFilter"!==e[n].filterType&&!r.includes("value"))}return!1}))}function u(e){var t=["string"],n=["numericFilter","betweenFilter"];return Object.values(e).every((function(e){if(t.includes(i()(e)))return!0;if(Array.isArray(e))return e.every((function(e){return t.includes(i()(e))}));if(!Object(a.isPlainObject)(e))return!1;var r=e.filterType,o=e.value,c=e.fromValue,s=e.toValue;if(r&&!n.includes(r))return!1;var l=Object.keys(e);return r&&"numericFilter"!==r?"betweenFilter"===r&&(l.includes("fromValue")&&l.includes("toValue")&&[c,s].every((function(e){return!Object(a.isPlainObject)(e)||"int64Value"in e}))):l.includes("operation")&&l.includes("value")&&(!Object(a.isPlainObject)(o)||"int64Value"in o)}))}function d(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(a.isPlainObject)(e)&&((!e.hasOwnProperty("desc")||"boolean"==typeof e.desc)&&(e.metric?!e.dimension&&"string"==typeof(null===(t=e.metric)||void 0===t?void 0:t.metricName):!!e.dimension&&"string"==typeof(null===(n=e.dimension)||void 0===n?void 0:n.dimensionName)));var t,n}))}},123:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(21),i=n.n(r),a=n(6),o=n.n(a),c=n(25),s=n.n(c),l=n(1),u=n.n(l),d=n(11),f=n.n(d);function Cell(t){var n,r=t.className,a=t.alignTop,c=t.alignMiddle,l=t.alignBottom,u=t.alignRight,d=t.alignLeft,p=t.smAlignRight,g=t.mdAlignRight,m=t.lgAlignRight,h=t.smSize,b=t.smStart,v=t.smOrder,y=t.mdSize,E=t.mdStart,_=t.mdOrder,O=t.lgSize,k=t.lgStart,w=t.lgOrder,j=t.size,S=t.children,x=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",i()({},x,{className:f()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":l,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":p,"mdc-layout-grid__cell--align-right-tablet":g,"mdc-layout-grid__cell--align-right-desktop":m},o()(n,"mdc-layout-grid__cell--span-".concat(j),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(O,"-desktop"),12>=O&&O>0),o()(n,"mdc-layout-grid__cell--start-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--order-".concat(w,"-desktop"),12>=w&&w>0),o()(n,"mdc-layout-grid__cell--span-".concat(y,"-tablet"),8>=y&&y>0),o()(n,"mdc-layout-grid__cell--start-".concat(E,"-tablet"),8>=E&&E>0),o()(n,"mdc-layout-grid__cell--order-".concat(_,"-tablet"),8>=_&&_>0),o()(n,"mdc-layout-grid__cell--span-".concat(h,"-phone"),4>=h&&h>0),o()(n,"mdc-layout-grid__cell--start-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--order-".concat(v,"-phone"),4>=v&&v>0),n))}),S)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},124:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",i()({ref:n,className:u()("mdc-layout-grid__inner",r)},c),a)}));f.displayName="Row",f.propTypes={className:s.a.string,children:s.a.node},f.defaultProps={className:""},t.a=f}).call(this,n(4))},125:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.alignLeft,a=t.fill,c=t.className,s=t.children,l=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",i()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":l,"mdc-layout-grid--fill":a})},d,{ref:n}),s)}));f.displayName="Grid",f.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},f.defaultProps={className:""},t.a=f}).call(this,n(4))},126:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},1260:function(e,t,n){"use strict";n.r(t),function(e){var r=n(3),i=n.n(r),a=n(810);Object(a.c)(i.a);var o=Object(a.a)(i.a);Object(a.b)(o),void 0===e.googlesitekit&&(e.googlesitekit={}),e.googlesitekit.notifications=o,t.default=o}.call(this,n(28))},127:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},128:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var r="core/site",i="primary",a="secondary"},131:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},137:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},147:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(15),o=n.n(a),c=n(0),s=n(409),l=n(157);t.a=function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=Object(c.useState)(Object(s.a)(r.location.href,t)||n),u=o()(a,2),d=u[0],f=u[1],p=function(e){f(e);var n=Object(l.a)(r.location.href,i()({},t,e));r.history.replaceState(null,"",n)};return[d,p]}}).call(this,n(28))},148:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M3.332 24.75h21.335c2.182 0 3.542-2.366 2.451-4.25L16.451 2.07C15.36.184 12.64.184 11.549 2.07L.882 20.5c-1.091 1.884.269 4.25 2.45 4.25zM14 14.833a1.42 1.42 0 01-1.417-1.416v-2.834c0-.779.638-1.416 1.417-1.416.78 0 1.417.637 1.417 1.416v2.834A1.42 1.42 0 0114 14.833zm1.417 5.667h-2.834v-2.833h2.834V20.5z",fill:"currentColor",fillRule:"nonzero"});t.a=function SvgError(e){return r.createElement("svg",i({viewBox:"0 0 28 25"},e),a)}},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},168:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALinkSubtle}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(73),f=n(10),p=n(70);function CTALinkSubtle(t){var n=t.id,r=t.ctaLink,a=t.ctaLabel,c=t.onCTAClick,s=t.isCTALinkExternal,l=void 0!==s&&s,g=t.gaTrackingEventArgs,m=t.tertiary,h=void 0!==m&&m,b=t.isSaving,v=void 0!==b&&b,y=Object(d.a)(n,null==g?void 0:g.category),E=function(){var e=o()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==c?void 0:c(t);case 2:y.confirm(null==g?void 0:g.label,null==g?void 0:g.value);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(f.Button,{className:u()("googlesitekit-subtle-notification__cta",{"googlesitekit-subtle-notification__cta--spinner__running":v}),href:r,onClick:E,target:l?"_blank":"_self",trailingIcon:l?e.createElement(p.a,{width:14,height:14}):void 0,icon:v?e.createElement(f.CircularProgress,{size:14}):void 0,tertiary:h},a)}CTALinkSubtle.propTypes={id:s.a.string,ctaLink:s.a.string,ctaLabel:s.a.string,onCTAClick:s.a.func,isCTALinkExternal:s.a.bool,gaTrackingEventArgs:s.a.shape({label:s.a.string,value:s.a.string}),tertiary:s.a.bool,isSaving:s.a.bool}}).call(this,n(4))},17:function(e,t,n){"use strict";var r=n(254);n.d(t,"i",(function(){return r.a}));var i=n(319);n.d(t,"f",(function(){return i.a}));var a=n(320);n.d(t,"h",(function(){return a.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var s=n(91),l=n.n(s);n.d(t,"b",(function(){return l.a})),n.d(t,"c",(function(){return s.DialogContent})),n.d(t,"d",(function(){return s.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},175:function(e,t,n){"use strict";var r=n(216);n.d(t,"b",(function(){return r.a}));var i=n(221);n.d(t,"a",(function(){return i.a}))},176:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(374);function i(e){return Object(r.a)(e)}},178:function(e,t,n){"use strict";n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"d",(function(){return s}));var r=n(33),i=n.n(r);function a(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return!0};return"string"==typeof e?n(e):!("object"!==i()(e)||!t(e))||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e?n(e):"object"===i()(e)&&t(e)}))}function o(e){var t=e.startDate,n=e.endDate,r=t&&t.match(/^\d{4}-\d{2}-\d{2}$/),i=n&&n.match(/^\d{4}-\d{2}-\d{2}$/);return r&&i}function c(e){var t=function(e){var t=e.hasOwnProperty("fieldName")&&!!e.fieldName,n=e.hasOwnProperty("sortOrder")&&/(ASCENDING|DESCENDING)/i.test(e.sortOrder.toString());return t&&n};return Array.isArray(e)?e.every((function(e){return"object"===i()(e)&&t(e)})):"object"===i()(e)&&t(e)}function s(e){return"string"==typeof e||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e}))}},179:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActionsCTALinkDismiss}));var r=n(1),i=n.n(r),a=n(2),o=n(0),c=n(3),s=n(32),l=n(92),u=n(187);function ActionsCTALinkDismiss(t){var n=t.id,r=t.className,i=void 0===r?"googlesitekit-publisher-win__actions":r,d=t.ctaLink,f=t.ctaLabel,p=t.ctaDisabled,g=void 0!==p&&p,m=t.onCTAClick,h=t.ctaDismissOptions,b=t.isSaving,v=void 0!==b&&b,y=t.onDismiss,E=void 0===y?function(){}:y,_=t.dismissLabel,O=void 0===_?Object(a.__)("OK, Got it!","google-site-kit"):_,k=t.dismissOnCTAClick,w=void 0===k||k,j=t.dismissExpires,S=void 0===j?0:j,x=t.dismissOptions,A=void 0===x?{}:x,N=t.gaTrackingEventArgs,T=void 0===N?{}:N,C=Object(c.useSelect)((function(e){return!!d&&e(s.a).isNavigatingTo(d)}));return e.createElement(o.Fragment,null,e.createElement("div",{className:i},e.createElement(u.a,{id:n,ctaLink:d,ctaLabel:f,onCTAClick:m,dismissOnCTAClick:w,dismissExpires:S,dismissOptions:h,gaTrackingEventArgs:T,isSaving:v,isDisabled:g}),e.createElement(l.a,{id:n,primary:!1,dismissLabel:O,dismissExpires:S,disabled:C,onDismiss:E,dismissOptions:A,gaTrackingEventArgs:T})))}ActionsCTALinkDismiss.propTypes={id:i.a.string,className:i.a.string,ctaDisabled:i.a.bool,ctaLink:i.a.string,ctaLabel:i.a.string,onCTAClick:i.a.func,isSaving:i.a.bool,onDismiss:i.a.func,ctaDismissOptions:i.a.object,dismissLabel:i.a.string,dismissOnCTAClick:i.a.bool,dismissExpires:i.a.number,dismissOptions:i.a.object,gaTrackingEventArgs:i.a.object}}).call(this,n(4))},18:function(e,t,n){"use strict";var r=n(0),i=n(61);t.a=function(){return Object(r.useContext)(i.b)}},180:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LearnMoreLink}));var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(20),u=n(73);function LearnMoreLink(t){var n=t.id,r=t.label,a=t.url,c=t.ariaLabel,s=t.gaTrackingEventArgs,d=t.external,f=void 0===d||d,p=o()(t,["id","label","url","ariaLabel","gaTrackingEventArgs","external"]),g=Object(u.a)(n);return e.createElement(l.a,i()({onClick:function(e){e.persist(),g.clickLearnMore(null==s?void 0:s.label,null==s?void 0:s.value)},href:a,"aria-label":c,external:f},p),r)}LearnMoreLink.propTypes={id:s.a.string,label:s.a.string,url:s.a.string,ariaLabel:s.a.string,gaTrackingEventArgs:s.a.shape({label:s.a.string,value:s.a.string}),external:s.a.bool}}).call(this,n(4))},187:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALink}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(15),u=n.n(l),d=n(1),f=n.n(d),p=n(206),g=n(0),m=n(3),h=n(41),b=n(32),v=n(13),y=n(73),E=n(10);function _(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function CTALink(t){var n=t.id,r=t.ctaLink,a=t.ctaLabel,o=t.onCTAClick,c=t.isSaving,l=t.dismissOnCTAClick,d=void 0!==l&&l,f=t.dismissExpires,_=void 0===f?0:f,k=t.dismissOptions,w=void 0===k?{}:k,j=t.gaTrackingEventArgs,S=t.isDisabled,x=void 0!==S&&S,A=Object(g.useState)(!1),N=u()(A,2),T=N[0],C=N[1],D=Object(p.a)(),R=Object(y.a)(n,null==j?void 0:j.category),M=Object(m.useSelect)((function(e){return!!r&&e(b.a).isNavigatingTo(r)})),L=Object(m.useDispatch)(v.c),P=L.clearError,F=L.receiveError,I=Object(m.useDispatch)(h.a).dismissNotification,B=Object(m.useDispatch)(b.a).navigateTo,U=function(){var e=s()(i.a.mark((function e(t){var a,c,s;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return P("notificationAction",[n]),t.persist(),!t.defaultPrevented&&r&&t.preventDefault(),C(!0),e.next=6,null==o?void 0:o(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:if(a=e.t0,c=a.error,D()&&C(!1),!c){e.next=15;break}return F(c,"notificationAction",[n]),e.abrupt("return");case 15:return s=[R.confirm()],d&&s.push(I(n,O(O({},w),{},{expiresInSeconds:_}))),e.next=19,Promise.all(s);case 19:r&&B(r);case 20:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(E.SpinnerButton,{className:"googlesitekit-notification__cta",href:r,onClick:U,disabled:T||M||x,isSaving:T||M||c},a)}CTALink.propTypes={id:f.a.string,ctaLink:f.a.string,ctaLabel:f.a.string,onCTAClick:f.a.func,dismissOnCTAClick:f.a.bool,dismissExpires:f.a.number,dismissOptions:f.a.object,isDisabled:f.a.bool}}).call(this,n(4))},189:function(e,t,n){"use strict";(function(e){function Title(t){var n=t.title;return e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n)}n.d(t,"a",(function(){return Title}))}).call(this,n(4))},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="core/modules",i="insufficient_module_dependencies"},196:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationError}));var r=n(17),i=n(222),a=n(189);function NotificationError(t){var n=t.title,o=t.description,c=t.actions;return e.createElement(r.e,null,e.createElement(r.k,null,e.createElement(r.a,{smSize:3,mdSize:7,lgSize:11,className:"googlesitekit-publisher-win__content"},e.createElement(a.a,{title:n}),o,c),e.createElement(i.a,{type:"win-error"})))}}).call(this,n(4))},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(146),f=n(0),p=n(2),g=n(126),m=n(127),h=n(128),b=n(70),v=n(76),y=Object(f.forwardRef)((function(t,n){var r,a=t["aria-label"],c=t.secondary,l=void 0!==c&&c,u=t.arrow,f=void 0!==u&&u,y=t.back,E=void 0!==y&&y,_=t.caps,O=void 0!==_&&_,k=t.children,w=t.className,j=void 0===w?"":w,S=t.danger,x=void 0!==S&&S,A=t.disabled,N=void 0!==A&&A,T=t.external,C=void 0!==T&&T,D=t.hideExternalIndicator,R=void 0!==D&&D,M=t.href,L=void 0===M?"":M,P=t.inverse,F=void 0!==P&&P,I=t.noFlex,B=void 0!==I&&I,U=t.onClick,V=t.small,z=void 0!==V&&V,G=t.standalone,W=void 0!==G&&G,q=t.linkButton,H=void 0!==q&&q,K=t.to,$=t.leadingIcon,Q=t.trailingIcon,J=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),X=L||K||!U?K?"ROUTER_LINK":C?"EXTERNAL_LINK":"LINK":N?"BUTTON_DISABLED":"BUTTON",Y="BUTTON"===X||"BUTTON_DISABLED"===X?"button":"ROUTER_LINK"===X?d.b:"a",Z=("EXTERNAL_LINK"===X&&(r=Object(p._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===X&&(r=Object(p._x)("(disabled)","screen reader text","google-site-kit")),r?a?"".concat(a," ").concat(r):"string"==typeof k?"".concat(k," ").concat(r):void 0:a),ee=$,te=Q;return E&&(ee=e.createElement(h.a,{width:14,height:14})),C&&!R&&(te=e.createElement(b.a,{width:14,height:14})),f&&!F&&(te=e.createElement(g.a,{width:14,height:14})),f&&F&&(te=e.createElement(m.a,{width:14,height:14})),e.createElement(Y,i()({"aria-label":Z,className:s()("googlesitekit-cta-link",j,{"googlesitekit-cta-link--secondary":l,"googlesitekit-cta-link--inverse":F,"googlesitekit-cta-link--small":z,"googlesitekit-cta-link--caps":O,"googlesitekit-cta-link--danger":x,"googlesitekit-cta-link--disabled":N,"googlesitekit-cta-link--standalone":W,"googlesitekit-cta-link--link-button":H,"googlesitekit-cta-link--no-flex":!!B}),disabled:N,href:"LINK"!==X&&"EXTERNAL_LINK"!==X||N?void 0:L,onClick:U,rel:"EXTERNAL_LINK"===X?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===X?"_blank":void 0,to:K},J),!!ee&&e.createElement(v.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},k),!!te&&e.createElement(v.a,{marginLeft:5},te))}));y.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=y}).call(this,n(4))},205:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a}));var r="warning-notification-fpm",i="fpm-setup-cta",a={ERROR_HIGH:30,ERROR_LOW:60,WARNING:100,INFO:150,SETUP_CTA_HIGH:150,SETUP_CTA_LOW:200}},216:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(0),u=n(3),d=n(13),f=n(23);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e){var t=Object(u.useDispatch)(f.b).setValue,n=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.2")})),r=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.4")}));return Object(l.useCallback)(s()(i.a.mark((function a(){var o,c,s,l;return i.a.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(o=document.querySelector("#adminmenu").offsetHeight>0){i.next=7;break}if(!(c=document.getElementById("wp-admin-bar-menu-toggle"))){i.next=7;break}return c.firstChild.click(),i.next=7,new Promise((function(e){setTimeout(e,0)}));case 7:"#adminmenu [href*='page=googlesitekit-dashboard']",(s=!!document.querySelector("".concat("#adminmenu [href*='page=googlesitekit-dashboard']","[aria-haspopup=true]")))&&document.querySelector("#adminmenu [href*='page=googlesitekit-dashboard']").click(),n&&!r&&(l=document.hasFocus,document.hasFocus=function(){return document.hasFocus=l,!1}),t("admin-menu-tooltip",g({isTooltipVisible:!0,rehideAdminMenu:!o,rehideAdminSubMenu:s},e));case 12:case"end":return i.stop()}}),a)}))),[n,r,t,e])}},22:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return i})),n.d(t,"o",(function(){return a})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return s})),n.d(t,"s",(function(){return l})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return f})),n.d(t,"k",(function(){return p})),n.d(t,"u",(function(){return g})),n.d(t,"v",(function(){return m})),n.d(t,"q",(function(){return h})),n.d(t,"p",(function(){return b})),n.d(t,"b",(function(){return v})),n.d(t,"e",(function(){return y})),n.d(t,"a",(function(){return E})),n.d(t,"d",(function(){return _})),n.d(t,"c",(function(){return O})),n.d(t,"f",(function(){return k})),n.d(t,"g",(function(){return w}));var r="mainDashboard",i="entityDashboard",a="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",s="activation",l="splash",u="adminBar",d="adminBarViewOnly",f="settings",p="adBlockingRecovery",g="wpDashboard",m="wpDashboardViewOnly",h="moduleSetup",b="metricSelection",v="key-metrics",y="traffic",E="content",_="speed",O="monetization",k=[r,i,a,o,c,l,f,h,b],w=[a,o,d,m]},221:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdminMenuTooltip}));var r=n(0),i=n(3),a=n(223),o=n(23),c=n(9),s=n(18);function AdminMenuTooltip(){var t=Object(s.a)(),n=Object(i.useDispatch)(o.b).setValue,l=Object(i.useSelect)((function(e){return e(o.b).getValue("admin-menu-tooltip")||{isTooltipVisible:!1}})),u=l.isTooltipVisible,d=void 0!==u&&u,f=l.rehideAdminMenu,p=void 0!==f&&f,g=l.rehideAdminSubMenu,m=void 0!==g&&g,h=l.tooltipSlug,b=l.title,v=l.content,y=l.dismissLabel,E=Object(r.useCallback)((function(){var e;p&&(document.querySelector("#adminmenu").offsetHeight>0&&(null===(e=document.getElementById("wp-admin-bar-menu-toggle"))||void 0===e||e.click()));m&&document.querySelector("body").click(),h&&Object(c.I)("".concat(t,"_").concat(h),"tooltip_dismiss"),n("admin-menu-tooltip",void 0)}),[p,m,n,h,t]);return d?e.createElement(a.a,{target:'#adminmenu [href*="page=googlesitekit-settings"]',slug:"ga4-activation-banner-admin-menu-tooltip",title:b,content:v,dismissLabel:y,onView:function(){Object(c.I)("".concat(t,"_").concat(h),"tooltip_view")},onDismiss:E}):null}}).call(this,n(4))},222:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var r=n(1),i=n.n(r),a=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var r="win-warning"===n?e.createElement(a.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},r))}BannerIcon.propTypes={type:i.a.string}}).call(this,n(4))},223:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return JoyrideTooltip}));var i=n(6),a=n.n(i),o=n(15),c=n.n(o),s=n(1),l=n(30),u=n(421),d=n(0),f=n(107),p=n(72),g=n(90);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function JoyrideTooltip(t){var n=t.title,i=t.content,a=t.dismissLabel,o=t.target,s=t.cta,m=void 0!==s&&s,b=t.className,v=t.styles,y=void 0===v?{}:v,E=t.slug,_=void 0===E?"":E,O=t.onDismiss,k=void 0===O?function(){}:O,w=t.onView,j=void 0===w?function(){}:w,S=t.onTourStart,x=void 0===S?function(){}:S,A=t.onTourEnd,N=void 0===A?function(){}:A,T=function(){return!!e.document.querySelector(o)},C=Object(d.useState)(T),D=c()(C,2),R=D[0],M=D[1];if(Object(u.a)((function(){T()&&M(!0)}),R?null:250),Object(d.useEffect)((function(){if(R&&e.ResizeObserver){var t=e.document.querySelector(o),n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}),[o,R]),!R)return null;var L=[{title:n,target:o,content:i,disableBeacon:!0,isFixed:!0,placement:"auto",cta:m,className:b}],P={close:a,last:a};return r.createElement(p.a,{slug:_},r.createElement(l.e,{callback:function(t){switch(t.type){case l.b.TOUR_START:x(),e.document.body.classList.add("googlesitekit-showing-tooltip");break;case l.b.TOUR_END:N(),e.document.body.classList.remove("googlesitekit-showing-tooltip");break;case l.b.STEP_AFTER:k();break;case l.b.TOOLTIP:j()}},disableOverlay:!0,disableScrolling:!0,spotlightPadding:0,floaterProps:g.b,locale:P,steps:L,styles:h(h(h({},g.c),y),{},{options:h(h({},g.c.options),null==y?void 0:y.options),spotlight:h(h({},g.c.spotlight),null==y?void 0:y.spotlight)}),tooltipComponent:f.a,run:!0}))}JoyrideTooltip.propTypes={title:s.PropTypes.node,content:s.PropTypes.string,dismissLabel:s.PropTypes.string,target:s.PropTypes.string.isRequired,onDismiss:s.PropTypes.func,onShow:s.PropTypes.func,className:s.PropTypes.string,styles:s.PropTypes.object,slug:s.PropTypes.string,onView:s.PropTypes.func}}).call(this,n(28),n(4))},23:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r="core/ui",i="activeContextID"},24:function(e,t,n){"use strict";n.d(t,"d",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return s}));var r=n(79),i="xlarge",a="desktop",o="tablet",c="small";function s(){var e=Object(r.a)();return e>1280?i:e>960?a:e>600?o:c}},242:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationWithSVG}));var r=n(21),i=n.n(r),a=n(11),o=n.n(a),c=n(24),s=n(17),l=n(255);function NotificationWithSVG(t){var n=t.id,r=t.title,a=t.description,u=t.actions,d=t.SVG,f=t.primaryCellSizes,p=t.SVGCellSizes,g=Object(c.e)(),m={mdSize:(null==p?void 0:p.md)||8,lgSize:(null==p?void 0:p.lg)||6};return g===c.c&&(m={mdSize:(null==p?void 0:p.md)||8}),g===c.b&&(m={smSize:(null==p?void 0:p.sm)||12}),e.createElement("div",{className:"googlesitekit-widget-context"},e.createElement(s.e,{className:"googlesitekit-widget-area"},e.createElement(s.k,null,e.createElement(s.a,{size:12},e.createElement("div",{className:o()("googlesitekit-widget","googlesitekit-widget--no-padding","googlesitekit-setup-cta-banner","googlesitekit-setup-cta-banner--".concat(n))},e.createElement("div",{className:"googlesitekit-widget__body"},e.createElement(s.e,{collapsed:!0},e.createElement(s.k,null,e.createElement(s.a,{smSize:(null==f?void 0:f.sm)||12,mdSize:(null==f?void 0:f.md)||8,lgSize:(null==f?void 0:f.lg)||6,className:"googlesitekit-setup-cta-banner__primary-cell"},e.createElement("h3",{className:"googlesitekit-setup-cta-banner__title"},r),a,e.createElement(l.a,{id:n}),u),e.createElement(s.a,i()({alignBottom:!0,className:"googlesitekit-setup-cta-banner__svg-wrapper--".concat(n)},m),e.createElement(d,null))))))))))}}).call(this,n(4))},255:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Error}));var r=n(1),i=n.n(r),a=n(0),o=n(3),c=n(13),s=n(54);function Error(t){var n=t.id,r=Object(o.useSelect)((function(e){return e(c.c).getError("notificationAction",[n])})),i=Object(o.useDispatch)(c.c).clearError;return Object(a.useEffect)((function(){return function(){i("notificationAction",[n])}}),[i,n]),r?e.createElement(s.a,{message:r.message}):null}Error.propTypes={id:i.a.string}}).call(this,n(4))},275:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var r=n(6),i=n.n(r),a=n(25),o=n.n(a),c=n(63),s=n.n(c),l=n(14);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f=s()((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.metrics,n=e.dimensions,r=o()(e,["metrics","dimensions"]);return d({metrics:p(t),dimensions:g(n)},r)})),p=function(e){return Object(l.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(l.isPlainObject)(e)}))},g=function(e){return Object(l.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(l.isPlainObject)(e)}))}},29:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/forms"},3:function(e,t){e.exports=googlesitekit.data},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(22),i=n(18);function a(){var e=Object(i.a)();return r.g.includes(e)}},342:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(14),i=n(122);function a(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(r.isPlainObject)(e)&&(!(!e.hasOwnProperty("fieldNames")||!Array.isArray(e.fieldNames)||0===e.fieldNames.length)&&(!(!e.hasOwnProperty("limit")||"number"!=typeof e.limit)&&!(e.hasOwnProperty("orderby")&&!Object(i.e)(e.orderby))))}))}},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return E})),n.d(t,"b",(function(){return v})),n.d(t,"c",(function(){return y}));var r=n(99),i=e._googlesitekitTrackingData||{},a=i.activeModules,o=void 0===a?[]:a,c=i.isSiteKitScreen,s=i.trackingEnabled,l=i.trackingID,u=i.referenceSiteURL,d=i.userIDHash,f=i.isAuthenticated,p={activeModules:o,trackingEnabled:s,trackingID:l,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:i.userRoles,isAuthenticated:f,pluginVersion:"1.151.0"},g=Object(r.a)(p),m=g.enableTracking,h=g.disableTracking,b=(g.isTrackingEnabled,g.initializeSnippet),v=g.trackEvent,y=g.trackEventOnce;function E(e){e?m():h()}c&&s&&b()}).call(this,n(28))},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return v})),n.d(t,"f",(function(){return y})),n.d(t,"c",(function(){return E})),n.d(t,"e",(function(){return _})),n.d(t,"b",(function(){return O}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u,d="googlesitekit_",f="".concat(d).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),p=["sessionStorage","localStorage"],g=[].concat(p),m=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,a="__storage_test__",r.setItem(a,a),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function h(){return b.apply(this,arguments)}function b(){return(b=o()(i.a.mark((function t(){var n,r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=s(g),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(a=r.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,m(a);case 11:if(!t.sent){t.next=13;break}u=e[a];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var v=function(){var e=o()(i.a.mark((function e(t){var n,r,a,o,c,s,l;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(f).concat(t)))){e.next=10;break}if(a=JSON.parse(r),o=a.timestamp,c=a.ttl,s=a.value,l=a.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:l});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),y=function(){var t=o()(i.a.mark((function t(n,r){var a,o,s,l,u,d,p,g,m=arguments;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=m.length>2&&void 0!==m[2]?m[2]:{},o=a.ttl,s=void 0===o?c.b:o,l=a.timestamp,u=void 0===l?Math.round(Date.now()/1e3):l,d=a.isError,p=void 0!==d&&d,t.next=3,h();case 3:if(!(g=t.sent)){t.next=14;break}return t.prev=5,g.setItem("".concat(f).concat(n),JSON.stringify({timestamp:u,ttl:s,value:r,isError:p})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),E=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,h();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,a=n.startsWith(d)?n:"".concat(f).concat(n),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),_=function(){var t=o()(i.a.mark((function t(){var n,r,a,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,h();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],a=0;a<n.length;a++)0===(o=n.key(a)).indexOf(d)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),O=function(){var e=o()(i.a.mark((function e(){var t,n,r,a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h();case 2:if(!e.sent){e.next=25;break}return e.next=6,_();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return a=r.value,e.next=14,E(a);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},373:function(e,t,n){"use strict";t.a=function(e){if("string"==typeof e&&e.match(/[0-9]{8}/)){var t=e.slice(0,4),n=Number(e.slice(4,6))-1,r=e.slice(6,8);return new Date(t,n.toString(),r)}return!1}},376:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"d",(function(){return o.a})),n.d(t,"e",(function(){return c.a})),n.d(t,"a",(function(){return s.a}));var r=n(2);function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.keyColumnIndex,i=void 0===n?0:n,a=t.maxSlices,o=t.withOthers,c=void 0!==o&&o,s=t.tooltipCallback,l=e||{},u=l.rows,d=void 0===u?[]:u,f="function"==typeof s,p=["Source","Percent"];f&&p.push({type:"string",role:"tooltip",p:{html:!0}});var g=[p],m=d.filter((function(e){return"date_range_0"===e.dimensionValues[1].value})),h=m.reduce((function(e,t){return e+parseInt(t.metricValues[0].value,10)}),0),b=d.filter((function(e){return"date_range_1"===e.dimensionValues[1].value})),v=b.reduce((function(e,t){return e+parseInt(t.metricValues[0].value,10)}),0),y=c,E=m.length,_=h,O=v;a>0?(y=c&&m.length>a,E=Math.min(m.length,y?a-1:a)):(y=!1,E=m.length);for(var k=function(e){var t=m[e],n=t.metricValues[i].value,r=b.find((function(e){return e.dimensionValues[0].value===t.dimensionValues[0].value})),a=r?r.metricValues[i].value:0;_-=n,O-=a;var o=h>0?n/h:0,c=[t.dimensionValues[0].value,o];if(f){var l=d.find((function(e){var n=e.dimensionValues;return"date_range_1"===n[1].value&&n[0].value===t.dimensionValues[0].value}));c.push(s(t,l,c))}g.push(c)},w=0;w<E;w++)k(w);if(y&&_>0){var j=[Object(r.__)("Others","google-site-kit"),_/h];f&&j.push(s({metricValues:[{value:_}]},{metricValues:[{value:O}]},j)),g.push(j)}return g}var a=function(e){var t,n,r,i,a,o,c;if(void 0!==e){var s=((null==e?void 0:e.rows)||[]).filter((function(e){return"date_range_0"===e.dimensionValues[1].value}));return 1===(null==s?void 0:s.length)||(null==s||null===(t=s[0])||void 0===t||null===(n=t.metricValues)||void 0===n||null===(r=n[0])||void 0===r?void 0:r.value)===(null==e||null===(i=e.totals)||void 0===i||null===(a=i[0])||void 0===a||null===(o=a.metricValues)||void 0===o||null===(c=o[0])||void 0===c?void 0:c.value)}},o=n(393),c=n(275),s=(n(122),n(342),n(447))},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="_googlesitekitDataLayer",i="data-googlesitekit-gtag"},393:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(14);function i(e){var t;if(void 0!==e)return!((null==e?void 0:e.rows)&&(null==e?void 0:e.totals)&&!(null==e||null===(t=e.totals)||void 0===t?void 0:t.every(r.isEmpty)))||!e.totals.some((function(e){return!!e.metricValues&&e.metricValues.some((function(e){return e.value>0}))}))}},41:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(22),i="core/notifications",a={ERRORS:"notification-area-errors",BANNERS_ABOVE_NAV:"notification-area-banners-above-nav",BANNERS_BELOW_NAV:"notification-area-banners-below-nav"},o={DEFAULT:"default",SETUP_CTAS:"setup-ctas"},c=[r.s,r.n,r.l,r.o,r.m]},447:function(e,t,n){"use strict";n.d(t,"a",(function(){return v}));var r=n(27),i=n.n(r),a=n(11),o=n.n(a),c=n(14),s=n(2),l=n(83),u=n(9),d=n(15),f=n.n(d),p=n(12),g=n.n(p);function m(e,t){var n=t.dateRangeLength;g()(Array.isArray(e),"report must be an array to partition."),g()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=function(t){return e.filter((function(e){return f()(e.dimensionValues,2)[1].value===t}))},i=-1*n;return{currentRange:r("date_range_0").slice(i),compareRange:r("date_range_1").slice(2*i,i)}}var h=n(373);function b(e,t){var n=[];return e.forEach((function(e){if(e.metricValues){var r=e.metricValues[t].value,i=e.dimensionValues[0].value,a=Object(h.a)(i);n.push([a,r])}})),n}function v(e,t,n,r){var a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[Object(s.__)("Users","google-site-kit"),Object(s.__)("Sessions","google-site-kit"),Object(s.__)("Engagement Rate","google-site-kit"),Object(s.__)("Session Duration","google-site-kit")],d=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[function(e){return parseFloat(e).toLocaleString()},function(e){return parseFloat(e).toLocaleString()},function(e){return Object(u.B)(e/100,{style:"percent",signDisplay:"never",maximumFractionDigits:2})},function(e){return Object(u.B)(e,"s")}],f=arguments.length>6&&void 0!==arguments[6]?arguments[6]:[c.identity,c.identity,function(e){return 100*e},c.identity],p=i()((null==e?void 0:e.rows)||[]),g=p.length;if(2*n>g){for(var h=Object(u.G)(r),v=0;n>v;v++){var y=(h.getMonth()+1).toString(),E=h.getDate().toString(),_=h.getFullYear().toString()+(2>y.length?"0":"")+y+(2>E.length?"0":"")+E;if(v>g){var O=[{dimensionValues:[{value:_},{value:"date_range_0"}],metricValues:[{value:0},{value:0}]},{dimensionValues:[{value:_},{value:"date_range_1"}],metricValues:[{value:0},{value:0}]}];p.unshift.apply(p,O)}h.setDate(h.getDate()-1)}p.push({dimensionValues:[{value:"0"},{value:"date_range_0"}]},{dimensionValues:[{value:"0"},{value:"date_range_1"}]})}var k=a[t]===Object(s.__)("Session Duration","google-site-kit"),w=k?"timeofday":"number",j=[[{type:"date",label:Object(s.__)("Day","google-site-kit")},{type:"string",role:"tooltip",p:{html:!0}},{type:w,label:a[t]},{type:w,label:Object(s.__)("Previous period","google-site-kit")}]],S=m(p,{dateRangeLength:n}),x=S.compareRange,A=S.currentRange,N=b(A,t),T=b(x,t),C=Object(l.b)(),D={weekday:"short",month:"short",day:"numeric"};return N.forEach((function(e,n){if(e[0]&&e[1]&&T[n]){var r=f[t],i=r(e[1]),c=r(T[n][1]),l=parseFloat(c),p=Object(u.h)(i,l),g=Object(u.o)(p),m=Object(s.sprintf)(
/* translators: 1: date for user stats, 2: previous date for user stats comparison */
Object(s._x)("%1$s vs %2$s","Date range for chart tooltip","google-site-kit"),e[0].toLocaleDateString(C,D),T[n][0].toLocaleDateString(C,D)),h=Object(s.sprintf)(
/* translators: 1: selected stat label, 2: numeric value of selected stat, 3: up or down arrow , 4: different change in percentage */
Object(s._x)("%1$s: <strong>%2$s</strong> <em>%3$s %4$s</em>","Stat information for chart tooltip","google-site-kit"),a[t],d[t](i),g,Object(u.B)(Math.abs(p),"%"));j.push([e[0],'<div class="'.concat(o()("googlesitekit-visualization-tooltip",{"googlesitekit-visualization-tooltip--up":p>0,"googlesitekit-visualization-tooltip--down":p<0}),'">\n\t\t\t\t<p>').concat(m,"</p>\n\t\t\t\t<p>").concat(h,"</p>\n\t\t\t</div>"),k?Object(u.j)(i):i,k?Object(u.j)(c):c])}})),j}},46:function(e,t,n){"use strict";n.d(t,"g",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"f",(function(){return s})),n.d(t,"h",(function(){return l})),n.d(t,"e",(function(){return u})),n.d(t,"i",(function(){return d}));var r="modules/tagmanager",i="account_create",a="container_create",o="web",c="amp",s="tagmanagerSetup",l="https://www.googleapis.com/auth/tagmanager.readonly",u="https://www.googleapis.com/auth/tagmanager.edit.containers",d="SETUP_WITH_ANALYTICS"},54:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,r=t.reconnectURL,i=t.noPrefix;if(!n)return null;var s=n;void 0!==i&&i||(s=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),r&&Object(a.a)(r)&&(s=s+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),r));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(s,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:i.a.string.isRequired,reconnectURL:i.a.string,noPrefix:i.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},547:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SimpleNotification}));var r=n(17),i=n(189);function SimpleNotification(t){var n=t.actions,a=t.description,o=t.title;return e.createElement(r.e,null,e.createElement(r.k,null,e.createElement(r.a,{size:11,className:"googlesitekit-publisher-win__content"},e.createElement(i.a,{title:o}),a,n)))}}).call(this,n(4))},57:function(e,t,n){"use strict";(function(e){var r,i;n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=new Set((null===(r=e)||void 0===r||null===(i=r._googlesitekitBaseData)||void 0===i?void 0:i.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,n(28))},58:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return r.createElement("svg",i({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),a,o)}},580:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="consent-mode-setup-cta-widget"},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(39);function i(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(0),i=Object(r.createContext)(""),a=(i.Consumer,i.Provider);t.b=i},62:function(e,t,n){"use strict";n.d(t,"a",(function(){return S})),n.d(t,"b",(function(){return x})),n.d(t,"c",(function(){return A})),n.d(t,"d",(function(){return T})),n.d(t,"e",(function(){return C})),n.d(t,"g",(function(){return R})),n.d(t,"f",(function(){return M}));var r,i=n(5),a=n.n(i),o=n(27),c=n.n(o),s=n(6),l=n.n(s),u=n(12),d=n.n(u),f=n(63),p=n.n(f),g=n(14),m=n(116);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.reduce((function(e,t){return b(b({},e),t)}),{}),i=t.reduce((function(e,t){return[].concat(c()(e),c()(Object.keys(t)))}),[]),a=N(i);return d()(0===a.length,"collect() cannot accept collections with duplicate keys. Your call to collect() contains the following duplicated functions: ".concat(a.join(", "),". Check your data stores for duplicates.")),r},y=v,E=v,_=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i=[].concat(t);return"function"!=typeof i[0]&&(r=i.shift()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i.reduce((function(e,n){return n(e,t)}),e)}},O=v,k=v,w=v,j=function(e){return e},S=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=w.apply(void 0,c()(t.map((function(e){return e.initialState||{}}))));return{initialState:r,controls:E.apply(void 0,c()(t.map((function(e){return e.controls||{}})))),actions:y.apply(void 0,c()(t.map((function(e){return e.actions||{}})))),reducer:_.apply(void 0,[r].concat(c()(t.map((function(e){return e.reducer||j}))))),resolvers:O.apply(void 0,c()(t.map((function(e){return e.resolvers||{}})))),selectors:k.apply(void 0,c()(t.map((function(e){return e.selectors||{}}))))}},x={getRegistry:function(){return{payload:{},type:"GET_REGISTRY"}},await:a.a.mark((function e(t){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{payload:{value:t},type:"AWAIT"});case 1:case"end":return e.stop()}}),e)}))},A=(r={},l()(r,"GET_REGISTRY",Object(m.a)((function(e){return function(){return e}}))),l()(r,"AWAIT",(function(e){return e.payload.value})),r),N=function(e){for(var t=[],n={},r=0;r<e.length;r++){var i=e[r];n[i]=n[i]>=1?n[i]+1:1,n[i]>1&&t.push(i)}return t},T={actions:x,controls:A,reducer:j},C=function(e){return function(t){return D(e(t))}},D=p()((function(e){return Object(g.mapValues)(e,(function(e,t){return function(){var n=e.apply(void 0,arguments);return d()(void 0!==n,"".concat(t,"(...) is not resolved")),n}}))}));function R(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.negate,r=void 0!==n&&n,i=Object(m.b)((function(t){return function(n){var i=!r,a=!!r;try{for(var o=arguments.length,c=new Array(o>1?o-1:0),s=1;s<o;s++)c[s-1]=arguments[s];return e.apply(void 0,[t,n].concat(c)),i}catch(e){return a}}})),a=Object(m.b)((function(t){return function(n){for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];e.apply(void 0,[t,n].concat(i))}}));return{safeSelector:i,dangerousSelector:a}}function M(e,t){return d()("function"==typeof e,"a validator function is required."),d()("function"==typeof t,"an action creator function is required."),d()("Generator"!==e[Symbol.toStringTag]&&"GeneratorFunction"!==e[Symbol.toStringTag],"an action’s validator function must not be a generator."),function(){return e.apply(void 0,arguments),t.apply(void 0,arguments)}}},654:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ProgressBar}));var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(24),s=n(12),l=n.n(s);function ProgressBar(t){var n,r=t.className,i=t.small,a=t.compress,s=t.indeterminate,u=t.height,d=t.smallHeight,f=t.tabletHeight,p=t.desktopHeight,g=t.progress,m=Object(c.e)(),h=u;c.b===m&&void 0!==d?h=d:c.c===m&&void 0!==f?h=f:c.d!==m&&c.a!==m||void 0===p||(h=p),void 0!==h&&(l()(h>=4,"height must be >= 4."),n=Math.round((h-4)/2));var b=g?"scaleX(".concat(g,")"):void 0;return e.createElement("div",{role:"progressbar",style:{marginTop:n,marginBottom:n},className:o()("mdc-linear-progress",r,{"mdc-linear-progress--indeterminate":s,"mdc-linear-progress--small":i,"mdc-linear-progress--compress":a})},e.createElement("div",{className:"mdc-linear-progress__buffering-dots"}),e.createElement("div",{className:"mdc-linear-progress__buffer"}),e.createElement("div",{className:"mdc-linear-progress__bar mdc-linear-progress__primary-bar",style:{transform:b}},e.createElement("span",{className:"mdc-linear-progress__bar-inner"})),e.createElement("div",{className:"mdc-linear-progress__bar mdc-linear-progress__secondary-bar"},e.createElement("span",{className:"mdc-linear-progress__bar-inner"})))}ProgressBar.propTypes={className:i.a.string,small:i.a.bool,compress:i.a.bool,indeterminate:i.a.bool,progress:i.a.number,height:i.a.number,smallHeight:i.a.number,tabletHeight:i.a.number,desktopHeight:i.a.number},ProgressBar.defaultProps={className:"",small:!1,compress:!1,indeterminate:!0,progress:0}}).call(this,n(4))},66:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r="modules/search-console",i=1},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return l})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return f})),n.d(t,"J",(function(){return p})),n.d(t,"I",(function(){return g})),n.d(t,"N",(function(){return m})),n.d(t,"f",(function(){return h})),n.d(t,"g",(function(){return b})),n.d(t,"h",(function(){return v})),n.d(t,"j",(function(){return y})),n.d(t,"l",(function(){return E})),n.d(t,"m",(function(){return _})),n.d(t,"n",(function(){return O})),n.d(t,"o",(function(){return k})),n.d(t,"q",(function(){return w})),n.d(t,"s",(function(){return j})),n.d(t,"r",(function(){return S})),n.d(t,"t",(function(){return x})),n.d(t,"w",(function(){return A})),n.d(t,"u",(function(){return N})),n.d(t,"v",(function(){return T})),n.d(t,"x",(function(){return C})),n.d(t,"y",(function(){return D})),n.d(t,"A",(function(){return R})),n.d(t,"B",(function(){return M})),n.d(t,"C",(function(){return L})),n.d(t,"D",(function(){return P})),n.d(t,"k",(function(){return F})),n.d(t,"F",(function(){return I})),n.d(t,"z",(function(){return B})),n.d(t,"G",(function(){return U})),n.d(t,"E",(function(){return V})),n.d(t,"i",(function(){return z})),n.d(t,"p",(function(){return G})),n.d(t,"Q",(function(){return W})),n.d(t,"P",(function(){return q}));var r="core/user",i="connected_url_mismatch",a="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",l="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",f="googlesitekit_read_shared_module_data",p="googlesitekit_manage_module_sharing_options",g="googlesitekit_delegate_module_sharing_management",m="googlesitekit_update_plugins",h="kmAnalyticsAdSenseTopEarningContent",b="kmAnalyticsEngagedTrafficSource",v="kmAnalyticsLeastEngagingPages",y="kmAnalyticsNewVisitors",E="kmAnalyticsPopularAuthors",_="kmAnalyticsPopularContent",O="kmAnalyticsPopularProducts",k="kmAnalyticsReturningVisitors",w="kmAnalyticsTopCities",j="kmAnalyticsTopCitiesDrivingLeads",S="kmAnalyticsTopCitiesDrivingAddToCart",x="kmAnalyticsTopCitiesDrivingPurchases",A="kmAnalyticsTopDeviceDrivingPurchases",N="kmAnalyticsTopConvertingTrafficSource",T="kmAnalyticsTopCountries",C="kmAnalyticsTopPagesDrivingLeads",D="kmAnalyticsTopRecentTrendingPages",R="kmAnalyticsTopTrafficSource",M="kmAnalyticsTopTrafficSourceDrivingAddToCart",L="kmAnalyticsTopTrafficSourceDrivingLeads",P="kmAnalyticsTopTrafficSourceDrivingPurchases",F="kmAnalyticsPagesPerVisit",I="kmAnalyticsVisitLength",B="kmAnalyticsTopReturningVisitorPages",U="kmSearchConsolePopularKeywords",V="kmAnalyticsVisitsPerVisitor",z="kmAnalyticsMostEngagingPages",G="kmAnalyticsTopCategories",W=[h,b,v,y,E,_,O,k,G,w,j,S,x,A,N,T,D,R,M,F,I,B,V,z,G],q=[].concat(W,[U])},70:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},72:function(e,t,n){"use strict";var r=n(15),i=n.n(r),a=n(265),o=n(1),c=n.n(o),s=n(0),l=n(144);function Portal(e){var t=e.children,n=e.slug,r=Object(s.useState)(document.createElement("div")),o=i()(r,1)[0];return Object(a.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(l.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},73:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),i=n(18),a=n(9);function o(e,t){var n=Object(i.a)(),o=null!=t?t:"".concat(n,"_").concat(e);return{view:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"view_notification"].concat(t))}),[o]),confirm:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"confirm_notification"].concat(t))}),[o]),dismiss:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"dismiss_notification"].concat(t))}),[o]),clickLearnMore:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"click_learn_more_link"].concat(t))}),[o])}}},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(33),i=n.n(r),a=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,n="object"===i()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(1),i=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,i=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:i}},n)}IconWrapper.propTypes={children:i.a.node.isRequired,marginLeft:i.a.number,marginRight:i.a.number}}).call(this,n(4))},776:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationWithSmallSVG}));var r=n(17),i=n(189);function NotificationWithSmallSVG(t){var n=t.title,a=t.description,o=t.actions,c=t.SmallImageSVG;return e.createElement(r.e,null,e.createElement(r.k,null,e.createElement(r.a,{size:1,className:"googlesitekit-publisher-win__small-media"},e.createElement(c,null)),e.createElement(r.a,{smSize:3,mdSize:7,lgSize:11,className:"googlesitekit-publisher-win__content"},e.createElement(i.a,{title:n}),a,o)))}}).call(this,n(4))},79:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(15),i=n.n(r),a=n(188),o=n(133),c={},s=void 0===e?null:e,l=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,u=void 0===r?0:r,d=e.initialHeight,f=void 0===d?0:d,p=Object(a.a)("undefined"==typeof document?[u,f]:l,t,n),g=i()(p,2),m=g[0],h=g[1],b=function(){return h(l)};return Object(o.a)(s,"resize",b),Object(o.a)(s,"orientationchange",b),m},d=function(e){return u(e)[0]}}).call(this,n(28))},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"s",(function(){return a})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return s})),n.d(t,"g",(function(){return l})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return f})),n.d(t,"k",(function(){return p})),n.d(t,"m",(function(){return g})),n.d(t,"n",(function(){return m})),n.d(t,"h",(function(){return h})),n.d(t,"x",(function(){return b})),n.d(t,"w",(function(){return v})),n.d(t,"y",(function(){return y})),n.d(t,"u",(function(){return E})),n.d(t,"v",(function(){return _})),n.d(t,"f",(function(){return O})),n.d(t,"l",(function(){return k})),n.d(t,"e",(function(){return w})),n.d(t,"t",(function(){return j})),n.d(t,"c",(function(){return S})),n.d(t,"d",(function(){return x})),n.d(t,"b",(function(){return A}));var r="modules/analytics-4",i="account_create",a="property_create",o="webdatastream_create",c="analyticsSetup",s=10,l=1,u="https://www.googleapis.com/auth/tagmanager.readonly",d="enhanced-measurement-form",f="enhanced-measurement-enabled",p="enhanced-measurement-should-dismiss-activation-banner",g="analyticsAccountCreate",m="analyticsCustomDimensionsCreate",h="https://www.googleapis.com/auth/analytics.edit",b="dashboardAllTrafficWidgetDimensionName",v="dashboardAllTrafficWidgetDimensionColor",y="dashboardAllTrafficWidgetDimensionValue",E="dashboardAllTrafficWidgetActiveRowIndex",_="dashboardAllTrafficWidgetLoaded",O={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},k={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},w=[k.CONTACT,k.GENERATE_LEAD,k.SUBMIT_LEAD_FORM],j={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},S="audiencePermissionsSetup",x="audienceTileCustomDimensionCreate",A="audience-selection-panel-expirable-new-badge-"},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(106);function i(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),i=e.replace(n.origin,"");if(i.length<t)return i;var a=i.length-Math.floor(t)+1;return"…"+i.substr(a)}},810:function(e,t,n){"use strict";n.d(t,"c",(function(){return _})),n.d(t,"b",(function(){return ae})),n.d(t,"a",(function(){return oe}));var r,i,a,o,c,s,l,u,d,f,p,g,m,h,b=n(41),v=n(3),y=n(984),E=Object(v.combineStores)(v.commonStore,y.a),_=function(e){e.registerStore(b.a,E)},O=n(6),k=n.n(O),w=n(5),j=n.n(w),S=n(16),x=n.n(S),A=n(409),N=n(22),T=n(205),C=n(29),D=n(13),R=n(7),M=n(23),L=n(19),P=n(8),F=n(376),I=n(66),B=n(46),U=n(986),V=n(987),z=n(988),G=n(989),W=n(992),q=n(994),H=n(995),K=n(996),$=n(997),Q=n(825),J=n(1004),X=n(580),Y=n(1005),Z=n(828),ee=n(9),te=n(1009),ne=n(1014),re=n(1017),ie=(r={"authentication-error":{Component:V.a,priority:T.c.ERROR_LOW,areaSlug:b.b.ERRORS,viewContexts:[N.n,N.o,N.l,N.m,N.r],checkRequirements:(h=x()(j.a.mark((function e(t){var n,r,i,a,o,c,s,l;return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.select,r=t.resolveSelect,e.next=3,Promise.all([r(D.c).getSiteInfo(),r(R.a).getAuthentication(),r(L.a).getModules()]);case 3:return i=n(D.c).getSetupErrorMessage(),a=n(R.a).isAuthenticated(),o=n(L.a).isModuleConnected("analytics-4"),c=n(R.a).hasScope(B.h),s=n(R.a).getUnsatisfiedScopes(),l=o&&!c&&1===(null==s?void 0:s.length),e.abrupt("return",(null==s?void 0:s.length)&&!i&&a&&!l);case 10:case"end":return e.stop()}}),e)}))),function(e){return h.apply(this,arguments)}),isDismissible:!1},"authentication-error-gte":{Component:z.a,priority:T.c.ERROR_LOW,areaSlug:b.b.ERRORS,viewContexts:[N.n,N.o,N.l,N.m,N.r],checkRequirements:(m=x()(j.a.mark((function e(t){var n,r,i,a,o,c,s;return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.select,r=t.resolveSelect,e.next=3,Promise.all([r(D.c).getSiteInfo(),r(R.a).getAuthentication(),r(L.a).getModules()]);case 3:return i=n(D.c).getSetupErrorMessage(),a=n(R.a).isAuthenticated(),o=n(L.a).isModuleConnected("analytics-4"),c=n(R.a).hasScope(B.h),s=o&&!c,e.abrupt("return",!i&&a&&s);case 9:case"end":return e.stop()}}),e)}))),function(e){return m.apply(this,arguments)}),isDismissible:!1},setup_error:{Component:H.a,priority:T.c.ERROR_HIGH,areaSlug:b.b.ERRORS,viewContexts:[N.s],checkRequirements:(g=x()(j.a.mark((function e(t){var n,r,i,a,o;return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.select,r=t.resolveSelect,e.next=3,r(D.c).getSiteInfo();case 3:if(i=n(D.c).getSetupErrorMessage(),a=n(C.a).getValue(R.d,"permissionsError")||{},o=a.data,i&&!(null==o?void 0:o.skipDefaultErrorNotifications)){e.next=7;break}return e.abrupt("return",!1);case 7:return e.abrupt("return",!0);case 8:case"end":return e.stop()}}),e)}))),function(e){return g.apply(this,arguments)}),isDismissible:!1},setup_plugin_error:{Component:K.a,priority:T.c.ERROR_HIGH,areaSlug:b.b.ERRORS,viewContexts:[N.n,N.o,N.l,N.m,N.r],checkRequirements:(p=x()(j.a.mark((function e(t){var n,r,i,a,o;return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.select,i=t.resolveSelect,e.next=3,i(D.c).getSiteInfo();case 3:if(!(null==(a=r(C.a).getValue(R.d,"permissionsError"))||null===(n=a.data)||void 0===n?void 0:n.skipDefaultErrorNotifications)){e.next=6;break}return e.abrupt("return",!1);case 6:return o=r(D.c).getSetupErrorMessage(),e.abrupt("return",!!o);case 8:case"end":return e.stop()}}),e)}))),function(e){return p.apply(this,arguments)}),isDismissible:!1},"auth-error":{Component:U.a,priority:T.c.ERROR_HIGH,areaSlug:b.b.ERRORS,viewContexts:[N.n,N.l,N.r],checkRequirements:function(e){return!!(0,e.select)(R.a).getAuthError()},isDismissible:!1},"top-earning-pages-success-notification":{Component:q.a,areaSlug:b.b.BANNERS_BELOW_NAV,viewContexts:[N.n,N.l],checkRequirements:(f=x()(j.a.mark((function e(t){var n,r,i,a,o,c,s,l,u,d;return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.select,r=t.resolveSelect,i=t.dispatch,e.next=3,r(L.a).isModuleConnected("adsense");case 3:return a=e.sent,e.next=6,r(L.a).isModuleConnected("analytics-4");case 6:if(o=e.sent,a&&o){e.next=9;break}return e.abrupt("return",!1);case 9:return e.next=11,r(P.r).getSettings();case 11:if(n(P.r).getAdSenseLinked()){e.next=14;break}return e.abrupt("return",!1);case 14:return c=n(R.a).getDateRangeDates({offsetDays:P.g}),s=c.startDate,l=c.endDate,u={startDate:s,endDate:l,dimensions:["pagePath"],metrics:[{name:"totalAdRevenue"}],orderby:[{metric:{metricName:"totalAdRevenue"},desc:!0}],limit:3},e.next=18,r(P.r).getReport(u);case 18:if(d=e.sent,!1!==Object(F.d)(d)){e.next=23;break}return e.next=22,i(b.a).dismissNotification("top-earning-pages-success-notification");case 22:return e.abrupt("return",!1);case 23:return e.abrupt("return",!0);case 24:case"end":return e.stop()}}),e)}))),function(e){return f.apply(this,arguments)}),isDismissible:!0},"setup-success-notification-site-kit":{Component:ne.a,areaSlug:b.b.BANNERS_BELOW_NAV,viewContexts:[N.n],checkRequirements:function(){var e=Object(A.a)(location.href,"notification"),t=Object(A.a)(location.href,"slug");return"authentication_success"===e&&!t}},"setup-success-notification-module":{Component:re.a,areaSlug:b.b.BANNERS_BELOW_NAV,viewContexts:[N.n],checkRequirements:(d=x()(j.a.mark((function e(t){var n,r,i,a,o;return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.select,r=t.resolveSelect,e.next=3,Promise.all([r(L.a).getModules()]);case 3:if(i=Object(A.a)(location.href,"notification"),a=Object(A.a)(location.href,"slug"),o=n(L.a).getModule(a),"authentication_success"!==i||!1!==o.overrideSetupSuccessNotification||!o.active){e.next=8;break}return e.abrupt("return",!0);case 8:return e.abrupt("return",!1);case 9:case"end":return e.stop()}}),e)}))),function(e){return d.apply(this,arguments)})}},k()(r,Z.a,{Component:Z.b,priority:T.c.SETUP_CTA_LOW,areaSlug:b.b.BANNERS_BELOW_NAV,groupID:b.c.SETUP_CTAS,viewContexts:[N.n,N.o],checkRequirements:(u=x()(j.a.mark((function e(t){var n,r,i,a,o,c,s,l,u,d;return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.select,r=t.resolveSelect,i=t.dispatch,e.next=3,Promise.all([r(R.a).getCapabilities(),r(D.c).getSiteInfo()]);case 3:if(a=Object(A.a)(location.href,"notification"),o=Object(A.a)(location.href,"slug"),c=i(b.a),s=c.dismissNotification,"authentication_success"!==a||o){e.next=10;break}return e.next=9,s("auto-update-cta",{expiresInSeconds:10*ee.e});case 9:return e.abrupt("return",!1);case 10:if(l=n(R.a).hasCapability(R.N),u=n(D.c).hasChangePluginAutoUpdatesCapacity(),d=n(D.c).getSiteKitAutoUpdatesEnabled(),!l||!u||d){e.next=15;break}return e.abrupt("return",!0);case 15:return e.abrupt("return",!1);case 16:case"end":return e.stop()}}),e)}))),function(e){return u.apply(this,arguments)}),isDismissible:!0}),k()(r,"gathering-data-notification",{Component:G.a,priority:T.c.INFO,areaSlug:b.b.BANNERS_BELOW_NAV,viewContexts:[N.n,N.o,N.l,N.m],checkRequirements:(l=x()(j.a.mark((function e(t,n){var r,i,a,o,c,s,l,u,d,f;return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.select,i=t.resolveSelect,a=N.g.includes(n),e.next=4,Promise.all([i(L.a).getModules(),a?i(L.a).getRecoverableModules():Promise.resolve([])]);case 4:return o=r(L.a).isModuleConnected("analytics-4"),c=!a||r(R.a).canViewSharedModule("analytics-4"),s=!a||r(R.a).canViewSharedModule("search-console"),e.next=9,function(){if(!a)return!1;var e=r(L.a).getRecoverableModules();return Object.keys(e).includes("analytics-4")}();case 9:return l=e.sent,e.next=12,function(){if(!a)return!1;var e=r(L.a).getRecoverableModules();return Object.keys(e).includes("search-console")}();case 12:if(u=e.sent,!o||!c||!1!==l){e.next=19;break}return e.next=16,i(P.r).isGatheringData();case 16:e.t0=e.sent,e.next=20;break;case 19:e.t0=!1;case 20:if(d=e.t0,e.t1=s&&!1===u,!e.t1){e.next=26;break}return e.next=25,i(I.b).isGatheringData();case 25:e.t1=e.sent;case 26:return f=e.t1,e.abrupt("return",d||f);case 28:case"end":return e.stop()}}),e)}))),function(e,t){return l.apply(this,arguments)}),isDismissible:!0}),k()(r,"zero-data-notification",{Component:W.a,priority:T.c.INFO,areaSlug:b.b.BANNERS_BELOW_NAV,viewContexts:[N.n,N.o,N.l,N.m],checkRequirements:(s=x()(j.a.mark((function e(t,n){var r,i,a,o,c,s;return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.select,i=t.resolveSelect,a=N.g.includes(n),e.next=4,Promise.all([i(L.a).getModules(),a?i(L.a).getRecoverableModules():Promise.resolve([])]);case 4:return o=function(){var e=x()(j.a.mark((function e(t,n){return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r(L.a).isModuleConnected(t)){e.next=3;break}return e.abrupt("return","disconnected");case 3:if(!a){e.next=10;break}if(r(R.a).canViewSharedModule(t)){e.next=7;break}return e.abrupt("return","cant-view");case 7:if(!r(L.a).getRecoverableModules()[t]){e.next=10;break}return e.abrupt("return","recovering");case 10:return e.next=12,i(n).isGatheringData();case 12:if(!e.sent){e.next=15;break}return e.abrupt("return","gathering");case 15:return e.next=17,i(n).getReport(r(n).getSampleReportArgs());case 17:if(!r(n).hasZeroData()){e.next=19;break}return e.abrupt("return","zero-data");case 19:return e.abrupt("return","connected");case 20:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),e.next=7,o("analytics-4",P.r);case 7:return c=e.sent,e.next=10,o("search-console",I.b);case 10:if(s=e.sent,"gathering"!==c&&"gathering"!==s){e.next=13;break}return e.abrupt("return",!1);case 13:return e.abrupt("return","zero-data"===c||"zero-data"===s);case 14:case"end":return e.stop()}}),e)}))),function(e,t){return s.apply(this,arguments)}),isDismissible:!0}),k()(r,"module-recovery-alert",{Component:te.a,priority:T.c.ERROR_LOW,areaSlug:b.b.BANNERS_BELOW_NAV,viewContexts:[N.n],isDismissible:!1,checkRequirements:(c=x()(j.a.mark((function e(t){var n,r;return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.resolveSelect,e.next=3,n(L.a).getRecoverableModules();case 3:if(r=e.sent,Object.keys(r||{}).length){e.next=7;break}return e.abrupt("return",!1);case 7:return e.abrupt("return",!0);case 8:case"end":return e.stop()}}),e)}))),function(e){return c.apply(this,arguments)})}),k()(r,X.a,{Component:Y.a,priority:T.c.SETUP_CTA_HIGH,areaSlug:b.b.BANNERS_BELOW_NAV,groupID:b.c.SETUP_CTAS,viewContexts:[N.n],isDismissible:!0,checkRequirements:(o=x()(j.a.mark((function e(t){var n,r;return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.select,r=t.resolveSelect,e.next=3,r(D.c).getConsentModeSettings();case 3:if(!1===n(D.c).isConsentModeEnabled()){e.next=6;break}return e.abrupt("return",!1);case 6:return e.abrupt("return",r(D.c).isAdsConnected());case 7:case"end":return e.stop()}}),e)}))),function(e){return o.apply(this,arguments)}),dismissRetries:2}),k()(r,T.b,{Component:Q.b,priority:T.c.SETUP_CTA_LOW,areaSlug:b.b.BANNERS_BELOW_NAV,groupID:b.c.SETUP_CTAS,viewContexts:[N.n],checkRequirements:(a=x()(j.a.mark((function e(t){var n,r,i,a,o,c,s,l,u;return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.select,r=t.resolveSelect,i=t.dispatch,n(D.c).isAnyFirstPartyModeModuleConnected()){e.next=4;break}return e.abrupt("return",!1);case 4:return e.next=6,r(D.c).getFirstPartyModeSettings();case 6:if(a=n(D.c),o=a.isFirstPartyModeEnabled,c=a.isFPMHealthy,s=a.isScriptAccessEnabled,!o()){e.next=9;break}return e.abrupt("return",!1);case 9:if(l=c(),u=s(),![l,u].includes(null)){e.next=14;break}return i(D.c).fetchGetFPMServerRequirementStatus(),e.abrupt("return",!1);case 14:return e.abrupt("return",l&&u);case 15:case"end":return e.stop()}}),e)}))),function(e){return a.apply(this,arguments)}),isDismissible:!0,featureFlag:"firstPartyMode"}),k()(r,T.a,{Component:$.a,areaSlug:b.b.BANNERS_BELOW_NAV,viewContexts:[N.n],checkRequirements:(i=x()(j.a.mark((function e(t){var n,r,i,a,o,c;return j.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.select,r=t.resolveSelect,n(D.c).isAnyFirstPartyModeModuleConnected()){e.next=4;break}return e.abrupt("return",!1);case 4:return e.next=6,r(D.c).getFirstPartyModeSettings();case 6:return i=n(D.c),a=i.isFirstPartyModeEnabled,o=i.isFPMHealthy,c=i.isScriptAccessEnabled,e.abrupt("return",a()&&(!o()||!c()));case 8:case"end":return e.stop()}}),e)}))),function(e){return i.apply(this,arguments)}),isDismissible:!0,featureFlag:"firstPartyMode"}),k()(r,"setup-success-notification-fpm",{Component:J.a,areaSlug:b.b.BANNERS_BELOW_NAV,viewContexts:[N.n],isDismissible:!1,checkRequirements:function(e){return!!(0,e.select)(M.b).getValue(Q.a)},featureFlag:"firstPartyMode"}),r);function ae(e){for(var t in ie)e.registerNotification(t,ie[t])}function oe(e){var t=e.dispatch;return{registerNotification:function(e,n){t(b.a).registerNotification(e,n)}}}},825:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return x})),n.d(t,"b",(function(){return FirstPartyModeSetupBanner}));var r=n(6),i=n.n(r),a=n(5),o=n.n(a),c=n(16),s=n.n(c),l=n(0),u=n(2),d=n(3),f=n(175),p=n(41),g=n(13),m=n(7),h=n(23),b=n(111),v=n(180),y=n(242),E=n(179),_=n(1001),O=n(1002),k=n(1003),w=n(24),j=n(18),S=n(9),x="fpm-show-setup-success-notification";function FirstPartyModeSetupBanner(t){var n,r=t.id,a=t.Notification,c=Object(w.e)(),A=Object(j.a)(),N=Object(d.useDispatch)(g.c),T=N.setFirstPartyModeEnabled,C=N.saveFirstPartyModeSettings,D={tooltipSlug:r,content:Object(u.__)("You can always enable First-party mode in Settings later","google-site-kit"),dismissLabel:Object(u.__)("Got it","google-site-kit")},R=Object(f.b)(D),M=Object(d.useSelect)((function(e){return e(g.c).isUsingProxy()})),L=Object(d.useDispatch)(p.a).invalidateResolution,P=Object(d.useDispatch)(h.b).setValue,F=Object(d.useSelect)((function(e){return e(g.c).getDocumentationLinkURL("first-party-mode-introduction")})),I=function(){var e=s()(o.a.mark((function e(){var t,n;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return T(!0),e.next=3,C();case 3:if(t=e.sent,!(n=t.error)){e.next=7;break}return e.abrupt("return",{error:n});case 7:P(x,!0),L("getQueuedNotifications",[A,p.c.DEFAULT]);case 9:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),B=Object(d.useDispatch)(m.a).triggerSurvey,U=Object(l.useCallback)((function(){M&&B("view_fpm_setup_cta",{ttl:S.a})}),[B,M]),V=(n={},i()(n,w.b,k.a),i()(n,w.c,O.a),n);return e.createElement(a,{onView:U},e.createElement(y.a,{id:r,title:Object(u.__)("Get more comprehensive stats by collecting metrics via your own site","google-site-kit"),description:e.createElement(b.a,{text:Object(u.__)("Enable First-party mode (<em>beta</em>) to send measurement through your own domain - this helps improve the quality and completeness of Analytics or Ads metrics.","google-site-kit"),learnMoreLink:e.createElement(v.a,{id:r,label:Object(u.__)("Learn more","google-site-kit"),url:F})}),actions:e.createElement(E.a,{id:r,className:"googlesitekit-setup-cta-banner__actions-wrapper",ctaLabel:Object(u.__)("Enable First-party mode","google-site-kit"),onCTAClick:I,dismissLabel:Object(u.__)("Maybe later","google-site-kit"),onDismiss:R}),SVG:V[c]||_.a}))}}).call(this,n(4))},828:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return E})),n.d(t,"b",(function(){return EnableAutoUpdateBannerNotification}));var r=n(15),i=n.n(r),a=n(5),o=n.n(a),c=n(16),s=n.n(c),l=n(1),u=n.n(l),d=n(2),f=n(0),p=n(3),g=n(13),m=n(18),h=n(547),b=n(111),v=n(179),y=n(92),E="auto-update-cta";function EnableAutoUpdateBannerNotification(t){var n=t.id,r=t.Notification,a=Object(m.a)(),c=Object(p.useSelect)((function(e){return e(g.c).getSiteKitAutoUpdatesEnabled()})),l=Object(p.useSelect)((function(e){return e(g.c).getErrorForAction("enableAutoUpdate",[])})),u=Object(p.useDispatch)(g.c).enableAutoUpdate,_=Object(f.useCallback)(s()(o.a.mark((function e(){return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,u();case 2:case"end":return e.stop()}}),e)}))),[u]),O=Object(f.useState)(!1),k=i()(O,2),w=k[0],j=k[1];if(Object(f.useEffect)((function(){!1===w&&!0===c&&j(!0)}),[w,j,c]),w){var S={category:"".concat(a,"_").concat(E,"-success")};return e.createElement(r,{className:"googlesitekit-publisher-win"},e.createElement(h.a,{title:Object(d.__)("Thanks for enabling auto-updates","google-site-kit"),description:e.createElement(b.a,{text:Object(d.__)("Auto-updates have been enabled. Your version of Site Kit will automatically be updated when new versions are available.","google-site-kit"),errorText:null==l?void 0:l.message}),actions:e.createElement(y.a,{id:n,dismissLabel:Object(d.__)("Dismiss","google-site-kit"),gaTrackingEventArgs:S,dismissExpires:1})}))}return e.createElement(r,{className:"googlesitekit-publisher-win"},e.createElement(h.a,{title:Object(d.__)("Keep Site Kit up-to-date","google-site-kit"),description:e.createElement(b.a,{text:Object(d.__)("Turn on auto-updates so you always have the latest version of Site Kit. We constantly introduce new features to help you get the insights you need to be successful on the web.","google-site-kit"),errorText:null==l?void 0:l.message}),actions:e.createElement(v.a,{id:n,ctaLabel:Object(d.__)("Enable auto-updates","google-site-kit"),dismissOnCTAClick:!1,onCTAClick:_,dismissLabel:Object(d.__)("Dismiss","google-site-kit")})}))}EnableAutoUpdateBannerNotification.propTypes={id:u.a.string.isRequired,Notification:u.a.elementType.isRequired}}).call(this,n(4))},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return k})),n.d(t,"d",(function(){return w})),n.d(t,"e",(function(){return S})),n.d(t,"c",(function(){return x})),n.d(t,"b",(function(){return A}));var r=n(15),i=n.n(r),a=n(33),o=n.n(a),c=n(6),s=n.n(c),l=n(25),u=n.n(l),d=n(14),f=n(63),p=n.n(f),g=n(2);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var b=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=E(e,t),r=n.formatUnit,i=n.formatDecimal;try{return r()}catch(e){return i()}},v=function(e){var t=y(e),n=t.hours,r=t.minutes,i=t.seconds;return i=("0"+i).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(i):"".concat(n,":").concat(r,":").concat(i)},y=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},E=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=y(e),r=n.hours,i=n.minutes,a=n.seconds;return{hours:r,minutes:i,seconds:a,formatUnit:function(){var n=t.unitDisplay,o=h(h({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?S(a,h(h({},o),{},{unit:"second"})):Object(g.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(g._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?S(a,h(h({},o),{},{unit:"second"})):"",i?S(i,h(h({},o),{},{unit:"minute"})):"",r?S(r,h(h({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(g.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(g.__)("%ds","google-site-kit"),a);if(0===e)return t;var n=Object(g.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(g.__)("%dm","google-site-kit"),i),o=Object(g.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(g.__)("%dh","google-site-kit"),r);return Object(g.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(g._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",i?n:"",r?o:"").trim()}}},_=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},O=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(g.sprintf)(// translators: %s: an abbreviated number in millions.
Object(g.__)("%sM","google-site-kit"),S(_(e),e%10==0?{}:t)):1e4<=e?Object(g.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(g.__)("%sK","google-site-kit"),S(_(e))):1e3<=e?Object(g.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(g.__)("%sK","google-site-kit"),S(_(e),e%10==0?{}:t)):S(e,{signDisplay:"never",maximumFractionDigits:1})};function k(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=h({},e)),t}function w(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=k(t),r=n.style,i=void 0===r?"metric":r;return"metric"===i?O(e):"duration"===i?b(e,n):"durationISO"===i?v(e):S(e,n)}var j=p()(console.warn),S=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?A():n,a=u()(t,["locale"]);try{return new Intl.NumberFormat(r,a).format(e)}catch(t){j("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],l={},d=0,f=Object.entries(a);d<f.length;d++){var p=i()(f[d],2),g=p[0],m=p[1];c[g]&&m===c[g]||(s.includes(g)||(l[g]=m))}try{return new Intl.NumberFormat(r,l).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},x=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?A():n,i=t.style,a=void 0===i?"long":i,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(r,{style:a,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var l=Object(g.__)(", ","google-site-kit");return e.join(l)},A=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i}));var r=n(149),i=n.n(r)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a);function ChangeArrow(t){var n=t.direction,r=t.invertColor,i=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:i,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:i.a.string,invertColor:i.a.bool,width:i.a.number,height:i.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(12),i=n.n(r),a=function(e,t){var n=t.dateRangeLength;i()(Array.isArray(e),"report must be an array to partition."),i()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return i.b})),n.d(t,"J",(function(){return i.c})),n.d(t,"F",(function(){return a.a})),n.d(t,"K",(function(){return a.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return m})),n.d(t,"j",(function(){return h})),n.d(t,"i",(function(){return b})),n.d(t,"d",(function(){return k})),n.d(t,"c",(function(){return w})),n.d(t,"e",(function(){return j})),n.d(t,"b",(function(){return S})),n.d(t,"a",(function(){return x})),n.d(t,"f",(function(){return A})),n.d(t,"n",(function(){return N})),n.d(t,"w",(function(){return T})),n.d(t,"p",(function(){return C})),n.d(t,"G",(function(){return D})),n.d(t,"s",(function(){return R})),n.d(t,"v",(function(){return M})),n.d(t,"k",(function(){return L})),n.d(t,"o",(function(){return P.b})),n.d(t,"h",(function(){return P.a})),n.d(t,"t",(function(){return F.b})),n.d(t,"q",(function(){return F.a})),n.d(t,"A",(function(){return F.c})),n.d(t,"x",(function(){return I})),n.d(t,"u",(function(){return B})),n.d(t,"E",(function(){return z})),n.d(t,"D",(function(){return G.a})),n.d(t,"g",(function(){return W})),n.d(t,"L",(function(){return q})),n.d(t,"l",(function(){return H}));var r=n(14),i=n(36),a=n(75),o=n(33),c=n.n(o),s=n(96),l=n.n(s),u=function(e){return l()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var i=t[r];i&&"object"===c()(i)&&!Array.isArray(i)&&(i=e(i)),n[r]=i})),n}(e)))};n(97);var d=n(83);function f(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function p(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function g(e){return e.replace(/\n/gi,"<br>")}function m(e){for(var t=e,n=0,r=[f,p,g];n<r.length;n++){t=(0,r[n])(t)}return t}var h=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},b=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},v=n(15),y=n.n(v),E=n(12),_=n.n(E),O=n(2),k="Invalid dateString parameter, it must be a string.",w='Invalid date range, it must be a string with the format "last-x-days".',j=60,S=60*j,x=24*S,A=7*x;function N(){var e=function(e){return Object(O.sprintf)(
/* translators: %s: number of days */
Object(O._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function T(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function C(e){_()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function D(e){_()(T(e),k);var t=e.split("-"),n=y()(t,3),r=n[0],i=n[1],a=n[2];return new Date(r,i-1,a)}function R(e,t){return C(L(e,t*x))}function M(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function L(e,t){_()(T(e)||Object(r.isDate)(e)&&!isNaN(e),k);var n=T(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var P=n(98),F=n(80);function I(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function B(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var U=n(27),V=n.n(U),z=function(e){return Array.isArray(e)?V()(e).sort():e},G=n(89);function W(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var q=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},H=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},90:function(e,t,n){"use strict";(function(e,r){n.d(t,"c",(function(){return v})),n.d(t,"b",(function(){return E})),n.d(t,"a",(function(){return TourTooltips}));var i=n(6),a=n.n(i),o=n(81),c=n(30),s=n(1),l=n.n(s),u=n(2),d=n(3),f=n(23),p=n(7),g=n(36),m=n(107),h=n(18);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var v={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},y={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},E={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},_="feature_tooltip_view",O="feature_tooltip_advance",k="feature_tooltip_return",w="feature_tooltip_dismiss",j="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,i=t.tourID,s=t.gaEventCategory,l=t.callback,u="".concat(i,"-step"),S="".concat(i,"-run"),x=Object(d.useDispatch)(f.b).setValue,A=Object(d.useDispatch)(p.a).dismissTour,N=Object(d.useRegistry)(),T=Object(h.a)(),C=Object(d.useSelect)((function(e){return e(f.b).getValue(u)})),D=Object(d.useSelect)((function(e){return e(f.b).getValue(S)&&!1===e(p.a).isTourDismissed(i)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),x(S,!0)}));var R=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return r.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,r=e.lifecycle,i=e.size,a=e.status,o=e.type,l=t+1,u="function"==typeof s?s(T):s;o===c.b.TOOLTIP&&r===c.c.TOOLTIP?Object(g.b)(u,_,l):n===c.a.CLOSE&&r===c.c.COMPLETE?Object(g.b)(u,w,l):n===c.a.NEXT&&a===c.d.FINISHED&&o===c.b.TOUR_END&&i===l&&Object(g.b)(u,j,l),r===c.c.COMPLETE&&a!==c.d.FINISHED&&(n===c.a.PREV&&Object(g.b)(u,k,l),n===c.a.NEXT&&Object(g.b)(u,O,l))}(t);var n=t.action,r=t.index,a=t.status,o=t.step,d=t.type,f=n===c.a.CLOSE,p=!f&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),m=[c.d.FINISHED,c.d.SKIPPED].includes(a),h=f&&d===c.b.STEP_AFTER,b=m||h;if(c.b.STEP_BEFORE===d){var v,y,E=o.target;"string"==typeof o.target&&(E=e.document.querySelector(o.target)),null===(v=E)||void 0===v||null===(y=v.scrollIntoView)||void 0===y||y.call(v,{block:"center"})}p?function(e,t){x(u,e+(t===c.a.PREV?-1:1))}(r,n):b&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),A(i)),l&&l(t,N)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:E,locale:y,run:D,showProgress:!0,stepIndex:C,steps:R,styles:v,tooltipComponent:m.a})}TourTooltips.propTypes={steps:l.a.arrayOf(l.a.object).isRequired,tourID:l.a.string.isRequired,gaEventCategory:l.a.oneOfType([l.a.string,l.a.func]).isRequired,callback:l.a.func}}).call(this,n(28),n(4))},92:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Dismiss}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(1),u=n.n(l),d=n(2),f=n(3),p=n(73),g=n(41),m=n(10);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Dismiss(t){var n=t.id,r=t.primary,a=void 0===r||r,o=t.dismissLabel,c=void 0===o?Object(d.__)("OK, Got it!","google-site-kit"):o,l=t.dismissExpires,u=void 0===l?0:l,h=t.disabled,v=t.onDismiss,y=void 0===v?function(){}:v,E=t.gaTrackingEventArgs,_=t.dismissOptions,O=Object(p.a)(n,null==E?void 0:E.category),k=Object(f.useDispatch)(g.a).dismissNotification,w=function(){var e=s()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==y?void 0:y(t);case 2:O.dismiss(null==E?void 0:E.label,null==E?void 0:E.value),k(n,b(b({},_),{},{expiresInSeconds:u}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(m.Button,{tertiary:!a,onClick:w,disabled:h},c)}Dismiss.propTypes={id:u.a.string,primary:u.a.bool,dismissLabel:u.a.string,dismissExpires:u.a.number,disabled:u.a.bool,onDismiss:u.a.func,gaTrackingEventArgs:u.a.shape({label:u.a.string,value:u.a.string})}}).call(this,n(4))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o}));var r=n(239),i=n(85),a=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=n.invertColor,o=void 0!==a&&a;return Object(r.a)(e.createElement(i.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},984:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(25),o=n.n(a),c=n(16),s=n.n(c),l=n(5),u=n.n(l),d=n(12),f=n.n(d),p=n(3),g=n(176),m=n(41),h=n(7),b=n(62),v=n(985),y=n(57);function E(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?E(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):E(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var O={registerNotification:function(e,t){var n=t.Component,r=t.priority,i=void 0===r?10:r,a=t.areaSlug,o=t.groupID,c=void 0===o?m.c.DEFAULT:o,s=t.viewContexts,l=t.checkRequirements,u=t.isDismissible,d=t.dismissRetries,p=void 0===d?0:d,g=t.featureFlag,h=void 0===g?"":g;f()(n,"Component is required to register a notification.");var b=Object.values(m.b);return f()(b.includes(a),"Notification area should be one of: ".concat(b.join(", "),', but "').concat(a,'" was provided.')),f()(Array.isArray(s)&&s.some(m.d.includes,m.d),"Notification view context should be one of: ".concat(m.d.join(", "),', but "').concat(s,'" was provided.')),{payload:{id:e,settings:{Component:n,priority:i,areaSlug:a,groupID:c,viewContexts:s,checkRequirements:l,isDismissible:u,dismissRetries:p,featureFlag:h}},type:"REGISTER_NOTIFICATION"}},receiveQueuedNotifications:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:m.c.DEFAULT;return{payload:{queuedNotifications:e,groupID:t},type:"RECEIVE_QUEUED_NOTIFICATIONS"}},resetQueue:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m.c.DEFAULT;return{type:"RESET_QUEUE",payload:{groupID:e}}},populateQueue:u.a.mark((function e(t){var n,r=arguments;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:m.c.DEFAULT,e.next=3,{type:"POPULATE_QUEUE",payload:{viewContext:t,groupID:n}};case 3:case"end":return e.stop()}}),e)})),queueNotification:function(e){return{payload:{notification:e},type:"QUEUE_NOTIFICATION"}},dismissNotification:Object(b.f)((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};f()(e,"A notification id is required to dismiss a notification.");var n=t.expiresInSeconds,r=void 0===n?0:n;f()(Number.isInteger(r),"expiresInSeconds must be an integer.")}),u.a.mark((function e(t){var n,r,i,a,o,c,s,l=arguments;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=l.length>1&&void 0!==l[1]?l[1]:{},r=n.expiresInSeconds,i=void 0===r?0:r,e.next=4,p.commonActions.getRegistry();case 4:if(a=e.sent,n.skipHidingFromQueue){e.next=8;break}return e.next=8,{type:"DISMISS_NOTIFICATION",payload:{id:t}};case 8:if(!0===(o=a.select(m.a).getNotification(t)).isDismissible){e.next=11;break}return e.abrupt("return");case 11:if(!(o.dismissRetries>0)){e.next=17;break}return c=a.select(h.a).getPromptDismissCount(t),s=c<o.dismissRetries?i:0,e.next=16,p.commonActions.await(a.dispatch(h.a).dismissPrompt(t,{expiresInSeconds:s}));case 16:return e.abrupt("return",e.sent);case 17:return e.next=19,p.commonActions.await(a.dispatch(h.a).dismissItem(t,{expiresInSeconds:i}));case 19:return e.abrupt("return",e.sent);case 20:case"end":return e.stop()}}),e)})))},k=i()({},"POPULATE_QUEUE",Object(p.createRegistryControl)((function(e){return function(){var t=s()(u.a.mark((function t(n){var r,i,a,c,l,d,f,p,g,b;return u.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=n.payload,i=r.viewContext,a=r.groupID,c=e.select(m.a),l=c.isNotificationDismissed,d=e.select(m.a).getNotifications(),t.next=6,Promise.all([e.resolveSelect(h.a).getDismissedItems(),e.resolveSelect(h.a).getDismissedPrompts()]);case 6:f=Object.values(d).filter((function(e){return!(null==e?void 0:e.featureFlag)||Object(y.b)(e.featureFlag)})).filter((function(e){return e.groupID===a})).filter((function(e){return e.viewContexts.includes(i)})).filter((function(e){var t=e.isDismissible,n=e.id;return!t||!l(n)})).map((function(t){var n=t.checkRequirements;return _(_({},o()(t,["checkRequirements"])),{},{checkRequirements:n,check:function(){return s()(u.a.mark((function t(){return u.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!n){t.next=4;break}return t.next=3,n(e);case 3:return t.abrupt("return",t.sent);case 4:return t.abrupt("return",!0);case 5:case"end":return t.stop()}}),t)})))()}})})),p=e.dispatch(m.a),g=p.queueNotification;case 8:return t.next=10,Object(v.a)(f);case 10:(b=t.sent)&&(g(b),f=f.filter((function(e){return e!==b})));case 12:if(b){t.next=8;break}case 13:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}))),w=Object(g.a)((function(t,n){var r=n.type,i=n.payload;switch(r){case"REGISTER_NOTIFICATION":var a=i.id,o=i.settings;void 0!==t.notifications[a]?e.console.warn('Could not register notification with ID "'.concat(a,'". Notification "').concat(a,'" is already registered.')):t.notifications[a]=_(_({},o),{},{id:a});break;case"RECEIVE_QUEUED_NOTIFICATIONS":t.queuedNotifications[i.groupID]=i.queuedNotifications;break;case"RESET_QUEUE":t.queuedNotifications[i.groupID]=[];break;case"QUEUE_NOTIFICATION":var c=i.notification.groupID;t.queuedNotifications[c]=t.queuedNotifications[c]||[],t.queuedNotifications[c].push(i.notification);break;case"DISMISS_NOTIFICATION":var s,l,u,d=i.id,f=null===(s=t.notifications)||void 0===s||null===(l=s[d])||void 0===l?void 0:l.groupID,p=null===(u=t.queuedNotifications[f])||void 0===u?void 0:u.findIndex((function(e){return e.id===d}));p>=0&&t.queuedNotifications[f].splice(p,1)}})),j={getQueuedNotifications:u.a.mark((function e(t){var n,r=arguments;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:m.c.DEFAULT,e.next=3,O.resetQueue(n);case 3:return e.next=5,O.populateQueue(t,n);case 5:case"end":return e.stop()}}),e)}))},S={getNotifications:function(e){return e.notifications},getNotification:function(e,t){return e.notifications[t]},getQueuedNotifications:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:m.c.DEFAULT;return f()(t,"viewContext is required."),e.queuedNotifications[n]},isNotificationDismissed:Object(p.createRegistrySelector)((function(e){return function(t,n){var r=e(m.a).getNotification(n);if(void 0!==r)return r.dismissRetries>0?e(h.a).isPromptDismissed(n):e(h.a).isItemDismissed(n)}})),isNotificationDismissalFinal:Object(p.createRegistrySelector)((function(e){return function(t,n){var r=e(m.a).getNotification(n);if(void 0!==r)return f()(r.isDismissible,"Notification should be dismissible to check if a notification is on its final dismissal."),0===r.dismissRetries||e(h.a).getPromptDismissCount(n)>=r.dismissRetries}}))};t.a={initialState:{notifications:{},queuedNotifications:{}},actions:O,controls:k,reducer:w,resolvers:j,selectors:S}}).call(this,n(28))},985:function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var r=n(5),i=n.n(r),a=n(27),o=n.n(a),c=n(16),s=n.n(c);function l(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return u(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return u(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function d(e){return f.apply(this,arguments)}function f(){return(f=s()(i.a.mark((function e(t){var n,r,a,c,s,u,d,f,g;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=t.reduce((function(e,t){var n=parseInt(t.priority,10);return e[n]=e[n]||[],e[n].push(t.check().then((function(e){return{task:t,result:!!e}})).catch((function(){return{result:!1}}))),e}),{}),r=Object.keys(n).sort((function(e,t){return e-t})),a=l(r),e.prev=3,a.s();case 5:if((c=a.n()).done){e.next=33;break}s=c.value,u=o()(n[s]);case 8:return e.next=10,Promise.race(u);case 10:if(!(d=e.sent).result){e.next=13;break}return e.abrupt("return",d.task);case 13:e.t0=i.a.keys(u);case 14:if((e.t1=e.t0()).done){e.next=29;break}return f=e.t1.value,g=u[f],e.next=19,p(g);case 19:if(e.t2=e.sent,!e.t2){e.next=25;break}return e.next=23,g;case 23:e.t3=e.sent.result,e.t2=!1===e.t3;case 25:if(!e.t2){e.next=27;break}delete u[f];case 27:e.next=14;break;case 29:u=u.filter((function(e){return e}));case 30:if(u.length){e.next=8;break}case 31:e.next=5;break;case 33:e.next=38;break;case 35:e.prev=35,e.t4=e.catch(3),a.e(e.t4);case 38:return e.prev=38,a.f(),e.finish(38);case 41:return e.abrupt("return",null);case 42:case"end":return e.stop()}}),e,null,[[3,35,38,41]])})))).apply(this,arguments)}function p(e){return g.apply(this,arguments)}function g(){return(g=s()(i.a.mark((function e(t){var n;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n="SECRETVALUETHATNOPROMISESHOULDEVERRESOLVETO",t instanceof Promise||(t=Promise.resolve(t)),e.t0=n,e.next=5,Promise.race([t,Promise.resolve(n)]);case 5:return e.t1=e.sent,e.abrupt("return",e.t0!==e.t1);case 7:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},986:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AuthError}));var r=n(2),i=n(3),a=n(7),o=n(196),c=n(187),s=n(111);function AuthError(t){var n=t.id,l=t.Notification,u=Object(i.useSelect)((function(e){return e(a.a).getAuthError()}));return e.createElement(l,{className:"googlesitekit-publisher-win googlesitekit-publisher-win--win-error"},e.createElement(o.a,{title:Object(r.__)("Site Kit can’t access necessary data","google-site-kit"),description:e.createElement(s.a,{text:u.message}),actions:e.createElement(c.a,{id:n,ctaLabel:Object(r.__)("Redo the plugin setup","google-site-kit"),ctaLink:u.data.reconnectURL})}))}}).call(this,n(4))},987:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return UnsatisfiedScopesAlert}));var i=n(5),a=n.n(i),o=n(16),c=n.n(o),s=n(15),l=n.n(s),u=n(14),d=n(81),f=n(0),p=n(2),g=n(3),m=n(9),h=n(37),b=n(29),v=n(32),y=n(19),E=n(13),_=n(7),O=n(196),k=n(111),w=n(187),j={siteverification:"site-verification",webmasters:"search-console",analytics:"analytics-4"};function UnsatisfiedScopesAlert(t){var n,i,o=t.id,s=t.Notification,S=Object(f.useRef)(),x=Object(f.useState)(!1),A=l()(x,2),N=A[0],T=A[1],C=Object(g.useSelect)((function(e){return e(v.a).isNavigatingTo(new RegExp("//oauth2|action=googlesitekit_connect","i"))})),D=Object(g.useSelect)((function(e){return e(b.a).getValue(_.d,"permissionsError")})),R=Object(g.useSelect)((function(e){return e(_.a).getUnsatisfiedScopes()})),M=(null==D?void 0:D.data)?{additionalScopes:null===(n=D.data)||void 0===n?void 0:n.scopes,redirectURL:(null===(i=D.data)||void 0===i?void 0:i.redirectURL)||e.location.href}:{redirectURL:e.location.href},L=Object(g.useSelect)((function(e){return e(_.a).getConnectURL(M)})),P=Object(g.useSelect)((function(e){return e(y.a).getModules()})),F=Object(g.useDispatch)(y.a).activateModule,I=Object(g.useDispatch)(v.a).navigateTo,B=Object(g.useDispatch)(E.c).setInternalServerError;Object(d.a)(c()(a.a.mark((function e(){var t,n,r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(h.d)("module_setup");case 2:t=e.sent,n=t.cacheHit,r=t.value,n&&T(r);case 6:case"end":return e.stop()}}),e)}))));var U,V,z,G=Object(f.useCallback)(c()(a.a.mark((function e(){var t,n,r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(S.current=!0,N){e.next=3;break}return e.abrupt("return");case 3:return e.next=5,F(N);case 5:t=e.sent,n=t.error,r=t.response,n?B({id:"activate-module-error",description:n.message}):I(r.moduleReauthURL);case 9:case"end":return e.stop()}}),e)}))),[F,N,I,B]);if(C&&!S.current||!(null==R?void 0:R.length)||void 0===L)return null;U=R.some((function(e){return!e.match(new RegExp("^https://www\\.googleapis\\.com/auth/"))}))||!(V=function(e,t){return void 0===t?null:e.map((function(e){return e.match(new RegExp("^https://www\\.googleapis\\.com/auth/([a-z]+)"))})).map((function(e){var t=l()(e,2)[1];return j[t]||t})).map((function(e){var n;return(null===(n=t[e])||void 0===n?void 0:n.name)||!1}))}(R,P))||V.some((function(e){return!1===e}))?"generic":1<(V=Object(u.uniq)(V)).length?"multiple":"single";var W=Object(p.__)("Site Kit can’t access necessary data","google-site-kit"),q=(null==D?void 0:D.data)?Object(p.__)("Grant permission","google-site-kit"):Object(p.__)("Redo setup","google-site-kit");switch(U){case"multiple":z=Object(p.sprintf)(
/* translators: %s: List of product names */
Object(p.__)("Site Kit can’t access all relevant data because you haven’t granted all permissions requested during setup. To use Site Kit, you’ll need to redo the setup for: %s – make sure to approve all permissions at the authentication stage.","google-site-kit"),Object(m.y)(V));break;case"single":z=Object(p.sprintf)(
/* translators: %s: Product name */
Object(p.__)("Site Kit can’t access the relevant data from %1$s because you haven’t granted all permissions requested during setup. To use Site Kit, you’ll need to redo the setup for %1$s – make sure to approve all permissions at the authentication stage.","google-site-kit"),V[0]);break;case"generic":z=Object(p.__)("Site Kit can’t access all relevant data because you haven’t granted all permissions requested during setup. To use Site Kit, you’ll need to redo the setup – make sure to approve all permissions at the authentication stage.","google-site-kit")}return r.createElement(s,{className:"googlesitekit-publisher-win googlesitekit-publisher-win--win-error"},r.createElement(O.a,{title:W,description:r.createElement(k.a,{text:z}),actions:r.createElement(w.a,{id:o,ctaLabel:q,ctaLink:N?void 0:L,onCTAClick:G})}))}}).call(this,n(28),n(4))},988:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return UnsatisfiedScopesAlertGTE}));var i=n(2),a=n(3),o=n(13),c=n(7),s=n(46),l=n(29),u=n(196),d=n(111),f=n(180),p=n(187);function UnsatisfiedScopesAlertGTE(t){var n=t.id,g=t.Notification,m=Object(a.useSelect)((function(e){return e(l.a).getValue(c.d,"permissionsError")})),h=Object(a.useSelect)((function(t){var n;return t(c.a).getConnectURL({additionalScopes:[s.h],redirectURL:(null==m||null===(n=m.data)||void 0===n?void 0:n.redirectURL)||e.location.href})})),b=Object(a.useSelect)((function(e){return e(o.c).getGoogleSupportURL({path:"/tagmanager/answer/11994839"})}));return void 0===h?null:r.createElement(g,{className:"googlesitekit-publisher-win googlesitekit-publisher-win--win-error"},r.createElement(u.a,{title:Object(i.__)("Site Kit needs additional permissions to detect updates to tags on your site","google-site-kit"),description:r.createElement(d.a,{text:Object(i.__)("To continue using Analytics with Site Kit, you need to grant permission to check for any changes in your Google tag’s target Analytics property. The Google tag feature was recently updated to allow users to change a tag’s connected Analytics property without editing site code. Because of this change, Site Kit now must regularly check if the tag on your site matches the Analytics property destination.","google-site-kit"),learnMoreLink:r.createElement(f.a,{id:n,label:Object(i.__)("Learn more","google-site-kit"),url:b})}),actions:r.createElement(p.a,{id:n,ctaLabel:Object(i.__)("Grant permission","google-site-kit"),ctaLink:h})}))}}).call(this,n(28),n(4))},989:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GatheringDataNotification}));var r=n(1),i=n.n(r),a=n(2),o=n(3),c=n(13),s=n(9),l=n(990),u=n(776),d=n(111),f=n(179),p=n(991);function GatheringDataNotification(t){var n,r=t.id,i=t.Notification,g=Object(o.useSelect)((function(e){return e(c.c).getAdminURL("googlesitekit-settings")})),m=Object(l.a)(),h=m.analyticsGatheringData,b=m.searchConsoleGatheringData,v=72;return h&&b?n=Object(a.__)("Search Console and Analytics are gathering data","google-site-kit"):h?n=Object(a.__)("Analytics is gathering data","google-site-kit"):b&&(v=48,n=Object(a.__)("Search Console is gathering data","google-site-kit")),v?e.createElement(i,{className:"googlesitekit-publisher-win"},e.createElement(u.a,{title:n,description:e.createElement(d.a,{text:Object(a.sprintf)(
/* translators: %s: the number of hours the site can be in a gathering data state */
Object(a._n)("It can take up to %s hour before stats show up for your site. While you’re waiting, connect more services to get more stats.","It can take up to %s hours before stats show up for your site. While you’re waiting, connect more services to get more stats.",v,"google-site-kit"),v)}),actions:e.createElement(f.a,{id:r,ctaLabel:Object(a.__)("See other services","google-site-kit"),ctaLink:"".concat(g,"#/connect-more-services"),dismissLabel:Object(a.__)("Maybe later","google-site-kit"),dismissExpires:s.a}),SmallImageSVG:p.a})):null}GatheringDataNotification.propTypes={id:i.a.string,Notification:i.a.elementType}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(6),i=n.n(r),a=n(14),o=n(100),c=n(101);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=l(l({},u),t);i.referenceSiteURL&&(i.referenceSiteURL=i.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(i,n),d=Object(c.a)(i,n,s,r),f={},p=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);f[r]||(f[r]=Object(a.once)(d)),f[r].apply(f,t)};return{enableTracking:function(){i.trackingEnabled=!0},disableTracking:function(){i.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!i.trackingEnabled},trackEvent:d,trackEventOnce:p}}}).call(this,n(28))},990:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var r=n(3),i=n(19),a=n(7),o=n(8),c=n(66),s=n(34);function l(){var e=Object(s.a)(),t=Object(r.useSelect)((function(e){return e(i.a).isModuleConnected("analytics-4")})),n=Object(r.useSelect)((function(t){return!e||t(a.a).canViewSharedModule("analytics-4")})),l=Object(r.useSelect)((function(t){return!e||t(a.a).canViewSharedModule("search-console")})),u=Object(r.useSelect)((function(t){if(!e)return!1;var n=t(i.a).getRecoverableModules();return void 0!==n?Object.keys(n).includes("analytics-4"):void 0})),d=Object(r.useSelect)((function(t){if(!e)return!1;var n=t(i.a).getRecoverableModules();return void 0!==n?Object.keys(n).includes("search-console"):void 0}));return{analyticsGatheringData:Object(r.useInViewSelect)((function(e){return!(!t||!n||!1!==u)&&e(o.r).isGatheringData()}),[t,n,u]),searchConsoleGatheringData:Object(r.useInViewSelect)((function(e){return l&&!1===d&&e(c.b).isGatheringData()}),[l,d]),analyticsHasZeroData:Object(r.useInViewSelect)((function(e){return!(!t||!n||!1!==u)&&e(o.r).hasZeroData()}),[t,n,u]),searchConsoleHasZeroData:Object(r.useInViewSelect)((function(e){return l&&!1===d&&e(c.b).hasZeroData()}),[l,d])}}},991:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M128.91 163c-12.56 3.66-25.8 10.57-31.82 22.81-6.35 12.93-1.46 28.49 10.19 36.65M271.09 163c12.56 3.66 25.8 10.57 31.82 22.81 6.35 12.93 1.46 28.49-10.19 36.65",stroke:"#EE675C",strokeLinejoin:"round",strokeWidth:9.27}),o=r.createElement("ellipse",{cx:200,cy:381.74,rx:79.51,ry:8.26,fill:"#F1F3F4"}),c=r.createElement("path",{stroke:"#E8EAED",strokeLinecap:"round",strokeMiterlimit:10,strokeWidth:6,d:"M200 10v31.42M105 35.46l15.71 27.2M35.46 105l27.2 15.71M10 200h31.42M35.46 295l27.2-15.71M364.54 295l-27.2-15.71M390 200h-31.42M364.54 105l-27.2 15.71M295 35.46l-15.71 27.2"}),s=r.createElement("path",{d:"M216.76 201.8s-64.53 36.51-77.35 71.84c-4.72 13 3.53 27.36 17.27 29 11.31 1.39 28.4-3.14 53.06-24.33v31.36",stroke:"#C5221F",strokeLinejoin:"round",strokeWidth:9.27}),l=r.createElement("path",{d:"M216.76 201.8s-35.21 19.92-58.76 44.56",stroke:"#B31412",strokeLinejoin:"round",strokeWidth:9.27}),u=r.createElement("path",{stroke:"#C5221F",strokeLinejoin:"round",strokeWidth:9.27,d:"M218.98 201.47V380h29.52"}),d=r.createElement("path",{stroke:"#B31412",strokeLinejoin:"round",strokeWidth:9.27,d:"M218.98 201.47v47.5"}),f=r.createElement("circle",{cx:200,cy:162.34,r:81.45,fill:"#D93025"}),p=r.createElement("path",{d:"M206.36 156.18c-18.33 10.42-42 6.07-50.25-19.59M164.26 137.72c-5.22-2.69-9-2.82-16.17-.91",stroke:"#FFF",strokeMiterlimit:10,strokeWidth:6.62}),g=r.createElement("path",{d:"M107.28 222.48c28.36 19.87 76.07-7.28 88.72-35.17M292.72 222.48c-28.36 19.87-76.07-7.28-88.72-35.17",stroke:"#EE675C",strokeLinejoin:"round",strokeWidth:9.27});t.a=function SvgZeroStateRed(e){return r.createElement("svg",i({viewBox:"0 0 400 400",fill:"none"},e),a,o,c,s,l,u,d,f,p,g)}},992:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ZeroDataNotification}));var r=n(2),i=n(3),a=n(13),o=n(993),c=n(9),s=n(776),l=n(111),u=n(180),d=n(92);function ZeroDataNotification(t){var n=t.id,f=t.Notification,p=Object(i.useSelect)((function(e){return e(a.c).getDocumentationLinkURL("not-enough-traffic")}));return e.createElement(f,{className:"googlesitekit-publisher-win"},e.createElement(s.a,{title:Object(r.__)("Not enough traffic yet to display stats","google-site-kit"),description:e.createElement(l.a,{text:Object(r.__)("Site Kit will start showing stats on the dashboard as soon as enough people have visited your site. Keep working on your site to attract more visitors.","google-site-kit"),learnMoreLink:e.createElement(u.a,{id:n,label:Object(r.__)("Learn more","google-site-kit"),url:p})}),actions:e.createElement(d.a,{id:n,dismissLabel:Object(r.__)("Remind me later","google-site-kit"),dismissExpires:c.a}),SmallImageSVG:o.a}))}}).call(this,n(4))},993:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("ellipse",{cx:175.51,cy:381.74,rx:104,ry:8.26,fill:"#F1F3F4"}),o=r.createElement("path",{stroke:"#E8EAED",strokeLinecap:"round",strokeMiterlimit:10,strokeWidth:6,d:"M200 10v31.42M105 35.46l15.71 27.2M35.46 105l27.2 15.71M10 200h31.42M35.46 295l27.2-15.71M364.54 295l-27.2-15.71M390 200h-31.42M364.54 105l-27.2 15.71M295 35.46l-15.71 27.2"}),c=r.createElement("path",{d:"M201.85 238.9l61.65 74a20 20 0 011 24.29l-30 42.81h32.13",stroke:"#1967D2",strokeLinejoin:"round",strokeWidth:9.27}),s=r.createElement("path",{stroke:"#1967D2",strokeLinejoin:"round",strokeWidth:9.27,d:"M214.03 222.14L114.03 380h-29.6"}),l=r.createElement("path",{stroke:"#185ABC",strokeLinejoin:"round",strokeWidth:9.27,d:"M201.85 238.9l46.71 56.1M214.03 222.14L167.87 295"}),u=r.createElement("path",{d:"M129.21 229.83l-11.7-42.66A50 50 0 01136 133.78L191 93a10 10 0 004-8V61.71M270.79 229.83l11.7-42.66A50 50 0 00264 133.78L209.05 93a10 10 0 01-4.05-8V61.71",stroke:"#4285F4",strokeLinejoin:"round",strokeWidth:9.27}),d=r.createElement("path",{d:"M199.87 289.41a81.45 81.45 0 0081.58-81.32l-162.9-.26a81.45 81.45 0 0081.32 81.58z",fill:"#1A73E8"}),f=r.createElement("path",{d:"M172.86 256.89c13.38 16.47 37.19 21.05 54.28 0",stroke:"#FFF",strokeMiterlimit:10,strokeWidth:6.66});t.a=function SvgZeroStateBlue(e){return r.createElement("svg",i({viewBox:"0 0 400 400",fill:"none"},e),a,o,c,s,l,u,d,f)}},994:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GA4AdSenseLinkedNotification}));var r=n(2),i=n(115),a=n(92);function GA4AdSenseLinkedNotification(t){var n=t.id,o=t.Notification;return e.createElement(o,null,e.createElement(i.a,{title:Object(r.__)("Your AdSense and Analytics accounts are linked","google-site-kit"),description:Object(r.__)("We’ll let you know as soon as there’s enough data available","google-site-kit"),dismissCTA:e.createElement(a.a,{id:n,primary:!1,dismissLabel:Object(r.__)("Got it","google-site-kit")})}))}}).call(this,n(4))},995:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupErrorNotification}));var r=n(2),i=n(3),a=n(13),o=n(196),c=n(111),s=n(187);function SetupErrorNotification(t){var n=t.id,l=t.Notification,u=Object(i.useSelect)((function(e){return e(a.c).getSetupErrorMessage()})),d=Object(i.useSelect)((function(e){return e(a.c).getSetupErrorRedoURL()}));return e.createElement(l,{className:"googlesitekit-publisher-win googlesitekit-publisher-win--win-error"},e.createElement(o.a,{title:Object(r.__)("Oops! There was a problem during set up. Please try again.","google-site-kit"),description:e.createElement(c.a,{text:u}),actions:d&&e.createElement(s.a,{id:n,ctaLabel:Object(r.__)("Redo the plugin setup","google-site-kit"),ctaLink:d})}))}}).call(this,n(4))},996:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return SetupErrorMessageNotification}));var i=n(21),a=n.n(i),o=n(2),c=n(3),s=n(7),l=n(13),u=n(29),d=n(196),f=n(111),p=n(180),g=n(187),m=n(18);function SetupErrorMessageNotification(t){var n=t.Notification,i=Object(m.a)(),h=Object(c.useSelect)((function(e){return e(s.a).isAuthenticated()})),b=Object(c.useSelect)((function(e){return e(l.c).getSetupErrorCode()})),v=Object(c.useSelect)((function(e){return e(l.c).getSetupErrorMessage()})),y=Object(c.useSelect)((function(e){return e(u.a).getValue(s.d,"permissionsError")})),E=Object(c.useSelect)((function(t){var n,r;return(null==y?void 0:y.data)?t(s.a).getConnectURL({additionalScopes:null==y||null===(n=y.data)||void 0===n?void 0:n.scopes,redirectURL:(null==y||null===(r=y.data)||void 0===r?void 0:r.redirectURL)||e.location.href}):"access_denied"===b&&!(null==y?void 0:y.data)&&h?null:t(l.c).getSetupErrorRedoURL()})),_=Object(c.useSelect)((function(e){return e(l.c).getErrorTroubleshootingLinkURL({code:b})})),O=Object(o.__)("Error connecting Site Kit","google-site-kit"),k=Object(o.__)("Redo the plugin setup","google-site-kit");"access_denied"===b&&(O=Object(o.__)("Permissions Error","google-site-kit"),(null==y?void 0:y.data)?k=Object(o.__)("Grant permission","google-site-kit"):!(null==y?void 0:y.data)&&h&&(k=null));var w={gaTrackingEventArgs:{category:"".concat(i,"_setup_error")}};return r.createElement(n,a()({},w,{className:"googlesitekit-publisher-win googlesitekit-publisher-win--win-error"}),r.createElement(d.a,{title:O,description:r.createElement(f.a,{text:v,learnMoreLink:r.createElement(p.a,{id:"setup_error",label:Object(o.__)("Get help","google-site-kit"),url:_})}),actions:E&&r.createElement(g.a,{id:"setup_error",ctaLabel:k,ctaLink:E})}))}}).call(this,n(28),n(4))},997:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return FirstPartyModeWarningNotification}));var r=n(38),i=n(2),a=n(115),o=n(20),c=n(92),s=n(13),l=n(3),u=n(9),d=n(18);function FirstPartyModeWarningNotification(t){var n=t.id,f=t.Notification,p=Object(d.a)(),g=Object(l.useSelect)((function(e){return e(s.c).getDocumentationLinkURL("first-party-mode-server-requirements")}));return e.createElement(f,null,e.createElement(a.a,{className:"googlesitekit-fpm-subtle-notification",title:Object(r.a)(Object(i.__)("First-party mode has been disabled due to server configuration issues. Measurement data is now being routed through the default Google server. Please contact your hosting provider to resolve the issue. <a>Learn more</a>","google-site-kit"),{a:e.createElement(o.a,{href:g,onClick:function(){Object(u.I)("".concat(p,"_warning-notification-fpm"),"click_learn_more_link")},external:!0,"aria-label":Object(i.__)("Learn more about First-party mode server requirements","google-site-kit")})}),dismissCTA:e.createElement(c.a,{id:n,primary:!1,dismissLabel:Object(i.__)("Got it","google-site-kit")}),type:"warning"}))}}).call(this,n(4))}},[[1260,1,0]]]);