(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[14],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(59),i=n(39),a=n(57);function o(t,n){var o,c=Object(r.a)(n),l=t.activeModules,s=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,f=void 0===d?[]:d,g=t.isAuthenticated,m=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(i.b,"]"))),!o){o=!0;var r=(null==f?void 0:f.length)?f.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:s,plugin_version:m||"",enabled_features:Array.from(a.a).join(","),active_modules:l.join(","),authenticated:g?"1":"0",user_properties:{user_roles:r,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(i.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a)}}}}}).call(this,n(28))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function f(e,t,n,r){var a=Object(s.a)(t);return function(){var t=l()(i.a.mark((function t(o,c,l,s){var u;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:l,value:s},t.abrupt("return",new Promise((function(e){var t,n,i=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),l=function(){clearTimeout(i),e()};a("event",c,d(d({},u),{},{event_callback:l})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&l()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,i){return t.apply(this,arguments)}}()}},102:function(e,t,n){"use strict";var r=n(123);n.d(t,"a",(function(){return r.a}));var i=n(124);n.d(t,"c",(function(){return i.a}));var a=n(125);n.d(t,"b",(function(){return a.a}))},104:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return r.createElement("svg",i({viewBox:"0 0 14 14",fill:"none"},e),a)}},105:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s);function VisuallyHidden(t){var n=t.className,r=t.children,a=o()(t,["className","children"]);return r?e.createElement("span",i()({},a,{className:u()("screen-reader-text",n)}),r):null}VisuallyHidden.propTypes={className:l.a.string,children:l.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(4))},107:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var r=n(21),i=n.n(r),a=n(152),o=n.n(a),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(2),f=n(10),g=n(154),m=n(104);function TourTooltip(t){var n=t.backProps,r=t.closeProps,c=t.index,s=t.primaryProps,u=t.size,p=t.step,h=t.tooltipProps,v=u>1?Object(g.a)(u):[],b=function(e){return l()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",i()({className:l()("googlesitekit-tour-tooltip",p.className)},h),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},p.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},p.content)),e.createElement(a.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},v.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:b(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(f.Button,i()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),p.cta,s.title&&e.createElement(f.Button,i()({className:"googlesitekit-tooltip-button",text:!0},s),s.title))),e.createElement(f.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(m.a,{width:"14",height:"14"}),onClick:r.onClick,"aria-label":Object(d.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(4))},109:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(208),l=n(38),s=n(2),u=n(10),d=n(58);function ModalDialog(t){var n=t.className,r=void 0===n?"":n,i=t.dialogActive,a=void 0!==i&&i,f=t.handleDialog,g=void 0===f?null:f,m=t.onOpen,p=void 0===m?null:m,h=t.onClose,v=void 0===h?null:h,b=t.title,E=void 0===b?null:b,_=t.provides,O=t.handleConfirm,y=t.subtitle,k=t.confirmButton,j=void 0===k?null:k,S=t.dependentModules,w=t.danger,x=void 0!==w&&w,C=t.inProgress,N=void 0!==C&&C,A=t.small,T=void 0!==A&&A,D=t.medium,R=void 0!==D&&D,B=t.buttonLink,M=void 0===B?null:B,I=Object(c.a)(ModalDialog),L="googlesitekit-dialog-description-".concat(I),F=!(!_||!_.length);return e.createElement(u.Dialog,{open:a,onOpen:p,onClose:v,"aria-describedby":F?L:void 0,tabIndex:"-1",className:o()(r,{"googlesitekit-dialog-sm":T,"googlesitekit-dialog-md":R})},e.createElement(u.DialogTitle,null,x&&e.createElement(d.a,{width:28,height:28}),E),y?e.createElement("p",{className:"mdc-dialog__lead"},y):[],e.createElement(u.DialogContent,null,F&&e.createElement("section",{id:L,className:"mdc-dialog__provides"},e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},_.map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t},e.createElement("span",{className:"mdc-list-item__text"},t))})))),S&&e.createElement("p",{className:"mdc-dialog__dependencies"},Object(l.a)(Object(s.sprintf)(
/* translators: %s is replaced with the dependent modules. */
Object(s.__)("<strong>Note:</strong> %s","google-site-kit"),S),{strong:e.createElement("strong",null)}))),e.createElement(u.DialogFooter,null,e.createElement(u.Button,{className:"mdc-dialog__cancel-button",tertiary:!0,onClick:g,disabled:N},Object(s.__)("Cancel","google-site-kit")),M?e.createElement(u.Button,{href:M,onClick:O,target:"_blank",danger:x},j):e.createElement(u.SpinnerButton,{onClick:O,danger:x,disabled:N,isSaving:N},j||Object(s.__)("Disconnect","google-site-kit"))))}ModalDialog.displayName="Dialog",ModalDialog.propTypes={className:i.a.string,dialogActive:i.a.bool,handleDialog:i.a.func,handleConfirm:i.a.func.isRequired,onOpen:i.a.func,onClose:i.a.func,title:i.a.string,confirmButton:i.a.string,danger:i.a.bool,small:i.a.bool,medium:i.a.bool,buttonLink:i.a.string},t.a=ModalDialog}).call(this,n(4))},111:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(1),i=n.n(r),a=n(0),o=n(9),c=n(54);function Description(t){var n=t.className,r=void 0===n?"googlesitekit-publisher-win__desc":n,i=t.text,l=t.learnMoreLink,s=t.errorText,u=t.children;return e.createElement(a.Fragment,null,e.createElement("div",{className:r},e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.F)(i,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",l)),s&&e.createElement(c.a,{message:s}),u)}Description.propTypes={className:i.a.string,text:i.a.string,learnMoreLink:i.a.node,errorText:i.a.string,children:i.a.node}}).call(this,n(4))},112:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(392),i=function(e,t,n){Object(r.a)((function(n){return e.includes(n.keyCode)&&t.current.contains(n.target)}),n)}},115:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(0),l=n(137),s=n(58),u=n(131),d=n(17),f=Object(c.forwardRef)((function(t,n){var r=t.className,i=t.title,a=t.description,c=t.dismissCTA,f=t.additionalCTA,g=t.reverseCTAs,m=void 0!==g&&g,p=t.type,h=void 0===p?"success":p,v=t.icon;return e.createElement(d.e,{ref:n},e.createElement(d.k,null,e.createElement(d.a,{alignMiddle:!0,size:12,className:o()("googlesitekit-subtle-notification",r,{"googlesitekit-subtle-notification--success":"success"===h,"googlesitekit-subtle-notification--warning":"warning"===h,"googlesitekit-subtle-notification--new-feature":"new-feature"===h})},e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},v,"success"===h&&!v&&e.createElement(l.a,{width:24,height:24}),"warning"===h&&!v&&e.createElement(s.a,{width:24,height:24}),"new-feature"===h&&!v&&e.createElement(u.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,i),e.createElement("p",{className:"googlesitekit-subtle-notification__secondary_description"},a)),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},!m&&c,m&&f,!m&&f,m&&c))))}));f.propTypes={className:i.a.string,title:i.a.node,description:i.a.node,dismissCTA:i.a.node,additionalCTA:i.a.node,reverseCTAs:i.a.bool,type:i.a.oneOf(["success","warning","new-feature"]),icon:i.a.object},t.a=f}).call(this,n(4))},1177:function(e,t,n){"use strict";(function(e){var r=n(11),i=n.n(r),a=n(0),o=n(38),c=n(2),l=n(3),s=n(234),u=n(71),d=n(430),f=n(707),g=n(712),m=n(235),p=n(22),h=n(13),v=n(7),b=n(20),E=n(105),_=n(17),O=n(365),y=n(192),k=n(47),j=n(627),S=n(1178),w=n(695),x=n(34),C=n(628),N=n(722),A=n(715),T=n(629);t.a=function DashboardEntityApp(){var t=Object(x.a)(),n=Object(l.useSelect)((function(e){return t?e(v.a).getViewableModules():null})),r=Object(l.useSelect)((function(e){return e(h.c).getCurrentEntityURL()})),D=Object(l.useSelect)((function(e){return e(h.c).getPermaLinkParam()})),R=Object(l.useSelect)((function(e){return e(h.c).getAdminURL("googlesitekit-dashboard")})),B={modules:n||void 0},M=Object(l.useSelect)((function(e){return e(k.a).isWidgetContextActive(u.CONTEXT_ENTITY_DASHBOARD_TRAFFIC,B)})),I=Object(l.useSelect)((function(e){return e(k.a).isWidgetContextActive(u.CONTEXT_ENTITY_DASHBOARD_CONTENT,B)})),L=Object(l.useSelect)((function(e){return e(k.a).isWidgetContextActive(u.CONTEXT_ENTITY_DASHBOARD_SPEED,B)})),F=Object(l.useSelect)((function(e){return e(k.a).isWidgetContextActive(u.CONTEXT_ENTITY_DASHBOARD_MONETIZATION,B)})),P=Object(l.useSelect)((function(e){return e(h.c).getDocumentationLinkURL("url-not-part-of-this-site")}));Object(N.a)();var z=null;return F?z=p.c:L?z=p.d:I?z=p.a:M&&(z=p.e),null===r?e.createElement("div",{className:"googlesitekit-widget-context googlesitekit-module-page googlesitekit-entity-dashboard"},e.createElement(j.a,null),e.createElement(T.a,null),e.createElement(_.e,null,e.createElement(_.k,null,e.createElement(_.a,{size:12},e.createElement(a.Fragment,null,e.createElement(b.a,{href:R,back:!0,small:!0},Object(c.__)("Back to the Site Kit Dashboard","google-site-kit")),e.createElement(O.a,{title:Object(c.__)("Detailed Page Stats","google-site-kit"),className:"googlesitekit-heading-2 googlesitekit-entity-dashboard__heading",fullWidth:!0}),e.createElement(y.a,{className:"googlesitekit-entity-dashboard__entity-header"},e.createElement(_.e,null,e.createElement(_.k,null,e.createElement(_.a,{size:12},e.createElement("p",null,Object(o.a)(Object(c.sprintf)(
/* translators: %s: current entity URL. */
Object(c.__)("It looks like the URL %s is not part of this site or is not based on standard WordPress content types, therefore there is no data available to display. Visit our <link1>support forums</link1> or <link2><VisuallyHidden>Site Kit</VisuallyHidden> website</link2> for support or further information.","google-site-kit"),"<strong>".concat(D,"</strong>")),{strong:e.createElement("strong",null),link1:e.createElement(b.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0}),link2:e.createElement(b.a,{href:P,external:!0}),VisuallyHidden:e.createElement(E.a,null)})))))))))),e.createElement(C.a,null)):e.createElement(a.Fragment,null,e.createElement(j.a,null),e.createElement(T.a,null),e.createElement(s.a,{subHeader:e.createElement(S.a,null),showNavigation:!0},e.createElement(f.a,null),e.createElement(g.a,null),!t&&e.createElement(w.a,null),e.createElement(m.a,null)),e.createElement(A.a,null),e.createElement(d.a,{id:p.e,slug:u.CONTEXT_ENTITY_DASHBOARD_TRAFFIC,className:i()({"googlesitekit-widget-context--last":z===p.e})}),e.createElement(d.a,{id:p.a,slug:u.CONTEXT_ENTITY_DASHBOARD_CONTENT,className:i()({"googlesitekit-widget-context--last":z===p.a})}),e.createElement(d.a,{id:p.d,slug:u.CONTEXT_ENTITY_DASHBOARD_SPEED,className:i()({"googlesitekit-widget-context--last":z===p.d})}),e.createElement(d.a,{id:p.c,slug:u.CONTEXT_ENTITY_DASHBOARD_MONETIZATION,className:i()({"googlesitekit-widget-context--last":z===p.c})}),e.createElement(C.a,null))}}).call(this,n(4))},1178:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return EntityBannerNotifications}));var r=n(0),i=n(167),a=n(41);function EntityBannerNotifications(){return e.createElement(r.Fragment,null,e.createElement(i.a,{areaSlug:a.b.BANNERS_ABOVE_NAV}))}}).call(this,n(4))},118:function(e,t,n){"use strict";n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return d}));var r,i=n(6),a=n.n(i),o=n(47),c=n(174),l=n(163),s=(r={},a()(r,o.c.QUARTER,3),a()(r,o.c.HALF,6),a()(r,o.c.FULL,12),r),u="googlesitekit-hidden",d=[c.a,l.a]},123:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(21),i=n.n(r),a=n(6),o=n.n(a),c=n(25),l=n.n(c),s=n(1),u=n.n(s),d=n(11),f=n.n(d);function Cell(t){var n,r=t.className,a=t.alignTop,c=t.alignMiddle,s=t.alignBottom,u=t.alignRight,d=t.alignLeft,g=t.smAlignRight,m=t.mdAlignRight,p=t.lgAlignRight,h=t.smSize,v=t.smStart,b=t.smOrder,E=t.mdSize,_=t.mdStart,O=t.mdOrder,y=t.lgSize,k=t.lgStart,j=t.lgOrder,S=t.size,w=t.children,x=l()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",i()({},x,{className:f()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":s,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":g,"mdc-layout-grid__cell--align-right-tablet":m,"mdc-layout-grid__cell--align-right-desktop":p},o()(n,"mdc-layout-grid__cell--span-".concat(S),12>=S&&S>0),o()(n,"mdc-layout-grid__cell--span-".concat(y,"-desktop"),12>=y&&y>0),o()(n,"mdc-layout-grid__cell--start-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--order-".concat(j,"-desktop"),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(E,"-tablet"),8>=E&&E>0),o()(n,"mdc-layout-grid__cell--start-".concat(_,"-tablet"),8>=_&&_>0),o()(n,"mdc-layout-grid__cell--order-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--span-".concat(h,"-phone"),4>=h&&h>0),o()(n,"mdc-layout-grid__cell--start-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--order-".concat(b,"-phone"),4>=b&&b>0),n))}),w)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},124:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",i()({ref:n,className:u()("mdc-layout-grid__inner",r)},c),a)}));f.displayName="Row",f.propTypes={className:l.a.string,children:l.a.node},f.defaultProps={className:""},t.a=f}).call(this,n(4))},125:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.alignLeft,a=t.fill,c=t.className,l=t.children,s=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",i()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":s,"mdc-layout-grid--fill":a})},d,{ref:n}),l)}));f.displayName="Grid",f.propTypes={alignLeft:l.a.bool,fill:l.a.bool,className:l.a.string,collapsed:l.a.bool,children:l.a.node},f.defaultProps={className:""},t.a=f}).call(this,n(4))},126:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},127:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},128:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},1287:function(e,t,n){"use strict";n.r(t),function(e){var t=n(332),r=n(144),i=n(1177),a=n(224),o=n(22);Object(t.a)((function(){var t=document.getElementById("js-googlesitekit-entity-dashboard");if(t){var n=t.dataset.viewOnly;Object(r.render)(e.createElement(a.a,{viewContext:n?o.m:o.l},e.createElement(i.a,null)),t)}}))}.call(this,n(4))},129:function(e,t,n){"use strict";n.d(t,"a",(function(){return p})),n.d(t,"c",(function(){return v})),n.d(t,"b",(function(){return b}));var r=n(25),i=n.n(r),a=n(6),o=n.n(a),c=n(5),l=n.n(c),s=n(12),u=n.n(s),d=n(3),f=n.n(d),g=n(37),m=n(9),p=function(e){var t;u()(e,"storeName is required to create a snapshot store.");var n={},r={deleteSnapshot:l.a.mark((function e(){var t;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"DELETE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})),restoreSnapshot:l.a.mark((function e(){var t,n,r,i,a,o,c=arguments;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},n=t.clearAfterRestore,r=void 0===n||n,e.next=4,{payload:{},type:"RESTORE_SNAPSHOT"};case 4:if(i=e.sent,a=i.cacheHit,o=i.value,!a){e.next=13;break}return e.next=10,{payload:{snapshot:o},type:"SET_STATE_FROM_SNAPSHOT"};case 10:if(!r){e.next=13;break}return e.next=13,{payload:{},type:"DELETE_SNAPSHOT"};case 13:return e.abrupt("return",a);case 14:case"end":return e.stop()}}),e)})),createSnapshot:l.a.mark((function e(){var t;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"CREATE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))},a=(t={},o()(t,"DELETE_SNAPSHOT",(function(){return Object(g.c)("datastore::cache::".concat(e))})),o()(t,"CREATE_SNAPSHOT",Object(d.createRegistryControl)((function(t){return function(){return Object(g.f)("datastore::cache::".concat(e),t.stores[e].store.getState())}}))),o()(t,"RESTORE_SNAPSHOT",(function(){return Object(g.d)("datastore::cache::".concat(e),m.b)})),t);return{initialState:n,actions:r,controls:a,reducer:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1?arguments[1]:void 0,r=t.type,a=t.payload;switch(r){case"SET_STATE_FROM_SNAPSHOT":var o=a.snapshot,c=(o.error,i()(o,["error"]));return c;default:return e}}}},h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.a;return Object.values(e.stores).filter((function(e){return Object.keys(e.getActions()).includes("restoreSnapshot")}))},v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.a;return Promise.all(h(e).map((function(e){return e.getActions().createSnapshot()})))},b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.a;return Promise.all(h(e).map((function(e){return e.getActions().restoreSnapshot()})))}},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var r="core/site",i="primary",a="secondary"},130:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(14),i=function(e){return Object(r.isFinite)(e)?e:0}},131:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},134:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(38),l=n(2),s=n(20),u=n(34);function SourceLink(t){var n=t.name,r=t.href,i=t.className,a=t.external;return Object(u.a)()?null:e.createElement("div",{className:o()("googlesitekit-source-link",i)},Object(c.a)(Object(l.sprintf)(
/* translators: %s: source link */
Object(l.__)("Source: %s","google-site-kit"),"<a>".concat(n,"</a>")),{a:e.createElement(s.a,{key:"link",href:r,external:a})}))}SourceLink.propTypes={name:i.a.string,href:i.a.string,className:i.a.string,external:i.a.bool},SourceLink.defaultProps={name:"",href:"",className:"",external:!1},t.a=SourceLink}).call(this,n(4))},135:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportErrorActions}));var r=n(6),i=n.n(r),a=n(1),o=n.n(a),c=n(0),l=n(38),s=n(2),u=n(3),d=n(10),f=n(13),g=n(19),m=n(35),p=n(34),h=n(20);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportErrorActions(t){var n=t.moduleSlug,r=t.error,i=t.GetHelpLink,a=t.hideGetHelpLink,o=t.buttonVariant,v=t.onRetry,E=t.onRequestAccess,_=t.getHelpClassName,O=t.RequestAccessButton,y=t.RetryButton,k=Object(p.a)(),j=Object(u.useSelect)((function(e){return e(g.a).getModuleStoreName(n)})),S=Object(u.useSelect)((function(e){var t;return"function"==typeof(null===(t=e(j))||void 0===t?void 0:t.getServiceEntityAccessURL)?e(j).getServiceEntityAccessURL():null})),w=Array.isArray(r)?r:[r],x=Object(u.useSelect)((function(e){return w.map((function(t){var n,r=null===(n=e(j))||void 0===n?void 0:n.getSelectorDataForError(t);return b(b({},t),{},{selectorData:r})}))})),C=null==x?void 0:x.filter((function(e){return Object(m.d)(e,e.selectorData)&&"getReport"===e.selectorData.name})),N=!!C.length,A=Object(u.useSelect)((function(e){var t=b({},N?C[0]:w[0]);return Object(m.e)(t)&&(t.code="".concat(n,"_insufficient_permissions")),e(f.c).getErrorTroubleshootingLinkURL(t)})),T=Object(u.useDispatch)(),D=w.some((function(e){return Object(m.e)(e)})),R=Object(c.useCallback)((function(){C.forEach((function(e){var t=e.selectorData;T(t.storeName).invalidateResolution(t.name,t.args)})),null==v||v()}),[T,C,v]),B=S&&D&&!k;return e.createElement("div",{className:"googlesitekit-report-error-actions"},B&&("function"==typeof O?e.createElement(O,{requestAccessURL:S}):e.createElement(d.Button,{onClick:E,href:S,target:"_blank",danger:"danger"===o,tertiary:"tertiary"===o},Object(s.__)("Request access","google-site-kit"))),N&&e.createElement(c.Fragment,null,"function"==typeof y?e.createElement(y,{handleRetry:R}):e.createElement(d.Button,{onClick:R,danger:"danger"===o,tertiary:"tertiary"===o},Object(s.__)("Retry","google-site-kit")),!a&&e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(l.a)(Object(s.__)("Retry didn’t work? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(h.a,{href:A,external:!0,hideExternalIndicator:!0},Object(s.__)("Get help","google-site-kit"))}))),!N&&!a&&e.createElement("div",{className:_},"function"==typeof i?e.createElement(i,{linkURL:A}):e.createElement(h.a,{href:A,external:!0,hideExternalIndicator:!0},Object(s.__)("Get help","google-site-kit"))))}ReportErrorActions.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired,GetHelpLink:o.a.elementType,hideGetHelpLink:o.a.bool,buttonVariant:o.a.string,onRetry:o.a.func,onRequestAccess:o.a.func,getHelpClassName:o.a.string,RequestAccessButton:o.a.elementType,RetryButton:o.a.elementType}}).call(this,n(4))},136:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"d",(function(){return a})),n.d(t,"c",(function(){return o}));function r(e){var t=e.format,n=void 0===t?"small":t,r=e.hasErrorOrWarning,i=e.hasSmallImageSVG,o=e.hasWinImageSVG,c={smSize:4,mdSize:8,lgSize:12},l=a(n);return Object.keys(c).forEach((function(e){var t=c[e];r&&(t-=1),i&&(t-=1),o&&0<t-l[e]&&(t-=l[e]),c[e]=t})),c}var i=function(e){switch(e){case"small":return{};case"larger":return{smOrder:2,mdOrder:2,lgOrder:1};default:return{smOrder:2,mdOrder:1}}},a=function(e){switch(e){case"smaller":return{smSize:4,mdSize:2,lgSize:2};case"larger":return{smSize:4,mdSize:8,lgSize:7};default:return{smSize:4,mdSize:2,lgSize:4}}},o=function(e){switch(e){case"larger":return{smOrder:1,mdOrder:1,lgOrder:2};default:return{smOrder:1,mdOrder:2}}}},137:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},139:function(e,t,n){"use strict";var r=n(0),i=Object(r.createContext)(!1);t.a=i},142:function(e,t,n){"use strict";var r=n(166);n.d(t,"c",(function(){return r.a}));var i=n(65);n.d(t,"b",(function(){return i.c})),n.d(t,"a",(function(){return i.a}))},143:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),i=n(3),a=n(47);function o(e,t,n){var o=Object(i.useDispatch)(a.a),c=o.setWidgetState,l=o.unsetWidgetState;Object(r.useEffect)((function(){return c(e,t,n),function(){l(e,t,n)}}),[e,t,n,c,l])}},147:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(15),o=n.n(a),c=n(0),l=n(409),s=n(157);t.a=function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=Object(c.useState)(Object(l.a)(r.location.href,t)||n),u=o()(a,2),d=u[0],f=u[1],g=function(e){f(e);var n=Object(s.a)(r.location.href,i()({},t,e));r.history.replaceState(null,"",n)};return[d,g]}}).call(this,n(28))},148:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M3.332 24.75h21.335c2.182 0 3.542-2.366 2.451-4.25L16.451 2.07C15.36.184 12.64.184 11.549 2.07L.882 20.5c-1.091 1.884.269 4.25 2.45 4.25zM14 14.833a1.42 1.42 0 01-1.417-1.416v-2.834c0-.779.638-1.416 1.417-1.416.78 0 1.417.637 1.417 1.416v2.834A1.42 1.42 0 0114 14.833zm1.417 5.667h-2.834v-2.833h2.834V20.5z",fill:"currentColor",fillRule:"nonzero"});t.a=function SvgError(e){return r.createElement("svg",i({viewBox:"0 0 28 25"},e),a)}},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},155:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("path",{d:"M2.253 12.252l7.399 5.658A13.055 13.055 0 009 22c0 1.43.229 2.805.652 4.09l-7.4 5.658A22.02 22.02 0 010 22c0-3.506.81-6.814 2.253-9.748z",fill:"#FBBC05"}),r.createElement("path",{d:"M9.652 17.91l-7.4-5.658A21.935 21.935 0 0122 0c5.6 0 10.6 2.1 14.5 5.5l-6.4 6.4C27.9 10.1 25.1 9 22 9c-5.77 0-10.64 3.725-12.348 8.91z",fill:"#EA4335"}),r.createElement("path",{d:"M2.25 31.742l7.396-5.67A12.975 12.975 0 0022 35c6.1 0 10.7-3.1 11.8-8.5H22V18h20.5c.3 1.3.5 2.7.5 4 0 14-10 22-21 22A21.935 21.935 0 012.25 31.742z",fill:"#34A853"}),r.createElement("path",{d:"M36.34 38.52l-7.025-5.437c2.297-1.45 3.895-3.685 4.485-6.583H22V18h20.5c.3 1.3.5 2.7.5 4 0 7.17-2.623 12.767-6.66 16.52z",fill:"#4285F4"}));t.a=function SvgLogoG(e){return r.createElement("svg",i({viewBox:"0 0 43 44"},e),a)}},158:function(e,t,n){"use strict";var r=n(0),i=n(57),a=Object(r.createContext)(i.a);t.a=a},159:function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(0),l=n(3),s=n(13),u=n(7),d=n(19),f=n(32),g=n(37),m=n(36),p=n(18);function h(e){var t=Object(p.a)(),n=Object(l.useSelect)((function(t){return t(d.a).getModule(e)})),r=Object(l.useSelect)((function(e){return e(u.a).hasCapability(u.K)})),a=Object(l.useDispatch)(d.a).activateModule,h=Object(l.useDispatch)(f.a).navigateTo,v=Object(l.useDispatch)(s.c).setInternalServerError,b=Object(c.useCallback)(o()(i.a.mark((function n(){var r,o,c;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,a(e);case 2:if(r=n.sent,o=r.error,c=r.response,o){n.next=13;break}return n.next=8,Object(m.b)("".concat(t,"_widget-activation-cta"),"activate_module",e);case 8:return n.next=10,Object(g.f)("module_setup",e,{ttl:300});case 10:h(c.moduleReauthURL),n.next=14;break;case 13:v({id:"".concat(e,"-setup-error"),description:o.message});case 14:case"end":return n.stop()}}),n)}))),[a,e,h,v,t]);return(null==n?void 0:n.name)&&r?b:null}},160:function(e,t,n){"use strict";var r=n(139),i=(r.a.Consumer,r.a.Provider);t.a=i},161:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(3),i=n(23),a=function(e){return"notification/".concat(e,"/viewed")};function o(e){return Object(r.useSelect)((function(t){return!!t(i.b).getValue(a(e))}),[e])}o.getKey=a},163:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RecoverableModules}));var r=n(1),i=n.n(r),a=n(2),o=n(3),c=n(19),l=n(95);function RecoverableModules(t){var n=t.moduleSlugs,r=Object(o.useSelect)((function(e){var t=e(c.a).getModules();if(void 0!==t)return n.map((function(e){return t[e].name}))}));if(void 0===r)return null;var i=1===r.length?Object(a.sprintf)(
/* translators: %s: Module name */
Object(a.__)("%s data was previously shared by an admin who no longer has access. Please contact another admin to restore it.","google-site-kit"),r[0]):Object(a.sprintf)(
/* translators: %s: List of module names */
Object(a.__)("The data for the following modules was previously shared by an admin who no longer has access: %s. Please contact another admin to restore it.","google-site-kit"),r.join(Object(a._x)(", ","Recoverable modules","google-site-kit")));return e.createElement(l.a,{title:Object(a.__)("Data Unavailable","google-site-kit"),description:i})}RecoverableModules.propTypes={moduleSlugs:i.a.arrayOf(i.a.string).isRequired}}).call(this,n(4))},164:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(1),l=n.n(c),s=n(0),u=n(20),d=n(9),f=n(18);function HelpMenuLink(t){var n=t.children,r=t.href,a=t.gaEventLabel,c=Object(f.a)(),l=Object(s.useCallback)(o()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!a){e.next=3;break}return e.next=3,Object(d.I)("".concat(c,"_headerbar_helpmenu"),"click_outgoing_link",a);case 3:case"end":return e.stop()}}),e)}))),[a,c]);return e.createElement("li",{className:"googlesitekit-help-menu-link mdc-list-item",role:"none"},e.createElement(u.a,{className:"mdc-list-item__text",href:r,external:!0,hideExternalIndicator:!0,role:"menuitem",onClick:l},n))}HelpMenuLink.propTypes={children:l.a.node.isRequired,href:l.a.string.isRequired,gaEventLabel:l.a.string},t.a=HelpMenuLink}).call(this,n(4))},166:function(e,t,n){"use strict";(function(e){var r=n(11),i=n.n(r),a=n(1),o=n.n(a),c=n(2),l=n(3),s=n(201),u=n(210),d=n(65),f=n(7),g=n(10),m=n(0),p=Object(m.forwardRef)((function(t,n){var r=t.className,a=t.children,o=t.type,m=t.dismiss,p=void 0===m?"":m,h=t.dismissCallback,v=t.dismissLabel,b=void 0===v?Object(c.__)("OK, Got it!","google-site-kit"):v,E=t.Icon,_=void 0===E?Object(d.d)(o):E,O=t.OuterCTA,y=Object(l.useDispatch)(f.a).dismissItem,k=Object(l.useSelect)((function(e){return p?e(f.a).isItemDismissed(p):void 0}));if(p&&k)return null;var j=a?u.a:s.a;return e.createElement("div",{ref:n,className:i()(r,"googlesitekit-settings-notice","googlesitekit-settings-notice--".concat(o),{"googlesitekit-settings-notice--single-row":!a,"googlesitekit-settings-notice--multi-row":a})},e.createElement("div",{className:"googlesitekit-settings-notice__icon"},e.createElement(_,{width:"20",height:"20"})),e.createElement("div",{className:"googlesitekit-settings-notice__body"},e.createElement(j,t)),p&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(g.Button,{tertiary:!0,onClick:function(){"string"==typeof p&&y(p),null==h||h()}},b)),O&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(O,null)))}));p.propTypes={className:o.a.string,children:o.a.node,notice:o.a.node.isRequired,type:o.a.oneOf([d.a,d.c,d.b]),Icon:o.a.elementType,LearnMore:o.a.elementType,CTA:o.a.elementType,OuterCTA:o.a.elementType,dismiss:o.a.string,dismissLabel:o.a.string,dismissCallback:o.a.func},p.defaultProps={type:d.a},t.a=p}).call(this,n(4))},167:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notifications}));var r=n(6),i=n.n(r),a=n(1),o=n.n(a),c=n(3),l=n(18),s=n(41),u=n(283);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Notifications(t){var n,r=t.areaSlug,a=t.groupID,o=void 0===a?s.c.DEFAULT:a,f=Object(l.a)(),g=Object(c.useSelect)((function(e){return e(s.a).getQueuedNotifications(f,o)}));if(void 0===(null==g?void 0:g[0])||(null==g||null===(n=g[0])||void 0===n?void 0:n.areaSlug)!==r)return null;var m=g[0],p=m.id,h=m.Component,v=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},Object(u.a)(p));return e.createElement(h,v)}Notifications.propTypes={viewContext:o.a.string,areaSlug:o.a.string}}).call(this,n(4))},168:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALinkSubtle}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(73),f=n(10),g=n(70);function CTALinkSubtle(t){var n=t.id,r=t.ctaLink,a=t.ctaLabel,c=t.onCTAClick,l=t.isCTALinkExternal,s=void 0!==l&&l,m=t.gaTrackingEventArgs,p=t.tertiary,h=void 0!==p&&p,v=t.isSaving,b=void 0!==v&&v,E=Object(d.a)(n,null==m?void 0:m.category),_=function(){var e=o()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==c?void 0:c(t);case 2:E.confirm(null==m?void 0:m.label,null==m?void 0:m.value);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(f.Button,{className:u()("googlesitekit-subtle-notification__cta",{"googlesitekit-subtle-notification__cta--spinner__running":b}),href:r,onClick:_,target:s?"_blank":"_self",trailingIcon:s?e.createElement(g.a,{width:14,height:14}):void 0,icon:b?e.createElement(f.CircularProgress,{size:14}):void 0,tertiary:h},a)}CTALinkSubtle.propTypes={id:l.a.string,ctaLink:l.a.string,ctaLabel:l.a.string,onCTAClick:l.a.func,isCTALinkExternal:l.a.bool,gaTrackingEventArgs:l.a.shape({label:l.a.string,value:l.a.string}),tertiary:l.a.bool,isSaving:l.a.bool}}).call(this,n(4))},169:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(2);function i(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},i=n.slug,a=void 0===i?"":i,o=n.name,c=void 0===o?"":o,l=n.owner,s=void 0===l?{}:l;if(!a||!c)return e;var u="",d="";return"analytics-4"===a?e.match(/account/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):"search-console"===a&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),u||(u=Object(r.sprintf)(
/* translators: %s: module name */
Object(r.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),c)),s&&s.login&&(d=Object(r.sprintf)(
/* translators: %s: owner name */
Object(r.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),s.login)),d||(d=Object(r.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(u," ").concat(d)}},17:function(e,t,n){"use strict";var r=n(254);n.d(t,"i",(function(){return r.a}));var i=n(319);n.d(t,"f",(function(){return i.a}));var a=n(320);n.d(t,"h",(function(){return a.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var l=n(91),s=n.n(l);n.d(t,"b",(function(){return s.a})),n.d(t,"c",(function(){return l.DialogContent})),n.d(t,"d",(function(){return l.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},170:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportError}));var r=n(6),i=n.n(r),a=n(1),o=n.n(a),c=n(14),l=n(0),s=n(2),u=n(3),d=n(19),f=n(35),g=n(169),m=n(84),p=n(54),h=n(95),v=n(135),b=n(34);function E(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?E(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):E(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportError(t){var n,r=t.moduleSlug,i=t.error,a=Object(b.a)(),o=Object(u.useSelect)((function(e){return e(d.a).getModule(r)})),E=Array.isArray(i)?i:[i],O=function(e){return Object(f.e)(e)?a?(n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Access lost to %s","google-site-kit"),null==o?void 0:o.name),Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("The administrator sharing this module with you has lost access to the %s service, so you won’t be able to see stats from it on the Site Kit dashboard. You can contact them or another administrator to restore access.","google-site-kit"),null==o?void 0:o.name)):(n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Insufficient permissions in %s","google-site-kit"),null==o?void 0:o.name),Object(g.a)(e.message,o)):Object(f.b)(e)},y=Object(c.uniqWith)(E.map((function(e){var t;return _(_({},e),{},{message:O(e),reconnectURL:null===(t=e.data)||void 0===t?void 0:t.reconnectURL})})),(function(e,t){return e.message===t.message&&e.reconnectURL===t.reconnectURL})),k=E.some((function(e){return Object(f.e)(e)}));k||1!==y.length?!k&&y.length>1&&(n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Data errors in %s","google-site-kit"),null==o?void 0:o.name)):n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Data error in %s","google-site-kit"),null==o?void 0:o.name);var j=e.createElement(l.Fragment,null,y.map((function(t){var n,r=null==i||null===(n=i.data)||void 0===n?void 0:n.reconnectURL;return r?e.createElement(p.a,{key:t.message,message:t.message,reconnectURL:r}):e.createElement("p",{key:t.message},m.a.sanitize(t.message,{ALLOWED_TAGS:[]}))})));return e.createElement(h.a,{title:n,description:j,error:!0},e.createElement(v.a,{moduleSlug:r,error:i}))}ReportError.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired}}).call(this,n(4))},171:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"b",(function(){return a}));var r="editing-user-role-select-slug-key",i="dashboardSharingDialogOpen",a="resetSharingDialogOpen"},172:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GenericErrorHandlerActions}));var r=n(1),i=n.n(r),a=n(2),o=n(20),c=n(194);function GenericErrorHandlerActions(t){var n=t.message,r=t.componentStack;return e.createElement("div",{className:"googlesitekit-generic-error-handler-actions"},e.createElement(c.a,{message:n,componentStack:r}),e.createElement(o.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0},Object(a.__)("Report this problem","google-site-kit")))}GenericErrorHandlerActions.propTypes={message:i.a.string,componentStack:i.a.string}}).call(this,n(4))},173:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(22),i=function(e){return r.f.includes(e)}},174:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportZero}));var r=n(1),i=n.n(r),a=n(2),o=n(3),c=n(19),l=n(95);function ReportZero(t){var n=t.moduleSlug,r=Object(o.useSelect)((function(e){return e(c.a).getModule(n)}));return e.createElement(l.a,{title:Object(a.sprintf)(
/* translators: %s: Module name */
Object(a.__)("%s Gathering Data","google-site-kit"),null==r?void 0:r.name),description:Object(a.sprintf)(
/* translators: %s: Module name */
Object(a.__)("%s data is not yet available, please check back later","google-site-kit"),null==r?void 0:r.name)})}ReportZero.propTypes={moduleSlug:i.a.string.isRequired}}).call(this,n(4))},175:function(e,t,n){"use strict";var r=n(216);n.d(t,"b",(function(){return r.a}));var i=n(221);n.d(t,"a",(function(){return i.a}))},179:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActionsCTALinkDismiss}));var r=n(1),i=n.n(r),a=n(2),o=n(0),c=n(3),l=n(32),s=n(92),u=n(187);function ActionsCTALinkDismiss(t){var n=t.id,r=t.className,i=void 0===r?"googlesitekit-publisher-win__actions":r,d=t.ctaLink,f=t.ctaLabel,g=t.ctaDisabled,m=void 0!==g&&g,p=t.onCTAClick,h=t.ctaDismissOptions,v=t.isSaving,b=void 0!==v&&v,E=t.onDismiss,_=void 0===E?function(){}:E,O=t.dismissLabel,y=void 0===O?Object(a.__)("OK, Got it!","google-site-kit"):O,k=t.dismissOnCTAClick,j=void 0===k||k,S=t.dismissExpires,w=void 0===S?0:S,x=t.dismissOptions,C=void 0===x?{}:x,N=t.gaTrackingEventArgs,A=void 0===N?{}:N,T=Object(c.useSelect)((function(e){return!!d&&e(l.a).isNavigatingTo(d)}));return e.createElement(o.Fragment,null,e.createElement("div",{className:i},e.createElement(u.a,{id:n,ctaLink:d,ctaLabel:f,onCTAClick:p,dismissOnCTAClick:j,dismissExpires:w,dismissOptions:h,gaTrackingEventArgs:A,isSaving:b,isDisabled:m}),e.createElement(s.a,{id:n,primary:!1,dismissLabel:y,dismissExpires:w,disabled:T,onDismiss:_,dismissOptions:C,gaTrackingEventArgs:A})))}ActionsCTALinkDismiss.propTypes={id:i.a.string,className:i.a.string,ctaDisabled:i.a.bool,ctaLink:i.a.string,ctaLabel:i.a.string,onCTAClick:i.a.func,isSaving:i.a.bool,onDismiss:i.a.func,ctaDismissOptions:i.a.object,dismissLabel:i.a.string,dismissOnCTAClick:i.a.bool,dismissExpires:i.a.number,dismissOptions:i.a.object,gaTrackingEventArgs:i.a.object}}).call(this,n(4))},18:function(e,t,n){"use strict";var r=n(0),i=n(61);t.a=function(){return Object(r.useContext)(i.b)}},180:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LearnMoreLink}));var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(20),u=n(73);function LearnMoreLink(t){var n=t.id,r=t.label,a=t.url,c=t.ariaLabel,l=t.gaTrackingEventArgs,d=t.external,f=void 0===d||d,g=o()(t,["id","label","url","ariaLabel","gaTrackingEventArgs","external"]),m=Object(u.a)(n);return e.createElement(s.a,i()({onClick:function(e){e.persist(),m.clickLearnMore(null==l?void 0:l.label,null==l?void 0:l.value)},href:a,"aria-label":c,external:f},g),r)}LearnMoreLink.propTypes={id:l.a.string,label:l.a.string,url:l.a.string,ariaLabel:l.a.string,gaTrackingEventArgs:l.a.shape({label:l.a.string,value:l.a.string}),external:l.a.bool}}).call(this,n(4))},183:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LoadingWrapper}));var r=n(6),i=n.n(r),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(44);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function LoadingWrapper(t){var n=t.loading,r=t.children,i=o()(t,["loading","children"]);return n?e.createElement(s.a,i):r}LoadingWrapper.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({loading:l.a.bool,children:l.a.node},s.a.propTypes)}).call(this,n(4))},186:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 0h2v7H0zm0 10h2v2H0z",fill:"currentColor",fillRule:"evenodd"});t.a=function SvgWarningIcon(e){return r.createElement("svg",i({viewBox:"0 0 2 12"},e),a)}},187:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALink}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(15),u=n.n(s),d=n(1),f=n.n(d),g=n(206),m=n(0),p=n(3),h=n(41),v=n(32),b=n(13),E=n(73),_=n(10);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function CTALink(t){var n=t.id,r=t.ctaLink,a=t.ctaLabel,o=t.onCTAClick,c=t.isSaving,s=t.dismissOnCTAClick,d=void 0!==s&&s,f=t.dismissExpires,O=void 0===f?0:f,k=t.dismissOptions,j=void 0===k?{}:k,S=t.gaTrackingEventArgs,w=t.isDisabled,x=void 0!==w&&w,C=Object(m.useState)(!1),N=u()(C,2),A=N[0],T=N[1],D=Object(g.a)(),R=Object(E.a)(n,null==S?void 0:S.category),B=Object(p.useSelect)((function(e){return!!r&&e(v.a).isNavigatingTo(r)})),M=Object(p.useDispatch)(b.c),I=M.clearError,L=M.receiveError,F=Object(p.useDispatch)(h.a).dismissNotification,P=Object(p.useDispatch)(v.a).navigateTo,z=function(){var e=l()(i.a.mark((function e(t){var a,c,l;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return I("notificationAction",[n]),t.persist(),!t.defaultPrevented&&r&&t.preventDefault(),T(!0),e.next=6,null==o?void 0:o(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:if(a=e.t0,c=a.error,D()&&T(!1),!c){e.next=15;break}return L(c,"notificationAction",[n]),e.abrupt("return");case 15:return l=[R.confirm()],d&&l.push(F(n,y(y({},j),{},{expiresInSeconds:O}))),e.next=19,Promise.all(l);case 19:r&&P(r);case 20:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(_.SpinnerButton,{className:"googlesitekit-notification__cta",href:r,onClick:z,disabled:A||B||x,isSaving:A||B||c},a)}CTALink.propTypes={id:f.a.string,ctaLink:f.a.string,ctaLabel:f.a.string,onCTAClick:f.a.func,dismissOnCTAClick:f.a.bool,dismissExpires:f.a.number,dismissOptions:f.a.object,isDisabled:f.a.bool}}).call(this,n(4))},189:function(e,t,n){"use strict";(function(e){function Title(t){var n=t.title;return e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n)}n.d(t,"a",(function(){return Title}))}).call(this,n(4))},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="core/modules",i="insufficient_module_dependencies"},191:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notification}));var r=n(15),i=n.n(r),a=n(1),o=n.n(a),c=n(0),l=n(282),s=n(161),u=n(73);function Notification(t){var n=t.id,r=t.className,a=t.gaTrackingEventArgs,o=t.children,d=t.onView,f=Object(c.useRef)(),g=Object(s.a)(n),m=Object(u.a)(n,null==a?void 0:a.category),p=Object(c.useState)(!1),h=i()(p,2),v=h[0],b=h[1];return Object(c.useEffect)((function(){!v&&g&&(m.view(null==a?void 0:a.label,null==a?void 0:a.value),null==d||d(),b(!0))}),[g,m,v,a,d]),e.createElement("section",{id:n,ref:f,className:r},o,!g&&e.createElement(l.a,{id:n,observeRef:f,threshold:.5}))}Notification.propTypes={id:o.a.string,className:o.a.string,gaTrackingEventArgs:o.a.shape({category:o.a.string,label:o.a.string,value:o.a.string}),children:o.a.node,onView:o.a.func}}).call(this,n(4))},192:function(e,t,n){"use strict";(function(e){var r=n(51),i=n.n(r),a=n(53),o=n.n(a),c=n(68),l=n.n(c),s=n(69),u=n.n(s),d=n(49),f=n.n(d),g=n(1),m=n.n(g),p=n(11),h=n.n(p),v=n(0),b=n(329),E=n(330);function _(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var i=f()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return u()(this,n)}}var O=function(t){l()(Layout,t);var n=_(Layout);function Layout(){return i()(this,Layout),n.apply(this,arguments)}return o()(Layout,[{key:"render",value:function(){var t=this.props,n=t.header,r=t.footer,i=t.children,a=t.title,o=t.badge,c=t.headerCTALabel,l=t.headerCTALink,s=t.footerCTALabel,u=t.footerCTALink,d=t.footerContent,f=t.className,g=t.fill,m=t.relative,p=t.rounded,v=void 0!==p&&p,_=t.transparent,O=void 0!==_&&_;return e.createElement("div",{className:h()("googlesitekit-layout",f,{"googlesitekit-layout--fill":g,"googlesitekit-layout--relative":m,"googlesitekit-layout--rounded":v,"googlesitekit-layout--transparent":O})},n&&e.createElement(b.a,{title:a,badge:o,ctaLabel:c,ctaLink:l}),i,r&&e.createElement(E.a,{ctaLabel:s,ctaLink:u,footerContent:d}))}}]),Layout}(v.Component);O.propTypes={header:m.a.bool,footer:m.a.bool,children:m.a.node.isRequired,title:m.a.string,badge:m.a.node,headerCTALabel:m.a.string,headerCTALink:m.a.string,footerCTALabel:m.a.string,footerCTALink:m.a.string,footerContent:m.a.node,className:m.a.string,fill:m.a.bool,relative:m.a.bool,rounded:m.a.bool,transparent:m.a.bool},O.defaultProps={header:!1,footer:!1,title:"",badge:null,headerCTALabel:"",headerCTALink:"",footerCTALabel:"",footerCTALink:"",footerContent:null,className:"",fill:!1,relative:!1},t.a=O}).call(this,n(4))},194:function(e,t,n){"use strict";(function(e){var r=n(15),i=n.n(r),a=n(195),o=n.n(a),c=n(1),l=n.n(c),s=n(0),u=n(2),d=n(266),f=n(423),g=n(424),m=n(10);function ReportErrorButton(t){var n=t.message,r=t.componentStack,a=Object(s.useState)(!1),c=i()(a,2),l=c[0],p=c[1];return e.createElement(m.Button,{"aria-label":l?Object(u.__)("Error message copied to clipboard. Click to copy the error message again.","google-site-kit"):void 0,onClick:function(){o()("`".concat(n,"\n").concat(r,"`")),p(!0)},trailingIcon:e.createElement(d.a,{className:"mdc-button__icon",icon:l?f.a:g.a})},l?Object(u.__)("Copied to clipboard","google-site-kit"):Object(u.__)("Copy error contents","google-site-kit"))}ReportErrorButton.propTypes={message:l.a.string,componentStack:l.a.string},t.a=ReportErrorButton}).call(this,n(4))},196:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationError}));var r=n(17),i=n(222),a=n(189);function NotificationError(t){var n=t.title,o=t.description,c=t.actions;return e.createElement(r.e,null,e.createElement(r.k,null,e.createElement(r.a,{smSize:3,mdSize:7,lgSize:11,className:"googlesitekit-publisher-win__content"},e.createElement(a.a,{title:n}),o,c),e.createElement(i.a,{type:"win-error"})))}}).call(this,n(4))},197:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerNotification}));var r=n(21),i=n.n(r),a=n(6),o=n.n(a),c=n(5),l=n.n(c),s=n(16),u=n.n(s),d=n(15),f=n.n(d),g=n(1),m=n.n(g),p=n(11),h=n.n(p),v=n(206),b=n(240),E=n(81),_=n(0),O=n(106),y=n(3),k=n(17),j=n(93),S=n(37),w=n(24),x=n(211),C=n(213),N=n(212),A=n(226),T=n(227),D=n(86),R=n(136),B=n(130),M=n(32),I=n(228),L=n(79);function BannerNotification(t){var n,r=t.badgeLabel,a=t.children,c=t.className,s=void 0===c?"":c,d=t.ctaLabel,g=t.ctaLink,m=t.ctaTarget,p=t.description,F=t.dismiss,P=t.dismissExpires,z=void 0===P?0:P,H=t.format,W=void 0===H?"":H,G=t.id,V=t.isDismissible,U=void 0===V||V,q=t.learnMoreDescription,K=t.learnMoreLabel,X=t.learnMoreURL,Y=t.learnMoreTarget,$=void 0===Y?D.a.EXTERNAL:Y,Z=t.logo,J=t.module,Q=t.moduleName,ee=t.onCTAClick,te=t.onView,ne=t.onDismiss,re=t.onLearnMoreClick,ie=t.showOnce,ae=void 0!==ie&&ie,oe=t.SmallImageSVG,ce=t.title,le=t.type,se=t.WinImageSVG,ue=t.showSmallWinImage,de=void 0===ue||ue,fe=t.smallWinImageSVGWidth,ge=void 0===fe?75:fe,me=t.smallWinImageSVGHeight,pe=void 0===me?75:me,he=t.mediumWinImageSVGWidth,ve=void 0===he?105:he,be=t.mediumWinImageSVGHeight,Ee=void 0===be?105:be,_e=t.rounded,Oe=void 0!==_e&&_e,ye=t.footer,ke=t.secondaryPane,je=t.ctaComponent,Se=Object(_.useState)(!1),we=f()(Se,2),xe=we[0],Ce=we[1],Ne=Object(_.useState)(!1),Ae=f()(Ne,2),Te=Ae[0],De=Ae[1],Re="notification::dismissed::".concat(G),Be=function(){return Object(S.f)(Re,new Date,{ttl:null})},Me=Object(L.a)(),Ie=Object(w.e)(),Le=Object(v.a)(),Fe=Object(_.useState)(!1),Pe=f()(Fe,2),ze=Pe[0],He=Pe[1],We=Object(_.useRef)(),Ge=Object(b.a)(We,{rootMargin:"".concat(-Object(B.a)(Object(j.c)(Ie)),"px 0px 0px 0px"),threshold:0});Object(_.useEffect)((function(){!ze&&(null==Ge?void 0:Ge.isIntersecting)&&("function"==typeof te&&te(),He(!0))}),[G,te,ze,Ge]);var Ve=Me>=600;Object(E.a)(u()(l.a.mark((function e(){var t,n;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(z>0)){e.next=3;break}return e.next=3,Ze();case 3:if(!U){e.next=9;break}return e.next=6,Object(S.d)(Re);case 6:t=e.sent,n=t.cacheHit,De(n);case 9:if(!ae){e.next=12;break}return e.next=12,Be();case 12:case"end":return e.stop()}}),e)}))));var Ue=function(){var e=u()(l.a.mark((function e(t){return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),t.preventDefault(),!ne){e.next=5;break}return e.next=5,ne(t);case 5:Ke();case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),qe=Object(O.a)(g)&&"_blank"!==m,Ke=function(){return qe||Ce(!0),new Promise((function(e){setTimeout(u()(l.a.mark((function t(){var n;return l.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Be();case 2:Le()&&De(!0),n=new Event("notificationDismissed"),document.dispatchEvent(n),e();case 6:case"end":return t.stop()}}),t)}))),350)}))},Xe=Object(y.useSelect)((function(e){return!!g&&e(M.a).isNavigatingTo(g)})),Ye=Object(y.useDispatch)(M.a).navigateTo,$e=function(){var e=u()(l.a.mark((function e(t){var n,r,i;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),qe&&!t.defaultPrevented&&t.preventDefault(),n=!0,!ee){e.next=12;break}return e.next=6,ee(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:r=e.t0,i=r.dismissOnCTAClick,n=void 0===i||i;case 12:if(!U||!n){e.next=15;break}return e.next=15,Ke();case 15:qe&&Ye(g);case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Ze=function(){var e=u()(l.a.mark((function e(){var t,n,r;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(S.d)(Re);case 2:if(t=e.sent,!(n=t.value)){e.next=10;break}if((r=new Date(n)).setSeconds(r.getSeconds()+parseInt(z,10)),!(r<new Date)){e.next=10;break}return e.next=10,Object(S.c)(Re);case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();if(!Xe&&U&&(void 0===Te||Te))return null;var Je=!Xe&&xe?"is-closed":"is-open",Qe=Object(R.d)(W),et=Object(R.c)(W),tt=Object(R.a)(W),nt=Object(R.b)({format:W,hasErrorOrWarning:"win-error"===le||"win-warning"===le,hasSmallImageSVG:!!oe,hasWinImageSVG:!!se});return e.createElement(x.a,{id:G,className:h()(s,(n={},o()(n,"googlesitekit-publisher-win--".concat(W),W),o()(n,"googlesitekit-publisher-win--".concat(le),le),o()(n,"googlesitekit-publisher-win--".concat(Je),Je),o()(n,"googlesitekit-publisher-win--rounded",Oe),n)),secondaryPane:ke,ref:We},Z&&e.createElement(T.a,{module:J,moduleName:Q}),oe&&e.createElement(k.a,{size:1,className:"googlesitekit-publisher-win__small-media"},e.createElement(oe,null)),e.createElement(k.a,i()({},nt,tt,{className:"googlesitekit-publisher-win__content"}),e.createElement(C.a,{title:ce,badgeLabel:r,smallWinImageSVGHeight:pe,smallWinImageSVGWidth:ge,winImageFormat:W,WinImageSVG:!Ve&&de?se:void 0}),e.createElement(I.a,{description:p,learnMoreURL:X,learnMoreLabel:K,learnMoreTarget:$,learnMoreDescription:q,onLearnMoreClick:re}),a,e.createElement(N.a,{ctaLink:g,ctaLabel:d,ctaComponent:je,ctaTarget:m,ctaCallback:$e,dismissLabel:U?F:void 0,dismissCallback:Ue}),ye&&e.createElement("div",{className:"googlesitekit-publisher-win__footer"},ye)),se&&(Ve||!de)&&e.createElement(k.a,i()({},Qe,et,{alignBottom:"larger"===W,className:"googlesitekit-publisher-win__image"}),e.createElement("div",{className:"googlesitekit-publisher-win__image-".concat(W)},e.createElement(se,{style:{maxWidth:ve,maxHeight:Ee}}))),e.createElement(A.a,{type:le}))}BannerNotification.propTypes={id:m.a.string.isRequired,className:m.a.string,title:m.a.string.isRequired,description:m.a.node,learnMoreURL:m.a.string,learnMoreDescription:m.a.string,learnMoreLabel:m.a.string,learnMoreTarget:m.a.oneOf(Object.values(D.a)),WinImageSVG:m.a.elementType,SmallImageSVG:m.a.elementType,format:m.a.string,ctaLink:m.a.string,ctaLabel:m.a.string,type:m.a.string,dismiss:m.a.string,isDismissible:m.a.bool,logo:m.a.bool,module:m.a.string,moduleName:m.a.string,dismissExpires:m.a.number,showOnce:m.a.bool,onCTAClick:m.a.func,onView:m.a.func,onDismiss:m.a.func,onLearnMoreClick:m.a.func,badgeLabel:m.a.string,rounded:m.a.bool,footer:m.a.node,secondaryPane:m.a.node,showSmallWinImage:m.a.bool,smallWinImageSVGWidth:m.a.number,smallWinImageSVGHeight:m.a.number,mediumWinImageSVGWidth:m.a.number,mediumWinImageSVGHeight:m.a.number}}).call(this,n(4))},198:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleIcon}));var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(3),u=n(19);function ModuleIcon(t){var n=t.slug,r=t.size,a=o()(t,["slug","size"]),c=Object(s.useSelect)((function(e){return e(u.a).getModuleIcon(n)}));return c?e.createElement(c,i()({width:r,height:r},a)):null}ModuleIcon.propTypes={slug:l.a.string.isRequired,size:l.a.number},ModuleIcon.defaultProps={size:33}}).call(this,n(4))},199:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SupportLink}));var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(3),u=n(13),d=n(20);function SupportLink(t){var n=t.path,r=t.query,a=t.hash,c=o()(t,["path","query","hash"]),l=Object(s.useSelect)((function(e){return e(u.c).getGoogleSupportURL({path:n,query:r,hash:a})}));return e.createElement(d.a,i()({},c,{href:l}))}SupportLink.propTypes={path:l.a.string.isRequired,query:l.a.object,hash:l.a.string}}).call(this,n(4))},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(146),f=n(0),g=n(2),m=n(126),p=n(127),h=n(128),v=n(70),b=n(76),E=Object(f.forwardRef)((function(t,n){var r,a=t["aria-label"],c=t.secondary,s=void 0!==c&&c,u=t.arrow,f=void 0!==u&&u,E=t.back,_=void 0!==E&&E,O=t.caps,y=void 0!==O&&O,k=t.children,j=t.className,S=void 0===j?"":j,w=t.danger,x=void 0!==w&&w,C=t.disabled,N=void 0!==C&&C,A=t.external,T=void 0!==A&&A,D=t.hideExternalIndicator,R=void 0!==D&&D,B=t.href,M=void 0===B?"":B,I=t.inverse,L=void 0!==I&&I,F=t.noFlex,P=void 0!==F&&F,z=t.onClick,H=t.small,W=void 0!==H&&H,G=t.standalone,V=void 0!==G&&G,U=t.linkButton,q=void 0!==U&&U,K=t.to,X=t.leadingIcon,Y=t.trailingIcon,$=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),Z=M||K||!z?K?"ROUTER_LINK":T?"EXTERNAL_LINK":"LINK":N?"BUTTON_DISABLED":"BUTTON",J="BUTTON"===Z||"BUTTON_DISABLED"===Z?"button":"ROUTER_LINK"===Z?d.b:"a",Q=("EXTERNAL_LINK"===Z&&(r=Object(g._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===Z&&(r=Object(g._x)("(disabled)","screen reader text","google-site-kit")),r?a?"".concat(a," ").concat(r):"string"==typeof k?"".concat(k," ").concat(r):void 0:a),ee=X,te=Y;return _&&(ee=e.createElement(h.a,{width:14,height:14})),T&&!R&&(te=e.createElement(v.a,{width:14,height:14})),f&&!L&&(te=e.createElement(m.a,{width:14,height:14})),f&&L&&(te=e.createElement(p.a,{width:14,height:14})),e.createElement(J,i()({"aria-label":Q,className:l()("googlesitekit-cta-link",S,{"googlesitekit-cta-link--secondary":s,"googlesitekit-cta-link--inverse":L,"googlesitekit-cta-link--small":W,"googlesitekit-cta-link--caps":y,"googlesitekit-cta-link--danger":x,"googlesitekit-cta-link--disabled":N,"googlesitekit-cta-link--standalone":V,"googlesitekit-cta-link--link-button":q,"googlesitekit-cta-link--no-flex":!!P}),disabled:N,href:"LINK"!==Z&&"EXTERNAL_LINK"!==Z||N?void 0:M,onClick:z,rel:"EXTERNAL_LINK"===Z?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===Z?"_blank":void 0,to:K},$),!!ee&&e.createElement(b.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},k),!!te&&e.createElement(b.a,{marginLeft:5},te))}));E.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=E}).call(this,n(4))},201:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeSingleRow}));var r=n(1),i=n.n(r),a=n(0);function SettingsNoticeSingleRow(t){var n=t.notice,r=t.LearnMore,i=t.CTA;return e.createElement(a.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),i&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(i,null)))}SettingsNoticeSingleRow.propTypes={notice:i.a.node.isRequired,LearnMore:i.a.elementType,CTA:i.a.elementType}}).call(this,n(4))},204:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OverlayNotification}));var r=n(591),i=n(11),a=n.n(i),o=n(1),c=n.n(o),l=n(0),s=n(3),u=n(23),d=n(24);function OverlayNotification(t){var n=t.className,i=t.children,o=t.GraphicDesktop,c=t.GraphicMobile,f=t.notificationID,g=t.onShow,m=t.shouldShowNotification,p=Object(d.e)(),h=Object(s.useSelect)((function(e){return e(u.b).isShowingOverlayNotification(f)})),v=Object(s.useDispatch)(u.b).setOverlayNotificationToShow;if(Object(l.useEffect)((function(){m&&!h&&(v(f),null==g||g())}),[h,f,g,v,m]),!m||!h)return null;var b=a()("googlesitekit-overlay-notification",n);return p===d.b?e.createElement("div",{className:b},i,c&&e.createElement(c,null)):e.createElement(r.a,{direction:"up",in:h},e.createElement("div",{className:b},o&&e.createElement(o,null),i))}OverlayNotification.propTypes={className:c.a.string,children:c.a.node,GraphicDesktop:c.a.elementType,GraphicMobile:c.a.elementType,onShow:c.a.func,notificationID:c.a.string.isRequired,shouldShowNotification:c.a.bool}}).call(this,n(4))},210:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeMultiRow}));var r=n(1),i=n.n(r),a=n(0);function SettingsNoticeMultiRow(t){var n=t.notice,r=t.LearnMore,i=t.CTA,o=t.children;return e.createElement(a.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),e.createElement("div",{className:"googlesitekit-settings-notice__inner-row"},e.createElement("div",{className:"googlesitekit-settings-notice__children-container"},o),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),i&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(i,null))))}SettingsNoticeMultiRow.propTypes={children:i.a.node.isRequired,notice:i.a.node.isRequired,LearnMore:i.a.elementType,CTA:i.a.elementType}}).call(this,n(4))},211:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(0),l=n(17),s=Object(c.forwardRef)((function(t,n){var r=t.id,i=t.className,a=t.children,s=t.secondaryPane;return e.createElement("section",{id:r,className:o()(i,"googlesitekit-publisher-win"),ref:n},e.createElement(l.e,null,e.createElement(l.k,null,a)),s&&e.createElement(c.Fragment,null,e.createElement("div",{className:"googlesitekit-publisher-win__secondary-pane-divider"}),e.createElement(l.e,{className:"googlesitekit-publisher-win__secondary-pane"},e.createElement(l.k,null,e.createElement(l.a,{className:"googlesitekit-publisher-win__secondary-pane",size:12},s)))))}));s.displayName="Banner",s.propTypes={id:i.a.string,className:i.a.string,secondaryPane:i.a.node},t.a=s}).call(this,n(4))},212:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerActions}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(1),u=n.n(s),d=n(206),f=n(0),g=n(3),m=n(10),p=n(32);function BannerActions(t){var n=t.ctaLink,r=t.ctaLabel,a=t.ctaComponent,c=t.ctaTarget,s=t.ctaCallback,u=t.dismissLabel,h=t.dismissCallback,v=Object(f.useState)(!1),b=l()(v,2),E=b[0],_=b[1],O=Object(d.a)(),y=Object(g.useSelect)((function(e){return!!n&&e(p.a).isNavigatingTo(n)})),k=function(){var e=o()(i.a.mark((function e(){var t,n,r,a=arguments;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(_(!0),t=a.length,n=new Array(t),r=0;r<t;r++)n[r]=a[r];return e.next=4,null==s?void 0:s.apply(void 0,n);case 4:O()&&_(!1);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return n||u||a?e.createElement("div",{className:"googlesitekit-publisher-win__actions"},a,r&&e.createElement(m.SpinnerButton,{className:"googlesitekit-notification__cta",href:n,target:c,onClick:k,disabled:E||y,isSaving:E||y},r),u&&e.createElement(m.Button,{tertiary:n||a,onClick:h,disabled:E||y},u)):null}BannerActions.propTypes={ctaLink:u.a.string,ctaLabel:u.a.string,ctaComponent:u.a.element,ctaTarget:u.a.string,ctaCallback:u.a.func,dismissLabel:u.a.string,dismissCallback:u.a.func}}).call(this,n(4))},213:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerTitle}));var r=n(6),i=n.n(r),a=n(1),o=n.n(a),c=n(11),l=n.n(c),s=n(77);function BannerTitle(t){var n=t.title,r=t.badgeLabel,a=t.WinImageSVG,o=t.winImageFormat,c=void 0===o?"":o,u=t.smallWinImageSVGWidth,d=void 0===u?75:u,f=t.smallWinImageSVGHeight,g=void 0===f?75:f;return n?e.createElement("div",{className:"googlesitekit-publisher-win__title-image-wrapper"},e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n,r&&e.createElement(s.a,{label:r})),a&&e.createElement("div",{className:l()(i()({},"googlesitekit-publisher-win__image-".concat(c),c))},e.createElement(a,{width:d,height:g}))):null}BannerTitle.propTypes={title:o.a.string,badgeLabel:o.a.string,WinImageSVG:o.a.elementType,winImageFormat:o.a.string,smallWinImageSVGWidth:o.a.number,smallWinImageSVGHeight:o.a.number}}).call(this,n(4))},216:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(0),u=n(3),d=n(13),f=n(23);function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e){var t=Object(u.useDispatch)(f.b).setValue,n=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.2")})),r=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.4")}));return Object(s.useCallback)(l()(i.a.mark((function a(){var o,c,l,s;return i.a.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(o=document.querySelector("#adminmenu").offsetHeight>0){i.next=7;break}if(!(c=document.getElementById("wp-admin-bar-menu-toggle"))){i.next=7;break}return c.firstChild.click(),i.next=7,new Promise((function(e){setTimeout(e,0)}));case 7:"#adminmenu [href*='page=googlesitekit-dashboard']",(l=!!document.querySelector("".concat("#adminmenu [href*='page=googlesitekit-dashboard']","[aria-haspopup=true]")))&&document.querySelector("#adminmenu [href*='page=googlesitekit-dashboard']").click(),n&&!r&&(s=document.hasFocus,document.hasFocus=function(){return document.hasFocus=s,!1}),t("admin-menu-tooltip",m({isTooltipVisible:!0,rehideAdminMenu:!o,rehideAdminSubMenu:l},e));case 12:case"end":return i.stop()}}),a)}))),[n,r,t,e])}},217:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WarningNotice}));var r=n(11),i=n.n(r),a=n(1),o=n.n(a);function WarningNotice(t){var n=t.children,r=t.className;return e.createElement("div",{className:i()("googlesitekit-warning-notice",r)},n)}WarningNotice.propTypes={children:o.a.node.isRequired,className:o.a.string}}).call(this,n(4))},218:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OptIn}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),f=n(38),g=n(2),m=n(3),p=n(10),h=n(7),v=n(36),b=n(20),E=n(18);function OptIn(t){var n=t.id,r=void 0===n?"googlesitekit-opt-in":n,a=t.name,c=void 0===a?"optIn":a,l=t.className,s=t.trackEventCategory,_=t.alignLeftCheckbox,O=void 0!==_&&_,y=Object(m.useSelect)((function(e){return e(h.a).isTrackingEnabled()})),k=Object(m.useSelect)((function(e){return e(h.a).isSavingTrackingEnabled()})),j=Object(m.useSelect)((function(e){return e(h.a).getErrorForAction("setTrackingEnabled",[!y])})),S=Object(m.useDispatch)(h.a).setTrackingEnabled,w=Object(E.a)(),x=Object(d.useCallback)(function(){var e=o()(i.a.mark((function e(t){var n,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S(!!t.target.checked);case 2:n=e.sent,r=n.response,n.error||(Object(v.a)(r.enabled),r.enabled&&Object(v.b)(s||w,"tracking_optin"));case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[S,s,w]);return e.createElement("div",{className:u()("googlesitekit-opt-in",l)},e.createElement(p.Checkbox,{id:r,name:c,value:"1",checked:y,disabled:k,onChange:x,loading:void 0===y,alignLeft:O},Object(f.a)(Object(g.__)("<span>Help us improve Site Kit by sharing anonymous usage data.</span> <span>All collected data is treated in accordance with the <a>Google Privacy Policy.</a></span>","google-site-kit"),{a:e.createElement(b.a,{key:"link",href:"https://policies.google.com/privacy",external:!0}),span:e.createElement("span",null)})),(null==j?void 0:j.message)&&e.createElement("div",{className:"googlesitekit-error-text"},null==j?void 0:j.message))}OptIn.propTypes={id:l.a.string,name:l.a.string,className:l.a.string,trackEventCategory:l.a.string,alignLeftCheckbox:l.a.bool}}).call(this,n(4))},22:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return i})),n.d(t,"o",(function(){return a})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return l})),n.d(t,"s",(function(){return s})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return f})),n.d(t,"k",(function(){return g})),n.d(t,"u",(function(){return m})),n.d(t,"v",(function(){return p})),n.d(t,"q",(function(){return h})),n.d(t,"p",(function(){return v})),n.d(t,"b",(function(){return b})),n.d(t,"e",(function(){return E})),n.d(t,"a",(function(){return _})),n.d(t,"d",(function(){return O})),n.d(t,"c",(function(){return y})),n.d(t,"f",(function(){return k})),n.d(t,"g",(function(){return j}));var r="mainDashboard",i="entityDashboard",a="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",l="activation",s="splash",u="adminBar",d="adminBarViewOnly",f="settings",g="adBlockingRecovery",m="wpDashboard",p="wpDashboardViewOnly",h="moduleSetup",v="metricSelection",b="key-metrics",E="traffic",_="content",O="speed",y="monetization",k=[r,i,a,o,c,s,f,h,v],j=[a,o,d,p]},220:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Item}));var r=n(1),i=n.n(r);function Item(t){var n=t.icon,r=t.label;return e.createElement("div",{className:"googlesitekit-user-menu__item"},e.createElement("div",{className:"googlesitekit-user-menu__item-icon"},n),e.createElement("span",{className:"googlesitekit-user-menu__item-label"},r))}Item.propTypes={icon:i.a.node,label:i.a.string}}).call(this,n(4))},221:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdminMenuTooltip}));var r=n(0),i=n(3),a=n(223),o=n(23),c=n(9),l=n(18);function AdminMenuTooltip(){var t=Object(l.a)(),n=Object(i.useDispatch)(o.b).setValue,s=Object(i.useSelect)((function(e){return e(o.b).getValue("admin-menu-tooltip")||{isTooltipVisible:!1}})),u=s.isTooltipVisible,d=void 0!==u&&u,f=s.rehideAdminMenu,g=void 0!==f&&f,m=s.rehideAdminSubMenu,p=void 0!==m&&m,h=s.tooltipSlug,v=s.title,b=s.content,E=s.dismissLabel,_=Object(r.useCallback)((function(){var e;g&&(document.querySelector("#adminmenu").offsetHeight>0&&(null===(e=document.getElementById("wp-admin-bar-menu-toggle"))||void 0===e||e.click()));p&&document.querySelector("body").click(),h&&Object(c.I)("".concat(t,"_").concat(h),"tooltip_dismiss"),n("admin-menu-tooltip",void 0)}),[g,p,n,h,t]);return d?e.createElement(a.a,{target:'#adminmenu [href*="page=googlesitekit-settings"]',slug:"ga4-activation-banner-admin-menu-tooltip",title:v,content:b,dismissLabel:E,onView:function(){Object(c.I)("".concat(t,"_").concat(h),"tooltip_view")},onDismiss:_}):null}}).call(this,n(4))},222:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var r=n(1),i=n.n(r),a=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var r="win-warning"===n?e.createElement(a.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},r))}BannerIcon.propTypes={type:i.a.string}}).call(this,n(4))},223:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return JoyrideTooltip}));var i=n(6),a=n.n(i),o=n(15),c=n.n(o),l=n(1),s=n(30),u=n(421),d=n(0),f=n(107),g=n(72),m=n(90);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function JoyrideTooltip(t){var n=t.title,i=t.content,a=t.dismissLabel,o=t.target,l=t.cta,p=void 0!==l&&l,v=t.className,b=t.styles,E=void 0===b?{}:b,_=t.slug,O=void 0===_?"":_,y=t.onDismiss,k=void 0===y?function(){}:y,j=t.onView,S=void 0===j?function(){}:j,w=t.onTourStart,x=void 0===w?function(){}:w,C=t.onTourEnd,N=void 0===C?function(){}:C,A=function(){return!!e.document.querySelector(o)},T=Object(d.useState)(A),D=c()(T,2),R=D[0],B=D[1];if(Object(u.a)((function(){A()&&B(!0)}),R?null:250),Object(d.useEffect)((function(){if(R&&e.ResizeObserver){var t=e.document.querySelector(o),n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}),[o,R]),!R)return null;var M=[{title:n,target:o,content:i,disableBeacon:!0,isFixed:!0,placement:"auto",cta:p,className:v}],I={close:a,last:a};return r.createElement(g.a,{slug:O},r.createElement(s.e,{callback:function(t){switch(t.type){case s.b.TOUR_START:x(),e.document.body.classList.add("googlesitekit-showing-tooltip");break;case s.b.TOUR_END:N(),e.document.body.classList.remove("googlesitekit-showing-tooltip");break;case s.b.STEP_AFTER:k();break;case s.b.TOOLTIP:S()}},disableOverlay:!0,disableScrolling:!0,spotlightPadding:0,floaterProps:m.b,locale:I,steps:M,styles:h(h(h({},m.c),E),{},{options:h(h({},m.c.options),null==E?void 0:E.options),spotlight:h(h({},m.c.spotlight),null==E?void 0:E.spotlight)}),tooltipComponent:f.a,run:!0}))}JoyrideTooltip.propTypes={title:l.PropTypes.node,content:l.PropTypes.string,dismissLabel:l.PropTypes.string,target:l.PropTypes.string.isRequired,onDismiss:l.PropTypes.func,onShow:l.PropTypes.func,className:l.PropTypes.string,styles:l.PropTypes.object,slug:l.PropTypes.string,onView:l.PropTypes.func}}).call(this,n(28),n(4))},224:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Root}));var r=n(15),i=n.n(r),a=n(1),o=n.n(a),c=n(432),l=n(534),s=n(0),u=n(3),d=n.n(u),f=n(225),g=n(229),m=n(57),p=n(230),h=n(232),v=n(233),b=n(61),E=n(160),_=n(173);function Root(t){var n=t.children,r=t.registry,a=t.viewContext,o=void 0===a?null:a,d=c.a,O=Object(s.useState)({key:"Root",value:!0}),y=i()(O,1)[0];return e.createElement(s.StrictMode,null,e.createElement(E.a,{value:y},e.createElement(u.RegistryProvider,{value:r},e.createElement(g.a,{value:m.a},e.createElement(b.a,{value:o},e.createElement(l.a,{theme:d()},e.createElement(f.a,null,e.createElement(h.a,null,n,o&&e.createElement(v.a,null)),Object(_.a)(o)&&e.createElement(p.a,null))))))))}Root.propTypes={children:o.a.node,registry:o.a.object,viewContext:o.a.string.isRequired},Root.defaultProps={registry:d.a}}).call(this,n(4))},225:function(e,t,n){"use strict";(function(e,r){var i=n(51),a=n.n(i),o=n(53),c=n.n(o),l=n(68),s=n.n(l),u=n(69),d=n.n(u),f=n(49),g=n.n(f),m=n(1),p=n.n(m),h=n(0),v=n(2),b=n(172),E=n(61),_=n(197),O=n(9);function y(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=g()(e);if(t){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d()(this,n)}}var k=function(t){s()(ErrorHandler,t);var n=y(ErrorHandler);function ErrorHandler(e){var t;return a()(this,ErrorHandler),(t=n.call(this,e)).state={error:null,info:null,copied:!1},t}return c()(ErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t,info:n}),Object(O.I)("react_error","handle_".concat(this.context||"unknown","_error"),"".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,i=t.info;return n?r.createElement(_.a,{id:"googlesitekit-error",className:"googlesitekit-error-handler",title:Object(v.__)("Site Kit encountered an error","google-site-kit"),description:r.createElement(b.a,{message:n.message,componentStack:i.componentStack}),isDismissible:!1,format:"small",type:"win-error"},r.createElement("pre",{className:"googlesitekit-overflow-auto"},n.message,i.componentStack)):e}}]),ErrorHandler}(h.Component);k.contextType=E.b,k.propTypes={children:p.a.node.isRequired},t.a=k}).call(this,n(28),n(4))},226:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var r=n(1),i=n.n(r),a=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var r="win-warning"===n?e.createElement(a.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},r))}BannerIcon.propTypes={type:i.a.string}}).call(this,n(4))},227:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerLogo}));var r=n(1),i=n.n(r),a=n(17),o=n(155),c=n(198);function BannerLogo(t){var n=t.module,r=t.moduleName;return e.createElement(a.a,{size:12},e.createElement("div",{className:"googlesitekit-publisher-win__logo"},n&&e.createElement(c.a,{slug:n,size:19}),!n&&e.createElement(o.a,{height:"34",width:"32"})),r&&e.createElement("div",{className:"googlesitekit-publisher-win__module-name"},r))}BannerLogo.propTypes={module:i.a.string,moduleName:i.a.string}}).call(this,n(4))},228:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerDescription}));var r=n(1),i=n.n(r),a=n(0),o=n(75),c=n(20),l=n(86);function BannerDescription(t){var n=t.description,r=t.learnMoreLabel,i=t.learnMoreURL,s=t.learnMoreTarget,u=t.learnMoreDescription,d=t.onLearnMoreClick;if(!n)return null;var f;return r&&(f=e.createElement(a.Fragment,null,e.createElement(c.a,{onClick:function(e){e.persist(),null==d||d()},href:i,external:s===l.a.EXTERNAL},r),u)),e.createElement("div",{className:"googlesitekit-publisher-win__desc"},Object(a.isValidElement)(n)?e.createElement(a.Fragment,null,n,f&&e.createElement("p",null,f)):e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.a)(n,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",f))}BannerDescription.propTypes={description:i.a.node,learnMoreURL:i.a.string,learnMoreDescription:i.a.string,learnMoreLabel:i.a.string,learnMoreTarget:i.a.oneOf(Object.values(l.a)),onLearnMoreClick:i.a.func}}).call(this,n(4))},229:function(e,t,n){"use strict";var r=n(158),i=(r.a.Consumer,r.a.Provider);t.a=i},23:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r="core/ui",i="activeContextID"},230:function(e,t,n){"use strict";(function(e){var r=n(3),i=n(231),a=n(7);t.a=function PermissionsModal(){return Object(r.useSelect)((function(e){return e(a.a).isAuthenticated()}))?e.createElement(i.a,null):null}}).call(this,n(4))},231:function(e,t,n){"use strict";(function(e,r){var i=n(5),a=n.n(i),o=n(16),c=n.n(o),l=n(2),s=n(0),u=n(3),d=n(109),f=n(29),g=n(32),m=n(7),p=n(129),h=n(72);t.a=function AuthenticatedPermissionsModal(){var t,n,i,o,v=Object(u.useRegistry)(),b=Object(u.useSelect)((function(e){return e(m.a).getPermissionScopeError()})),E=Object(u.useSelect)((function(e){return e(m.a).getUnsatisfiedScopes()})),_=Object(u.useSelect)((function(t){var n,r,i;return t(m.a).getConnectURL({additionalScopes:null==b||null===(n=b.data)||void 0===n?void 0:n.scopes,redirectURL:(null==b||null===(r=b.data)||void 0===r?void 0:r.redirectURL)||e.location.href,errorRedirectURL:null==b||null===(i=b.data)||void 0===i?void 0:i.errorRedirectURL})})),O=Object(u.useDispatch)(m.a).clearPermissionScopeError,y=Object(u.useDispatch)(g.a).navigateTo,k=Object(u.useDispatch)(f.a).setValues,j=Object(s.useCallback)((function(){O()}),[O]),S=Object(s.useCallback)(c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return k(m.d,{permissionsError:b}),e.next=3,Object(p.c)(v);case 3:y(_);case 4:case"end":return e.stop()}}),e)}))),[v,_,y,b,k]);return Object(s.useEffect)((function(){(function(){var e=c()(a.a.mark((function e(){var t,n,r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(null==b||null===(t=b.data)||void 0===t?void 0:t.skipModal)||!(null==b||null===(n=b.data)||void 0===n||null===(r=n.scopes)||void 0===r?void 0:r.length)){e.next=3;break}return e.next=3,S();case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[S,b]),b?(null==b||null===(t=b.data)||void 0===t||null===(n=t.scopes)||void 0===n?void 0:n.length)?(null==b||null===(i=b.data)||void 0===i?void 0:i.skipModal)||E&&(null==b||null===(o=b.data)||void 0===o?void 0:o.scopes.every((function(e){return E.includes(e)})))?null:r.createElement(h.a,null,r.createElement(d.a,{title:Object(l.__)("Additional Permissions Required","google-site-kit"),subtitle:b.message,confirmButton:Object(l.__)("Proceed","google-site-kit"),dialogActive:!0,handleConfirm:S,handleDialog:j,medium:!0})):(e.console.warn("permissionsError lacks scopes array to use for redirect, so not showing the PermissionsModal. permissionsError was:",b),null):null}}).call(this,n(28),n(4))},232:function(e,t,n){"use strict";var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(0),u=n(3),d=n(129);t.a=function RestoreSnapshots(e){var t=e.children,n=Object(u.useRegistry)(),r=Object(s.useState)(!1),a=l()(r,2),c=a[0],f=a[1];return Object(s.useEffect)((function(){c||o()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(d.b)(n);case 2:f(!0);case 3:case"end":return e.stop()}}),e)})))()}),[n,c]),c?t:null}},233:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return FeatureTours}));var i=n(81),a=n(0),o=n(3),c=n(7),l=n(18),s=n(90);function FeatureTours(){var t=Object(l.a)(),n=Object(o.useDispatch)(c.a).triggerTourForView;Object(i.a)((function(){n(t)}));var u=Object(o.useSelect)((function(e){return e(c.a).getCurrentTour()}));return Object(a.useEffect)((function(){if(u){var t=document.getElementById("js-googlesitekit-main-dashboard");if(t){var n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}}),[u]),u?r.createElement(s.a,{tourID:u.slug,steps:u.steps,gaEventCategory:u.gaEventCategory,callback:u.callback}):null}}).call(this,n(28),n(4))},234:function(e,t,n){"use strict";(function(e){var r=n(15),i=n.n(r),a=n(1),o=n.n(a),c=n(11),l=n.n(c),s=n(590),u=n(2),d=n(0),f=n(3),g=n(256),m=n(276),p=n(280),h=n(7),v=n(17),b=n(284),E=n(291),_=n(293),O=n(34),y=n(52),k=n(20),j=n(299),S=n(13),w=n(300);function Header(t){var n,r=t.children,a=t.subHeader,o=t.showNavigation,c=!!Object(y.c)(),x=Object(O.a)();Object(w.a)();var C=Object(f.useSelect)((function(e){return e(S.c).getAdminURL("googlesitekit-dashboard")})),N=Object(f.useSelect)((function(e){return e(h.a).isAuthenticated()})),A=Object(s.a)({childList:!0}),T=i()(A,2),D=T[0],R=!!(null===(n=T[1].target)||void 0===n?void 0:n.childElementCount);return e.createElement(d.Fragment,null,e.createElement("header",{className:l()("googlesitekit-header",{"googlesitekit-header--has-subheader":R,"googlesitekit-header--has-navigation":o})},e.createElement(v.e,null,e.createElement(v.k,null,e.createElement(v.a,{smSize:1,mdSize:2,lgSize:4,className:"googlesitekit-header__logo",alignMiddle:!0},e.createElement(k.a,{"aria-label":Object(u.__)("Go to dashboard","google-site-kit"),className:"googlesitekit-header__logo-link",href:C},e.createElement(g.a,null))),e.createElement(v.a,{smSize:3,mdSize:6,lgSize:8,className:"googlesitekit-header__children",alignMiddle:!0},r,!N&&c&&x&&e.createElement(_.a,null),N&&!x&&e.createElement(m.a,null))))),e.createElement("div",{className:"googlesitekit-subheader",ref:D},e.createElement(p.a,null),a),o&&e.createElement(b.a,null),c&&e.createElement(j.a,null),e.createElement(E.a,null))}Header.displayName="Header",Header.propTypes={children:o.a.node,subHeader:o.a.element,showNavigation:o.a.bool},Header.defaultProps={children:null,subHeader:null},t.a=Header}).call(this,n(4))},235:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return HelpMenu}));var r=n(15),i=n.n(r),a=n(1),o=n.n(a),c=n(209),l=n(0),s=n(56),u=n(2),d=n(3),f=n(10),g=n(301),m=n(112),p=n(9),h=n(164),v=n(19),b=n(18),E=n(13);function HelpMenu(t){var n=t.children,r=Object(l.useState)(!1),a=i()(r,2),o=a[0],_=a[1],O=Object(l.useRef)(),y=Object(b.a)();Object(c.a)(O,(function(){return _(!1)})),Object(m.a)([s.c,s.f],O,(function(){return _(!1)}));var k=Object(d.useSelect)((function(e){return e(v.a).isModuleActive("adsense")})),j=Object(l.useCallback)((function(){o||Object(p.I)("".concat(y,"_headerbar"),"open_helpmenu"),_(!o)}),[o,y]),S=Object(l.useCallback)((function(){_(!1)}),[]),w=Object(d.useSelect)((function(e){return e(E.c).getDocumentationLinkURL("fix-common-issues")}));return e.createElement("div",{ref:O,className:"googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},e.createElement(f.Button,{"aria-controls":"googlesitekit-help-menu","aria-expanded":o,"aria-label":Object(u.__)("Help","google-site-kit"),"aria-haspopup":"menu",className:"googlesitekit-header__dropdown googlesitekit-border-radius-round googlesitekit-button-icon googlesitekit-help-menu__button mdc-button--dropdown",icon:e.createElement(g.a,{width:"20",height:"20"}),onClick:j,text:!0,tooltipEnterDelayInMS:500}),e.createElement(f.Menu,{className:"googlesitekit-width-auto",menuOpen:o,id:"googlesitekit-help-menu",onSelected:S},n,e.createElement(h.a,{gaEventLabel:"fix_common_issues",href:w},Object(u.__)("Fix common issues","google-site-kit")),e.createElement(h.a,{gaEventLabel:"documentation",href:"https://sitekit.withgoogle.com/documentation/"},Object(u.__)("Read help docs","google-site-kit")),e.createElement(h.a,{gaEventLabel:"support_forum",href:"https://wordpress.org/support/plugin/google-site-kit/"},Object(u.__)("Get support","google-site-kit")),k&&e.createElement(h.a,{gaEventLabel:"adsense_help",href:"https://support.google.com/adsense/"},Object(u.__)("Get help with AdSense","google-site-kit"))))}HelpMenu.propTypes={children:o.a.node}}).call(this,n(4))},24:function(e,t,n){"use strict";n.d(t,"d",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return l}));var r=n(79),i="xlarge",a="desktop",o="tablet",c="small";function l(){var e=Object(r.a)();return e>1280?i:e>960?a:e>600?o:c}},242:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationWithSVG}));var r=n(21),i=n.n(r),a=n(11),o=n.n(a),c=n(24),l=n(17),s=n(255);function NotificationWithSVG(t){var n=t.id,r=t.title,a=t.description,u=t.actions,d=t.SVG,f=t.primaryCellSizes,g=t.SVGCellSizes,m=Object(c.e)(),p={mdSize:(null==g?void 0:g.md)||8,lgSize:(null==g?void 0:g.lg)||6};return m===c.c&&(p={mdSize:(null==g?void 0:g.md)||8}),m===c.b&&(p={smSize:(null==g?void 0:g.sm)||12}),e.createElement("div",{className:"googlesitekit-widget-context"},e.createElement(l.e,{className:"googlesitekit-widget-area"},e.createElement(l.k,null,e.createElement(l.a,{size:12},e.createElement("div",{className:o()("googlesitekit-widget","googlesitekit-widget--no-padding","googlesitekit-setup-cta-banner","googlesitekit-setup-cta-banner--".concat(n))},e.createElement("div",{className:"googlesitekit-widget__body"},e.createElement(l.e,{collapsed:!0},e.createElement(l.k,null,e.createElement(l.a,{smSize:(null==f?void 0:f.sm)||12,mdSize:(null==f?void 0:f.md)||8,lgSize:(null==f?void 0:f.lg)||6,className:"googlesitekit-setup-cta-banner__primary-cell"},e.createElement("h3",{className:"googlesitekit-setup-cta-banner__title"},r),a,e.createElement(s.a,{id:n}),u),e.createElement(l.a,i()({alignBottom:!0,className:"googlesitekit-setup-cta-banner__svg-wrapper--".concat(n)},p),e.createElement(d,null))))))))))}}).call(this,n(4))},243:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return m})),n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return v}));var r=n(21),i=n.n(r),a=n(63),o=n.n(a),c=n(267),l=n(322),s=n(323),u=n(244),d=n(268),f=n(324),g=n(0),m=o()((function(e){return{widgetSlug:e,Widget:p(e)(c.a),WidgetRecoverableModules:p(e)(d.a),WidgetReportZero:p(e)(l.a),WidgetReportError:p(e)(s.a),WidgetNull:p(e)(u.a)}}));function p(t){return function(n){var r=Object(g.forwardRef)((function(r,a){return e.createElement(n,i()({},r,{ref:a,widgetSlug:t}))}));return r.displayName="WithWidgetSlug",(n.displayName||n.name)&&(r.displayName+="(".concat(n.displayName||n.name,")")),r}}var h=function(t){var n=m(t);return function(t){function DecoratedComponent(r){return e.createElement(t,i()({},r,n))}return DecoratedComponent.displayName="WithWidgetComponentProps",(t.displayName||t.name)&&(DecoratedComponent.displayName+="(".concat(t.displayName||t.name,")")),DecoratedComponent}},v=function(t){return function(n){function DecoratedComponent(r){return e.createElement(n,i()({},r,{WPDashboardReportError:p(t)(f.a)}))}return DecoratedComponent.displayName="WithWPDashboardWidgetComponentProps",(n.displayName||n.name)&&(DecoratedComponent.displayName+="(".concat(n.displayName||n.name,")")),DecoratedComponent}}}).call(this,n(4))},244:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetNull}));var r=n(6),i=n.n(r),a=n(1),o=n.n(a),c=n(143),l=n(74);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var u={};function WidgetNull(t){var n=t.widgetSlug;return Object(c.a)(n,l.a,u),e.createElement(l.a,null)}WidgetNull.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:o.a.string.isRequired},l.a.propTypes)}).call(this,n(4))},246:function(e,t,n){"use strict";n.d(t,"d",(function(){return f})),n.d(t,"b",(function(){return m})),n.d(t,"c",(function(){return p.a})),n.d(t,"g",(function(){return p.c})),n.d(t,"a",(function(){return c.a})),n.d(t,"f",(function(){return h})),n.d(t,"e",(function(){return s}));var r=n(15),i=n.n(r),a=n(27),o=n.n(a),c=n(118),l=n(74);function s(e){return!!e&&e.Component===l.a}function u(e,t){if(9!==t)return[e,t];for(var n=(e=o()(e)).length-1;0!==t&&n>=0;)3===e[n]?(t-=3,e[n]=4):6===e[n]&&(t-=6,e[n]=8),n--;return[e,t]}function d(e,t){return(Array.isArray(t.width)?t.width:[t.width]).map((function(t){return{counter:e+c.c[t],width:t}}))}function f(e,t){var n=[],r=[];if(!(null==e?void 0:e.length))return{columnWidths:n,rowIndexes:r};var a=0,o=0,l=function(e,t){return e.counter-t.counter},f=function(e,t){var n=e.counter;return t.counter-n},g=function(e){return e.counter<=12};if(e.forEach((function(m,p){if(s(t[m.slug]))return n.push(0),void r.push(o);var h=d(a,m),v=function(e,t,n){for(;++e<t.length;)if(!s(n[t[e].slug]))return t[e];return null}(p,e,t);null!==v&&0!==d(h.sort(l)[0].counter,v).filter(g).length||h.some(g)&&(h=(h=h.sort(f)).filter(g));var b=h[0].width;if(r.push(o),(a+=c.c[b])>12){if(a-=c.c[b],r[p]++,9===a){var E=u(n,a),_=i()(E,2);n=_[0],a=_[1]}a=c.c[b],o++}else 12===a&&(a=0,o++);n.push(c.c[b])})),9===a){var m=u(n,a),p=i()(m,2);n=p[0],a=p[1]}return{columnWidths:n,rowIndexes:r}}var g=n(14);function m(e,t,n){var r=n.columnWidths,i=n.rowIndexes,a=[],l=o()(r);if(!(null==e?void 0:e.length))return{gridColumnWidths:l,overrideComponents:a};var s=null,u=-1,d=[];if(function(e,t){for(var n={},r=0;r<e.length;r++){var i,a=e[r],o=null==t?void 0:t[a.slug],l=null==o?void 0:o.Component,s=null==o||null===(i=o.metadata)||void 0===i?void 0:i.moduleSlug,u=c.b.includes(l);if(!l||!s||!u)return!1;if(n[s]){if(n[s]!==l)return!1}else n[s]=l}return!(Object.keys(n).length>1)}(e,t)){var f=Array.from({length:e.length-1}).fill(0);return{overrideComponents:[t[e[0].slug]],gridColumnWidths:[12].concat(o()(f))}}return e.forEach((function(n,o){var c,f,m,p,h;if(a.push(null),s=t[n.slug],u=i[o],s)if(f=s,m=t[null===(c=e[o+1])||void 0===c?void 0:c.slug],p=u,h=i[o+1],p===h&&Object(g.isEqual)(f,m))d.push(r[o]),l[o]=0;else if(d.length>0){d.push(r[o]);var v=d.reduce((function(e,t){return e+t}),0);a[o]=s,l[o]=v,d=[]}})),{gridColumnWidths:l,overrideComponents:a}}var p=n(243);function h(e){return(Array.isArray(e)?e:[e]).filter((function(e){return"string"==typeof e&&e.length>0}))}},249:function(e,t,n){"use strict";(function(e){var r=n(15),i=n.n(r),a=n(0);t.a=function(t,n){var r=Object(a.useState)(null),o=i()(r,2),c=o[0],l=o[1];return Object(a.useEffect)((function(){if(t.current&&"function"==typeof e.IntersectionObserver){var r=new e.IntersectionObserver((function(e){l(e[e.length-1])}),n);return r.observe(t.current),function(){l(null),r.disconnect()}}return function(){}}),[t.current,n.threshold,n.root,n.rootMargin]),c}}).call(this,n(28))},255:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Error}));var r=n(1),i=n.n(r),a=n(0),o=n(3),c=n(13),l=n(54);function Error(t){var n=t.id,r=Object(o.useSelect)((function(e){return e(c.c).getError("notificationAction",[n])})),i=Object(o.useDispatch)(c.c).clearError;return Object(a.useEffect)((function(){return function(){i("notificationAction",[n])}}),[i,n]),r?e.createElement(l.a,{message:r.message}):null}Error.propTypes={id:i.a.string}}).call(this,n(4))},256:function(e,t,n){"use strict";(function(e){var r=n(2),i=n(155),a=n(257),o=n(105);t.a=function Logo(){return e.createElement("div",{className:"googlesitekit-logo","aria-hidden":"true"},e.createElement(i.a,{className:"googlesitekit-logo__logo-g",height:"34",width:"32"}),e.createElement(a.a,{className:"googlesitekit-logo__logo-sitekit",height:"26",width:"99"}),e.createElement(o.a,null,Object(r.__)("Site Kit by Google Logo","google-site-kit")))}}).call(this,n(4))},257:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M62.09 1.664h3.038v.1L58.34 9.593l7.241 10.224v.1H62.7L56.755 11.4 53.95 14.64v5.278h-2.351V1.664h2.35v9.415h.1l8.04-9.415zM69.984 3.117c0 .454-.166.853-.487 1.175-.322.322-.71.488-1.176.488-.455 0-.854-.166-1.175-.488a1.599 1.599 0 01-.488-1.175c0-.466.166-.854.488-1.176.321-.322.71-.488 1.175-.488.455 0 .854.166 1.176.488.332.333.487.72.487 1.176zm-.476 4.313v12.498h-2.351V7.43h2.35zM77.016 20.128c-1.02 0-1.864-.31-2.54-.943-.676-.632-1.02-1.508-1.031-2.628V9.57h-2.196V7.43h2.196V3.603h2.35V7.43h3.061v2.14h-3.06v6.222c0 .831.166 1.397.488 1.696.321.3.687.444 1.097.444.189 0 .366-.022.555-.067.188-.044.344-.1.499-.166l.743 2.096c-.632.222-1.342.333-2.162.333zM2.673 18.952C1.375 18.009.488 16.678 0 14.97l2.883-1.176c.289 1.076.799 1.94 1.542 2.628.732.677 1.619 1.02 2.65 1.02.965 0 1.774-.244 2.45-.742.677-.5 1.01-1.187 1.01-2.052 0-.798-.3-1.453-.887-1.974-.588-.521-1.62-1.042-3.094-1.564l-1.22-.432C4.025 10.224 2.928 9.57 2.04 8.716 1.153 7.862.71 6.742.71 5.346c0-.966.266-1.853.787-2.673C2.018 1.852 2.75 1.209 3.693.72 4.624.244 5.678 0 6.864 0c1.708 0 3.072.41 4.081 1.242 1.02.832 1.697 1.752 2.04 2.795L10.236 5.2c-.2-.621-.576-1.164-1.142-1.63-.565-.477-1.286-.71-2.173-.71s-1.641.222-2.251.676c-.61.455-.91 1.032-.91 1.742 0 .676.278 1.22.82 1.663.544.432 1.398.854 2.563 1.253l1.22.41c1.674.577 2.96 1.342 3.88 2.274.921.931 1.376 2.184 1.376 3.748 0 1.275-.322 2.34-.976 3.193a6.01 6.01 0 01-2.495 1.919 8.014 8.014 0 01-3.116.621c-1.62 0-3.072-.466-4.358-1.408zM15.969 3.449a1.95 1.95 0 01-.588-1.43c0-.566.2-1.043.588-1.431A1.95 1.95 0 0117.399 0c.566 0 1.043.2 1.43.588.389.388.588.865.588 1.43 0 .566-.2 1.043-.587 1.43a1.95 1.95 0 01-1.43.589c-.566-.012-1.043-.2-1.431-.588zm-.067 2.595h2.994v13.883h-2.994V6.044zM25.405 19.85c-.543-.2-.986-.466-1.33-.788-.776-.776-1.176-1.84-1.176-3.182V8.683h-2.428v-2.64h2.428V2.13h2.994v3.926h3.372v2.639h-3.372v6.531c0 .743.145 1.276.433 1.575.277.366.743.543 1.42.543.31 0 .576-.044.82-.122.233-.077.488-.21.765-.399v2.917c-.599.277-1.32.41-2.173.41a5.01 5.01 0 01-1.753-.3zM33.623 19.407a6.63 6.63 0 01-2.529-2.628c-.61-1.12-.909-2.373-.909-3.77 0-1.332.3-2.551.887-3.693.588-1.132 1.409-2.04 2.462-2.706 1.053-.666 2.251-1.01 3.593-1.01 1.397 0 2.606.311 3.637.921a6.123 6.123 0 012.34 2.528c.532 1.076.799 2.274.799 3.627 0 .255-.023.576-.078.953H33.179c.111 1.287.566 2.285 1.375 2.983a4.162 4.162 0 002.817 1.043c.854 0 1.597-.189 2.218-.588a4.266 4.266 0 001.508-1.597l2.528 1.198c-.654 1.142-1.508 2.04-2.561 2.694-1.054.655-2.318.976-3.782.976-1.364.022-2.584-.288-3.66-.931zm7.23-8.051a3.332 3.332 0 00-.466-1.453c-.277-.477-.687-.887-1.242-1.208-.554-.322-1.23-.488-2.03-.488-.964 0-1.773.288-2.439.853-.665.566-1.12 1.342-1.375 2.296h7.552z",fill:"#5F6368"});t.a=function SvgLogoSitekit(e){return r.createElement("svg",i({viewBox:"0 0 80 21",fill:"none"},e),a)}},267:function(e,t,n){"use strict";(function(e){var r=n(11),i=n.n(r),a=n(1),o=n.n(a),c=n(0),l=Object(c.forwardRef)((function(t,n){var r=t.children,a=t.className,o=t.widgetSlug,c=t.noPadding,l=t.Header,s=t.Footer;return e.createElement("div",{className:i()("googlesitekit-widget","googlesitekit-widget--".concat(o),{"googlesitekit-widget--no-padding":c},{"googlesitekit-widget--with-header":l},a),ref:n},l&&e.createElement("div",{className:"googlesitekit-widget__header"},e.createElement(l,null)),e.createElement("div",{className:"googlesitekit-widget__body"},r),s&&e.createElement("div",{className:"googlesitekit-widget__footer"},e.createElement(s,null)))}));l.defaultProps={children:void 0,noPadding:!1},l.propTypes={children:o.a.node,widgetSlug:o.a.string.isRequired,noPadding:o.a.bool,Header:o.a.elementType,Footer:o.a.elementType},t.a=l}).call(this,n(4))},268:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetRecoverableModules}));var r=n(6),i=n.n(r),a=n(21),o=n.n(a),c=n(27),l=n.n(c),s=n(25),u=n.n(s),d=n(1),f=n.n(d),g=n(0),m=n(143),p=n(163);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function WidgetRecoverableModules(t){var n=t.widgetSlug,r=t.moduleSlugs,i=u()(t,["widgetSlug","moduleSlugs"]),a=Object(g.useMemo)((function(){return{moduleSlug:l()(r).sort().join(","),moduleSlugs:r}}),[r]);return Object(m.a)(n,p.a,a),e.createElement(p.a,o()({moduleSlugs:r},i))}WidgetRecoverableModules.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:f.a.string.isRequired},p.a.propTypes)}).call(this,n(4))},273:function(e,t,n){"use strict";(function(e){var r=n(55),i=n.n(r),a=n(274),o=e._googlesitekitAPIFetchData||{},c=o.nonce,l=o.nonceEndpoint,s=o.preloadedData,u=o.rootURL;i.a.nonceEndpoint=l,i.a.nonceMiddleware=i.a.createNonceMiddleware(c),i.a.rootURLMiddleware=i.a.createRootURLMiddleware(u),i.a.preloadingMiddleware=Object(a.a)(s),i.a.use(i.a.nonceMiddleware),i.a.use(i.a.mediaUploadMiddleware),i.a.use(i.a.rootURLMiddleware),i.a.use(i.a.preloadingMiddleware),t.default=i.a}).call(this,n(28))},274:function(e,t,n){"use strict";var r=n(262);t.a=function(e){var t=Object.keys(e).reduce((function(t,n){return t[Object(r.getStablePath)(n)]=e[n],t}),{}),n=!1;return function(e,i){if(n)return i(e);setTimeout((function(){n=!0}),3e3);var a=e.parse,o=void 0===a||a,c=e.path;if("string"==typeof e.path){var l,s=(null===(l=e.method)||void 0===l?void 0:l.toUpperCase())||"GET",u=Object(r.getStablePath)(c);if(o&&"GET"===s&&t[u]){var d=Promise.resolve(t[u].body);return delete t[u],d}if("OPTIONS"===s&&t[s]&&t[s][u]){var f=Promise.resolve(t[s][u]);return delete t[s][u],f}}return i(e)}}},276:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return UserMenu}));var i=n(5),a=n.n(i),o=n(16),c=n.n(o),l=n(15),s=n.n(l),u=n(209),d=n(0),f=n(2),g=n(56),m=n(3),p=n(10),h=n(109),v=n(9),b=n(37),E=n(72),_=n(277),O=n(220),y=n(278),k=n(279),j=n(29),S=n(13),w=n(7),x=n(32),C=n(8),N=n(112),A=n(18);function UserMenu(){var t=Object(m.useSelect)((function(e){return e(S.c).getProxyPermissionsURL()})),n=Object(m.useSelect)((function(e){return e(w.a).getEmail()})),i=Object(m.useSelect)((function(e){return e(w.a).getPicture()})),o=Object(m.useSelect)((function(e){return e(w.a).getFullName()})),l=Object(m.useSelect)((function(e){return e(S.c).getAdminURL("googlesitekit-splash",{googlesitekit_context:"revoked"})})),T=Object(m.useSelect)((function(e){return e(j.a).getValue(C.d,"isAutoCreatingCustomDimensionsForAudience")})),D=Object(d.useState)(!1),R=s()(D,2),B=R[0],M=R[1],I=Object(d.useState)(!1),L=s()(I,2),F=L[0],P=L[1],z=Object(d.useRef)(),H=Object(d.useRef)(),W=Object(A.a)(),G=Object(m.useDispatch)(x.a).navigateTo;Object(u.a)(z,(function(){return P(!1)})),Object(N.a)([g.c,g.f],z,(function(){var e;P(!1),null===(e=H.current)||void 0===e||e.focus()})),Object(d.useEffect)((function(){var t=function(e){g.c===e.keyCode&&(M(!1),P(!1))};return e.addEventListener("keyup",t),function(){e.removeEventListener("keyup",t)}}),[]);var V,U=Object(d.useCallback)((function(){F||Object(v.I)("".concat(W,"_headerbar"),"open_usermenu"),P(!F)}),[F,W]),q=Object(d.useCallback)((function(){M(!B),P(!1)}),[B]),K=Object(d.useCallback)(function(){var e=c()(a.a.mark((function e(n,r){var i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:i=r.detail.item,e.t0=null==i?void 0:i.id,e.next="manage-sites"===e.t0?4:"disconnect"===e.t0?9:11;break;case 4:if(!t){e.next=8;break}return e.next=7,Object(v.I)("".concat(W,"_headerbar_usermenu"),"manage_sites");case 7:G(t);case 8:return e.abrupt("break",12);case 9:return q(),e.abrupt("break",12);case 11:U();case 12:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),[t,U,q,G,W]),X=Object(d.useCallback)(c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return M(!1),e.next=3,Object(b.b)();case 3:return e.next=5,Object(v.I)("".concat(W,"_headerbar_usermenu"),"disconnect_user");case 5:G(l);case 6:case"end":return e.stop()}}),e)}))),[l,G,W]);return n?(o&&n&&(V=Object(f.sprintf)(
/* translators: Account info text. 1: User's (full) name 2: User's email address. */
Object(f.__)("Google Account for %1$s (Email: %2$s)","google-site-kit"),o,n)),o&&!n&&(V=Object(f.sprintf)(
/* translators: Account info text. 1: User's (full) name. */
Object(f.__)("Google Account for %1$s","google-site-kit"),o)),!o&&n&&(V=Object(f.sprintf)(
/* translators: Account info text. 1: User's email address. */
Object(f.__)("Google Account (Email: %1$s)","google-site-kit"),n)),r.createElement(d.Fragment,null,r.createElement("div",{ref:z,className:"googlesitekit-user-selector googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},r.createElement(p.Button,{disabled:T,ref:H,className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--tablet googlesitekit-border-radius-round--phone googlesitekit-border-radius-round googlesitekit-button-icon",text:!0,onClick:U,icon:!!i&&r.createElement("i",{className:"mdc-button__icon mdc-button__account","aria-hidden":"true"},r.createElement("img",{className:"mdc-button__icon--image",src:i,alt:Object(f.__)("User Avatar","google-site-kit")})),"aria-haspopup":"menu","aria-expanded":F,"aria-controls":"user-menu","aria-label":T?void 0:Object(f.__)("Account","google-site-kit"),tooltip:!0,tooltipEnterDelayInMS:500,customizedTooltip:T?null:r.createElement("span",{"aria-label":V},r.createElement("strong",null,Object(f.__)("Google Account","google-site-kit")),r.createElement("br",null),r.createElement("br",null),o,o&&r.createElement("br",null),n)}),r.createElement(p.Menu,{className:"googlesitekit-user-menu",menuOpen:F,onSelected:K,id:"user-menu"},r.createElement("li",null,r.createElement(_.a,null)),!!t&&r.createElement("li",{id:"manage-sites",className:"mdc-list-item",role:"menuitem"},r.createElement(O.a,{icon:r.createElement(k.a,{width:"22"}),label:Object(f.__)("Manage Sites","google-site-kit")})),r.createElement("li",{id:"disconnect",className:"mdc-list-item",role:"menuitem"},r.createElement(O.a,{icon:r.createElement(y.a,{width:"22"}),label:Object(f.__)("Disconnect","google-site-kit")})))),r.createElement(E.a,null,r.createElement(h.a,{dialogActive:B,handleConfirm:X,handleDialog:q,title:Object(f.__)("Disconnect","google-site-kit"),subtitle:Object(f.__)("Disconnecting Site Kit by Google will remove your access to all services. After disconnecting, you will need to re-authorize to restore service.","google-site-kit"),confirmButton:Object(f.__)("Disconnect","google-site-kit"),danger:!0,small:!0})))):null}}).call(this,n(28),n(4))},277:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Details}));var r=n(2),i=n(3),a=n(7);function Details(){var t=Object(i.useSelect)((function(e){return e(a.a).getPicture()})),n=Object(i.useSelect)((function(e){return e(a.a).getFullName()})),o=Object(i.useSelect)((function(e){return e(a.a).getEmail()}));return e.createElement("div",{className:"googlesitekit-user-menu__details","aria-label":Object(r.__)("Google account","google-site-kit")},!!t&&e.createElement("img",{className:"googlesitekit-user-menu__details-avatar",src:t,alt:""}),e.createElement("div",{className:"googlesitekit-user-menu__details-info"},e.createElement("p",{className:"googlesitekit-user-menu__details-info__name"},n),e.createElement("p",{className:"googlesitekit-user-menu__details-info__email","aria-label":Object(r.__)("Email","google-site-kit")},o)))}}).call(this,n(4))},278:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M6.83 2H20a2 2 0 012 2v12c0 .34-.09.66-.23.94L20 15.17V6h-9.17l-4-4zm13.66 19.31L17.17 18H4a2 2 0 01-2-2V4c0-.34.08-.66.23-.94L.69 1.51 2.1.1l19.8 19.8-1.41 1.41zM15.17 16l-10-10H4v10h11.17z",fill:"currentColor"});t.a=function SvgDisconnect(e){return r.createElement("svg",i({viewBox:"0 0 22 22",fill:"none"},e),a)}},279:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M20 0H2C.9 0 0 .9 0 2v12c0 1.1.9 2 2 2h5v2h8v-2h5c1.1 0 2-.9 2-2V2c0-1.1-.9-2-2-2zm0 14H2V2h18v12zm-2-9H7v2h11V5zm0 4H7v2h11V9zM6 5H4v2h2V5zm0 4H4v2h2V9z",fill:"currentColor"});t.a=function SvgManageSites(e){return r.createElement("svg",i({viewBox:"0 0 22 18",fill:"none"},e),a)}},280:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotifications}));var r=n(0),i=n(281),a=n(167),o=n(41);function ErrorNotifications(){return e.createElement(r.Fragment,null,e.createElement(i.a,null),e.createElement(a.a,{areaSlug:o.b.ERRORS}))}}).call(this,n(4))},281:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InternalServerError}));var r=n(3),i=n(13),a=n(196),o=n(191),c=n(111);function InternalServerError(){var t=Object(r.useSelect)((function(e){return e(i.c).getInternalServerError()}));return t?e.createElement(o.a,{className:"googlesitekit-publisher-win googlesitekit-publisher-win--win-error"},e.createElement(a.a,{title:t.title,description:e.createElement(c.a,{text:t.description})})):null}}).call(this,n(4))},282:function(e,t,n){"use strict";n.d(t,"a",(function(){return ViewedStateObserver}));var r=n(1),i=n.n(r),a=n(0),o=n(3),c=n(23),l=n(249),s=n(161);function ViewedStateObserver(e){var t=e.id,n=e.observeRef,r=e.threshold,i=Object(l.a)(n,{threshold:r}),u=Object(o.useDispatch)(c.b).setValue,d=!!(null==i?void 0:i.isIntersecting),f=Object(s.a)(t);return Object(a.useEffect)((function(){!f&&d&&u(s.a.getKey(t),!0)}),[f,d,u,t]),null}ViewedStateObserver.propTypes={id:i.a.string,observeRef:i.a.object,threshold:i.a.number}},283:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return l}));var r=n(21),i=n.n(r),a=n(63),o=n.n(a),c=n(191),l=o()((function(e){return{id:e,Notification:s(e)(c.a)}}));function s(t){return function(n){function WithNotificationID(r){return e.createElement(n,i()({},r,{id:t}))}return WithNotificationID.displayName="WithNotificationID",(n.displayName||n.name)&&(WithNotificationID.displayName+="(".concat(n.displayName||n.name,")")),WithNotificationID}}}).call(this,n(4))},284:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardNavigation}));var r=n(3),i=n(7),a=n(34),o=n(183),c=n(285);function DashboardNavigation(){var t=Object(a.a)(),n=Object(r.useSelect)((function(e){return t?e(i.a).getViewableModules():null})),l=Object(r.useSelect)((function(e){return e(i.a).getKeyMetrics()}));return e.createElement(o.a,{loading:void 0===n||void 0===l,width:"100%",smallHeight:"59px",height:"71px"},e.createElement(c.a,null))}}).call(this,n(4))},285:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return Navigation}));var i=n(27),a=n.n(i),o=n(15),c=n.n(o),l=n(11),s=n.n(l),u=n(14),d=n(81),f=n(153),g=n(0),m=n(2),p=n(3),h=n(286),v=n(287),b=n(288),E=n(289),_=n(290),O=n(22),y=n(7),k=n(47),j=n(23),S=n(71),w=n(52),x=n(24),C=n(93),N=n(9),A=n(18),T=n(34);function D(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return R(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return R(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Navigation(){var t,n=Object(w.c)(),i=Object(g.useRef)(),o=Object(x.e)(),l=null===(t=e.location.hash)||void 0===t?void 0:t.substring(1),R=Object(g.useState)(l),B=c()(R,2),M=B[0],I=B[1],L=Object(g.useState)(l||void 0),F=c()(L,2),P=F[0],z=F[1],H=Object(g.useState)(!1),W=c()(H,2),G=W[0],V=W[1],U=Object(A.a)(),q=Object(T.a)(),K=Object(p.useDispatch)(j.b).setValue,X=Object(p.useSelect)((function(e){return q?e(y.a).getViewableModules():null})),Y=Object(p.useSelect)((function(e){return e(y.a).isKeyMetricsWidgetHidden()})),$={modules:X||void 0},Z=Object(p.useSelect)((function(e){return n===w.b&&!0!==Y&&e(k.a).isWidgetContextActive(S.CONTEXT_MAIN_DASHBOARD_KEY_METRICS,$)})),J=Object(p.useSelect)((function(e){return e(k.a).isWidgetContextActive(n===w.b?S.CONTEXT_MAIN_DASHBOARD_TRAFFIC:S.CONTEXT_ENTITY_DASHBOARD_TRAFFIC,$)})),Q=Object(p.useSelect)((function(e){return e(k.a).isWidgetContextActive(n===w.b?S.CONTEXT_MAIN_DASHBOARD_CONTENT:S.CONTEXT_ENTITY_DASHBOARD_CONTENT,$)})),ee=Object(p.useSelect)((function(e){return e(k.a).isWidgetContextActive(n===w.b?S.CONTEXT_MAIN_DASHBOARD_SPEED:S.CONTEXT_ENTITY_DASHBOARD_SPEED,$)})),te=Object(p.useSelect)((function(e){return e(k.a).isWidgetContextActive(n===w.b?S.CONTEXT_MAIN_DASHBOARD_MONETIZATION:S.CONTEXT_ENTITY_DASHBOARD_MONETIZATION,$)})),ne=Object(g.useCallback)((function(){return Z?O.b:q?J?O.e:Q?O.a:ee?O.d:te?O.c:"":O.e}),[Z,J,Q,ee,te,q]),re=Object(g.useCallback)((function(t){var n,r=t.target.closest(".mdc-chip"),i=null==r||null===(n=r.dataset)||void 0===n?void 0:n.contextId;e.history.replaceState({},"","#".concat(i)),z(i),Object(N.I)("".concat(U,"_navigation"),"tab_select",i),e.scrollTo({top:i!==ne()?Object(C.a)("#".concat(i),o):0,behavior:"smooth"}),setTimeout((function(){K(j.a,i)}),50)}),[o,U,K,ne]);return Object(d.a)((function(){var t=ne();if(!l)return I(t),void setTimeout((function(){return e.history.replaceState({},"","#".concat(t))}));var n=l;(function(e){return!(!Z||e!==O.b)||(!(!J||e!==O.e)||(!(!Q||e!==O.a)||(!(!ee||e!==O.d)||!(!te||e!==O.c))))})(n)||(n=t),K(j.a,n),I(n),setTimeout((function(){var r=n!==t?Object(C.a)("#".concat(n),o):0;e.scrollY!==r?e.scrollTo({top:r,behavior:"smooth"}):K(j.a,void 0)}),50)})),Object(g.useEffect)((function(){var t=function(e){K(j.a,void 0),I(e),z(void 0)},n=Object(u.throttle)((function(n){var r,o,c,l,s=e.scrollY,u=null===(r=document.querySelector(".googlesitekit-entity-header"))||void 0===r||null===(o=r.getBoundingClientRect())||void 0===o?void 0:o.bottom,d=null==i||null===(c=i.current)||void 0===c?void 0:c.getBoundingClientRect(),f=d.bottom,g=d.top,m=[].concat(a()(Z?[O.b]:[]),a()(J?[O.e]:[]),a()(Q?[O.a]:[]),a()(ee?[O.d]:[]),a()(te?[O.c]:[])),p=ne();if(0===s)V(!1);else{var h,v=null===(h=document.querySelector(".googlesitekit-header"))||void 0===h?void 0:h.getBoundingClientRect().bottom;V(g===v)}var b,E=D(m);try{for(E.s();!(b=E.n()).done;){var _=b.value,y=document.getElementById(_);if(y){var k=y.getBoundingClientRect().top-20-(u||f||0);k<0&&(void 0===l||l<k)&&(l=k,p=_)}}}catch(e){E.e(e)}finally{E.f()}if(P)P===p&&t(p);else{var j=e.location.hash;p!==(null==j?void 0:j.substring(1))&&(n&&Object(N.I)("".concat(U,"_navigation"),"tab_scroll",p),e.history.replaceState({},"","#".concat(p)),t(p))}}),150);return e.addEventListener("scroll",n),function(){e.removeEventListener("scroll",n)}}),[P,Z,J,Q,ee,te,U,K,ne]),r.createElement("nav",{className:s()("mdc-chip-set","googlesitekit-navigation","googlesitekit-navigation--".concat(n),{"googlesitekit-navigation--is-sticky":G}),ref:i},Z&&r.createElement(f.Chip,{id:O.b,label:Object(m.__)("Key metrics","google-site-kit"),leadingIcon:r.createElement(h.a,{width:"18",height:"16"}),onClick:re,selected:M===O.b,"data-context-id":O.b}),J&&r.createElement(f.Chip,{id:O.e,label:Object(m.__)("Traffic","google-site-kit"),leadingIcon:r.createElement(v.a,{width:"18",height:"16"}),onClick:re,selected:M===O.e,"data-context-id":O.e}),Q&&r.createElement(f.Chip,{id:O.a,label:Object(m.__)("Content","google-site-kit"),leadingIcon:r.createElement(b.a,{width:"18",height:"18"}),onClick:re,selected:M===O.a,"data-context-id":O.a}),ee&&r.createElement(f.Chip,{id:O.d,label:Object(m.__)("Speed","google-site-kit"),leadingIcon:r.createElement(E.a,{width:"20",height:"16"}),onClick:re,selected:M===O.d,"data-context-id":O.d}),te&&r.createElement(f.Chip,{id:O.c,label:Object(m.__)("Monetization","google-site-kit"),leadingIcon:r.createElement(_.a,{width:"18",height:"16"}),onClick:re,selected:M===O.c,"data-context-id":O.c}))}}).call(this,n(28),n(4))},286:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("rect",{x:.5,width:5,height:5,rx:1,fill:"currentColor"}),o=r.createElement("rect",{x:7.5,width:5,height:5,rx:1,fill:"currentColor"}),c=r.createElement("rect",{x:.5,y:7,width:5,height:5,rx:1,fill:"currentColor"}),l=r.createElement("rect",{x:7.5,y:7,width:5,height:5,rx:1,fill:"currentColor"});t.a=function SvgNavKeyMetricsIcon(e){return r.createElement("svg",i({viewBox:"0 0 13 12",fill:"none"},e),a,o,c,l)}},287:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 0h3.971v16H7V0zM0 8h4v8H0V8zm18-3h-4v11h4V5z",fill:"currentColor"});t.a=function SvgNavTrafficIcon(e){return r.createElement("svg",i({viewBox:"0 0 18 16",fill:"none"},e),a)}},288:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 16V2c0-1.1-1-2-2.222-2H2.222C1 0 0 .9 0 2v14c0 1.1 1 2 2.222 2h13.556C17 18 18 17.1 18 16zM9 7h5V5H9v2zm7-5H2v14h14V2zM4 4h4v4H4V4zm10 7H9v2h5v-2zM4 10h4v4H4v-4z",fill:"currentColor"});t.a=function SvgNavContentIcon(e){return r.createElement("svg",i({viewBox:"0 0 18 18",fill:"none"},e),a)}},289:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M18.378 4.543l-1.232 1.854a8.024 8.024 0 01-.22 7.598H3.043A8.024 8.024 0 014.154 4.49 8.011 8.011 0 0113.57 2.82l1.853-1.233A10.01 10.01 0 003.117 2.758a10.026 10.026 0 00-1.797 12.24A2.004 2.004 0 003.043 16h13.873a2.003 2.003 0 001.742-1.002 10.03 10.03 0 00-.27-10.465l-.01.01z",fill:"currentColor"}),o=r.createElement("path",{d:"M8.572 11.399a2.003 2.003 0 002.835 0l5.669-8.51-8.504 5.673a2.005 2.005 0 000 2.837z",fill:"currentColor"});t.a=function SvgNavSpeedIcon(e){return r.createElement("svg",i({viewBox:"0 0 20 16",fill:"none"},e),a,o)}},29:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/forms"},290:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M16.1 0v2h2.967l-5.946 5.17-4.6-4L0 10.59 1.621 12l6.9-6 4.6 4L20.7 3.42V6H23V0h-6.9z",fill:"currentColor"});t.a=function SvgNavMonetizationIcon(e){return r.createElement("svg",i({viewBox:"0 0 23 12",fill:"none"},e),a)}},291:function(e,t,n){"use strict";(function(e,r){var i=n(15),a=n.n(i),o=n(14),c=n(2),l=n(0),s=n(3),u=n(10),d=n(13),f=n(292),g=n(32),m=n(20),p=n(80),h=n(9),v=n(52),b=n(18);t.a=function EntityHeader(){var t=Object(b.a)(),n=Object(v.c)(),i=Object(s.useSelect)((function(e){return e(d.c).getCurrentEntityTitle()})),E=Object(s.useSelect)((function(e){return e(d.c).getCurrentEntityURL()})),_=Object(l.useRef)(),O=Object(l.useState)(E),y=a()(O,2),k=y[0],j=y[1];Object(l.useEffect)((function(){var t=function(){if(_.current){var t=_.current.clientWidth-40,n=e.getComputedStyle(_.current.lastChild,null).getPropertyValue("font-size"),r=2*t/parseFloat(n);j(Object(p.d)(E,r))}},n=Object(o.throttle)(t,100);return t(),e.addEventListener("resize",n),function(){e.removeEventListener("resize",n)}}),[E,_,j]);var S=Object(s.useDispatch)(g.a).navigateTo,w=Object(s.useSelect)((function(e){return e(d.c).getAdminURL("googlesitekit-dashboard")})),x=Object(l.useCallback)((function(){Object(h.I)("".concat(t,"_navigation"),"return_to_dashboard"),S(w)}),[w,S,t]);return v.a!==n||null===E||null===i?null:r.createElement("div",{className:"googlesitekit-entity-header"},r.createElement("div",{className:"googlesitekit-entity-header__back"},r.createElement(u.Button,{icon:r.createElement(f.a,{width:24,height:24}),"aria-label":Object(c.__)("Back to dashboard","google-site-kit"),onClick:x,text:!0,tertiary:!0},Object(c.__)("Back to dashboard","google-site-kit"))),r.createElement("div",{ref:_,className:"googlesitekit-entity-header__details"},r.createElement("p",null,i),r.createElement(m.a,{secondary:!0,href:E,"aria-label":E,external:!0},k)))}}).call(this,n(28),n(4))},292:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M21 11H6.83l3.58-3.59L9 6l-6 6 6 6 1.41-1.41L6.83 13H21z",fill:"currentColor"});t.a=function SvgKeyboardBackspace(e){return r.createElement("svg",i({viewBox:"0 0 24 24"},e),a,o)}},293:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ViewOnlyMenu}));var r=n(15),i=n.n(r),a=n(209),o=n(11),c=n.n(o),l=n(0),s=n(2),u=n(56),d=n(10),f=n(18),g=n(112),m=n(9),p=n(294),h=n(295),v=n(296),b=n(298),E=n(3),_=n(7);function ViewOnlyMenu(){var t=Object(l.useState)(!1),n=i()(t,2),r=n[0],o=n[1],O=Object(l.useRef)(),y=Object(f.a)();Object(a.a)(O,(function(){return o(!1)})),Object(g.a)([u.c,u.f],O,(function(){return o(!1)}));var k=Object(l.useCallback)((function(){r||Object(m.I)("".concat(y,"_headerbar"),"open_viewonly"),o(!r)}),[r,y]),j=Object(E.useSelect)((function(e){return e(_.a).hasCapability(_.H)}));return e.createElement("div",{ref:O,className:c()("googlesitekit-view-only-menu","googlesitekit-dropdown-menu","googlesitekit-dropdown-menu__icon-menu","mdc-menu-surface--anchor",{"googlesitekit-view-only-menu--user-can-authenticate":j})},e.createElement(d.Button,{className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--phone googlesitekit-button-icon",text:!0,onClick:k,icon:e.createElement("span",{className:"mdc-button__icon","aria-hidden":"true"},e.createElement(p.a,{className:"mdc-button__icon--image"})),"aria-haspopup":"menu","aria-expanded":r,"aria-controls":"view-only-menu","aria-label":Object(s.__)("View only","google-site-kit"),tooltip:!0,tooltipEnterDelayInMS:500},Object(s.__)("View only","google-site-kit")),e.createElement(d.Menu,{menuOpen:r,nonInteractive:!0,onSelected:k,id:"view-only-menu"},e.createElement(h.a,null),e.createElement(v.a,null),e.createElement("li",{className:"mdc-list-divider",role:"separator"}),e.createElement(b.a,null)))}}).call(this,n(4))},294:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M8 1.333c2.756 0 5.214 1.42 6.415 3.667-1.2 2.247-3.659 3.667-6.415 3.667-2.756 0-5.215-1.42-6.415-3.667C2.785 2.753 5.244 1.333 8 1.333zM8 0C4.364 0 1.258 2.073 0 5c1.258 2.927 4.364 5 8 5s6.742-2.073 8-5c-1.258-2.927-4.364-5-8-5zm0 3.333c1.004 0 1.818.747 1.818 1.667S9.004 6.667 8 6.667 6.182 5.92 6.182 5 6.996 3.333 8 3.333zM8 2C6.196 2 4.727 3.347 4.727 5S6.197 8 8 8c1.804 0 3.273-1.347 3.273-3S9.803 2 8 2z",fill:"currentColor"});t.a=function SvgView(e){return r.createElement("svg",i({viewBox:"0 0 16 10",fill:"none"},e),a)}},295:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(0),l=n(38),s=n(2),u=n(3),d=n(10),f=n(32),g=n(13),m=n(7),p=n(9),h=n(20),v=n(18),b=n(37);function Description(){var t=Object(v.a)(),n=Object(u.useSelect)((function(e){return e(m.a).hasCapability(m.H)})),r=Object(u.useSelect)((function(e){return e(g.c).getProxySetupURL()})),a=Object(u.useSelect)((function(e){return e(g.c).getDocumentationLinkURL("dashboard-sharing")})),E=Object(u.useDispatch)(f.a).navigateTo,_=Object(c.useCallback)(function(){var e=o()(i.a.mark((function e(n){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.preventDefault(),e.next=3,Promise.all([Object(b.f)("start_user_setup",!0),Object(p.I)("".concat(t,"_headerbar_viewonly"),"start_user_setup",r?"proxy":"custom-oauth")]);case 3:E(r);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[r,E,t]),O=Object(c.useCallback)((function(){Object(p.I)("".concat(t,"_headerbar_viewonly"),"click_learn_more_link")}),[t]),y=n?Object(l.a)(Object(s.__)("You can see stats from all shared Google services, but you can't make any changes. <strong>Sign in to connect more services and control sharing access.</strong>","google-site-kit"),{strong:e.createElement("strong",null)}):Object(l.a)(Object(s.__)("You can see stats from all shared Google services, but you can't make any changes. <a>Learn more</a>","google-site-kit"),{a:e.createElement(h.a,{href:a,external:!0,onClick:O,"aria-label":Object(s.__)("Learn more about dashboard sharing","google-site-kit")})});return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item googlesitekit-view-only-menu__description"},e.createElement("p",null,y),n&&e.createElement(d.Button,{onClick:_},Object(s._x)("Sign in with Google","Service name","google-site-kit")))}}).call(this,n(4))},296:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SharedServices}));var r=n(2),i=n(3),a=n(7),o=n(297);function SharedServices(){var t=Object(i.useSelect)((function(e){return e(a.a).getViewableModules()}));return void 0===t?null:e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("h4",null,Object(r.__)("Shared services","google-site-kit")),e.createElement("ul",null,t.map((function(t){return e.createElement(o.a,{key:t,module:t})}))))}}).call(this,n(4))},297:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Service}));var r=n(1),i=n.n(r),a=n(38),o=n(2),c=n(3),l=n(19),s=n(7);function Service(t){var n=t.module,r=Object(c.useSelect)((function(e){return e(s.a).hasCapability(s.H)})),i=Object(c.useSelect)((function(e){return e(l.a).getModule(n)||{}})),u=i.name,d=i.owner,f=Object(c.useSelect)((function(e){return e(l.a).getModuleIcon(n)}));return e.createElement("li",{className:"googlesitekit-view-only-menu__service"},e.createElement("span",{className:"googlesitekit-view-only-menu__service--icon"},e.createElement(f,{height:26})),e.createElement("span",{className:"googlesitekit-view-only-menu__service--name"},u),r&&(null==d?void 0:d.login)&&e.createElement("span",{className:"googlesitekit-view-only-menu__service--owner"},Object(a.a)(Object(o.sprintf)(
/* translators: %s: module owner Google Account email address */
Object(o.__)("Shared by <strong>%s</strong>","google-site-kit"),d.login),{strong:e.createElement("strong",{title:d.login})})))}Service.propTypes={module:i.a.string.isRequired}}).call(this,n(4))},298:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Tracking}));var r=n(38),i=n(2),a=n(218),o=n(18);function Tracking(){var t=Object(o.a)();return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("p",null,Object(r.a)(Object(i.__)("Thanks for using Site Kit!<br />Help us make it even better","google-site-kit"),{br:e.createElement("br",null)})),e.createElement(a.a,{trackEventCategory:"".concat(t,"_headerbar_viewonly"),alignCheckboxLeft:!0}))}}).call(this,n(4))},299:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SubtleNotifications}));var r=n(167),i=n(41);function SubtleNotifications(){return e.createElement(r.a,{areaSlug:i.b.BANNERS_BELOW_NAV})}}).call(this,n(4))},3:function(e,t){e.exports=googlesitekit.data},300:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(0),l=n(3),s=n(13),u=n(18),d=n(37),f=n(9),g=function(){var e=Object(u.a)(),t=Object(l.useSelect)((function(e){return e(s.c).isUsingProxy()})),n=Object(l.useSelect)((function(e){return e(s.c).getSetupErrorMessage()}));Object(c.useEffect)((function(){n||void 0===t||function(){var n=o()(i.a.mark((function n(){var r,a;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,Object(d.d)("start_user_setup");case 2:return r=n.sent,n.next=5,Object(d.d)("start_site_setup");case 5:if(a=n.sent,!r.cacheHit){n.next=10;break}return n.next=9,Object(d.c)("start_user_setup");case 9:Object(f.I)("".concat(e,"_setup"),"complete_user_setup",t?"proxy":"custom-oauth");case 10:if(!a.cacheHit){n.next=14;break}return n.next=13,Object(d.c)("start_site_setup");case 13:Object(f.I)("".concat(e,"_setup"),"complete_site_setup",t?"proxy":"custom-oauth");case 14:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}()()}),[e,t,n])}},301:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M9 16h2v-2H9v2zm1-16C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14C7.79 4 6 5.79 6 8h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z",fill:"currentColor"});t.a=function SvgHelp(e){return r.createElement("svg",i({viewBox:"0 0 20 20",fill:"none"},e),a)}},302:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(0),l=n(2),s=n(10),u=n(77),d=n(20);function NewBadge(t){var n=t.tooltipTitle,r=t.learnMoreLink,i=t.forceOpen,a=t.hasLeftSpacing,f=t.hasNoSpacing,g=t.onLearnMoreClick,m=void 0===g?function(){}:g,p=e.createElement(u.a,{className:o()("googlesitekit-new-badge",{"googlesitekit-new-badge--has-no-spacing":f}),label:Object(l.__)("New","google-site-kit"),hasLeftSpacing:a});return n?e.createElement(s.Tooltip,{tooltipClassName:"googlesitekit-new-badge__tooltip",title:e.createElement(c.Fragment,null,n,e.createElement("br",null),e.createElement(d.a,{href:r,onClick:m,external:!0,hideExternalIndicator:!0},Object(l.__)("Learn more","google-site-kit"))),placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,interactive:!0,open:i},p):p}NewBadge.propTypes={tooltipTitle:i.a.string,learnMoreLink:i.a.string,forceOpen:i.a.bool,onLearnMoreClick:i.a.func,hasLeftSpacing:i.a.bool,hasNoSpacing:i.a.bool},t.a=NewBadge}).call(this,n(4))},31:function(e,t,n){"use strict";n.d(t,"l",(function(){return r})),n.d(t,"i",(function(){return i})),n.d(t,"f",(function(){return a})),n.d(t,"e",(function(){return o})),n.d(t,"g",(function(){return c})),n.d(t,"d",(function(){return l})),n.d(t,"h",(function(){return s})),n.d(t,"a",(function(){return u})),n.d(t,"c",(function(){return d})),n.d(t,"b",(function(){return f})),n.d(t,"j",(function(){return g})),n.d(t,"k",(function(){return m}));var r="modules/adsense",i=1,a="READY",o="NEEDS_ATTENTION",c="REQUIRES_REVIEW",l="GETTING_READY",s="background-submit-suspended",u="adsenseAdBlockingFormSettings",d="googlesitekit-ad-blocking-recovery-setup-create-message-cta-clicked",f="ad-blocking-recovery-notification",g={TAG_PLACED:"tag-placed",SETUP_CONFIRMED:"setup-confirmed"},m={PLACE_TAGS:0,CREATE_MESSAGE:1,COMPLETE:2}},311:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"currentColor",fillRule:"evenodd"},r.createElement("path",{d:"M0 6.414L1.415 5l5.292 5.292-1.414 1.415z"}),r.createElement("path",{d:"M14.146.146l1.415 1.414L5.414 11.707 4 10.292z"}));t.a=function SvgConnected(e){return r.createElement("svg",i({viewBox:"0 0 16 12"},e),a)}},312:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"currentColor",fillRule:"evenodd"},r.createElement("path",{d:"M0 0h2v7H0zM0 10h2v2H0z"}));t.a=function SvgExclamation(e){return r.createElement("svg",i({viewBox:"0 0 2 12"},e),a)}},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},322:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportZero}));var r=n(6),i=n.n(r),a=n(21),o=n.n(a),c=n(25),l=n.n(c),s=n(1),u=n.n(s),d=n(0),f=n(143),g=n(174);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function WidgetReportZero(t){var n=t.widgetSlug,r=t.moduleSlug,i=l()(t,["widgetSlug","moduleSlug"]),a=Object(d.useMemo)((function(){return{moduleSlug:r}}),[r]);return Object(f.a)(n,g.a,a),e.createElement(g.a,o()({moduleSlug:r},i))}WidgetReportZero.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:u.a.string.isRequired},g.a.propTypes)}).call(this,n(4))},323:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportError}));var r=n(6),i=n.n(r),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(170);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function WidgetReportError(t){t.widgetSlug;var n=o()(t,["widgetSlug"]);return e.createElement(s.a,n)}WidgetReportError.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:l.a.string.isRequired},s.a.propTypes)}).call(this,n(4))},324:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WPDashboardReportError}));var r=n(1),i=n.n(r),a=n(550),o=n(208),c=n(3),l=n(23),s=n(170);function WPDashboardReportError(t){var n=t.moduleSlug,r=t.error,i=Object(o.a)(WPDashboardReportError,"WPDashboardReportError"),u=Object(c.useDispatch)(l.b).setValue,d=r.message,f=Object(c.useSelect)((function(e){return e(l.b).getValue("WPDashboardReportError-".concat(n,"-").concat(d))}));return Object(a.a)((function(){u("WPDashboardReportError-".concat(n,"-").concat(d),i)}),(function(){u("WPDashboardReportError-".concat(n,"-").concat(d),void 0)})),f!==i?null:e.createElement(s.a,{moduleSlug:n,error:r})}WPDashboardReportError.propTypes={moduleSlug:i.a.string.isRequired,error:i.a.object.isRequired}}).call(this,n(4))},329:function(e,t,n){"use strict";(function(e){var r=n(51),i=n.n(r),a=n(53),o=n.n(a),c=n(68),l=n.n(c),s=n(69),u=n.n(s),d=n(49),f=n.n(d),g=n(1),m=n.n(g),p=n(0),h=n(17),v=n(20);function b(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var i=f()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return u()(this,n)}}var E=function(t){l()(LayoutHeader,t);var n=b(LayoutHeader);function LayoutHeader(){return i()(this,LayoutHeader),n.apply(this,arguments)}return o()(LayoutHeader,[{key:"render",value:function(){var t=this.props,n=t.title,r=t.badge,i=t.ctaLabel,a=t.ctaLink,o=a?{alignMiddle:!0,smSize:4,lgSize:6}:{alignMiddle:!0,smSize:4,mdSize:8,lgSize:12};return e.createElement("header",{className:"googlesitekit-layout__header"},e.createElement(h.e,null,e.createElement(h.k,null,n&&e.createElement(h.a,o,e.createElement("h3",{className:"googlesitekit-subheading-1 googlesitekit-layout__header-title"},n,r)),a&&e.createElement(h.a,{alignMiddle:!0,mdAlignRight:!0,smSize:4,lgSize:6},e.createElement(v.a,{href:a,external:!0},i)))))}}]),LayoutHeader}(p.Component);E.propTypes={title:m.a.string,badge:m.a.node,ctaLabel:m.a.string,ctaLink:m.a.string},E.defaultProps={title:"",badge:null,ctaLabel:"",ctaLink:""},t.a=E}).call(this,n(4))},330:function(e,t,n){"use strict";(function(e){var r=n(51),i=n.n(r),a=n(53),o=n.n(a),c=n(68),l=n.n(c),s=n(69),u=n.n(s),d=n(49),f=n.n(d),g=n(1),m=n.n(g),p=n(0),h=n(17),v=n(134);function b(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var i=f()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return u()(this,n)}}var E=function(t){l()(LayoutFooter,t);var n=b(LayoutFooter);function LayoutFooter(){return i()(this,LayoutFooter),n.apply(this,arguments)}return o()(LayoutFooter,[{key:"render",value:function(){var t=this.props,n=t.ctaLabel,r=t.ctaLink,i=t.footerContent;return e.createElement("footer",{className:"googlesitekit-layout__footer"},e.createElement(h.e,null,e.createElement(h.k,null,e.createElement(h.a,{size:12},r&&n&&e.createElement(v.a,{className:"googlesitekit-data-block__source",name:n,href:r,external:!0}),i))))}}]),LayoutFooter}(p.Component);E.propTypes={ctaLabel:m.a.string,ctaLink:m.a.string},t.a=E}).call(this,n(4))},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(22),i=n(18);function a(){var e=Object(i.a)();return r.g.includes(e)}},35:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return l})),n.d(t,"c",(function(){return s})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return d}));n(14);var r=n(2),i="missing_required_scopes",a="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===i}function l(e){var t;return[a,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function s(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||l(e)||c(e)||s(e))}function d(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(r.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(r.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},353:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i}));var r=n(0);function i(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;Object(r.useEffect)((function(){var r,i=!1,a=function(){r=e.setTimeout((function(){i=!0}),n)},o=function(){e.clearTimeout(r),i&&(i=!1,t())};return e.addEventListener("focus",o),e.addEventListener("blur",a),function(){e.removeEventListener("focus",o),e.removeEventListener("blur",a),e.clearTimeout(r)}}),[n,t])}}).call(this,n(28))},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return _})),n.d(t,"b",(function(){return b})),n.d(t,"c",(function(){return E}));var r=n(99),i=e._googlesitekitTrackingData||{},a=i.activeModules,o=void 0===a?[]:a,c=i.isSiteKitScreen,l=i.trackingEnabled,s=i.trackingID,u=i.referenceSiteURL,d=i.userIDHash,f=i.isAuthenticated,g={activeModules:o,trackingEnabled:l,trackingID:s,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:i.userRoles,isAuthenticated:f,pluginVersion:"1.151.0"},m=Object(r.a)(g),p=m.enableTracking,h=m.disableTracking,v=(m.isTrackingEnabled,m.initializeSnippet),b=m.trackEvent,E=m.trackEventOnce;function _(e){e?p():h()}c&&l&&v()}).call(this,n(28))},365:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PageHeader}));var r=n(11),i=n.n(r),a=n(1),o=n.n(a),c=n(17),l=n(311),s=n(312),u=n(76);function PageHeader(t){var n=t.title,r=t.icon,a=t.className,o=t.status,d=t.statusText,f=t.fullWidth,g=t.children,m=f?{size:12}:{smSize:4,mdSize:4,lgSize:6},p=""!==o||Boolean(g);return e.createElement("header",{className:"googlesitekit-page-header"},e.createElement(c.k,null,n&&e.createElement(c.a,m,r,e.createElement("h1",{className:i()("googlesitekit-page-header__title",a)},n)),p&&e.createElement(c.a,{alignBottom:!0,mdAlignRight:!0,smSize:4,mdSize:4,lgSize:6},e.createElement("div",{className:"googlesitekit-page-header__details"},o&&e.createElement("span",{className:i()("googlesitekit-page-header__status","googlesitekit-page-header__status--".concat(o))},d,e.createElement(u.a,null,"connected"===o?e.createElement(l.a,{width:10,height:8}):e.createElement(s.a,{width:2,height:12}))),g))))}PageHeader.propTypes={title:o.a.string,icon:o.a.node,className:o.a.string,status:o.a.string,statusText:o.a.string,fullWidth:o.a.bool},PageHeader.defaultProps={title:"",icon:null,className:"googlesitekit-heading-3",status:"",statusText:"",fullWidth:!1}}).call(this,n(4))},367:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),i=n(158),a=n(57),o=function(e){var t=Object(r.useContext)(i.a);return Object(a.b)(e,t)}},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return b})),n.d(t,"f",(function(){return E})),n.d(t,"c",(function(){return _})),n.d(t,"e",(function(){return O})),n.d(t,"b",(function(){return y}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=(n(27),n(9));function l(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u,d="googlesitekit_",f="".concat(d).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),g=["sessionStorage","localStorage"],m=[].concat(g),p=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,a="__storage_test__",r.setItem(a,a),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function h(){return v.apply(this,arguments)}function v(){return(v=o()(i.a.mark((function t(){var n,r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=l(m),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(a=r.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,p(a);case 11:if(!t.sent){t.next=13;break}u=e[a];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var b=function(){var e=o()(i.a.mark((function e(t){var n,r,a,o,c,l,s;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(f).concat(t)))){e.next=10;break}if(a=JSON.parse(r),o=a.timestamp,c=a.ttl,l=a.value,s=a.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:l,isError:s});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),E=function(){var t=o()(i.a.mark((function t(n,r){var a,o,l,s,u,d,g,m,p=arguments;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=p.length>2&&void 0!==p[2]?p[2]:{},o=a.ttl,l=void 0===o?c.b:o,s=a.timestamp,u=void 0===s?Math.round(Date.now()/1e3):s,d=a.isError,g=void 0!==d&&d,t.next=3,h();case 3:if(!(m=t.sent)){t.next=14;break}return t.prev=5,m.setItem("".concat(f).concat(n),JSON.stringify({timestamp:u,ttl:l,value:r,isError:g})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),_=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,h();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,a=n.startsWith(d)?n:"".concat(f).concat(n),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),O=function(){var t=o()(i.a.mark((function t(){var n,r,a,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,h();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],a=0;a<n.length;a++)0===(o=n.key(a)).indexOf(d)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),y=function(){var e=o()(i.a.mark((function e(){var t,n,r,a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h();case 2:if(!e.sent){e.next=25;break}return e.next=6,O();case 6:t=e.sent,n=l(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return a=r.value,e.next=14,_(a);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},371:function(e,t,n){"use strict";(function(e,r){var i=n(2),a=n(10),o=n(3),c=n(399),l=n(400),s=n(204),u=n(93),d=n(24),f=n(23),g=n(7),m=n(19),p=n(18),h=n(34),v=n(9),b=n(8),E=n(52),_=n(50);t.a=Object(_.a)({moduleName:"analytics-4"})((function AudienceSegmentationIntroductoryOverlayNotification(){var t=Object(p.a)(),n=Object(h.a)(),_=Object(d.e)(),O=Object(E.c)(),y=Object(o.useSelect)((function(e){return e(g.a).isDismissingItem("audienceSegmentationIntroductoryOverlayNotification")})),k=Object(o.useSelect)((function(e){var t=e(g.a).isItemDismissed("audienceSegmentationIntroductoryOverlayNotification"),r=e(g.a).isAudienceSegmentationWidgetHidden(),i=e(m.a).isModuleActive("analytics-4"),a=!n||e(g.a).canViewSharedModule("analytics-4"),o=e(b.r).getAudienceSegmentationSetupCompletedBy(),c=e(g.a).getID();return E.b===O&&!1===t&&!1===r&&i&&a&&Number.isInteger(o)&&o!==c})),j=Object(o.useDispatch)(f.b).dismissOverlayNotification,S=function(){j("audienceSegmentationIntroductoryOverlayNotification")};return r.createElement(s.a,{shouldShowNotification:k,GraphicDesktop:c.a,GraphicMobile:l.a,notificationID:"audienceSegmentationIntroductoryOverlayNotification",onShow:function(){Object(v.I)("".concat(t,"_audiences-secondary-user-intro"),"view_notification")}},r.createElement("div",{className:"googlesitekit-overlay-notification__body"},r.createElement("h3",null,Object(i.__)("New! Visitor groups","google-site-kit")),r.createElement("p",null,Object(i.__)("You can now learn more about your site visitor groups by comparing different metrics","google-site-kit"))),r.createElement("div",{className:"googlesitekit-overlay-notification__actions"},r.createElement(a.Button,{tertiary:!0,disabled:y,onClick:function(){Object(v.I)("".concat(t,"_audiences-secondary-user-intro"),"dismiss_notification").finally((function(){S()}))}},Object(i.__)("Got it","google-site-kit")),r.createElement(a.Button,{disabled:y,onClick:function(n){n.preventDefault();setTimeout((function(){e.scrollTo({top:Object(u.a)(".googlesitekit-widget-area--mainDashboardTrafficAudienceSegmentation",_),behavior:"smooth"})}),0),Object(v.I)("".concat(t,"_audiences-secondary-user-intro"),"confirm_notification").finally((function(){S()}))}},Object(i.__)("Show me","google-site-kit"))))}))}).call(this,n(28),n(4))},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="_googlesitekitDataLayer",i="data-googlesitekit-gtag"},393:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(14);function i(e){var t;if(void 0!==e)return!((null==e?void 0:e.rows)&&(null==e?void 0:e.totals)&&!(null==e||null===(t=e.totals)||void 0===t?void 0:t.every(r.isEmpty)))||!e.totals.some((function(e){return!!e.metricValues&&e.metricValues.some((function(e){return e.value>0}))}))}},399:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#audience-segmentation-introductory-graphic-desktop_svg__clip0_1395_20972)"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h264c8.837 0 16 7.163 16 16v147H0V16z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-desktop_svg__filter0_d_1395_20972)"},r.createElement("rect",{x:-10,y:25,width:153,height:174,rx:11,fill:"#fff"})),r.createElement("rect",{x:9.031,y:110.641,width:53.016,height:9.516,rx:4.758,fill:"#EBEEF0"}),r.createElement("rect",{x:9.031,y:95.688,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),r.createElement("rect",{x:9.031,y:148.703,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),r.createElement("path",{d:"M94.672 108.602a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 010 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),r.createElement("rect",{x:9,y:46,width:36,height:9,rx:4.5,fill:"#EBEEF0"}),r.createElement("path",{d:"M94.672 161.617a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 110 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#FFDED3"}),r.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-desktop_svg__filter1_d_1395_20972)"},r.createElement("rect",{x:152,y:25,width:153,height:174,rx:11,fill:"#fff"})),r.createElement("rect",{x:170.955,y:110.641,width:52.805,height:9.516,rx:4.758,fill:"#EBEEF0"}),r.createElement("rect",{x:170.955,y:95.688,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),r.createElement("rect",{x:170.955,y:148.703,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),r.createElement("path",{d:"M256.256 108.602a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),r.createElement("rect",{x:171,y:46,width:36,height:9,rx:4.5,fill:"#EBEEF0"}),r.createElement("path",{d:"M295 73.5H152",stroke:"#EBEEF0",strokeWidth:2}),r.createElement("path",{d:"M256.256 161.617a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836z",fill:"#FFDED3"}),r.createElement("path",{d:"M143 73.5H0",stroke:"#EBEEF0",strokeWidth:2})),o=r.createElement("defs",null,r.createElement("filter",{id:"audience-segmentation-introductory-graphic-desktop_svg__filter0_d_1395_20972",x:-26,y:13,width:185,height:206,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1395_20972"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1395_20972",result:"shape"})),r.createElement("filter",{id:"audience-segmentation-introductory-graphic-desktop_svg__filter1_d_1395_20972",x:136,y:13,width:185,height:206,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1395_20972"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1395_20972",result:"shape"})),r.createElement("clipPath",{id:"audience-segmentation-introductory-graphic-desktop_svg__clip0_1395_20972"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h264c8.837 0 16 7.163 16 16v147H0V16z",fill:"#fff"})));t.a=function SvgAudienceSegmentationIntroductoryGraphicDesktop(e){return r.createElement("svg",i({viewBox:"0 0 296 163",fill:"none"},e),a,o)}},400:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M41.064 27.146a64.127 64.127 0 016.51-5.868C69.418 4.126 87.464 4.153 112.45 7.283c16.891 2.116 26.759 10.166 49.788 8.9 23.029-1.266 28.929-7.127 57.117-5.25 22.315 1.487 32.324 5.897 52.163 16.213 18.36 9.549 35.031 26.324 43.408 48.509 14.361 38.026-11.243 106.466-45.58 109.693-24.881 2.339-45.414-25.243-70.527-18.855-15.47 3.936-24.646 20.444-36.581 31.339-13.925 12.711-43.922 11.912-60.227 5.129-15.538-6.464-30.653-19.276-35.728-38.145-3.863-14.369-4.916-31.498-15.733-44.622-13.09-15.883-21.087-22.968-25.581-44.54-3.903-18.734 4.494-36.505 16.095-48.508z",fill:"#B8E6CA"}),o=r.createElement("path",{d:"M41.064 27.146a64.127 64.127 0 016.51-5.868C69.418 4.126 87.464 4.153 112.45 7.283c16.891 2.116 26.759 10.166 49.788 8.9 23.029-1.266 28.929-7.127 57.117-5.25 22.315 1.487 32.324 5.897 52.163 16.213 18.36 9.549 35.031 26.324 43.408 48.509 14.361 38.026-11.243 106.466-45.58 109.693-24.881 2.339-45.414-25.243-70.527-18.855-15.47 3.936-24.646 20.444-36.581 31.339-13.925 12.711-43.922 11.912-60.227 5.129-15.538-6.464-30.653-19.276-35.728-38.145-3.863-14.369-4.916-31.498-15.733-44.622-13.09-15.883-21.087-22.968-25.581-44.54-3.903-18.734 4.494-36.505 16.095-48.508z",fill:"#B8E6CA"}),c=r.createElement("g",{mask:"url(#audience-segmentation-introductory-graphic-mobile_svg__a)"},r.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-mobile_svg__filter0_d_2898_16651)"},r.createElement("rect",{x:71.449,y:21.433,width:100.401,height:136.493,rx:7.218,fill:"#fff"})),r.createElement("rect",{x:83.941,y:77.631,width:34.79,height:6.244,rx:3.122,fill:"#EBEEF0"}),r.createElement("rect",{x:83.941,y:67.819,width:12.489,height:6.244,rx:3.122,fill:"#EBEEF0"}),r.createElement("rect",{x:83.941,y:99.983,width:12.489,height:6.244,rx:3.122,fill:"#EBEEF0"}),r.createElement("path",{d:"M140.133 76.293a5.798 5.798 0 015.798-5.798h8.921a5.798 5.798 0 010 11.596h-8.921a5.798 5.798 0 01-5.798-5.798z",fill:"#B8E6CA"}),r.createElement("rect",{x:83.926,y:35.213,width:23.624,height:5.906,rx:2.953,fill:"#EBEEF0"}),r.createElement("path",{d:"M140.133 108.458a5.798 5.798 0 015.798-5.798h8.921a5.798 5.798 0 010 11.597h-8.921a5.798 5.798 0 01-5.798-5.799z",fill:"#FFDED3"}),r.createElement("rect",{x:83.043,y:109.796,width:36.574,height:6.244,rx:3.122,fill:"#EBEEF0"}),r.createElement("path",{d:"M171.848 53.259H72.103",stroke:"#EBEEF0",strokeWidth:1.312}),r.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-mobile_svg__filter1_d_2898_16651)"},r.createElement("rect",{x:184.973,y:21.433,width:100.401,height:136.493,rx:7.218,fill:"#fff"})),r.createElement("rect",{x:197.414,y:77.631,width:34.652,height:6.244,rx:3.122,fill:"#EBEEF0"}),r.createElement("rect",{x:197.414,y:67.819,width:12.439,height:6.244,rx:3.122,fill:"#EBEEF0"}),r.createElement("rect",{x:197.41,y:99.983,width:12.439,height:6.244,rx:3.122,fill:"#EBEEF0"}),r.createElement("path",{d:"M253.391 76.293a5.798 5.798 0 015.798-5.798h8.839a5.798 5.798 0 010 11.596h-8.839a5.798 5.798 0 01-5.798-5.798z",fill:"#B8E6CA"}),r.createElement("rect",{x:197.449,y:35.213,width:23.624,height:5.906,rx:2.953,fill:"#EBEEF0"}),r.createElement("path",{d:"M278.82 53.259h-93.838",stroke:"#EBEEF0",strokeWidth:1.312}),r.createElement("path",{d:"M253.391 108.458a5.798 5.798 0 015.798-5.798h8.839a5.798 5.798 0 010 11.597h-8.839a5.798 5.798 0 01-5.798-5.799z",fill:"#FFDED3"}),r.createElement("rect",{x:196.523,y:109.796,width:36.429,height:6.244,rx:3.122,fill:"#EBEEF0"})),l=r.createElement("defs",null,r.createElement("filter",{id:"audience-segmentation-introductory-graphic-mobile_svg__filter0_d_2898_16651",x:55.449,y:9.433,width:132.402,height:168.493,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2898_16651"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2898_16651",result:"shape"})),r.createElement("filter",{id:"audience-segmentation-introductory-graphic-mobile_svg__filter1_d_2898_16651",x:168.973,y:9.433,width:132.402,height:168.493,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2898_16651"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2898_16651",result:"shape"})),r.createElement("clipPath",{id:"audience-segmentation-introductory-graphic-mobile_svg__clip0_2898_16651"},r.createElement("path",{fill:"#fff",d:"M0 0h343v128H0z"})));t.a=function SvgAudienceSegmentationIntroductoryGraphicMobile(e){return r.createElement("svg",i({viewBox:"0 0 343 123",fill:"none"},e),r.createElement("g",{clipPath:"url(#audience-segmentation-introductory-graphic-mobile_svg__clip0_2898_16651)"},a,r.createElement("mask",{id:"audience-segmentation-introductory-graphic-mobile_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:24,y:5,width:295,height:203},o),c),l)}},41:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(22),i="core/notifications",a={ERRORS:"notification-area-errors",BANNERS_ABOVE_NAV:"notification-area-banners-above-nav",BANNERS_BELOW_NAV:"notification-area-banners-below-nav"},o={DEFAULT:"default",SETUP_CTAS:"setup-ctas"},c=[r.s,r.n,r.l,r.o,r.m]},415:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M16.666 7.5V5H15v2.5h-2.5v1.666H15v2.5h1.666v-2.5h2.5V7.5h-2.5zM7.5 10a3.332 3.332 0 100-6.667A3.332 3.332 0 107.5 10zm0-5c.916 0 1.666.75 1.666 1.666 0 .917-.75 1.667-1.666 1.667-.917 0-1.667-.75-1.667-1.667C5.833 5.75 6.583 5 7.5 5zm5.325 7.133c-1.4-.717-3.217-1.3-5.325-1.3-2.109 0-3.925.583-5.325 1.3A2.476 2.476 0 00.833 14.35v2.316h13.333V14.35c0-.934-.508-1.792-1.341-2.217zM12.5 15h-10v-.65c0-.317.166-.6.433-.734A10.09 10.09 0 017.5 12.5c1.975 0 3.575.608 4.566 1.116a.81.81 0 01.434.734V15z",fill:"currentColor"});t.a=function SvgShare(e){return r.createElement("svg",i({viewBox:"0 0 20 20",fill:"none"},e),a)}},42:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"k",(function(){return c})),n.d(t,"b",(function(){return l})),n.d(t,"g",(function(){return s})),n.d(t,"f",(function(){return u})),n.d(t,"i",(function(){return d})),n.d(t,"h",(function(){return f})),n.d(t,"j",(function(){return g}));var r="non_https_site",i="modules/reader-revenue-manager",a="reader-revenue-manager",o={ONBOARDING_COMPLETE:"ONBOARDING_COMPLETE",ONBOARDING_ACTION_REQUIRED:"ONBOARDING_ACTION_REQUIRED",PENDING_VERIFICATION:"PENDING_VERIFICATION",UNSPECIFIED:"ONBOARDING_STATE_UNSPECIFIED"},c="READER_REVENUE_MANAGER_SHOW_PUBLICATION_APPROVED_NOTIFICATION",l="rrm_module_setup_banner_dismissed_key",s="readerRevenueManagerSetupForm",u="readerRevenueManagerNoticesForm",d="showPublicationCreate",f="resetPublications",g="syncPublication"},430:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(3),l=n(702),s=n(7),u=n(47),d=n(17),f=n(34);function WidgetContextRenderer(t){var n=t.id,r=t.slug,i=t.className,a=t.Header,g=t.Footer,m=Object(f.a)(),p=Object(c.useSelect)((function(e){return m?e(s.a).getViewableModules():null})),h=Object(c.useSelect)((function(e){return r?e(u.a).getWidgetAreas(r):null})),v=Object(c.useSelect)((function(e){return!!r&&e(u.a).isWidgetContextActive(r,{modules:p||void 0})}));return void 0===p?null:e.createElement("div",{id:n,className:o()("googlesitekit-widget-context",{"googlesitekit-hidden":!v},i)},a&&v&&e.createElement(d.e,null,e.createElement(d.k,null,e.createElement(d.a,{size:12},e.createElement(a,null)))),h&&h.map((function(t){return e.createElement(l.a,{key:t.slug,slug:t.slug,contextID:n})})),g&&v&&e.createElement(d.e,null,e.createElement(d.k,null,e.createElement(d.a,{size:12},e.createElement(g,null)))))}WidgetContextRenderer.propTypes={id:i.a.string,slug:i.a.string,className:i.a.string,Header:i.a.elementType,Footer:i.a.elementType},t.a=WidgetContextRenderer}).call(this,n(4))},44:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(1),o=n.n(a),c=n(11),l=n.n(c),s=n(24);function PreviewBlock(t){var n,r,a=t.className,o=t.width,c=t.height,u=t.shape,d=t.padding,f=t.smallWidth,g=t.smallHeight,m=t.tabletWidth,p=t.tabletHeight,h=t.desktopWidth,v=t.desktopHeight,b=Object(s.e)(),E={width:(n={},i()(n,s.b,f),i()(n,s.c,m),i()(n,s.a,h),i()(n,s.d,h),n),height:(r={},i()(r,s.b,g),i()(r,s.c,p),i()(r,s.a,v),i()(r,s.d,h),r)};return e.createElement("div",{className:l()("googlesitekit-preview-block",a,{"googlesitekit-preview-block--padding":d}),style:{width:E.width[b]||o,height:E.height[b]||c}},e.createElement("div",{className:l()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(4))},45:function(e,t){e.exports=googlesitekit.api},47:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var r={BOXES:"boxes",COMPOSITE:"composite"},i={QUARTER:"quarter",HALF:"half",FULL:"full"},a="core/widgets"},50:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(3),i=n(19),a=n(82);function o(t){var n=t.moduleName,o=t.FallbackComponent,c=t.IncompleteComponent;return function(t){function WhenActiveComponent(a){var l=Object(r.useSelect)((function(e){return e(i.a).getModule(n)}),[n]);if(!l)return null;var s=o||a.WidgetNull||null;if(!1===l.active)return s&&e.createElement(s,a);if(!1===l.connected){var u=c||s;return u&&e.createElement(u,a)}return e.createElement(t,a)}return WhenActiveComponent.displayName="When".concat(Object(a.c)(n),"Active"),(t.displayName||t.name)&&(WhenActiveComponent.displayName+="(".concat(t.displayName||t.name,")")),WhenActiveComponent}}}).call(this,n(4))},513:function(e,t,n){"use strict";n(520),n(521);var r=n(557);n.d(t,"a",(function(){return r.a}));var i=n(558);n.d(t,"d",(function(){return i.a}));var a=n(559);n.d(t,"c",(function(){return a.a}));var o=n(560);n.d(t,"b",(function(){return o.a}))},52:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(22),i=n(18),a=r.n,o=r.l;function c(){var e=Object(i.a)();return e===r.n||e===r.o?a:e===r.l||e===r.m?o:null}},520:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ProductIDContributionsNotification}));var r=n(1),i=n.n(r),a=n(2),o=n(3),c=n(13),l=n(115),s=n(92),u=n(168);function ProductIDContributionsNotification(t){var n=t.id,r=t.Notification,i=Object(o.useSelect)((function(e){return e(c.c).getAdminURL("googlesitekit-settings")}));return e.createElement(r,null,e.createElement(l.a,{type:"new-feature",description:Object(a.__)("New! You can now select product IDs to use with your Reader Revenue Manager snippet","google-site-kit"),dismissCTA:e.createElement(s.a,{id:n,primary:!1,dismissLabel:Object(a.__)("Got it","google-site-kit")}),additionalCTA:e.createElement(u.a,{id:n,ctaLabel:Object(a.__)("Edit settings","google-site-kit"),ctaLink:"".concat(i,"#connected-services/reader-revenue-manager/edit")})}))}ProductIDContributionsNotification.propTypes={id:i.a.string.isRequired,Notification:i.a.elementType.isRequired}}).call(this,n(4))},521:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ProductIDSubscriptionsNotification}));var r=n(1),i=n.n(r),a=n(2),o=n(3),c=n(13),l=n(115),s=n(92),u=n(168);function ProductIDSubscriptionsNotification(t){var n=t.id,r=t.Notification,i=Object(o.useSelect)((function(e){return e(c.c).getAdminURL("googlesitekit-settings")}));return e.createElement(r,null,e.createElement(l.a,{type:"warning",description:Object(a.__)("To complete your Reader Revenue Manager paywall setup, add your product IDs in settings","google-site-kit"),dismissCTA:e.createElement(s.a,{id:n,primary:!1,dismissLabel:Object(a.__)("Got it","google-site-kit")}),additionalCTA:e.createElement(u.a,{id:n,ctaLabel:Object(a.__)("Edit settings","google-site-kit"),ctaLink:"".concat(i,"#connected-services/reader-revenue-manager/edit")})}))}ProductIDSubscriptionsNotification.propTypes={id:i.a.string.isRequired,Notification:i.a.elementType.isRequired}}).call(this,n(4))},54:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,r=t.reconnectURL,i=t.noPrefix;if(!n)return null;var l=n;void 0!==i&&i||(l=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),r&&Object(a.a)(r)&&(l=l+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),r));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(l,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:i.a.string.isRequired,reconnectURL:i.a.string,noPrefix:i.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},557:function(e,t,n){"use strict";(function(e){var r=n(0),i=n(2),a=n(204),o=n(569),c=n(570),l=n(52),s=n(18),u=n(34),d=n(70),f=n(9),g=n(10),m=n(3),p=n(7),h=n(23),v=n(22),b=n(42),E=n(50),_=b.d.ONBOARDING_COMPLETE;t.a=Object(E.a)({moduleName:b.e})((function PublicationApprovedOverlayNotification(){var t=Object(s.a)(),n=Object(u.a)(),E=Object(l.c)(),O=Object(m.useDispatch)(b.c),y=O.saveSettings,k=O.setPublicationOnboardingStateChanged,j=Object(m.useSelect)((function(e){return e(b.c).getSettings()||{}})),S=j.publicationID,w=j.publicationOnboardingState,x=j.publicationOnboardingStateChanged,C=Object(m.useSelect)((function(e){return e(b.c).hasFinishedResolution("getSettings")})),N=Object(r.useRef)(),A=Object(m.useSelect)((function(e){return e(p.a).isItemDismissed("rrmPublicationApprovedOverlayNotification")})),T=Object(m.useSelect)((function(e){return e(b.c).getServiceURL({path:"reader-revenue-manager",query:{publication:S}})})),D=Object(m.useSelect)((function(e){return e(h.b).getValue(b.k)})),R=!1===A&&!n&&E===v.n&&(!0===D||!0===N.current&&w===_),B=Object(m.useSelect)((function(e){return e(p.a).isDismissingItem("rrmPublicationApprovedOverlayNotification")})),M=Object(m.useDispatch)(h.b).dismissOverlayNotification,I=function(){M("rrmPublicationApprovedOverlayNotification")};return Object(r.useEffect)((function(){C&&void 0===N.current&&(N.current=x,!0===x&&(k(!1),y()))}),[x,y,k,C]),e.createElement(a.a,{className:"googlesitekit-reader-revenue-manager-overlay-notification googlesitekit-reader-revenue-manager-publication-approved-notification",GraphicDesktop:o.a,GraphicMobile:c.a,onShow:function(){Object(f.I)("".concat(t,"_rrm-publication-approved-notification"),"view_notification")},shouldShowNotification:R,notificationID:"rrmPublicationApprovedOverlayNotification"},e.createElement("div",{className:"googlesitekit-overlay-notification__body"},e.createElement("h3",null,Object(i.__)("Your Reader Revenue Manager publication is approved","google-site-kit")),e.createElement("p",null,Object(i.__)("Unlock your full reader opportunity by enabling features like paywall, subscriptions, contributions and newsletter sign ups.","google-site-kit"))),e.createElement("div",{className:"googlesitekit-overlay-notification__actions"},e.createElement(g.Button,{tertiary:!0,disabled:B,onClick:function(){Object(f.I)("".concat(t,"_rrm-publication-approved-notification"),"dismiss_notification").finally((function(){I()}))}},Object(i.__)("Maybe later","google-site-kit")),e.createElement(g.Button,{disabled:B,href:T,onClick:function(){Object(f.I)("".concat(t,"_rrm-publication-approved-notification"),"confirm_notification").finally((function(){I()}))},trailingIcon:e.createElement(d.a,{width:13,height:13}),target:"_blank"},Object(i.__)("Enable features","google-site-kit"))))}))}).call(this,n(4))},558:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReaderRevenueManagerSetupCTABanner}));var r=n(6),i=n.n(r),a=n(1),o=n.n(a),c=n(0),l=n(38),s=n(2),u=n(3),d=n(24),f=n(159),g=n(7),m=n(42),p=n(571),h=n(572),v=n(573),b=n(175),E=n(41),_=n(242),O=n(180),y=n(179),k=n(9);function ReaderRevenueManagerSetupCTABanner(t){var n,r=t.id,a=t.Notification,o=Object(d.e)(),j=Object(f.a)(m.e),S={tooltipSlug:"rrm-setup-notification",content:Object(s.__)("You can always enable Reader Revenue Manager in Settings later","google-site-kit"),dismissLabel:Object(s.__)("Got it","google-site-kit")},w=Object(b.b)(S),x=Object(u.useDispatch)(g.a).triggerSurvey,C=Object(u.useSelect)((function(e){return e(E.a).isNotificationDismissalFinal(r)}));Object(c.useEffect)((function(){x("view_reader_revenue_manager_cta")}),[x]);var N=(n={},i()(n,d.b,v.a),i()(n,d.c,h.a),n);return e.createElement(a,null,e.createElement(_.a,{id:r,title:Object(s.__)("Grow your revenue and deepen reader engagement","google-site-kit"),description:e.createElement("div",{className:"googlesitekit-setup-cta-banner__description"},e.createElement("p",null,Object(l.a)(Object(s.__)("Turn casual visitors into loyal readers and earn more from your content with paywalls, contributions, surveys, newsletter sign-ups and reader insight tools. <a>Learn more</a>","google-site-kit"),{a:e.createElement(O.a,{id:r,label:Object(s.__)("Learn more","google-site-kit"),url:"https://readerrevenue.withgoogle.com"})}))),actions:e.createElement(y.a,{id:r,className:"googlesitekit-setup-cta-banner__actions-wrapper",ctaLabel:Object(s.__)("Set up Reader Revenue Manager","google-site-kit"),onCTAClick:j,dismissLabel:C?Object(s.__)("Don’t show again","google-site-kit"):Object(s.__)("Maybe later","google-site-kit"),onDismiss:w,dismissExpires:2*k.f}),SVG:N[o]||p.a}))}ReaderRevenueManagerSetupCTABanner.propTypes={id:o.a.string,Notification:o.a.elementType}}).call(this,n(4))},559:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return RRMSetupSuccessSubtleNotification}));var i=n(21),a=n.n(i),o=n(5),c=n.n(o),l=n(16),s=n.n(l),u=n(6),d=n.n(u),f=n(15),g=n.n(f),m=n(0),p=n(38),h=n(2),v=n(3),b=n(147),E=n(353),_=n(29),O=n(13),y=n(23),k=n(42),j=n(180),S=n(115),w=n(168),x=n(92),C=k.d.ONBOARDING_COMPLETE,N=k.d.PENDING_VERIFICATION,A=k.d.ONBOARDING_ACTION_REQUIRED;function RRMSetupSuccessSubtleNotification(t){var n=t.id,i=t.Notification,o=Object(b.a)("notification"),l=g()(o,2),u=l[0],f=l[1],T=Object(b.a)("slug"),D=g()(T,2),R=D[0],B=D[1],M=[N,A],I=Object(v.useSelect)((function(e){return e(k.c).getPublicationOnboardingState()})),L=Object(v.useSelect)((function(e){return e(k.c).getPublicationID()})),F=Object(v.useSelect)((function(e){return e(k.c).getServiceURL({path:"reader-revenue-manager",query:{publication:L}})})),P=Object(v.useSelect)((function(e){return e(_.a).getValue(k.f,k.j)&&M.includes(I)})),z=Object(v.useSelect)((function(e){return e(k.c).getPublicationOnboardingState()})),H=Object(v.useSelect)((function(e){return e(k.c).getPaymentOption()})),W=Object(v.useSelect)((function(e){return e(k.c).getProductID()})),G=Object(v.useSelect)((function(e){return e(k.c).getProductIDs()})),V=Object(v.useSelect)((function(e){return e(O.c).getAdminURL("googlesitekit-settings")})),U=Object(v.useDispatch)(_.a).setValues,q=Object(v.useDispatch)(y.b).setValue,K=Object(v.useDispatch)(k.c).syncPublicationOnboardingState,X=Object(m.useCallback)((function(){f(void 0),B(void 0)}),[f,B]),Y=function(t){t.preventDefault(),M.includes(I)&&U(k.f,d()({},k.j,!0)),e.open(F,"_blank")},$=Object(m.useCallback)(s()(c.a.mark((function e(){var t,n,r;return c.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(P){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,K();case 4:t=e.sent,n=t.response,r=null==n?void 0:n.publicationOnboardingState,z&&r!==z&&r===k.d.ONBOARDING_COMPLETE&&q(k.k,!0);case 8:case"end":return e.stop()}}),e)}))),[z,q,P,K]);Object(E.a)($,15e3);var Z="authentication_success"===u&&R===k.e;Object(m.useEffect)((function(){Z&&I===C&&""===H&&(q(k.k,!0),X())}),[X,H,I,q,Z]);var J=!!W&&"openaccess"!==W,Q={gaTrackingEventArgs:{label:"".concat(I,":").concat(H,":").concat(J?"yes":"no")}};if(I===N)return r.createElement(i,Q,r.createElement(S.a,{title:Object(h.__)("Your Reader Revenue Manager account was successfully set up!","google-site-kit"),description:Object(h.__)("Your publication is still awaiting review, you can check its status in Reader Revenue Manager.","google-site-kit"),dismissCTA:r.createElement(x.a,a()({id:n,primary:!1,dismissLabel:Object(h.__)("Got it","google-site-kit"),onDismiss:X},Q)),additionalCTA:r.createElement(w.a,a()({id:n,ctaLabel:Object(h.__)("Check publication status","google-site-kit"),ctaLink:F,onCTAClick:Y,isCTALinkExternal:!0},Q))}));if(I===A)return r.createElement(i,Q,r.createElement(S.a,{title:Object(h.__)("Your Reader Revenue Manager account was successfully set up, but your publication still requires further setup in Reader Revenue Manager.","google-site-kit"),dismissCTA:r.createElement(x.a,a()({id:n,primary:!1,dismissLabel:Object(h.__)("Got it","google-site-kit"),onDismiss:X},Q)),additionalCTA:r.createElement(w.a,a()({id:n,ctaLabel:Object(h.__)("Complete publication setup","google-site-kit"),ctaLink:F,onCTAClick:Y,isCTALinkExternal:!0},Q)),type:"warning"}));if(I===C){if(""===H)return null;var ee={title:Object(h.__)("Success! Your Reader Revenue Manager account is set up","google-site-kit"),description:"",primaryButton:{text:Object(h.__)("Manage CTAs","google-site-kit"),ctaLink:"".concat(V,"#connected-services/reader-revenue-manager/edit"),isCTALinkExternal:!1},secondaryButton:{text:Object(h.__)("Got it","google-site-kit"),onClick:X}};switch(H){case"subscriptions":ee.description="openaccess"===W?Object(h.__)("You can edit your settings to manage product IDs and select which of your site’s pages will include a subscription CTA.","google-site-kit"):Object(h.__)("You can edit your settings and select which of your site’s pages will include a subscription CTA.","google-site-kit");break;case"contributions":G.length>0&&"openaccess"===W?ee.description=Object(h.__)("You can edit your settings to manage product IDs and select which of your site’s pages will include a contribution CTA.","google-site-kit"):ee.description=Object(h.__)("You can edit your settings and select which of your site’s pages will include a contribution CTA.","google-site-kit");break;case"noPayment":ee.description=Object(p.a)(Object(h.__)("Explore Reader Revenue Manager’s additional features, such as paywalls, subscriptions and contributions. <a>Learn more</a>","google-site-kit"),{a:r.createElement(j.a,a()({id:n,ariaLabel:Object(h.__)("Learn more about Reader Revenue Manager features","google-site-kit"),label:Object(h.__)("Learn more","google-site-kit"),url:"https://support.google.com/news/publisher-center/answer/12813936",hideExternalIndicator:!0},Q))}),ee.primaryButton={text:Object(h.__)("Get started","google-site-kit"),ctaLink:F,isCTALinkExternal:!0}}return r.createElement(i,Q,r.createElement(S.a,{title:ee.title,description:ee.description,dismissCTA:r.createElement(x.a,a()({id:n,primary:!1,dismissLabel:ee.secondaryButton.text,onDismiss:ee.secondaryButton.onClick},Q)),additionalCTA:r.createElement(w.a,a()({id:n,ctaLabel:ee.primaryButton.text,ctaLink:ee.primaryButton.ctaLink,isCTALinkExternal:ee.primaryButton.isCTALinkExternal},Q))}))}return null}}).call(this,n(28),n(4))},560:function(e,t,n){"use strict";(function(e){var r=n(15),i=n.n(r),a=n(38),o=n(2),c=n(10),l=n(70),s=n(204),u=n(574),d=n(575),f=n(199),g=n(52),m=n(3),p=n(147),h=n(18),v=n(34),b=n(50),E=n(9),_=n(23),O=n(7),y=n(42),k=n(22),j=n(13),S=y.d.ONBOARDING_COMPLETE;t.a=Object(b.a)({moduleName:y.e})((function RRMIntroductoryOverlayNotification(){var t=Object(v.a)(),n=Object(g.c)(),r=Object(h.a)(),b=Object(p.a)("notification"),w=i()(b,1)[0],x=Object(p.a)("slug"),C=i()(x,1)[0],N=Object(m.useSelect)((function(e){return e(y.c).getSettings()||{}})),A=N.publicationID,T=N.publicationOnboardingState,D=N.paymentOption,R=Object(m.useSelect)((function(e){return e(O.a).isItemDismissed("rrmIntroductoryOverlayNotification")})),B=Object(m.useSelect)((function(e){return e(O.a).isDismissingItem("rrmIntroductoryOverlayNotification")})),M=Object(m.useSelect)((function(e){return e(y.c).getServiceURL({path:"reader-revenue-manager",query:{publication:A}})})),I=Object(m.useSelect)((function(e){return e(j.c).getGoogleSupportURL({path:"/news/publisher-center/answer/********"})})),L=Object(m.useDispatch)(_.b).dismissOverlayNotification,F="authentication_success"===w&&C===y.e,P=!1===R&&!t&&n===k.n&&!F&&T===S&&["noPayment",""].includes(D),z=function(){L("rrmIntroductoryOverlayNotification")},H="".concat(r,"_rrm-introductory-notification"),W="".concat(T,":").concat(D||"");return e.createElement(s.a,{className:"googlesitekit-reader-revenue-manager-overlay-notification googlesitekit-reader-revenue-manager-introductory-notification",GraphicDesktop:u.a,GraphicMobile:d.a,shouldShowNotification:P,notificationID:"rrmIntroductoryOverlayNotification",onShow:function(){Object(E.I)(H,"view_notification",W)}},e.createElement("div",{className:"googlesitekit-overlay-notification__body"},e.createElement("h3",null,"noPayment"===D?Object(o.__)("New! Monetize your content with Reader Revenue Manager","google-site-kit"):Object(o.__)("Complete account setup with Reader Revenue Manager","google-site-kit")),e.createElement("p",null,"noPayment"===D?Object(a.a)(Object(o.__)("Now you can offer your users subscription options to access content behind a paywall, or make voluntary contributions. <a>Learn more</a>","google-site-kit"),{a:e.createElement(f.a,{path:"/news/publisher-center/answer/********",external:!0,hideExternalIndicator:!0,onClick:function(){Object(E.I)(H,"click_learn_more_link",W)}})}):Object(o.__)("Easily monetize your content by offering users subscription options to access content behind a paywall, or make voluntary contributions.","google-site-kit"))),e.createElement("div",{className:"googlesitekit-overlay-notification__actions"},e.createElement(c.Button,{tertiary:!0,disabled:B,onClick:function(){Object(E.I)(H,"dismiss_notification",W).finally((function(){z()}))}},Object(o.__)("Maybe later","google-site-kit")),e.createElement(c.Button,{disabled:B,href:"noPayment"===D?M:I,onClick:function(){Object(E.I)(H,"confirm_notification",W).finally((function(){z()}))},trailingIcon:e.createElement(l.a,{width:13,height:13}),target:"_blank"},"noPayment"===D?Object(o.__)("Explore features","google-site-kit"):Object(o.__)("Learn more","google-site-kit"))))}))}).call(this,n(4))},569:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#reader-revenue-manager-introductory-graphic-desktop_svg__clip0_192_4045)"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h316c8.837 0 16 7.163 16 16v147H0V16z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#reader-revenue-manager-introductory-graphic-desktop_svg__filter0_d_192_4045)"},r.createElement("rect",{x:101.5,y:79,width:195,height:172,rx:13.764,fill:"#fff"})),r.createElement("rect",{x:218,y:108,width:64,height:38,rx:5.161,fill:"#EBEEF0"}),r.createElement("g",{filter:"url(#reader-revenue-manager-introductory-graphic-desktop_svg__filter1_d_192_4045)"},r.createElement("rect",{x:81.5,y:56,width:195,height:172,rx:13.764,fill:"#fff"})),r.createElement("rect",{x:97,y:85,width:165,height:96,rx:5.161,fill:"#EBEEF0"}),r.createElement("rect",{x:96,y:148,width:59,height:55,rx:5.457,fill:"#EBEEF0"}),r.createElement("rect",{x:167,y:148,width:54,height:10,rx:5,fill:"#EBEEF0"}),r.createElement("g",{filter:"url(#reader-revenue-manager-introductory-graphic-desktop_svg__filter2_d_192_4045)"},r.createElement("rect",{x:61.5,y:30,width:195,height:172,rx:13.764,fill:"#fff"})),r.createElement("rect",{x:76.5,y:50,width:165,height:51,rx:5.161,fill:"#6FD3D3"}),r.createElement("rect",{x:147.5,y:113,width:54,height:14,rx:7,fill:"#EBEEF0"}),r.createElement("rect",{x:147.5,y:135,width:94,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("rect",{x:76.5,y:135,width:59,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("rect",{x:76.5,y:124,width:59,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("rect",{x:76.5,y:146,width:59,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("rect",{x:76.5,y:113,width:59,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("rect",{x:147.5,y:146,width:94,height:5,rx:2.5,fill:"#EBEEF0"})),o=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-introductory-graphic-desktop_svg__filter0_d_192_4045",x:91.5,y:73,width:215,height:192,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:5}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_192_4045"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_192_4045",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-introductory-graphic-desktop_svg__filter1_d_192_4045",x:65.5,y:44,width:227,height:204,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_192_4045"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_192_4045",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-introductory-graphic-desktop_svg__filter2_d_192_4045",x:45.5,y:18,width:227,height:204,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_192_4045"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_192_4045",result:"shape"})),r.createElement("clipPath",{id:"reader-revenue-manager-introductory-graphic-desktop_svg__clip0_192_4045"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h316c8.837 0 16 7.163 16 16v147H0V16z",fill:"#fff"})));t.a=function SvgReaderRevenueManagerIntroductoryGraphicDesktop(e){return r.createElement("svg",i({viewBox:"0 0 348 163",fill:"none"},e),a,o)}},57:function(e,t,n){"use strict";(function(e){var r,i;n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=new Set((null===(r=e)||void 0===r||null===(i=r._googlesitekitBaseData)||void 0===i?void 0:i.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,n(28))},570:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#reader-revenue-manager-introductory-graphic-mobile_svg__clip0_584_3892)"},r.createElement("path",{d:"M29.447 64.365c-2.4 22.73 4.803 32.78 23.025 59.949 18.222 27.17-7.404 59.277 20.78 89.869 33.527 36.394 150.685 39.364 201.231 24.212 50.546-15.153 63.581-46.473 59.948-75.155C329.5 124.314 302.482 112.077 289.5 93c-19.276-28.325 2.813-54.786-34.5-77.5s-80.086 6.697-120.326 4.388c-23.216-1.332-46.017-5.627-66.626.968-20.832 6.667-36.72 25.428-38.6 43.509z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#reader-revenue-manager-introductory-graphic-mobile_svg__filter0_d_584_3892)"},r.createElement("rect",{x:108.926,y:53.721,width:158.074,height:139.43,rx:11.158,fill:"#fff"})),r.createElement("rect",{x:203.365,y:77.23,width:51.881,height:30.804,rx:4.184,fill:"#EBEEF0"}),r.createElement("g",{filter:"url(#reader-revenue-manager-introductory-graphic-mobile_svg__filter1_d_584_3892)"},r.createElement("rect",{x:92.713,y:35.077,width:158.074,height:139.43,rx:11.158,fill:"#fff"})),r.createElement("rect",{x:105.277,y:58.585,width:133.755,height:77.821,rx:4.184,fill:"#EBEEF0"}),r.createElement("rect",{x:104.467,y:109.655,width:47.828,height:44.585,rx:4.424,fill:"#EBEEF0"}),r.createElement("rect",{x:162.021,y:109.655,width:43.775,height:8.106,rx:4.053,fill:"#EBEEF0"}),r.createElement("g",{filter:"url(#reader-revenue-manager-introductory-graphic-mobile_svg__filter2_d_584_3892)"},r.createElement("rect",{x:76.5,y:14,width:158.074,height:139.43,rx:11.158,fill:"#fff"})),r.createElement("rect",{x:88.66,y:30.213,width:133.755,height:41.343,rx:4.184,fill:"#6FD3D3"}),r.createElement("rect",{x:146.215,y:81.283,width:43.775,height:11.349,rx:5.674,fill:"#EBEEF0"}),r.createElement("rect",{x:146.215,y:99.117,width:76.2,height:4.053,rx:2.027,fill:"#EBEEF0"}),r.createElement("rect",{x:88.66,y:99.117,width:47.828,height:4.053,rx:2.027,fill:"#EBEEF0"}),r.createElement("rect",{x:88.66,y:90.2,width:47.828,height:4.053,rx:2.027,fill:"#EBEEF0"}),r.createElement("rect",{x:88.66,y:108.034,width:47.828,height:4.053,rx:2.027,fill:"#EBEEF0"}),r.createElement("rect",{x:88.66,y:81.283,width:47.828,height:4.053,rx:2.027,fill:"#EBEEF0"}),r.createElement("rect",{x:146.215,y:108.034,width:76.2,height:4.053,rx:2.027,fill:"#EBEEF0"})),o=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-introductory-graphic-mobile_svg__filter0_d_584_3892",x:95.926,y:43.721,width:184.074,height:165.43,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3}),r.createElement("feGaussianBlur",{stdDeviation:6.5}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_584_3892"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_584_3892",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-introductory-graphic-mobile_svg__filter1_d_584_3892",x:79.743,y:25.349,width:184.015,height:165.37,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.243}),r.createElement("feGaussianBlur",{stdDeviation:6.485}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_584_3892"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_584_3892",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-introductory-graphic-mobile_svg__filter2_d_584_3892",x:63.53,y:4.272,width:184.015,height:165.37,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.243}),r.createElement("feGaussianBlur",{stdDeviation:6.485}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_584_3892"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_584_3892",result:"shape"})),r.createElement("clipPath",{id:"reader-revenue-manager-introductory-graphic-mobile_svg__clip0_584_3892"},r.createElement("path",{fill:"#fff",d:"M0 0h343v118H0z"})));t.a=function SvgReaderRevenueManagerIntroductoryGraphicMobile(e){return r.createElement("svg",i({viewBox:"0 0 343 118",fill:"none"},e),a,o)}},571:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M74.032-2.916C70.583 36.886 92.52 48.59 92.52 103.233c0 54.644-91.918 88.181-52.084 179.603 31.917 73.252 194.23 87.748 287.396 67.074 93.167-20.673 127.301-72.44 122.081-122.662-7.085-68.16-44.978-103.341-59.664-164.902-14.686-61.56 10.917-93.72-27.996-124.067-39.469-30.78-75.267-11.025-116.573-11.025-33.439 0-66.088-25.668-114.278-6.874-29.933 11.674-54.665 45.044-57.369 76.704z",fill:"#B8E6CA"}),o=r.createElement("g",{filter:"url(#reader-revenue-manager-setup_svg__filter0_d_30_1196)"},r.createElement("rect",{x:168.849,y:39.561,width:136,height:270,rx:20,fill:"#fff"}),r.createElement("circle",{cx:54.898,cy:177.171,r:25.926,fill:"#2F9F9F"}),r.createElement("path",{d:"M60.112 170.211c-.678-.753-2.71-2.258-5.42-2.258-3.389 0-5.422 2.258-5.422 4.517 0 6.212 10.842 2.675 10.842 9.035 0 2.259-2.033 4.517-5.42 4.517-2.711 0-4.744-1.506-5.422-2.258M54.69 163.435v27.105",stroke:"#fff",strokeWidth:2,strokeLinecap:"round"}),r.createElement("circle",{cx:348.514,cy:36.337,r:17.485,fill:"#2F9F9F"}),r.createElement("path",{d:"M352.031 31.643c-.457-.508-1.828-1.523-3.656-1.523-2.285 0-3.656 1.523-3.656 3.046 0 4.19 7.312 1.805 7.312 6.094 0 1.523-1.371 3.047-3.656 3.047-1.828 0-3.199-1.016-3.656-1.524M348.375 27.073v18.28",stroke:"#fff",strokeWidth:1.5,strokeLinecap:"round"}),r.createElement("circle",{cx:418.514,cy:149.337,r:21.515,fill:"#2F9F9F"}),r.createElement("path",{d:"M422.841 143.562c-.562-.625-2.249-1.875-4.499-1.875-2.811 0-4.498 1.875-4.498 3.749 0 5.155 8.997 2.22 8.997 7.497 0 1.875-1.687 3.749-4.499 3.749-2.249 0-3.936-1.249-4.498-1.874M418.343 137.939v22.492",stroke:"#fff",strokeWidth:2,strokeLinecap:"round"}),r.createElement("rect",{x:184.94,y:54.47,width:104.361,height:18.417,rx:4,fill:"#F3F5F7"}),r.createElement("rect",{x:184.94,y:104.458,width:104.361,height:114.008,rx:4,fill:"#F3F5F7"}),r.createElement("rect",{x:184.94,y:228.482,width:104.361,height:7.016,rx:3.508,fill:"#F3F5F7"}),r.createElement("rect",{x:184.94,y:241.482,width:104.361,height:7.016,rx:3.508,fill:"#F3F5F7"}),r.createElement("rect",{x:204.234,y:81.656,width:65.774,height:10.524,rx:5.262,fill:"#F3F5F7"}),r.createElement("rect",{x:184.94,y:82.533,width:13.155,height:1.754,rx:.877,fill:"#DEE3E6"}),r.createElement("rect",{x:184.94,y:86.041,width:13.155,height:1.754,rx:.877,fill:"#DEE3E6"}),r.createElement("rect",{x:184.94,y:89.549,width:13.155,height:1.754,rx:.877,fill:"#DEE3E6"})),c=r.createElement("g",{filter:"url(#reader-revenue-manager-setup_svg__filter1_d_30_1196)"},r.createElement("rect",{x:131.451,y:131.057,width:210,height:105,rx:8,fill:"#fff"}),r.createElement("rect",{x:180.451,y:166.057,width:112,height:5,rx:2.5,fill:"#F3F5F7"}),r.createElement("rect",{x:199.451,y:175.057,width:75,height:5,rx:2.5,fill:"#F3F5F7"}),r.createElement("rect",{x:181.849,y:190.057,width:108,height:20,rx:10,fill:"#6FD3D3"})),l=r.createElement("path",{d:"M236.5 158.266c14.762 0 26.638-12.259 26.638-27.266 0-15.007-11.876-27.266-26.638-27.266-14.762 0-26.638 12.259-26.638 27.266 0 15.007 11.876 27.266 26.638 27.266z",fill:"#6FD3D3",stroke:"#fff",strokeWidth:4.276}),s=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M236.38 123.237l-.038-.009v-.686c0-.408-.157-.751-.472-1.029a1.508 1.508 0 00-1.103-.441c-.437 0-.814.147-1.129.441a1.354 1.354 0 00-.446 1.029v.686c-1.4.327-2.537 1.021-3.412 2.082-.875 1.046-1.313 2.246-1.313 3.602v6.86h-2.1v1.96h16.8v-1.96h-2.1v-6.86c0-.363-.031-.715-.094-1.055a5.002 5.002 0 01-4.593-4.62zm-1.613 17.435a2.169 2.169 0 01-1.496-.564c-.403-.392-.604-.857-.604-1.396h4.2c0 .539-.21 1.004-.63 1.396-.402.376-.892.564-1.47.564zM246.5 121.872h-7v-2h7v2z",fill:"#fff"}),u=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M241.867 124.372v-7h2v7h-2z",fill:"#fff"}),d=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-setup_svg__filter0_d_30_1196",x:12.972,y:6.852,width:443.057,height:322.709,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_30_1196"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_30_1196",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-setup_svg__filter1_d_30_1196",x:115.451,y:123.057,width:242,height:137,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:8}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_30_1196"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_30_1196",result:"shape"})));t.a=function SvgReaderRevenueManagerSetup(e){return r.createElement("svg",i({viewBox:"0 0 479 272",fill:"none"},e),a,o,c,l,s,u,d)}},572:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#reader-revenue-manager-setup-tablet_svg__clip0_545_5344)"},r.createElement("path",{d:"M134.999 170.5c0-40.522-17.5-50-18-88s26-61.5 47-66 23.5-1.302 51.5 0 39.5-17.5 79.5-14.5 45.5 19 69 25.5c23.874 6.603 33.464 1.321 48.5 21.5 19.001 25.5-2.875 51.848 8 97.5 14.77 62-276.999 64-285.5 24z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#reader-revenue-manager-setup-tablet_svg__filter0_d_545_5344)"},r.createElement("rect",{x:234.539,y:17.537,width:86.677,height:172.078,rx:12.746,fill:"#fff"}),r.createElement("circle",{cx:169.523,cy:104.523,r:16.523,fill:"#2F9F9F"}),r.createElement("path",{d:"M172.845 100.088c-.432-.48-1.727-1.44-3.455-1.44-2.159 0-3.454 1.44-3.454 2.879 0 3.959 6.909 1.705 6.909 5.758 0 1.44-1.295 2.879-3.455 2.879-1.727 0-3.023-.959-3.454-1.439M169.391 95.769v17.274",stroke:"#fff",strokeWidth:1.275,strokeLinecap:"round"}),r.createElement("circle",{cx:105.5,cy:70.5,r:22.5,fill:"#2F9F9F"}),r.createElement("path",{d:"M110.022 64.46c-.588-.653-2.352-1.96-4.704-1.96-2.941 0-4.705 1.96-4.705 3.92 0 5.392 9.409 2.322 9.409 7.841 0 1.96-1.764 3.92-4.704 3.92-2.352 0-4.117-1.306-4.705-1.96M105.318 58.58v23.522",stroke:"#fff",strokeWidth:1.275,strokeLinecap:"round"}),r.createElement("circle",{cx:359.048,cy:37.482,r:11.144,fill:"#2F9F9F"}),r.createElement("path",{d:"M361.289 34.49c-.291-.323-1.165-.97-2.33-.97-1.456 0-2.33.97-2.33 1.941 0 2.67 4.66 1.15 4.66 3.884 0 .97-.874 1.942-2.33 1.942-1.165 0-2.039-.648-2.33-.971M358.959 31.578v11.65",stroke:"#fff",strokeWidth:.956,strokeLinecap:"round"}),r.createElement("circle",{cx:411.659,cy:109.5,r:13.712,fill:"#2F9F9F"}),r.createElement("path",{d:"M414.416 105.819c-.359-.398-1.434-1.195-2.867-1.195-1.792 0-2.867 1.195-2.867 2.39 0 3.285 5.734 1.414 5.734 4.778 0 1.195-1.075 2.389-2.867 2.389-1.434 0-2.509-.796-2.867-1.194M411.547 102.235v14.335",stroke:"#fff",strokeWidth:1.275,strokeLinecap:"round"}),r.createElement("circle",{cx:457.288,cy:50.288,r:19.288,fill:"#2F9F9F"}),r.createElement("path",{d:"M461.166 45.11c-.504-.56-2.017-1.68-4.033-1.68-2.521 0-4.033 1.68-4.033 3.36 0 4.622 8.066 1.99 8.066 6.722 0 1.68-1.513 3.36-4.033 3.36-2.017 0-3.529-1.12-4.033-1.68M457.131 40.069v20.165",stroke:"#fff",strokeWidth:1.275,strokeLinecap:"round"}),r.createElement("rect",{x:244.795,y:29.038,width:66.512,height:11.737,rx:2.549,fill:"#F3F5F7"}),r.createElement("rect",{x:244.795,y:60.897,width:66.512,height:72.66,rx:2.549,fill:"#F3F5F7"}),r.createElement("rect",{x:244.795,y:137.941,width:66.512,height:4.471,rx:2.236,fill:"#F3F5F7"}),r.createElement("rect",{x:257.094,y:44.365,width:41.919,height:6.707,rx:3.354,fill:"#F3F5F7"}),r.createElement("rect",{x:244.795,y:44.924,width:8.384,height:1.118,rx:.559,fill:"#DEE3E6"}),r.createElement("rect",{x:244.795,y:47.16,width:8.384,height:1.118,rx:.559,fill:"#DEE3E6"}),r.createElement("rect",{x:244.795,y:49.396,width:8.384,height:1.118,rx:.559,fill:"#DEE3E6"})),r.createElement("g",{filter:"url(#reader-revenue-manager-setup-tablet_svg__filter1_d_545_5344)"},r.createElement("rect",{x:211,y:64,width:134,height:63,rx:5.099,fill:"#fff"}),r.createElement("rect",{x:241.936,y:86.156,width:71.381,height:3.187,rx:1.593,fill:"#F3F5F7"}),r.createElement("rect",{x:254.043,y:91.892,width:47.8,height:3.187,rx:1.593,fill:"#F3F5F7"}),r.createElement("rect",{x:242.826,y:101.452,width:68.831,height:12.746,rx:6.373,fill:"#6FD3D3"})),r.createElement("path",{d:"M277.661 81.19c9.409 0 16.977-7.812 16.977-17.377 0-9.564-7.568-17.377-16.977-17.377-9.408 0-16.977 7.813-16.977 17.377 0 9.565 7.569 17.378 16.977 17.378z",fill:"#6FD3D3",stroke:"#fff",strokeWidth:2.725}),r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M277.728 57.666a4.675 4.675 0 00-.166-.04v-.429c0-.255-.1-.469-.301-.642a.97.97 0 00-.702-.275c-.279 0-.519.092-.72.275a.837.837 0 00-.284.642v.428c-.892.204-1.617.637-2.175 1.3a3.357 3.357 0 00-.837 2.247v4.28h-1.338v1.223h10.707v-1.223h-1.338v-3.638a3.187 3.187 0 01-2.846-4.148zm-1.169 10.844c-.368 0-.686-.118-.954-.352a1.166 1.166 0 01-.385-.871h2.677c0 .336-.134.626-.402.871a1.336 1.336 0 01-.936.352zM284.032 57.996h-4.462V56.72h4.462v1.275z",fill:"#fff"}),r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M281.077 59.59v-4.462h1.275v4.461h-1.275z",fill:"#fff"})),o=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-setup-tablet_svg__filter0_d_545_5344",x:72.803,y:9.889,width:413.971,height:192.473,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:2.549}),r.createElement("feGaussianBlur",{stdDeviation:5.099}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_545_5344"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_545_5344",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-setup-tablet_svg__filter1_d_545_5344",x:200.803,y:58.901,width:154.394,height:83.394,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:5.099}),r.createElement("feGaussianBlur",{stdDeviation:5.099}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_545_5344"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_545_5344",result:"shape"})),r.createElement("clipPath",{id:"reader-revenue-manager-setup-tablet_svg__clip0_545_5344"},r.createElement("path",{fill:"#fff",d:"M0 0h553v140H0z"})));t.a=function SvgReaderRevenueManagerSetupTablet(e){return r.createElement("svg",i({viewBox:"0 0 553 140",fill:"none"},e),a,o)}},573:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#reader-revenue-manager-setup-mobile_svg__clip0_545_3220)"},r.createElement("path",{d:"M48.29 62.984C45.736 92.5 61.979 101.179 61.979 141.7c0 40.523-68.064 65.392-38.567 133.189 23.634 54.322 143.822 65.071 212.81 49.74 68.988-15.331 94.263-53.72 90.398-90.963-5.246-50.546-33.305-76.635-44.18-122.287s8.084-69.5-20.73-92.004c-29.226-22.826-55.734-8.177-86.32-8.177-24.76 0-48.937-19.034-84.62-5.097-22.165 8.657-40.478 33.403-42.48 56.882z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#reader-revenue-manager-setup-mobile_svg__filter0_d_545_3220)"},r.createElement("rect",{x:129.539,y:17.537,width:86.677,height:172.078,rx:12.746,fill:"#fff"}),r.createElement("circle",{cx:49.916,cy:77.24,r:16.523,fill:"#2F9F9F"}),r.createElement("path",{d:"M53.238 72.804c-.432-.48-1.728-1.44-3.455-1.44-2.16 0-3.455 1.44-3.455 2.88 0 3.959 6.91 1.704 6.91 5.757 0 1.44-1.296 2.88-3.455 2.88-1.727 0-3.023-.96-3.455-1.44M49.783 68.485V85.76",stroke:"#fff",strokeWidth:1.275,strokeLinecap:"round"}),r.createElement("circle",{cx:254.048,cy:37.482,r:11.144,fill:"#2F9F9F"}),r.createElement("path",{d:"M256.289 34.49c-.291-.323-1.165-.97-2.33-.97-1.456 0-2.33.97-2.33 1.941 0 2.67 4.66 1.15 4.66 3.884 0 .97-.874 1.942-2.33 1.942-1.165 0-2.039-.648-2.33-.971M253.959 31.578v11.65",stroke:"#fff",strokeWidth:.956,strokeLinecap:"round"}),r.createElement("circle",{cx:306.659,cy:109.5,r:13.712,fill:"#2F9F9F"}),r.createElement("path",{d:"M309.416 105.819c-.359-.398-1.434-1.195-2.867-1.195-1.792 0-2.867 1.195-2.867 2.39 0 3.285 5.734 1.414 5.734 4.778 0 1.195-1.075 2.389-2.867 2.389-1.434 0-2.509-.796-2.867-1.194M306.547 102.235v14.335",stroke:"#fff",strokeWidth:1.275,strokeLinecap:"round"}),r.createElement("rect",{x:139.795,y:29.038,width:66.512,height:11.737,rx:2.549,fill:"#F3F5F7"}),r.createElement("rect",{x:139.795,y:60.897,width:66.512,height:72.66,rx:2.549,fill:"#F3F5F7"}),r.createElement("rect",{x:139.795,y:137.941,width:66.512,height:4.471,rx:2.236,fill:"#F3F5F7"}),r.createElement("rect",{x:152.094,y:44.365,width:41.919,height:6.707,rx:3.354,fill:"#F3F5F7"}),r.createElement("rect",{x:139.795,y:44.924,width:8.384,height:1.118,rx:.559,fill:"#DEE3E6"}),r.createElement("rect",{x:139.795,y:47.16,width:8.384,height:1.118,rx:.559,fill:"#DEE3E6"}),r.createElement("rect",{x:139.795,y:49.396,width:8.384,height:1.118,rx:.559,fill:"#DEE3E6"})),r.createElement("g",{filter:"url(#reader-revenue-manager-setup-mobile_svg__filter1_d_545_3220)"},r.createElement("rect",{x:106,y:64,width:134,height:63,rx:5.099,fill:"#fff"}),r.createElement("rect",{x:136.936,y:86.156,width:71.381,height:3.187,rx:1.593,fill:"#F3F5F7"}),r.createElement("rect",{x:149.043,y:91.892,width:47.8,height:3.187,rx:1.593,fill:"#F3F5F7"}),r.createElement("rect",{x:137.826,y:101.452,width:68.831,height:12.746,rx:6.373,fill:"#6FD3D3"})),r.createElement("path",{d:"M172.661 81.19c9.409 0 16.977-7.812 16.977-17.377 0-9.564-7.568-17.377-16.977-17.377-9.408 0-16.977 7.813-16.977 17.377 0 9.565 7.569 17.378 16.977 17.378z",fill:"#6FD3D3",stroke:"#fff",strokeWidth:2.725}),r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M172.728 57.666a4.675 4.675 0 00-.166-.04v-.429c0-.255-.1-.469-.301-.642a.97.97 0 00-.702-.275c-.279 0-.519.092-.72.275a.837.837 0 00-.284.642v.428c-.892.204-1.617.637-2.175 1.3a3.357 3.357 0 00-.837 2.247v4.28h-1.338v1.223h10.707v-1.223h-1.338v-3.638a3.187 3.187 0 01-2.846-4.148zm-1.169 10.844c-.368 0-.686-.118-.954-.352a1.166 1.166 0 01-.385-.871h2.677c0 .336-.134.626-.402.871a1.336 1.336 0 01-.936.352zM179.032 57.996h-4.462V56.72h4.462v1.275z",fill:"#fff"}),r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M176.077 59.59v-4.462h1.275v4.461h-1.275z",fill:"#fff"})),o=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-setup-mobile_svg__filter0_d_545_3220",x:23.195,y:9.889,width:307.373,height:192.473,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:2.549}),r.createElement("feGaussianBlur",{stdDeviation:5.099}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_545_3220"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_545_3220",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-setup-mobile_svg__filter1_d_545_3220",x:95.803,y:58.901,width:154.394,height:83.394,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:5.099}),r.createElement("feGaussianBlur",{stdDeviation:5.099}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_545_3220"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_545_3220",result:"shape"})),r.createElement("clipPath",{id:"reader-revenue-manager-setup-mobile_svg__clip0_545_3220"},r.createElement("path",{fill:"#fff",d:"M0 0h343v140H0z"})));t.a=function SvgReaderRevenueManagerSetupMobile(e){return r.createElement("svg",i({viewBox:"0 0 343 140",fill:"none"},e),a,o)}},574:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#reader-revenue-manager-monetize-graphic-desktop_svg__clip0_2428_20430)"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h316c8.837 0 16 7.163 16 16v147H0V16z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#reader-revenue-manager-monetize-graphic-desktop_svg__filter0_d_2428_20430)"},r.createElement("rect",{x:48,y:65,width:252,height:200,rx:20,fill:"#fff"})),r.createElement("rect",{x:69,y:92,width:133,height:11,rx:5.5,fill:"#F3F5F7"}),r.createElement("rect",{x:219,y:95,width:61,height:87,rx:4,fill:"#F3F5F7"}),r.createElement("rect",{x:69,y:133,width:133,height:11,rx:5.5,fill:"#F3F5F7"}),r.createElement("rect",{x:69,y:114,width:133,height:11,rx:5.5,fill:"#F3F5F7"}),r.createElement("rect",{x:69,y:152,width:133,height:11,rx:5.5,fill:"#F3F5F7"}),r.createElement("g",{filter:"url(#reader-revenue-manager-monetize-graphic-desktop_svg__filter1_d_2428_20430)"},r.createElement("rect",{x:98,y:25,width:152,height:174,rx:20,fill:"#fff"})),r.createElement("rect",{x:146,y:116,width:56,height:20,rx:10,fill:"#6FD3D3"}),r.createElement("rect",{x:132,y:74,width:84,height:11,rx:5.5,fill:"#F3F5F7"}),r.createElement("rect",{x:132,y:91,width:84,height:11,rx:5.5,fill:"#F3F5F7"}),r.createElement("mask",{id:"reader-revenue-manager-monetize-graphic-desktop_svg__a",fill:"#fff"},r.createElement("rect",{x:165.333,y:46.929,width:16.667,height:14.286,rx:1.786})),r.createElement("rect",{x:165.333,y:46.929,width:16.667,height:14.286,rx:1.786,stroke:"#6FD3D3",strokeWidth:4.444,mask:"url(#reader-revenue-manager-monetize-graphic-desktop_svg__a)"}),r.createElement("path",{d:"M169.5 47.524v-2.38a4.166 4.166 0 014.167-4.167v0a4.166 4.166 0 014.166 4.166v2.381",stroke:"#6FD3D3",strokeWidth:2.222}),r.createElement("circle",{cx:173.667,cy:54.072,r:1.786,fill:"#6FD3D3"})),o=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-monetize-graphic-desktop_svg__filter0_d_2428_20430",x:32,y:53,width:284,height:232,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2428_20430"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2428_20430",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-monetize-graphic-desktop_svg__filter1_d_2428_20430",x:82,y:17,width:184,height:206,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:8}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2428_20430"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2428_20430",result:"shape"})),r.createElement("clipPath",{id:"reader-revenue-manager-monetize-graphic-desktop_svg__clip0_2428_20430"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h316c8.837 0 16 7.163 16 16v147H0V16z",fill:"#fff"})));t.a=function SvgReaderRevenueManagerMonetizeGraphicDesktop(e){return r.createElement("svg",i({viewBox:"0 0 348 163",fill:"none"},e),a,o)}},575:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#reader-revenue-manager-monetize-graphic-mobile_svg__clip0_2084_4987)"},r.createElement("path",{d:"M29.447 64.365c-2.4 22.73 4.803 32.78 23.025 59.949 18.222 27.17-7.404 59.276 20.78 89.869 33.527 36.394 150.685 39.364 201.231 24.211 50.546-15.152 63.581-46.473 59.948-75.154C329.5 124.314 302.482 112.076 289.5 93c-19.276-28.325 2.813-54.786-34.5-77.5s-80.086 6.696-120.326 4.388c-23.216-1.332-46.017-5.627-66.626.968-20.832 6.667-36.72 25.427-38.6 43.509z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#reader-revenue-manager-monetize-graphic-mobile_svg__filter0_d_2084_4987)"},r.createElement("rect",{x:76,y:48.778,width:196,height:155.556,rx:15.556,fill:"#fff"})),r.createElement("rect",{x:92.334,y:69.778,width:103.444,height:8.556,rx:4.278,fill:"#F3F5F7"}),r.createElement("rect",{x:209,y:72.111,width:47.444,height:67.667,rx:3.111,fill:"#F3F5F7"}),r.createElement("rect",{x:92.334,y:101.667,width:103.444,height:8.556,rx:4.278,fill:"#F3F5F7"}),r.createElement("rect",{x:92.334,y:86.889,width:103.444,height:8.556,rx:4.278,fill:"#F3F5F7"}),r.createElement("rect",{x:92.334,y:116.444,width:103.444,height:8.556,rx:4.278,fill:"#F3F5F7"}),r.createElement("g",{filter:"url(#reader-revenue-manager-monetize-graphic-mobile_svg__filter1_d_2084_4987)"},r.createElement("rect",{x:114.889,y:17.666,width:118.222,height:135.333,rx:15.556,fill:"#fff"})),r.createElement("rect",{x:152.223,y:88.444,width:43.556,height:15.556,rx:7.778,fill:"#6FD3D3"}),r.createElement("rect",{x:141.334,y:55.778,width:65.333,height:8.556,rx:4.278,fill:"#F3F5F7"}),r.createElement("rect",{x:141.334,y:69,width:65.333,height:8.556,rx:4.278,fill:"#F3F5F7"}),r.createElement("mask",{id:"reader-revenue-manager-monetize-graphic-mobile_svg__a",fill:"#fff"},r.createElement("rect",{x:167.26,y:34.722,width:12.963,height:11.111,rx:1.389})),r.createElement("rect",{x:167.26,y:34.722,width:12.963,height:11.111,rx:1.389,stroke:"#6FD3D3",strokeWidth:3.457,mask:"url(#reader-revenue-manager-monetize-graphic-mobile_svg__a)"}),r.createElement("path",{d:"M170.5 35.185v-1.852a3.24 3.24 0 013.241-3.24v0a3.24 3.24 0 013.24 3.24v1.852",stroke:"#6FD3D3",strokeWidth:1.728}),r.createElement("circle",{cx:173.74,cy:40.278,r:1.389,fill:"#6FD3D3"})),o=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-monetize-graphic-mobile_svg__filter0_d_2084_4987",x:63.556,y:39.444,width:220.889,height:180.445,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.111}),r.createElement("feGaussianBlur",{stdDeviation:6.222}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2084_4987"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2084_4987",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-monetize-graphic-mobile_svg__filter1_d_2084_4987",x:102.444,y:11.444,width:143.112,height:160.222,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:6.222}),r.createElement("feGaussianBlur",{stdDeviation:6.222}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2084_4987"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2084_4987",result:"shape"})),r.createElement("clipPath",{id:"reader-revenue-manager-monetize-graphic-mobile_svg__clip0_2084_4987"},r.createElement("path",{fill:"#fff",d:"M0 0h343v118H0z"})));t.a=function SvgReaderRevenueManagerMonetizeGraphicMobile(e){return r.createElement("svg",i({viewBox:"0 0 343 118",fill:"none"},e),a,o)}},579:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"currentColor",fillRule:"evenodd"},r.createElement("path",{d:"M0 6.414L1.415 5l5.292 5.292-1.414 1.415z"}),r.createElement("path",{d:"M14.146.146l1.415 1.414L5.414 11.707 4 10.292z"}));t.a=function SvgCheck(e){return r.createElement("svg",i({viewBox:"0 0 16 12"},e),a)}},58:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return r.createElement("svg",i({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),a,o)}},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(39);function i(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},606:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetNewBadge}));var r=n(1),i=n.n(r),a=n(0),o=n(3),c=n(7),l=n(302),s=n(9),u=n(47);function WidgetNewBadge(t){var n=t.slug,r=Object(o.useSelect)((function(e){return e(u.a).getWidgetArea(n)})).hasNewBadge,i="widget-area-expirable-new-badge-".concat(n),d=Object(o.useSelect)((function(e){return e(c.a).hasExpirableItem(i)})),f=Object(o.useSelect)((function(e){return e(c.a).isExpirableItemActive(i)})),g=r&&(!1===d||f),m=Object(o.useDispatch)(c.a).setExpirableItemTimers;return Object(a.useEffect)((function(){void 0!==d&&void 0!==f&&r&&!d&&m([{slug:i,expiresInSeconds:4*s.f}])}),[r,i,d,f,m]),!!g&&e.createElement(l.a,null)}WidgetNewBadge.propTypes={slug:i.a.string.isRequired}}).call(this,n(4))},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(0),i=Object(r.createContext)(""),a=(i.Consumer,i.Provider);t.b=i},627:function(e,t,n){"use strict";n.d(t,"a",(function(){return ScrollEffect}));var r=n(714);function ScrollEffect(){return Object(r.a)(),null}},628:function(e,t,n){"use strict";(function(e){var r=n(15),i=n.n(r),a=n(11),o=n.n(a),c=n(0),l=n(2),s=n(3),u=n(142),d=n(23),f=n(201),g=n(10);t.a=function OfflineNotification(){var t=Object(c.useState)(!1),n=i()(t,2),r=n[0],a=n[1],m=Object(s.useSelect)((function(e){return e(d.b).getIsOnline()}));return Object(c.useEffect)((function(){m&&r&&a(!1)}),[m,r]),e.createElement("div",{"aria-live":"polite"},!m&&!r&&e.createElement("div",{className:o()("googlesitekit-margin-top-0","googlesitekit-margin-bottom-0","googlesitekit-settings-notice-offline-notice","googlesitekit-settings-notice","googlesitekit-settings-notice--single-row","googlesitekit-settings-notice--".concat(u.b))},e.createElement("div",{className:"googlesitekit-settings-notice__body"},e.createElement(f.a,{notice:Object(l.__)("You are currently offline. Some features may not be available.","google-site-kit")})),e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(g.Button,{onClick:function(){a(!0)}},Object(l.__)("OK, Got it!","google-site-kit")))))}}).call(this,n(4))},629:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleDashboardEffects}));var r,i=n(27),a=n.n(i),o=n(6),c=n.n(o),l=n(3),s=n(19),u=n(52),d=(r={},c()(r,u.b,"DashboardMainEffectComponent"),c()(r,u.a,"DashboardEntityEffectComponent"),r);function ModuleDashboardEffects(){var t=Object(u.c)(),n=Object(l.useSelect)((function(e){return e(s.a).getModules()}));if(!n)return null;var r=d[t];return Object.values(n).reduce((function(t,n){var i=n[r];return n.active&&i?[].concat(a()(t),[e.createElement(i,{key:n.slug})]):t}),[])}}).call(this,n(4))},65:function(e,t,n){"use strict";n.d(t,"c",(function(){return p})),n.d(t,"a",(function(){return h})),n.d(t,"b",(function(){return v})),n.d(t,"d",(function(){return E}));var r=n(6),i=n.n(r),a=n(0);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var c=a.createElement("path",{d:"M10 15.27L16.18 19l-1.64-7.03L20 7.24l-7.19-.61L10 0 7.19 6.63 0 7.24l5.46 4.73L3.82 19 10 15.27z"});var l=function SvgInfoIcon(e){return a.createElement("svg",o({viewBox:"0 0 20 20",fill:"currentColor"},e),c)};function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var u=a.createElement("path",{d:"M0 4h2v7H0zm0-4h2v2H0z",fill:"currentColor",fillRule:"evenodd"});var d,f=function SvgSuggestionIcon(e){return a.createElement("svg",s({viewBox:"0 0 2 11"},e),u)},g=n(186),m=n(74),p="warning",h="info",v="suggestion",b=(d={},i()(d,h,l),i()(d,p,g.a),i()(d,v,f),d),E=function(e){return b[e]||m.a}},695:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardSharingSettingsButton}));var r=n(2),i=n(0),a=n(3),o=n(10),c=n(415),l=n(18),s=n(9),u=n(29),d=n(23),f=n(13),g=n(8),m=n(171),p=n(696);function DashboardSharingSettingsButton(){var t=Object(l.a)(),n=Object(a.useDispatch)(d.b).setValue,h=Object(a.useSelect)((function(e){return e(f.c).hasMultipleAdmins()})),v=Object(a.useSelect)((function(e){return e(u.a).getValue(g.d,"isAutoCreatingCustomDimensionsForAudience")})),b=Object(i.useCallback)((function(){Object(s.I)("".concat(t,"_headerbar"),"open_sharing",h?"advanced":"simple"),n(m.c,!0)}),[n,t,h]);return e.createElement(i.Fragment,null,e.createElement(o.Button,{"aria-label":Object(r.__)("Open sharing settings","google-site-kit"),className:"googlesitekit-sharing-settings__button googlesitekit-header__dropdown googlesitekit-border-radius-round googlesitekit-button-icon",onClick:b,icon:e.createElement(c.a,{width:20,height:20}),tooltipEnterDelayInMS:500,disabled:v}),e.createElement(p.a,null))}}).call(this,n(4))},696:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardSharingDialog}));var r=n(813),i=n(11),a=n.n(i),o=n(2),c=n(0),l=n(38),s=n(266),u=n(1216),d=n(3),f=n(10),g=n(23),m=n(13),p=n(19),h=n(171),v=n(24),b=n(72),E=n(17),_=n(415),O=n(20),y=n(697),k=n(700);function DashboardSharingDialog(){var t=Object(v.e)(),n=Object(r.a)().y,i=Object(d.useDispatch)(g.b).setValue,j=Object(d.useDispatch)(p.a).rollbackSharingSettings,S=Object(d.useSelect)((function(e){return!!e(g.b).getValue(h.c)})),w=Object(d.useSelect)((function(e){return!!e(g.b).getValue(h.b)})),x=Object(d.useSelect)((function(e){return e(g.b).getValue(h.a)})),C=Object(d.useSelect)((function(e){return e(p.a).haveSharingSettingsChanged()})),N=Object(d.useSelect)((function(e){return e(m.c).getDocumentationLinkURL("dashboard-sharing")})),A={};t===v.b&&(A.top="".concat(n<46?46-n:0,"px"),A.height="calc(100% - 46px + ".concat(n<46?n:46,"px)")),Object(c.useEffect)((function(){!S&&C&&j()}),[S,C,j]);var T=Object(c.useCallback)((function(){i(h.c,!0)}),[i]),D=Object(c.useCallback)((function(){i(h.c,!1),i(h.a,void 0)}),[i]),R=Object(c.useCallback)((function(){D(),i(h.b,!0)}),[D,i]),B=Object(c.useCallback)((function(){i(h.b,!1),T()}),[T,i]),M=Object(c.useCallback)((function(){if(w)return B(),null;D()}),[B,D,w]);return e.createElement(b.a,null,e.createElement(E.b,{open:S||w,onClose:M,className:"googlesitekit-dialog googlesitekit-sharing-settings-dialog",style:A,escapeKeyAction:void 0===x?"close":""},e.createElement("div",{className:"googlesitekit-dialog__back-wrapper","aria-hidden":t!==v.b},e.createElement(f.Button,{"aria-label":Object(o.__)("Back","google-site-kit"),className:"googlesitekit-dialog__back",onClick:M},e.createElement(s.a,{icon:u.a}))),e.createElement(E.c,{className:"googlesitekit-dialog__content"},e.createElement("div",{className:"googlesitekit-dialog__header"},S&&e.createElement("div",{className:"googlesitekit-dialog__header-icon","aria-hidden":t===v.b},e.createElement("span",null,e.createElement(_.a,{width:20,height:20}))),e.createElement("div",{className:"googlesitekit-dialog__header-titles"},e.createElement("h2",{className:"googlesitekit-dialog__title"},S&&Object(o.__)("Dashboard sharing & permissions","google-site-kit"),w&&Object(o.__)("Reset Dashboard Sharing permissions","google-site-kit")),e.createElement("p",{className:a()("googlesitekit-dialog__subtitle",{"googlesitekit-dialog__subtitle--emphasis":w})},S&&Object(l.a)(Object(o.__)("Share a view-only version of your Site Kit dashboard with other WordPress roles. <a>Learn more</a>","google-site-kit"),{a:e.createElement(O.a,{"aria-label":Object(o.__)("Learn more about dashboard sharing","google-site-kit"),href:N,external:!0})}),w&&Object(o.__)("Warning: Resetting these permissions will remove view-only access for all users. Are you sure you want to reset all Dashboard Sharing permissions?","google-site-kit")))),S&&e.createElement("div",{className:"googlesitekit-dialog__main"},e.createElement(y.a,null))),e.createElement(E.d,{className:"googlesitekit-dialog__footer"},e.createElement(k.a,{closeDialog:M,openResetDialog:R}))))}}).call(this,n(4))},697:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return DashboardSharingSettings}));var i=n(11),a=n.n(i),o=n(2),c=n(3),l=n(698),s=n(19),u=n(13),d=n(7);function DashboardSharingSettings(){var t=Object(c.useSelect)((function(e){return e(s.a).hasRecoverableModules()})),n=Object(c.useSelect)((function(e){return e(u.c).hasMultipleAdmins()})),i=t||n,f=Object(c.useSelect)((function(t){for(var n=t(d.a).getID(),r=t(s.a).getShareableModules(),i=[],a=[],o=[],c=0,l=Object.values(r);c<l.length;c++){var u;(null===(u=(e=l[c]).owner)||void 0===u?void 0:u.id)===n?i.push(e):t(d.a).hasCapability(d.J,e.slug)?a.push(e):o.push(e)}return[].concat(i,a,o)}));return void 0===f?null:r.createElement("div",{className:a()("googlesitekit-dashboard-sharing-settings",{"googlesitekit-dashboard-sharing-settings--has-multiple-admins":i})},r.createElement("header",{className:"googlesitekit-dashboard-sharing-settings__header googlesitekit-dashboard-sharing-settings__row"},r.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__column--product"},Object(o.__)("Product","google-site-kit")),r.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__column--view"},Object(o.__)("Who can view","google-site-kit")),i&&r.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__column--manage"},Object(o.__)("Who can manage view access","google-site-kit"))),r.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__main"},f.map((function(e){var t=e.slug,n=e.name,i=e.owner,a=e.recoverable;return r.createElement(l.a,{key:t,moduleSlug:t,moduleName:n,ownerUsername:null==i?void 0:i.login,recoverable:a})}))))}}).call(this,n(948)(e),n(4))},698:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Module}));var r=n(15),i=n.n(r),a=n(1),o=n.n(a),c=n(11),l=n.n(c),s=n(2),u=n(266),d=n(589),f=n(0),g=n(38),m=n(10),p=n(3),h=n(198),v=n(699),b=n(18),E=n(19),_=n(13),O=n(23),y=n(171),k=n(9),j=n(7),S=n(217),w=n(20),x=[{value:"owner",label:Object(s.__)("Only me","google-site-kit")},{value:"all_admins",label:Object(s.__)("Any admin signed in with Google","google-site-kit")}];function Module(t){var n=t.moduleSlug,r=t.moduleName,a=t.ownerUsername,o=t.recoverable,c=Object(b.a)(),C=Object(f.useRef)(),N=Object(f.useState)(void 0),A=i()(N,2),T=A[0],D=A[1],R=Object(p.useSelect)((function(e){return e(E.a).hasRecoverableModules()})),B=Object(p.useSelect)((function(e){return e(_.c).hasMultipleAdmins()})),M=R||B,I=Object(p.useSelect)((function(e){var t;return null!==(t=e(E.a).getSharingManagement(n))&&void 0!==t?t:"owner"})),L=Object(p.useSelect)((function(e){return e(j.a).hasCapability(j.I,n)})),F=Object(p.useSelect)((function(e){return e(j.a).hasCapability(j.J,n)})),P=Object(p.useSelect)((function(e){return e(E.a).getSharedOwnershipModules()})),z=Object(p.useSelect)((function(e){return e(O.b).getValue(y.a)})),H=Object(p.useSelect)((function(e){return e(E.a).isDoingSubmitSharingChanges()})),W=Object(p.useSelect)((function(e){return e(_.c).getDocumentationLinkURL("dashboard-sharing-module-recovery")})),G=Object(p.useDispatch)(E.a).setSharingManagement,V=P&&Object.keys(P).includes(n);Object(f.useEffect)((function(){D(V?"all_admins":I)}),[I,V]);var U=Object(p.useSelect)((function(e){return e(E.a).haveModuleSharingSettingsChanged(n,"management")}));Object(f.useEffect)((function(){U&&Object(k.I)("".concat(c,"_sharing"),"change_management_".concat(I),n)}),[U,I,n,c]);var q=Object(f.useCallback)((function(e){var t=e.target.value;D(t),G(n,t)}),[G,D,n]),K=n===z,X=!K&&void 0!==z||H;return e.createElement("div",{className:l()("googlesitekit-dashboard-sharing-settings__module","googlesitekit-dashboard-sharing-settings__row",{"googlesitekit-dashboard-sharing-settings__row--editing":K,"googlesitekit-dashboard-sharing-settings__row--disabled":X}),ref:C},e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__column--product"},e.createElement(h.a,{slug:n,size:48}),e.createElement("span",{className:"googlesitekit-dashboard-sharing-settings__module-name"},r)),e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__column--view"},F&&e.createElement(v.a,{moduleSlug:n,isLocked:X,ref:C}),o&&e.createElement(S.a,null,Object(g.a)(Object(s.__)("Managing user required to manage view access. <Link>Learn more</Link>","google-site-kit"),{Link:e.createElement(w.a,{href:W,external:!0,hideExternalIndicator:!0})})),!F&&!o&&e.createElement("p",{className:"googlesitekit-dashboard-sharing-settings__note"},Object(s.__)("Contact managing user to manage view access","google-site-kit"))),M&&e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__column--manage"},V&&e.createElement("p",{className:"googlesitekit-dashboard-sharing-settings__note"},e.createElement("span",null,Object(s.__)("Any admin signed in with Google","google-site-kit")),e.createElement(m.Tooltip,{title:Object(s.__)("This service requires general access to Google APIs rather than access to a specific user-owned property/entity, so view access is manageable by any admin signed in with Google.","google-site-kit")},e.createElement("span",{className:"googlesitekit-dashboard-sharing-settings__tooltip-icon"},e.createElement(u.a,{icon:d.a,size:18})))),!V&&L&&e.createElement(m.Select,{className:"googlesitekit-dashboard-sharing-settings__select",value:T,options:x,onChange:q,onClick:q,outlined:!0}),!V&&!L&&a&&e.createElement("p",{className:"googlesitekit-dashboard-sharing-settings__note"},Object(g.a)(Object(s.sprintf)(
/* translators: %s: user who manages the module. */
Object(s.__)("<span>Managed by</span> <strong>%s</strong>","google-site-kit"),a),{span:e.createElement("span",null),strong:e.createElement("strong",null)}),e.createElement(m.Tooltip,{title:F?Object(s.sprintf)(
/* translators: %s: name of the user who manages the module. */
Object(s.__)("%s has connected this and given managing permissions to all admins. You can change who can view this on the dashboard.","google-site-kit"),a):Object(s.sprintf)(
/* translators: %s: name of the user who manages the module. */
Object(s.__)("Contact %s to change who can manage view access for this module","google-site-kit"),a)},e.createElement("span",{className:"googlesitekit-dashboard-sharing-settings__tooltip-icon"},e.createElement(u.a,{icon:d.a,size:18}))))))}Module.propTypes={moduleSlug:o.a.string.isRequired,moduleName:o.a.string.isRequired,ownerUsername:o.a.string}}).call(this,n(4))},699:function(e,t,n){"use strict";(function(e){var r=n(27),i=n.n(r),a=n(1),o=n.n(a),c=n(11),l=n.n(c),s=n(2),u=n(56),d=n(0),f=n(3),g=n(10),m=n(20),p=n(415),h=n(579),v=n(18),b=n(112),E=n(9),_=n(19),O=n(23),y=n(171),k=Object(s.__)("All","google-site-kit"),j=Object(d.forwardRef)((function(t,n){var r=t.moduleSlug,a=t.isLocked,o=void 0!==a&&a,c=Object(v.a)(),j=Object(d.useRef)(),S=Object(f.useDispatch)(_.a).setSharedRoles,w=Object(f.useDispatch)(O.b).setValue,x=Object(f.useSelect)((function(e){return e(_.a).getShareableRoles()})),C=Object(f.useSelect)((function(e){return e(_.a).getSharedRoles(r)})),N=Object(f.useSelect)((function(e){return e(O.b).getValue(y.a)}))===r;Object(b.a)([u.c],n,(function(){N&&w(y.a,void 0)}));var A=Object(f.useSelect)((function(e){return e(_.a).haveModuleSharingSettingsChanged(r,"sharedRoles")})),T=Object(d.useCallback)((function(){N?(w(y.a,void 0),A&&Object(E.I)("".concat(c,"_sharing"),"change_shared_roles",r)):w(y.a,r)}),[N,A,r,w,c]);Object(d.useEffect)((function(){j.current&&(N?j.current.firstChild.focus():j.current.focus())}),[N]);var D=Object(d.useCallback)((function(e){var t,n=e.type,a=e.target,o=e.keyCode;if("keydown"!==n||o===u.b){var c,l=a.closest(".mdc-chip"),s=null==l||null===(t=l.dataset)||void 0===t?void 0:t.chipId;if(s)c="all"===s?(null==C?void 0:C.length)===(null==x?void 0:x.length)?[]:x.map((function(e){return e.id})):null===C?[s]:C.includes(s)?C.filter((function(e){return e!==s})):[].concat(i()(C),[s]),S(r,c)}}),[r,S,C,x]);return x?e.createElement("div",{className:l()("googlesitekit-user-role-select",{"googlesitekit-user-role-select--open":N})},!N&&e.createElement(g.Button,{"aria-label":Object(s.__)("Edit roles","google-site-kit"),className:"googlesitekit-user-role-select__button",onClick:T,icon:e.createElement(p.a,{width:23,height:23}),tabIndex:o?-1:void 0,ref:j}),!N&&(null==C?void 0:C.length)>0&&e.createElement("span",{className:"googlesitekit-user-role-select__current-roles"},(null==x?void 0:x.reduce((function(e,t){return C.includes(t.id)&&e.push(t.displayName),e}),[])).join(", ")),!N&&(!C||0===(null==C?void 0:C.length))&&e.createElement("span",{className:"googlesitekit-user-role-select__add-roles"},e.createElement(m.a,{onClick:T,tabIndex:o?-1:void 0},Object(s.__)("Add roles","google-site-kit"))),N&&e.createElement(d.Fragment,null,e.createElement("div",{className:"googlesitekit-user-role-select__chipset",ref:j},e.createElement(g.Chip,{id:"all",label:k,onClick:D,onKeyDown:D,selected:(null==C?void 0:C.length)===(null==x?void 0:x.length),className:"googlesitekit-user-role-select__chip--all"}),x.map((function(t,n){var r=t.id,i=t.displayName;return e.createElement(g.Chip,{key:n,id:r,label:i,onClick:D,onKeyDown:D,selected:null==C?void 0:C.includes(r)})}))),e.createElement(g.Button,{"aria-label":Object(s.__)("Done editing roles","google-site-kit"),title:Object(s.__)("Done","google-site-kit"),className:"googlesitekit-user-role-select__button",onClick:T,icon:e.createElement(h.a,{width:18,height:18}),tabIndex:o?-1:void 0}))):null}));j.propTypes={moduleSlug:o.a.string.isRequired,isLocked:o.a.bool},t.a=j}).call(this,n(4))},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return l})),n.d(t,"M",(function(){return s})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return f})),n.d(t,"J",(function(){return g})),n.d(t,"I",(function(){return m})),n.d(t,"N",(function(){return p})),n.d(t,"f",(function(){return h})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return b})),n.d(t,"j",(function(){return E})),n.d(t,"l",(function(){return _})),n.d(t,"m",(function(){return O})),n.d(t,"n",(function(){return y})),n.d(t,"o",(function(){return k})),n.d(t,"q",(function(){return j})),n.d(t,"s",(function(){return S})),n.d(t,"r",(function(){return w})),n.d(t,"t",(function(){return x})),n.d(t,"w",(function(){return C})),n.d(t,"u",(function(){return N})),n.d(t,"v",(function(){return A})),n.d(t,"x",(function(){return T})),n.d(t,"y",(function(){return D})),n.d(t,"A",(function(){return R})),n.d(t,"B",(function(){return B})),n.d(t,"C",(function(){return M})),n.d(t,"D",(function(){return I})),n.d(t,"k",(function(){return L})),n.d(t,"F",(function(){return F})),n.d(t,"z",(function(){return P})),n.d(t,"G",(function(){return z})),n.d(t,"E",(function(){return H})),n.d(t,"i",(function(){return W})),n.d(t,"p",(function(){return G})),n.d(t,"Q",(function(){return V})),n.d(t,"P",(function(){return U}));var r="core/user",i="connected_url_mismatch",a="__global",o="temporary_persist_permission_error",c="adblocker_active",l="googlesitekit_authenticate",s="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",f="googlesitekit_read_shared_module_data",g="googlesitekit_manage_module_sharing_options",m="googlesitekit_delegate_module_sharing_management",p="googlesitekit_update_plugins",h="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",b="kmAnalyticsLeastEngagingPages",E="kmAnalyticsNewVisitors",_="kmAnalyticsPopularAuthors",O="kmAnalyticsPopularContent",y="kmAnalyticsPopularProducts",k="kmAnalyticsReturningVisitors",j="kmAnalyticsTopCities",S="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",x="kmAnalyticsTopCitiesDrivingPurchases",C="kmAnalyticsTopDeviceDrivingPurchases",N="kmAnalyticsTopConvertingTrafficSource",A="kmAnalyticsTopCountries",T="kmAnalyticsTopPagesDrivingLeads",D="kmAnalyticsTopRecentTrendingPages",R="kmAnalyticsTopTrafficSource",B="kmAnalyticsTopTrafficSourceDrivingAddToCart",M="kmAnalyticsTopTrafficSourceDrivingLeads",I="kmAnalyticsTopTrafficSourceDrivingPurchases",L="kmAnalyticsPagesPerVisit",F="kmAnalyticsVisitLength",P="kmAnalyticsTopReturningVisitorPages",z="kmSearchConsolePopularKeywords",H="kmAnalyticsVisitsPerVisitor",W="kmAnalyticsMostEngagingPages",G="kmAnalyticsTopCategories",V=[h,v,b,E,_,O,y,k,G,j,S,w,x,C,N,A,D,R,B,L,F,P,H,W,G],U=[].concat(V,[z])},70:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},700:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Footer}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(1),u=n.n(s),d=n(2),f=n(0),g=n(10),m=n(3),p=n(19),h=n(23),v=n(171),b=n(18),E=n(9),_=n(20),O=n(701),y=n(54);function Footer(t){var n=t.closeDialog,r=t.openResetDialog,a=Object(b.a)(),c=Object(f.useState)(null),s=l()(c,2),u=s[0],k=s[1],j=Object(f.useState)(!1),S=l()(j,2),w=S[0],x=S[1],C=Object(m.useSelect)((function(e){return e(p.a).canSubmitSharingChanges()})),N=Object(m.useSelect)((function(e){return e(p.a).isDoingSubmitSharingChanges()})),A=Object(m.useSelect)((function(e){return e(p.a).haveSharingSettingsExpanded("management")})),T=Object(m.useSelect)((function(e){return e(p.a).haveSharingSettingsExpanded("sharedRoles")})),D=Object(m.useSelect)((function(e){return e(p.a).haveSharingSettingsUpdated()})),R=Object(m.useSelect)((function(e){return!!e(h.b).getValue(v.c)})),B=Object(m.useSelect)((function(e){return!!e(h.b).getValue(v.b)})),M=Object(m.useDispatch)(p.a),I=M.resetSharingSettings,L=M.saveSharingSettings,F=Object(m.useDispatch)(h.b).setValue,P=Object(f.useCallback)(o()(i.a.mark((function e(){var t,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return k(null),e.next=3,L();case 3:if(t=e.sent,!(r=t.error)){e.next=8;break}return k(r.message),e.abrupt("return");case 8:Object(E.I)("".concat(a,"_sharing"),"settings_confirm"),F(v.a,void 0),n();case 11:case"end":return e.stop()}}),e)}))),[a,L,F,n]),z=Object(f.useCallback)(o()(i.a.mark((function e(){var t,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return k(null),x(!0),e.next=4,I();case 4:if(t=e.sent,!(r=t.error)){e.next=9;break}return k(r.message),e.abrupt("return");case 9:x(!1),n();case 11:case"end":return e.stop()}}),e)}))),[n,I]),H=Object(f.useCallback)((function(){Object(E.I)("".concat(a,"_sharing"),"settings_cancel"),n()}),[n,a]),W=u||A||T;return e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__footer"},W&&e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__footer-notice"},u&&e.createElement(y.a,{message:u}),!u&&e.createElement(O.a,null)),e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__footer-actions"},D&&R&&!W&&e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__footer-actions-left"},e.createElement(_.a,{onClick:r,danger:!0},Object(d.__)("Reset sharing permissions","google-site-kit"))),e.createElement("div",{className:"googlesitekit-dashboard-sharing-settings__footer-actions-right"},e.createElement(g.Button,{tertiary:!0,onClick:H},Object(d.__)("Cancel","google-site-kit")),R&&e.createElement(g.SpinnerButton,{onClick:P,disabled:N||!C,isSaving:N},Object(d.__)("Apply","google-site-kit")),B&&e.createElement(g.SpinnerButton,{onClick:z,disabled:w,isSaving:w,danger:!0},Object(d.__)("Reset","google-site-kit")))))}Footer.propTypes={closeDialog:u.a.func.isRequired}}).call(this,n(4))},701:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notice}));var r=n(2),i=n(38),a=n(3),o=n(19);function Notice(){var t=Object(a.useSelect)((function(e){return e(o.a).canSubmitSharingChanges()})),n=Object(a.useSelect)((function(e){return e(o.a).haveSharingSettingsExpanded("management")})),c=Object(a.useSelect)((function(e){return e(o.a).haveSharingSettingsExpanded("sharedRoles")}));return e.createElement("p",{className:"googlesitekit-dashboard-sharing-settings__notice"},n&&t&&Object(i.a)(Object(r.__)("By clicking <strong>Apply</strong>, you will give other authenticated admins of your site permission to manage view-only access to Site Kit Dashboard data from the chosen Google service","google-site-kit"),{span:e.createElement("span",null),strong:e.createElement("strong",null)}),!n&&t&&c&&Object(i.a)(Object(r.__)("By clicking <strong>Apply</strong>, you’re granting the selected roles view-only access to data from the Google services you’ve connected via your account","google-site-kit"),{span:e.createElement("span",null),strong:e.createElement("strong",null)}))}}).call(this,n(4))},702:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetAreaRenderer}));var r=n(15),i=n.n(r),a=n(6),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),f=n(3),g=n(246),m=n(93),p=n(47),h=n(23),v=n(17),b=n(24),E=n(160),_=n(703),O=n(704),y=n(705),k=n(34),j=n(7),S=n(249),w=n(706),x=n(79);function C(e){var t,n=(t={},o()(t,b.d,48),o()(t,b.a,48),o()(t,b.c,32),o()(t,b.b,32),t)[e],r=Math.abs(Object(m.b)(e)+n);return"".concat(-r,"px ").concat(-n,"px ").concat(-n,"px ").concat(-n,"px")}function WidgetAreaRenderer(t){var n,r=t.slug,a=t.contextID,c=Object(k.a)(),l=Object(f.useSelect)((function(e){return c?e(j.a).getViewableModules():null})),s=Object(x.a)(),m=Object(b.e)(),N=Object(d.useRef)(),A=Object(S.a)(N,{rootMargin:C(m),threshold:0}),T=Object(f.useSelect)((function(e){return e(p.a).getWidgetArea(r)})),D=T.Icon,R=T.title,B=T.style,M=T.subtitle,I=T.CTA,L=T.Footer,F=Object(f.useSelect)((function(e){return e(p.a).getWidgets(r,{modules:l||void 0})})),P=Object(f.useSelect)((function(e){return e(p.a).getWidgetStates()})),z=Object(f.useSelect)((function(e){return e(p.a).isWidgetAreaActive(r,{modules:l||void 0})})),H=Object(f.useSelect)((function(e){return e(h.b).getValue(h.a)})),W=Object(d.useState)({key:"WidgetAreaRenderer-".concat(r),value:H?H===a:!!(null==A?void 0:A.intersectionRatio)}),G=i()(W,2),V=G[0],U=G[1];Object(d.useEffect)((function(){U({key:"WidgetAreaRenderer-".concat(r),value:H?H===a:!!(null==A?void 0:A.intersectionRatio)})}),[A,r,H,a]);var q=I&&s<=782;if(void 0===l)return null;var K=Object(g.d)(F,P),X=K.columnWidths,Y=K.rowIndexes,$=Object(g.b)(F,P,{columnWidths:X,rowIndexes:Y}),Z=$.gridColumnWidths,J=$.overrideComponents,Q=F.map((function(t,n){return e.createElement(O.a,{key:"".concat(t.slug,"-wrapper"),gridColumnWidth:Z[n]},e.createElement(y.a,{slug:t.slug},e.createElement(_.a,{OverrideComponent:J[n]?function(){var t=J[n],r=t.Component,i=t.metadata;return e.createElement(r,i)}:void 0,slug:t.slug})))}));return e.createElement(E.a,{value:V},!!z&&e.createElement(v.e,{className:u()("googlesitekit-widget-area","googlesitekit-widget-area--".concat(r),"googlesitekit-widget-area--".concat(B)),ref:N},e.createElement(v.k,null,e.createElement(v.a,{className:"googlesitekit-widget-area-header",size:12},e.createElement(w.a,{slug:r,Icon:D,title:R,subtitle:M,CTA:I}))),e.createElement("div",{className:"googlesitekit-widget-area-widgets"},e.createElement(v.k,null,B===p.b.BOXES&&Q,B===p.b.COMPOSITE&&e.createElement(v.a,{size:12},e.createElement(v.e,null,e.createElement(v.k,null,Q))))),e.createElement(v.k,null,q&&e.createElement(v.a,{className:"googlesitekit-widget-area-footer",lgSize:12,mdSize:4,smSize:2},e.createElement("div",{className:"googlesitekit-widget-area-footer__cta"},e.createElement(I,null))),L&&e.createElement(v.a,{className:"googlesitekit-widget-area-footer",lgSize:12,mdSize:q?4:8,smSize:q?2:4},e.createElement(L,null)))),!z&&e.createElement(v.e,{className:u()(g.a,"googlesitekit-widget-area",(n={},o()(n,"googlesitekit-widget-area--".concat(r),!!r),o()(n,"googlesitekit-widget-area--".concat(B),!!B),n)),ref:N},Q))}WidgetAreaRenderer.propTypes={slug:l.a.string.isRequired,contextID:l.a.string}}).call(this,n(4))},703:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(14),o=n(0),c=n(3),l=n(47),s=n(19),u=n(267),d=n(268),f=n(246),g=n(118),m=n(34),p=n(24);function WidgetRenderer(t){var n,r=t.slug,i=t.OverrideComponent,h=Object(c.useSelect)((function(e){return e(l.a).getWidget(r)})),v=Object(p.e)(),b=Object(f.c)(r),E=b.Widget,_=b.WidgetNull,O=Object(c.useSelect)((function(e){return e(s.a).getRecoverableModules()})),y=Object(m.a)(),k=Object(o.useMemo)((function(){return h&&O&&Object(a.intersection)(h.modules,Object.keys(O))}),[O,h]),j=Object(c.useSelect)((function(e){return e(l.a).isWidgetPreloaded(r)}));if(!h||void 0===k||(null==h||null===(n=h.hideOnBreakpoints)||void 0===n?void 0:n.includes(v)))return e.createElement(_,null);var S=h.Component,w=h.wrapWidget,x=e.createElement(S,b);return y&&(null==k?void 0:k.length)&&(x=e.createElement(d.a,{widgetSlug:r,moduleSlugs:k})),i?x=e.createElement(o.Fragment,null,e.createElement(u.a,{widgetSlug:"overridden"},e.createElement(i,null)),e.createElement("div",{className:g.a},x)):w&&(x=e.createElement(E,null,x)),j?e.createElement("div",{className:g.a},x):x}WidgetRenderer.propTypes={slug:i.a.string.isRequired,OverrideComponent:i.a.elementType},t.a=WidgetRenderer}).call(this,n(4))},704:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(118),o=n(17);function WidgetCellWrapper(t){var n=t.gridColumnWidth,r=t.children;return 0===n?e.createElement("div",{className:a.a},r):n<6?e.createElement(o.a,{lgSize:n,mdSize:4,smSize:2},r):n<8?e.createElement(o.a,{lgSize:n,mdSize:8},r):e.createElement(o.a,{size:n},r)}WidgetCellWrapper.propTypes={gridColumnWidth:i.a.number.isRequired,children:i.a.element.isRequired},t.a=WidgetCellWrapper}).call(this,n(4))},705:function(e,t,n){"use strict";(function(e,r){var i=n(51),a=n.n(i),o=n(53),c=n.n(o),l=n(237),s=n.n(l),u=n(68),d=n.n(u),f=n(69),g=n.n(f),m=n(49),p=n.n(m),h=n(195),v=n.n(h),b=n(1),E=n.n(b),_=n(0),O=n(2),y=n(95),k=n(172),j=n(61),S=n(9);function w(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=p()(e);if(t){var i=p()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return g()(this,n)}}var x=function(t){d()(WidgetErrorHandler,t);var n=w(WidgetErrorHandler);function WidgetErrorHandler(e){var t;return a()(this,WidgetErrorHandler),(t=n.call(this,e)).state={error:null,info:null},t.onErrorClick=t.onErrorClick.bind(s()(t)),t}return c()(WidgetErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Widget error:",t,n),this.setState({error:t,info:n}),Object(S.I)("widget_error","handle_".concat(this.context||"unknown","_error"),"".concat(this.props.slug,"_").concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500-this.props.slug.length-1))}},{key:"onErrorClick",value:function(){var e=this.state,t=e.error,n=e.info;v()("`".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack,"`"))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,i=t.info;return n?r.createElement("div",{className:"googlesitekit-widget-error-handler"},r.createElement(y.a,{description:r.createElement(_.Fragment,null,r.createElement("p",null,Object(O.__)("An error prevented this Widget from being displayed properly. Report the exact contents of the error on the support forum to find out what caused it.","google-site-kit")),r.createElement(k.a,{message:n.message,componentStack:i.componentStack})),error:!0,onErrorClick:this.onErrorClick,onClick:this.onErrorClick,title:Object(O.__)("Error in Widget","google-site-kit")})):e}}]),WidgetErrorHandler}(_.Component);x.contextType=j.b,x.propTypes={children:E.a.node.isRequired},t.a=x}).call(this,n(28),n(4))},706:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetAreaHeader}));var r=n(1),i=n.n(r),a=n(0),o=n(606),c=n(79);function WidgetAreaHeader(t){var n=t.slug,r=t.Icon,i=void 0!==r&&r,l=t.title,s=void 0===l?"":l,u=t.subtitle,d=void 0===u?"":u,f=t.CTA,g=Object(c.a)(),m=f&&g>=783,p="function"==typeof d?d:void 0;return e.createElement(a.Fragment,null,i&&e.createElement(i,{width:33,height:33}),s&&e.createElement("h3",{className:"googlesitekit-widget-area-header__title googlesitekit-heading-3"},s,e.createElement(o.a,{slug:n})),(d||f)&&e.createElement("div",{className:"googlesitekit-widget-area-header__details"},d&&e.createElement("h4",{className:"googlesitekit-widget-area-header__subtitle"},p&&e.createElement(p,null),!p&&d,!s&&e.createElement(o.a,{slug:n})),m&&e.createElement("div",{className:"googlesitekit-widget-area-header__cta"},e.createElement(f,null))))}WidgetAreaHeader.propTypes={slug:i.a.string.isRequired,Icon:i.a.bool,title:i.a.oneOfType([i.a.string,i.a.element]),subtitle:i.a.oneOfType([i.a.string,i.a.elementType]),CTA:i.a.elementType}}).call(this,n(4))},707:function(e,t,n){"use strict";(function(e){var r=n(15),i=n.n(r),a=n(81),o=n(535),c=n(208),l=n(0),s=n(2),u=n(3),d=n(10),f=n(105),g=n(708),m=n(709),p=n(710),h=n(13),v=n(32),b=n(9),E=n(52),_=n(18);t.a=function EntitySearchInput(){var t=Object(c.a)(EntitySearchInput,"EntitySearchInput"),n=Object(l.useState)(!1),r=i()(n,2),O=r[0],y=r[1],k=Object(l.useState)(!1),j=i()(k,2),S=j[0],w=j[1],x=Object(l.useState)(!1),C=i()(x,2),N=C[0],A=C[1],T=Object(_.a)(),D=Object(E.c)(),R=Object(l.useRef)(),B=Object(l.useCallback)((function(){Object(b.I)("".concat(T,"_headerbar"),"open_urlsearch"),y(!0)}),[T]),M=Object(l.useCallback)((function(){Object(b.I)("".concat(T,"_headerbar"),"close_urlsearch"),y(!1)}),[T]),I=Object(l.useState)({}),L=i()(I,2),F=L[0],P=L[1],z=Object(u.useSelect)((function(e){return(null==F?void 0:F.url)?e(h.c).getAdminURL("googlesitekit-dashboard",{permaLink:F.url}):null})),H=Object(u.useDispatch)(v.a).navigateTo;return Object(l.useEffect)((function(){z&&Object(b.I)("".concat(T,"_headerbar_urlsearch"),"open_urldetails").finally((function(){H(z)}))}),[z,H,T]),Object(a.a)((function(){D===E.a&&y(!0)})),Object(o.a)((function(){var e;O||(null==R||null===(e=R.current)||void 0===e||e.focus())}),[O]),O?e.createElement("div",{className:"googlesitekit-entity-search googlesitekit-entity-search--is-open"},e.createElement(f.a,null,e.createElement("label",{htmlFor:t},Object(s.__)("Page/URL Search","google-site-kit"))),e.createElement(p.a,{id:t,match:F,setIsActive:A,setMatch:P,placeholder:Object(s.__)("Enter title or URL…","google-site-kit"),isLoading:S,setIsLoading:w,showDropdown:N,onClose:M,autoFocus:!0}),S&&N&&e.createElement(d.ProgressBar,{className:"googlesitekit-entity-search__loading",compress:!0}),e.createElement("div",{className:"googlesitekit-entity-search__actions"},e.createElement(d.Button,{onClick:M,trailingIcon:e.createElement(m.a,{width:"30",height:"20"}),className:"googlesitekit-entity-search__close",title:Object(s.__)("Close","google-site-kit"),text:!0,tooltip:!0,tooltipEnterDelayInMS:500}))):e.createElement("div",{className:"googlesitekit-entity-search"},e.createElement(d.Button,{className:"googlesitekit-border-radius-round--phone googlesitekit-button-icon--phone",onClick:B,text:!0,ref:R,title:Object(s.__)("Search","google-site-kit"),trailingIcon:e.createElement(g.a,{width:"20",height:"20"}),tooltip:!0,tooltipEnterDelayInMS:500},Object(s.__)("URL Search","google-site-kit")))}}).call(this,n(4))},708:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 9.5c0 1.407-.45 2.714-1.218 3.783L20.49 19 19 20.49l-5.717-5.708A6.463 6.463 0 019.5 16 6.5 6.5 0 1116 9.5zm-11 0C5 11.99 7.01 14 9.5 14S14 11.99 14 9.5 11.99 5 9.5 5 5 7.01 5 9.5z",fill:"currentColor"});t.a=function SvgMagnifyingGlass(e){return r.createElement("svg",i({viewBox:"0 0 20 20",fill:"none"},e),a)}},709:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M15.833 5.342l-1.175-1.175L10 8.825 5.342 4.167 4.167 5.342 8.825 10l-4.658 4.658 1.175 1.175L10 11.175l4.658 4.658 1.175-1.175L11.175 10l4.658-4.658z",fill:"currentColor"});t.a=function SvgCloseDark(e){return r.createElement("svg",i({viewBox:"0 0 20 20",fill:"none"},e),a)}},71:function(e,t,n){"use strict";n.r(t),n.d(t,"CONTEXT_MAIN_DASHBOARD_KEY_METRICS",(function(){return r})),n.d(t,"CONTEXT_MAIN_DASHBOARD_TRAFFIC",(function(){return i})),n.d(t,"CONTEXT_MAIN_DASHBOARD_CONTENT",(function(){return a})),n.d(t,"CONTEXT_MAIN_DASHBOARD_SPEED",(function(){return o})),n.d(t,"CONTEXT_MAIN_DASHBOARD_MONETIZATION",(function(){return c})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_TRAFFIC",(function(){return l})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_CONTENT",(function(){return s})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_SPEED",(function(){return u})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_MONETIZATION",(function(){return d}));var r="mainDashboardKeyMetrics",i="mainDashboardTraffic",a="mainDashboardContent",o="mainDashboardSpeed",c="mainDashboardMonetization",l="entityDashboardTraffic",s="entityDashboardContent",u="entityDashboardSpeed",d="entityDashboardMonetization";t.default={CONTEXT_MAIN_DASHBOARD_KEY_METRICS:r,CONTEXT_MAIN_DASHBOARD_TRAFFIC:i,CONTEXT_MAIN_DASHBOARD_CONTENT:a,CONTEXT_MAIN_DASHBOARD_SPEED:o,CONTEXT_MAIN_DASHBOARD_MONETIZATION:c,CONTEXT_ENTITY_DASHBOARD_TRAFFIC:l,CONTEXT_ENTITY_DASHBOARD_CONTENT:s,CONTEXT_ENTITY_DASHBOARD_SPEED:u,CONTEXT_ENTITY_DASHBOARD_MONETIZATION:d}},710:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PostSearcherAutoSuggest}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(1),u=n.n(s),d=n(358),f=n(0),g=n(2),m=n(56),p=n(45),h=n.n(p),v=n(3),b=n(711),E=n(13),_=function(){};function PostSearcherAutoSuggest(t){var n=t.id,r=t.match,a=t.setMatch,c=t.isLoading,s=t.showDropdown,u=void 0===s||s,p=t.setIsLoading,O=void 0===p?_:p,y=t.setIsActive,k=void 0===y?_:y,j=t.autoFocus,S=t.setCanSubmit,w=void 0===S?_:S,x=t.onClose,C=void 0===x?_:x,N=t.placeholder,A=void 0===N?"":N,T=Object(f.useRef)(),D=Object(f.useState)(""),R=l()(D,2),B=R[0],M=R[1],I=null==r?void 0:r.title,L=Object(b.a)(B,B===I?0:200),F=Object(f.useState)([]),P=l()(F,2),z=P[0],H=P[1],W=Object(g.__)("No results found","google-site-kit"),G=Object(v.useSelect)((function(e){return e(E.c).getCurrentEntityTitle()})),V=Object(f.useRef)(null),U=Object(f.useCallback)((function(){k(!0)}),[k]),q=Object(f.useCallback)((function(e){var t,n,r;(null===(t=e.relatedTarget)||void 0===t?void 0:t.classList.contains("autocomplete__option--result"))||(k(!1),M(null!==(n=null!==(r=V.current)&&void 0!==r?r:G)&&void 0!==n?n:""))}),[G,k]),K=Object(f.useCallback)((function(e){if(Array.isArray(z)&&e!==W){var t=z.find((function(t){return t.title.toLowerCase()===e.toLowerCase()}));t?(V.current=t.title,w(!0),a(t),M(t.title)):V.current=null}else V.current=null,w(!1)}),[z,w,a,W,M]),X=Object(f.useCallback)((function(e){w(!1),M(e.target.value)}),[w]);Object(f.useEffect)((function(){if(""!==L&&L!==G&&(null==L?void 0:L.toLowerCase())!==(null==I?void 0:I.toLowerCase())){var e="undefined"==typeof AbortController?void 0:new AbortController;return(t=o()(i.a.mark((function t(){var n,r;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return O(!0),n=h.a.get("core","search","entity-search",{query:encodeURIComponent(L)},{useCache:!1,signal:null==e?void 0:e.signal}),T.current=n,t.prev=3,t.next=6,n;case 6:r=t.sent,H(r),t.next=13;break;case 10:t.prev=10,t.t0=t.catch(3),H(null);case 13:return t.prev=13,n===T.current&&O(!1),t.finish(13);case 16:case"end":return t.stop()}}),t,null,[[3,10,13,16]])}))),function(){return t.apply(this,arguments)})(),function(){return null==e?void 0:e.abort()}}var t}),[L,O,G,I]),Object(f.useEffect)((function(){B||H([])}),[B]),Object(f.useEffect)((function(){G&&M(G)}),[G]);var Y=Object(f.useRef)(),$=Object(f.useCallback)((function(e){var t=Y.current;switch(e.keyCode){case m.d:(null==t?void 0:t.value)&&(e.preventDefault(),t.selectionStart=0,t.selectionEnd=0);break;case m.a:(null==t?void 0:t.value)&&(e.preventDefault(),t.selectionStart=t.value.length,t.selectionEnd=t.value.length)}switch(e.keyCode){case m.c:return C();case m.b:return K(B)}}),[C,K,B]);return e.createElement(d.a,{className:"autocomplete__wrapper",onSelect:K},e.createElement(d.b,{ref:Y,id:n,className:"autocomplete__input autocomplete__input--default",type:"text",onBlur:q,onChange:X,onFocus:U,placeholder:A,onKeyDown:$,value:B,autoFocus:j}),!c&&u&&L!==G&&""!==L&&0===(null==z?void 0:z.length)&&e.createElement(d.e,{portal:!1},e.createElement(d.c,{className:"autocomplete__menu autocomplete__menu--inline"},e.createElement(d.d,{value:W,className:"autocomplete__option autocomplete__option--no-results"}))),u&&""!==L&&L!==G&&(null==z?void 0:z.length)>0&&e.createElement(d.e,{portal:!1},e.createElement(d.c,{className:"autocomplete__menu autocomplete__menu--inline"},z.map((function(t){var n=t.id,r=t.title;return e.createElement(d.d,{key:n,value:r,className:"autocomplete__option autocomplete__option--result"})})))))}PostSearcherAutoSuggest.propTypes={id:u.a.string,match:u.a.object,setCanSubmit:u.a.func,setMatch:u.a.func,isLoading:u.a.bool,setIsLoading:u.a.func,onKeyDown:u.a.func,autoFocus:u.a.bool,placeholder:u.a.string}}).call(this,n(4))},711:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(15),i=n.n(r),a=n(0);function o(e,t){var n=Object(a.useState)(e),r=i()(n,2),o=r[0],c=r[1];return Object(a.useEffect)((function(){var n=setTimeout((function(){c(e)}),t);return function(){clearTimeout(n)}}),[e,t]),o}},712:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DateRangeSelector}));var r=n(15),i=n.n(r),a=n(209),o=n(11),c=n.n(o),l=n(0),s=n(56),u=n(2),d=n(3),f=n(10),g=n(713),m=n(7),p=n(112),h=n(9),v=n(23),b=n(18);function DateRangeSelector(){var t,n=Object(h.n)(),r=Object(d.useSelect)((function(e){return e(m.a).getDateRange()})),o=Object(d.useDispatch)(m.a).setDateRange,E=Object(d.useDispatch)(v.b).resetInViewHook,_=Object(l.useState)(!1),O=i()(_,2),y=O[0],k=O[1],j=Object(l.useRef)(),S=Object(b.a)();Object(a.a)(j,(function(){return k(!1)})),Object(p.a)([s.c,s.f],j,(function(){return k(!1)}));var w=Object(l.useCallback)((function(){k(!y)}),[y]),x=Object(l.useCallback)((function(e){var t=Object.values(n)[e].slug;r!==t&&Object(h.I)("".concat(S,"_headerbar"),"change_daterange",t),E(),o(t),k(!1)}),[n,r,E,o,S]),C=null===(t=n[r])||void 0===t?void 0:t.label,N=Object.values(n).map((function(e){return e.label}));return e.createElement("div",{ref:j,className:"googlesitekit-date-range-selector googlesitekit-dropdown-menu mdc-menu-surface--anchor"},e.createElement(f.Button,{className:c()("mdc-button--dropdown","googlesitekit-header__dropdown","googlesitekit-header__date-range-selector-menu","googlesitekit-border-radius-round--phone","googlesitekit-button-icon--phone"),text:!0,onClick:w,icon:e.createElement(g.a,{width:"20",height:"20"}),"aria-haspopup":"menu","aria-expanded":y,"aria-controls":"date-range-selector-menu",title:Object(u.__)("Date range","google-site-kit"),tooltip:!0,tooltipEnterDelayInMS:500},C),e.createElement(f.Menu,{menuOpen:y,menuItems:N,onSelected:x,id:"date-range-selector-menu",className:"googlesitekit-width-auto"}))}}).call(this,n(4))},713:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M6 9H4v2h2V9zm4 0H8v2h2V9zm4 0h-2v2h2V9zm2-7h-1V0h-2v2H5V0H3v2H2C.89 2 .01 2.9.01 4L0 18a2 2 0 002 2h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 16H2V7h14v11z",fill:"currentColor",fillRule:"evenodd"});t.a=function SvgDateRange(e){return r.createElement("svg",i({viewBox:"0 0 18 20"},e),a)}},714:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return c}));var r=n(15),i=n.n(r),a=n(813),o=n(0),c=function(){var t=Object(a.a)().y,n=Object(o.useState)(!1),r=i()(n,2),c=r[0],l=r[1],s="googlesitekit-plugin--has-scrolled";return Object(o.useEffect)((function(){c?e.document.body.classList.add(s):e.document.body.classList.remove(s)}),[c]),t>0&&!c?l(!0):0===t&&c&&l(!1),c}}).call(this,n(28))},715:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OverlayNotificationsRenderer}));var r=n(0),i=n(367),a=n(371),o=n(716),c=n(719),l=n(513);function OverlayNotificationsRenderer(){var t=Object(i.a)("rrmModule");return e.createElement(r.Fragment,null,e.createElement(c.a,null),e.createElement(o.a,null),e.createElement(a.a,null),t&&e.createElement(r.Fragment,null,e.createElement(l.a,null),e.createElement(l.b,null)))}}).call(this,n(4))},716:function(e,t,n){"use strict";(function(e,r){var i=n(377),a=n(0),o=n(2),c=n(10),l=n(3),s=n(717),u=n(718),d=n(22),f=n(23),g=n(7),m=n(19),p=n(24),h=n(52),v=n(31),b=n(8),E=n(93),_=n(204),O=n(393),y=n(9),k=n(18),j=n(50);t.a=Object(i.a)(Object(j.a)({moduleName:"analytics-4"}),Object(j.a)({moduleName:"adsense"}))((function AnalyticsAndAdSenseAccountsDetectedAsLinkedOverlayNotification(){var t=Object(p.e)(),n=Object(h.c)()===h.b,i=Object(k.a)(),j=Object(l.useSelect)((function(e){return e(g.a).isItemDismissed("AnalyticsAndAdSenseLinkedOverlayNotification")})),S=Object(l.useSelect)((function(e){return e(g.a).isDismissingItem("AnalyticsAndAdSenseLinkedOverlayNotification")})),w=Object(l.useSelect)((function(e){return!n||j?null:e(m.a).isModuleConnected("analytics-4")})),x=Object(l.useSelect)((function(e){return!n||j?null:e(m.a).isModuleConnected("adsense")})),C=Object(l.useSelect)((function(e){return!n||j?null:e(g.a).hasAccessToShareableModule("analytics-4")})),N=Object(l.useSelect)((function(e){return!n||j?null:e(g.a).hasAccessToShareableModule("adsense")})),A=Object(l.useSelect)((function(e){return!n||j?null:e(b.r).getAdSenseLinked()})),T=Object(l.useSelect)((function(e){return x?e(v.l).getAccountID():null})),D=Object(l.useSelect)((function(e){return e(g.a).getDateRangeDates({offsetDays:b.g})})),R={startDate:D.startDate,endDate:D.endDate,dimensions:["pagePath","adSourceName"],metrics:[{name:"totalAdRevenue"}],dimensionFilters:{adSourceName:"Google AdSense account (".concat(T,")")},orderby:[{metric:{metricName:"totalAdRevenue"},desc:!0}],limit:1},B=Object(l.useSelect)((function(e){return n&&!1===j&&A&&x&&w&&N&&C?e(b.r).getReport(R):null})),M=!1===Object(O.a)(B),I=n&&!1===j&&w&&x&&C&&N&&A&&M,L=Object(l.useDispatch)(f.b).dismissOverlayNotification,F=Object(a.useCallback)((function(){Object(y.I)("".concat(i,"_top-earning-pages-widget"),"view_overlay_CTA")}),[i]),P=function(){L("AnalyticsAndAdSenseLinkedOverlayNotification")};return r.createElement(_.a,{shouldShowNotification:I,GraphicDesktop:s.a,GraphicMobile:u.a,notificationID:"AnalyticsAndAdSenseLinkedOverlayNotification",onShow:F},r.createElement("div",{className:"googlesitekit-overlay-notification__body"},r.createElement("h3",null,Object(o.__)("See your top earning content","google-site-kit")),r.createElement("p",null,Object(o.__)("Data is now available for the pages that earn the most AdSense revenue.","google-site-kit"))),r.createElement("div",{className:"googlesitekit-overlay-notification__actions"},r.createElement(c.Button,{tertiary:!0,disabled:S,onClick:function(){P(),Object(y.I)("".concat(i,"_top-earning-pages-widget"),"dismiss_overlay_CTA")}},Object(o.__)("Maybe later","google-site-kit")),r.createElement(c.Button,{disabled:S,onClick:function(n){!function(n){n.preventDefault(),P(),setTimeout((function(){e.history.replaceState({},"","#".concat(d.c)),e.scrollTo({top:Object(E.a)(".googlesitekit-widget--adsenseTopEarningPagesGA4",t),behavior:"smooth"})}),50)}(n),Object(y.I)("".concat(i,"_top-earning-pages-widget"),"confirm_overlay_CTA")}},Object(o.__)("Show me","google-site-kit"))))}))}).call(this,n(28),n(4))},717:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#analytics-adsense-linked-desktop_svg__clip0_100_3563)"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h264c8.837 0 16 7.163 16 16v147H0V16z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#analytics-adsense-linked-desktop_svg__filter0_d_100_3563)"},r.createElement("rect",{x:24,y:23,width:248,height:174,rx:11,fill:"#fff"}),r.createElement("rect",{x:48,y:96,width:116,height:14,rx:7,fill:"#9CEBEB"}),r.createElement("rect",{x:48,y:124,width:141,height:14,rx:7,fill:"#9CEBEB"}),r.createElement("rect",{x:48,y:153,width:97,height:14,rx:7,fill:"#9CEBEB"}),r.createElement("path",{d:"M222 103a7 7 0 017-7h12a7 7 0 110 14h-12a7 7 0 01-7-7zM222 131a7 7 0 017-7h12a7 7 0 110 14h-12a7 7 0 01-7-7zM222 159a7 7 0 017-7h12a7 7 0 110 14h-12a7 7 0 01-7-7z",fill:"#EBEEF0"}),r.createElement("rect",{x:48,y:44,width:36,height:9,rx:4.5,fill:"#EBEEF0"}),r.createElement("rect",{x:46.672,y:162.656,width:55.734,height:9.516,rx:4.758,fill:"#EBEEF0"}),r.createElement("path",{d:"M272 72H24",stroke:"#EBEEF0",strokeWidth:2}))),o=r.createElement("defs",null,r.createElement("clipPath",{id:"analytics-adsense-linked-desktop_svg__clip0_100_3563"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h264c8.837 0 16 7.163 16 16v147H0V16z",fill:"#fff"})),r.createElement("filter",{id:"analytics-adsense-linked-desktop_svg__filter0_d_100_3563",x:8,y:11,width:280,height:206,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_100_3563"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_100_3563",result:"shape"})));t.a=function SvgAnalyticsAdsenseLinkedDesktop(e){return r.createElement("svg",i({viewBox:"0 0 296 163",fill:"none"},e),a,o)}},718:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#analytics-adsense-linked-mobile_svg__clip0_413_2990)"},r.createElement("path",{d:"M311.553 64.365c2.4 22.73-4.803 32.78-23.025 59.95-18.222 27.169 7.404 59.276-20.779 89.868-33.528 36.394-150.686 39.364-201.232 24.212-50.546-15.153-63.58-46.473-59.948-75.155C11.5 124.315 38.52 112.077 51.5 93 70.776 64.675 48.687 38.214 86 15.5s80.086 6.697 120.326 4.388c23.216-1.332 46.017-5.627 66.626.968 20.832 6.667 36.719 25.428 38.601 43.509z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#analytics-adsense-linked-mobile_svg__filter0_d_413_2990)"},r.createElement("rect",{x:74.81,y:12.732,width:193.381,height:135.679,rx:8.577,fill:"#fff"}),r.createElement("rect",{x:93.524,y:69.655,width:90.452,height:10.917,rx:5.458,fill:"#9CEBEB"}),r.createElement("rect",{x:93.524,y:91.488,width:109.946,height:10.917,rx:5.458,fill:"#9CEBEB"}),r.createElement("rect",{x:93.524,y:114.101,width:75.637,height:10.917,rx:5.458,fill:"#9CEBEB"}),r.createElement("path",{d:"M229.202 75.113a5.458 5.458 0 015.458-5.458h9.358a5.458 5.458 0 010 10.916h-9.358a5.458 5.458 0 01-5.458-5.458zM229.202 96.946a5.458 5.458 0 015.458-5.458h9.358a5.458 5.458 0 010 10.917h-9.358a5.459 5.459 0 01-5.458-5.459zM229.202 118.78a5.459 5.459 0 015.458-5.459h9.358a5.459 5.459 0 010 10.917h-9.358a5.458 5.458 0 01-5.458-5.458z",fill:"#EBEEF0"}),r.createElement("rect",{x:93.524,y:29.107,width:28.071,height:7.018,rx:3.509,fill:"#EBEEF0"}),r.createElement("rect",{x:92.488,y:121.631,width:43.459,height:7.42,rx:3.71,fill:"#EBEEF0"}),r.createElement("path",{d:"M268.19 50.94H74.81",stroke:"#EBEEF0",strokeWidth:1.56}))),o=r.createElement("defs",null,r.createElement("clipPath",{id:"analytics-adsense-linked-mobile_svg__clip0_413_2990"},r.createElement("path",{fill:"#fff",d:"M0 0h343v123H0z"})),r.createElement("filter",{id:"analytics-adsense-linked-mobile_svg__filter0_d_413_2990",x:62.333,y:3.375,width:218.333,height:160.631,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.119}),r.createElement("feGaussianBlur",{stdDeviation:6.238}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_413_2990"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_413_2990",result:"shape"})));t.a=function SvgAnalyticsAdsenseLinkedMobile(e){return r.createElement("svg",i({viewBox:"0 0 343 123",fill:"none"},e),a,o)}},719:function(e,t,n){"use strict";(function(e){var r=n(377),i=n(2),a=n(10),o=n(3),c=n(7),l=n(13),s=n(23),u=n(19),d=n(8),f=n(720),g=n(721),m=n(204),p=n(34),h=n(52),v=n(50);t.a=Object(r.a)(Object(v.a)({moduleName:"analytics-4"}),Object(v.a)({moduleName:"adsense"}))((function LinkAnalyticsAndAdSenseAccountsOverlayNotification(){var t=Object(p.a)(),n=Object(h.c)()===h.b,r=Object(o.useSelect)((function(e){return e(l.c).getGoogleSupportURL({path:"/adsense/answer/6084409"})})),v=Object(o.useSelect)((function(e){return e(c.a).isItemDismissed("LinkAnalyticsAndAdSenseAccountsOverlayNotification")})),b=Object(o.useSelect)((function(e){return e(c.a).isDismissingItem("LinkAnalyticsAndAdSenseAccountsOverlayNotification")})),E=Object(o.useSelect)((function(e){return t||!n||v?null:e(u.a).isModuleConnected("analytics-4")})),_=Object(o.useSelect)((function(e){return t||!n||v?null:e(u.a).isModuleConnected("adsense")})),O=Object(o.useSelect)((function(e){return t||!n||v?null:e(d.r).getAdSenseLinked()})),y=!t&&n&&(E&&_)&&!1===O&&!1===v,k=Object(o.useDispatch)(s.b).dismissOverlayNotification,j=function(){k("LinkAnalyticsAndAdSenseAccountsOverlayNotification")};return e.createElement(m.a,{shouldShowNotification:y,GraphicDesktop:f.a,GraphicMobile:g.a,notificationID:"LinkAnalyticsAndAdSenseAccountsOverlayNotification"},e.createElement("div",{className:"googlesitekit-overlay-notification__body"},e.createElement("h3",null,Object(i.__)("See which content earns you the most","google-site-kit")),e.createElement("p",null,Object(i.__)("Link your Analytics and AdSense accounts to find out which content brings you the most revenue.","google-site-kit"))),e.createElement("div",{className:"googlesitekit-overlay-notification__actions"},e.createElement(a.Button,{tertiary:!0,disabled:b,onClick:j},Object(i.__)("Maybe later","google-site-kit")),e.createElement(a.Button,{disabled:b,href:r,target:"_blank",onClick:j,"aria-label":Object(i.__)("Learn how (opens in a new tab)","google-site-kit")},Object(i.__)("Learn how","google-site-kit"))))}))}).call(this,n(4))},72:function(e,t,n){"use strict";var r=n(15),i=n.n(r),a=n(265),o=n(1),c=n.n(o),l=n(0),s=n(144);function Portal(e){var t=e.children,n=e.slug,r=Object(l.useState)(document.createElement("div")),o=i()(r,1)[0];return Object(a.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(s.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},720:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#analytics-adsense-connect-desktop_svg__clip0_50_9707)"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h264c8.837 0 16 7.163 16 16v147H0V16z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#analytics-adsense-connect-desktop_svg__filter0_d_50_9707)"},r.createElement("rect",{x:22,y:30,width:252,height:172.055,rx:13.764,fill:"#fff"}),r.createElement("rect",{x:36,y:65,width:224,height:47,rx:5.161,fill:"#DCE8FF"}),r.createElement("rect",{x:43,y:145,width:30,height:3,rx:1.5,fill:"#9BB8F0"}),r.createElement("rect",{x:43,y:151,width:40,height:3,rx:1.5,fill:"#9BB8F0"}),r.createElement("path",{d:"M168.539 90.857L188 112h-81l29.455-32 21.039 22.857 11.045-12z",fill:"#9BB8F0"}),r.createElement("path",{d:"M22 43.764C22 36.162 28.162 30 35.764 30h224.472C267.838 30 274 36.162 274 43.764v7.091H22v-7.091z",fill:"#EBEEF0"}),r.createElement("rect",{x:32.427,y:36.952,width:6.952,height:6.952,rx:3.476,fill:"#CBD0D3"}),r.createElement("rect",{x:42.855,y:36.952,width:6.952,height:6.952,rx:3.476,fill:"#CBD0D3"}),r.createElement("rect",{x:36,y:122,width:59,height:55,rx:5.457,fill:"#EBEEF0"}),r.createElement("rect",{x:107,y:122,width:54,height:10,rx:5,fill:"#EBEEF0"}),r.createElement("rect",{x:107,y:141,width:153,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("rect",{x:107,y:154,width:153,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("path",{d:"M238.51 171.625c11.868 0 21.49-3.528 21.49-7.88v-2.865h-42.98v2.865c0 4.352 9.621 7.88 21.49 7.88z",fill:"#E1B155"}),r.createElement("ellipse",{cx:238.51,cy:159.88,rx:21.49,ry:7.88,fill:"#FECE72"}),r.createElement("path",{d:"M237.859 162.363v-.48a4.538 4.538 0 01-.901-.154 2.897 2.897 0 01-.77-.342 1.423 1.423 0 01-.496-.534l.952-.209c.081.161.23.306.446.435.222.128.479.211.769.248v-1.223l-.03-.011c-.54-.111-.986-.254-1.337-.43-.351-.18-.526-.41-.526-.689 0-.165.074-.321.222-.468.156-.147.375-.269.659-.364a3.58 3.58 0 011.012-.176v-.474h.81v.485c.352.029.645.086.882.17.243.081.432.175.567.281.***************.293.304l-.931.22a.881.881 0 00-.284-.243 1.504 1.504 0 00-.527-.181v1.157c.365.081.689.171.973.27.283.095.506.211.668.347a.606.606 0 01.253.496.69.69 0 01-.273.551c-.176.151-.409.27-.699.358a4.225 4.225 0 01-.922.171v.485h-.81zm-.85-3.389c0 .***************.298.149.077.362.152.638.225v-.991a1.796 1.796 0 00-.617.17c-.156.085-.233.184-.233.298zm2.501 1.829c0-.125-.074-.226-.223-.303a2.833 2.833 0 00-.618-.22v1.041c.244-.033.443-.093.598-.181.162-.089.243-.201.243-.337z",fill:"#AA7A1E"}),r.createElement("path",{d:"M238.51 165.625c11.868 0 21.49-3.528 21.49-7.88v-2.865h-42.98v2.865c0 4.352 9.621 7.88 21.49 7.88z",fill:"#E1B155"}),r.createElement("ellipse",{cx:238.51,cy:153.88,rx:21.49,ry:7.88,fill:"#FECE72"}),r.createElement("path",{d:"M237.859 156.363v-.48a4.538 4.538 0 01-.901-.154 2.897 2.897 0 01-.77-.342 1.423 1.423 0 01-.496-.534l.952-.209c.081.161.23.306.446.435.222.128.479.211.769.248v-1.223l-.03-.011c-.54-.111-.986-.254-1.337-.43-.351-.18-.526-.41-.526-.689 0-.165.074-.321.222-.468.156-.147.375-.269.659-.364a3.58 3.58 0 011.012-.176v-.474h.81v.485c.352.029.645.086.882.17.243.081.432.175.567.281.***************.293.304l-.931.22a.881.881 0 00-.284-.243 1.504 1.504 0 00-.527-.181v1.157c.365.081.689.171.973.27.283.095.506.211.668.347a.606.606 0 01.253.496.69.69 0 01-.273.551c-.176.151-.409.27-.699.358a4.225 4.225 0 01-.922.171v.485h-.81zm-.85-3.389c0 .***************.298.149.077.362.152.638.225v-.991a1.796 1.796 0 00-.617.17c-.156.085-.233.184-.233.298zm2.501 1.829c0-.125-.074-.226-.223-.303a2.833 2.833 0 00-.618-.22v1.041c.244-.033.443-.093.598-.181.162-.089.243-.201.243-.337z",fill:"#AA7A1E"}),r.createElement("path",{d:"M238.51 159.625c11.868 0 21.49-3.528 21.49-7.88v-2.865h-42.98v2.865c0 4.352 9.621 7.88 21.49 7.88z",fill:"#E1B155"}),r.createElement("ellipse",{cx:238.51,cy:147.88,rx:21.49,ry:7.88,fill:"#FECE72"}),r.createElement("path",{d:"M237.859 150.363v-.48a4.538 4.538 0 01-.901-.154 2.897 2.897 0 01-.77-.342 1.423 1.423 0 01-.496-.534l.952-.209c.081.161.23.306.446.435.222.128.479.211.769.248v-1.223l-.03-.011c-.54-.111-.986-.254-1.337-.43-.351-.18-.526-.41-.526-.689 0-.165.074-.321.222-.468.156-.147.375-.269.659-.364a3.58 3.58 0 011.012-.176v-.474h.81v.485c.352.029.645.086.882.17.243.081.432.175.567.281.***************.293.304l-.931.22a.881.881 0 00-.284-.243 1.504 1.504 0 00-.527-.181v1.157c.365.081.689.171.973.27.283.095.506.211.668.347a.606.606 0 01.253.496.69.69 0 01-.273.551c-.176.151-.409.27-.699.358a4.225 4.225 0 01-.922.171v.485h-.81zm-.85-3.389c0 .***************.298.149.077.362.152.638.225v-.991a1.796 1.796 0 00-.617.17c-.156.085-.233.184-.233.298zm2.501 1.829c0-.125-.074-.226-.223-.303a2.833 2.833 0 00-.618-.22v1.041c.244-.033.443-.093.598-.181.162-.089.243-.201.243-.337z",fill:"#AA7A1E"}),r.createElement("path",{d:"M238.51 153.625c11.868 0 21.49-3.528 21.49-7.88v-2.865h-42.98v2.865c0 4.352 9.621 7.88 21.49 7.88z",fill:"#E1B155"}),r.createElement("ellipse",{cx:238.51,cy:141.88,rx:21.49,ry:7.88,fill:"#FECE72"}),r.createElement("path",{d:"M237.859 144.363v-.48a4.538 4.538 0 01-.901-.154 2.897 2.897 0 01-.77-.342 1.423 1.423 0 01-.496-.534l.952-.209c.081.161.23.306.446.435.222.128.479.211.769.248v-1.223l-.03-.011c-.54-.111-.986-.254-1.337-.43-.351-.18-.526-.41-.526-.689 0-.165.074-.321.222-.468.156-.147.375-.269.659-.364a3.58 3.58 0 011.012-.176v-.474h.81v.485c.352.029.645.086.882.17.243.081.432.175.567.281.***************.293.304l-.931.22a.881.881 0 00-.284-.243 1.504 1.504 0 00-.527-.181v1.157c.365.081.689.171.973.27.283.095.506.211.668.347a.606.606 0 01.253.496.69.69 0 01-.273.551c-.176.151-.409.27-.699.358a4.225 4.225 0 01-.922.171v.485h-.81zm-.85-3.389c0 .***************.298.149.077.362.152.638.225v-.991a1.796 1.796 0 00-.617.17c-.156.085-.233.184-.233.298zm2.501 1.829c0-.125-.074-.226-.223-.303a2.833 2.833 0 00-.618-.22v1.041c.244-.033.443-.093.598-.181.162-.089.243-.201.243-.337z",fill:"#AA7A1E"}),r.createElement("path",{d:"M238.51 147.625c11.868 0 21.49-3.528 21.49-7.88v-2.865h-42.98v2.865c0 4.352 9.621 7.88 21.49 7.88z",fill:"#E1B155"}),r.createElement("ellipse",{cx:238.51,cy:135.88,rx:21.49,ry:7.88,fill:"#FECE72"}),r.createElement("path",{d:"M237.859 138.363v-.48a4.538 4.538 0 01-.901-.154 2.897 2.897 0 01-.77-.342 1.423 1.423 0 01-.496-.534l.952-.209c.081.161.23.306.446.435.222.128.479.211.769.248v-1.223l-.03-.011c-.54-.111-.986-.254-1.337-.43-.351-.18-.526-.41-.526-.689 0-.165.074-.321.222-.468.156-.147.375-.269.659-.364a3.58 3.58 0 011.012-.176v-.474h.81v.485c.352.029.645.086.882.17.243.081.432.175.567.281.***************.293.304l-.931.22a.881.881 0 00-.284-.243 1.504 1.504 0 00-.527-.181v1.157c.365.081.689.171.973.27.283.095.506.211.668.347a.606.606 0 01.253.496.69.69 0 01-.273.551c-.176.151-.409.27-.699.358a4.225 4.225 0 01-.922.171v.485h-.81zm-.85-3.389c0 .***************.298.149.077.362.152.638.225v-.991a1.796 1.796 0 00-.617.17c-.156.085-.233.184-.233.298zm2.501 1.829c0-.125-.074-.226-.223-.303a2.833 2.833 0 00-.618-.22v1.041c.244-.033.443-.093.598-.181.162-.089.243-.201.243-.337z",fill:"#AA7A1E"}),r.createElement("path",{d:"M238.51 141.625c11.868 0 21.49-3.528 21.49-7.88v-2.865h-42.98v2.865c0 4.352 9.621 7.88 21.49 7.88z",fill:"#E1B155"}),r.createElement("ellipse",{cx:238.51,cy:129.88,rx:21.49,ry:7.88,fill:"#FECE72"}),r.createElement("path",{d:"M237.859 132.363v-.48a4.538 4.538 0 01-.901-.154 2.897 2.897 0 01-.77-.342 1.423 1.423 0 01-.496-.534l.952-.209c.081.161.23.306.446.435.222.128.479.211.769.248v-1.223l-.03-.011c-.54-.111-.986-.254-1.337-.43-.351-.18-.526-.41-.526-.689 0-.165.074-.321.222-.468.156-.147.375-.269.659-.364a3.58 3.58 0 011.012-.176v-.474h.81v.485c.352.029.645.086.882.17.243.081.432.175.567.281.***************.293.304l-.931.22a.881.881 0 00-.284-.243 1.504 1.504 0 00-.527-.181v1.157c.365.081.689.171.973.27.283.095.506.211.668.347a.606.606 0 01.253.496.69.69 0 01-.273.551c-.176.151-.409.27-.699.358a4.225 4.225 0 01-.922.171v.485h-.81zm-.85-3.389c0 .***************.298.149.077.362.152.638.225v-.991a1.796 1.796 0 00-.617.17c-.156.085-.233.184-.233.298zm2.501 1.829c0-.125-.074-.226-.223-.303a2.833 2.833 0 00-.618-.22v1.041c.244-.033.443-.093.598-.181.162-.089.243-.201.243-.337z",fill:"#AA7A1E"}),r.createElement("path",{d:"M256.323 129.88c0 .547-.319 1.188-1.166 1.881-.841.688-2.107 1.344-3.743 1.91-3.264 1.13-7.827 1.844-12.904 1.844-5.078 0-9.64-.714-12.904-1.844-1.636-.566-2.902-1.222-3.743-1.91-.847-.693-1.166-1.334-1.166-1.881 0-.548.319-1.189 1.166-1.882.841-.688 2.107-1.344 3.743-1.91 3.264-1.13 7.826-1.844 12.904-1.844 5.077 0 9.64.714 12.904 1.844 1.636.566 2.902 1.222 3.743 1.91.847.693 1.166 1.334 1.166 1.882z",stroke:"#E1B155",strokeWidth:1.624}),r.createElement("ellipse",{cx:209.51,cy:166.88,rx:21.49,ry:7.88,fill:"#FECE72"}),r.createElement("path",{d:"M209.51 172.625c11.868 0 21.49-3.528 21.49-7.88v-2.865h-42.98v2.865c0 4.352 9.621 7.88 21.49 7.88z",fill:"#E1B155"}),r.createElement("ellipse",{cx:209.51,cy:160.88,rx:21.49,ry:7.88,fill:"#FECE72"}),r.createElement("path",{d:"M208.859 163.363v-.48a4.538 4.538 0 01-.901-.154 2.897 2.897 0 01-.77-.342 1.423 1.423 0 01-.496-.534l.952-.209c.081.161.23.306.446.435.222.128.479.211.769.248v-1.223l-.03-.011c-.54-.111-.986-.254-1.337-.43-.351-.18-.526-.41-.526-.689 0-.165.074-.321.222-.468.156-.147.375-.269.659-.364a3.58 3.58 0 011.012-.176v-.474h.81v.485c.352.029.645.086.882.17.243.081.432.175.567.281.***************.293.304l-.931.22a.881.881 0 00-.284-.243 1.504 1.504 0 00-.527-.181v1.157c.365.081.689.171.973.27.283.095.506.211.668.347a.606.606 0 01.253.496.69.69 0 01-.273.551c-.176.151-.409.27-.699.358a4.225 4.225 0 01-.922.171v.485h-.81zm-.85-3.389c0 .***************.298.149.077.362.152.638.225v-.991a1.796 1.796 0 00-.617.17c-.156.085-.233.184-.233.298zm2.501 1.829c0-.125-.074-.226-.223-.303a2.833 2.833 0 00-.618-.22v1.041c.244-.033.443-.093.598-.181.162-.089.243-.201.243-.337z",fill:"#AA7A1E"}),r.createElement("path",{d:"M209.51 166.625c11.868 0 21.49-3.528 21.49-7.88v-2.865h-42.98v2.865c0 4.352 9.621 7.88 21.49 7.88z",fill:"#E1B155"}),r.createElement("ellipse",{cx:209.51,cy:154.88,rx:21.49,ry:7.88,fill:"#FECE72"}),r.createElement("path",{d:"M208.859 157.363v-.48a4.538 4.538 0 01-.901-.154 2.897 2.897 0 01-.77-.342 1.423 1.423 0 01-.496-.534l.952-.209c.081.161.23.306.446.435.222.128.479.211.769.248v-1.223l-.03-.011c-.54-.111-.986-.254-1.337-.43-.351-.18-.526-.41-.526-.689 0-.165.074-.321.222-.468.156-.147.375-.269.659-.364a3.58 3.58 0 011.012-.176v-.474h.81v.485c.352.029.645.086.882.17.243.081.432.175.567.281.***************.293.304l-.931.22a.881.881 0 00-.284-.243 1.504 1.504 0 00-.527-.181v1.157c.365.081.689.171.973.27.283.095.506.211.668.347a.606.606 0 01.253.496.69.69 0 01-.273.551c-.176.151-.409.27-.699.358a4.225 4.225 0 01-.922.171v.485h-.81zm-.85-3.389c0 .***************.298.149.077.362.152.638.225v-.991a1.796 1.796 0 00-.617.17c-.156.085-.233.184-.233.298zm2.501 1.829c0-.125-.074-.226-.223-.303a2.833 2.833 0 00-.618-.22v1.041c.244-.033.443-.093.598-.181.162-.089.243-.201.243-.337z",fill:"#AA7A1E"}),r.createElement("path",{d:"M209.51 160.625c11.868 0 21.49-3.528 21.49-7.88v-2.865h-42.98v2.865c0 4.352 9.621 7.88 21.49 7.88z",fill:"#E1B155"}),r.createElement("ellipse",{cx:209.51,cy:148.88,rx:21.49,ry:7.88,fill:"#FECE72"}),r.createElement("path",{d:"M208.859 151.363v-.48a4.538 4.538 0 01-.901-.154 2.897 2.897 0 01-.77-.342 1.423 1.423 0 01-.496-.534l.952-.209c.081.161.23.306.446.435.222.128.479.211.769.248v-1.223l-.03-.011c-.54-.111-.986-.254-1.337-.43-.351-.18-.526-.41-.526-.689 0-.165.074-.321.222-.468.156-.147.375-.269.659-.364a3.58 3.58 0 011.012-.176v-.474h.81v.485c.352.029.645.086.882.17.243.081.432.175.567.281.***************.293.304l-.931.22a.881.881 0 00-.284-.243 1.504 1.504 0 00-.527-.181v1.157c.365.081.689.171.973.27.283.095.506.211.668.347a.606.606 0 01.253.496.69.69 0 01-.273.551c-.176.151-.409.27-.699.358a4.225 4.225 0 01-.922.171v.485h-.81zm-.85-3.389c0 .***************.298.149.077.362.152.638.225v-.991a1.796 1.796 0 00-.617.17c-.156.085-.233.184-.233.298zm2.501 1.829c0-.125-.074-.226-.223-.303a2.833 2.833 0 00-.618-.22v1.041c.244-.033.443-.093.598-.181.162-.089.243-.201.243-.337z",fill:"#AA7A1E"}),r.createElement("path",{d:"M227.323 148.88c0 .547-.319 1.188-1.166 1.881-.841.688-2.107 1.344-3.743 1.91-3.264 1.13-7.827 1.844-12.904 1.844-5.078 0-9.64-.714-12.904-1.844-1.636-.566-2.902-1.222-3.743-1.91-.847-.693-1.166-1.334-1.166-1.881 0-.548.319-1.189 1.166-1.882.841-.688 2.107-1.344 3.743-1.91 3.264-1.13 7.826-1.844 12.904-1.844 5.077 0 9.64.714 12.904 1.844 1.636.566 2.902 1.222 3.743 1.91.847.693 1.166 1.334 1.166 1.882z",stroke:"#E1B155",strokeWidth:1.624}))),o=r.createElement("defs",null,r.createElement("clipPath",{id:"analytics-adsense-connect-desktop_svg__clip0_50_9707"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h264c8.837 0 16 7.163 16 16v147H0V16z",fill:"#fff"})),r.createElement("filter",{id:"analytics-adsense-connect-desktop_svg__filter0_d_50_9707",x:6,y:18,width:284,height:204.055,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_50_9707"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_50_9707",result:"shape"})));t.a=function SvgAnalyticsAdsenseConnectDesktop(e){return r.createElement("svg",i({viewBox:"0 0 296 163",fill:"none"},e),a,o)}},721:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#analytics-adsense-connect-mobile_svg__clip0_413_2382)"},r.createElement("path",{d:"M311.553 64.365c2.4 22.73-4.803 32.78-23.025 59.95-18.222 27.169 7.404 59.276-20.779 89.868-33.528 36.394-150.686 39.364-201.232 24.212-50.546-15.153-63.58-46.473-59.948-75.155C11.5 124.315 38.52 112.077 51.5 93 70.776 64.675 48.687 38.214 86 15.5s80.086 6.697 120.326 4.388c23.216-1.332 46.017-5.627 66.626.968 20.832 6.667 36.719 25.428 38.601 43.509z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#analytics-adsense-connect-mobile_svg__filter0_d_413_2382)"},r.createElement("rect",{x:56,y:14,width:209.444,height:143,rx:11.44,fill:"#fff"}),r.createElement("rect",{x:67.635,y:43.09,width:186.173,height:39.063,rx:4.29,fill:"#DCE8FF"}),r.createElement("rect",{x:73.454,y:109.58,width:24.934,height:2.493,rx:1.247,fill:"#9BB8F0"}),r.createElement("rect",{x:73.454,y:114.567,width:33.245,height:2.493,rx:1.247,fill:"#9BB8F0"}),r.createElement("path",{d:"M177.792 64.58l16.175 17.573h-67.322l24.481-26.597 17.486 18.998 9.18-9.974z",fill:"#9BB8F0"}),r.createElement("path",{d:"M56 25.44C56 19.121 61.122 14 67.44 14h186.565c6.318 0 11.439 5.122 11.439 11.44v5.893H56V25.44z",fill:"#EBEEF0"}),r.createElement("rect",{x:64.666,y:19.778,width:5.778,height:5.778,rx:2.889,fill:"#CBD0D3"}),r.createElement("rect",{x:73.333,y:19.778,width:5.778,height:5.778,rx:2.889,fill:"#CBD0D3"}),r.createElement("rect",{x:67.635,y:90.464,width:49.037,height:45.712,rx:4.536,fill:"#EBEEF0"}),r.createElement("rect",{x:126.646,y:90.464,width:44.881,height:8.311,rx:4.156,fill:"#EBEEF0"}),r.createElement("rect",{x:126.646,y:106.255,width:127.163,height:4.156,rx:2.078,fill:"#EBEEF0"}),r.createElement("rect",{x:126.646,y:117.06,width:127.163,height:4.156,rx:2.078,fill:"#EBEEF0"}),r.createElement("path",{d:"M235.947 131.708c9.864 0 17.861-2.932 17.861-6.549v-2.381h-35.722v2.381c0 3.617 7.997 6.549 17.861 6.549z",fill:"#E1B155"}),r.createElement("ellipse",{cx:235.947,cy:121.947,rx:17.861,ry:6.549,fill:"#FECE72"}),r.createElement("path",{d:"M235.406 124.011v-.399a3.771 3.771 0 01-.749-.128 2.446 2.446 0 01-.64-.284 1.183 1.183 0 01-.412-.444l.791-.174c.***************.37.361.186.107.399.176.64.207v-1.017l-.025-.009c-.449-.092-.819-.211-1.111-.358-.292-.149-.438-.34-.438-.572 0-.137.062-.267.185-.389.129-.122.312-.223.547-.303a3 3 0 01.842-.146v-.394h.674v.403c.291.024.535.072.732.142.202.067.359.145.471.234a.882.882 0 01.244.251l-.774.184a.736.736 0 00-.236-.202 1.248 1.248 0 00-.437-.151v.962c.303.067.572.142.808.*************.176.555.289.************.21.412 0 .18-.075.333-.227.458a1.602 1.602 0 01-.581.298c-.235.073-.491.12-.765.142v.403h-.674zm-.707-2.817c0 .**************.*************.127.53.188v-.824a1.46 1.46 0 00-.513.142c-.129.07-.194.152-.194.247zm2.079 1.52c0-.103-.062-.187-.185-.251a2.303 2.303 0 00-.513-.184v.866c.202-.028.367-.078.496-.151.135-.073.202-.167.202-.28z",fill:"#AA7A1E"}),r.createElement("path",{d:"M235.947 126.722c9.864 0 17.861-2.932 17.861-6.549v-2.382h-35.722v2.382c0 3.617 7.997 6.549 17.861 6.549z",fill:"#E1B155"}),r.createElement("ellipse",{cx:235.947,cy:116.96,rx:17.861,ry:6.549,fill:"#FECE72"}),r.createElement("path",{d:"M235.406 119.024v-.399a3.771 3.771 0 01-.749-.128 2.446 2.446 0 01-.64-.284 1.183 1.183 0 01-.412-.444l.791-.174c.***************.37.362.186.107.399.175.64.206v-1.017l-.025-.009a4.55 4.55 0 01-1.111-.357c-.292-.15-.438-.341-.438-.573 0-.137.062-.267.185-.389a1.47 1.47 0 01.547-.302c.236-.083.517-.132.842-.147v-.394h.674v.403c.291.025.535.072.732.142.202.067.359.145.471.234a.871.871 0 01.244.252l-.774.183a.748.748 0 00-.236-.202 1.248 1.248 0 00-.437-.151v.962c.303.067.572.142.808.*************.176.555.289.************.21.412 0 .18-.075.333-.227.458a1.602 1.602 0 01-.581.298c-.235.073-.491.12-.765.142v.403h-.674zm-.707-2.817c0 .**************.*************.126.53.187v-.824a1.482 1.482 0 00-.513.142c-.129.07-.194.153-.194.247zm2.079 1.521c0-.104-.062-.188-.185-.252a2.36 2.36 0 00-.513-.183v.865c.202-.027.367-.078.496-.151.135-.073.202-.166.202-.279z",fill:"#AA7A1E"}),r.createElement("path",{d:"M235.947 121.735c9.864 0 17.861-2.932 17.861-6.549v-2.382h-35.722v2.382c0 3.617 7.997 6.549 17.861 6.549z",fill:"#E1B155"}),r.createElement("ellipse",{cx:235.947,cy:111.973,rx:17.861,ry:6.549,fill:"#FECE72"}),r.createElement("path",{d:"M235.406 114.037v-.398a3.846 3.846 0 01-.749-.129 2.446 2.446 0 01-.64-.284 1.183 1.183 0 01-.412-.444l.791-.174c.***************.37.362.186.107.399.175.64.206v-1.017l-.025-.009a4.508 4.508 0 01-1.111-.357c-.292-.15-.438-.341-.438-.573 0-.137.062-.267.185-.389a1.47 1.47 0 01.547-.302c.236-.083.517-.131.842-.147v-.393h.674v.403c.291.024.535.071.732.141.202.068.359.146.471.234a.897.897 0 01.244.252l-.774.183a.735.735 0 00-.236-.201 1.227 1.227 0 00-.437-.152v.962c.303.067.572.142.808.**************.175.555.288.************.21.412 0 .18-.075.333-.227.458a1.587 1.587 0 01-.581.298 3.473 3.473 0 01-.765.142v.403h-.674zm-.707-2.817c0 .***************.*************.126.53.188v-.825a1.505 1.505 0 00-.513.142c-.129.07-.194.153-.194.247zm2.079 1.521c0-.104-.062-.188-.185-.252a2.36 2.36 0 00-.513-.183v.865c.202-.027.367-.077.496-.151.135-.073.202-.166.202-.279z",fill:"#AA7A1E"}),r.createElement("path",{d:"M235.947 116.748c9.864 0 17.861-2.932 17.861-6.549v-2.381h-35.722v2.381c0 3.617 7.997 6.549 17.861 6.549z",fill:"#E1B155"}),r.createElement("ellipse",{cx:235.947,cy:106.986,rx:17.861,ry:6.549,fill:"#FECE72"}),r.createElement("path",{d:"M235.406 109.05v-.398a3.845 3.845 0 01-.749-.128 2.479 2.479 0 01-.64-.284 1.193 1.193 0 01-.412-.445l.791-.174c.***************.37.362.186.107.399.176.64.206v-1.016l-.025-.01c-.449-.091-.819-.21-1.111-.357-.292-.149-.438-.34-.438-.572 0-.138.062-.267.185-.39.129-.122.312-.222.547-.302a3 3 0 01.842-.146v-.394h.674v.403c.291.024.535.071.732.142.202.067.359.145.471.233a.897.897 0 01.244.252l-.774.183a.735.735 0 00-.236-.201 1.228 1.228 0 00-.437-.151v.961c.303.068.572.142.808.**************.175.555.288.************.21.413 0 .18-.075.332-.227.458a1.586 1.586 0 01-.581.297 3.473 3.473 0 01-.765.142v.403h-.674zm-.707-2.816c0 .**************.*************.127.53.188v-.825a1.505 1.505 0 00-.513.142c-.129.071-.194.153-.194.248zm2.079 1.52c0-.104-.062-.188-.185-.252a2.36 2.36 0 00-.513-.183v.866c.202-.028.367-.078.496-.151.135-.074.202-.167.202-.28z",fill:"#AA7A1E"}),r.createElement("path",{d:"M235.947 111.761c9.864 0 17.861-2.932 17.861-6.549v-2.381h-35.722v2.381c0 3.617 7.997 6.549 17.861 6.549z",fill:"#E1B155"}),r.createElement("ellipse",{cx:235.947,cy:102,rx:17.861,ry:6.549,fill:"#FECE72"}),r.createElement("path",{d:"M235.406 104.063v-.398a3.771 3.771 0 01-.749-.128 2.446 2.446 0 01-.64-.284 1.183 1.183 0 01-.412-.444l.791-.174c.***************.37.361.186.107.399.176.64.206v-1.016l-.025-.009c-.449-.092-.819-.211-1.111-.358-.292-.149-.438-.34-.438-.572 0-.137.062-.267.185-.389.129-.122.312-.223.547-.303a3 3 0 01.842-.146v-.394h.674v.403c.291.024.535.072.732.142.202.067.359.145.471.234a.882.882 0 01.244.251l-.774.184a.724.724 0 00-.236-.202 1.248 1.248 0 00-.437-.151v.962c.303.067.572.142.808.**************.176.555.289.************.21.412 0 .18-.075.333-.227.458a1.586 1.586 0 01-.581.297 3.416 3.416 0 01-.765.142v.403h-.674zm-.707-2.816c0 .**************.*************.127.53.188v-.824a1.46 1.46 0 00-.513.142c-.129.07-.194.152-.194.247zm2.079 1.52c0-.103-.062-.187-.185-.252a2.36 2.36 0 00-.513-.183v.866c.202-.028.367-.078.496-.151.135-.074.202-.167.202-.28z",fill:"#AA7A1E"}),r.createElement("path",{d:"M235.947 106.775c9.864 0 17.861-2.933 17.861-6.549v-2.382h-35.722v2.382c0 3.616 7.997 6.549 17.861 6.549z",fill:"#E1B155"}),r.createElement("ellipse",{cx:235.947,cy:97.013,rx:17.861,ry:6.549,fill:"#FECE72"}),r.createElement("path",{d:"M235.406 99.077v-.399a3.778 3.778 0 01-.749-.128 2.449 2.449 0 01-.64-.284 1.184 1.184 0 01-.412-.444l.791-.174c.***************.37.362a1.7 1.7 0 00.64.206v-1.017l-.025-.01a4.511 4.511 0 01-1.111-.356c-.292-.15-.438-.34-.438-.573 0-.137.062-.267.185-.39.129-.121.312-.222.547-.301.236-.083.517-.132.842-.147v-.394h.674v.403c.291.025.535.072.732.142.202.067.359.145.471.234a.886.886 0 01.244.252l-.774.183a.735.735 0 00-.236-.202 1.236 1.236 0 00-.437-.15v.96c.303.068.572.143.808.*************.176.555.289.************.21.412 0 .18-.075.333-.227.458a1.588 1.588 0 01-.581.298c-.235.073-.491.12-.765.142v.403h-.674zm-.707-2.817c0 .**************.*************.127.53.188v-.824a1.487 1.487 0 00-.513.142c-.129.07-.194.152-.194.247zm2.079 1.52c0-.103-.062-.187-.185-.251a2.354 2.354 0 00-.513-.183v.865c.202-.027.367-.078.496-.151.135-.073.202-.166.202-.28z",fill:"#AA7A1E"}),r.createElement("path",{d:"M250.751 97.013c0 .455-.265.987-.969 1.563-.698.572-1.751 1.117-3.11 1.588-2.713.939-6.505 1.532-10.725 1.532s-8.012-.593-10.725-1.532c-1.36-.47-2.412-1.016-3.111-1.588-.704-.576-.969-1.108-.969-1.563 0-.456.265-.988.969-1.564.699-.572 1.751-1.117 3.111-1.587 2.713-.94 6.505-1.533 10.725-1.533s8.012.593 10.725 1.533c1.359.47 2.412 1.015 3.11 1.587.704.576.969 1.108.969 1.564z",stroke:"#E1B155",strokeWidth:1.349}),r.createElement("ellipse",{cx:211.845,cy:127.765,rx:17.861,ry:6.549,fill:"#FECE72"}),r.createElement("ellipse",{cx:211.845,cy:122.778,rx:17.861,ry:6.549,fill:"#FECE72"}),r.createElement("path",{d:"M211.304 124.842v-.399a3.771 3.771 0 01-.749-.128 2.446 2.446 0 01-.64-.284 1.183 1.183 0 01-.412-.444l.791-.174c.***************.37.362.186.106.399.175.64.206v-1.017l-.025-.009a4.55 4.55 0 01-1.111-.357c-.292-.15-.438-.341-.438-.573 0-.137.062-.267.185-.389a1.47 1.47 0 01.547-.302c.236-.083.517-.132.842-.147v-.394h.673v.403c.292.025.536.072.733.142.202.067.359.145.471.234a.871.871 0 01.244.252l-.774.183a.736.736 0 00-.236-.202 1.246 1.246 0 00-.438-.151v.962c.303.067.573.142.808.224.236.08.421.176.556.289.************.21.412 0 .18-.075.333-.227.458a1.602 1.602 0 01-.581.298 3.53 3.53 0 01-.766.142v.403h-.673zm-.707-2.817c0 .**************.*************.127.53.188v-.824a1.482 1.482 0 00-.513.142c-.129.07-.194.152-.194.247zm2.079 1.521c0-.104-.062-.188-.185-.252a2.353 2.353 0 00-.514-.183v.865c.202-.027.368-.078.497-.151.135-.073.202-.166.202-.279z",fill:"#AA7A1E"}),r.createElement("path",{d:"M211.845 127.553c9.864 0 17.861-2.932 17.861-6.549v-2.382h-35.722v2.382c0 3.617 7.996 6.549 17.861 6.549z",fill:"#E1B155"}),r.createElement("ellipse",{cx:211.845,cy:117.791,rx:17.861,ry:6.549,fill:"#FECE72"}),r.createElement("path",{d:"M211.304 119.855v-.399a3.771 3.771 0 01-.749-.128 2.446 2.446 0 01-.64-.284 1.183 1.183 0 01-.412-.444l.791-.174c.***************.37.362.186.107.399.175.64.206v-1.017l-.025-.009a4.55 4.55 0 01-1.111-.357c-.292-.15-.438-.341-.438-.573 0-.137.062-.267.185-.389a1.47 1.47 0 01.547-.302c.236-.083.517-.131.842-.147v-.394h.673v.403c.292.025.536.072.733.142.202.068.359.145.471.234a.884.884 0 01.244.252l-.774.183a.735.735 0 00-.236-.201 1.226 1.226 0 00-.438-.152v.962c.303.067.573.142.808.225.236.079.421.175.556.288.************.21.412 0 .18-.075.333-.227.458a1.587 1.587 0 01-.581.298 3.53 3.53 0 01-.766.142v.403h-.673zm-.707-2.817c0 .**************.*************.126.53.187v-.824a1.505 1.505 0 00-.513.142c-.129.07-.194.153-.194.247zm2.079 1.521c0-.104-.062-.188-.185-.252a2.353 2.353 0 00-.514-.183v.865c.202-.027.368-.078.497-.151.135-.073.202-.166.202-.279z",fill:"#AA7A1E"}),r.createElement("path",{d:"M211.845 122.566c9.864 0 17.861-2.932 17.861-6.549v-2.381h-35.722v2.381c0 3.617 7.996 6.549 17.861 6.549z",fill:"#E1B155"}),r.createElement("ellipse",{cx:211.845,cy:112.804,rx:17.861,ry:6.549,fill:"#FECE72"}),r.createElement("path",{d:"M211.304 114.868v-.398a3.772 3.772 0 01-.749-.129 2.413 2.413 0 01-.64-.284 1.183 1.183 0 01-.412-.444l.791-.174c.***************.37.362.186.107.399.176.64.206v-1.017l-.025-.009c-.449-.091-.819-.21-1.111-.357-.292-.15-.438-.34-.438-.572 0-.138.062-.268.185-.39a1.47 1.47 0 01.547-.302c.236-.082.517-.131.842-.147v-.393h.673v.403c.292.024.536.071.733.142.202.067.359.145.471.233a.897.897 0 01.244.252l-.774.183a.735.735 0 00-.236-.201 1.227 1.227 0 00-.438-.151v.961c.303.068.573.142.808.225.236.079.421.175.556.288.************.21.412a.57.57 0 01-.227.458 1.572 1.572 0 01-.581.298 3.471 3.471 0 01-.766.142v.403h-.673zm-.707-2.816c0 .**************.*************.127.53.188v-.825a1.505 1.505 0 00-.513.142c-.129.07-.194.153-.194.248zm2.079 1.52c0-.104-.062-.188-.185-.252a2.353 2.353 0 00-.514-.183v.866c.202-.028.368-.078.497-.152.135-.073.202-.166.202-.279z",fill:"#AA7A1E"}),r.createElement("path",{d:"M226.649 112.804c0 .456-.265.988-.969 1.564-.698.572-1.751 1.117-3.11 1.587-2.713.94-6.505 1.533-10.725 1.533s-8.012-.593-10.725-1.533c-1.36-.47-2.412-1.015-3.111-1.587-.704-.576-.969-1.108-.969-1.564 0-.455.265-.987.969-1.563.699-.572 1.751-1.117 3.111-1.588 2.713-.939 6.505-1.532 10.725-1.532s8.012.593 10.725 1.532c1.359.471 2.412 1.016 3.11 1.588.704.576.969 1.108.969 1.563z",stroke:"#E1B155",strokeWidth:1.349}))),o=r.createElement("defs",null,r.createElement("clipPath",{id:"analytics-adsense-connect-mobile_svg__clip0_413_2382"},r.createElement("path",{fill:"#fff",d:"M0 0h343v123H0z"})),r.createElement("filter",{id:"analytics-adsense-connect-mobile_svg__filter0_d_413_2382",x:42.702,y:4.026,width:236.04,height:169.596,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.325}),r.createElement("feGaussianBlur",{stdDeviation:6.649}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_413_2382"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_413_2382",result:"shape"})));t.a=function SvgAnalyticsAdsenseConnectMobile(e){return r.createElement("svg",i({viewBox:"0 0 343 123",fill:"none"},e),a,o)}},722:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(550),l=n(421),s=n(273),u=n(0),d=n(3),f=n(23);function g(){var t=Object(d.useDispatch)(f.b).setIsOnline,n=Object(d.useSelect)((function(e){return e(f.b).getIsOnline()})),r=Object(u.useCallback)(o()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(navigator.onLine){e.next=3;break}return t(!1),e.abrupt("return");case 3:return e.prev=3,e.next=6,Object(s.default)({path:"/google-site-kit/v1/"});case 6:e.next=13;break;case 8:if(e.prev=8,e.t0=e.catch(3),"fetch_error"!==(null===e.t0||void 0===e.t0?void 0:e.t0.code)){e.next=13;break}return t(!1),e.abrupt("return");case 13:t(!0);case 14:case"end":return e.stop()}}),e,null,[[3,8]])}))),[t]);Object(c.a)((function(){e.addEventListener("online",r),e.addEventListener("offline",r)}),(function(){e.removeEventListener("online",r),e.removeEventListener("offline",r)})),Object(l.a)(r,n?12e4:15e3)}}).call(this,n(28))},73:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),i=n(18),a=n(9);function o(e,t){var n=Object(i.a)(),o=null!=t?t:"".concat(n,"_").concat(e);return{view:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"view_notification"].concat(t))}),[o]),confirm:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"confirm_notification"].concat(t))}),[o]),dismiss:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"dismiss_notification"].concat(t))}),[o]),clickLearnMore:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"click_learn_more_link"].concat(t))}),[o])}}},74:function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(33),i=n.n(r),a=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,n="object"===i()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(1),i=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,i=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:i}},n)}IconWrapper.propTypes={children:i.a.node.isRequired,marginLeft:i.a.number,marginRight:i.a.number}}).call(this,n(4))},77:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.label,a=t.className,c=t.hasLeftSpacing,s=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",i()({ref:n},u,{className:l()("googlesitekit-badge",a,{"googlesitekit-badge--has-left-spacing":s})}),r)}));f.displayName="Badge",f.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=f}).call(this,n(4))},79:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(15),i=n.n(r),a=n(188),o=n(133),c={},l=void 0===e?null:e,s=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,u=void 0===r?0:r,d=e.initialHeight,f=void 0===d?0:d,g=Object(a.a)("undefined"==typeof document?[u,f]:s,t,n),m=i()(g,2),p=m[0],h=m[1],v=function(){return h(s)};return Object(o.a)(l,"resize",v),Object(o.a)(l,"orientationchange",v),p},d=function(e){return u(e)[0]}}).call(this,n(28))},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"s",(function(){return a})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return l})),n.d(t,"g",(function(){return s})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return f})),n.d(t,"k",(function(){return g})),n.d(t,"m",(function(){return m})),n.d(t,"n",(function(){return p})),n.d(t,"h",(function(){return h})),n.d(t,"x",(function(){return v})),n.d(t,"w",(function(){return b})),n.d(t,"y",(function(){return E})),n.d(t,"u",(function(){return _})),n.d(t,"v",(function(){return O})),n.d(t,"f",(function(){return y})),n.d(t,"l",(function(){return k})),n.d(t,"e",(function(){return j})),n.d(t,"t",(function(){return S})),n.d(t,"c",(function(){return w})),n.d(t,"d",(function(){return x})),n.d(t,"b",(function(){return C}));var r="modules/analytics-4",i="account_create",a="property_create",o="webdatastream_create",c="analyticsSetup",l=10,s=1,u="https://www.googleapis.com/auth/tagmanager.readonly",d="enhanced-measurement-form",f="enhanced-measurement-enabled",g="enhanced-measurement-should-dismiss-activation-banner",m="analyticsAccountCreate",p="analyticsCustomDimensionsCreate",h="https://www.googleapis.com/auth/analytics.edit",v="dashboardAllTrafficWidgetDimensionName",b="dashboardAllTrafficWidgetDimensionColor",E="dashboardAllTrafficWidgetDimensionValue",_="dashboardAllTrafficWidgetActiveRowIndex",O="dashboardAllTrafficWidgetLoaded",y={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},k={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},j=[k.CONTACT,k.GENERATE_LEAD,k.SUBMIT_LEAD_FORM],S={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},w="audiencePermissionsSetup",x="audienceTileCustomDimensionCreate",C="audience-selection-panel-expirable-new-badge-"},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(106);function i(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),i=e.replace(n.origin,"");if(i.length<t)return i;var a=i.length-Math.floor(t)+1;return"…"+i.substr(a)}},82:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return a}));var r=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},i=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function a(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return k})),n.d(t,"d",(function(){return j})),n.d(t,"e",(function(){return w})),n.d(t,"c",(function(){return x})),n.d(t,"b",(function(){return C}));var r=n(15),i=n.n(r),a=n(33),o=n.n(a),c=n(6),l=n.n(c),s=n(25),u=n.n(s),d=n(14),f=n(63),g=n.n(f),m=n(2);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=_(e,t),r=n.formatUnit,i=n.formatDecimal;try{return r()}catch(e){return i()}},b=function(e){var t=E(e),n=t.hours,r=t.minutes,i=t.seconds;return i=("0"+i).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(i):"".concat(n,":").concat(r,":").concat(i)},E=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},_=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=E(e),r=n.hours,i=n.minutes,a=n.seconds;return{hours:r,minutes:i,seconds:a,formatUnit:function(){var n=t.unitDisplay,o=h(h({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(a,h(h({},o),{},{unit:"second"})):Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?w(a,h(h({},o),{},{unit:"second"})):"",i?w(i,h(h({},o),{},{unit:"minute"})):"",r?w(r,h(h({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(m.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(m.__)("%ds","google-site-kit"),a);if(0===e)return t;var n=Object(m.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(m.__)("%dm","google-site-kit"),i),o=Object(m.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(m.__)("%dh","google-site-kit"),r);return Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",i?n:"",r?o:"").trim()}}},O=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},y=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in millions.
Object(m.__)("%sM","google-site-kit"),w(O(e),e%10==0?{}:t)):1e4<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),w(O(e))):1e3<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),w(O(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function k(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=h({},e)),t}function j(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=k(t),r=n.style,i=void 0===r?"metric":r;return"metric"===i?y(e):"duration"===i?v(e,n):"durationISO"===i?b(e):w(e,n)}var S=g()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?C():n,a=u()(t,["locale"]);try{return new Intl.NumberFormat(r,a).format(e)}catch(t){S("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},l=["signDisplay","compactDisplay"],s={},d=0,f=Object.entries(a);d<f.length;d++){var g=i()(f[d],2),m=g[0],p=g[1];c[m]&&p===c[m]||(l.includes(m)||(s[m]=p))}try{return new Intl.NumberFormat(r,s).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},x=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?C():n,i=t.style,a=void 0===i?"long":i,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var l=new Intl.ListFormat(r,{style:a,type:c});return l.format(e)}
/* translators: used between list items, there is a space after the comma. */var s=Object(m.__)(", ","google-site-kit");return e.join(s)},C=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i}));var r=n(149),i=n.n(r)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a);function ChangeArrow(t){var n=t.direction,r=t.invertColor,i=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:i,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:i.a.string,invertColor:i.a.bool,width:i.a.number,height:i.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},86:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={EXTERNAL:"external",INTERNAL:"internal"}},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(12),i=n.n(r),a=function(e,t){var n=t.dateRangeLength;i()(Array.isArray(e),"report must be an array to partition."),i()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return i.b})),n.d(t,"J",(function(){return i.c})),n.d(t,"F",(function(){return a.a})),n.d(t,"K",(function(){return a.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return p})),n.d(t,"j",(function(){return h})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return k})),n.d(t,"c",(function(){return j})),n.d(t,"e",(function(){return S})),n.d(t,"b",(function(){return w})),n.d(t,"a",(function(){return x})),n.d(t,"f",(function(){return C})),n.d(t,"n",(function(){return N})),n.d(t,"w",(function(){return A})),n.d(t,"p",(function(){return T})),n.d(t,"G",(function(){return D})),n.d(t,"s",(function(){return R})),n.d(t,"v",(function(){return B})),n.d(t,"k",(function(){return M})),n.d(t,"o",(function(){return I.b})),n.d(t,"h",(function(){return I.a})),n.d(t,"t",(function(){return L.b})),n.d(t,"q",(function(){return L.a})),n.d(t,"A",(function(){return L.c})),n.d(t,"x",(function(){return F})),n.d(t,"u",(function(){return P})),n.d(t,"E",(function(){return W})),n.d(t,"D",(function(){return G.a})),n.d(t,"g",(function(){return V})),n.d(t,"L",(function(){return U})),n.d(t,"l",(function(){return q}));var r=n(14),i=n(36),a=n(75),o=n(33),c=n.n(o),l=n(96),s=n.n(l),u=function(e){return s()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var i=t[r];i&&"object"===c()(i)&&!Array.isArray(i)&&(i=e(i)),n[r]=i})),n}(e)))};n(97);var d=n(83);function f(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function g(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function m(e){return e.replace(/\n/gi,"<br>")}function p(e){for(var t=e,n=0,r=[f,g,m];n<r.length;n++){t=(0,r[n])(t)}return t}var h=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},b=n(15),E=n.n(b),_=n(12),O=n.n(_),y=n(2),k="Invalid dateString parameter, it must be a string.",j='Invalid date range, it must be a string with the format "last-x-days".',S=60,w=60*S,x=24*w,C=7*x;function N(){var e=function(e){return Object(y.sprintf)(
/* translators: %s: number of days */
Object(y._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function A(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function T(e){O()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function D(e){O()(A(e),k);var t=e.split("-"),n=E()(t,3),r=n[0],i=n[1],a=n[2];return new Date(r,i-1,a)}function R(e,t){return T(M(e,t*x))}function B(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function M(e,t){O()(A(e)||Object(r.isDate)(e)&&!isNaN(e),k);var n=A(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var I=n(98),L=n(80);function F(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function P(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var z=n(27),H=n.n(z),W=function(e){return Array.isArray(e)?H()(e).sort():e},G=n(89);function V(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var U=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},q=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},90:function(e,t,n){"use strict";(function(e,r){n.d(t,"c",(function(){return b})),n.d(t,"b",(function(){return _})),n.d(t,"a",(function(){return TourTooltips}));var i=n(6),a=n.n(i),o=n(81),c=n(30),l=n(1),s=n.n(l),u=n(2),d=n(3),f=n(23),g=n(7),m=n(36),p=n(107),h=n(18);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var b={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},E={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},_={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},O="feature_tooltip_view",y="feature_tooltip_advance",k="feature_tooltip_return",j="feature_tooltip_dismiss",S="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,i=t.tourID,l=t.gaEventCategory,s=t.callback,u="".concat(i,"-step"),w="".concat(i,"-run"),x=Object(d.useDispatch)(f.b).setValue,C=Object(d.useDispatch)(g.a).dismissTour,N=Object(d.useRegistry)(),A=Object(h.a)(),T=Object(d.useSelect)((function(e){return e(f.b).getValue(u)})),D=Object(d.useSelect)((function(e){return e(f.b).getValue(w)&&!1===e(g.a).isTourDismissed(i)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),x(w,!0)}));var R=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return r.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,r=e.lifecycle,i=e.size,a=e.status,o=e.type,s=t+1,u="function"==typeof l?l(A):l;o===c.b.TOOLTIP&&r===c.c.TOOLTIP?Object(m.b)(u,O,s):n===c.a.CLOSE&&r===c.c.COMPLETE?Object(m.b)(u,j,s):n===c.a.NEXT&&a===c.d.FINISHED&&o===c.b.TOUR_END&&i===s&&Object(m.b)(u,S,s),r===c.c.COMPLETE&&a!==c.d.FINISHED&&(n===c.a.PREV&&Object(m.b)(u,k,s),n===c.a.NEXT&&Object(m.b)(u,y,s))}(t);var n=t.action,r=t.index,a=t.status,o=t.step,d=t.type,f=n===c.a.CLOSE,g=!f&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),p=[c.d.FINISHED,c.d.SKIPPED].includes(a),h=f&&d===c.b.STEP_AFTER,v=p||h;if(c.b.STEP_BEFORE===d){var b,E,_=o.target;"string"==typeof o.target&&(_=e.document.querySelector(o.target)),null===(b=_)||void 0===b||null===(E=b.scrollIntoView)||void 0===E||E.call(b,{block:"center"})}g?function(e,t){x(u,e+(t===c.a.PREV?-1:1))}(r,n):v&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),C(i)),s&&s(t,N)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:_,locale:E,run:D,showProgress:!0,stepIndex:T,steps:R,styles:b,tooltipComponent:p.a})}TourTooltips.propTypes={steps:s.a.arrayOf(s.a.object).isRequired,tourID:s.a.string.isRequired,gaEventCategory:s.a.oneOfType([s.a.string,s.a.func]).isRequired,callback:s.a.func}}).call(this,n(28),n(4))},92:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Dismiss}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(1),u=n.n(s),d=n(2),f=n(3),g=n(73),m=n(41),p=n(10);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Dismiss(t){var n=t.id,r=t.primary,a=void 0===r||r,o=t.dismissLabel,c=void 0===o?Object(d.__)("OK, Got it!","google-site-kit"):o,s=t.dismissExpires,u=void 0===s?0:s,h=t.disabled,b=t.onDismiss,E=void 0===b?function(){}:b,_=t.gaTrackingEventArgs,O=t.dismissOptions,y=Object(g.a)(n,null==_?void 0:_.category),k=Object(f.useDispatch)(m.a).dismissNotification,j=function(){var e=l()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==E?void 0:E(t);case 2:y.dismiss(null==_?void 0:_.label,null==_?void 0:_.value),k(n,v(v({},O),{},{expiresInSeconds:u}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(p.Button,{tertiary:!a,onClick:j,disabled:h},c)}Dismiss.propTypes={id:u.a.string,primary:u.a.bool,dismissLabel:u.a.string,dismissExpires:u.a.number,disabled:u.a.bool,onDismiss:u.a.func,gaTrackingEventArgs:u.a.shape({label:u.a.string,value:u.a.string})}}).call(this,n(4))},93:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(24),i=n(130);function a(t,n){var r=document.querySelector(t);if(!r)return 0;var i=r.getBoundingClientRect().top,a=o(n);return i+e.scrollY-a}function o(e){var t=c(e),n=document.querySelectorAll(".googlesitekit-navigation, .googlesitekit-entity-header");return t+=Array.from(n).reduce((function(e,t){return e+t.offsetHeight}),0)}function c(t){var n=0,a=document.querySelector(".googlesitekit-header");return n=!!a&&"sticky"===e.getComputedStyle(a).position?function(e){var t=document.querySelector(".googlesitekit-header");if(t){if(e===r.b)return t.offsetHeight;var n=t.getBoundingClientRect().bottom;return n<0?0:n}return 0}(t):function(e){var t=document.querySelector("#wpadminbar");return t&&e!==r.b?t.offsetHeight:0}(t),(n=Object(i.a)(n))<0?0:n}}).call(this,n(28))},95:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(10),l=n(20);function CTA(t){var n=t.title,r=t.headerText,i=t.headerContent,a=t.description,s=t.ctaLink,u=t.ctaLabel,d=t.ctaLinkExternal,f=t.ctaType,g=t.error,m=t.onClick,p=t["aria-label"],h=t.children;return e.createElement("div",{className:o()("googlesitekit-cta",{"googlesitekit-cta--error":g})},(r||i)&&e.createElement("div",{className:"googlesitekit-cta__header"},r&&e.createElement("h2",{className:"googlesitekit-cta__header_text"},r),i),e.createElement("div",{className:"googlesitekit-cta__body"},n&&e.createElement("h3",{className:"googlesitekit-cta__title"},n),a&&"string"==typeof a&&e.createElement("p",{className:"googlesitekit-cta__description"},a),a&&"string"!=typeof a&&e.createElement("div",{className:"googlesitekit-cta__description"},a),u&&"button"===f&&e.createElement(c.Button,{"aria-label":p,href:s,onClick:m},u),u&&"link"===f&&e.createElement(l.a,{href:s,onClick:m,"aria-label":p,external:d,hideExternalIndicator:d,arrow:!0},u),h))}CTA.propTypes={title:i.a.string.isRequired,headerText:i.a.string,description:i.a.oneOfType([i.a.string,i.a.node]),ctaLink:i.a.string,ctaLinkExternal:i.a.bool,ctaLabel:i.a.string,ctaType:i.a.string,"aria-label":i.a.string,error:i.a.bool,onClick:i.a.func,children:i.a.node,headerContent:i.a.node},CTA.defaultProps={title:"",headerText:"",headerContent:"",description:"",ctaLink:"",ctaLabel:"",ctaType:"link",error:!1,onClick:function(){}},t.a=CTA}).call(this,n(4))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o}));var r=n(239),i=n(85),a=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=n.invertColor,o=void 0!==a&&a;return Object(r.a)(e.createElement(i.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(6),i=n.n(r),a=n(14),o=n(100),c=n(101);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=s(s({},u),t);i.referenceSiteURL&&(i.referenceSiteURL=i.referenceSiteURL.toString().replace(/\/+$/,""));var l=Object(o.a)(i,n),d=Object(c.a)(i,n,l,r),f={},g=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);f[r]||(f[r]=Object(a.once)(d)),f[r].apply(f,t)};return{enableTracking:function(){i.trackingEnabled=!0},disableTracking:function(){i.trackingEnabled=!1},initializeSnippet:l,isTrackingEnabled:function(){return!!i.trackingEnabled},trackEvent:d,trackEventOnce:g}}}).call(this,n(28))}},[[1287,1,0]]]);