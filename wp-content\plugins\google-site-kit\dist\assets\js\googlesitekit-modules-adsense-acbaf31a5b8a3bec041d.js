(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[19],[,,function(e,t){e.exports=googlesitekit.i18n},function(e,t){e.exports=googlesitekit.data},,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r})),n.d(t,"e",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return l})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return g})),n.d(t,"J",(function(){return m})),n.d(t,"I",(function(){return f})),n.d(t,"N",(function(){return p})),n.d(t,"f",(function(){return b})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return y})),n.d(t,"l",(function(){return O})),n.d(t,"m",(function(){return _})),n.d(t,"n",(function(){return E})),n.d(t,"o",(function(){return k})),n.d(t,"q",(function(){return S})),n.d(t,"s",(function(){return j})),n.d(t,"r",(function(){return T})),n.d(t,"t",(function(){return A})),n.d(t,"w",(function(){return N})),n.d(t,"u",(function(){return C})),n.d(t,"v",(function(){return w})),n.d(t,"x",(function(){return I})),n.d(t,"y",(function(){return M})),n.d(t,"A",(function(){return R})),n.d(t,"B",(function(){return D})),n.d(t,"C",(function(){return x})),n.d(t,"D",(function(){return L})),n.d(t,"k",(function(){return P})),n.d(t,"F",(function(){return G})),n.d(t,"z",(function(){return Z})),n.d(t,"G",(function(){return B})),n.d(t,"E",(function(){return U})),n.d(t,"i",(function(){return F})),n.d(t,"p",(function(){return z})),n.d(t,"Q",(function(){return V})),n.d(t,"P",(function(){return H}));var a="core/user",r="connected_url_mismatch",i="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",l="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",g="googlesitekit_read_shared_module_data",m="googlesitekit_manage_module_sharing_options",f="googlesitekit_delegate_module_sharing_management",p="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",y="kmAnalyticsNewVisitors",O="kmAnalyticsPopularAuthors",_="kmAnalyticsPopularContent",E="kmAnalyticsPopularProducts",k="kmAnalyticsReturningVisitors",S="kmAnalyticsTopCities",j="kmAnalyticsTopCitiesDrivingLeads",T="kmAnalyticsTopCitiesDrivingAddToCart",A="kmAnalyticsTopCitiesDrivingPurchases",N="kmAnalyticsTopDeviceDrivingPurchases",C="kmAnalyticsTopConvertingTrafficSource",w="kmAnalyticsTopCountries",I="kmAnalyticsTopPagesDrivingLeads",M="kmAnalyticsTopRecentTrendingPages",R="kmAnalyticsTopTrafficSource",D="kmAnalyticsTopTrafficSourceDrivingAddToCart",x="kmAnalyticsTopTrafficSourceDrivingLeads",L="kmAnalyticsTopTrafficSourceDrivingPurchases",P="kmAnalyticsPagesPerVisit",G="kmAnalyticsVisitLength",Z="kmAnalyticsTopReturningVisitorPages",B="kmSearchConsolePopularKeywords",U="kmAnalyticsVisitsPerVisitor",F="kmAnalyticsMostEngagingPages",z="kmAnalyticsTopCategories",V=[b,v,h,y,O,_,E,k,z,S,j,T,A,N,C,w,M,R,D,P,G,Z,U,F,z],H=[].concat(V,[B])},function(e,t,n){"use strict";n.d(t,"r",(function(){return a})),n.d(t,"a",(function(){return r})),n.d(t,"s",(function(){return i})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return s})),n.d(t,"g",(function(){return l})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return g})),n.d(t,"k",(function(){return m})),n.d(t,"m",(function(){return f})),n.d(t,"n",(function(){return p})),n.d(t,"h",(function(){return b})),n.d(t,"x",(function(){return v})),n.d(t,"w",(function(){return h})),n.d(t,"y",(function(){return y})),n.d(t,"u",(function(){return O})),n.d(t,"v",(function(){return _})),n.d(t,"f",(function(){return E})),n.d(t,"l",(function(){return k})),n.d(t,"e",(function(){return S})),n.d(t,"t",(function(){return j})),n.d(t,"c",(function(){return T})),n.d(t,"d",(function(){return A})),n.d(t,"b",(function(){return N}));var a="modules/analytics-4",r="account_create",i="property_create",o="webdatastream_create",c="analyticsSetup",s=10,l=1,u="https://www.googleapis.com/auth/tagmanager.readonly",d="enhanced-measurement-form",g="enhanced-measurement-enabled",m="enhanced-measurement-should-dismiss-activation-banner",f="analyticsAccountCreate",p="analyticsCustomDimensionsCreate",b="https://www.googleapis.com/auth/analytics.edit",v="dashboardAllTrafficWidgetDimensionName",h="dashboardAllTrafficWidgetDimensionColor",y="dashboardAllTrafficWidgetDimensionValue",O="dashboardAllTrafficWidgetActiveRowIndex",_="dashboardAllTrafficWidgetLoaded",E={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},k={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},S=[k.CONTACT,k.GENERATE_LEAD,k.SUBMIT_LEAD_FORM],j={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},T="audiencePermissionsSetup",A="audienceTileCustomDimensionCreate",N="audience-selection-panel-expirable-new-badge-"},function(e,t,n){"use strict";n.d(t,"I",(function(){return r.b})),n.d(t,"J",(function(){return r.c})),n.d(t,"F",(function(){return i.a})),n.d(t,"K",(function(){return i.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return p})),n.d(t,"j",(function(){return b})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return k})),n.d(t,"c",(function(){return S})),n.d(t,"e",(function(){return j})),n.d(t,"b",(function(){return T})),n.d(t,"a",(function(){return A})),n.d(t,"f",(function(){return N})),n.d(t,"n",(function(){return C})),n.d(t,"w",(function(){return w})),n.d(t,"p",(function(){return I})),n.d(t,"G",(function(){return M})),n.d(t,"s",(function(){return R})),n.d(t,"v",(function(){return D})),n.d(t,"k",(function(){return x})),n.d(t,"o",(function(){return L.b})),n.d(t,"h",(function(){return L.a})),n.d(t,"t",(function(){return P.b})),n.d(t,"q",(function(){return P.a})),n.d(t,"A",(function(){return P.c})),n.d(t,"x",(function(){return G})),n.d(t,"u",(function(){return Z})),n.d(t,"E",(function(){return F})),n.d(t,"D",(function(){return z.a})),n.d(t,"g",(function(){return V})),n.d(t,"L",(function(){return H})),n.d(t,"l",(function(){return W}));var a=n(14),r=n(36),i=n(75),o=n(33),c=n.n(o),s=n(96),l=n.n(s),u=function(e){return l()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(a){var r=t[a];r&&"object"===c()(r)&&!Array.isArray(r)&&(r=e(r)),n[a]=r})),n}(e)))};n(97);var d=n(83);function g(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function m(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function f(e){return e.replace(/\n/gi,"<br>")}function p(e){for(var t=e,n=0,a=[g,m,f];n<a.length;n++){t=(0,a[n])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(15),y=n.n(h),O=n(12),_=n.n(O),E=n(2),k="Invalid dateString parameter, it must be a string.",S='Invalid date range, it must be a string with the format "last-x-days".',j=60,T=60*j,A=24*T,N=7*A;function C(){var e=function(e){return Object(E.sprintf)(
/* translators: %s: number of days */
Object(E._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function w(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(a.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(a.isDate)(n)&&!isNaN(n)}function I(e){_()(Object(a.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function M(e){_()(w(e),k);var t=e.split("-"),n=y()(t,3),a=n[0],r=n[1],i=n[2];return new Date(a,r-1,i)}function R(e,t){return I(x(e,t*A))}function D(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function x(e,t){_()(w(e)||Object(a.isDate)(e)&&!isNaN(e),k);var n=w(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var L=n(98),P=n(80);function G(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function Z(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var B=n(27),U=n.n(B),F=function(e){return Array.isArray(e)?U()(e).sort():e},z=n(89);function V(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var a=(t-e)/e;return Number.isNaN(a)||!Number.isFinite(a)?null:a}var H=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},W=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(a.unescape)(t)}},function(e,t){e.exports=googlesitekit.components},,,function(e,t,n){"use strict";n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var a="core/site",r="primary",i="secondary"},,,,function(e,t,n){"use strict";var a=n(254);n.d(t,"i",(function(){return a.a}));var r=n(319);n.d(t,"f",(function(){return r.a}));var i=n(320);n.d(t,"h",(function(){return i.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var s=n(91),l=n.n(s);n.d(t,"b",(function(){return l.a})),n.d(t,"c",(function(){return s.DialogContent})),n.d(t,"d",(function(){return s.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},function(e,t,n){"use strict";var a=n(0),r=n(61);t.a=function(){return Object(a.useContext)(r.b)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a="core/modules",r="insufficient_module_dependencies"},function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(146),g=n(0),m=n(2),f=n(126),p=n(127),b=n(128),v=n(70),h=n(76),y=Object(g.forwardRef)((function(t,n){var a,i=t["aria-label"],c=t.secondary,l=void 0!==c&&c,u=t.arrow,g=void 0!==u&&u,y=t.back,O=void 0!==y&&y,_=t.caps,E=void 0!==_&&_,k=t.children,S=t.className,j=void 0===S?"":S,T=t.danger,A=void 0!==T&&T,N=t.disabled,C=void 0!==N&&N,w=t.external,I=void 0!==w&&w,M=t.hideExternalIndicator,R=void 0!==M&&M,D=t.href,x=void 0===D?"":D,L=t.inverse,P=void 0!==L&&L,G=t.noFlex,Z=void 0!==G&&G,B=t.onClick,U=t.small,F=void 0!==U&&U,z=t.standalone,V=void 0!==z&&z,H=t.linkButton,W=void 0!==H&&H,q=t.to,K=t.leadingIcon,Y=t.trailingIcon,$=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),X=x||q||!B?q?"ROUTER_LINK":I?"EXTERNAL_LINK":"LINK":C?"BUTTON_DISABLED":"BUTTON",J="BUTTON"===X||"BUTTON_DISABLED"===X?"button":"ROUTER_LINK"===X?d.b:"a",Q=("EXTERNAL_LINK"===X&&(a=Object(m._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===X&&(a=Object(m._x)("(disabled)","screen reader text","google-site-kit")),a?i?"".concat(i," ").concat(a):"string"==typeof k?"".concat(k," ").concat(a):void 0:i),ee=K,te=Y;return O&&(ee=e.createElement(b.a,{width:14,height:14})),I&&!R&&(te=e.createElement(v.a,{width:14,height:14})),g&&!P&&(te=e.createElement(f.a,{width:14,height:14})),g&&P&&(te=e.createElement(p.a,{width:14,height:14})),e.createElement(J,r()({"aria-label":Q,className:s()("googlesitekit-cta-link",j,{"googlesitekit-cta-link--secondary":l,"googlesitekit-cta-link--inverse":P,"googlesitekit-cta-link--small":F,"googlesitekit-cta-link--caps":E,"googlesitekit-cta-link--danger":A,"googlesitekit-cta-link--disabled":C,"googlesitekit-cta-link--standalone":V,"googlesitekit-cta-link--link-button":W,"googlesitekit-cta-link--no-flex":!!Z}),disabled:C,href:"LINK"!==X&&"EXTERNAL_LINK"!==X||C?void 0:x,onClick:B,rel:"EXTERNAL_LINK"===X?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===X?"_blank":void 0,to:q},$),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},k),!!te&&e.createElement(h.a,{marginLeft:5},te))}));y.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=y}).call(this,n(4))},,function(e,t,n){"use strict";n.d(t,"n",(function(){return a})),n.d(t,"l",(function(){return r})),n.d(t,"o",(function(){return i})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return s})),n.d(t,"s",(function(){return l})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return g})),n.d(t,"k",(function(){return m})),n.d(t,"u",(function(){return f})),n.d(t,"v",(function(){return p})),n.d(t,"q",(function(){return b})),n.d(t,"p",(function(){return v})),n.d(t,"b",(function(){return h})),n.d(t,"e",(function(){return y})),n.d(t,"a",(function(){return O})),n.d(t,"d",(function(){return _})),n.d(t,"c",(function(){return E})),n.d(t,"f",(function(){return k})),n.d(t,"g",(function(){return S}));var a="mainDashboard",r="entityDashboard",i="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",s="activation",l="splash",u="adminBar",d="adminBarViewOnly",g="settings",m="adBlockingRecovery",f="wpDashboard",p="wpDashboardViewOnly",b="moduleSetup",v="metricSelection",h="key-metrics",y="traffic",O="content",_="speed",E="monetization",k=[a,r,i,o,c,l,g,b,v],S=[i,o,d,p]},function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return r}));var a="core/ui",r="activeContextID"},function(e,t,n){"use strict";n.d(t,"d",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return s}));var a=n(79),r="xlarge",i="desktop",o="tablet",c="small";function s(){var e=Object(a.a)();return e>1280?r:e>960?i:e>600?o:c}},,function(e,t,n){"use strict";n.d(t,"l",(function(){return r})),n.d(t,"k",(function(){return i})),n.d(t,"j",(function(){return o})),n.d(t,"i",(function(){return c})),n.d(t,"a",(function(){return s})),n.d(t,"o",(function(){return l})),n.d(t,"n",(function(){return u})),n.d(t,"m",(function(){return d})),n.d(t,"c",(function(){return g})),n.d(t,"g",(function(){return m})),n.d(t,"h",(function(){return f})),n.d(t,"d",(function(){return p})),n.d(t,"e",(function(){return b})),n.d(t,"f",(function(){return v})),n.d(t,"b",(function(){return h}));var a=n(2),r="key-metrics-setup-cta-widget",i="googlesitekit-key-metrics-selection-panel-opened",o="key-metrics-selection-form",c="key-metrics-selected",s="key-metrics-effective-selection",l="key-metrics-unstaged-selection",u=2,d=8,g={SLUG:"current-selection",LABEL:Object(a.__)("Current selection","google-site-kit")},m={SLUG:"suggested",LABEL:Object(a.__)("Suggested","google-site-kit")},f={SLUG:"visitors",LABEL:Object(a.__)("Visitors","google-site-kit")},p={SLUG:"driving-traffic",LABEL:Object(a.__)("Driving traffic","google-site-kit")},b={SLUG:"generating-leads",LABEL:Object(a.__)("Generating leads","google-site-kit")},v={SLUG:"selling-products",LABEL:Object(a.__)("Selling products","google-site-kit")},h={SLUG:"content-performance",LABEL:Object(a.__)("Content performance","google-site-kit")}},,,function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var a="core/forms"},,function(e,t,n){"use strict";n.d(t,"l",(function(){return a})),n.d(t,"i",(function(){return r})),n.d(t,"f",(function(){return i})),n.d(t,"e",(function(){return o})),n.d(t,"g",(function(){return c})),n.d(t,"d",(function(){return s})),n.d(t,"h",(function(){return l})),n.d(t,"a",(function(){return u})),n.d(t,"c",(function(){return d})),n.d(t,"b",(function(){return g})),n.d(t,"j",(function(){return m})),n.d(t,"k",(function(){return f}));var a="modules/adsense",r=1,i="READY",o="NEEDS_ATTENTION",c="REQUIRES_REVIEW",s="GETTING_READY",l="background-submit-suspended",u="adsenseAdBlockingFormSettings",d="googlesitekit-ad-blocking-recovery-setup-create-message-cta-clicked",g="ad-blocking-recovery-notification",m={TAG_PLACED:"tag-placed",SETUP_CONFIRMED:"setup-confirmed"},f={PLACE_TAGS:0,CREATE_MESSAGE:1,COMPLETE:2}},function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var a="core/location"},,function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var a=n(22),r=n(18);function i(){var e=Object(r.a)();return a.g.includes(e)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return d}));n(14);var a=n(2),r="missing_required_scopes",i="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===r}function s(e){var t;return[i,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function l(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||s(e)||c(e)||l(e))}function d(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(a.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(a.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return O})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return y}));var a=n(99),r=e._googlesitekitTrackingData||{},i=r.activeModules,o=void 0===i?[]:i,c=r.isSiteKitScreen,s=r.trackingEnabled,l=r.trackingID,u=r.referenceSiteURL,d=r.userIDHash,g=r.isAuthenticated,m={activeModules:o,trackingEnabled:s,trackingID:l,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:r.userRoles,isAuthenticated:g,pluginVersion:"1.151.0"},f=Object(a.a)(m),p=f.enableTracking,b=f.disableTracking,v=(f.isTrackingEnabled,f.initializeSnippet),h=f.trackEvent,y=f.trackEventOnce;function O(e){e?p():b()}c&&s&&v()}).call(this,n(28))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return y})),n.d(t,"c",(function(){return O})),n.d(t,"e",(function(){return _})),n.d(t,"b",(function(){return E}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,r=function(){};return{s:r,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var u,d="googlesitekit_",g="".concat(d).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),m=["sessionStorage","localStorage"],f=[].concat(m),p=function(){var t=o()(r.a.mark((function t(n){var a,i;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(a=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,i="__storage_test__",a.setItem(i,i),a.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==a.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function b(){return v.apply(this,arguments)}function v(){return(v=o()(r.a.mark((function t(){var n,a,i;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=s(f),t.prev=3,n.s();case 5:if((a=n.n()).done){t.next=15;break}if(i=a.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,p(i);case 11:if(!t.sent){t.next=13;break}u=e[i];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=o()(r.a.mark((function e(t){var n,a,i,o,c,s,l;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!(n=e.sent)){e.next=10;break}if(!(a=n.getItem("".concat(g).concat(t)))){e.next=10;break}if(i=JSON.parse(a),o=i.timestamp,c=i.ttl,s=i.value,l=i.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:l});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),y=function(){var t=o()(r.a.mark((function t(n,a){var i,o,s,l,u,d,m,f,p=arguments;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=p.length>2&&void 0!==p[2]?p[2]:{},o=i.ttl,s=void 0===o?c.b:o,l=i.timestamp,u=void 0===l?Math.round(Date.now()/1e3):l,d=i.isError,m=void 0!==d&&d,t.next=3,b();case 3:if(!(f=t.sent)){t.next=14;break}return t.prev=5,f.setItem("".concat(g).concat(n),JSON.stringify({timestamp:u,ttl:s,value:a,isError:m})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),O=function(){var t=o()(r.a.mark((function t(n){var a,i;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(a=t.sent)){t.next=14;break}return t.prev=4,i=n.startsWith(d)?n:"".concat(g).concat(n),a.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),_=function(){var t=o()(r.a.mark((function t(){var n,a,i,o;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,a=[],i=0;i<n.length;i++)0===(o=n.key(i)).indexOf(d)&&a.push(o);return t.abrupt("return",a);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),E=function(){var e=o()(r.a.mark((function e(){var t,n,a,i;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!e.sent){e.next=25;break}return e.next=6,_();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((a=n.n()).done){e.next=16;break}return i=a.value,e.next=14,O(i);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},,function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a="_googlesitekitDataLayer",r="data-googlesitekit-gtag"},,function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var a=n(22),r="core/notifications",i={ERRORS:"notification-area-errors",BANNERS_ABOVE_NAV:"notification-area-banners-above-nav",BANNERS_BELOW_NAV:"notification-area-banners-below-nav"},o={DEFAULT:"default",SETUP_CTAS:"setup-ctas"},c=[a.s,a.n,a.l,a.o,a.m]},,,function(e,t,n){"use strict";(function(e){var a=n(6),r=n.n(a),i=n(1),o=n.n(i),c=n(11),s=n.n(c),l=n(24);function PreviewBlock(t){var n,a,i=t.className,o=t.width,c=t.height,u=t.shape,d=t.padding,g=t.smallWidth,m=t.smallHeight,f=t.tabletWidth,p=t.tabletHeight,b=t.desktopWidth,v=t.desktopHeight,h=Object(l.e)(),y={width:(n={},r()(n,l.b,g),r()(n,l.c,f),r()(n,l.a,b),r()(n,l.d,b),n),height:(a={},r()(a,l.b,m),r()(a,l.c,p),r()(a,l.a,v),r()(a,l.d,b),a)};return e.createElement("div",{className:s()("googlesitekit-preview-block",i,{"googlesitekit-preview-block--padding":d}),style:{width:y.width[h]||o,height:y.height[h]||c}},e.createElement("div",{className:s()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(4))},function(e,t){e.exports=googlesitekit.api},,function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i}));var a={BOXES:"boxes",COMPOSITE:"composite"},r={QUARTER:"quarter",HALF:"half",FULL:"full"},i="core/widgets"},function(e,t,n){"use strict";n.d(t,"a",(function(){return O}));var a=n(5),r=n.n(a),i=n(6),o=n.n(i),c=n(12),s=n.n(c),l=n(14),u=n(64),d=n(82),g=n(9);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var p=function(e){return e},b=function(){return{}},v=function(){},h=u.a.clearError,y=u.a.receiveError,O=function(e){var t,n,a=r.a.mark(x),i=e.baseName,c=e.controlCallback,u=e.reducerCallback,m=void 0===u?p:u,O=e.argsToParams,_=void 0===O?b:O,E=e.validateParams,k=void 0===E?v:E;s()(i,"baseName is required."),s()("function"==typeof c,"controlCallback is required and must be a function."),s()("function"==typeof m,"reducerCallback must be a function."),s()("function"==typeof _,"argsToParams must be a function."),s()("function"==typeof k,"validateParams must be a function.");try{k(_()),n=!1}catch(e){n=!0}var S=Object(d.b)(i),j=Object(d.a)(i),T="FETCH_".concat(j),A="START_".concat(T),N="FINISH_".concat(T),C="CATCH_".concat(T),w="RECEIVE_".concat(j),I="fetch".concat(S),M="receive".concat(S),R="isFetching".concat(S),D=o()({},R,{});function x(e,t){var n,o;return r.a.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,{payload:{params:e},type:A};case 2:return a.next=4,h(i,t);case 4:return a.prev=4,a.next=7,{payload:{params:e},type:T};case 7:return n=a.sent,a.next=10,L[M](n,e);case 10:return a.next=12,{payload:{params:e},type:N};case 12:a.next=21;break;case 14:return a.prev=14,a.t0=a.catch(4),o=a.t0,a.next=19,y(o,i,t);case 19:return a.next=21,{payload:{params:e},type:C};case 21:return a.abrupt("return",{response:n,error:o});case 22:case"end":return a.stop()}}),a,null,[[4,14]])}var L=(t={},o()(t,I,(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=_.apply(void 0,t);return k(a),x(a,t)})),o()(t,M,(function(e,t){return s()(void 0!==e,"response is required."),n?(s()(Object(l.isPlainObject)(t),"params is required."),k(t)):t={},{payload:{response:e,params:t},type:w}})),t),P=o()({},T,(function(e){var t=e.payload;return c(t.params)})),G=o()({},R,(function(e){if(void 0===e[R])return!1;var t;try{for(var n=arguments.length,a=new Array(n>1?n-1:0),r=1;r<n;r++)a[r-1]=arguments[r];t=_.apply(void 0,a),k(t)}catch(e){return!1}return!!e[R][Object(g.H)(t)]}));return{initialState:D,actions:L,controls:P,reducer:function(e,t){var n=t.type,a=t.payload;switch(n){case A:var r=a.params;return f(f({},e),{},o()({},R,f(f({},e[R]),{},o()({},Object(g.H)(r),!0))));case w:var i=a.response,c=a.params;return m(e,i,c);case N:var s=a.params;return f(f({},e),{},o()({},R,f(f({},e[R]),{},o()({},Object(g.H)(s),!1))));case C:var l=a.params;return f(f({},e),{},o()({},R,f(f({},e[R]),{},o()({},Object(g.H)(l),!1))));default:return e}},resolvers:{},selectors:G}}},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var a=n(3),r=n(19),i=n(82);function o(t){var n=t.moduleName,o=t.FallbackComponent,c=t.IncompleteComponent;return function(t){function WhenActiveComponent(i){var s=Object(a.useSelect)((function(e){return e(r.a).getModule(n)}),[n]);if(!s)return null;var l=o||i.WidgetNull||null;if(!1===s.active)return l&&e.createElement(l,i);if(!1===s.connected){var u=c||l;return u&&e.createElement(u,i)}return e.createElement(t,i)}return WhenActiveComponent.displayName="When".concat(Object(i.c)(n),"Active"),(t.displayName||t.name)&&(WhenActiveComponent.displayName+="(".concat(t.displayName||t.name,")")),WhenActiveComponent}}}).call(this,n(4))},,function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return c}));var a=n(22),r=n(18),i=a.n,o=a.l;function c(){var e=Object(r.a)();return e===a.n||e===a.o?i:e===a.l||e===a.m?o:null}},,function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,a=t.reconnectURL,r=t.noPrefix;if(!n)return null;var s=n;void 0!==r&&r||(s=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),a&&Object(i.a)(a)&&(s=s+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),a));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(s,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:r.a.string.isRequired,reconnectURL:r.a.string,noPrefix:r.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},,,function(e,t,n){"use strict";(function(e){var a,r;n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var i=new Set((null===(a=e)||void 0===a||null===(r=a._googlesitekitBaseData)||void 0===r?void 0:r.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;return t instanceof Set&&t.has(e)}}).call(this,n(28))},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=a.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return a.createElement("svg",r({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),i,o)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var a=n(39);function r(e){return function(){e[a.a]=e[a.a]||[],e[a.a].push(arguments)}}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var a=n(0),r=Object(a.createContext)(""),i=(r.Consumer,r.Provider);t.b=r},function(e,t,n){"use strict";n.d(t,"a",(function(){return T})),n.d(t,"b",(function(){return A})),n.d(t,"c",(function(){return N})),n.d(t,"d",(function(){return w})),n.d(t,"e",(function(){return I})),n.d(t,"g",(function(){return R})),n.d(t,"f",(function(){return D}));var a,r=n(5),i=n.n(r),o=n(27),c=n.n(o),s=n(6),l=n.n(s),u=n(12),d=n.n(u),g=n(63),m=n.n(g),f=n(14),p=n(116);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=t.reduce((function(e,t){return v(v({},e),t)}),{}),r=t.reduce((function(e,t){return[].concat(c()(e),c()(Object.keys(t)))}),[]),i=C(r);return d()(0===i.length,"collect() cannot accept collections with duplicate keys. Your call to collect() contains the following duplicated functions: ".concat(i.join(", "),". Check your data stores for duplicates.")),a},y=h,O=h,_=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a,r=[].concat(t);return"function"!=typeof r[0]&&(a=r.shift()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return r.reduce((function(e,n){return n(e,t)}),e)}},E=h,k=h,S=h,j=function(e){return e},T=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=S.apply(void 0,c()(t.map((function(e){return e.initialState||{}}))));return{initialState:a,controls:O.apply(void 0,c()(t.map((function(e){return e.controls||{}})))),actions:y.apply(void 0,c()(t.map((function(e){return e.actions||{}})))),reducer:_.apply(void 0,[a].concat(c()(t.map((function(e){return e.reducer||j}))))),resolvers:E.apply(void 0,c()(t.map((function(e){return e.resolvers||{}})))),selectors:k.apply(void 0,c()(t.map((function(e){return e.selectors||{}}))))}},A={getRegistry:function(){return{payload:{},type:"GET_REGISTRY"}},await:i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{payload:{value:t},type:"AWAIT"});case 1:case"end":return e.stop()}}),e)}))},N=(a={},l()(a,"GET_REGISTRY",Object(p.a)((function(e){return function(){return e}}))),l()(a,"AWAIT",(function(e){return e.payload.value})),a),C=function(e){for(var t=[],n={},a=0;a<e.length;a++){var r=e[a];n[r]=n[r]>=1?n[r]+1:1,n[r]>1&&t.push(r)}return t},w={actions:A,controls:N,reducer:j},I=function(e){return function(t){return M(e(t))}},M=m()((function(e){return Object(f.mapValues)(e,(function(e,t){return function(){var n=e.apply(void 0,arguments);return d()(void 0!==n,"".concat(t,"(...) is not resolved")),n}}))}));function R(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.negate,a=void 0!==n&&n,r=Object(p.b)((function(t){return function(n){var r=!a,i=!!a;try{for(var o=arguments.length,c=new Array(o>1?o-1:0),s=1;s<o;s++)c[s-1]=arguments[s];return e.apply(void 0,[t,n].concat(c)),r}catch(e){return i}}})),i=Object(p.b)((function(t){return function(n){for(var a=arguments.length,r=new Array(a>1?a-1:0),i=1;i<a;i++)r[i-1]=arguments[i];e.apply(void 0,[t,n].concat(r))}}));return{safeSelector:r,dangerousSelector:i}}function D(e,t){return d()("function"==typeof e,"a validator function is required."),d()("function"==typeof t,"an action creator function is required."),d()("Generator"!==e[Symbol.toStringTag]&&"GeneratorFunction"!==e[Symbol.toStringTag],"an action’s validator function must not be a generator."),function(){return e.apply(void 0,arguments),t.apply(void 0,arguments)}}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return v}));var a=n(6),r=n.n(a),i=n(33),o=n.n(i),c=n(116),s=n(12),l=n.n(s),u=n(96),d=n.n(u),g=n(9);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t){if(t&&Array.isArray(t)){var n=t.map((function(e){return"object"===o()(e)?Object(g.H)(e):e}));return"".concat(e,"::").concat(d()(JSON.stringify(n)))}return e}var b={receiveError:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(e,"error is required."),l()(t,"baseName is required."),l()(n&&Array.isArray(n),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:n}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return l()(e,"baseName is required."),l()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function v(e){l()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,n){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(n,"selectorName is required."),t.getError(e,n,a)},getErrorForAction:function(e,n){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(n,"actionName is required."),t.getError(e,n,a)},getError:function(e,t,n){var a=e.errors;return l()(t,"baseName is required."),a[p(t,n)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var n=Object.keys(e.errors).find((function(n){return e.errors[n]===t}));return n?{baseName:n.substring(0,n.indexOf("::")),args:e.errorArgs[n]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(n,a){var r=t(e).getMetaDataForError(a);if(r){var i=r.baseName,o=r.args;if(!!t(e)[i])return{storeName:e,name:i,args:o}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:b,controls:{},reducer:function(e,t){var n=t.type,a=t.payload;switch(n){case"RECEIVE_ERROR":var i=a.baseName,o=a.args,c=a.error,s=p(i,o);return f(f({},e),{},{errors:f(f({},e.errors||{}),{},r()({},s,c)),errorArgs:f(f({},e.errorArgs||{}),{},r()({},s,o))});case"CLEAR_ERROR":var l=a.baseName,u=a.args,d=f({},e),g=p(l,u);return d.errors=f({},e.errors||{}),d.errorArgs=f({},e.errorArgs||{}),delete d.errors[g],delete d.errorArgs[g],d;case"CLEAR_ERRORS":var m=a.baseName,b=f({},e);if(m)for(var v in b.errors=f({},e.errors||{}),b.errorArgs=f({},e.errorArgs||{}),b.errors)(v===m||v.startsWith("".concat(m,"::")))&&(delete b.errors[v],delete b.errorArgs[v]);else b.errors={},b.errorArgs={};return b;default:return e}},resolvers:{},selectors:t}}},function(e,t,n){"use strict";n.d(t,"c",(function(){return p})),n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return v})),n.d(t,"d",(function(){return y}));var a=n(6),r=n.n(a),i=n(0);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var c=i.createElement("path",{d:"M10 15.27L16.18 19l-1.64-7.03L20 7.24l-7.19-.61L10 0 7.19 6.63 0 7.24l5.46 4.73L3.82 19 10 15.27z"});var s=function SvgInfoIcon(e){return i.createElement("svg",o({viewBox:"0 0 20 20",fill:"currentColor"},e),c)};function l(){return(l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var u=i.createElement("path",{d:"M0 4h2v7H0zm0-4h2v2H0z",fill:"currentColor",fillRule:"evenodd"});var d,g=function SvgSuggestionIcon(e){return i.createElement("svg",l({viewBox:"0 0 2 11"},e),u)},m=n(186),f=n(74),p="warning",b="info",v="suggestion",h=(d={},r()(d,b,s),r()(d,p,m.a),r()(d,v,g),d),y=function(e){return h[e]||f.a}},function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return r}));var a="modules/search-console",r=1},function(e,t,n){"use strict";n.d(t,"b",(function(){return g})),n.d(t,"e",(function(){return m})),n.d(t,"f",(function(){return f})),n.d(t,"g",(function(){return p})),n.d(t,"i",(function(){return b})),n.d(t,"h",(function(){return v})),n.d(t,"d",(function(){return h})),n.d(t,"c",(function(){return y})),n.d(t,"l",(function(){return O})),n.d(t,"k",(function(){return _})),n.d(t,"j",(function(){return E}));var a=n(12),r=n.n(a),i=n(14),o=n(8),c=n(9);n.d(t,"a",(function(){return c.x}));var s=n(178),l=n(275),u=n(122),d=n(342);function g(e){return e===o.a||Object(c.x)(e)}function m(e){return"string"==typeof e&&/^\d+$/.test(e)}function f(e){return e===o.s||m(e)}function p(e){return"string"==typeof e&&/^\d+$/.test(e)}function b(e){return e===o.z||p(e)}function v(e){return"string"==typeof e&&e.trim().length>0}function h(e){return"string"==typeof e&&/^G-[a-zA-Z0-9]+$/.test(e)}function y(e){return"string"==typeof e&&/^(G|GT|AW)-[a-zA-Z0-9]+$/.test(e)}function O(e){r()(Object(i.isPlainObject)(e),"options for Analytics 4 report must be an object."),r()(Object(s.a)(e),"Either date range or start/end dates must be provided for Analytics 4 report.");var t=Object(l.a)(e),n=t.metrics,a=t.dimensions,o=t.dimensionFilters,c=t.metricFilters,d=t.orderby;r()(n.length,"Requests must specify at least one metric for an Analytics 4 report."),r()(Object(u.d)(n),'metrics for an Analytics 4 report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property. Metric names must match the expression ^[a-zA-Z0-9_]+$.'),a&&r()(Object(u.b)(a),'dimensions for an Analytics 4 report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property.'),o&&r()(Object(u.a)(o),"dimensionFilters for an Analytics 4 report must be a map of dimension names as keys and dimension values as values."),c&&r()(Object(u.c)(c),"metricFilters for an Analytics 4 report must be a map of metric names as keys and filter value(s) as numeric fields, depending on the filterType."),d&&r()(Object(u.e)(d),'orderby for an Analytics 4 report must be an array of OrderBy objects where each object should have either a "metric" or "dimension" property, and an optional "desc" property.')}function _(e){r()(Object(i.isPlainObject)(e),"options for Analytics 4 pivot report must be an object."),r()(Object(s.a)(e),"Start/end dates must be provided for Analytics 4 pivot report.");var t=Object(l.a)(e),n=t.metrics,a=t.dimensions,o=t.dimensionFilters,c=t.metricFilters,g=t.pivots,m=t.orderby,f=t.limit;r()(n.length,"Requests must specify at least one metric for an Analytics 4 pivot report."),r()(Object(u.d)(n),'metrics for an Analytics 4 pivot report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property. Metric names must match the expression ^[a-zA-Z0-9_]+$.'),r()(Object(d.a)(g),'pivots for an Analytics 4 pivot report must be an array of objects. Each object must have a "fieldNames" property and a "limit".'),m&&r()(Array.isArray(m),"orderby for an Analytics 4 pivot report must be passed within a pivot."),f&&r()("number"==typeof f,"limit for an Analytics 4 pivot report must be passed within a pivot."),a&&r()(Object(u.b)(a),'dimensions for an Analytics 4 pivot report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property.'),o&&r()(Object(u.a)(o),"dimensionFilters for an Analytics 4 pivot report must be a map of dimension names as keys and dimension values as values."),c&&r()(Object(u.c)(c),"metricFilters for an Analytics 4 pivot report must be a map of metric names as keys and filter value(s) as numeric fields, depending on the filterType.")}function E(e){var t=["displayName","description","membershipDurationDays","eventTrigger","exclusionDurationMode","filterClauses"];r()(Object(i.isPlainObject)(e),"Audience must be an object."),Object.keys(e).forEach((function(e){r()(t.includes(e),'Audience object must contain only valid keys. Invalid key: "'.concat(e,'"'))})),["displayName","description","membershipDurationDays","filterClauses"].forEach((function(t){r()(e[t],'Audience object must contain required keys. Missing key: "'.concat(t,'"'))})),r()(Object(i.isArray)(e.filterClauses),"filterClauses must be an array with AudienceFilterClause objects.")}},,,function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return a.createElement("svg",r({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},function(e,t,n){"use strict";n.r(t),n.d(t,"CONTEXT_MAIN_DASHBOARD_KEY_METRICS",(function(){return a})),n.d(t,"CONTEXT_MAIN_DASHBOARD_TRAFFIC",(function(){return r})),n.d(t,"CONTEXT_MAIN_DASHBOARD_CONTENT",(function(){return i})),n.d(t,"CONTEXT_MAIN_DASHBOARD_SPEED",(function(){return o})),n.d(t,"CONTEXT_MAIN_DASHBOARD_MONETIZATION",(function(){return c})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_TRAFFIC",(function(){return s})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_CONTENT",(function(){return l})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_SPEED",(function(){return u})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_MONETIZATION",(function(){return d}));var a="mainDashboardKeyMetrics",r="mainDashboardTraffic",i="mainDashboardContent",o="mainDashboardSpeed",c="mainDashboardMonetization",s="entityDashboardTraffic",l="entityDashboardContent",u="entityDashboardSpeed",d="entityDashboardMonetization";t.default={CONTEXT_MAIN_DASHBOARD_KEY_METRICS:a,CONTEXT_MAIN_DASHBOARD_TRAFFIC:r,CONTEXT_MAIN_DASHBOARD_CONTENT:i,CONTEXT_MAIN_DASHBOARD_SPEED:o,CONTEXT_MAIN_DASHBOARD_MONETIZATION:c,CONTEXT_ENTITY_DASHBOARD_TRAFFIC:s,CONTEXT_ENTITY_DASHBOARD_CONTENT:l,CONTEXT_ENTITY_DASHBOARD_SPEED:u,CONTEXT_ENTITY_DASHBOARD_MONETIZATION:d}},function(e,t,n){"use strict";var a=n(15),r=n.n(a),i=n(265),o=n(1),c=n.n(o),s=n(0),l=n(144);function Portal(e){var t=e.children,n=e.slug,a=Object(s.useState)(document.createElement("div")),o=r()(a,1)[0];return Object(i.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(l.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var a=n(0),r=n(18),i=n(9);function o(e,t){var n=Object(r.a)(),o=null!=t?t:"".concat(n,"_").concat(e);return{view:Object(a.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.I.apply(void 0,[o,"view_notification"].concat(t))}),[o]),confirm:Object(a.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.I.apply(void 0,[o,"confirm_notification"].concat(t))}),[o]),dismiss:Object(a.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.I.apply(void 0,[o,"dismiss_notification"].concat(t))}),[o]),clickLearnMore:Object(a.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.I.apply(void 0,[o,"click_learn_more_link"].concat(t))}),[o])}}},function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))},function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var a=n(33),r=n.n(a),i=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:i.a.sanitize(e,t)}};function c(e){var t,n="object"===r()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var a=n(1),r=n.n(a);function IconWrapper(t){var n=t.children,a=t.marginLeft,r=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:a,marginRight:r}},n)}IconWrapper.propTypes={children:r.a.node.isRequired,marginLeft:r.a.number,marginRight:r.a.number}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(0),g=Object(d.forwardRef)((function(t,n){var a=t.label,i=t.className,c=t.hasLeftSpacing,l=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",r()({ref:n},u,{className:s()("googlesitekit-badge",i,{"googlesitekit-badge--has-left-spacing":l})}),a)}));g.displayName="Badge",g.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=g}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var a=n(15),r=n.n(a),i=n(188),o=n(133),c={},s=void 0===e?null:e,l=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,a=e.initialWidth,u=void 0===a?0:a,d=e.initialHeight,g=void 0===d?0:d,m=Object(i.a)("undefined"==typeof document?[u,g]:l,t,n),f=r()(m,2),p=f[0],b=f[1],v=function(){return b(l)};return Object(o.a)(s,"resize",v),Object(o.a)(s,"orientationchange",v),p},d=function(e){return u(e)[0]}}).call(this,n(28))},function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var a=n(106);function r(e){try{return new URL(e).pathname}catch(e){}return null}function i(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(a.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),r=e.replace(n.origin,"");if(r.length<t)return r;var i=r.length-Math.floor(t)+1;return"…"+r.substr(i)}},,function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return r})),n.d(t,"c",(function(){return i}));var a=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},r=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function i(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return k})),n.d(t,"d",(function(){return S})),n.d(t,"e",(function(){return T})),n.d(t,"c",(function(){return A})),n.d(t,"b",(function(){return N}));var a=n(15),r=n.n(a),i=n(33),o=n.n(i),c=n(6),s=n.n(c),l=n(25),u=n.n(l),d=n(14),g=n(63),m=n.n(g),f=n(2);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=O(e,t),a=n.formatUnit,r=n.formatDecimal;try{return a()}catch(e){return r()}},h=function(e){var t=y(e),n=t.hours,a=t.minutes,r=t.seconds;return r=("0"+r).slice(-2),a=("0"+a).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(a,":").concat(r):"".concat(n,":").concat(a,":").concat(r)},y=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},O=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=y(e),a=n.hours,r=n.minutes,i=n.seconds;return{hours:a,minutes:r,seconds:i,formatUnit:function(){var n=t.unitDisplay,o=b(b({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?T(i,b(b({},o),{},{unit:"second"})):Object(f.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(f._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?T(i,b(b({},o),{},{unit:"second"})):"",r?T(r,b(b({},o),{},{unit:"minute"})):"",a?T(a,b(b({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(f.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(f.__)("%ds","google-site-kit"),i);if(0===e)return t;var n=Object(f.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(f.__)("%dm","google-site-kit"),r),o=Object(f.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(f.__)("%dh","google-site-kit"),a);return Object(f.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(f._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?t:"",r?n:"",a?o:"").trim()}}},_=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},E=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(f.sprintf)(// translators: %s: an abbreviated number in millions.
Object(f.__)("%sM","google-site-kit"),T(_(e),e%10==0?{}:t)):1e4<=e?Object(f.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(f.__)("%sK","google-site-kit"),T(_(e))):1e3<=e?Object(f.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(f.__)("%sK","google-site-kit"),T(_(e),e%10==0?{}:t)):T(e,{signDisplay:"never",maximumFractionDigits:1})};function k(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=b({},e)),t}function S(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=k(t),a=n.style,r=void 0===a?"metric":a;return"metric"===r?E(e):"duration"===r?v(e,n):"durationISO"===r?h(e):T(e,n)}var j=m()(console.warn),T=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,a=void 0===n?N():n,i=u()(t,["locale"]);try{return new Intl.NumberFormat(a,i).format(e)}catch(t){j("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(a),", ").concat(JSON.stringify(i)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],l={},d=0,g=Object.entries(i);d<g.length;d++){var m=r()(g[d],2),f=m[0],p=m[1];c[f]&&p===c[f]||(s.includes(f)||(l[f]=p))}try{return new Intl.NumberFormat(a,l).format(e)}catch(t){return new Intl.NumberFormat(a).format(e)}},A=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,a=void 0===n?N():n,r=t.style,i=void 0===r?"long":r,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(a,{style:i,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var l=Object(f.__)(", ","google-site-kit");return e.join(l)},N=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var a=n.match(/^(\w{2})?(_)?(\w{2})/);if(a&&a[0])return a[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return r}));var a=n(149),r=n.n(a)()(e)}).call(this,n(28))},function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i);function ChangeArrow(t){var n=t.direction,a=t.invertColor,r=t.width,i=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":a}),width:r,height:i,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:r.a.string,invertColor:r.a.bool,width:r.a.number,height:r.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},,,function(e,t,n){"use strict";n.r(t),n.d(t,"AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY",(function(){return a})),n.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY",(function(){return r})),n.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION",(function(){return i})),n.d(t,"AREA_MAIN_DASHBOARD_CONTENT_PRIMARY",(function(){return o})),n.d(t,"AREA_MAIN_DASHBOARD_SPEED_PRIMARY",(function(){return c})),n.d(t,"AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY",(function(){return s})),n.d(t,"AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY",(function(){return l})),n.d(t,"AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY",(function(){return u})),n.d(t,"AREA_ENTITY_DASHBOARD_SPEED_PRIMARY",(function(){return d})),n.d(t,"AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY",(function(){return g}));var a="mainDashboardKeyMetricsPrimary",r="mainDashboardTrafficPrimary",i="mainDashboardTrafficAudienceSegmentation",o="mainDashboardContentPrimary",c="mainDashboardSpeedPrimary",s="mainDashboardMonetizationPrimary",l="entityDashboardTrafficPrimary",u="entityDashboardContentPrimary",d="entityDashboardSpeedPrimary",g="entityDashboardMonetizationPrimary";t.default={AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY:a,AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY:r,AREA_MAIN_DASHBOARD_CONTENT_PRIMARY:o,AREA_MAIN_DASHBOARD_SPEED_PRIMARY:c,AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY:s,AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY:l,AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY:u,AREA_ENTITY_DASHBOARD_SPEED_PRIMARY:d,AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY:g}},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var a=n(12),r=n.n(a),i=function(e,t){var n=t.dateRangeLength;r()(Array.isArray(e),"report must be an array to partition."),r()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var a=-1*n;return{currentRange:e.slice(a),compareRange:e.slice(2*a,a)}}},function(e,t,n){"use strict";(function(e,a){n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return O})),n.d(t,"a",(function(){return TourTooltips}));var r=n(6),i=n.n(r),o=n(81),c=n(30),s=n(1),l=n.n(s),u=n(2),d=n(3),g=n(23),m=n(7),f=n(36),p=n(107),b=n(18);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}var h={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},y={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},O={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},_="feature_tooltip_view",E="feature_tooltip_advance",k="feature_tooltip_return",S="feature_tooltip_dismiss",j="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,r=t.tourID,s=t.gaEventCategory,l=t.callback,u="".concat(r,"-step"),T="".concat(r,"-run"),A=Object(d.useDispatch)(g.b).setValue,N=Object(d.useDispatch)(m.a).dismissTour,C=Object(d.useRegistry)(),w=Object(b.a)(),I=Object(d.useSelect)((function(e){return e(g.b).getValue(u)})),M=Object(d.useSelect)((function(e){return e(g.b).getValue(T)&&!1===e(m.a).isTourDismissed(r)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(r)),A(T,!0)}));var R=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return a.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,a=e.lifecycle,r=e.size,i=e.status,o=e.type,l=t+1,u="function"==typeof s?s(w):s;o===c.b.TOOLTIP&&a===c.c.TOOLTIP?Object(f.b)(u,_,l):n===c.a.CLOSE&&a===c.c.COMPLETE?Object(f.b)(u,S,l):n===c.a.NEXT&&i===c.d.FINISHED&&o===c.b.TOUR_END&&r===l&&Object(f.b)(u,j,l),a===c.c.COMPLETE&&i!==c.d.FINISHED&&(n===c.a.PREV&&Object(f.b)(u,k,l),n===c.a.NEXT&&Object(f.b)(u,E,l))}(t);var n=t.action,a=t.index,i=t.status,o=t.step,d=t.type,g=n===c.a.CLOSE,m=!g&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),p=[c.d.FINISHED,c.d.SKIPPED].includes(i),b=g&&d===c.b.STEP_AFTER,v=p||b;if(c.b.STEP_BEFORE===d){var h,y,O=o.target;"string"==typeof o.target&&(O=e.document.querySelector(o.target)),null===(h=O)||void 0===h||null===(y=h.scrollIntoView)||void 0===y||y.call(h,{block:"center"})}m?function(e,t){A(u,e+(t===c.a.PREV?-1:1))}(a,n):v&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(r)),N(r)),l&&l(t,C)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:O,locale:y,run:M,showProgress:!0,stepIndex:I,steps:R,styles:h,tooltipComponent:p.a})}TourTooltips.propTypes={steps:l.a.arrayOf(l.a.object).isRequired,tourID:l.a.string.isRequired,gaEventCategory:l.a.oneOfType([l.a.string,l.a.func]).isRequired,callback:l.a.func}}).call(this,n(28),n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Dismiss}));var a=n(5),r=n.n(a),i=n(6),o=n.n(i),c=n(16),s=n.n(c),l=n(1),u=n.n(l),d=n(2),g=n(3),m=n(73),f=n(41),p=n(10);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Dismiss(t){var n=t.id,a=t.primary,i=void 0===a||a,o=t.dismissLabel,c=void 0===o?Object(d.__)("OK, Got it!","google-site-kit"):o,l=t.dismissExpires,u=void 0===l?0:l,b=t.disabled,h=t.onDismiss,y=void 0===h?function(){}:h,O=t.gaTrackingEventArgs,_=t.dismissOptions,E=Object(m.a)(n,null==O?void 0:O.category),k=Object(g.useDispatch)(f.a).dismissNotification,S=function(){var e=s()(r.a.mark((function e(t){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==y?void 0:y(t);case 2:E.dismiss(null==O?void 0:O.label,null==O?void 0:O.value),k(n,v(v({},_),{},{expiresInSeconds:u}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(p.Button,{tertiary:!i,onClick:S,disabled:b},c)}Dismiss.propTypes={id:u.a.string,primary:u.a.bool,dismissLabel:u.a.string,dismissExpires:u.a.number,disabled:u.a.bool,onDismiss:u.a.func,gaTrackingEventArgs:u.a.shape({label:u.a.string,value:u.a.string})}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var a=n(24),r=n(130);function i(t,n){var a=document.querySelector(t);if(!a)return 0;var r=a.getBoundingClientRect().top,i=o(n);return r+e.scrollY-i}function o(e){var t=c(e),n=document.querySelectorAll(".googlesitekit-navigation, .googlesitekit-entity-header");return t+=Array.from(n).reduce((function(e,t){return e+t.offsetHeight}),0)}function c(t){var n=0,i=document.querySelector(".googlesitekit-header");return n=!!i&&"sticky"===e.getComputedStyle(i).position?function(e){var t=document.querySelector(".googlesitekit-header");if(t){if(e===a.b)return t.offsetHeight;var n=t.getBoundingClientRect().bottom;return n<0?0:n}return 0}(t):function(e){var t=document.querySelector("#wpadminbar");return t&&e!==a.b?t.offsetHeight:0}(t),(n=Object(r.a)(n))<0?0:n}}).call(this,n(28))},,function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(10),s=n(20);function CTA(t){var n=t.title,a=t.headerText,r=t.headerContent,i=t.description,l=t.ctaLink,u=t.ctaLabel,d=t.ctaLinkExternal,g=t.ctaType,m=t.error,f=t.onClick,p=t["aria-label"],b=t.children;return e.createElement("div",{className:o()("googlesitekit-cta",{"googlesitekit-cta--error":m})},(a||r)&&e.createElement("div",{className:"googlesitekit-cta__header"},a&&e.createElement("h2",{className:"googlesitekit-cta__header_text"},a),r),e.createElement("div",{className:"googlesitekit-cta__body"},n&&e.createElement("h3",{className:"googlesitekit-cta__title"},n),i&&"string"==typeof i&&e.createElement("p",{className:"googlesitekit-cta__description"},i),i&&"string"!=typeof i&&e.createElement("div",{className:"googlesitekit-cta__description"},i),u&&"button"===g&&e.createElement(c.Button,{"aria-label":p,href:l,onClick:f},u),u&&"link"===g&&e.createElement(s.a,{href:l,onClick:f,"aria-label":p,external:d,hideExternalIndicator:d,arrow:!0},u),b))}CTA.propTypes={title:r.a.string.isRequired,headerText:r.a.string,description:r.a.oneOfType([r.a.string,r.a.node]),ctaLink:r.a.string,ctaLinkExternal:r.a.bool,ctaLabel:r.a.string,ctaType:r.a.string,"aria-label":r.a.string,error:r.a.bool,onClick:r.a.func,children:r.a.node,headerContent:r.a.node},CTA.defaultProps={title:"",headerText:"",headerContent:"",description:"",ctaLink:"",ctaLabel:"",ctaType:"link",error:!1,onClick:function(){}},t.a=CTA}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o}));var a=n(239),r=n(85),i=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var i=n.invertColor,o=void 0!==i&&i;return Object(a.a)(e.createElement(r.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var a=n(6),r=n.n(a),i=n(14),o=n(100),c=n(101);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,r=l(l({},u),t);r.referenceSiteURL&&(r.referenceSiteURL=r.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(r,n),d=Object(c.a)(r,n,s,a),g={},m=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=JSON.stringify(t);g[a]||(g[a]=Object(i.once)(d)),g[a].apply(g,t)};return{enableTracking:function(){r.trackingEnabled=!0},disableTracking:function(){r.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!r.trackingEnabled},trackEvent:d,trackEventOnce:m}}}).call(this,n(28))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var a=n(59),r=n(39),i=n(57);function o(t,n){var o,c=Object(a.a)(n),s=t.activeModules,l=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,g=void 0===d?[]:d,m=t.isAuthenticated,f=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(r.b,"]"))),!o){o=!0;var a=(null==g?void 0:g.length)?g.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:l,plugin_version:f||"",enabled_features:Array.from(i.a).join(","),active_modules:s.join(","),authenticated:m?"1":"0",user_properties:{user_roles:a,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(r.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(r.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(r.a)}}}}}).call(this,n(28))},function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var a=n(5),r=n.n(a),i=n(6),o=n.n(i),c=n(16),s=n.n(c),l=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n,a){var i=Object(l.a)(t);return function(){var t=s()(r.a.mark((function t(o,c,s,l){var u;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:s,value:l},t.abrupt("return",new Promise((function(e){var t,n,r=setTimeout((function(){a.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(r),e()};i("event",c,d(d({},u),{},{event_callback:s})),(null===(t=a._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,a,r){return t.apply(this,arguments)}}()}},function(e,t,n){"use strict";var a=n(123);n.d(t,"a",(function(){return a.a}));var r=n(124);n.d(t,"c",(function(){return r.a}));var i=n(125);n.d(t,"b",(function(){return i.a}))},,function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return a.createElement("svg",r({viewBox:"0 0 14 14",fill:"none"},e),i)}},function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(11),u=n.n(l);function VisuallyHidden(t){var n=t.className,a=t.children,i=o()(t,["className","children"]);return a?e.createElement("span",r()({},i,{className:u()("screen-reader-text",n)}),a):null}VisuallyHidden.propTypes={className:s.a.string,children:s.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var a=n(21),r=n.n(a),i=n(152),o=n.n(i),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(2),g=n(10),m=n(154),f=n(104);function TourTooltip(t){var n=t.backProps,a=t.closeProps,c=t.index,l=t.primaryProps,u=t.size,p=t.step,b=t.tooltipProps,v=u>1?Object(m.a)(u):[],h=function(e){return s()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",r()({className:s()("googlesitekit-tour-tooltip",p.className)},b),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},p.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},p.content)),e.createElement(i.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},v.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:h(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(g.Button,r()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),p.cta,l.title&&e.createElement(g.Button,r()({className:"googlesitekit-tooltip-button",text:!0},l),l.title))),e.createElement(g.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(f.a,{width:"14",height:"14"}),onClick:a.onClick,"aria-label":Object(d.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";var a=n(434);n.d(t,"a",(function(){return a.a}));var r=n(435);n.d(t,"b",(function(){return r.a}));var i=n(436);n.d(t,"c",(function(){return i.a}));var o=n(437);n.d(t,"d",(function(){return o.a}));var c=n(438);n.d(t,"e",(function(){return c.a}));var s=n(439);n.d(t,"f",(function(){return s.a}));var l=n(272);n.d(t,"g",(function(){return l.a}));var u=n(202);n.d(t,"h",(function(){return u.a}));n(359)},function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(208),s=n(38),l=n(2),u=n(10),d=n(58);function ModalDialog(t){var n=t.className,a=void 0===n?"":n,r=t.dialogActive,i=void 0!==r&&r,g=t.handleDialog,m=void 0===g?null:g,f=t.onOpen,p=void 0===f?null:f,b=t.onClose,v=void 0===b?null:b,h=t.title,y=void 0===h?null:h,O=t.provides,_=t.handleConfirm,E=t.subtitle,k=t.confirmButton,S=void 0===k?null:k,j=t.dependentModules,T=t.danger,A=void 0!==T&&T,N=t.inProgress,C=void 0!==N&&N,w=t.small,I=void 0!==w&&w,M=t.medium,R=void 0!==M&&M,D=t.buttonLink,x=void 0===D?null:D,L=Object(c.a)(ModalDialog),P="googlesitekit-dialog-description-".concat(L),G=!(!O||!O.length);return e.createElement(u.Dialog,{open:i,onOpen:p,onClose:v,"aria-describedby":G?P:void 0,tabIndex:"-1",className:o()(a,{"googlesitekit-dialog-sm":I,"googlesitekit-dialog-md":R})},e.createElement(u.DialogTitle,null,A&&e.createElement(d.a,{width:28,height:28}),y),E?e.createElement("p",{className:"mdc-dialog__lead"},E):[],e.createElement(u.DialogContent,null,G&&e.createElement("section",{id:P,className:"mdc-dialog__provides"},e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},O.map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t},e.createElement("span",{className:"mdc-list-item__text"},t))})))),j&&e.createElement("p",{className:"mdc-dialog__dependencies"},Object(s.a)(Object(l.sprintf)(
/* translators: %s is replaced with the dependent modules. */
Object(l.__)("<strong>Note:</strong> %s","google-site-kit"),j),{strong:e.createElement("strong",null)}))),e.createElement(u.DialogFooter,null,e.createElement(u.Button,{className:"mdc-dialog__cancel-button",tertiary:!0,onClick:m,disabled:C},Object(l.__)("Cancel","google-site-kit")),x?e.createElement(u.Button,{href:x,onClick:_,target:"_blank",danger:A},S):e.createElement(u.SpinnerButton,{onClick:_,danger:A,disabled:C,isSaving:C},S||Object(l.__)("Disconnect","google-site-kit"))))}ModalDialog.displayName="Dialog",ModalDialog.propTypes={className:r.a.string,dialogActive:r.a.bool,handleDialog:r.a.func,handleConfirm:r.a.func.isRequired,onOpen:r.a.func,onClose:r.a.func,title:r.a.string,confirmButton:r.a.string,danger:r.a.bool,small:r.a.bool,medium:r.a.bool,buttonLink:r.a.string},t.a=ModalDialog}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var a=n(1),r=n.n(a),i=n(0),o=n(9),c=n(54);function Description(t){var n=t.className,a=void 0===n?"googlesitekit-publisher-win__desc":n,r=t.text,s=t.learnMoreLink,l=t.errorText,u=t.children;return e.createElement(i.Fragment,null,e.createElement("div",{className:a},e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.F)(r,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",s)),l&&e.createElement(c.a,{message:l}),u)}Description.propTypes={className:r.a.string,text:r.a.string,learnMoreLink:r.a.node,errorText:r.a.string,children:r.a.node}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var a=n(392),r=function(e,t,n){Object(a.a)((function(n){return e.includes(n.keyCode)&&t.current.contains(n.target)}),n)}},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return u}));var a=n(6),r=n.n(a),i=n(1),o=n.n(i),c=n(11),s=n.n(c),l=n(2),u={DEFAULT:"default",OVERLAY:"overlay",SMALL:"small",SMALL_OVERLAY:"small-overlay",LARGE:"large"};function GatheringDataNotice(t){var n=t.style;return e.createElement("div",{className:s()("googlesitekit-gathering-data-notice",r()({},"googlesitekit-gathering-data-notice--has-style-".concat(n),!!n))},e.createElement("span",null,Object(l.__)("Gathering data…","google-site-kit")))}GatheringDataNotice.propTypes={style:o.a.oneOf(Object.values(u))},t.b=GatheringDataNotice}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(0),s=n(137),l=n(58),u=n(131),d=n(17),g=Object(c.forwardRef)((function(t,n){var a=t.className,r=t.title,i=t.description,c=t.dismissCTA,g=t.additionalCTA,m=t.reverseCTAs,f=void 0!==m&&m,p=t.type,b=void 0===p?"success":p,v=t.icon;return e.createElement(d.e,{ref:n},e.createElement(d.k,null,e.createElement(d.a,{alignMiddle:!0,size:12,className:o()("googlesitekit-subtle-notification",a,{"googlesitekit-subtle-notification--success":"success"===b,"googlesitekit-subtle-notification--warning":"warning"===b,"googlesitekit-subtle-notification--new-feature":"new-feature"===b})},e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},v,"success"===b&&!v&&e.createElement(s.a,{width:24,height:24}),"warning"===b&&!v&&e.createElement(l.a,{width:24,height:24}),"new-feature"===b&&!v&&e.createElement(u.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,r),e.createElement("p",{className:"googlesitekit-subtle-notification__secondary_description"},i)),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},!f&&c,f&&g,!f&&g,f&&c))))}));g.propTypes={className:r.a.string,title:r.a.node,description:r.a.node,dismissCTA:r.a.node,additionalCTA:r.a.node,reverseCTAs:r.a.bool,type:r.a.oneOf(["success","warning","new-feature"]),icon:r.a.object},t.a=g}).call(this,n(4))},,function(e,t,n){"use strict";var a=n(326),r=n(314);n.d(t,"b",(function(){return r.a}));var i=n(315);n.d(t,"c",(function(){return i.a}));var o=n(316);n.d(t,"d",(function(){return o.a}));var c=n(317);n.d(t,"a",(function(){return c.a})),t.e=a.a},,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var a=n(1),r=n.n(a),i=n(0),o=n(2),c=n(3),s=n(10),l=n(35),u=n(54);function ErrorNotice(t){var n,a=t.error,r=t.hasButton,d=void 0!==r&&r,g=t.storeName,m=t.message,f=void 0===m?a.message:m,p=t.noPrefix,b=void 0!==p&&p,v=t.skipRetryMessage,h=t.Icon,y=Object(c.useDispatch)(),O=Object(c.useSelect)((function(e){return g?e(g).getSelectorDataForError(a):null})),_=Object(i.useCallback)((function(){y(O.storeName).invalidateResolution(O.name,O.args)}),[y,O]);if(!a||Object(l.f)(a))return null;var E=d&&Object(l.d)(a,O);return d||v||(f=Object(o.sprintf)(
/* translators: %1$s: Error message from Google API. */
Object(o.__)("%1$s%2$s Please try again.","google-site-kit"),f,f.endsWith(".")?"":".")),e.createElement(i.Fragment,null,h&&e.createElement("div",{className:"googlesitekit-error-notice__icon"},e.createElement(h,{width:"24",height:"24"})),e.createElement(u.a,{message:f,reconnectURL:null===(n=a.data)||void 0===n?void 0:n.reconnectURL,noPrefix:b}),E&&e.createElement(s.Button,{className:"googlesitekit-error-notice__retry-button",onClick:_},Object(o.__)("Retry","google-site-kit")))}ErrorNotice.propTypes={error:r.a.shape({message:r.a.string}),hasButton:r.a.bool,storeName:r.a.string,message:r.a.string,noPrefix:r.a.bool,skipRetryMessage:r.a.bool,Icon:r.a.elementType}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var a=n(219),r=n(14),i=n(0);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=Object(a.b)((function(){return r.debounce.apply(void 0,t)}),t);return Object(i.useEffect)((function(){return function(){return o.cancel()}}),[o]),o}},function(e,t,n){"use strict";n.d(t,"d",(function(){return c})),n.d(t,"b",(function(){return s})),n.d(t,"a",(function(){return l})),n.d(t,"c",(function(){return u})),n.d(t,"e",(function(){return d}));var a=n(33),r=n.n(a),i=n(14),o=n(178);function c(e){var t=function(e){return"string"==typeof e&&/^[a-zA-Z0-9_]+$/.test(e)};return"string"==typeof e?e.split(",").every(t):Object(o.c)(e,(function(e){var n=e.hasOwnProperty("name")&&t(e.name);if(!e.hasOwnProperty("expression"))return n;var a="string"==typeof e.expression;return n&&a}),t)}function s(e){return Object(o.c)(e,(function(e){return e.hasOwnProperty("name")&&"string"==typeof e.name}))}function l(e){var t=["string"];return Object.keys(e).every((function(n){if(t.includes(r()(e[n])))return!0;if(Array.isArray(e[n]))return e[n].every((function(e){return t.includes(r()(e))}));if(Object(i.isPlainObject)(e[n])){var a=Object.keys(e[n]);return!!a.includes("filterType")&&!("emptyFilter"!==e[n].filterType&&!a.includes("value"))}return!1}))}function u(e){var t=["string"],n=["numericFilter","betweenFilter"];return Object.values(e).every((function(e){if(t.includes(r()(e)))return!0;if(Array.isArray(e))return e.every((function(e){return t.includes(r()(e))}));if(!Object(i.isPlainObject)(e))return!1;var a=e.filterType,o=e.value,c=e.fromValue,s=e.toValue;if(a&&!n.includes(a))return!1;var l=Object.keys(e);return a&&"numericFilter"!==a?"betweenFilter"===a&&(l.includes("fromValue")&&l.includes("toValue")&&[c,s].every((function(e){return!Object(i.isPlainObject)(e)||"int64Value"in e}))):l.includes("operation")&&l.includes("value")&&(!Object(i.isPlainObject)(o)||"int64Value"in o)}))}function d(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(i.isPlainObject)(e)&&((!e.hasOwnProperty("desc")||"boolean"==typeof e.desc)&&(e.metric?!e.dimension&&"string"==typeof(null===(t=e.metric)||void 0===t?void 0:t.metricName):!!e.dimension&&"string"==typeof(null===(n=e.dimension)||void 0===n?void 0:n.dimensionName)));var t,n}))}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var a=n(21),r=n.n(a),i=n(6),o=n.n(i),c=n(25),s=n.n(c),l=n(1),u=n.n(l),d=n(11),g=n.n(d);function Cell(t){var n,a=t.className,i=t.alignTop,c=t.alignMiddle,l=t.alignBottom,u=t.alignRight,d=t.alignLeft,m=t.smAlignRight,f=t.mdAlignRight,p=t.lgAlignRight,b=t.smSize,v=t.smStart,h=t.smOrder,y=t.mdSize,O=t.mdStart,_=t.mdOrder,E=t.lgSize,k=t.lgStart,S=t.lgOrder,j=t.size,T=t.children,A=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",r()({},A,{className:g()(a,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":i,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":l,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":m,"mdc-layout-grid__cell--align-right-tablet":f,"mdc-layout-grid__cell--align-right-desktop":p},o()(n,"mdc-layout-grid__cell--span-".concat(j),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(E,"-desktop"),12>=E&&E>0),o()(n,"mdc-layout-grid__cell--start-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--order-".concat(S,"-desktop"),12>=S&&S>0),o()(n,"mdc-layout-grid__cell--span-".concat(y,"-tablet"),8>=y&&y>0),o()(n,"mdc-layout-grid__cell--start-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--order-".concat(_,"-tablet"),8>=_&&_>0),o()(n,"mdc-layout-grid__cell--span-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--start-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--order-".concat(h,"-phone"),4>=h&&h>0),n))}),T)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),g=Object(d.forwardRef)((function(t,n){var a=t.className,i=t.children,c=o()(t,["className","children"]);return e.createElement("div",r()({ref:n,className:u()("mdc-layout-grid__inner",a)},c),i)}));g.displayName="Row",g.propTypes={className:s.a.string,children:s.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),g=Object(d.forwardRef)((function(t,n){var a=t.alignLeft,i=t.fill,c=t.className,s=t.children,l=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",r()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":a,"mdc-layout-grid--collapsed":l,"mdc-layout-grid--fill":i})},d,{ref:n}),s)}));g.displayName="Grid",g.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(4))},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),a.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return a.createElement("svg",r({viewBox:"0 0 13 13"},e),i)}},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),a.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return a.createElement("svg",r({viewBox:"0 0 13 13"},e),i)}},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return a.createElement("svg",r({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var a=n(14),r=function(e){return Object(a.isFinite)(e)?e:0}},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return a.createElement("svg",r({viewBox:"0 0 24 24",fill:"none"},e),i)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InfoTooltip}));var a=n(11),r=n.n(a),i=n(1),o=n.n(i),c=n(10),s=n(325);function InfoTooltip(t){var n=t.onOpen,a=t.title,i=t.tooltipClassName;return a?e.createElement(c.Tooltip,{className:"googlesitekit-info-tooltip",tooltipClassName:r()("googlesitekit-info-tooltip__content",i),title:a,placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,interactive:!0,onOpen:n},e.createElement("span",null,e.createElement(s.a,{width:"16",height:"16"}))):null}InfoTooltip.propTypes={onOpen:o.a.func,title:o.a.oneOfType([o.a.string,o.a.element]),tooltipClassName:o.a.string}}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(38),s=n(2),l=n(20),u=n(34);function SourceLink(t){var n=t.name,a=t.href,r=t.className,i=t.external;return Object(u.a)()?null:e.createElement("div",{className:o()("googlesitekit-source-link",r)},Object(c.a)(Object(s.sprintf)(
/* translators: %s: source link */
Object(s.__)("Source: %s","google-site-kit"),"<a>".concat(n,"</a>")),{a:e.createElement(l.a,{key:"link",href:a,external:i})}))}SourceLink.propTypes={name:r.a.string,href:r.a.string,className:r.a.string,external:r.a.bool},SourceLink.defaultProps={name:"",href:"",className:"",external:!1},t.a=SourceLink}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportErrorActions}));var a=n(6),r=n.n(a),i=n(1),o=n.n(i),c=n(0),s=n(38),l=n(2),u=n(3),d=n(10),g=n(13),m=n(19),f=n(35),p=n(34),b=n(20);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportErrorActions(t){var n=t.moduleSlug,a=t.error,r=t.GetHelpLink,i=t.hideGetHelpLink,o=t.buttonVariant,v=t.onRetry,y=t.onRequestAccess,O=t.getHelpClassName,_=t.RequestAccessButton,E=t.RetryButton,k=Object(p.a)(),S=Object(u.useSelect)((function(e){return e(m.a).getModuleStoreName(n)})),j=Object(u.useSelect)((function(e){var t;return"function"==typeof(null===(t=e(S))||void 0===t?void 0:t.getServiceEntityAccessURL)?e(S).getServiceEntityAccessURL():null})),T=Array.isArray(a)?a:[a],A=Object(u.useSelect)((function(e){return T.map((function(t){var n,a=null===(n=e(S))||void 0===n?void 0:n.getSelectorDataForError(t);return h(h({},t),{},{selectorData:a})}))})),N=null==A?void 0:A.filter((function(e){return Object(f.d)(e,e.selectorData)&&"getReport"===e.selectorData.name})),C=!!N.length,w=Object(u.useSelect)((function(e){var t=h({},C?N[0]:T[0]);return Object(f.e)(t)&&(t.code="".concat(n,"_insufficient_permissions")),e(g.c).getErrorTroubleshootingLinkURL(t)})),I=Object(u.useDispatch)(),M=T.some((function(e){return Object(f.e)(e)})),R=Object(c.useCallback)((function(){N.forEach((function(e){var t=e.selectorData;I(t.storeName).invalidateResolution(t.name,t.args)})),null==v||v()}),[I,N,v]),D=j&&M&&!k;return e.createElement("div",{className:"googlesitekit-report-error-actions"},D&&("function"==typeof _?e.createElement(_,{requestAccessURL:j}):e.createElement(d.Button,{onClick:y,href:j,target:"_blank",danger:"danger"===o,tertiary:"tertiary"===o},Object(l.__)("Request access","google-site-kit"))),C&&e.createElement(c.Fragment,null,"function"==typeof E?e.createElement(E,{handleRetry:R}):e.createElement(d.Button,{onClick:R,danger:"danger"===o,tertiary:"tertiary"===o},Object(l.__)("Retry","google-site-kit")),!i&&e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(s.a)(Object(l.__)("Retry didn’t work? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(b.a,{href:w,external:!0,hideExternalIndicator:!0},Object(l.__)("Get help","google-site-kit"))}))),!C&&!i&&e.createElement("div",{className:O},"function"==typeof r?e.createElement(r,{linkURL:w}):e.createElement(b.a,{href:w,external:!0,hideExternalIndicator:!0},Object(l.__)("Get help","google-site-kit"))))}ReportErrorActions.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired,GetHelpLink:o.a.elementType,hideGetHelpLink:o.a.bool,buttonVariant:o.a.string,onRetry:o.a.func,onRequestAccess:o.a.func,getHelpClassName:o.a.string,RequestAccessButton:o.a.elementType,RetryButton:o.a.elementType}}).call(this,n(4))},,function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return a.createElement("svg",r({viewBox:"0 0 24 24",fill:"none"},e),i)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConnectGA4CTATileWidget}));var a=n(1),r=n.n(a),i=n(3),o=n(530),c=n(143),s=n(7),l=n(74),u={moduleSlug:"analytics-4"};function ConnectGA4CTATileWidget(t){var n=t.Widget,a=t.widgetSlug,r=Object(i.useSelect)((function(e){var t=e(s.a).getKeyMetrics();return t?t.filter((function(e){return s.Q.includes(e)})).length:[]}))>3?l.a:o.a;return Object(c.a)(a,r,u),e.createElement(n,null,e.createElement(o.a,u))}ConnectGA4CTATileWidget.propTypes={Widget:r.a.elementType.isRequired,widgetSlug:r.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";var a=n(0),r=Object(a.createContext)(!1);t.a=r},function(e,t,n){"use strict";n.d(t,"d",(function(){return a})),n.d(t,"e",(function(){return r})),n.d(t,"j",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"g",(function(){return c})),n.d(t,"k",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"b",(function(){return u})),n.d(t,"h",(function(){return d})),n.d(t,"f",(function(){return g})),n.d(t,"i",(function(){return m})),n.d(t,"l",(function(){return f})),n.d(t,"n",(function(){return p})),n.d(t,"r",(function(){return b})),n.d(t,"m",(function(){return v})),n.d(t,"p",(function(){return h})),n.d(t,"q",(function(){return y})),n.d(t,"o",(function(){return O})),n.d(t,"t",(function(){return _})),n.d(t,"s",(function(){return E}));var a="disapproved",r="graylisted",i="pending",o="approved",c="needs-attention",s="ready",l="client-requires-review",u="client-getting-ready",d="none",g="multiple",m="no-client",f="added",p="needs-attention",b="requires-review",v="getting-ready",h="ready",y="ready-no-auto-ads",O="none",_=[a,r,i,o],E=function(e){return e===r||e===i}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return StoreErrorNotices}));var a=n(1),r=n.n(a),i=n(3),o=n(120),c=n(19),s=n(35),l=n(169);function StoreErrorNotices(t){var n=t.hasButton,a=void 0!==n&&n,r=t.moduleSlug,u=t.storeName,d=Object(i.useSelect)((function(e){return e(u).getErrors()})),g=Object(i.useSelect)((function(e){return e(c.a).getModule(r)})),m=[];return d.filter((function(e){return!(!(null==e?void 0:e.message)||m.includes(e.message))&&(m.push(e.message),!0)})).map((function(t,n){var r=t.message;return Object(s.e)(t)&&(r=Object(l.a)(r,g)),e.createElement(o.a,{key:n,error:t,hasButton:a,storeName:u,message:r})}))}StoreErrorNotices.propTypes={hasButton:r.a.bool,storeName:r.a.string.isRequired,moduleSlug:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";var a=n(166);n.d(t,"c",(function(){return a.a}));var r=n(65);n.d(t,"b",(function(){return r.c})),n.d(t,"a",(function(){return r.a}))},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var a=n(0),r=n(3),i=n(47);function o(e,t,n){var o=Object(r.useDispatch)(i.a),c=o.setWidgetState,s=o.unsetWidgetState;Object(a.useEffect)((function(){return c(e,t,n),function(){s(e,t,n)}}),[e,t,n,c,s])}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var a=n(6),r=n.n(a),i=n(2),o=n(7),c=n(13),s=n(8);function l(e,t,n){return e(s.r).hasConversionReportingEvents(this.requiredConversionEventName)||e(o.a).isKeyMetricActive(n)}var u,d=n(26);function g(e,t){return!t||!(!t||!e(s.r).getAdSenseLinked())}function m(e,t){return!t||e(s.r).hasCustomDimensions(this.requiredCustomDimensions)}var f=(u={},r()(u,o.f,{title:Object(i.__)("Top earning pages","google-site-kit"),description:Object(i.__)("Pages that generated the most AdSense revenue","google-site-kit"),infoTooltip:Object(i.__)("Pages that generated the most AdSense revenue","google-site-kit"),displayInSelectionPanel:g,displayInList:g,metadata:{group:d.b.SLUG}}),r()(u,o.y,{title:Object(i.__)("Top recent trending pages","google-site-kit"),description:Object(i.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),infoTooltip:Object(i.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_date"],displayInSelectionPanel:m,displayInWidgetArea:m,displayInList:m,metadata:{group:d.b.SLUG}}),r()(u,o.l,{title:Object(i.__)("Most popular authors by pageviews","google-site-kit"),description:Object(i.__)("Authors whose posts got the most visits","google-site-kit"),infoTooltip:Object(i.__)("Authors whose posts got the most visits","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_author"],displayInSelectionPanel:m,displayInWidgetArea:m,displayInList:m,metadata:{group:d.b.SLUG}}),r()(u,o.p,{title:Object(i.__)("Top categories by pageviews","google-site-kit"),description:Object(i.__)("Categories that your site visitors viewed the most","google-site-kit"),infoTooltip:Object(i.__)("Categories that your site visitors viewed the most","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_categories"],displayInSelectionPanel:m,displayInWidgetArea:m,displayInList:m,metadata:{group:d.b.SLUG}}),r()(u,o.m,{title:Object(i.__)("Most popular content by pageviews","google-site-kit"),description:Object(i.__)("Pages that brought in the most visitors","google-site-kit"),infoTooltip:Object(i.__)("Pages your visitors read the most","google-site-kit"),metadata:{group:d.b.SLUG}}),r()(u,o.n,{title:Object(i.__)("Most popular products by pageviews","google-site-kit"),description:Object(i.__)("Products that brought in the most visitors","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_type"],displayInSelectionPanel:function(e){return e(o.a).isKeyMetricActive(o.n)||e(c.c).getProductPostType()},displayInWidgetArea:m,metadata:{group:d.f.SLUG}}),r()(u,o.k,{title:Object(i.__)("Pages per visit","google-site-kit"),description:Object(i.__)("Number of pages visitors viewed per session on average","google-site-kit"),infoTooltip:Object(i.__)("Number of pages visitors viewed per session on average","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.F,{title:Object(i.__)("Visit length","google-site-kit"),description:Object(i.__)("Average duration of engaged visits","google-site-kit"),infoTooltip:Object(i.__)("Average duration of engaged visits","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.E,{title:Object(i.__)("Visits per visitor","google-site-kit"),description:Object(i.__)("Average number of sessions per site visitor","google-site-kit"),infoTooltip:Object(i.__)("Average number of sessions per site visitor","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.i,{title:Object(i.__)("Most engaging pages","google-site-kit"),description:Object(i.__)("Pages with the highest engagement rate","google-site-kit"),infoTooltip:Object(i.__)("Pages with the highest engagement rate","google-site-kit"),metadata:{group:d.b.SLUG}}),r()(u,o.h,{title:Object(i.__)("Least engaging pages","google-site-kit"),description:Object(i.__)("Pages with the highest percentage of visitors that left without engagement with your site","google-site-kit"),infoTooltip:Object(i.__)("Percentage of visitors that left without engagement with your site","google-site-kit"),metadata:{group:d.b.SLUG}}),r()(u,o.z,{title:Object(i.__)("Top pages by returning visitors","google-site-kit"),description:Object(i.__)("Pages that attracted the most returning visitors","google-site-kit"),infoTooltip:Object(i.__)("Pages that attracted the most returning visitors","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.j,{title:Object(i.__)("New visitors","google-site-kit"),description:Object(i.__)("How many new visitors you got and how the overall audience changed","google-site-kit"),infoTooltip:Object(i.__)("Portion of visitors who visited your site for the first time in this timeframe","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.o,{title:Object(i.__)("Returning visitors","google-site-kit"),description:Object(i.__)("Portion of people who visited your site more than once","google-site-kit"),infoTooltip:Object(i.__)("Portion of your site’s visitors that returned at least once in this timeframe","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.A,{title:Object(i.__)("Top traffic source","google-site-kit"),description:Object(i.__)("Channel which brought in the most visitors to your site","google-site-kit"),infoTooltip:Object(i.__)("Channel (e.g. social, paid, search) that brought in the most visitors to your site","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.B,{title:Object(i.__)("Top traffic source driving add to cart","google-site-kit"),description:Object(i.__)("Channel which brought in the most add to cart events to your site","google-site-kit"),infoTooltip:Object(i.__)("Channel (e.g. social, paid, search) that brought in the most add to cart events to your site","google-site-kit"),requiredConversionEventName:[s.l.ADD_TO_CART],displayInSelectionPanel:l,displayInList:l,metadata:{group:d.f.SLUG}}),r()(u,o.C,{title:Object(i.__)("Top traffic source driving leads","google-site-kit"),description:Object(i.__)("Channel which brought in the most leads to your site","google-site-kit"),infoTooltip:Object(i.__)("Channel (e.g. social, paid, search) that brought in the most leads to your site","google-site-kit"),requiredConversionEventName:[s.l.SUBMIT_LEAD_FORM,s.l.CONTACT,s.l.GENERATE_LEAD],displayInSelectionPanel:l,displayInList:l,metadata:{group:d.e.SLUG}}),r()(u,o.D,{title:Object(i.__)("Top traffic source driving purchases","google-site-kit"),description:Object(i.__)("Channel which brought in the most purchases to your site","google-site-kit"),infoTooltip:Object(i.__)("Channel (e.g. social, paid, search) that brought in the most purchases to your site","google-site-kit"),requiredConversionEventName:[s.l.PURCHASE],displayInSelectionPanel:l,displayInList:l,metadata:{group:d.f.SLUG}}),r()(u,o.g,{title:Object(i.__)("Most engaged traffic source","google-site-kit"),description:Object(i.__)("Visitors coming via this channel spent the most time on your site","google-site-kit"),infoTooltip:Object(i.__)("Channel (e.g. social, paid, search) that brought in the most visitors who had a meaningful engagement with your site","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.u,{title:Object(i.__)("Top converting traffic source","google-site-kit"),description:Object(i.__)("Channel which brought in the most visits that resulted in conversions","google-site-kit"),infoTooltip:Object(i.__)("Channel (e.g. social, paid, search) that brought in visitors who generated the most conversions","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.q,{title:Object(i.__)("Top cities driving traffic","google-site-kit"),description:Object(i.__)("Which cities you get the most visitors from","google-site-kit"),infoTooltip:Object(i.__)("The cities where most of your visitors came from","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.s,{title:Object(i.__)("Top cities driving leads","google-site-kit"),description:Object(i.__)("Cities driving the most contact form submissions","google-site-kit"),infoTooltip:Object(i.__)("Cities driving the most contact form submissions","google-site-kit"),requiredConversionEventName:[s.l.SUBMIT_LEAD_FORM,s.l.CONTACT,s.l.GENERATE_LEAD],displayInSelectionPanel:l,displayInList:l,metadata:{group:d.e.SLUG}}),r()(u,o.r,{title:Object(i.__)("Top cities driving add to cart","google-site-kit"),description:Object(i.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),infoTooltip:Object(i.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),requiredConversionEventName:[s.l.ADD_TO_CART],displayInSelectionPanel:l,displayInList:l,metadata:{group:d.f.SLUG}}),r()(u,o.t,{title:Object(i.__)("Top cities driving purchases","google-site-kit"),description:Object(i.__)("Cities driving the most purchases","google-site-kit"),infoTooltip:Object(i.__)("Cities driving the most purchases","google-site-kit"),requiredConversionEventName:[s.l.PURCHASE],displayInSelectionPanel:l,displayInList:l,metadata:{group:d.f.SLUG}}),r()(u,o.w,{title:Object(i.__)("Top device driving purchases","google-site-kit"),description:Object(i.__)("Top device driving the most purchases","google-site-kit"),infoTooltip:Object(i.__)("Top device driving the most purchases","google-site-kit"),requiredConversionEventName:[s.l.PURCHASE],displayInSelectionPanel:l,displayInList:l,metadata:{group:d.f.SLUG}}),r()(u,o.v,{title:Object(i.__)("Top countries driving traffic","google-site-kit"),description:Object(i.__)("Which countries you get the most visitors from","google-site-kit"),infoTooltip:Object(i.__)("The countries where most of your visitors came from","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.G,{title:Object(i.__)("Top performing keywords","google-site-kit"),description:Object(i.__)("What people searched for before they came to your site","google-site-kit"),infoTooltip:Object(i.__)("The top search queries for your site by highest clickthrough rate","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.x,{title:Object(i.__)("Top pages driving leads","google-site-kit"),description:Object(i.__)("Pages on which forms are most frequently submitted","google-site-kit"),requiredConversionEventName:[s.l.SUBMIT_LEAD_FORM,s.l.CONTACT,s.l.GENERATE_LEAD],displayInSelectionPanel:l,displayInList:l,metadata:{group:d.e.SLUG}}),u)},,function(e,t,n){"use strict";(function(e){var a=n(6),r=n.n(a),i=n(15),o=n.n(i),c=n(0),s=n(409),l=n(157);t.a=function(t,n){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=Object(c.useState)(Object(s.a)(a.location.href,t)||n),u=o()(i,2),d=u[0],g=u[1],m=function(e){g(e);var n=Object(l.a)(a.location.href,r()({},t,e));a.history.replaceState(null,"",n)};return[d,m]}}).call(this,n(28))},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M3.332 24.75h21.335c2.182 0 3.542-2.366 2.451-4.25L16.451 2.07C15.36.184 12.64.184 11.549 2.07L.882 20.5c-1.091 1.884.269 4.25 2.45 4.25zM14 14.833a1.42 1.42 0 01-1.417-1.416v-2.834c0-.779.638-1.416 1.417-1.416.78 0 1.417.637 1.417 1.416v2.834A1.42 1.42 0 0114 14.833zm1.417 5.667h-2.834v-2.833h2.834V20.5z",fill:"currentColor",fillRule:"nonzero"});t.a=function SvgError(e){return a.createElement("svg",r({viewBox:"0 0 28 25"},e),i)}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return a.a})),n.d(t,"c",(function(){return r.a})),n.d(t,"b",(function(){return i.a})),n.d(t,"g",(function(){return o.a})),n.d(t,"d",(function(){return c.a})),n.d(t,"e",(function(){return s.a})),n.d(t,"f",(function(){return l.a})),n.d(t,"h",(function(){return ZeroDataMessage}));var a=n(551),r=n(552),i=n(553),o=(n(352),n(554)),c=n(555),s=n(519),l=(n(425),n(394),n(556)),u=n(1),d=n.n(u),g=n(2),m=n(3),f=n(13);function ZeroDataMessage(e){var t=e.skipPrefix,n=Object(m.useSelect)((function(e){return e(f.c).getCurrentEntityURL()}));return t?n?Object(g.__)("Your page hasn’t received any visitors yet","google-site-kit"):Object(g.__)("Your site hasn’t received any visitors yet","google-site-kit"):n?Object(g.__)("No data to display: your page hasn’t received any visitors yet","google-site-kit"):Object(g.__)("No data to display: your site hasn’t received any visitors yet","google-site-kit")}ZeroDataMessage.propTypes={skipPrefix:d.a.bool}},function(e,t,n){"use strict";(function(e,a){var r=n(51),i=n.n(r),o=n(53),c=n.n(o),s=n(68),l=n.n(s),u=n(69),d=n.n(u),g=n(49),m=n.n(g),f=n(1),p=n.n(f),b=n(0),v=n(2),h=n(54);function y(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=m()(e);if(t){var r=m()(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return d()(this,n)}}var O=function(t){l()(MediaErrorHandler,t);var n=y(MediaErrorHandler);function MediaErrorHandler(e){var t;return i()(this,MediaErrorHandler),(t=n.call(this,e)).state={error:null},t}return c()(MediaErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t})}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.errorMessage;return this.state.error?a.createElement(h.a,{message:n}):t}}]),MediaErrorHandler}(b.Component);O.defaultProps={errorMessage:Object(v.__)("Failed to load media","google-site-kit")},O.propTypes={children:p.a.node.isRequired,errorMessage:p.a.string.isRequired},t.a=O}).call(this,n(28),n(4))},,,function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var a=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("path",{d:"M2.253 12.252l7.399 5.658A13.055 13.055 0 009 22c0 1.43.229 2.805.652 4.09l-7.4 5.658A22.02 22.02 0 010 22c0-3.506.81-6.814 2.253-9.748z",fill:"#FBBC05"}),a.createElement("path",{d:"M9.652 17.91l-7.4-5.658A21.935 21.935 0 0122 0c5.6 0 10.6 2.1 14.5 5.5l-6.4 6.4C27.9 10.1 25.1 9 22 9c-5.77 0-10.64 3.725-12.348 8.91z",fill:"#EA4335"}),a.createElement("path",{d:"M2.25 31.742l7.396-5.67A12.975 12.975 0 0022 35c6.1 0 10.7-3.1 11.8-8.5H22V18h20.5c.3 1.3.5 2.7.5 4 0 14-10 22-21 22A21.935 21.935 0 012.25 31.742z",fill:"#34A853"}),a.createElement("path",{d:"M36.34 38.52l-7.025-5.437c2.297-1.45 3.895-3.685 4.485-6.583H22V18h20.5c.3 1.3.5 2.7.5 4 0 7.17-2.623 12.767-6.66 16.52z",fill:"#4285F4"}));t.a=function SvgLogoG(e){return a.createElement("svg",r({viewBox:"0 0 43 44"},e),i)}},,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(0),s=n(3),l=n(13),u=n(7),d=n(19),g=n(32),m=n(37),f=n(36),p=n(18);function b(e){var t=Object(p.a)(),n=Object(s.useSelect)((function(t){return t(d.a).getModule(e)})),a=Object(s.useSelect)((function(e){return e(u.a).hasCapability(u.K)})),i=Object(s.useDispatch)(d.a).activateModule,b=Object(s.useDispatch)(g.a).navigateTo,v=Object(s.useDispatch)(l.c).setInternalServerError,h=Object(c.useCallback)(o()(r.a.mark((function n(){var a,o,c;return r.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,i(e);case 2:if(a=n.sent,o=a.error,c=a.response,o){n.next=13;break}return n.next=8,Object(f.b)("".concat(t,"_widget-activation-cta"),"activate_module",e);case 8:return n.next=10,Object(m.f)("module_setup",e,{ttl:300});case 10:b(c.moduleReauthURL),n.next=14;break;case 13:v({id:"".concat(e,"-setup-error"),description:o.message});case 14:case"end":return n.stop()}}),n)}))),[i,e,b,v,t]);return(null==n?void 0:n.name)&&a?h:null}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var a=n(3),r=n(23),i=function(e){return"notification/".concat(e,"/viewed")};function o(e){return Object(a.useSelect)((function(t){return!!t(r.b).getValue(i(e))}),[e])}o.getKey=i},function(e,t,n){"use strict";var a=n(646);n.d(t,"a",(function(){return a.a}));var r=n(647);n.d(t,"b",(function(){return r.a}));var i=n(648);n.d(t,"d",(function(){return i.a}));var o=n(649);n.d(t,"f",(function(){return o.a}));var c=n(650);n.d(t,"e",(function(){return c.a}));n(595);var s=n(407);n.d(t,"c",(function(){return s.c}));n(596)},,function(e,t,n){"use strict";(function(e){var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(1),s=n.n(c),l=n(0),u=n(20),d=n(9),g=n(18);function HelpMenuLink(t){var n=t.children,a=t.href,i=t.gaEventLabel,c=Object(g.a)(),s=Object(l.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!i){e.next=3;break}return e.next=3,Object(d.I)("".concat(c,"_headerbar_helpmenu"),"click_outgoing_link",i);case 3:case"end":return e.stop()}}),e)}))),[i,c]);return e.createElement("li",{className:"googlesitekit-help-menu-link mdc-list-item",role:"none"},e.createElement(u.a,{className:"mdc-list-item__text",href:a,external:!0,hideExternalIndicator:!0,role:"menuitem",onClick:s},n))}HelpMenuLink.propTypes={children:s.a.node.isRequired,href:s.a.string.isRequired,gaEventLabel:s.a.string},t.a=HelpMenuLink}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){var a=n(11),r=n.n(a),i=n(1),o=n.n(i),c=n(2),s=n(3),l=n(201),u=n(210),d=n(65),g=n(7),m=n(10),f=n(0),p=Object(f.forwardRef)((function(t,n){var a=t.className,i=t.children,o=t.type,f=t.dismiss,p=void 0===f?"":f,b=t.dismissCallback,v=t.dismissLabel,h=void 0===v?Object(c.__)("OK, Got it!","google-site-kit"):v,y=t.Icon,O=void 0===y?Object(d.d)(o):y,_=t.OuterCTA,E=Object(s.useDispatch)(g.a).dismissItem,k=Object(s.useSelect)((function(e){return p?e(g.a).isItemDismissed(p):void 0}));if(p&&k)return null;var S=i?u.a:l.a;return e.createElement("div",{ref:n,className:r()(a,"googlesitekit-settings-notice","googlesitekit-settings-notice--".concat(o),{"googlesitekit-settings-notice--single-row":!i,"googlesitekit-settings-notice--multi-row":i})},e.createElement("div",{className:"googlesitekit-settings-notice__icon"},e.createElement(O,{width:"20",height:"20"})),e.createElement("div",{className:"googlesitekit-settings-notice__body"},e.createElement(S,t)),p&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(m.Button,{tertiary:!0,onClick:function(){"string"==typeof p&&E(p),null==b||b()}},h)),_&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(_,null)))}));p.propTypes={className:o.a.string,children:o.a.node,notice:o.a.node.isRequired,type:o.a.oneOf([d.a,d.c,d.b]),Icon:o.a.elementType,LearnMore:o.a.elementType,CTA:o.a.elementType,OuterCTA:o.a.elementType,dismiss:o.a.string,dismissLabel:o.a.string,dismissCallback:o.a.func},p.defaultProps={type:d.a},t.a=p}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notifications}));var a=n(6),r=n.n(a),i=n(1),o=n.n(i),c=n(3),s=n(18),l=n(41),u=n(283);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function Notifications(t){var n,a=t.areaSlug,i=t.groupID,o=void 0===i?l.c.DEFAULT:i,g=Object(s.a)(),m=Object(c.useSelect)((function(e){return e(l.a).getQueuedNotifications(g,o)}));if(void 0===(null==m?void 0:m[0])||(null==m||null===(n=m[0])||void 0===n?void 0:n.areaSlug)!==a)return null;var f=m[0],p=f.id,b=f.Component,v=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},Object(u.a)(p));return e.createElement(b,v)}Notifications.propTypes={viewContext:o.a.string,areaSlug:o.a.string}}).call(this,n(4))},,function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var a=n(2);function r(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},r=n.slug,i=void 0===r?"":r,o=n.name,c=void 0===o?"":o,s=n.owner,l=void 0===s?{}:s;if(!i||!c)return e;var u="",d="";return"analytics-4"===i?e.match(/account/i)?u=Object(a.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?u=Object(a.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(u=Object(a.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):"search-console"===i&&(u=Object(a.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),u||(u=Object(a.sprintf)(
/* translators: %s: module name */
Object(a.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),c)),l&&l.login&&(d=Object(a.sprintf)(
/* translators: %s: owner name */
Object(a.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),l.login)),d||(d=Object(a.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(u," ").concat(d)}},,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GenericErrorHandlerActions}));var a=n(1),r=n.n(a),i=n(2),o=n(20),c=n(194);function GenericErrorHandlerActions(t){var n=t.message,a=t.componentStack;return e.createElement("div",{className:"googlesitekit-generic-error-handler-actions"},e.createElement(c.a,{message:n,componentStack:a}),e.createElement(o.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0},Object(i.__)("Report this problem","google-site-kit")))}GenericErrorHandlerActions.propTypes={message:r.a.string,componentStack:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var a=n(22),r=function(e){return a.f.includes(e)}},,function(e,t,n){"use strict";var a=n(216);n.d(t,"b",(function(){return a.a}));var r=n(221);n.d(t,"a",(function(){return r.a}))},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var a=n(374);function r(e){return Object(a.a)(e)}},,function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"d",(function(){return s}));var a=n(33),r=n.n(a);function i(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return!0};return"string"==typeof e?n(e):!("object"!==r()(e)||!t(e))||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e?n(e):"object"===r()(e)&&t(e)}))}function o(e){var t=e.startDate,n=e.endDate,a=t&&t.match(/^\d{4}-\d{2}-\d{2}$/),r=n&&n.match(/^\d{4}-\d{2}-\d{2}$/);return a&&r}function c(e){var t=function(e){var t=e.hasOwnProperty("fieldName")&&!!e.fieldName,n=e.hasOwnProperty("sortOrder")&&/(ASCENDING|DESCENDING)/i.test(e.sortOrder.toString());return t&&n};return Array.isArray(e)?e.every((function(e){return"object"===r()(e)&&t(e)})):"object"===r()(e)&&t(e)}function s(e){return"string"==typeof e||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e}))}},,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var a=n(1),r=n.n(a),i=" ";function DisplaySetting(e){return e.value||i}DisplaySetting.propTypes={value:r.a.oneOfType([r.a.string,r.a.bool,r.a.number])},t.b=DisplaySetting},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LoadingWrapper}));var a=n(6),r=n.n(a),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(44);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function LoadingWrapper(t){var n=t.loading,a=t.children,r=o()(t,["loading","children"]);return n?e.createElement(l.a,r):a}LoadingWrapper.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({loading:s.a.bool,children:s.a.node},l.a.propTypes)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeBadge}));var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(9);function ChangeBadge(t){var n=t.previousValue,a=t.currentValue,r=t.isAbsolute?a-n:Object(c.g)(n,a),i=r<0,s=0===r;return null===r?null:e.createElement("div",{className:o()("googlesitekit-change-badge",{"googlesitekit-change-badge--negative":i,"googlesitekit-change-badge--zero":s})},Object(c.B)(r,{style:"percent",signDisplay:"exceptZero",maximumFractionDigits:1}))}ChangeBadge.propTypes={isAbsolute:r.a.bool,previousValue:r.a.number.isRequired,currentValue:r.a.number.isRequired}}).call(this,n(4))},,function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M0 0h2v7H0zm0 10h2v2H0z",fill:"currentColor",fillRule:"evenodd"});t.a=function SvgWarningIcon(e){return a.createElement("svg",r({viewBox:"0 0 2 12"},e),i)}},,,function(e,t,n){"use strict";(function(e){function Title(t){var n=t.title;return e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n)}n.d(t,"a",(function(){return Title}))}).call(this,n(4))},function(e,t){e.exports=googlesitekit.modules},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notification}));var a=n(15),r=n.n(a),i=n(1),o=n.n(i),c=n(0),s=n(282),l=n(161),u=n(73);function Notification(t){var n=t.id,a=t.className,i=t.gaTrackingEventArgs,o=t.children,d=t.onView,g=Object(c.useRef)(),m=Object(l.a)(n),f=Object(u.a)(n,null==i?void 0:i.category),p=Object(c.useState)(!1),b=r()(p,2),v=b[0],h=b[1];return Object(c.useEffect)((function(){!v&&m&&(f.view(null==i?void 0:i.label,null==i?void 0:i.value),null==d||d(),h(!0))}),[m,f,v,i,d]),e.createElement("section",{id:n,ref:g,className:a},o,!m&&e.createElement(s.a,{id:n,observeRef:g,threshold:.5}))}Notification.propTypes={id:o.a.string,className:o.a.string,gaTrackingEventArgs:o.a.shape({category:o.a.string,label:o.a.string,value:o.a.string}),children:o.a.node,onView:o.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(51),r=n.n(a),i=n(53),o=n.n(i),c=n(68),s=n.n(c),l=n(69),u=n.n(l),d=n(49),g=n.n(d),m=n(1),f=n.n(m),p=n(11),b=n.n(p),v=n(0),h=n(329),y=n(330);function O(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=g()(e);if(t){var r=g()(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return u()(this,n)}}var _=function(t){s()(Layout,t);var n=O(Layout);function Layout(){return r()(this,Layout),n.apply(this,arguments)}return o()(Layout,[{key:"render",value:function(){var t=this.props,n=t.header,a=t.footer,r=t.children,i=t.title,o=t.badge,c=t.headerCTALabel,s=t.headerCTALink,l=t.footerCTALabel,u=t.footerCTALink,d=t.footerContent,g=t.className,m=t.fill,f=t.relative,p=t.rounded,v=void 0!==p&&p,O=t.transparent,_=void 0!==O&&O;return e.createElement("div",{className:b()("googlesitekit-layout",g,{"googlesitekit-layout--fill":m,"googlesitekit-layout--relative":f,"googlesitekit-layout--rounded":v,"googlesitekit-layout--transparent":_})},n&&e.createElement(h.a,{title:i,badge:o,ctaLabel:c,ctaLink:s}),r,a&&e.createElement(y.a,{ctaLabel:l,ctaLink:u,footerContent:d}))}}]),Layout}(v.Component);_.propTypes={header:f.a.bool,footer:f.a.bool,children:f.a.node.isRequired,title:f.a.string,badge:f.a.node,headerCTALabel:f.a.string,headerCTALink:f.a.string,footerCTALabel:f.a.string,footerCTALink:f.a.string,footerContent:f.a.node,className:f.a.string,fill:f.a.bool,relative:f.a.bool,rounded:f.a.bool,transparent:f.a.bool},_.defaultProps={header:!1,footer:!1,title:"",badge:null,headerCTALabel:"",headerCTALink:"",footerCTALabel:"",footerCTALink:"",footerContent:null,className:"",fill:!1,relative:!1},t.a=_}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){var a=n(15),r=n.n(a),i=n(195),o=n.n(i),c=n(1),s=n.n(c),l=n(0),u=n(2),d=n(266),g=n(423),m=n(424),f=n(10);function ReportErrorButton(t){var n=t.message,a=t.componentStack,i=Object(l.useState)(!1),c=r()(i,2),s=c[0],p=c[1];return e.createElement(f.Button,{"aria-label":s?Object(u.__)("Error message copied to clipboard. Click to copy the error message again.","google-site-kit"):void 0,onClick:function(){o()("`".concat(n,"\n").concat(a,"`")),p(!0)},trailingIcon:e.createElement(d.a,{className:"mdc-button__icon",icon:s?g.a:m.a})},s?Object(u.__)("Copied to clipboard","google-site-kit"):Object(u.__)("Copy error contents","google-site-kit"))}ReportErrorButton.propTypes={message:s.a.string,componentStack:s.a.string},t.a=ReportErrorButton}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationError}));var a=n(17),r=n(222),i=n(189);function NotificationError(t){var n=t.title,o=t.description,c=t.actions;return e.createElement(a.e,null,e.createElement(a.k,null,e.createElement(a.a,{smSize:3,mdSize:7,lgSize:11,className:"googlesitekit-publisher-win__content"},e.createElement(i.a,{title:n}),o,c),e.createElement(r.a,{type:"win-error"})))}}).call(this,n(4))},,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SupportLink}));var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(3),u=n(13),d=n(20);function SupportLink(t){var n=t.path,a=t.query,i=t.hash,c=o()(t,["path","query","hash"]),s=Object(l.useSelect)((function(e){return e(u.c).getGoogleSupportURL({path:n,query:a,hash:i})}));return e.createElement(d.a,r()({},c,{href:s}))}SupportLink.propTypes={path:s.a.string.isRequired,query:s.a.object,hash:s.a.string}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return k})),n.d(t,"b",(function(){return S})),n.d(t,"c",(function(){return j})),n.d(t,"g",(function(){return T})),n.d(t,"f",(function(){return A})),n.d(t,"d",(function(){return N})),n.d(t,"e",(function(){return C}));var a=n(16),r=n.n(a),i=n(5),o=n.n(i),c=n(6),s=n.n(c),l=n(12),u=n.n(l),d=n(14),g=n(45),m=n.n(g),f=n(3),p=n(62),b=n(82),v=n(48),h=n(64);function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var _=h.a.clearError,E=h.a.receiveError,k="cannot submit changes while submitting changes",S="cannot submit changes if settings have not changed",j=function(e,t,n){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=a.ownedSettingsSlugs,i=void 0===r?void 0:r,c=a.storeName,l=void 0===c?void 0:c,g=a.settingSlugs,h=void 0===g?[]:g,y=a.initialSettings,k=void 0===y?void 0:y,S=a.validateHaveSettingsChanged,j=void 0===S?C():S;u()(e,"type is required."),u()(t,"identifier is required."),u()(n,"datapoint is required.");var T=l||"".concat(e,"/").concat(t),A={ownedSettingsSlugs:i,settings:k,savedSettings:void 0},N=Object(v.a)({baseName:"getSettings",controlCallback:function(){return m.a.get(e,t,n,{},{useCache:!1})},reducerCallback:function(e,t){return O(O({},e),{},{savedSettings:O({},t),settings:O(O({},t),e.settings||{})})}}),w=Object(v.a)({baseName:"saveSettings",controlCallback:function(a){var r=a.values;return m.a.set(e,t,n,r)},reducerCallback:function(e,t){return O(O({},e),{},{savedSettings:O({},t),settings:O({},t)})},argsToParams:function(e){return{values:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.values;u()(Object(d.isPlainObject)(t),"values is required.")}}),I={},M={setSettings:function(e){return u()(Object(d.isPlainObject)(e),"values is required."),{payload:{values:e},type:"SET_SETTINGS"}},rollbackSettings:function(){return{payload:{},type:"ROLLBACK_SETTINGS"}},rollbackSetting:function(e){return u()(e,"setting is required."),{payload:{setting:e},type:"ROLLBACK_SETTING"}},saveSettings:o.a.mark((function e(){var t,n,a,r,i;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,_("saveSettings",[]);case 5:return n=t.select(T).getSettings(),e.next=8,w.actions.fetchSaveSettings(n);case 8:if(a=e.sent,r=a.response,!(i=a.error)){e.next=14;break}return e.next=14,E(i,"saveSettings",[]);case 14:return e.abrupt("return",{response:r,error:i});case 15:case"end":return e.stop()}}),e)}))},R={},D=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:A,t=arguments.length>1?arguments[1]:void 0,n=t.type,a=t.payload;switch(n){case"SET_SETTINGS":var r=a.values;return O(O({},e),{},{settings:O(O({},e.settings||{}),r)});case"ROLLBACK_SETTINGS":return O(O({},e),{},{settings:e.savedSettings});case"ROLLBACK_SETTING":var i=a.setting;return e.savedSettings[i]?O(O({},e),{},{settings:O(O({},e.settings||{}),{},s()({},i,e.savedSettings[i]))}):O({},e);default:return void 0!==I[n]?I[n](e,{type:n,payload:a}):e}},x={getSettings:o.a.mark((function e(){var t;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.commonActions.getRegistry();case 2:if(t=e.sent,t.select(T).getSettings()){e.next=7;break}return e.next=7,N.actions.fetchGetSettings();case 7:case"end":return e.stop()}}),e)}))},L=Object(p.g)(j),P=L.safeSelector,G=L.dangerousSelector,Z={haveSettingsChanged:P,__dangerousHaveSettingsChanged:G,getSettings:function(e){return e.settings},hasSettingChanged:function(e,t){u()(t,"setting is required.");var n=e.settings,a=e.savedSettings;return!(!n||!a)&&!Object(d.isEqual)(n[t],a[t])},isDoingSaveSettings:function(e){return Object.values(e.isFetchingSaveSettings).some(Boolean)},getOwnedSettingsSlugs:function(e){return e.ownedSettingsSlugs},haveOwnedSettingsChanged:Object(f.createRegistrySelector)((function(e){return function(){var t=e(T).getOwnedSettingsSlugs();return e(T).haveSettingsChanged(t)}}))};h.forEach((function(e){var t=Object(b.b)(e),n=Object(b.a)(e);M["set".concat(t)]=function(e){return u()(void 0!==e,"value is required for calls to set".concat(t,"().")),{payload:{value:e},type:"SET_".concat(n)}},I["SET_".concat(n)]=function(t,n){var a=n.payload.value;return O(O({},t),{},{settings:O(O({},t.settings||{}),{},s()({},e,a))})},Z["get".concat(t)]=Object(f.createRegistrySelector)((function(t){return function(){return(t(T).getSettings()||{})[e]}}))}));var B=Object(f.combineStores)(f.commonStore,N,w,{initialState:A,actions:M,controls:R,reducer:D,resolvers:x,selectors:Z});return O(O({},B),{},{STORE_NAME:T})};function T(e,t){return function(){var n=r()(o.a.mark((function n(a){var r,i,c,s;return o.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(r=a.select,i=a.dispatch,!r(t).haveSettingsChanged()){n.next=8;break}return n.next=4,i(t).saveSettings();case 4:if(c=n.sent,!(s=c.error)){n.next=8;break}return n.abrupt("return",{error:s});case 8:return n.next=10,m.a.invalidateCache("modules",e);case 10:return n.abrupt("return",{});case 11:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}function A(e){return function(t){var n=t.select,a=t.dispatch;return n(e).haveSettingsChanged()?a(e).rollbackSettings():{}}}function N(e){return function(t){var n=Object(p.e)(t)(e),a=n.haveSettingsChanged,r=n.isDoingSubmitChanges;u()(!r(),k),u()(a(),S)}}function C(){return function(e,t,n){var a=t.settings,r=t.savedSettings;n&&u()(!Object(d.isEqual)(Object(d.pick)(a,n),Object(d.pick)(r,n)),S),u()(!Object(d.isEqual)(a,r),S)}}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeSingleRow}));var a=n(1),r=n.n(a),i=n(0);function SettingsNoticeSingleRow(t){var n=t.notice,a=t.LearnMore,r=t.CTA;return e.createElement(i.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),a&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(a,null)),r&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(r,null)))}SettingsNoticeSingleRow.propTypes={notice:r.a.node.isRequired,LearnMore:r.a.elementType,CTA:r.a.elementType}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileWrapper}));var a=n(11),r=n.n(a),i=n(14),o=n(1),c=n.n(o),s=n(0),l=n(2),u=n(145),d=n(455),g=n(456),m=n(303),f=n(381),p=n(135),b=n(35),v=n(9),h=n(18);function MetricTileWrapper(t){var n,a,o,c=t.className,y=t.children,O=t.error,_=t.loading,E=t.moduleSlug,k=t.Widget,S=t.widgetSlug,j=t.title,T=void 0===j?null===(n=u.a[S])||void 0===n?void 0:n.title:j,A=t.infoTooltip,N=void 0===A?(null===(a=u.a[S])||void 0===a?void 0:a.infoTooltip)||(null===(o=u.a[S])||void 0===o?void 0:o.description):A,C=Object(h.a)(),w=!!O&&Object(i.castArray)(O).some(b.e),I=Object(s.useCallback)((function(){Object(v.I)("".concat(C,"_kmw"),"data_loading_error_retry")}),[C]);return Object(s.useEffect)((function(){O&&Object(v.I)("".concat(C,"_kmw"),"data_loading_error")}),[C,O]),O?e.createElement(m.a,{title:w?Object(l.__)("Insufficient permissions","google-site-kit"):Object(l.__)("Data loading failed","google-site-kit"),headerText:T,infoTooltip:N},e.createElement(p.a,{moduleSlug:E,error:O,onRetry:I,GetHelpLink:w?d.a:void 0,getHelpClassName:"googlesitekit-error-retry-text"})):e.createElement(k,{noPadding:!0},e.createElement("div",{className:r()("googlesitekit-km-widget-tile",c)},e.createElement(f.a,{title:T,infoTooltip:N,loading:_}),e.createElement("div",{className:"googlesitekit-km-widget-tile__body"},_&&e.createElement(g.a,null),!_&&y)))}MetricTileWrapper.propTypes={Widget:c.a.elementType.isRequired,loading:c.a.bool,title:c.a.string,infoTooltip:c.a.oneOfType([c.a.string,c.a.element]),moduleSlug:c.a.string.isRequired}}).call(this,n(4))},,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeMultiRow}));var a=n(1),r=n.n(a),i=n(0);function SettingsNoticeMultiRow(t){var n=t.notice,a=t.LearnMore,r=t.CTA,o=t.children;return e.createElement(i.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),e.createElement("div",{className:"googlesitekit-settings-notice__inner-row"},e.createElement("div",{className:"googlesitekit-settings-notice__children-container"},o),a&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(a,null)),r&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(r,null))))}SettingsNoticeMultiRow.propTypes={children:r.a.node.isRequired,notice:r.a.node.isRequired,LearnMore:r.a.elementType,CTA:r.a.elementType}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(0),s=n(17),l=Object(c.forwardRef)((function(t,n){var a=t.id,r=t.className,i=t.children,l=t.secondaryPane;return e.createElement("section",{id:a,className:o()(r,"googlesitekit-publisher-win"),ref:n},e.createElement(s.e,null,e.createElement(s.k,null,i)),l&&e.createElement(c.Fragment,null,e.createElement("div",{className:"googlesitekit-publisher-win__secondary-pane-divider"}),e.createElement(s.e,{className:"googlesitekit-publisher-win__secondary-pane"},e.createElement(s.k,null,e.createElement(s.a,{className:"googlesitekit-publisher-win__secondary-pane",size:12},l)))))}));l.displayName="Banner",l.propTypes={id:r.a.string,className:r.a.string,secondaryPane:r.a.node},t.a=l}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerActions}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(15),s=n.n(c),l=n(1),u=n.n(l),d=n(206),g=n(0),m=n(3),f=n(10),p=n(32);function BannerActions(t){var n=t.ctaLink,a=t.ctaLabel,i=t.ctaComponent,c=t.ctaTarget,l=t.ctaCallback,u=t.dismissLabel,b=t.dismissCallback,v=Object(g.useState)(!1),h=s()(v,2),y=h[0],O=h[1],_=Object(d.a)(),E=Object(m.useSelect)((function(e){return!!n&&e(p.a).isNavigatingTo(n)})),k=function(){var e=o()(r.a.mark((function e(){var t,n,a,i=arguments;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(O(!0),t=i.length,n=new Array(t),a=0;a<t;a++)n[a]=i[a];return e.next=4,null==l?void 0:l.apply(void 0,n);case 4:_()&&O(!1);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return n||u||i?e.createElement("div",{className:"googlesitekit-publisher-win__actions"},i,a&&e.createElement(f.SpinnerButton,{className:"googlesitekit-notification__cta",href:n,target:c,onClick:k,disabled:y||E,isSaving:y||E},a),u&&e.createElement(f.Button,{tertiary:n||i,onClick:b,disabled:y||E},u)):null}BannerActions.propTypes={ctaLink:u.a.string,ctaLabel:u.a.string,ctaComponent:u.a.element,ctaTarget:u.a.string,ctaCallback:u.a.func,dismissLabel:u.a.string,dismissCallback:u.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerTitle}));var a=n(6),r=n.n(a),i=n(1),o=n.n(i),c=n(11),s=n.n(c),l=n(77);function BannerTitle(t){var n=t.title,a=t.badgeLabel,i=t.WinImageSVG,o=t.winImageFormat,c=void 0===o?"":o,u=t.smallWinImageSVGWidth,d=void 0===u?75:u,g=t.smallWinImageSVGHeight,m=void 0===g?75:g;return n?e.createElement("div",{className:"googlesitekit-publisher-win__title-image-wrapper"},e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n,a&&e.createElement(l.a,{label:a})),i&&e.createElement("div",{className:s()(r()({},"googlesitekit-publisher-win__image-".concat(c),c))},e.createElement(i,{width:d,height:m}))):null}BannerTitle.propTypes={title:o.a.string,badgeLabel:o.a.string,WinImageSVG:o.a.elementType,winImageFormat:o.a.string,smallWinImageSVGWidth:o.a.number,smallWinImageSVGHeight:o.a.number}}).call(this,n(4))},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M2 5.309l1.474 2.14c.69 1.001 1.946 1.001 2.636 0L10 1.8",stroke:"currentColor",strokeWidth:1.6,strokeLinecap:"square"});t.a=function SvgCheck2(e){return a.createElement("svg",r({viewBox:"0 0 12 9",fill:"none"},e),i)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"d",(function(){return i})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var a=n(14),r=[{countryCode:"AF",displayName:"Afghanistan",defaultTimeZoneId:"Asia/Kabul",timeZone:[{timeZoneId:"Asia/Kabul",displayName:"(GMT+04:30) Afghanistan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"AL",displayName:"Albania",defaultTimeZoneId:"Europe/Tirane",timeZone:[{timeZoneId:"Europe/Tirane",displayName:"(GMT+02:00) Albania Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"DZ",displayName:"Algeria",defaultTimeZoneId:"Africa/Algiers",timeZone:[{timeZoneId:"Africa/Algiers",displayName:"(GMT+01:00) Algeria Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"AS",displayName:"American Samoa",defaultTimeZoneId:"Pacific/Pago_Pago",timeZone:[{timeZoneId:"Pacific/Pago_Pago",displayName:"(GMT-11:00) American Samoa Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"AD",displayName:"Andorra",defaultTimeZoneId:"Europe/Andorra",timeZone:[{timeZoneId:"Europe/Andorra",displayName:"(GMT+02:00) Andorra Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"AQ",displayName:"Antarctica",defaultTimeZoneId:"Antarctica/Palmer",timeZone:[{timeZoneId:"Antarctica/Palmer",displayName:"(GMT-03:00) Palmer Time"},{timeZoneId:"Antarctica/Rothera",displayName:"(GMT-03:00) Rothera Time"},{timeZoneId:"Antarctica/Syowa",displayName:"(GMT+03:00) Syowa Time"},{timeZoneId:"Antarctica/Mawson",displayName:"(GMT+05:00) Mawson Time"},{timeZoneId:"Antarctica/Vostok",displayName:"(GMT+06:00) Vostok Time"},{timeZoneId:"Antarctica/Davis",displayName:"(GMT+07:00) Davis Time"},{timeZoneId:"Antarctica/Casey",displayName:"(GMT+08:00) Casey Time"},{timeZoneId:"Antarctica/DumontDUrville",displayName:"(GMT+10:00) Dumont d’Urville Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"AR",displayName:"Argentina",defaultTimeZoneId:"America/Buenos_Aires",timeZone:[{timeZoneId:"America/Buenos_Aires",displayName:"(GMT-03:00) Buenos Aires Time"},{timeZoneId:"America/Cordoba",displayName:"(GMT-03:00) Cordoba Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"AM",displayName:"Armenia",defaultTimeZoneId:"Asia/Yerevan",timeZone:[{timeZoneId:"Asia/Yerevan",displayName:"(GMT+04:00) Armenia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"AU",displayName:"Australia",defaultTimeZoneId:"Australia/Perth",timeZone:[{timeZoneId:"Australia/Perth",displayName:"(GMT+08:00) Perth Time"},{timeZoneId:"Australia/Adelaide",displayName:"(GMT+09:30) Adelaide Time"},{timeZoneId:"Australia/Darwin",displayName:"(GMT+09:30) Darwin Time"},{timeZoneId:"Australia/Brisbane",displayName:"(GMT+10:00) Brisbane Time"},{timeZoneId:"Australia/Hobart",displayName:"(GMT+10:00) Hobart Time"},{timeZoneId:"Australia/Melbourne",displayName:"(GMT+10:00) Melbourne Time"},{timeZoneId:"Australia/Sydney",displayName:"(GMT+10:00) Sydney Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"AT",displayName:"Austria",defaultTimeZoneId:"Europe/Vienna",timeZone:[{timeZoneId:"Europe/Vienna",displayName:"(GMT+02:00) Austria Time"}],tosLocale:{language:"de",country:"DE"}},{countryCode:"AZ",displayName:"Azerbaijan",defaultTimeZoneId:"Asia/Baku",timeZone:[{timeZoneId:"Asia/Baku",displayName:"(GMT+04:00) Azerbaijan Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"BS",displayName:"Bahamas",defaultTimeZoneId:"America/Nassau",timeZone:[{timeZoneId:"America/Nassau",displayName:"(GMT-04:00) Bahamas Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"BD",displayName:"Bangladesh",defaultTimeZoneId:"Asia/Dhaka",timeZone:[{timeZoneId:"Asia/Dhaka",displayName:"(GMT+06:00) Bangladesh Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"BB",displayName:"Barbados",defaultTimeZoneId:"America/Barbados",timeZone:[{timeZoneId:"America/Barbados",displayName:"(GMT-04:00) Barbados Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"BY",displayName:"Belarus",defaultTimeZoneId:"Europe/Minsk",timeZone:[{timeZoneId:"Europe/Minsk",displayName:"(GMT+03:00) Belarus Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"BE",displayName:"Belgium",defaultTimeZoneId:"Europe/Brussels",timeZone:[{timeZoneId:"Europe/Brussels",displayName:"(GMT+02:00) Belgium Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"BZ",displayName:"Belize",defaultTimeZoneId:"America/Belize",timeZone:[{timeZoneId:"America/Belize",displayName:"(GMT-06:00) Belize Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"BM",displayName:"Bermuda",defaultTimeZoneId:"Atlantic/Bermuda",timeZone:[{timeZoneId:"Atlantic/Bermuda",displayName:"(GMT-03:00) Bermuda Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"BT",displayName:"Bhutan",defaultTimeZoneId:"Asia/Thimphu",timeZone:[{timeZoneId:"Asia/Thimphu",displayName:"(GMT+06:00) Bhutan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"BO",displayName:"Bolivia",defaultTimeZoneId:"America/La_Paz",timeZone:[{timeZoneId:"America/La_Paz",displayName:"(GMT-04:00) Bolivia Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"BA",displayName:"Bosnia & Herzegovina",defaultTimeZoneId:"Europe/Sarajevo",timeZone:[{timeZoneId:"Europe/Sarajevo",displayName:"(GMT+02:00) Bosnia & Herzegovina Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"BR",displayName:"Brazil",defaultTimeZoneId:"America/Rio_Branco",timeZone:[{timeZoneId:"America/Rio_Branco",displayName:"(GMT-05:00) Rio Branco Time"},{timeZoneId:"America/Boa_Vista",displayName:"(GMT-04:00) Boa Vista Time"},{timeZoneId:"America/Campo_Grande",displayName:"(GMT-04:00) Campo Grande Time"},{timeZoneId:"America/Cuiaba",displayName:"(GMT-04:00) Cuiaba Time"},{timeZoneId:"America/Manaus",displayName:"(GMT-04:00) Manaus Time"},{timeZoneId:"America/Porto_Velho",displayName:"(GMT-04:00) Porto Velho Time"},{timeZoneId:"America/Araguaina",displayName:"(GMT-03:00) Araguaina Time"},{timeZoneId:"America/Bahia",displayName:"(GMT-03:00) Bahia Time"},{timeZoneId:"America/Belem",displayName:"(GMT-03:00) Belem Time"},{timeZoneId:"America/Fortaleza",displayName:"(GMT-03:00) Fortaleza Time"},{timeZoneId:"America/Maceio",displayName:"(GMT-03:00) Maceio Time"},{timeZoneId:"America/Recife",displayName:"(GMT-03:00) Recife Time"},{timeZoneId:"America/Sao_Paulo",displayName:"(GMT-03:00) Sao Paulo Time"},{timeZoneId:"America/Noronha",displayName:"(GMT-02:00) Noronha Time"}],tosLocale:{language:"pt",country:"BR"}},{countryCode:"IO",displayName:"British Indian Ocean Territory",defaultTimeZoneId:"Indian/Chagos",timeZone:[{timeZoneId:"Indian/Chagos",displayName:"(GMT+06:00) British Indian Ocean Territory Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"BN",displayName:"Brunei",defaultTimeZoneId:"Asia/Brunei",timeZone:[{timeZoneId:"Asia/Brunei",displayName:"(GMT+08:00) Brunei Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"BG",displayName:"Bulgaria",defaultTimeZoneId:"Europe/Sofia",timeZone:[{timeZoneId:"Europe/Sofia",displayName:"(GMT+03:00) Bulgaria Time"}],tosLocale:{language:"bg",country:"BG"}},{countryCode:"CA",displayName:"Canada",defaultTimeZoneId:"America/Dawson",timeZone:[{timeZoneId:"America/Dawson",displayName:"(GMT-07:00) Dawson Time"},{timeZoneId:"America/Vancouver",displayName:"(GMT-07:00) Vancouver Time"},{timeZoneId:"America/Whitehorse",displayName:"(GMT-07:00) Whitehorse Time"},{timeZoneId:"America/Edmonton",displayName:"(GMT-06:00) Edmonton Time"},{timeZoneId:"America/Yellowknife",displayName:"(GMT-06:00) Yellowknife Time"},{timeZoneId:"America/Dawson_Creek",displayName:"(GMT-07:00) Dawson Creek Time"},{timeZoneId:"America/Winnipeg",displayName:"(GMT-05:00) Winnipeg Time"},{timeZoneId:"America/Regina",displayName:"(GMT-06:00) Regina Time"},{timeZoneId:"America/Iqaluit",displayName:"(GMT-04:00) Iqaluit Time"},{timeZoneId:"America/Toronto",displayName:"(GMT-04:00) Toronto Time"},{timeZoneId:"America/Halifax",displayName:"(GMT-03:00) Halifax Time"},{timeZoneId:"America/St_Johns",displayName:"(GMT-02:30) St. John’s Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"CV",displayName:"Cape Verde",defaultTimeZoneId:"Atlantic/Cape_Verde",timeZone:[{timeZoneId:"Atlantic/Cape_Verde",displayName:"(GMT-01:00) Cape Verde Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"TD",displayName:"Chad",defaultTimeZoneId:"Africa/Ndjamena",timeZone:[{timeZoneId:"Africa/Ndjamena",displayName:"(GMT+01:00) Chad Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"CL",displayName:"Chile",defaultTimeZoneId:"Pacific/Easter",timeZone:[{timeZoneId:"Pacific/Easter",displayName:"(GMT-06:00) Easter Time"},{timeZoneId:"America/Santiago",displayName:"(GMT-04:00) Chile Time"},{timeZoneId:"America/Punta_Arenas",displayName:"(GMT-03:00) Punta Arenas Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"CN",displayName:"China",defaultTimeZoneId:"Asia/Shanghai",timeZone:[{timeZoneId:"Asia/Shanghai",displayName:"(GMT+08:00) China Time"}],tosLocale:{language:"zh",country:"CN"}},{countryCode:"CX",displayName:"Christmas Island",defaultTimeZoneId:"Indian/Christmas",timeZone:[{timeZoneId:"Indian/Christmas",displayName:"(GMT+07:00) Christmas Island Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"CC",displayName:"Cocos (Keeling) Islands",defaultTimeZoneId:"Indian/Cocos",timeZone:[{timeZoneId:"Indian/Cocos",displayName:"(GMT+06:30) Cocos (Keeling) Islands Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"CO",displayName:"Colombia",defaultTimeZoneId:"America/Bogota",timeZone:[{timeZoneId:"America/Bogota",displayName:"(GMT-05:00) Colombia Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"CK",displayName:"Cook Islands",defaultTimeZoneId:"Pacific/Rarotonga",timeZone:[{timeZoneId:"Pacific/Rarotonga",displayName:"(GMT-10:00) Cook Islands Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"CR",displayName:"Costa Rica",defaultTimeZoneId:"America/Costa_Rica",timeZone:[{timeZoneId:"America/Costa_Rica",displayName:"(GMT-06:00) Costa Rica Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"CI",displayName:"Côte d’Ivoire",defaultTimeZoneId:"Africa/Abidjan",timeZone:[{timeZoneId:"Africa/Abidjan",displayName:"(GMT+00:00) Côte d’Ivoire Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"HR",displayName:"Croatia",defaultTimeZoneId:"Europe/Zagreb",timeZone:[{timeZoneId:"Europe/Zagreb",displayName:"(GMT+02:00) Croatia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"CU",displayName:"Cuba",defaultTimeZoneId:"America/Havana",timeZone:[{timeZoneId:"America/Havana",displayName:"(GMT-04:00) Cuba Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"CW",displayName:"Curaçao",defaultTimeZoneId:"America/Curacao",timeZone:[{timeZoneId:"America/Curacao",displayName:"(GMT-04:00) Curaçao Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"CY",displayName:"Cyprus",defaultTimeZoneId:"Asia/Nicosia",timeZone:[{timeZoneId:"Asia/Nicosia",displayName:"(GMT+03:00) Nicosia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"CZ",displayName:"Czechia",defaultTimeZoneId:"Europe/Prague",timeZone:[{timeZoneId:"Europe/Prague",displayName:"(GMT+02:00) Czechia Time"}],tosLocale:{language:"cs",country:"CZ"}},{countryCode:"DK",displayName:"Denmark",defaultTimeZoneId:"Europe/Copenhagen",timeZone:[{timeZoneId:"Europe/Copenhagen",displayName:"(GMT+02:00) Denmark Time"}],tosLocale:{language:"da",country:"DK"}},{countryCode:"DO",displayName:"Dominican Republic",defaultTimeZoneId:"America/Santo_Domingo",timeZone:[{timeZoneId:"America/Santo_Domingo",displayName:"(GMT-04:00) Dominican Republic Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"EC",displayName:"Ecuador",defaultTimeZoneId:"Pacific/Galapagos",timeZone:[{timeZoneId:"Pacific/Galapagos",displayName:"(GMT-06:00) Galapagos Time"},{timeZoneId:"America/Guayaquil",displayName:"(GMT-05:00) Ecuador Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"EG",displayName:"Egypt",defaultTimeZoneId:"Africa/Cairo",timeZone:[{timeZoneId:"Africa/Cairo",displayName:"(GMT+02:00) Egypt Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SV",displayName:"El Salvador",defaultTimeZoneId:"America/El_Salvador",timeZone:[{timeZoneId:"America/El_Salvador",displayName:"(GMT-06:00) El Salvador Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"EE",displayName:"Estonia",defaultTimeZoneId:"Europe/Tallinn",timeZone:[{timeZoneId:"Europe/Tallinn",displayName:"(GMT+03:00) Estonia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"FK",displayName:"Falkland Islands (Islas Malvinas)",defaultTimeZoneId:"Atlantic/Stanley",timeZone:[{timeZoneId:"Atlantic/Stanley",displayName:"(GMT-03:00) Falkland Islands (Islas Malvinas) Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"FO",displayName:"Faroe Islands",defaultTimeZoneId:"Atlantic/Faeroe",timeZone:[{timeZoneId:"Atlantic/Faeroe",displayName:"(GMT+01:00) Faroe Islands Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"FJ",displayName:"Fiji",defaultTimeZoneId:"Pacific/Fiji",timeZone:[{timeZoneId:"Pacific/Fiji",displayName:"(GMT+12:00) Fiji Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"FI",displayName:"Finland",defaultTimeZoneId:"Europe/Helsinki",timeZone:[{timeZoneId:"Europe/Helsinki",displayName:"(GMT+03:00) Finland Time"}],tosLocale:{language:"fi",country:"FI"}},{countryCode:"FR",displayName:"France",defaultTimeZoneId:"Europe/Paris",timeZone:[{timeZoneId:"Europe/Paris",displayName:"(GMT+02:00) France Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"GF",displayName:"French Guiana",defaultTimeZoneId:"America/Cayenne",timeZone:[{timeZoneId:"America/Cayenne",displayName:"(GMT-03:00) French Guiana Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"PF",displayName:"French Polynesia",defaultTimeZoneId:"Pacific/Tahiti",timeZone:[{timeZoneId:"Pacific/Tahiti",displayName:"(GMT-10:00) Tahiti Time"},{timeZoneId:"Pacific/Marquesas",displayName:"(GMT-09:30) Marquesas Time"},{timeZoneId:"Pacific/Gambier",displayName:"(GMT-09:00) Gambier Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"TF",displayName:"French Southern Territories",defaultTimeZoneId:"Indian/Kerguelen",timeZone:[{timeZoneId:"Indian/Kerguelen",displayName:"(GMT+05:00) French Southern Territories Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"GE",displayName:"Georgia",defaultTimeZoneId:"Asia/Tbilisi",timeZone:[{timeZoneId:"Asia/Tbilisi",displayName:"(GMT+04:00) Georgia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"DE",displayName:"Germany",defaultTimeZoneId:"Europe/Berlin",timeZone:[{timeZoneId:"Europe/Berlin",displayName:"(GMT+02:00) Germany Time"}],tosLocale:{language:"de",country:"DE"}},{countryCode:"GH",displayName:"Ghana",defaultTimeZoneId:"Africa/Accra",timeZone:[{timeZoneId:"Africa/Accra",displayName:"(GMT+00:00) Ghana Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"GI",displayName:"Gibraltar",defaultTimeZoneId:"Europe/Gibraltar",timeZone:[{timeZoneId:"Europe/Gibraltar",displayName:"(GMT+02:00) Gibraltar Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"GR",displayName:"Greece",defaultTimeZoneId:"Europe/Athens",timeZone:[{timeZoneId:"Europe/Athens",displayName:"(GMT+03:00) Greece Time"}],tosLocale:{language:"el",country:"GR"}},{countryCode:"GL",displayName:"Greenland",defaultTimeZoneId:"America/Thule",timeZone:[{timeZoneId:"America/Thule",displayName:"(GMT-03:00) Thule Time"},{timeZoneId:"America/Godthab",displayName:"(GMT-02:00) Nuuk Time"},{timeZoneId:"America/Scoresbysund",displayName:"(GMT+00:00) Ittoqqortoormiit Time"},{timeZoneId:"America/Danmarkshavn",displayName:"(GMT+00:00) Danmarkshavn Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"GU",displayName:"Guam",defaultTimeZoneId:"Pacific/Guam",timeZone:[{timeZoneId:"Pacific/Guam",displayName:"(GMT+10:00) Guam Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"GT",displayName:"Guatemala",defaultTimeZoneId:"America/Guatemala",timeZone:[{timeZoneId:"America/Guatemala",displayName:"(GMT-06:00) Guatemala Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"GW",displayName:"Guinea-Bissau",defaultTimeZoneId:"Africa/Bissau",timeZone:[{timeZoneId:"Africa/Bissau",displayName:"(GMT+00:00) Guinea-Bissau Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"GY",displayName:"Guyana",defaultTimeZoneId:"America/Guyana",timeZone:[{timeZoneId:"America/Guyana",displayName:"(GMT-04:00) Guyana Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"HT",displayName:"Haiti",defaultTimeZoneId:"America/Port-au-Prince",timeZone:[{timeZoneId:"America/Port-au-Prince",displayName:"(GMT-04:00) Haiti Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"HN",displayName:"Honduras",defaultTimeZoneId:"America/Tegucigalpa",timeZone:[{timeZoneId:"America/Tegucigalpa",displayName:"(GMT-06:00) Honduras Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"HK",displayName:"Hong Kong",defaultTimeZoneId:"Asia/Hong_Kong",timeZone:[{timeZoneId:"Asia/Hong_Kong",displayName:"(GMT+08:00) Hong Kong Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"HU",displayName:"Hungary",defaultTimeZoneId:"Europe/Budapest",timeZone:[{timeZoneId:"Europe/Budapest",displayName:"(GMT+02:00) Hungary Time"}],tosLocale:{language:"hu",country:"HU"}},{countryCode:"IS",displayName:"Iceland",defaultTimeZoneId:"Atlantic/Reykjavik",timeZone:[{timeZoneId:"Atlantic/Reykjavik",displayName:"(GMT+00:00) Iceland Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"IN",displayName:"India",defaultTimeZoneId:"Asia/Calcutta",timeZone:[{timeZoneId:"Asia/Calcutta",displayName:"(GMT+05:30) India Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"ID",displayName:"Indonesia",defaultTimeZoneId:"Asia/Jakarta",timeZone:[{timeZoneId:"Asia/Jakarta",displayName:"(GMT+07:00) Jakarta Time"},{timeZoneId:"Asia/Makassar",displayName:"(GMT+08:00) Makassar Time"},{timeZoneId:"Asia/Jayapura",displayName:"(GMT+09:00) Jayapura Time"}],tosLocale:{language:"in",country:"ID"}},{countryCode:"IR",displayName:"Iran",defaultTimeZoneId:"Asia/Tehran",timeZone:[{timeZoneId:"Asia/Tehran",displayName:"(GMT+04:30) Iran Time"}]},{countryCode:"IQ",displayName:"Iraq",defaultTimeZoneId:"Asia/Baghdad",timeZone:[{timeZoneId:"Asia/Baghdad",displayName:"(GMT+03:00) Iraq Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"IE",displayName:"Ireland",defaultTimeZoneId:"Europe/Dublin",timeZone:[{timeZoneId:"Europe/Dublin",displayName:"(GMT+01:00) Ireland Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"IL",displayName:"Israel",defaultTimeZoneId:"Asia/Jerusalem",timeZone:[{timeZoneId:"Asia/Jerusalem",displayName:"(GMT+03:00) Israel Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"IT",displayName:"Italy",defaultTimeZoneId:"Europe/Rome",timeZone:[{timeZoneId:"Europe/Rome",displayName:"(GMT+02:00) Italy Time"}],tosLocale:{language:"it",country:"IT"}},{countryCode:"JM",displayName:"Jamaica",defaultTimeZoneId:"America/Jamaica",timeZone:[{timeZoneId:"America/Jamaica",displayName:"(GMT-05:00) Jamaica Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"JP",displayName:"Japan",defaultTimeZoneId:"Asia/Tokyo",timeZone:[{timeZoneId:"Asia/Tokyo",displayName:"(GMT+09:00) Japan Time"}],tosLocale:{language:"ja",country:"JP"}},{countryCode:"JO",displayName:"Jordan",defaultTimeZoneId:"Asia/Amman",timeZone:[{timeZoneId:"Asia/Amman",displayName:"(GMT+03:00) Jordan Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"KZ",displayName:"Kazakhstan",defaultTimeZoneId:"Asia/Aqtau",timeZone:[{timeZoneId:"Asia/Aqtau",displayName:"(GMT+05:00) Aqtau Time"},{timeZoneId:"Asia/Aqtobe",displayName:"(GMT+05:00) Aqtobe Time"},{timeZoneId:"Asia/Almaty",displayName:"(GMT+06:00) Almaty Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"KE",displayName:"Kenya",defaultTimeZoneId:"Africa/Nairobi",timeZone:[{timeZoneId:"Africa/Nairobi",displayName:"(GMT+03:00) Kenya Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"KI",displayName:"Kiribati",defaultTimeZoneId:"Pacific/Tarawa",timeZone:[{timeZoneId:"Pacific/Tarawa",displayName:"(GMT+12:00) Tarawa Time"},{timeZoneId:"Pacific/Enderbury",displayName:"(GMT+13:00) Enderbury Time"},{timeZoneId:"Pacific/Kiritimati",displayName:"(GMT+14:00) Kiritimati Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"KG",displayName:"Kyrgyzstan",defaultTimeZoneId:"Asia/Bishkek",timeZone:[{timeZoneId:"Asia/Bishkek",displayName:"(GMT+06:00) Kyrgyzstan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"LV",displayName:"Latvia",defaultTimeZoneId:"Europe/Riga",timeZone:[{timeZoneId:"Europe/Riga",displayName:"(GMT+03:00) Latvia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"LB",displayName:"Lebanon",defaultTimeZoneId:"Asia/Beirut",timeZone:[{timeZoneId:"Asia/Beirut",displayName:"(GMT+03:00) Lebanon Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"LR",displayName:"Liberia",defaultTimeZoneId:"Africa/Monrovia",timeZone:[{timeZoneId:"Africa/Monrovia",displayName:"(GMT+00:00) Liberia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"LY",displayName:"Libya",defaultTimeZoneId:"Africa/Tripoli",timeZone:[{timeZoneId:"Africa/Tripoli",displayName:"(GMT+02:00) Libya Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"LT",displayName:"Lithuania",defaultTimeZoneId:"Europe/Vilnius",timeZone:[{timeZoneId:"Europe/Vilnius",displayName:"(GMT+03:00) Lithuania Time"}],tosLocale:{language:"lt",country:"LT"}},{countryCode:"LU",displayName:"Luxembourg",defaultTimeZoneId:"Europe/Luxembourg",timeZone:[{timeZoneId:"Europe/Luxembourg",displayName:"(GMT+02:00) Luxembourg Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MO",displayName:"Macao",defaultTimeZoneId:"Asia/Macau",timeZone:[{timeZoneId:"Asia/Macau",displayName:"(GMT+08:00) Macao Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"MY",displayName:"Malaysia",defaultTimeZoneId:"Asia/Kuala_Lumpur",timeZone:[{timeZoneId:"Asia/Kuala_Lumpur",displayName:"(GMT+08:00) Malaysia Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"MV",displayName:"Maldives",defaultTimeZoneId:"Indian/Maldives",timeZone:[{timeZoneId:"Indian/Maldives",displayName:"(GMT+05:00) Maldives Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"MT",displayName:"Malta",defaultTimeZoneId:"Europe/Malta",timeZone:[{timeZoneId:"Europe/Malta",displayName:"(GMT+02:00) Malta Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MH",displayName:"Marshall Islands",defaultTimeZoneId:"Pacific/Kwajalein",timeZone:[{timeZoneId:"Pacific/Kwajalein",displayName:"(GMT+12:00) Kwajalein Time"},{timeZoneId:"Pacific/Majuro",displayName:"(GMT+12:00) Marshall Islands Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"MQ",displayName:"Martinique",defaultTimeZoneId:"America/Martinique",timeZone:[{timeZoneId:"America/Martinique",displayName:"(GMT-04:00) Martinique Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"MU",displayName:"Mauritius",defaultTimeZoneId:"Indian/Mauritius",timeZone:[{timeZoneId:"Indian/Mauritius",displayName:"(GMT+04:00) Mauritius Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MX",displayName:"Mexico",defaultTimeZoneId:"America/Tijuana",timeZone:[{timeZoneId:"America/Tijuana",displayName:"(GMT-07:00) Tijuana Time"},{timeZoneId:"America/Mazatlan",displayName:"(GMT-06:00) Mazatlan Time"},{timeZoneId:"America/Hermosillo",displayName:"(GMT-07:00) Hermosillo Time"},{timeZoneId:"America/Mexico_City",displayName:"(GMT-05:00) Mexico City Time"},{timeZoneId:"America/Cancun",displayName:"(GMT-05:00) Cancun Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"FM",displayName:"Micronesia",defaultTimeZoneId:"Pacific/Truk",timeZone:[{timeZoneId:"Pacific/Truk",displayName:"(GMT+10:00) Chuuk Time"},{timeZoneId:"Pacific/Kosrae",displayName:"(GMT+11:00) Kosrae Time"},{timeZoneId:"Pacific/Ponape",displayName:"(GMT+11:00) Pohnpei Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"MD",displayName:"Moldova",defaultTimeZoneId:"Europe/Chisinau",timeZone:[{timeZoneId:"Europe/Chisinau",displayName:"(GMT+03:00) Moldova Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MC",displayName:"Monaco",defaultTimeZoneId:"Europe/Monaco",timeZone:[{timeZoneId:"Europe/Monaco",displayName:"(GMT+02:00) Monaco Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MN",displayName:"Mongolia",defaultTimeZoneId:"Asia/Hovd",timeZone:[{timeZoneId:"Asia/Hovd",displayName:"(GMT+07:00) Hovd Time"},{timeZoneId:"Asia/Choibalsan",displayName:"(GMT+08:00) Choibalsan Time"},{timeZoneId:"Asia/Ulaanbaatar",displayName:"(GMT+08:00) Ulaanbaatar Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"MA",displayName:"Morocco",defaultTimeZoneId:"Africa/Casablanca",timeZone:[{timeZoneId:"Africa/Casablanca",displayName:"(GMT+01:00) Morocco Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MZ",displayName:"Mozambique",defaultTimeZoneId:"Africa/Maputo",timeZone:[{timeZoneId:"Africa/Maputo",displayName:"(GMT+02:00) Mozambique Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MM",displayName:"Myanmar (Burma)",defaultTimeZoneId:"Asia/Rangoon",timeZone:[{timeZoneId:"Asia/Rangoon",displayName:"(GMT+06:30) Myanmar (Burma) Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"NA",displayName:"Namibia",defaultTimeZoneId:"Africa/Windhoek",timeZone:[{timeZoneId:"Africa/Windhoek",displayName:"(GMT+02:00) Namibia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"NR",displayName:"Nauru",defaultTimeZoneId:"Pacific/Nauru",timeZone:[{timeZoneId:"Pacific/Nauru",displayName:"(GMT+12:00) Nauru Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"NP",displayName:"Nepal",defaultTimeZoneId:"Asia/Katmandu",timeZone:[{timeZoneId:"Asia/Katmandu",displayName:"(GMT+05:45) Nepal Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"NL",displayName:"Netherlands",defaultTimeZoneId:"Europe/Amsterdam",timeZone:[{timeZoneId:"Europe/Amsterdam",displayName:"(GMT+02:00) Netherlands Time"}],tosLocale:{language:"nl",country:"NL"}},{countryCode:"NC",displayName:"New Caledonia",defaultTimeZoneId:"Pacific/Noumea",timeZone:[{timeZoneId:"Pacific/Noumea",displayName:"(GMT+11:00) New Caledonia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"NZ",displayName:"New Zealand",defaultTimeZoneId:"Pacific/Auckland",timeZone:[{timeZoneId:"Pacific/Auckland",displayName:"(GMT+12:00) New Zealand Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"NI",displayName:"Nicaragua",defaultTimeZoneId:"America/Managua",timeZone:[{timeZoneId:"America/Managua",displayName:"(GMT-06:00) Nicaragua Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"NG",displayName:"Nigeria",defaultTimeZoneId:"Africa/Lagos",timeZone:[{timeZoneId:"Africa/Lagos",displayName:"(GMT+01:00) Nigeria Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"NU",displayName:"Niue",defaultTimeZoneId:"Pacific/Niue",timeZone:[{timeZoneId:"Pacific/Niue",displayName:"(GMT-11:00) Niue Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"NF",displayName:"Norfolk Island",defaultTimeZoneId:"Pacific/Norfolk",timeZone:[{timeZoneId:"Pacific/Norfolk",displayName:"(GMT+11:00) Norfolk Island Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"KP",displayName:"North Korea",defaultTimeZoneId:"Asia/Pyongyang",timeZone:[{timeZoneId:"Asia/Pyongyang",displayName:"(GMT+09:00) North Korea Time"}]},{countryCode:"MK",displayName:"North Macedonia",defaultTimeZoneId:"Europe/Skopje",timeZone:[{timeZoneId:"Europe/Skopje",displayName:"(GMT+02:00) North Macedonia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"NO",displayName:"Norway",defaultTimeZoneId:"Europe/Oslo",timeZone:[{timeZoneId:"Europe/Oslo",displayName:"(GMT+02:00) Norway Time"}],tosLocale:{language:"no",country:"NO"}},{countryCode:"PK",displayName:"Pakistan",defaultTimeZoneId:"Asia/Karachi",timeZone:[{timeZoneId:"Asia/Karachi",displayName:"(GMT+05:00) Pakistan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"PW",displayName:"Palau",defaultTimeZoneId:"Pacific/Palau",timeZone:[{timeZoneId:"Pacific/Palau",displayName:"(GMT+09:00) Palau Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"PS",displayName:"Palestine",defaultTimeZoneId:"Asia/Gaza",timeZone:[{timeZoneId:"Asia/Gaza",displayName:"(GMT+03:00) Gaza Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"PA",displayName:"Panama",defaultTimeZoneId:"America/Panama",timeZone:[{timeZoneId:"America/Panama",displayName:"(GMT-05:00) Panama Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"PG",displayName:"Papua New Guinea",defaultTimeZoneId:"Pacific/Port_Moresby",timeZone:[{timeZoneId:"Pacific/Port_Moresby",displayName:"(GMT+10:00) Port Moresby Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"PY",displayName:"Paraguay",defaultTimeZoneId:"America/Asuncion",timeZone:[{timeZoneId:"America/Asuncion",displayName:"(GMT-04:00) Paraguay Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"PE",displayName:"Peru",defaultTimeZoneId:"America/Lima",timeZone:[{timeZoneId:"America/Lima",displayName:"(GMT-05:00) Peru Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"PH",displayName:"Philippines",defaultTimeZoneId:"Asia/Manila",timeZone:[{timeZoneId:"Asia/Manila",displayName:"(GMT+08:00) Philippines Time"}],tosLocale:{language:"tl",country:"PH"}},{countryCode:"PN",displayName:"Pitcairn Islands",defaultTimeZoneId:"Pacific/Pitcairn",timeZone:[{timeZoneId:"Pacific/Pitcairn",displayName:"(GMT-08:00) Pitcairn Islands Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"PL",displayName:"Poland",defaultTimeZoneId:"Europe/Warsaw",timeZone:[{timeZoneId:"Europe/Warsaw",displayName:"(GMT+02:00) Poland Time"}],tosLocale:{language:"pl",country:"PL"}},{countryCode:"PT",displayName:"Portugal",defaultTimeZoneId:"Atlantic/Azores",timeZone:[{timeZoneId:"Atlantic/Azores",displayName:"(GMT+00:00) Azores Time"},{timeZoneId:"Europe/Lisbon",displayName:"(GMT+01:00) Portugal Time"}],tosLocale:{language:"pt",country:"PT"}},{countryCode:"PR",displayName:"Puerto Rico",defaultTimeZoneId:"America/Puerto_Rico",timeZone:[{timeZoneId:"America/Puerto_Rico",displayName:"(GMT-04:00) Puerto Rico Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"QA",displayName:"Qatar",defaultTimeZoneId:"Asia/Qatar",timeZone:[{timeZoneId:"Asia/Qatar",displayName:"(GMT+03:00) Qatar Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"RE",displayName:"Réunion",defaultTimeZoneId:"Indian/Reunion",timeZone:[{timeZoneId:"Indian/Reunion",displayName:"(GMT+04:00) Réunion Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"RO",displayName:"Romania",defaultTimeZoneId:"Europe/Bucharest",timeZone:[{timeZoneId:"Europe/Bucharest",displayName:"(GMT+03:00) Romania Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"RU",displayName:"Russia",defaultTimeZoneId:"Europe/Kaliningrad",timeZone:[{timeZoneId:"Europe/Kaliningrad",displayName:"(GMT+02:00) Kaliningrad Time"},{timeZoneId:"Europe/Moscow",displayName:"(GMT+03:00) Moscow Time"},{timeZoneId:"Europe/Samara",displayName:"(GMT+04:00) Samara Time"},{timeZoneId:"Asia/Yekaterinburg",displayName:"(GMT+05:00) Yekaterinburg Time"},{timeZoneId:"Asia/Omsk",displayName:"(GMT+06:00) Omsk Time"},{timeZoneId:"Asia/Krasnoyarsk",displayName:"(GMT+07:00) Krasnoyarsk Time"},{timeZoneId:"Asia/Irkutsk",displayName:"(GMT+08:00) Irkutsk Time"},{timeZoneId:"Asia/Yakutsk",displayName:"(GMT+09:00) Yakutsk Time"},{timeZoneId:"Asia/Vladivostok",displayName:"(GMT+10:00) Vladivostok Time"},{timeZoneId:"Asia/Magadan",displayName:"(GMT+11:00) Magadan Time"},{timeZoneId:"Asia/Kamchatka",displayName:"(GMT+12:00) Kamchatka Time"}],tosLocale:{language:"ru",country:"RU"}},{countryCode:"WS",displayName:"Samoa",defaultTimeZoneId:"Pacific/Apia",timeZone:[{timeZoneId:"Pacific/Apia",displayName:"(GMT+13:00) Samoa Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"SM",displayName:"San Marino",defaultTimeZoneId:"Europe/San_Marino",timeZone:[{timeZoneId:"Europe/San_Marino",displayName:"(GMT+02:00) San Marino Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"ST",displayName:"São Tomé & Príncipe",defaultTimeZoneId:"Africa/Sao_Tome",timeZone:[{timeZoneId:"Africa/Sao_Tome",displayName:"(GMT+00:00) São Tomé & Príncipe Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SA",displayName:"Saudi Arabia",defaultTimeZoneId:"Asia/Riyadh",timeZone:[{timeZoneId:"Asia/Riyadh",displayName:"(GMT+03:00) Saudi Arabia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"RS",displayName:"Serbia",defaultTimeZoneId:"Europe/Belgrade",timeZone:[{timeZoneId:"Europe/Belgrade",displayName:"(GMT+02:00) Serbia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SC",displayName:"Seychelles",defaultTimeZoneId:"Indian/Mahe",timeZone:[{timeZoneId:"Indian/Mahe",displayName:"(GMT+04:00) Seychelles Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SG",displayName:"Singapore",defaultTimeZoneId:"Asia/Singapore",timeZone:[{timeZoneId:"Asia/Singapore",displayName:"(GMT+08:00) Singapore Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"SK",displayName:"Slovakia",defaultTimeZoneId:"Europe/Bratislava",timeZone:[{timeZoneId:"Europe/Bratislava",displayName:"(GMT+02:00) Slovakia Time"}],tosLocale:{language:"sk",country:"SK"}},{countryCode:"SI",displayName:"Slovenia",defaultTimeZoneId:"Europe/Ljubljana",timeZone:[{timeZoneId:"Europe/Ljubljana",displayName:"(GMT+02:00) Slovenia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SB",displayName:"Solomon Islands",defaultTimeZoneId:"Pacific/Guadalcanal",timeZone:[{timeZoneId:"Pacific/Guadalcanal",displayName:"(GMT+11:00) Solomon Islands Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"ZA",displayName:"South Africa",defaultTimeZoneId:"Africa/Johannesburg",timeZone:[{timeZoneId:"Africa/Johannesburg",displayName:"(GMT+02:00) South Africa Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"GS",displayName:"South Georgia & South Sandwich Islands",defaultTimeZoneId:"Atlantic/South_Georgia",timeZone:[{timeZoneId:"Atlantic/South_Georgia",displayName:"(GMT-02:00) South Georgia & South Sandwich Islands Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"KR",displayName:"South Korea",defaultTimeZoneId:"Asia/Seoul",timeZone:[{timeZoneId:"Asia/Seoul",displayName:"(GMT+09:00) South Korea Time"}],tosLocale:{language:"ko",country:"KR"}},{countryCode:"ES",displayName:"Spain",defaultTimeZoneId:"Atlantic/Canary",timeZone:[{timeZoneId:"Atlantic/Canary",displayName:"(GMT+01:00) Canary Time"},{timeZoneId:"Africa/Ceuta",displayName:"(GMT+02:00) Ceuta Time"},{timeZoneId:"Europe/Madrid",displayName:"(GMT+02:00) Spain Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"LK",displayName:"Sri Lanka",defaultTimeZoneId:"Asia/Colombo",timeZone:[{timeZoneId:"Asia/Colombo",displayName:"(GMT+05:30) Sri Lanka Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"PM",displayName:"St. Pierre & Miquelon",defaultTimeZoneId:"America/Miquelon",timeZone:[{timeZoneId:"America/Miquelon",displayName:"(GMT-02:00) St. Pierre & Miquelon Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SD",displayName:"Sudan",defaultTimeZoneId:"Africa/Khartoum",timeZone:[{timeZoneId:"Africa/Khartoum",displayName:"(GMT+02:00) Sudan Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SR",displayName:"Suriname",defaultTimeZoneId:"America/Paramaribo",timeZone:[{timeZoneId:"America/Paramaribo",displayName:"(GMT-03:00) Suriname Time"}],tosLocale:{language:"nl",country:"NL"}},{countryCode:"SJ",displayName:"Svalbard & Jan Mayen",defaultTimeZoneId:"Arctic/Longyearbyen",timeZone:[{timeZoneId:"Arctic/Longyearbyen",displayName:"(GMT+02:00) Svalbard & Jan Mayen Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SE",displayName:"Sweden",defaultTimeZoneId:"Europe/Stockholm",timeZone:[{timeZoneId:"Europe/Stockholm",displayName:"(GMT+02:00) Sweden Time"}],tosLocale:{language:"sv",country:"SE"}},{countryCode:"CH",displayName:"Switzerland",defaultTimeZoneId:"Europe/Zurich",timeZone:[{timeZoneId:"Europe/Zurich",displayName:"(GMT+02:00) Switzerland Time"}],tosLocale:{language:"de",country:"DE"}},{countryCode:"SY",displayName:"Syria",defaultTimeZoneId:"Asia/Damascus",timeZone:[{timeZoneId:"Asia/Damascus",displayName:"(GMT+03:00) Syria Time"}]},{countryCode:"TW",displayName:"Taiwan",defaultTimeZoneId:"Asia/Taipei",timeZone:[{timeZoneId:"Asia/Taipei",displayName:"(GMT+08:00) Taiwan Time"}],tosLocale:{language:"zh",country:"TW"}},{countryCode:"TJ",displayName:"Tajikistan",defaultTimeZoneId:"Asia/Dushanbe",timeZone:[{timeZoneId:"Asia/Dushanbe",displayName:"(GMT+05:00) Tajikistan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"TH",displayName:"Thailand",defaultTimeZoneId:"Asia/Bangkok",timeZone:[{timeZoneId:"Asia/Bangkok",displayName:"(GMT+07:00) Thailand Time"}],tosLocale:{language:"th",country:"TH"}},{countryCode:"TL",displayName:"Timor-Leste",defaultTimeZoneId:"Asia/Dili",timeZone:[{timeZoneId:"Asia/Dili",displayName:"(GMT+09:00) Timor-Leste Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"TK",displayName:"Tokelau",defaultTimeZoneId:"Pacific/Fakaofo",timeZone:[{timeZoneId:"Pacific/Fakaofo",displayName:"(GMT+13:00) Tokelau Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"TO",displayName:"Tonga",defaultTimeZoneId:"Pacific/Tongatapu",timeZone:[{timeZoneId:"Pacific/Tongatapu",displayName:"(GMT+13:00) Tonga Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"TT",displayName:"Trinidad & Tobago",defaultTimeZoneId:"America/Port_of_Spain",timeZone:[{timeZoneId:"America/Port_of_Spain",displayName:"(GMT-04:00) Trinidad & Tobago Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"TN",displayName:"Tunisia",defaultTimeZoneId:"Africa/Tunis",timeZone:[{timeZoneId:"Africa/Tunis",displayName:"(GMT+01:00) Tunisia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"TR",displayName:"Turkey",defaultTimeZoneId:"Europe/Istanbul",timeZone:[{timeZoneId:"Europe/Istanbul",displayName:"(GMT+03:00) Turkey Time"}],tosLocale:{language:"tr",country:"TR"}},{countryCode:"TM",displayName:"Turkmenistan",defaultTimeZoneId:"Asia/Ashgabat",timeZone:[{timeZoneId:"Asia/Ashgabat",displayName:"(GMT+05:00) Turkmenistan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"TC",displayName:"Turks & Caicos Islands",defaultTimeZoneId:"America/Grand_Turk",timeZone:[{timeZoneId:"America/Grand_Turk",displayName:"(GMT-04:00) Turks & Caicos Islands Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"TV",displayName:"Tuvalu",defaultTimeZoneId:"Pacific/Funafuti",timeZone:[{timeZoneId:"Pacific/Funafuti",displayName:"(GMT+12:00) Tuvalu Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"UM",displayName:"U.S. Outlying Islands",defaultTimeZoneId:"Pacific/Wake",timeZone:[{timeZoneId:"Pacific/Wake",displayName:"(GMT+12:00) Wake Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"UA",displayName:"Ukraine",defaultTimeZoneId:"Europe/Kiev",timeZone:[{timeZoneId:"Europe/Kiev",displayName:"(GMT+03:00) Ukraine Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"AE",displayName:"United Arab Emirates",defaultTimeZoneId:"Asia/Dubai",timeZone:[{timeZoneId:"Asia/Dubai",displayName:"(GMT+04:00) United Arab Emirates Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"GB",displayName:"United Kingdom",defaultTimeZoneId:"Etc/GMT",timeZone:[{timeZoneId:"Etc/GMT",displayName:"(GMT+00:00) GMT"},{timeZoneId:"Europe/London",displayName:"(GMT+01:00) United Kingdom Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"US",displayName:"United States",defaultTimeZoneId:"America/Los_Angeles",timeZone:[{timeZoneId:"Pacific/Honolulu",displayName:"(GMT-10:00) Honolulu Time"},{timeZoneId:"America/Anchorage",displayName:"(GMT-08:00) Anchorage Time"},{timeZoneId:"America/Los_Angeles",displayName:"(GMT-07:00) Los Angeles Time"},{timeZoneId:"America/Boise",displayName:"(GMT-06:00) Boise Time"},{timeZoneId:"America/Denver",displayName:"(GMT-06:00) Denver Time"},{timeZoneId:"America/Phoenix",displayName:"(GMT-07:00) Phoenix Time"},{timeZoneId:"America/Chicago",displayName:"(GMT-05:00) Chicago Time"},{timeZoneId:"America/Detroit",displayName:"(GMT-04:00) Detroit Time"},{timeZoneId:"America/New_York",displayName:"(GMT-04:00) New York Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"UY",displayName:"Uruguay",defaultTimeZoneId:"America/Montevideo",timeZone:[{timeZoneId:"America/Montevideo",displayName:"(GMT-03:00) Uruguay Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"UZ",displayName:"Uzbekistan",defaultTimeZoneId:"Asia/Tashkent",timeZone:[{timeZoneId:"Asia/Tashkent",displayName:"(GMT+05:00) Uzbekistan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"VU",displayName:"Vanuatu",defaultTimeZoneId:"Pacific/Efate",timeZone:[{timeZoneId:"Pacific/Efate",displayName:"(GMT+11:00) Vanuatu Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"VA",displayName:"Vatican City",defaultTimeZoneId:"Europe/Vatican",timeZone:[{timeZoneId:"Europe/Vatican",displayName:"(GMT+02:00) Vatican City Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"VE",displayName:"Venezuela",defaultTimeZoneId:"America/Caracas",timeZone:[{timeZoneId:"America/Caracas",displayName:"(GMT-04:00) Venezuela Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"VN",displayName:"Vietnam",defaultTimeZoneId:"Asia/Saigon",timeZone:[{timeZoneId:"Asia/Saigon",displayName:"(GMT+07:00) Vietnam Time"}],tosLocale:{language:"vi",country:"VN"}},{countryCode:"WF",displayName:"Wallis & Futuna",defaultTimeZoneId:"Pacific/Wallis",timeZone:[{timeZoneId:"Pacific/Wallis",displayName:"(GMT+12:00) Wallis & Futuna Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"EH",displayName:"Western Sahara",defaultTimeZoneId:"Africa/El_Aaiun",timeZone:[{timeZoneId:"Africa/El_Aaiun",displayName:"(GMT+01:00) Western Sahara Time"}],tosLocale:{language:"en",country:"GB"}}],i=r.reduce((function(e,t){return e[t.countryCode]=t.timeZone,e}),{}),o=Object(a.keyBy)(r,"countryCode"),c=r.reduce((function(e,t){return t.timeZone.forEach((function(n){var a=n.timeZoneId;return e[a]=t.countryCode})),e}),{})},function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var a=n(5),r=n.n(a),i=n(6),o=n.n(i),c=n(16),s=n.n(c),l=n(0),u=n(3),d=n(13),g=n(23);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e){var t=Object(u.useDispatch)(g.b).setValue,n=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.2")})),a=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.4")}));return Object(l.useCallback)(s()(r.a.mark((function i(){var o,c,s,l;return r.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(o=document.querySelector("#adminmenu").offsetHeight>0){r.next=7;break}if(!(c=document.getElementById("wp-admin-bar-menu-toggle"))){r.next=7;break}return c.firstChild.click(),r.next=7,new Promise((function(e){setTimeout(e,0)}));case 7:"#adminmenu [href*='page=googlesitekit-dashboard']",(s=!!document.querySelector("".concat("#adminmenu [href*='page=googlesitekit-dashboard']","[aria-haspopup=true]")))&&document.querySelector("#adminmenu [href*='page=googlesitekit-dashboard']").click(),n&&!a&&(l=document.hasFocus,document.hasFocus=function(){return document.hasFocus=l,!1}),t("admin-menu-tooltip",f({isTooltipVisible:!0,rehideAdminMenu:!o,rehideAdminSubMenu:s},e));case 12:case"end":return r.stop()}}),i)}))),[n,a,t,e])}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WarningNotice}));var a=n(11),r=n.n(a),i=n(1),o=n.n(i);function WarningNotice(t){var n=t.children,a=t.className;return e.createElement("div",{className:r()("googlesitekit-warning-notice",a)},n)}WarningNotice.propTypes={children:o.a.node.isRequired,className:o.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OptIn}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),g=n(38),m=n(2),f=n(3),p=n(10),b=n(7),v=n(36),h=n(20),y=n(18);function OptIn(t){var n=t.id,a=void 0===n?"googlesitekit-opt-in":n,i=t.name,c=void 0===i?"optIn":i,s=t.className,l=t.trackEventCategory,O=t.alignLeftCheckbox,_=void 0!==O&&O,E=Object(f.useSelect)((function(e){return e(b.a).isTrackingEnabled()})),k=Object(f.useSelect)((function(e){return e(b.a).isSavingTrackingEnabled()})),S=Object(f.useSelect)((function(e){return e(b.a).getErrorForAction("setTrackingEnabled",[!E])})),j=Object(f.useDispatch)(b.a).setTrackingEnabled,T=Object(y.a)(),A=Object(d.useCallback)(function(){var e=o()(r.a.mark((function e(t){var n,a;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,j(!!t.target.checked);case 2:n=e.sent,a=n.response,n.error||(Object(v.a)(a.enabled),a.enabled&&Object(v.b)(l||T,"tracking_optin"));case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[j,l,T]);return e.createElement("div",{className:u()("googlesitekit-opt-in",s)},e.createElement(p.Checkbox,{id:a,name:c,value:"1",checked:E,disabled:k,onChange:A,loading:void 0===E,alignLeft:_},Object(g.a)(Object(m.__)("<span>Help us improve Site Kit by sharing anonymous usage data.</span> <span>All collected data is treated in accordance with the <a>Google Privacy Policy.</a></span>","google-site-kit"),{a:e.createElement(h.a,{key:"link",href:"https://policies.google.com/privacy",external:!0}),span:e.createElement("span",null)})),(null==S?void 0:S.message)&&e.createElement("div",{className:"googlesitekit-error-text"},null==S?void 0:S.message))}OptIn.propTypes={id:s.a.string,name:s.a.string,className:s.a.string,trackEventCategory:s.a.string,alignLeftCheckbox:s.a.bool}}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Item}));var a=n(1),r=n.n(a);function Item(t){var n=t.icon,a=t.label;return e.createElement("div",{className:"googlesitekit-user-menu__item"},e.createElement("div",{className:"googlesitekit-user-menu__item-icon"},n),e.createElement("span",{className:"googlesitekit-user-menu__item-label"},a))}Item.propTypes={icon:r.a.node,label:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdminMenuTooltip}));var a=n(0),r=n(3),i=n(223),o=n(23),c=n(9),s=n(18);function AdminMenuTooltip(){var t=Object(s.a)(),n=Object(r.useDispatch)(o.b).setValue,l=Object(r.useSelect)((function(e){return e(o.b).getValue("admin-menu-tooltip")||{isTooltipVisible:!1}})),u=l.isTooltipVisible,d=void 0!==u&&u,g=l.rehideAdminMenu,m=void 0!==g&&g,f=l.rehideAdminSubMenu,p=void 0!==f&&f,b=l.tooltipSlug,v=l.title,h=l.content,y=l.dismissLabel,O=Object(a.useCallback)((function(){var e;m&&(document.querySelector("#adminmenu").offsetHeight>0&&(null===(e=document.getElementById("wp-admin-bar-menu-toggle"))||void 0===e||e.click()));p&&document.querySelector("body").click(),b&&Object(c.I)("".concat(t,"_").concat(b),"tooltip_dismiss"),n("admin-menu-tooltip",void 0)}),[m,p,n,b,t]);return d?e.createElement(i.a,{target:'#adminmenu [href*="page=googlesitekit-settings"]',slug:"ga4-activation-banner-admin-menu-tooltip",title:v,content:h,dismissLabel:y,onView:function(){Object(c.I)("".concat(t,"_").concat(b),"tooltip_view")},onDismiss:O}):null}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var a=n(1),r=n.n(a),i=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var a="win-warning"===n?e.createElement(i.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},a))}BannerIcon.propTypes={type:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return JoyrideTooltip}));var r=n(6),i=n.n(r),o=n(15),c=n.n(o),s=n(1),l=n(30),u=n(421),d=n(0),g=n(107),m=n(72),f=n(90);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function JoyrideTooltip(t){var n=t.title,r=t.content,i=t.dismissLabel,o=t.target,s=t.cta,p=void 0!==s&&s,v=t.className,h=t.styles,y=void 0===h?{}:h,O=t.slug,_=void 0===O?"":O,E=t.onDismiss,k=void 0===E?function(){}:E,S=t.onView,j=void 0===S?function(){}:S,T=t.onTourStart,A=void 0===T?function(){}:T,N=t.onTourEnd,C=void 0===N?function(){}:N,w=function(){return!!e.document.querySelector(o)},I=Object(d.useState)(w),M=c()(I,2),R=M[0],D=M[1];if(Object(u.a)((function(){w()&&D(!0)}),R?null:250),Object(d.useEffect)((function(){if(R&&e.ResizeObserver){var t=e.document.querySelector(o),n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}),[o,R]),!R)return null;var x=[{title:n,target:o,content:r,disableBeacon:!0,isFixed:!0,placement:"auto",cta:p,className:v}],L={close:i,last:i};return a.createElement(m.a,{slug:_},a.createElement(l.e,{callback:function(t){switch(t.type){case l.b.TOUR_START:A(),e.document.body.classList.add("googlesitekit-showing-tooltip");break;case l.b.TOUR_END:C(),e.document.body.classList.remove("googlesitekit-showing-tooltip");break;case l.b.STEP_AFTER:k();break;case l.b.TOOLTIP:j()}},disableOverlay:!0,disableScrolling:!0,spotlightPadding:0,floaterProps:f.b,locale:L,steps:x,styles:b(b(b({},f.c),y),{},{options:b(b({},f.c.options),null==y?void 0:y.options),spotlight:b(b({},f.c.spotlight),null==y?void 0:y.spotlight)}),tooltipComponent:g.a,run:!0}))}JoyrideTooltip.propTypes={title:s.PropTypes.node,content:s.PropTypes.string,dismissLabel:s.PropTypes.string,target:s.PropTypes.string.isRequired,onDismiss:s.PropTypes.func,onShow:s.PropTypes.func,className:s.PropTypes.string,styles:s.PropTypes.object,slug:s.PropTypes.string,onView:s.PropTypes.func}}).call(this,n(28),n(4))},,,,,,,,,,,function(e,t,n){"use strict";(function(e){var a=n(15),r=n.n(a),i=n(1),o=n.n(i),c=n(11),s=n.n(c),l=n(590),u=n(2),d=n(0),g=n(3),m=n(256),f=n(276),p=n(280),b=n(7),v=n(17),h=n(284),y=n(291),O=n(293),_=n(34),E=n(52),k=n(20),S=n(299),j=n(13),T=n(300);function Header(t){var n,a=t.children,i=t.subHeader,o=t.showNavigation,c=!!Object(E.c)(),A=Object(_.a)();Object(T.a)();var N=Object(g.useSelect)((function(e){return e(j.c).getAdminURL("googlesitekit-dashboard")})),C=Object(g.useSelect)((function(e){return e(b.a).isAuthenticated()})),w=Object(l.a)({childList:!0}),I=r()(w,2),M=I[0],R=!!(null===(n=I[1].target)||void 0===n?void 0:n.childElementCount);return e.createElement(d.Fragment,null,e.createElement("header",{className:s()("googlesitekit-header",{"googlesitekit-header--has-subheader":R,"googlesitekit-header--has-navigation":o})},e.createElement(v.e,null,e.createElement(v.k,null,e.createElement(v.a,{smSize:1,mdSize:2,lgSize:4,className:"googlesitekit-header__logo",alignMiddle:!0},e.createElement(k.a,{"aria-label":Object(u.__)("Go to dashboard","google-site-kit"),className:"googlesitekit-header__logo-link",href:N},e.createElement(m.a,null))),e.createElement(v.a,{smSize:3,mdSize:6,lgSize:8,className:"googlesitekit-header__children",alignMiddle:!0},a,!C&&c&&A&&e.createElement(O.a,null),C&&!A&&e.createElement(f.a,null))))),e.createElement("div",{className:"googlesitekit-subheader",ref:M},e.createElement(p.a,null),i),o&&e.createElement(h.a,null),c&&e.createElement(S.a,null),e.createElement(y.a,null))}Header.displayName="Header",Header.propTypes={children:o.a.node,subHeader:o.a.element,showNavigation:o.a.bool},Header.defaultProps={children:null,subHeader:null},t.a=Header}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return HelpMenu}));var a=n(15),r=n.n(a),i=n(1),o=n.n(i),c=n(209),s=n(0),l=n(56),u=n(2),d=n(3),g=n(10),m=n(301),f=n(112),p=n(9),b=n(164),v=n(19),h=n(18),y=n(13);function HelpMenu(t){var n=t.children,a=Object(s.useState)(!1),i=r()(a,2),o=i[0],O=i[1],_=Object(s.useRef)(),E=Object(h.a)();Object(c.a)(_,(function(){return O(!1)})),Object(f.a)([l.c,l.f],_,(function(){return O(!1)}));var k=Object(d.useSelect)((function(e){return e(v.a).isModuleActive("adsense")})),S=Object(s.useCallback)((function(){o||Object(p.I)("".concat(E,"_headerbar"),"open_helpmenu"),O(!o)}),[o,E]),j=Object(s.useCallback)((function(){O(!1)}),[]),T=Object(d.useSelect)((function(e){return e(y.c).getDocumentationLinkURL("fix-common-issues")}));return e.createElement("div",{ref:_,className:"googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},e.createElement(g.Button,{"aria-controls":"googlesitekit-help-menu","aria-expanded":o,"aria-label":Object(u.__)("Help","google-site-kit"),"aria-haspopup":"menu",className:"googlesitekit-header__dropdown googlesitekit-border-radius-round googlesitekit-button-icon googlesitekit-help-menu__button mdc-button--dropdown",icon:e.createElement(m.a,{width:"20",height:"20"}),onClick:S,text:!0,tooltipEnterDelayInMS:500}),e.createElement(g.Menu,{className:"googlesitekit-width-auto",menuOpen:o,id:"googlesitekit-help-menu",onSelected:j},n,e.createElement(b.a,{gaEventLabel:"fix_common_issues",href:T},Object(u.__)("Fix common issues","google-site-kit")),e.createElement(b.a,{gaEventLabel:"documentation",href:"https://sitekit.withgoogle.com/documentation/"},Object(u.__)("Read help docs","google-site-kit")),e.createElement(b.a,{gaEventLabel:"support_forum",href:"https://wordpress.org/support/plugin/google-site-kit/"},Object(u.__)("Get support","google-site-kit")),k&&e.createElement(b.a,{gaEventLabel:"adsense_help",href:"https://support.google.com/adsense/"},Object(u.__)("Get help with AdSense","google-site-kit"))))}HelpMenu.propTypes={children:o.a.node}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(0),s=n(114),l=n(9),u=n(402),d=n(77),g=n(403),m=n(134);function DataBlock(t){var n=t.stat,a=void 0===n?null:n,r=t.className,i=void 0===r?"":r,f=t.title,p=void 0===f?"":f,b=t.datapoint,v=void 0===b?null:b,h=t.datapointUnit,y=void 0===h?"":h,O=t.change,_=void 0===O?null:O,E=t.changeDataUnit,k=void 0===E?"":E,S=t.context,j=void 0===S?"default":S,T=t.period,A=void 0===T?"":T,N=t.selected,C=void 0!==N&&N,w=t.source,I=t.sparkline,M=t.handleStatSelection,R=void 0===M?null:M,D=t.invertChangeColor,x=void 0!==D&&D,L=t.gatheringData,P=void 0!==L&&L,G=t.gatheringDataNoticeStyle,Z=void 0===G?s.a.DEFAULT:G,B=t.badge,U=Object(c.useCallback)((function(){!P&&R&&R(a)}),[P,R,a]),F=Object(c.useCallback)((function(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),U())}),[U]),z=void 0===v?v:Object(l.B)(v,y),V="button"===j,H=V?"button":"";return e.createElement("div",{className:o()("googlesitekit-data-block",i,"googlesitekit-data-block--".concat(j),{"googlesitekit-data-block--selected":C,"googlesitekit-data-block--is-gathering-data":P}),tabIndex:V&&!P?"0":"-1",role:R&&H,onClick:U,onKeyDown:F,"aria-disabled":P||void 0,"aria-label":R&&p,"aria-pressed":R&&C},e.createElement("div",{className:"googlesitekit-data-block__title-datapoint-wrapper"},e.createElement("h3",{className:" googlesitekit-subheading-1 googlesitekit-data-block__title "},!0===B?e.createElement(d.a,{"aria-hidden":"true",className:"googlesitekit-badge--hidden",label:"X"}):B,e.createElement("span",{className:"googlesitekit-data-block__title-inner"},p)),!P&&e.createElement("div",{className:"googlesitekit-data-block__datapoint"},z)),!P&&I&&e.createElement(u.a,{sparkline:I,invertChangeColor:x}),!P&&e.createElement("div",{className:"googlesitekit-data-block__change-source-wrapper"},e.createElement(g.a,{change:_,changeDataUnit:k,period:A,invertChangeColor:x}),w&&e.createElement(m.a,{className:"googlesitekit-data-block__source",name:w.name,href:w.link,external:null==w?void 0:w.external})),P&&e.createElement(s.b,{style:Z}))}DataBlock.propTypes={stat:r.a.number,className:r.a.string,title:r.a.string,datapoint:r.a.oneOfType([r.a.string,r.a.number]),datapointUnit:r.a.string,change:r.a.oneOfType([r.a.string,r.a.number]),changeDataUnit:r.a.oneOfType([r.a.string,r.a.bool]),context:r.a.string,period:r.a.string,selected:r.a.bool,handleStatSelection:r.a.func,invertChangeColor:r.a.bool,gatheringData:r.a.bool,gatheringDataNoticeStyle:r.a.oneOf(Object.values(s.a)),badge:r.a.oneOfType([r.a.bool,r.a.node])},t.a=DataBlock}).call(this,n(4))},,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdBlockerWarning}));var a=n(1),r=n.n(a),i=n(3),o=n(13),c=n(19),s=n(395);function AdBlockerWarning(t){var n=t.moduleSlug,a=t.className,r=Object(i.useSelect)((function(e){return e(c.a).getModuleStoreName(n)})),l=Object(i.useSelect)((function(e){var t;return null===(t=e(r))||void 0===t?void 0:t.getAdBlockerWarningMessage()})),u=Object(i.useSelect)((function(e){return e(o.c).getDocumentationLinkURL("".concat(n,"-ad-blocker-detected"))}));return e.createElement(s.a,{className:a,getHelpLink:u,warningMessage:l})}AdBlockerWarning.propTypes={className:r.a.string,moduleSlug:r.a.string.isRequired}}).call(this,n(4))},,,,function(e,t,n){"use strict";(function(e){var a=n(15),r=n.n(a),i=n(0);t.a=function(t,n){var a=Object(i.useState)(null),o=r()(a,2),c=o[0],s=o[1];return Object(i.useEffect)((function(){if(t.current&&"function"==typeof e.IntersectionObserver){var a=new e.IntersectionObserver((function(e){s(e[e.length-1])}),n);return a.observe(t.current),function(){s(null),a.disconnect()}}return function(){}}),[t.current,n.threshold,n.root,n.rootMargin]),c}}).call(this,n(28))},,function(e,t,n){"use strict";n.d(t,"a",(function(){return SurveyViewTrigger}));var a=n(0),r=n(1),i=n.n(r),o=n(3),c=n(13),s=n(7);function SurveyViewTrigger(e){var t=e.triggerID,n=e.ttl,r=void 0===n?0:n,i=Object(o.useSelect)((function(e){return e(c.c).isUsingProxy()})),l=Object(o.useDispatch)(s.a).triggerSurvey;return Object(a.useEffect)((function(){i&&l(t,{ttl:r})}),[i,t,r,l]),null}SurveyViewTrigger.propTypes={triggerID:i.a.string.isRequired,ttl:i.a.number}},,,,,function(e,t,n){"use strict";(function(e){var a=n(2),r=n(155),i=n(257),o=n(105);t.a=function Logo(){return e.createElement("div",{className:"googlesitekit-logo","aria-hidden":"true"},e.createElement(r.a,{className:"googlesitekit-logo__logo-g",height:"34",width:"32"}),e.createElement(i.a,{className:"googlesitekit-logo__logo-sitekit",height:"26",width:"99"}),e.createElement(o.a,null,Object(a.__)("Site Kit by Google Logo","google-site-kit")))}}).call(this,n(4))},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M62.09 1.664h3.038v.1L58.34 9.593l7.241 10.224v.1H62.7L56.755 11.4 53.95 14.64v5.278h-2.351V1.664h2.35v9.415h.1l8.04-9.415zM69.984 3.117c0 .454-.166.853-.487 1.175-.322.322-.71.488-1.176.488-.455 0-.854-.166-1.175-.488a1.599 1.599 0 01-.488-1.175c0-.466.166-.854.488-1.176.321-.322.71-.488 1.175-.488.455 0 .854.166 1.176.488.332.333.487.72.487 1.176zm-.476 4.313v12.498h-2.351V7.43h2.35zM77.016 20.128c-1.02 0-1.864-.31-2.54-.943-.676-.632-1.02-1.508-1.031-2.628V9.57h-2.196V7.43h2.196V3.603h2.35V7.43h3.061v2.14h-3.06v6.222c0 .831.166 1.397.488 1.696.321.3.687.444 1.097.444.189 0 .366-.022.555-.067.188-.044.344-.1.499-.166l.743 2.096c-.632.222-1.342.333-2.162.333zM2.673 18.952C1.375 18.009.488 16.678 0 14.97l2.883-1.176c.289 1.076.799 1.94 1.542 2.628.732.677 1.619 1.02 2.65 1.02.965 0 1.774-.244 2.45-.742.677-.5 1.01-1.187 1.01-2.052 0-.798-.3-1.453-.887-1.974-.588-.521-1.62-1.042-3.094-1.564l-1.22-.432C4.025 10.224 2.928 9.57 2.04 8.716 1.153 7.862.71 6.742.71 5.346c0-.966.266-1.853.787-2.673C2.018 1.852 2.75 1.209 3.693.72 4.624.244 5.678 0 6.864 0c1.708 0 3.072.41 4.081 1.242 1.02.832 1.697 1.752 2.04 2.795L10.236 5.2c-.2-.621-.576-1.164-1.142-1.63-.565-.477-1.286-.71-2.173-.71s-1.641.222-2.251.676c-.61.455-.91 1.032-.91 1.742 0 .676.278 1.22.82 1.663.544.432 1.398.854 2.563 1.253l1.22.41c1.674.577 2.96 1.342 3.88 2.274.921.931 1.376 2.184 1.376 3.748 0 1.275-.322 2.34-.976 3.193a6.01 6.01 0 01-2.495 1.919 8.014 8.014 0 01-3.116.621c-1.62 0-3.072-.466-4.358-1.408zM15.969 3.449a1.95 1.95 0 01-.588-1.43c0-.566.2-1.043.588-1.431A1.95 1.95 0 0117.399 0c.566 0 1.043.2 1.43.588.389.388.588.865.588 1.43 0 .566-.2 1.043-.587 1.43a1.95 1.95 0 01-1.43.589c-.566-.012-1.043-.2-1.431-.588zm-.067 2.595h2.994v13.883h-2.994V6.044zM25.405 19.85c-.543-.2-.986-.466-1.33-.788-.776-.776-1.176-1.84-1.176-3.182V8.683h-2.428v-2.64h2.428V2.13h2.994v3.926h3.372v2.639h-3.372v6.531c0 .743.145 1.276.433 1.575.277.366.743.543 1.42.543.31 0 .576-.044.82-.122.233-.077.488-.21.765-.399v2.917c-.599.277-1.32.41-2.173.41a5.01 5.01 0 01-1.753-.3zM33.623 19.407a6.63 6.63 0 01-2.529-2.628c-.61-1.12-.909-2.373-.909-3.77 0-1.332.3-2.551.887-3.693.588-1.132 1.409-2.04 2.462-2.706 1.053-.666 2.251-1.01 3.593-1.01 1.397 0 2.606.311 3.637.921a6.123 6.123 0 012.34 2.528c.532 1.076.799 2.274.799 3.627 0 .255-.023.576-.078.953H33.179c.111 1.287.566 2.285 1.375 2.983a4.162 4.162 0 002.817 1.043c.854 0 1.597-.189 2.218-.588a4.266 4.266 0 001.508-1.597l2.528 1.198c-.654 1.142-1.508 2.04-2.561 2.694-1.054.655-2.318.976-3.782.976-1.364.022-2.584-.288-3.66-.931zm7.23-8.051a3.332 3.332 0 00-.466-1.453c-.277-.477-.687-.887-1.242-1.208-.554-.322-1.23-.488-2.03-.488-.964 0-1.773.288-2.439.853-.665.566-1.12 1.342-1.375 2.296h7.552z",fill:"#5F6368"});t.a=function SvgLogoSitekit(e){return a.createElement("svg",r({viewBox:"0 0 80 21",fill:"none"},e),i)}},,function(e,t,n){"use strict";n.d(t,"g",(function(){return r})),n.d(t,"i",(function(){return i.b})),n.d(t,"d",(function(){return u})),n.d(t,"a",(function(){return d.k})),n.d(t,"b",(function(){return d.l})),n.d(t,"c",(function(){return d.p})),n.d(t,"h",(function(){return d.t})),n.d(t,"e",(function(){return g.a})),n.d(t,"f",(function(){return g.b}));n(14);var a=n(9);function r(e,t){var n,a;if(void 0!==e){var r=e||{},i=r.rows,o=r.totals;return!(null==i?void 0:i.length)||(!(null==o||null===(n=o.cells)||void 0===n?void 0:n.length)||0==+(null===(a=o.cells[t])||void 0===a?void 0:a.value))}}var i=n(383),o=n(11),c=n.n(o),s=n(2);function l(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.year,n=void 0===t?0:t,a=e.month,r=void 0===a?1:a,i=e.day,o=void 0===i?0:i;return new Date(n,r-1,o)}function u(e,t,n,r,i){for(var o=[[{type:"date",label:Object(s.__)("Day","google-site-kit")},{type:"string",role:"tooltip",p:{html:!0}},{type:"number",label:n},{type:"number",label:Object(s.__)("Previous period","google-site-kit")}]],u=function(e){var t=new Date(e);return t.setDate(e.getDate()+1),t},d=function(e){return function(t){return e.getTime()===Object(a.G)(t.cells[0].value).getTime()}},g=l(e.startDate),m=l(t.startDate),f=l(e.endDate),p=Object(a.r)(),b={weekday:"short",month:"short",day:"numeric"};g<=f;){var v,h,y,O,_,E,k=parseFloat((null===(v=((null==e?void 0:e.rows)||[]).find(d(g)))||void 0===v||null===(h=v.cells)||void 0===h||null===(y=h[r])||void 0===y?void 0:y.value)||0),S=parseFloat((null===(O=((null==t?void 0:t.rows)||[]).find(d(m)))||void 0===O||null===(_=O.cells)||void 0===_||null===(E=_[r])||void 0===E?void 0:E.value)||0),j=Object(a.h)(k,S),T=Object(a.o)(j),A=Object(s.sprintf)(
/* translators: 1: date for user stats, 2: previous date for user stats comparison */
Object(s._x)("%1$s vs %2$s","Date range for chart tooltip","google-site-kit"),g.toLocaleDateString(p,b),m.toLocaleDateString(p,b)),N=Object(a.B)(k,null==i?void 0:i.currencyCode);"METRIC_RATIO"===(null==i?void 0:i.type)&&(N=Object(a.B)(k,"%"));var C=Object(s.sprintf)(
/* translators: 1: selected stat label, 2: numeric value of selected stat, 3: up or down arrow , 4: different change in percentage, %%: percent symbol */
Object(s._x)("%1$s: <strong>%2$s</strong> <em>%3$s %4$s%%</em>","Stat information for chart tooltip","google-site-kit"),n,N,T,Math.abs(j).toFixed(2).replace(/(.00|0)$/,""));o.push([g,'<div class="'.concat(c()("googlesitekit-visualization-tooltip",{"googlesitekit-visualization-tooltip--up":j>0,"googlesitekit-visualization-tooltip--down":j<0}),'">\n\t\t\t\t<p>').concat(A,"</p>\n\t\t\t\t<p>").concat(C,"</p>\n\t\t\t</div>"),k,S]),g=u(g),m=u(m)}return o}var d=n(140),g=n(545);n(601)},,,,function(e,t,n){"use strict";n.d(t,"d",(function(){return s})),n.d(t,"e",(function(){return l})),n.d(t,"b",(function(){return u})),n.d(t,"a",(function(){return d})),n.d(t,"c",(function(){return g}));var a=n(27),r=n.n(a),i=n(14),o=n(24),c=n(9),s=function(e,t){if(!(null==t?void 0:t.length))return e;var n=[];return(null==e?void 0:e.length)&&(n=e[0].reduce((function(e,t,n){return(null==t?void 0:t.role)?[].concat(r()(e),[n]):e}),[])),e.map((function(e){return e.filter((function(e,a){return 0===a||t.includes(a-1)||n.includes(a-1)}))}))},l=function(e,t,n,a){var r={height:e||t,width:n||a};return r.width&&!r.height&&(r.height="100%"),r.height&&!r.width&&(r.width="100%"),r},u=function(e,t,n){var a=r()(e||[]);return t&&a.push({eventName:"ready",callback:t}),n&&a.push({eventName:"select",callback:n}),a},d=function(e,t,n,a,r,s){var l,u,d,g,m,f,p,b,v=Object(i.cloneDeep)(e);t&&"LineChart"===n&&((null==e||null===(l=e.vAxis)||void 0===l||null===(u=l.viewWindow)||void 0===u?void 0:u.min)||Object(i.set)(v,"vAxis.viewWindow.min",0),(null==e||null===(d=e.vAxis)||void 0===d||null===(g=d.viewWindow)||void 0===g?void 0:g.max)||Object(i.set)(v,"vAxis.viewWindow.max",100),(null==e||null===(m=e.hAxis)||void 0===m||null===(f=m.viewWindow)||void 0===f?void 0:f.min)||(Object(i.set)(v,"hAxis.viewWindow.min",Object(c.G)(a)),delete v.hAxis.ticks),(null==e||null===(p=e.hAxis)||void 0===p||null===(b=p.viewWindow)||void 0===b?void 0:b.max)||(Object(i.set)(v,"hAxis.viewWindow.max",Object(c.G)(r)),delete v.hAxis.ticks));if("LineChart"===n){var h,y,O;if((null==e||null===(h=e.hAxis)||void 0===h?void 0:h.maxTextLines)||Object(i.set)(v,"hAxis.maxTextLines",1),!(null==e||null===(y=e.hAxis)||void 0===y?void 0:y.minTextSpacing)){var _=s===o.b?50:100;Object(i.set)(v,"hAxis.minTextSpacing",_)}void 0===(null==e||null===(O=e.tooltip)||void 0===O?void 0:O.isHtml)&&(Object(i.set)(v,"tooltip.isHtml",!0),Object(i.set)(v,"tooltip.trigger","both"))}return Object(i.merge)(v,{hAxis:{textStyle:{fontSize:10,color:"#5f6561"}},vAxis:{textStyle:{color:"#5f6561",fontSize:10}},legend:{textStyle:{color:"#131418",fontSize:12}}}),v},g=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object(c.r)(),n=Intl.NumberFormat(t,{style:"currency",currency:e}),a=n.formatToParts(1e6);return a.reduce((function(e,t){var n=t.value;switch(t.type){case"group":return e+",";case"decimal":return e+".";case"currency":return e+n;case"literal":return e+(/^\s*$/.test(n)?n:"");case"integer":var r=n.replace(/\d/g,"#");return e+(Object(i.findLast)(a,(function(e){return"integer"===e.type}))===t?r.replace(/#$/,"0"):r);case"fraction":return e+n.replace(/\d/g,"0");default:return e}}),"")}},,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileText}));var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(184),u=n(9),d=n(202);function MetricTileText(t){var n=t.metricValue,a=t.metricValueFormat,i=t.subText,c=t.previousValue,s=t.currentValue,g=o()(t,["metricValue","metricValueFormat","subText","previousValue","currentValue"]),m=Object(u.m)(a);return e.createElement(d.a,r()({className:"googlesitekit-km-widget-tile--text"},g),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-container"},e.createElement("div",{className:"googlesitekit-km-widget-tile__metric"},n),e.createElement("p",{className:"googlesitekit-km-widget-tile__subtext"},i)),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-change-container"},e.createElement(l.a,{previousValue:c,currentValue:s,isAbsolute:"percent"===(null==m?void 0:m.style)})))}MetricTileText.propTypes={metricValue:s.a.oneOfType([s.a.string,s.a.number]),subtext:s.a.string,previousValue:s.a.number,currentValue:s.a.number}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(55),r=n.n(a),i=n(274),o=e._googlesitekitAPIFetchData||{},c=o.nonce,s=o.nonceEndpoint,l=o.preloadedData,u=o.rootURL;r.a.nonceEndpoint=s,r.a.nonceMiddleware=r.a.createNonceMiddleware(c),r.a.rootURLMiddleware=r.a.createRootURLMiddleware(u),r.a.preloadingMiddleware=Object(i.a)(l),r.a.use(r.a.nonceMiddleware),r.a.use(r.a.mediaUploadMiddleware),r.a.use(r.a.rootURLMiddleware),r.a.use(r.a.preloadingMiddleware),t.default=r.a}).call(this,n(28))},function(e,t,n){"use strict";var a=n(262);t.a=function(e){var t=Object.keys(e).reduce((function(t,n){return t[Object(a.getStablePath)(n)]=e[n],t}),{}),n=!1;return function(e,r){if(n)return r(e);setTimeout((function(){n=!0}),3e3);var i=e.parse,o=void 0===i||i,c=e.path;if("string"==typeof e.path){var s,l=(null===(s=e.method)||void 0===s?void 0:s.toUpperCase())||"GET",u=Object(a.getStablePath)(c);if(o&&"GET"===l&&t[u]){var d=Promise.resolve(t[u].body);return delete t[u],d}if("OPTIONS"===l&&t[l]&&t[l][u]){var g=Promise.resolve(t[l][u]);return delete t[l][u],g}}return r(e)}}},function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var a=n(6),r=n.n(a),i=n(25),o=n.n(i),c=n(63),s=n.n(c),l=n(14);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var g=s()((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.metrics,n=e.dimensions,a=o()(e,["metrics","dimensions"]);return d({metrics:m(t),dimensions:f(n)},a)})),m=function(e){return Object(l.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(l.isPlainObject)(e)}))},f=function(e){return Object(l.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(l.isPlainObject)(e)}))}},function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return UserMenu}));var r=n(5),i=n.n(r),o=n(16),c=n.n(o),s=n(15),l=n.n(s),u=n(209),d=n(0),g=n(2),m=n(56),f=n(3),p=n(10),b=n(109),v=n(9),h=n(37),y=n(72),O=n(277),_=n(220),E=n(278),k=n(279),S=n(29),j=n(13),T=n(7),A=n(32),N=n(8),C=n(112),w=n(18);function UserMenu(){var t=Object(f.useSelect)((function(e){return e(j.c).getProxyPermissionsURL()})),n=Object(f.useSelect)((function(e){return e(T.a).getEmail()})),r=Object(f.useSelect)((function(e){return e(T.a).getPicture()})),o=Object(f.useSelect)((function(e){return e(T.a).getFullName()})),s=Object(f.useSelect)((function(e){return e(j.c).getAdminURL("googlesitekit-splash",{googlesitekit_context:"revoked"})})),I=Object(f.useSelect)((function(e){return e(S.a).getValue(N.d,"isAutoCreatingCustomDimensionsForAudience")})),M=Object(d.useState)(!1),R=l()(M,2),D=R[0],x=R[1],L=Object(d.useState)(!1),P=l()(L,2),G=P[0],Z=P[1],B=Object(d.useRef)(),U=Object(d.useRef)(),F=Object(w.a)(),z=Object(f.useDispatch)(A.a).navigateTo;Object(u.a)(B,(function(){return Z(!1)})),Object(C.a)([m.c,m.f],B,(function(){var e;Z(!1),null===(e=U.current)||void 0===e||e.focus()})),Object(d.useEffect)((function(){var t=function(e){m.c===e.keyCode&&(x(!1),Z(!1))};return e.addEventListener("keyup",t),function(){e.removeEventListener("keyup",t)}}),[]);var V,H=Object(d.useCallback)((function(){G||Object(v.I)("".concat(F,"_headerbar"),"open_usermenu"),Z(!G)}),[G,F]),W=Object(d.useCallback)((function(){x(!D),Z(!1)}),[D]),q=Object(d.useCallback)(function(){var e=c()(i.a.mark((function e(n,a){var r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=a.detail.item,e.t0=null==r?void 0:r.id,e.next="manage-sites"===e.t0?4:"disconnect"===e.t0?9:11;break;case 4:if(!t){e.next=8;break}return e.next=7,Object(v.I)("".concat(F,"_headerbar_usermenu"),"manage_sites");case 7:z(t);case 8:return e.abrupt("break",12);case 9:return W(),e.abrupt("break",12);case 11:H();case 12:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),[t,H,W,z,F]),K=Object(d.useCallback)(c()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return x(!1),e.next=3,Object(h.b)();case 3:return e.next=5,Object(v.I)("".concat(F,"_headerbar_usermenu"),"disconnect_user");case 5:z(s);case 6:case"end":return e.stop()}}),e)}))),[s,z,F]);return n?(o&&n&&(V=Object(g.sprintf)(
/* translators: Account info text. 1: User's (full) name 2: User's email address. */
Object(g.__)("Google Account for %1$s (Email: %2$s)","google-site-kit"),o,n)),o&&!n&&(V=Object(g.sprintf)(
/* translators: Account info text. 1: User's (full) name. */
Object(g.__)("Google Account for %1$s","google-site-kit"),o)),!o&&n&&(V=Object(g.sprintf)(
/* translators: Account info text. 1: User's email address. */
Object(g.__)("Google Account (Email: %1$s)","google-site-kit"),n)),a.createElement(d.Fragment,null,a.createElement("div",{ref:B,className:"googlesitekit-user-selector googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},a.createElement(p.Button,{disabled:I,ref:U,className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--tablet googlesitekit-border-radius-round--phone googlesitekit-border-radius-round googlesitekit-button-icon",text:!0,onClick:H,icon:!!r&&a.createElement("i",{className:"mdc-button__icon mdc-button__account","aria-hidden":"true"},a.createElement("img",{className:"mdc-button__icon--image",src:r,alt:Object(g.__)("User Avatar","google-site-kit")})),"aria-haspopup":"menu","aria-expanded":G,"aria-controls":"user-menu","aria-label":I?void 0:Object(g.__)("Account","google-site-kit"),tooltip:!0,tooltipEnterDelayInMS:500,customizedTooltip:I?null:a.createElement("span",{"aria-label":V},a.createElement("strong",null,Object(g.__)("Google Account","google-site-kit")),a.createElement("br",null),a.createElement("br",null),o,o&&a.createElement("br",null),n)}),a.createElement(p.Menu,{className:"googlesitekit-user-menu",menuOpen:G,onSelected:q,id:"user-menu"},a.createElement("li",null,a.createElement(O.a,null)),!!t&&a.createElement("li",{id:"manage-sites",className:"mdc-list-item",role:"menuitem"},a.createElement(_.a,{icon:a.createElement(k.a,{width:"22"}),label:Object(g.__)("Manage Sites","google-site-kit")})),a.createElement("li",{id:"disconnect",className:"mdc-list-item",role:"menuitem"},a.createElement(_.a,{icon:a.createElement(E.a,{width:"22"}),label:Object(g.__)("Disconnect","google-site-kit")})))),a.createElement(y.a,null,a.createElement(b.a,{dialogActive:D,handleConfirm:K,handleDialog:W,title:Object(g.__)("Disconnect","google-site-kit"),subtitle:Object(g.__)("Disconnecting Site Kit by Google will remove your access to all services. After disconnecting, you will need to re-authorize to restore service.","google-site-kit"),confirmButton:Object(g.__)("Disconnect","google-site-kit"),danger:!0,small:!0})))):null}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Details}));var a=n(2),r=n(3),i=n(7);function Details(){var t=Object(r.useSelect)((function(e){return e(i.a).getPicture()})),n=Object(r.useSelect)((function(e){return e(i.a).getFullName()})),o=Object(r.useSelect)((function(e){return e(i.a).getEmail()}));return e.createElement("div",{className:"googlesitekit-user-menu__details","aria-label":Object(a.__)("Google account","google-site-kit")},!!t&&e.createElement("img",{className:"googlesitekit-user-menu__details-avatar",src:t,alt:""}),e.createElement("div",{className:"googlesitekit-user-menu__details-info"},e.createElement("p",{className:"googlesitekit-user-menu__details-info__name"},n),e.createElement("p",{className:"googlesitekit-user-menu__details-info__email","aria-label":Object(a.__)("Email","google-site-kit")},o)))}}).call(this,n(4))},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M6.83 2H20a2 2 0 012 2v12c0 .34-.09.66-.23.94L20 15.17V6h-9.17l-4-4zm13.66 19.31L17.17 18H4a2 2 0 01-2-2V4c0-.34.08-.66.23-.94L.69 1.51 2.1.1l19.8 19.8-1.41 1.41zM15.17 16l-10-10H4v10h11.17z",fill:"currentColor"});t.a=function SvgDisconnect(e){return a.createElement("svg",r({viewBox:"0 0 22 22",fill:"none"},e),i)}},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M20 0H2C.9 0 0 .9 0 2v12c0 1.1.9 2 2 2h5v2h8v-2h5c1.1 0 2-.9 2-2V2c0-1.1-.9-2-2-2zm0 14H2V2h18v12zm-2-9H7v2h11V5zm0 4H7v2h11V9zM6 5H4v2h2V5zm0 4H4v2h2V9z",fill:"currentColor"});t.a=function SvgManageSites(e){return a.createElement("svg",r({viewBox:"0 0 22 18",fill:"none"},e),i)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotifications}));var a=n(0),r=n(281),i=n(167),o=n(41);function ErrorNotifications(){return e.createElement(a.Fragment,null,e.createElement(r.a,null),e.createElement(i.a,{areaSlug:o.b.ERRORS}))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InternalServerError}));var a=n(3),r=n(13),i=n(196),o=n(191),c=n(111);function InternalServerError(){var t=Object(a.useSelect)((function(e){return e(r.c).getInternalServerError()}));return t?e.createElement(o.a,{className:"googlesitekit-publisher-win googlesitekit-publisher-win--win-error"},e.createElement(i.a,{title:t.title,description:e.createElement(c.a,{text:t.description})})):null}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return ViewedStateObserver}));var a=n(1),r=n.n(a),i=n(0),o=n(3),c=n(23),s=n(249),l=n(161);function ViewedStateObserver(e){var t=e.id,n=e.observeRef,a=e.threshold,r=Object(s.a)(n,{threshold:a}),u=Object(o.useDispatch)(c.b).setValue,d=!!(null==r?void 0:r.isIntersecting),g=Object(l.a)(t);return Object(i.useEffect)((function(){!g&&d&&u(l.a.getKey(t),!0)}),[g,d,u,t]),null}ViewedStateObserver.propTypes={id:r.a.string,observeRef:r.a.object,threshold:r.a.number}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return s}));var a=n(21),r=n.n(a),i=n(63),o=n.n(i),c=n(191),s=o()((function(e){return{id:e,Notification:l(e)(c.a)}}));function l(t){return function(n){function WithNotificationID(a){return e.createElement(n,r()({},a,{id:t}))}return WithNotificationID.displayName="WithNotificationID",(n.displayName||n.name)&&(WithNotificationID.displayName+="(".concat(n.displayName||n.name,")")),WithNotificationID}}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardNavigation}));var a=n(3),r=n(7),i=n(34),o=n(183),c=n(285);function DashboardNavigation(){var t=Object(i.a)(),n=Object(a.useSelect)((function(e){return t?e(r.a).getViewableModules():null})),s=Object(a.useSelect)((function(e){return e(r.a).getKeyMetrics()}));return e.createElement(o.a,{loading:void 0===n||void 0===s,width:"100%",smallHeight:"59px",height:"71px"},e.createElement(c.a,null))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return Navigation}));var r=n(27),i=n.n(r),o=n(15),c=n.n(o),s=n(11),l=n.n(s),u=n(14),d=n(81),g=n(153),m=n(0),f=n(2),p=n(3),b=n(286),v=n(287),h=n(288),y=n(289),O=n(290),_=n(22),E=n(7),k=n(47),S=n(23),j=n(71),T=n(52),A=n(24),N=n(93),C=n(9),w=n(18),I=n(34);function M(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return R(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return R(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,r=function(){};return{s:r,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}function Navigation(){var t,n=Object(T.c)(),r=Object(m.useRef)(),o=Object(A.e)(),s=null===(t=e.location.hash)||void 0===t?void 0:t.substring(1),R=Object(m.useState)(s),D=c()(R,2),x=D[0],L=D[1],P=Object(m.useState)(s||void 0),G=c()(P,2),Z=G[0],B=G[1],U=Object(m.useState)(!1),F=c()(U,2),z=F[0],V=F[1],H=Object(w.a)(),W=Object(I.a)(),q=Object(p.useDispatch)(S.b).setValue,K=Object(p.useSelect)((function(e){return W?e(E.a).getViewableModules():null})),Y=Object(p.useSelect)((function(e){return e(E.a).isKeyMetricsWidgetHidden()})),$={modules:K||void 0},X=Object(p.useSelect)((function(e){return n===T.b&&!0!==Y&&e(k.a).isWidgetContextActive(j.CONTEXT_MAIN_DASHBOARD_KEY_METRICS,$)})),J=Object(p.useSelect)((function(e){return e(k.a).isWidgetContextActive(n===T.b?j.CONTEXT_MAIN_DASHBOARD_TRAFFIC:j.CONTEXT_ENTITY_DASHBOARD_TRAFFIC,$)})),Q=Object(p.useSelect)((function(e){return e(k.a).isWidgetContextActive(n===T.b?j.CONTEXT_MAIN_DASHBOARD_CONTENT:j.CONTEXT_ENTITY_DASHBOARD_CONTENT,$)})),ee=Object(p.useSelect)((function(e){return e(k.a).isWidgetContextActive(n===T.b?j.CONTEXT_MAIN_DASHBOARD_SPEED:j.CONTEXT_ENTITY_DASHBOARD_SPEED,$)})),te=Object(p.useSelect)((function(e){return e(k.a).isWidgetContextActive(n===T.b?j.CONTEXT_MAIN_DASHBOARD_MONETIZATION:j.CONTEXT_ENTITY_DASHBOARD_MONETIZATION,$)})),ne=Object(m.useCallback)((function(){return X?_.b:W?J?_.e:Q?_.a:ee?_.d:te?_.c:"":_.e}),[X,J,Q,ee,te,W]),ae=Object(m.useCallback)((function(t){var n,a=t.target.closest(".mdc-chip"),r=null==a||null===(n=a.dataset)||void 0===n?void 0:n.contextId;e.history.replaceState({},"","#".concat(r)),B(r),Object(C.I)("".concat(H,"_navigation"),"tab_select",r),e.scrollTo({top:r!==ne()?Object(N.a)("#".concat(r),o):0,behavior:"smooth"}),setTimeout((function(){q(S.a,r)}),50)}),[o,H,q,ne]);return Object(d.a)((function(){var t=ne();if(!s)return L(t),void setTimeout((function(){return e.history.replaceState({},"","#".concat(t))}));var n=s;(function(e){return!(!X||e!==_.b)||(!(!J||e!==_.e)||(!(!Q||e!==_.a)||(!(!ee||e!==_.d)||!(!te||e!==_.c))))})(n)||(n=t),q(S.a,n),L(n),setTimeout((function(){var a=n!==t?Object(N.a)("#".concat(n),o):0;e.scrollY!==a?e.scrollTo({top:a,behavior:"smooth"}):q(S.a,void 0)}),50)})),Object(m.useEffect)((function(){var t=function(e){q(S.a,void 0),L(e),B(void 0)},n=Object(u.throttle)((function(n){var a,o,c,s,l=e.scrollY,u=null===(a=document.querySelector(".googlesitekit-entity-header"))||void 0===a||null===(o=a.getBoundingClientRect())||void 0===o?void 0:o.bottom,d=null==r||null===(c=r.current)||void 0===c?void 0:c.getBoundingClientRect(),g=d.bottom,m=d.top,f=[].concat(i()(X?[_.b]:[]),i()(J?[_.e]:[]),i()(Q?[_.a]:[]),i()(ee?[_.d]:[]),i()(te?[_.c]:[])),p=ne();if(0===l)V(!1);else{var b,v=null===(b=document.querySelector(".googlesitekit-header"))||void 0===b?void 0:b.getBoundingClientRect().bottom;V(m===v)}var h,y=M(f);try{for(y.s();!(h=y.n()).done;){var O=h.value,E=document.getElementById(O);if(E){var k=E.getBoundingClientRect().top-20-(u||g||0);k<0&&(void 0===s||s<k)&&(s=k,p=O)}}}catch(e){y.e(e)}finally{y.f()}if(Z)Z===p&&t(p);else{var S=e.location.hash;p!==(null==S?void 0:S.substring(1))&&(n&&Object(C.I)("".concat(H,"_navigation"),"tab_scroll",p),e.history.replaceState({},"","#".concat(p)),t(p))}}),150);return e.addEventListener("scroll",n),function(){e.removeEventListener("scroll",n)}}),[Z,X,J,Q,ee,te,H,q,ne]),a.createElement("nav",{className:l()("mdc-chip-set","googlesitekit-navigation","googlesitekit-navigation--".concat(n),{"googlesitekit-navigation--is-sticky":z}),ref:r},X&&a.createElement(g.Chip,{id:_.b,label:Object(f.__)("Key metrics","google-site-kit"),leadingIcon:a.createElement(b.a,{width:"18",height:"16"}),onClick:ae,selected:x===_.b,"data-context-id":_.b}),J&&a.createElement(g.Chip,{id:_.e,label:Object(f.__)("Traffic","google-site-kit"),leadingIcon:a.createElement(v.a,{width:"18",height:"16"}),onClick:ae,selected:x===_.e,"data-context-id":_.e}),Q&&a.createElement(g.Chip,{id:_.a,label:Object(f.__)("Content","google-site-kit"),leadingIcon:a.createElement(h.a,{width:"18",height:"18"}),onClick:ae,selected:x===_.a,"data-context-id":_.a}),ee&&a.createElement(g.Chip,{id:_.d,label:Object(f.__)("Speed","google-site-kit"),leadingIcon:a.createElement(y.a,{width:"20",height:"16"}),onClick:ae,selected:x===_.d,"data-context-id":_.d}),te&&a.createElement(g.Chip,{id:_.c,label:Object(f.__)("Monetization","google-site-kit"),leadingIcon:a.createElement(O.a,{width:"18",height:"16"}),onClick:ae,selected:x===_.c,"data-context-id":_.c}))}}).call(this,n(28),n(4))},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("rect",{x:.5,width:5,height:5,rx:1,fill:"currentColor"}),o=a.createElement("rect",{x:7.5,width:5,height:5,rx:1,fill:"currentColor"}),c=a.createElement("rect",{x:.5,y:7,width:5,height:5,rx:1,fill:"currentColor"}),s=a.createElement("rect",{x:7.5,y:7,width:5,height:5,rx:1,fill:"currentColor"});t.a=function SvgNavKeyMetricsIcon(e){return a.createElement("svg",r({viewBox:"0 0 13 12",fill:"none"},e),i,o,c,s)}},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 0h3.971v16H7V0zM0 8h4v8H0V8zm18-3h-4v11h4V5z",fill:"currentColor"});t.a=function SvgNavTrafficIcon(e){return a.createElement("svg",r({viewBox:"0 0 18 16",fill:"none"},e),i)}},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 16V2c0-1.1-1-2-2.222-2H2.222C1 0 0 .9 0 2v14c0 1.1 1 2 2.222 2h13.556C17 18 18 17.1 18 16zM9 7h5V5H9v2zm7-5H2v14h14V2zM4 4h4v4H4V4zm10 7H9v2h5v-2zM4 10h4v4H4v-4z",fill:"currentColor"});t.a=function SvgNavContentIcon(e){return a.createElement("svg",r({viewBox:"0 0 18 18",fill:"none"},e),i)}},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M18.378 4.543l-1.232 1.854a8.024 8.024 0 01-.22 7.598H3.043A8.024 8.024 0 014.154 4.49 8.011 8.011 0 0113.57 2.82l1.853-1.233A10.01 10.01 0 003.117 2.758a10.026 10.026 0 00-1.797 12.24A2.004 2.004 0 003.043 16h13.873a2.003 2.003 0 001.742-1.002 10.03 10.03 0 00-.27-10.465l-.01.01z",fill:"currentColor"}),o=a.createElement("path",{d:"M8.572 11.399a2.003 2.003 0 002.835 0l5.669-8.51-8.504 5.673a2.005 2.005 0 000 2.837z",fill:"currentColor"});t.a=function SvgNavSpeedIcon(e){return a.createElement("svg",r({viewBox:"0 0 20 16",fill:"none"},e),i,o)}},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M16.1 0v2h2.967l-5.946 5.17-4.6-4L0 10.59 1.621 12l6.9-6 4.6 4L20.7 3.42V6H23V0h-6.9z",fill:"currentColor"});t.a=function SvgNavMonetizationIcon(e){return a.createElement("svg",r({viewBox:"0 0 23 12",fill:"none"},e),i)}},function(e,t,n){"use strict";(function(e,a){var r=n(15),i=n.n(r),o=n(14),c=n(2),s=n(0),l=n(3),u=n(10),d=n(13),g=n(292),m=n(32),f=n(20),p=n(80),b=n(9),v=n(52),h=n(18);t.a=function EntityHeader(){var t=Object(h.a)(),n=Object(v.c)(),r=Object(l.useSelect)((function(e){return e(d.c).getCurrentEntityTitle()})),y=Object(l.useSelect)((function(e){return e(d.c).getCurrentEntityURL()})),O=Object(s.useRef)(),_=Object(s.useState)(y),E=i()(_,2),k=E[0],S=E[1];Object(s.useEffect)((function(){var t=function(){if(O.current){var t=O.current.clientWidth-40,n=e.getComputedStyle(O.current.lastChild,null).getPropertyValue("font-size"),a=2*t/parseFloat(n);S(Object(p.d)(y,a))}},n=Object(o.throttle)(t,100);return t(),e.addEventListener("resize",n),function(){e.removeEventListener("resize",n)}}),[y,O,S]);var j=Object(l.useDispatch)(m.a).navigateTo,T=Object(l.useSelect)((function(e){return e(d.c).getAdminURL("googlesitekit-dashboard")})),A=Object(s.useCallback)((function(){Object(b.I)("".concat(t,"_navigation"),"return_to_dashboard"),j(T)}),[T,j,t]);return v.a!==n||null===y||null===r?null:a.createElement("div",{className:"googlesitekit-entity-header"},a.createElement("div",{className:"googlesitekit-entity-header__back"},a.createElement(u.Button,{icon:a.createElement(g.a,{width:24,height:24}),"aria-label":Object(c.__)("Back to dashboard","google-site-kit"),onClick:A,text:!0,tertiary:!0},Object(c.__)("Back to dashboard","google-site-kit"))),a.createElement("div",{ref:O,className:"googlesitekit-entity-header__details"},a.createElement("p",null,r),a.createElement(f.a,{secondary:!0,href:y,"aria-label":y,external:!0},k)))}}).call(this,n(28),n(4))},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=a.createElement("path",{d:"M21 11H6.83l3.58-3.59L9 6l-6 6 6 6 1.41-1.41L6.83 13H21z",fill:"currentColor"});t.a=function SvgKeyboardBackspace(e){return a.createElement("svg",r({viewBox:"0 0 24 24"},e),i,o)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ViewOnlyMenu}));var a=n(15),r=n.n(a),i=n(209),o=n(11),c=n.n(o),s=n(0),l=n(2),u=n(56),d=n(10),g=n(18),m=n(112),f=n(9),p=n(294),b=n(295),v=n(296),h=n(298),y=n(3),O=n(7);function ViewOnlyMenu(){var t=Object(s.useState)(!1),n=r()(t,2),a=n[0],o=n[1],_=Object(s.useRef)(),E=Object(g.a)();Object(i.a)(_,(function(){return o(!1)})),Object(m.a)([u.c,u.f],_,(function(){return o(!1)}));var k=Object(s.useCallback)((function(){a||Object(f.I)("".concat(E,"_headerbar"),"open_viewonly"),o(!a)}),[a,E]),S=Object(y.useSelect)((function(e){return e(O.a).hasCapability(O.H)}));return e.createElement("div",{ref:_,className:c()("googlesitekit-view-only-menu","googlesitekit-dropdown-menu","googlesitekit-dropdown-menu__icon-menu","mdc-menu-surface--anchor",{"googlesitekit-view-only-menu--user-can-authenticate":S})},e.createElement(d.Button,{className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--phone googlesitekit-button-icon",text:!0,onClick:k,icon:e.createElement("span",{className:"mdc-button__icon","aria-hidden":"true"},e.createElement(p.a,{className:"mdc-button__icon--image"})),"aria-haspopup":"menu","aria-expanded":a,"aria-controls":"view-only-menu","aria-label":Object(l.__)("View only","google-site-kit"),tooltip:!0,tooltipEnterDelayInMS:500},Object(l.__)("View only","google-site-kit")),e.createElement(d.Menu,{menuOpen:a,nonInteractive:!0,onSelected:k,id:"view-only-menu"},e.createElement(b.a,null),e.createElement(v.a,null),e.createElement("li",{className:"mdc-list-divider",role:"separator"}),e.createElement(h.a,null)))}}).call(this,n(4))},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M8 1.333c2.756 0 5.214 1.42 6.415 3.667-1.2 2.247-3.659 3.667-6.415 3.667-2.756 0-5.215-1.42-6.415-3.667C2.785 2.753 5.244 1.333 8 1.333zM8 0C4.364 0 1.258 2.073 0 5c1.258 2.927 4.364 5 8 5s6.742-2.073 8-5c-1.258-2.927-4.364-5-8-5zm0 3.333c1.004 0 1.818.747 1.818 1.667S9.004 6.667 8 6.667 6.182 5.92 6.182 5 6.996 3.333 8 3.333zM8 2C6.196 2 4.727 3.347 4.727 5S6.197 8 8 8c1.804 0 3.273-1.347 3.273-3S9.803 2 8 2z",fill:"currentColor"});t.a=function SvgView(e){return a.createElement("svg",r({viewBox:"0 0 16 10",fill:"none"},e),i)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(0),s=n(38),l=n(2),u=n(3),d=n(10),g=n(32),m=n(13),f=n(7),p=n(9),b=n(20),v=n(18),h=n(37);function Description(){var t=Object(v.a)(),n=Object(u.useSelect)((function(e){return e(f.a).hasCapability(f.H)})),a=Object(u.useSelect)((function(e){return e(m.c).getProxySetupURL()})),i=Object(u.useSelect)((function(e){return e(m.c).getDocumentationLinkURL("dashboard-sharing")})),y=Object(u.useDispatch)(g.a).navigateTo,O=Object(c.useCallback)(function(){var e=o()(r.a.mark((function e(n){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.preventDefault(),e.next=3,Promise.all([Object(h.f)("start_user_setup",!0),Object(p.I)("".concat(t,"_headerbar_viewonly"),"start_user_setup",a?"proxy":"custom-oauth")]);case 3:y(a);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[a,y,t]),_=Object(c.useCallback)((function(){Object(p.I)("".concat(t,"_headerbar_viewonly"),"click_learn_more_link")}),[t]),E=n?Object(s.a)(Object(l.__)("You can see stats from all shared Google services, but you can't make any changes. <strong>Sign in to connect more services and control sharing access.</strong>","google-site-kit"),{strong:e.createElement("strong",null)}):Object(s.a)(Object(l.__)("You can see stats from all shared Google services, but you can't make any changes. <a>Learn more</a>","google-site-kit"),{a:e.createElement(b.a,{href:i,external:!0,onClick:_,"aria-label":Object(l.__)("Learn more about dashboard sharing","google-site-kit")})});return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item googlesitekit-view-only-menu__description"},e.createElement("p",null,E),n&&e.createElement(d.Button,{onClick:O},Object(l._x)("Sign in with Google","Service name","google-site-kit")))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SharedServices}));var a=n(2),r=n(3),i=n(7),o=n(297);function SharedServices(){var t=Object(r.useSelect)((function(e){return e(i.a).getViewableModules()}));return void 0===t?null:e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("h4",null,Object(a.__)("Shared services","google-site-kit")),e.createElement("ul",null,t.map((function(t){return e.createElement(o.a,{key:t,module:t})}))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Service}));var a=n(1),r=n.n(a),i=n(38),o=n(2),c=n(3),s=n(19),l=n(7);function Service(t){var n=t.module,a=Object(c.useSelect)((function(e){return e(l.a).hasCapability(l.H)})),r=Object(c.useSelect)((function(e){return e(s.a).getModule(n)||{}})),u=r.name,d=r.owner,g=Object(c.useSelect)((function(e){return e(s.a).getModuleIcon(n)}));return e.createElement("li",{className:"googlesitekit-view-only-menu__service"},e.createElement("span",{className:"googlesitekit-view-only-menu__service--icon"},e.createElement(g,{height:26})),e.createElement("span",{className:"googlesitekit-view-only-menu__service--name"},u),a&&(null==d?void 0:d.login)&&e.createElement("span",{className:"googlesitekit-view-only-menu__service--owner"},Object(i.a)(Object(o.sprintf)(
/* translators: %s: module owner Google Account email address */
Object(o.__)("Shared by <strong>%s</strong>","google-site-kit"),d.login),{strong:e.createElement("strong",{title:d.login})})))}Service.propTypes={module:r.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Tracking}));var a=n(38),r=n(2),i=n(218),o=n(18);function Tracking(){var t=Object(o.a)();return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("p",null,Object(a.a)(Object(r.__)("Thanks for using Site Kit!<br />Help us make it even better","google-site-kit"),{br:e.createElement("br",null)})),e.createElement(i.a,{trackEventCategory:"".concat(t,"_headerbar_viewonly"),alignCheckboxLeft:!0}))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SubtleNotifications}));var a=n(167),r=n(41);function SubtleNotifications(){return e.createElement(a.a,{areaSlug:r.b.BANNERS_BELOW_NAV})}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(0),s=n(3),l=n(13),u=n(18),d=n(37),g=n(9),m=function(){var e=Object(u.a)(),t=Object(s.useSelect)((function(e){return e(l.c).isUsingProxy()})),n=Object(s.useSelect)((function(e){return e(l.c).getSetupErrorMessage()}));Object(c.useEffect)((function(){n||void 0===t||function(){var n=o()(r.a.mark((function n(){var a,i;return r.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,Object(d.d)("start_user_setup");case 2:return a=n.sent,n.next=5,Object(d.d)("start_site_setup");case 5:if(i=n.sent,!a.cacheHit){n.next=10;break}return n.next=9,Object(d.c)("start_user_setup");case 9:Object(g.I)("".concat(e,"_setup"),"complete_user_setup",t?"proxy":"custom-oauth");case 10:if(!i.cacheHit){n.next=14;break}return n.next=13,Object(d.c)("start_site_setup");case 13:Object(g.I)("".concat(e,"_setup"),"complete_site_setup",t?"proxy":"custom-oauth");case 14:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}()()}),[e,t,n])}},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M9 16h2v-2H9v2zm1-16C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14C7.79 4 6 5.79 6 8h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z",fill:"currentColor"});t.a=function SvgHelp(e){return a.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),i)}},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileError}));var a=n(95),r=n(132);function MetricTileError(t){var n=t.children,i=t.headerText,o=t.infoTooltip,c=t.title;return e.createElement("div",{className:"googlesitekit-km-widget-tile--error"},e.createElement(a.a,{title:c,headerText:i,headerContent:o&&e.createElement(r.a,{title:o}),description:"",error:!0},n))}}).call(this,n(4))},,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var a={UPCOMING:"upcoming",ACTIVE:"active",COMPLETED:"completed"}},,,function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.149 7.96l-5.166 5.166a.344.344 0 00-.094.176l-.35 1.755a.344.344 0 00.404.404l1.755-.35a.344.344 0 00.175-.095l5.166-5.165-1.89-1.89zm2.301-1.814a1.031 1.031 0 00-1.458 0L6.497 12.64a1.031 1.031 0 00-.282.527l-.35 1.755a1.031 1.031 0 001.213 1.213l1.754-.35c.2-.04.383-.139.527-.283l6.495-6.494a1.031 1.031 0 000-1.459L14.45 6.146z"}),o=a.createElement("path",{d:"M12.149 7.96l.117-.116a.165.165 0 00-.234 0l.117.117zm-5.166 5.166l-.116-.116.116.116zm-.094.176l.162.033-.162-.033zm-.35 1.755l.161.032-.162-.032zm.404.404l.032.162-.032-.162zm1.755-.35l.032.161-.032-.162zm.175-.095l.117.117-.117-.117zm5.166-5.165l.116.116a.165.165 0 000-.233l-.116.117zm-1.047-3.705l.116.116-.116-.116zm1.458 0l-.116.116.116-.116zM6.497 12.64l.117.117-.117-.117zm-.282.527l-.162-.032.162.032zm-.35 1.755l.161.032-.162-.032zm1.213 1.213l-.033-.162.033.162zm1.754-.35l.033.161-.033-.162zm.527-.283l.117.117-.117-.117zm6.495-6.494l-.117-.117.117.117zm0-1.459l.117-.116-.117.116zm-3.822.295L6.867 13.01l.233.233 5.166-5.165-.234-.234zM6.867 13.01a.509.509 0 00-.14.26l.324.065a.18.18 0 01.05-.092l-.234-.233zm-.14.26l-.35 1.754.323.065.351-1.755-.323-.064zm-.35 1.754a.509.509 0 00.598.599l-.064-.324a.179.179 0 01-.21-.21l-.324-.065zm.598.599l1.755-.35-.065-.325-1.754.351.064.324zm1.755-.35a.508.508 0 00.26-.14l-.233-.233a.18.18 0 01-.092.048l.065.324zm.26-.14l5.165-5.166-.233-.233L8.757 14.9l.233.233zm3.042-7.055l1.89 1.89.233-.234-1.89-1.89-.233.234zm1.076-1.816a.866.866 0 011.226 0l.233-.233a1.196 1.196 0 00-1.692 0l.233.233zm-6.494 6.495l6.494-6.495-.233-.233-6.494 6.495.233.233zm-.237.443a.866.866 0 01.237-.443l-.233-.233c-.167.167-.281.38-.328.61l.324.066zm-.35 1.754l.35-1.754-.324-.065-.35 1.755.323.064zm1.018 1.02a.866.866 0 01-1.019-1.02l-.323-.065a1.196 1.196 0 001.407 1.408l-.065-.324zm1.755-.351l-1.755.35.065.324 1.755-.35-.065-.324zm.443-.237a.866.866 0 01-.443.237l.065.323c.231-.046.444-.16.611-.327l-.233-.233zm6.494-6.495l-6.494 6.495.233.233 6.495-6.494-.234-.234zm0-1.225a.866.866 0 010 1.225l.234.234a1.196 1.196 0 000-1.692l-.234.233zm-1.403-1.404l1.403 1.404.234-.233-1.404-1.404-.233.233z"});t.a=function SvgPencilAlt(e){return a.createElement("svg",r({viewBox:"0 0 22 22",fill:"currentColor"},e),i,o)}},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("g",{fill:"currentColor",fillRule:"evenodd"},a.createElement("path",{d:"M0 6.414L1.415 5l5.292 5.292-1.414 1.415z"}),a.createElement("path",{d:"M14.146.146l1.415 1.414L5.414 11.707 4 10.292z"}));t.a=function SvgConnected(e){return a.createElement("svg",r({viewBox:"0 0 16 12"},e),i)}},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("g",{fill:"currentColor",fillRule:"evenodd"},a.createElement("path",{d:"M0 0h2v7H0zM0 10h2v2H0z"}));t.a=function SvgExclamation(e){return a.createElement("svg",r({viewBox:"0 0 2 12"},e),i)}},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelHeader}));var a=n(1),r=n.n(a),i=n(20),o=n(104);function SelectionPanelHeader(t){var n=t.children,a=t.title,r=t.onCloseClick;return e.createElement("header",{className:"googlesitekit-selection-panel-header"},e.createElement("div",{className:"googlesitekit-selection-panel-header__row"},e.createElement("h3",null,a),e.createElement(i.a,{className:"googlesitekit-selection-panel-header__close",onClick:r,linkButton:!0},e.createElement(o.a,{width:"15",height:"15"}))),n)}SelectionPanelHeader.propTypes={children:r.a.node,title:r.a.string,onCloseClick:r.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelItem}));var a=n(1),r=n.n(a),i=n(2),o=n(328),c=n(77);function SelectionPanelItem(t){var n=t.children,a=t.id,r=t.slug,s=t.title,l=t.description,u=t.isItemSelected,d=t.isItemDisabled,g=t.onCheckboxChange,m=t.subtitle,f=t.suffix,p=t.badge,b=t.isNewlyDetected;return e.createElement("div",{className:"googlesitekit-selection-panel-item"},e.createElement(o.a,{badge:p,checked:u,disabled:d,id:a,onChange:g,title:s,value:r},m&&e.createElement("span",{className:"googlesitekit-selection-panel-item__subtitle"},m),l,n),b&&e.createElement(c.a,{label:Object(i.__)("New","google-site-kit")}),f&&e.createElement("span",{className:"googlesitekit-selection-panel-item__suffix"},f))}SelectionPanelItem.propTypes={children:r.a.node,id:r.a.string,slug:r.a.string,title:r.a.string,description:r.a.string,isItemSelected:r.a.bool,isItemDisabled:r.a.bool,onCheckboxChange:r.a.func,subtitle:r.a.string,suffix:r.a.node,badge:r.a.node,isNewlyDetected:r.a.bool}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelItems}));var a=n(21),r=n.n(a),i=n(1),o=n.n(i),c=n(0),s=n(2);function SelectionPanelItems(t){var n=t.currentSelectionTitle,a=void 0===n?Object(s.__)("Current selection","google-site-kit"):n,i=t.availableItemsTitle,o=void 0===i?Object(s.__)("Additional items","google-site-kit"):i,l=t.savedItemSlugs,u=void 0===l?[]:l,d=t.availableSavedItems,g=void 0===d?{}:d,m=t.availableUnsavedItems,f=void 0===m?{}:m,p=t.ItemComponent,b=t.notice,v=function(t){return Object.keys(t).map((function(n){return e.createElement(p,r()({key:n,slug:n,savedItemSlugs:u},t[n]))}))},h=Object.keys(f).length;return e.createElement("div",{className:"googlesitekit-selection-panel-items"},0!==u.length&&e.createElement(c.Fragment,null,e.createElement("p",{className:"googlesitekit-selection-panel-items__subheading"},a),e.createElement("div",{className:"googlesitekit-selection-panel-items__subsection"},v(g)),h>0&&e.createElement("p",{className:"googlesitekit-selection-panel-items__subheading"},o)),h>0&&e.createElement("div",{className:"googlesitekit-selection-panel-items__subsection"},v(f)),b)}SelectionPanelItems.propTypes={currentSelectionTitle:o.a.string,availableItemsTitle:o.a.string,savedItemSlugs:o.a.array,availableSavedItems:o.a.object,availableUnsavedItems:o.a.object,ItemComponent:o.a.elementType,notice:o.a.node}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelFooter}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(15),s=n.n(c),l=n(14),u=n(1),d=n.n(u),g=n(0),m=n(38),f=n(2),p=n(3),b=n(10),v=n(120),h=n(9),y=n(8),O=n(44),_=n(54);function SelectionPanelFooter(t){var n=t.savedItemSlugs,a=void 0===n?[]:n,i=t.selectedItemSlugs,c=void 0===i?[]:i,u=t.saveSettings,d=void 0===u?function(){}:u,E=t.saveError,k=t.itemLimitError,S=t.minSelectedItemCount,j=void 0===S?0:S,T=t.maxSelectedItemCount,A=void 0===T?0:T,N=t.isBusy,C=t.onSaveSuccess,w=void 0===C?function(){}:C,I=t.onCancel,M=void 0===I?function(){}:I,R=t.isOpen,D=t.closePanel,x=void 0===D?function(){}:D,L=Object(g.useState)(null),P=s()(L,2),G=P[0],Z=P[1],B=Object(g.useState)(!1),U=s()(B,2),F=U[0],z=U[1],V=Object(p.useSelect)((function(e){return e(y.r).isFetchingSyncAvailableAudiences()})),H=Object(g.useMemo)((function(){return!Object(l.isEqual)(Object(h.E)(c),Object(h.E)(a))}),[a,c]),W=(null==a?void 0:a.length)>0&&H?Object(f.__)("Apply changes","google-site-kit"):Object(f.__)("Save selection","google-site-kit"),q=Object(g.useCallback)(o()(r.a.mark((function e(){var t;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,d(c);case 2:t=e.sent,t.error||(w(),x(),Z(W),z(!0));case 5:case"end":return e.stop()}}),e)}))),[d,c,w,x,W]),K=Object(g.useCallback)((function(){x(),M()}),[x,M]),Y=Object(g.useState)(null),$=s()(Y,2),X=$[0],J=$[1];Object(g.useEffect)((function(){null!==X&&X!==R&&R&&(Z(null),z(!1)),J(R)}),[R,X]);var Q=(null==c?void 0:c.length)||0,ee=V?e.createElement(O.a,{width:"89px",height:"20px"}):e.createElement("p",{className:"googlesitekit-selection-panel-footer__item-count"},Object(m.a)(Object(f.sprintf)(
/* translators: 1: Number of selected items. 2: Maximum number of items that can be selected. */
Object(f.__)("%1$d selected <MaxCount>(up to %2$d)</MaxCount>","google-site-kit"),Q,A),{MaxCount:e.createElement("span",{className:"googlesitekit-selection-panel-footer__item-count--max-count"})}));return e.createElement("footer",{className:"googlesitekit-selection-panel-footer"},E&&e.createElement(v.a,{error:E}),e.createElement("div",{className:"googlesitekit-selection-panel-footer__content"},H&&k?e.createElement(_.a,{noPrefix:!0,message:k}):ee,e.createElement("div",{className:"googlesitekit-selection-panel-footer__actions"},e.createElement(b.Button,{tertiary:!0,onClick:K,disabled:N},Object(f.__)("Cancel","google-site-kit")),e.createElement(b.SpinnerButton,{onClick:q,isSaving:N,disabled:Q<j||Q>A||N||!R&&F},G||W))))}SelectionPanelFooter.propTypes={savedItemSlugs:d.a.array,selectedItemSlugs:d.a.array,saveSettings:d.a.func,saveError:d.a.object,itemLimitError:d.a.string,minSelectedItemCount:d.a.number,maxSelectedItemCount:d.a.number,isBusy:d.a.bool,onSaveSuccess:d.a.func,onCancel:d.a.func,isOpen:d.a.bool,closePanel:d.a.func}}).call(this,n(4))},,,,,,,,function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M7.334 11.333h1.333v-4H7.334v4zM8.001 6a.658.658 0 00.667-.667.605.605 0 00-.2-.467.605.605 0 00-.467-.2.658.658 0 00-.667.667c0 .189.061.35.183.483A.69.69 0 008.001 6zm0 8.666a6.583 6.583 0 01-2.6-.516 6.85 6.85 0 01-2.117-1.434A6.85 6.85 0 011.851 10.6 6.582 6.582 0 011.334 8c0-.923.172-1.79.517-2.6a6.85 6.85 0 011.433-2.117c.6-.6 1.306-1.072 2.117-1.417A6.404 6.404 0 018 1.333c.922 0 1.789.178 2.6.533a6.618 6.618 0 012.116 1.417c.6.6 1.072 1.306 1.417 2.117.355.81.533 1.677.533 2.6 0 .922-.178 1.789-.533 2.6a6.619 6.619 0 01-1.417 2.116 6.85 6.85 0 01-2.116 1.434 6.583 6.583 0 01-2.6.516zm0-1.333c1.489 0 2.75-.517 3.783-1.55s1.55-2.294 1.55-3.783c0-1.49-.517-2.75-1.55-3.784-1.033-1.033-2.294-1.55-3.783-1.55-1.49 0-2.75.517-3.784 1.55C3.184 5.25 2.667 6.511 2.667 8c0 1.489.517 2.75 1.55 3.783 1.034 1.033 2.295 1.55 3.784 1.55z",fill:"currentColor"});t.a=function SvgInfoGreen(e){return a.createElement("svg",r({viewBox:"0 0 16 16",fill:"none"},e),i)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanel}));var a=n(11),r=n.n(a),i=n(1),o=n.n(i),c=n(327);function SelectionPanel(t){var n=t.children,a=t.isOpen,i=t.isLoading,o=t.onOpen,s=t.closePanel,l=t.className,u=null==l?void 0:l.split(/\s+/).map((function(e){return".".concat(e)})).join(""),d=u?"".concat(u," .googlesitekit-selection-panel-item .googlesitekit-selection-box input"):".googlesitekit-selection-panel-item .googlesitekit-selection-box input";return e.createElement(c.a,{className:r()("googlesitekit-selection-panel",l),isOpen:a,isLoading:i,onOpen:o,closeSheet:s,focusTrapOptions:{initialFocus:d}},n)}SelectionPanel.propTypes={children:o.a.node,isOpen:o.a.bool,isLoading:o.a.bool,onOpen:o.a.func,closePanel:o.a.func,className:o.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SideSheet}));var a=n(6),r=n.n(a),i=n(11),o=n.n(i),c=n(405),s=n.n(c),l=n(1),u=n.n(l),d=n(209),g=n(392),m=n(0),f=n(56),p=n(72);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function SideSheet(t){var n=t.className,a=t.children,r=t.isOpen,i=t.isLoading,c=t.onOpen,l=void 0===c?function(){}:c,u=t.closeSheet,b=void 0===u?function(){}:u,h=t.focusTrapOptions,y=void 0===h?{}:h,O=Object(m.useRef)();return Object(m.useEffect)((function(){r?(l(),document.body.classList.add("googlesitekit-side-sheet-scroll-lock")):document.body.classList.remove("googlesitekit-side-sheet-scroll-lock")}),[r,l]),Object(d.a)(O,b),Object(g.a)((function(e){return r&&f.c===e.keyCode}),b),e.createElement(p.a,null,e.createElement(s.a,{active:!!r&&!i,focusTrapOptions:v({fallbackFocus:"body"},y)},e.createElement("section",{ref:O,className:o()("googlesitekit-side-sheet",n,{"googlesitekit-side-sheet--open":r}),role:"dialog","aria-modal":"true","aria-hidden":!r,tabIndex:"0"},a)),r&&e.createElement("span",{className:"googlesitekit-side-sheet-overlay"}))}SideSheet.propTypes={className:u.a.string,children:u.a.node,isOpen:u.a.bool,isLoading:u.a.bool,onOpen:u.a.func,closeSheet:u.a.func,focusTrapOptions:u.a.object}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionBox}));var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(10);function SelectionBox(t){var n=t.badge,a=t.checked,r=t.children,i=t.disabled,s=t.id,l=t.onChange,u=t.title,d=t.value;return e.createElement("div",{className:o()("googlesitekit-selection-box",{"googlesitekit-selection-box--disabled":i})},e.createElement(c.Checkbox,{checked:a,description:r,disabled:i,id:s,name:s,onChange:l,value:d,badge:n},u))}SelectionBox.propTypes={badge:r.a.node,checked:r.a.bool,children:r.a.node,disabled:r.a.bool,id:r.a.string,onChange:r.a.func,title:r.a.string,value:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(51),r=n.n(a),i=n(53),o=n.n(i),c=n(68),s=n.n(c),l=n(69),u=n.n(l),d=n(49),g=n.n(d),m=n(1),f=n.n(m),p=n(0),b=n(17),v=n(20);function h(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=g()(e);if(t){var r=g()(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return u()(this,n)}}var y=function(t){s()(LayoutHeader,t);var n=h(LayoutHeader);function LayoutHeader(){return r()(this,LayoutHeader),n.apply(this,arguments)}return o()(LayoutHeader,[{key:"render",value:function(){var t=this.props,n=t.title,a=t.badge,r=t.ctaLabel,i=t.ctaLink,o=i?{alignMiddle:!0,smSize:4,lgSize:6}:{alignMiddle:!0,smSize:4,mdSize:8,lgSize:12};return e.createElement("header",{className:"googlesitekit-layout__header"},e.createElement(b.e,null,e.createElement(b.k,null,n&&e.createElement(b.a,o,e.createElement("h3",{className:"googlesitekit-subheading-1 googlesitekit-layout__header-title"},n,a)),i&&e.createElement(b.a,{alignMiddle:!0,mdAlignRight:!0,smSize:4,lgSize:6},e.createElement(v.a,{href:i,external:!0},r)))))}}]),LayoutHeader}(p.Component);y.propTypes={title:f.a.string,badge:f.a.node,ctaLabel:f.a.string,ctaLink:f.a.string},y.defaultProps={title:"",badge:null,ctaLabel:"",ctaLink:""},t.a=y}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(51),r=n.n(a),i=n(53),o=n.n(i),c=n(68),s=n.n(c),l=n(69),u=n.n(l),d=n(49),g=n.n(d),m=n(1),f=n.n(m),p=n(0),b=n(17),v=n(134);function h(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=g()(e);if(t){var r=g()(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return u()(this,n)}}var y=function(t){s()(LayoutFooter,t);var n=h(LayoutFooter);function LayoutFooter(){return r()(this,LayoutFooter),n.apply(this,arguments)}return o()(LayoutFooter,[{key:"render",value:function(){var t=this.props,n=t.ctaLabel,a=t.ctaLink,r=t.footerContent;return e.createElement("footer",{className:"googlesitekit-layout__footer"},e.createElement(b.e,null,e.createElement(b.k,null,e.createElement(b.a,{size:12},a&&n&&e.createElement(v.a,{className:"googlesitekit-data-block__source",name:n,href:a,external:!0}),r))))}}]),LayoutFooter}(p.Component);y.propTypes={ctaLabel:f.a.string,ctaLink:f.a.string},t.a=y}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CreateAccountField}));var a=n(11),r=n.n(a),i=n(10);function CreateAccountField(t){var n=t.hasError,a=t.value,o=t.setValue,c=t.name,s=t.label;return void 0===a?null:e.createElement(i.TextField,{className:r()("mdc-text-field",{"mdc-text-field--error":n}),label:s,name:c,onChange:function(e){o(e.target.value,c)},outlined:!0,value:a,id:"googlesitekit_analytics_account_create_".concat(c)})}}).call(this,n(4))},,,,,,,,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var a=n(14),r=n(122);function i(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(a.isPlainObject)(e)&&(!(!e.hasOwnProperty("fieldNames")||!Array.isArray(e.fieldNames)||0===e.fieldNames.length)&&(!(!e.hasOwnProperty("limit")||"number"!=typeof e.limit)&&!(e.hasOwnProperty("orderby")&&!Object(r.e)(e.orderby))))}))}},,,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return EnhancedMeasurementSwitch}));var a=n(6),r=n.n(a),i=n(11),o=n.n(i),c=n(1),s=n.n(c),l=n(81),u=n(0),d=n(38),g=n(2),m=n(3),f=n(10),p=n(29),b=n(8),v=n(199),h=n(9),y=n(18),O=n(372);function EnhancedMeasurementSwitch(t){var n=t.className,a=t.onClick,i=t.disabled,c=void 0!==i&&i,s=t.loading,_=void 0!==s&&s,E=t.formName,k=void 0===E?b.j:E,S=t.isEnhancedMeasurementAlreadyEnabled,j=void 0!==S&&S,T=t.showTick,A=void 0!==T&&T,N=Object(m.useSelect)((function(e){return e(p.a).getValue(k,b.i)})),C=Object(y.a)(),w=Object(m.useDispatch)(p.a).setValues,I=Object(u.useCallback)((function(){w(k,r()({},b.i,!N)),Object(h.I)("".concat(C,"_analytics"),N?"deactivate_enhanced_measurement":"activate_enhanced_measurement"),null==a||a()}),[k,N,a,w,C]);return Object(l.a)((function(){w(b.j,r()({},b.k,!0))})),e.createElement("div",{className:o()("googlesitekit-analytics-enable-enhanced-measurement",n,{"googlesitekit-analytics-enable-enhanced-measurement--loading":_})},_&&e.createElement(f.ProgressBar,{small:!0,className:"googlesitekit-analytics-enable-enhanced-measurement__progress--settings-edit"}),!_&&j&&e.createElement("div",{className:"googlesitekit-analytics-enable-enhanced-measurement__already-enabled-label"},A&&e.createElement("div",{className:"googlesitekit-analytics-enable-enhanced-measurement__already-enabled-tick"},e.createElement(O.a,null)),Object(g.__)("Enhanced measurement is enabled for this web data stream","google-site-kit")),!_&&!j&&e.createElement(f.Switch,{label:Object(g.__)("Enable enhanced measurement","google-site-kit"),checked:N,disabled:c,onClick:I,hideLabel:!1}),e.createElement("p",{className:"googlesitekit-module-settings-group__helper-text"},Object(d.a)(Object(g.__)("This allows you to measure interactions with your content (e.g. file downloads, form completions, video views). <a>Learn more</a>","google-site-kit"),{a:e.createElement(v.a,{path:"/analytics/answer/9216061",external:!0})})))}EnhancedMeasurementSwitch.propTypes={onClick:s.a.func,disabled:s.a.bool,loading:s.a.bool,isEnhancedMeasurementAlreadyEnabled:s.a.bool,showTick:s.a.bool}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return r}));var a=n(0);function r(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;Object(a.useEffect)((function(){var a,r=!1,i=function(){a=e.setTimeout((function(){r=!0}),n)},o=function(){e.clearTimeout(a),r&&(r=!1,t())};return e.addEventListener("focus",o),e.addEventListener("blur",i),function(){e.removeEventListener("focus",o),e.removeEventListener("blur",i),e.clearTimeout(a)}}),[n,t])}}).call(this,n(28))},,,,,,function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return ChipTabGroup}));var r,i=n(21),o=n.n(i),c=n(27),s=n.n(c),l=n(15),u=n.n(l),d=n(6),g=n.n(d),m=n(81),f=n(420),p=n(0),b=n(422),v=n(2),h=n(3),y=n(10),O=n(26),_=n(29),E=n(8),k=n(23),S=n(7),j=n(19),T=n(360),A=n(361),N=n(362),C=n(24),w=n(121),I=n(214),M=n(131),R=n(74),D=(r={},g()(r,O.c.SLUG,I.a),g()(r,O.g.SLUG,M.a),r);function ChipTabGroup(t){var n=t.allMetricItems,r=t.savedItemSlugs,i=Object(p.useRef)(),c=Object(p.useState)(O.c.SLUG),l=u()(c,2),d=l[0],I=l[1],M=Object(p.useState)(0),x=u()(M,2),L=x[0],P=x[1],G=Object(C.e)()===C.b,Z=Object(h.useSelect)((function(e){return e(_.a).getValue(O.j,O.i)})),B=Object(h.useSelect)((function(e){return e(_.a).getValue(O.j,O.a)||[]})),U=Object(h.useSelect)((function(e){return e(_.a).getValue(O.j,O.o)||[]})),F=Object(h.useSelect)((function(e){return e(S.a).isUserInputCompleted()})),z=Object(h.useSelect)((function(e){var t,n=e(S.a).getUserPickedMetrics();if(null==n?void 0:n.length){var a=e(E.r).getKeyMetricsConversionEventWidgets();return Object.keys(a).filter((function(e){return n.some((function(t){return a[e].includes(t)}))}))}var r=e(S.a).getUserInputSettings();return null==r||null===(t=r.includeConversionEvents)||void 0===t?void 0:t.values})),V=Object(h.useSelect)((function(e){return e(j.a).isModuleConnected("analytics-4")})),H=Object(h.useSelect)((function(e){return V?e(E.r).getDetectedEvents():[]})),W=Object(h.useSelect)((function(e){return e(S.a).getAnswerBasedMetrics(null,[].concat(s()(z||[]),s()(H||[])))})),q=[E.l.SUBMIT_LEAD_FORM,E.l.CONTACT,E.l.GENERATE_LEAD].filter((function(e){return(null==H?void 0:H.includes(e))||(null==z?void 0:z.includes(e))})),K=[E.l.ADD_TO_CART,E.l.PURCHASE].filter((function(e){return(null==H?void 0:H.includes(e))||(null==z?void 0:z.includes(e))})),Y=Object(p.useMemo)((function(){return[O.h,O.d].concat(s()((null==q?void 0:q.length)?[O.e]:[]),s()((null==K?void 0:K.length)?[O.f]:[]),[O.b])}),[q,K]),$=Object(p.useMemo)((function(){return F&&(null==W?void 0:W.length)?[O.c,O.g]:[O.c]}),[F,W]),X=Object(p.useMemo)((function(){return[].concat(s()($),s()(Y))}),[$,Y]),J=Object(h.useSelect)((function(e){if(!V)return[];var t=e(E.r).getNewBadgeEvents();if((null==H?void 0:H.length)&&(null==t?void 0:t.length)){var n=H.filter((function(e){return E.e.includes(e)})),a=t.filter((function(e){return E.e.includes(e)})),r=t.filter((function(e){return!E.e.includes(e)}));if((null==n?void 0:n.length)>1&&a.length>0)return r}return t})),Q=Object(h.useSelect)((function(e){return V?e(E.r).getKeyMetricsConversionEventWidgets():[]})),ee=Object(p.useCallback)((function(){var e,t,n,a=null===(e=i.current)||void 0===e?void 0:e.querySelector(".mdc-tab-scroller__scroll-content");if(G){var r=null===(t=i.current)||void 0===t?void 0:t.querySelectorAll(".googlesitekit-chip-tab-group__tab-items .mdc-tab");if((null==r?void 0:r.length)&&a){var o=null===(n=i.current)||void 0===n?void 0:n.getBoundingClientRect(),c=[];r.forEach((function(e,t){var n=e.getBoundingClientRect();n.left>=o.left&&n.right<=o.right&&c.push(t)}));var s=r[c.length];if(s){var l=s.getBoundingClientRect();(l.left>=o.right||l.left-o.right<0&&-(l.left-o.right)<=20)&&("2px"===a.style.columnGap?a.style.columnGap="20px":a.style.columnGap="2px",ee())}}}}),[G]),te=g()({},O.c.SLUG,0),ne={},ae={},re=function(e){var t,a=n[e].group;if((a===d||d===O.c.SLUG&&B.includes(e))&&(ne[e]=n[e]),d===O.g.SLUG&&W.includes(e)&&W.includes(e)&&(ne[e]=n[e]),!te[a]){var r=Object.keys(n).filter((function(e){return!(n[e].group!==a||!(null==Z?void 0:Z.includes(e)))})).length;te[a]=r}(null==J?void 0:J.length)&&(J.some((function(t){return Q[t].includes(e)}))&&(ae[a]=[].concat(s()(null!==(t=ae[a])&&void 0!==t?t:[]),[e])))};for(var ie in n)re(ie);var oe=Object(h.useDispatch)(_.a).setValues,ce=Object(p.useCallback)((function(){var e;oe(O.j,(e={},g()(e,O.i,Z),g()(e,O.a,[].concat(s()(B),s()(U))),g()(e,O.o,[]),e))}),[Z,B,U,oe]),se=Object(p.useCallback)((function(e,t){if(e)I(e);else{var n=X[t];P(t),I(n.SLUG)}U.length&&ce()}),[X,U,I,ce]),le=Object(h.useSelect)((function(e){return e(k.b).getValue(O.k)})),ue=Object(b.a)(le),de=Object.keys(ae);Object(p.useEffect)((function(){if(!ue&&le)if(I(O.c.SLUG),P(0),de.length&&G){var e=X.find((function(e){return e.SLUG===de[0]}));P(X.indexOf(e)),I(e.SLUG)}else P(0),I(O.c.SLUG);ue&&!le&&ce(),!ue&&le&&ee()}),[le,ue,U,X,G,de,ce,ee]);var ge=Object(w.a)(ee,50);Object(m.a)((function(){e.addEventListener("resize",ge)})),Object(f.a)((function(){return e.removeEventListener("resize",ge)}));var me=[[].concat(s()($),s()(Y.slice(0,2))),s()(Y.slice(2))];return a.createElement("div",{className:"googlesitekit-chip-tab-group"},a.createElement("div",{className:"googlesitekit-chip-tab-group__tab-items",ref:i},!G&&me.map((function(e){return a.createElement("div",{key:"row-".concat(e[0].SLUG),className:"googlesitekit-chip-tab-group__tab-items-row"},e.map((function(e){return a.createElement(T.a,{key:e.SLUG,slug:e.SLUG,label:e.LABEL,hasNewBadge:!!(null==ae?void 0:ae[e.SLUG]),isActive:e.SLUG===d,onClick:se,selectedCount:te[e.SLUG]})})))})),G&&a.createElement(y.TabBar,{activeIndex:L,handleActiveIndexUpdate:function(e){return se(null,e)}},X.map((function(e,t){var n=D[e.SLUG]||R.a;return a.createElement(y.Tab,{key:t,"aria-label":e.LABEL},a.createElement(n,{width:12,height:12,className:"googlesitekit-chip-tab-group__chip-item-svg googlesitekit-chip-tab-group__tab-item-mobile-svg googlesitekit-chip-tab-group__chip-item-svg__".concat(e.SLUG)}),e.LABEL,te[e.SLUG]>0&&a.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-count"},"(",te[e.SLUG],")"),!!(null==ae?void 0:ae[e.SLUG])&&a.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-new-dot"}))})))),a.createElement("div",{className:"googlesitekit-chip-tab-group__tab-item"},Object.keys(ne).map((function(e){var t,n=ne[e].group,i=null==ae||null===(t=ae[n])||void 0===t?void 0:t.includes(e);return a.createElement(A.a,o()({key:e,slug:e,savedItemSlugs:r,isNewlyDetected:i},ne[e]))})),!Object.keys(ne).length&&a.createElement("div",{className:"googlesitekit-chip-tab-group__graphic"},a.createElement(N.a,{height:250}),a.createElement("p",null,Object(v.__)("No metrics were selected yet","google-site-kit")))))}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Chip}));var a,r=n(6),i=n.n(r),o=n(1),c=n.n(o),s=n(11),l=n.n(s),u=n(10),d=n(26),g=n(214),m=n(131),f=n(74),p=(a={},i()(a,d.c.SLUG,g.a),i()(a,d.g.SLUG,m.a),a);function Chip(t){var n=t.slug,a=t.label,r=t.isActive,i=t.onClick,o=t.hasNewBadge,c=void 0!==o&&o,s=t.selectedCount,d=void 0===s?0:s,g=p[n]||f.a;return e.createElement(u.Button,{className:l()("googlesitekit-chip-tab-group__chip-item",{"googlesitekit-chip-tab-group__chip-item--active":r}),icon:e.createElement(g,{width:12,height:12,className:"googlesitekit-chip-tab-group__chip-item-svg googlesitekit-chip-tab-group__chip-item-svg__".concat(n)}),trailingIcon:d>0?e.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-count"},"(",d,")"):null,onClick:function(){return i(n)}},a,c&&e.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-new-dot"}))}Chip.propTypes={slug:c.a.string.isRequired,label:c.a.string.isRequired,isActive:c.a.bool,hasNewBadge:c.a.bool,selectedCount:c.a.number,onClick:c.a.func.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricItem}));var a=n(6),r=n.n(a),i=n(27),o=n.n(i),c=n(1),s=n.n(c),l=n(0),u=n(2),d=n(3),g=n(29),m=n(47),f=n(19),p=n(26),b=n(117);function MetricItem(t){var n=t.slug,a=t.title,i=t.description,c=t.isNewlyDetected,s=t.savedItemSlugs,v=void 0===s?[]:s,h=Object(d.useSelect)((function(e){var t=e(f.a).getModule,a=e(m.a).getWidget(n);return null==a?void 0:a.modules.reduce((function(e,n){var a=t(n);return(null==a?void 0:a.connected)||!(null==a?void 0:a.name)?e:[].concat(o()(e),[a.name])}),[])})),y=Object(d.useSelect)((function(e){return e(g.a).getValue(p.j,p.i)})),O=Object(d.useSelect)((function(e){return e(g.a)})).getValue,_=Object(d.useDispatch)(g.a).setValues,E=Object(l.useCallback)((function(e){var t,a=O(p.j,p.i),i=e.target.checked?a.concat([n]):a.filter((function(e){return e!==n}));_(p.j,(t={},r()(t,p.i,i),r()(t,p.o,i),t))}),[O,_,n]),k=null==y?void 0:y.includes(n),S=!v.includes(n)&&h.length>0,j="key-metric-selection-checkbox-".concat(n);return e.createElement(b.c,{id:j,slug:n,title:a,description:i,isNewlyDetected:c,isItemSelected:k,isItemDisabled:S,onCheckboxChange:E},h.length>0&&e.createElement("div",{className:"googlesitekit-selection-panel-item-error"},Object(u.sprintf)(
/* translators: %s: module names. */
Object(u._n)("%s is disconnected, no data to show","%s are disconnected, no data to show",h.length,"google-site-kit"),h.join(Object(u.__)(" and ","google-site-kit")))))}MetricItem.propTypes={slug:s.a.string.isRequired,title:s.a.string.isRequired,description:s.a.string.isRequired,isNewlyDetected:s.a.bool,savedItemSlugs:s.a.array}}).call(this,n(4))},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M59.238 58.571c-2.136 20.178 4.272 29.099 20.48 53.216 16.209 24.118-29.092 62.914 5.475 101.268 33.827 37.532 69.419.009 111.314-4.555 29.443-3.208 57.819 12.98 90.86 5.9 33.04-7.08 46.385-42.599 43.153-68.059-5.59-44.041-26.24-49.107-34.893-66.461-8.654-17.354 2.902-52.997-30.287-73.16-33.19-20.163-76.71 14.42-112.503 12.37-20.651-1.182-40.932-4.995-59.264.86-18.53 5.918-32.662 22.571-34.335 38.621z",fill:"#B8E6CA"}),o=a.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter0_d_2200_11981)"},a.createElement("rect",{x:242.455,y:45.266,width:130.621,height:89.651,rx:10.957,transform:"rotate(15 242.455 45.266)",fill:"#fff"})),c=a.createElement("rect",{x:253.726,y:64.785,width:24.903,height:7.969,rx:3.985,transform:"rotate(15 253.726 64.785)",fill:"#EBEEF0"}),s=a.createElement("rect",{x:249.342,y:81.144,width:49.806,height:19.923,rx:9.961,transform:"rotate(15 249.342 81.144)",fill:"#FFDED3"}),l=a.createElement("rect",{x:240.436,y:114.357,width:99.428,height:8.773,rx:3.985,transform:"rotate(15 240.436 114.357)",fill:"#EBEEF0"}),u=a.createElement("path",{d:"M256.195 90.198l4.644 8.044m0 0l1.412-4.986m-1.412 4.986l-5.023-1.27",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),d=a.createElement("rect",{x:268.706,y:93.551,width:19.923,height:5.977,rx:1.992,transform:"rotate(15 268.706 93.55)",fill:"#fff"}),g=a.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter1_d_2200_11981)"},a.createElement("rect",{x:13.887,y:79.094,width:130.621,height:89.68,rx:10.957,transform:"rotate(-15 13.887 79.094)",fill:"#fff"})),m=a.createElement("rect",{x:32.989,y:90.122,width:62.386,height:7.798,rx:3.899,transform:"rotate(-15 32.99 90.122)",fill:"#EBEEF0"}),f=a.createElement("rect",{x:37.691,y:106.902,width:49.806,height:19.923,rx:9.961,transform:"rotate(-15 37.691 106.902)",fill:"#FFDED3"}),p=a.createElement("rect",{x:46.612,y:140.967,width:99.428,height:7.798,rx:3.899,transform:"rotate(-15 46.612 140.967)",fill:"#EBEEF0"}),b=a.createElement("path",{d:"M48.152 111.318l8.044 4.645m0 0l-1.27-5.024m1.27 5.024l-4.986 1.411",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),v=a.createElement("rect",{x:60.663,y:107.966,width:19.923,height:5.977,rx:1.992,transform:"rotate(-15 60.663 107.966)",fill:"#fff"}),h=a.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter2_d_2200_11981)"},a.createElement("rect",{x:126.251,y:37.4,width:130.621,height:89.68,rx:10.957,fill:"#fff"})),y=a.createElement("rect",{x:143.013,y:53.134,width:98.333,height:7.867,rx:3.933,fill:"#EBEEF0"}),O=a.createElement("rect",{x:142.369,y:70.423,width:49.806,height:19.923,rx:9.961,fill:"#B8E6CA"}),_=a.createElement("rect",{x:143.013,y:105.84,width:33.04,height:7.867,rx:3.933,fill:"#EBEEF0"}),E=a.createElement("path",{d:"M151.336 84.036l6.568-6.567m0 0l-5.182-.073m5.182.073l.073 5.18",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),k=a.createElement("rect",{x:164.287,y:77.395,width:19.923,height:5.977,rx:1.992,fill:"#fff"}),S=a.createElement("path",{d:"M59.237 58.571C57.1 78.75 63.509 87.67 79.717 111.787c16.209 24.118-29.091 62.914 5.475 101.268 33.827 37.532 69.419.009 111.314-4.555 29.444-3.208 57.82 12.98 90.86 5.9s46.385-42.599 43.153-68.059c-5.59-44.041-26.24-49.107-34.893-66.461-8.654-17.354 2.902-52.997-30.287-73.16-33.19-20.163-76.71 14.42-112.503 12.37-20.651-1.182-40.932-4.995-59.264.86C75.042 25.867 60.91 42.52 59.237 58.57z",fill:"#B8E6CA"}),j=a.createElement("g",{mask:"url(#key-metrics-no-selected-items_svg__a)"},a.createElement("path",{d:"M227.674 108.973l11.312-8.418M218.925 98.852l2.868-12.68M205.623 102.87l-5.375-13.037",stroke:"#CBD0D3",strokeWidth:3.147,strokeMiterlimit:10}),a.createElement("path",{d:"M63.953 190.487c16.127 12.193 38.716 10.349 55.335 5.162 16.618-5.187 31.107-14.61 45.314-23.791 6.717-4.337 13.617-8.738 21.496-11.119 7.878-2.381 17.057-2.39 22.958 1.658 3.392 2.328 5.205 5.923 5.36 9.702",stroke:"#3C7251",strokeWidth:9.44,strokeLinejoin:"round"}),a.createElement("path",{d:"M215.831 109.67l-19.169 71.73",stroke:"#CBD0D3",strokeWidth:9.44,strokeMiterlimit:10,strokeLinecap:"round"}),a.createElement("path",{d:"M213.975 116.472l-19.169 71.731",stroke:"#161B18",strokeWidth:9.44,strokeMiterlimit:10})),T=a.createElement("defs",null,a.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter0_d_2200_11981",x:205.773,y:35.772,width:176.33,height:147.36,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},a.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),a.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),a.createElement("feOffset",{dy:3.985}),a.createElement("feGaussianBlur",{stdDeviation:7.969}),a.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),a.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),a.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),a.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})),a.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter1_d_2200_11981",x:.409,y:35.793,width:176.337,height:147.388,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},a.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),a.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),a.createElement("feOffset",{dy:3.985}),a.createElement("feGaussianBlur",{stdDeviation:7.969}),a.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),a.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),a.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),a.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})),a.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter2_d_2200_11981",x:110.313,y:25.447,width:162.497,height:121.556,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},a.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),a.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),a.createElement("feOffset",{dy:3.985}),a.createElement("feGaussianBlur",{stdDeviation:7.969}),a.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),a.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),a.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),a.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})));t.a=function SvgKeyMetricsNoSelectedItems(e){return a.createElement("svg",r({viewBox:"0 0 383 238",fill:"none"},e),i,o,c,s,l,u,d,g,m,f,p,b,v,h,y,O,_,E,k,a.createElement("mask",{id:"key-metrics-no-selected-items_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:58,y:0,width:273,height:230},S),j,T)}},function(e,t){e.exports=googlesitekit.notifications},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M0 19h22L11 0 0 19zm12-3h-2v-2h2v2zm0-4h-2V8h2v4z",fill:"currentColor"});t.a=function SvgWarningV2(e){return a.createElement("svg",r({viewBox:"0 0 22 19"},e),i)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PageHeader}));var a=n(11),r=n.n(a),i=n(1),o=n.n(i),c=n(17),s=n(311),l=n(312),u=n(76);function PageHeader(t){var n=t.title,a=t.icon,i=t.className,o=t.status,d=t.statusText,g=t.fullWidth,m=t.children,f=g?{size:12}:{smSize:4,mdSize:4,lgSize:6},p=""!==o||Boolean(m);return e.createElement("header",{className:"googlesitekit-page-header"},e.createElement(c.k,null,n&&e.createElement(c.a,f,a,e.createElement("h1",{className:r()("googlesitekit-page-header__title",i)},n)),p&&e.createElement(c.a,{alignBottom:!0,mdAlignRight:!0,smSize:4,mdSize:4,lgSize:6},e.createElement("div",{className:"googlesitekit-page-header__details"},o&&e.createElement("span",{className:r()("googlesitekit-page-header__status","googlesitekit-page-header__status--".concat(o))},d,e.createElement(u.a,null,"connected"===o?e.createElement(s.a,{width:10,height:8}):e.createElement(l.a,{width:2,height:12}))),m))))}PageHeader.propTypes={title:o.a.string,icon:o.a.node,className:o.a.string,status:o.a.string,statusText:o.a.string,fullWidth:o.a.bool},PageHeader.defaultProps={title:"",icon:null,className:"googlesitekit-heading-3",status:"",statusText:"",fullWidth:!1}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return GoogleChart}));var r=n(6),i=n.n(r),o=n(27),c=n.n(o),s=n(21),l=n.n(s),u=n(15),d=n.n(u),g=n(25),m=n.n(g),f=(n(594),n(11)),p=n.n(f),b=n(12),v=n.n(b),h=n(1),y=n.n(h),O=n(429),_=n(81),E=n(208),k=n(0),S=n(44),j=n(7),T=n(114),A=n(3),N=n(508),C=n(509),w=n(23),I=n(18),M=n(173),R=n(263),D=n(9),x=n(24);function L(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function P(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?L(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):L(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function GoogleChart(t){var n=t.chartEvents,r=t.chartType,i=t.children,o=t.className,s=t.data,u=t.dateMarkers,g=t.getChartWrapper,f=t.height,b=t.loaded,h=t.loadingHeight,y=t.loadingWidth,L=t.onMouseOver,P=t.onMouseOut,G=t.onReady,Z=t.onSelect,B=t.selectedStats,U=t.width,F=t.options,z=t.gatheringData,V=m()(t,["chartEvents","chartType","children","className","data","dateMarkers","getChartWrapper","height","loaded","loadingHeight","loadingWidth","onMouseOver","onMouseOut","onReady","onSelect","selectedStats","width","options","gatheringData"]),H=Object(E.a)(GoogleChart),W=Object(x.e)(),q=Object(A.useSelect)((function(e){return e(j.a).getDateRangeDates({offsetDays:0})})),K=q.startDate,Y=q.endDate,$=Object(I.a)(),X=Object(A.useSelect)((function(e){return e(w.b).getValue("googleChartsCollisionError")})),J=Object(k.useState)(!1),Q=d()(J,2),ee=Q[0],te=Q[1],ne=Object(A.useDispatch)(w.b).setValue,ae=Object(R.d)(s,B),re="PieChart"===r?"circular":"square",ie=Object(R.e)(h,f,y,U),oe=e.createElement("div",{className:"googlesitekit-chart-loading"},e.createElement(S.a,l()({className:"googlesitekit-chart-loading__wrapper",shape:re},ie))),ce=Object(k.useRef)(),se=Object(k.useRef)();Object(_.a)((function(){var e,t,n,r;void 0===X&&(Object(M.a)($)&&(null===(e=a)||void 0===e||null===(t=e.google)||void 0===t?void 0:t.charts)&&(a.google.charts=void 0),!Object(M.a)($)&&(null===(n=a)||void 0===n||null===(r=n.google)||void 0===r?void 0:r.charts)?ne("googleChartsCollisionError",!0):ne("googleChartsCollisionError",!1))})),Object(k.useEffect)((function(){return function(){if(se.current&&ce.current){var e=se.current.visualization.events;e.removeAllListeners(ce.current.getChart()),e.removeAllListeners(ce.current)}}}),[]),Object(k.useLayoutEffect)((function(){var e,t;L&&(null===(e=se.current)||void 0===e||e.visualization.events.addListener(ce.current.getChart(),"onmouseover",(function(e){L(e,{chartWrapper:ce.current,google:se.current})})));P&&(null===(t=se.current)||void 0===t||t.visualization.events.addListener(ce.current.getChart(),"onmouseout",(function(e){P(e,{chartWrapper:ce.current,google:se.current})})))}),[L,P]);var le=u.filter((function(e){return!!((t=new Date(e.date))&&K&&Y)&&!(t.getTime()<Object(D.G)(K).getTime()||t.getTime()>Object(D.G)(Y).getTime());var t}));if(X)return null;if(!b)return e.createElement("div",{className:p()("googlesitekit-chart","googlesitekit-chart-loading__forced",o)},oe);var ue=Object(R.b)([].concat(c()(n||[]),[{eventName:"ready",callback:function(){var e;if(ce.current&&le.length){var t=ce.current.getChart(),n=null==t?void 0:t.getChartLayoutInterface(),a=null==n?void 0:n.getChartAreaBoundingBox(),r=ce.current.getDataTable();if(n&&a&&r){le.forEach((function(e,t){var r=new Date(e.date),i=document.getElementById("googlesitekit-chart__date-marker-line--".concat(H,"-").concat(t));v()(i,"#googlesitekit-chart__date-marker-line--".concat(H,"-").concat(t," is missing from the DOM, but required to render date markers."));var o=Math.floor(n.getXLocation(Object(D.G)(Object(D.p)(r))));if(Object.assign(i.style,{left:"".concat(o-1,"px"),top:"".concat(Math.floor(a.top),"px"),height:"".concat(Math.floor(a.height),"px"),opacity:1}),e.text){var c=document.getElementById("googlesitekit-chart__date-marker-tooltip--".concat(H,"-").concat(t));v()(c,"#googlesitekit-chart__date-marker-tooltip--".concat(H,"-").concat(t," is missing from the DOM, but required to render date marker tooltips.")),Object.assign(c.style,{left:"".concat(o-9,"px"),top:"".concat(Math.floor(a.top)-18,"px"),opacity:1})}}));var i=null===(e=document.querySelector("#googlesitekit-chart-".concat(H," svg:first-of-type > g:first-of-type > g > g > text")))||void 0===e?void 0:e.parentElement.parentElement.parentElement;!!i&&document.querySelectorAll("#googlesitekit-chart-".concat(H," svg:first-of-type > g")).length>=3&&(i.style.transform="translateY(-10px)")}}}}]),G,Z),de=Object(R.a)(F,z,r,K,Y,W);return e.createElement(N.a,null,e.createElement("div",{className:p()("googlesitekit-chart","googlesitekit-chart--".concat(r),o),id:"googlesitekit-chart-".concat(H),tabIndex:-1},e.createElement(O.a,l()({className:"googlesitekit-chart__inner",chartEvents:ue,chartLanguage:Object(D.r)(),chartType:r,chartVersion:"49",data:ae,loader:oe,height:f,getChartWrapper:function(e,t){var n,a,r;(ee||te(!0),e!==ce.current)&&(null===(n=se.current)||void 0===n||n.visualization.events.removeAllListeners(null===(a=ce.current)||void 0===a?void 0:a.getChart()),null===(r=se.current)||void 0===r||r.visualization.events.removeAllListeners(ce.current));ce.current=e,se.current=t,g&&g(e,t)},width:U,options:de},V)),z&&ee&&e.createElement(T.b,{style:T.a.OVERLAY}),!!le.length&&le.map((function(t,n){return e.createElement(C.a,{key:"googlesitekit-chart__date-marker--".concat(H,"-").concat(n),id:"".concat(H,"-").concat(n),text:t.text})})),i))}GoogleChart.propTypes={className:y.a.string,children:y.a.node,chartEvents:y.a.arrayOf(y.a.shape({eventName:y.a.string,callback:y.a.func})),chartType:y.a.oneOf(["LineChart","PieChart"]).isRequired,data:y.a.array,dateMarkers:y.a.arrayOf(y.a.shape({date:y.a.string.isRequired,text:y.a.string})),getChartWrapper:y.a.func,height:y.a.string,loaded:y.a.bool,loadingHeight:y.a.string,loadingWidth:y.a.string,onMouseOut:y.a.func,onMouseOver:y.a.func,onReady:y.a.func,onSelect:y.a.func,selectedStats:y.a.arrayOf(y.a.number),width:y.a.string,options:y.a.object,gatheringData:y.a.bool},GoogleChart.defaultProps=P(P({},O.a.defaultProps),{},{dateMarkers:[],gatheringData:!1,loaded:!0})}).call(this,n(4),n(28))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupAccountSiteUI}));var a=n(1),r=n.n(a),i=n(0),o=n(3),c=n(10),s=n(32),l=n(162),u=n(410),d=n(31);function SetupAccountSiteUI(t){var n=t.heading,a=t.description,r=t.primaryButton,g=t.secondaryButton,m=Object(o.useSelect)((function(e){return e(d.l).isDoingSubmitChanges()||e(s.a).isNavigating()}));return e.createElement(i.Fragment,null,e.createElement("h3",{className:"googlesitekit-heading-4 googlesitekit-setup-module__title"},n),e.createElement(l.d,null),e.createElement("p",null,a),e.createElement(u.a,null),e.createElement("div",{className:"googlesitekit-setup-module__action"},e.createElement(c.SpinnerButton,{onClick:r.onClick,href:r.href,disabled:m,isSaving:m},r.label),g&&e.createElement("div",{className:"googlesitekit-setup-module__sub-action"},e.createElement(c.Button,{tertiary:!0,onClick:g.onClick},g.label))))}SetupAccountSiteUI.propTypes={heading:r.a.string.isRequired,description:r.a.string.isRequired,primaryButton:r.a.shape({label:r.a.string,href:r.a.string,onClick:r.a.func}).isRequired,secondaryButton:r.a.shape({label:r.a.string,onClick:r.a.func})}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return f})),n.d(t,"b",(function(){return p}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(12),s=n.n(c),l=n(14),u=n(273),d=n(106),g=n(157),m=n(13),f=function(e,t){var n=t.find((function(t){return t.test(e)}));return!!n&&n.exec(e)[1]},p=Object(l.memoize)(function(){var e=o()(r.a.mark((function e(t){var n,a,i,o;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.homeURL,a=t.ampMode,s()(Object(d.a)(n),"homeURL must be valid URL"),i=[n],m.b!==a){e.next=14;break}return e.prev=4,e.next=7,Object(u.default)({path:"/wp/v2/posts?per_page=1"}).then((function(e){return e.slice(0,1).map((function(e){return Object(g.a)(e.link,{amp:1})})).pop()}));case 7:(o=e.sent)&&i.push(o),e.next=14;break;case 11:return e.prev=11,e.t0=e.catch(4),e.abrupt("return",i);case 14:return e.abrupt("return",i);case 15:case"end":return e.stop()}}),e,null,[[4,11]])})));return function(t){return e.apply(this,arguments)}}())},,,function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M1 3.838L4.106 7 10 1",stroke:"currentColor",strokeWidth:1.5});t.a=function SvgTick(e){return a.createElement("svg",r({viewBox:"0 0 11 9",fill:"none"},e),i)}},,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsCTAContent}));var a=n(15),r=n.n(a),i=n(11),o=n.n(i),c=n(1),s=n.n(c),l=n(240),u=n(0),d=n(3),g=n(13),m=n(7),f=n(24),p=n(9),b=n(18),v=n(17),h=n(450),y=n(451),O=n(452),_=n(453),E=n(79);function KeyMetricsCTAContent(t){var n=t.className,a=t.title,i=t.description,c=t.actions,s=t.ga4Connected,k=Object(u.useRef)(),S=Object(f.e)(),j=Object(E.a)(),T=Object(b.a)(),A=S===f.b,N=S===f.c&&j<960,C=j>=1280,w=j>=960&&j<1280;s||(N=S===f.c&&j<800,w=j>=800&&j<1280);var I=Object(l.a)(k,{threshold:.25}),M=Object(u.useState)(!1),R=r()(M,2),D=R[0],x=R[1],L=!!(null==I?void 0:I.intersectionRatio),P=Object(d.useDispatch)(m.a).triggerSurvey,G=Object(d.useSelect)((function(e){return e(g.c).isUsingProxy()}));return Object(u.useEffect)((function(){L&&!D&&(s&&Object(p.I)("".concat(T,"_kmw-cta-notification"),"view_notification"),G&&P("view_kmw_setup_cta",{ttl:p.f}),x(!0))}),[L,T,s,D,G,P]),e.createElement("section",{ref:k,className:o()("googlesitekit-setup__wrapper","googlesitekit-setup__wrapper--key-metrics-setup-cta",n)},e.createElement(v.e,null,e.createElement(v.k,null,e.createElement(v.a,{smSize:5,mdSize:6,lgSize:5,className:"googlesitekit-widget-key-metrics-content__wrapper"},e.createElement("div",{className:"googlesitekit-widget-key-metrics-text__wrapper"},e.createElement("h3",{className:"googlesitekit-publisher-win__title"},a),e.createElement("p",null,i)),e.createElement("div",{className:"googlesitekit-widget-key-metrics-actions__wrapper"},c),N&&e.createElement(v.a,{className:"googlesitekit-widget-key-metrics-svg__wrapper"},e.createElement(O.a,null)),A&&e.createElement(v.a,{className:"googlesitekit-widget-key-metrics-svg__wrapper"},e.createElement(_.a,null))),w&&e.createElement(v.a,{className:"googlesitekit-widget-key-metrics-svg__wrapper"},e.createElement(y.a,null)),C&&e.createElement(v.a,{className:"googlesitekit-widget-key-metrics-svg__wrapper",smSize:6,mdSize:3,lgSize:6},e.createElement(h.a,null)))))}KeyMetricsCTAContent.propTypes={title:s.a.string,description:s.a.string,actions:s.a.node}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsCTAFooter}));var a=n(1),r=n.n(a),i=n(2),o=n(17),c=n(20);function KeyMetricsCTAFooter(t){var n=t.onActionClick,a=void 0===n?function(){}:n,r=t.showDismiss;return e.createElement(o.k,{className:"googlesitekit-widget-key-metrics-footer"},e.createElement(o.a,{size:12,className:"googlesitekit-widget-key-metrics-footer__cta-wrapper"},!r&&e.createElement("span",null,Object(i.__)("Interested in specific metrics?","google-site-kit")),e.createElement(c.a,{onClick:a},r?Object(i.__)("Maybe later","google-site-kit"):Object(i.__)("Select your own metrics","google-site-kit"))))}KeyMetricsCTAFooter.propTypes={onActionClick:r.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileHeader}));var a=n(1),r=n.n(a),i=n(132),o=n(105);function MetricTileHeader(t){var n=t.title,a=t.infoTooltip,r=t.loading;return e.createElement("div",{className:"googlesitekit-km-widget-tile__title-container"},e.createElement("h3",{className:"googlesitekit-km-widget-tile__title"},n),r?e.createElement(o.a,null,e.createElement(i.a,{title:a})):e.createElement(i.a,{title:a}))}MetricTileHeader.propTypes={title:r.a.string,infoTooltip:r.a.oneOfType([r.a.string,r.a.element]),loading:r.a.bool}}).call(this,n(4))},,function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var a=n(545);function r(e){if(Object(a.b)(e))return e.match(/pub-\d+$/)[0]}function i(e){if(Object(a.a)(e))return e.match(/pub-\d+$/)[0]}},,,,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notice}));var a=n(2),r=n(3),i=n(142),o=n(20),c=n(13);function Notice(){var t=Object(r.useSelect)((function(e){return e(c.c).getDocumentationLinkURL("ga4")}));return e.createElement(i.c,{type:i.a,LearnMore:function LearnMore(){return e.createElement(o.a,{href:t,external:!0},Object(a.__)("Learn more here.","google-site-kit"))},notice:Object(a.__)("Got a Google Analytics property and want to find out how to use it with Site Kit?","google-site-kit")})}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdBlockerWarningMessage}));var a=n(1),r=n.n(a),i=n(2),o=n(38),c=n(20),s=n(217),l=n(396);function AdBlockerWarningMessage(t){var n=t.className,a=void 0===n?"":n,r=t.getHelpLink,u=void 0===r?"":r,d=t.warningMessage,g=void 0===d?null:d;return g?e.createElement(s.a,{className:a},Object(o.a)(Object(i.sprintf)(
/* translators: 1: The warning message. 2: "Get help" text. */
Object(i.__)("%1$s. <Link>%2$s</Link>","google-site-kit"),g,Object(i.__)("Get help","google-site-kit")),{Link:e.createElement(c.a,{href:u,external:!0,hideExternalIndicator:!0,trailingIcon:e.createElement(l.a,{width:15,height:15})})})):null}AdBlockerWarningMessage.propTypes={className:r.a.string,getHelpLink:r.a.string,warningMessage:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M4.5 1.5H3a2 2 0 00-2 2v7a2 2 0 002 2h7a2 2 0 002-2V9M7 1.5h5v5M5 8.5L11.5 2",stroke:"currentColor",strokeWidth:1.5});t.a=function SvgExternalRounded(e){return a.createElement("svg",r({viewBox:"0 0 13 14",fill:"none"},e),i)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var a=n(3),r=n(19),i=n(7),o=n(26),c=n(8),s=n(66);function l(){return Object(a.useSelect)((function(e){var t=e(i.a).isItemDismissed(o.l),n=e(i.a).isDismissingItem(o.l),a=u(e,"search-console",s.b),r=u(e,"analytics-4",c.r);return!1===t&&!1===n&&a&&r}),[])}function u(e,t,n){if(e(r.a).isModuleConnected(t)){var a=e(n),i=a.isGatheringData,o=a.isDataAvailableOnLoad;return i(),o()}}},,,,function(e,t){e.exports=googlesitekit.widgets},function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(0);function Sparkline(t){var n=t.sparkline,a=t.invertChangeColor,r=n;return r&&a&&(r=Object(i.cloneElement)(n,{invertChangeColor:a})),e.createElement("div",{className:"googlesitekit-data-block__sparkline"},r)}Sparkline.propTypes={sparkline:r.a.element,invertChangeColor:r.a.bool},t.a=Sparkline}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(2),s=n(9),l=n(85);function Change(t){var n=t.change,a=t.changeDataUnit,r=t.period,i=t.invertChangeColor,u=n;return a&&(u="%"===a?Object(s.B)(n,{style:"percent",signDisplay:"never",maximumFractionDigits:1}):Object(s.B)(n,a)),r&&(u=Object(c.sprintf)(r,u)),e.createElement("div",{className:o()("googlesitekit-data-block__change",{"googlesitekit-data-block__change--no-change":!n})},!!n&&e.createElement("span",{className:"googlesitekit-data-block__arrow"},e.createElement(l.a,{direction:0<parseFloat(n)?"up":"down",invertColor:i})),e.createElement("span",{className:"googlesitekit-data-block__value"},u))}Change.propTypes={change:r.a.oneOfType([r.a.string,r.a.number]),changeDataUnit:r.a.oneOfType([r.a.string,r.a.bool]),period:r.a.string,invertChangeColor:r.a.bool},t.a=Change}).call(this,n(4))},,,,function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return d})),n.d(t,"a",(function(){return g})),n.d(t,"d",(function(){return m})),n.d(t,"c",(function(){return AutoAdExclusionSwitches}));var a,r=n(6),i=n.n(r),o=n(0),c=n(2),s=n(3),l=n(10),u=n(31),d="loggedinUsers",g="contentCreators",m=(a={},i()(a,d,Object(c.__)("All logged-in users","google-site-kit")),i()(a,g,Object(c.__)("Users who can write posts","google-site-kit")),a);function AutoAdExclusionSwitches(){var t,n=Object(s.useSelect)((function(e){return e(u.l).getAutoAdsDisabled()})),a=Object(s.useDispatch)(u.l).setAutoAdsDisabled;t=n&&n.includes(d)?Object(c.__)("Ads will not be displayed for all logged-in users","google-site-kit"):n&&n.includes(g)?Object(c.__)("Ads will not be displayed for users that can write posts","google-site-kit"):Object(c.__)("Ads will be displayed for all users","google-site-kit");var r=Object(o.useCallback)((function(e,t){var r=t?n.concat(e):n.filter((function(t){return t!==e}));a(r)}),[n,a]),i=Object(o.useCallback)((function(e){var t=e.target.checked;r(g,t)}),[r]),f=Object(o.useCallback)((function(e){var t=e.target.checked;r(d,t)}),[r]);return Array.isArray(n)?e.createElement("fieldset",{className:"googlesitekit-analytics-auto-ads-disabled"},e.createElement("legend",{className:"googlesitekit-setup-module__text"},Object(c.__)("Exclude from Ads","google-site-kit")),e.createElement("div",{className:"googlesitekit-settings-module__inline-items"},e.createElement("div",{className:"googlesitekit-settings-module__inline-item"},e.createElement(l.Switch,{label:m[d],checked:n.includes(d),onClick:f,hideLabel:!1})),!n.includes(d)&&e.createElement("div",{className:"googlesitekit-settings-module__inline-item"},e.createElement(l.Switch,{label:m[g],checked:n.includes(g),onClick:i,hideLabel:!1}))),e.createElement("p",null,t)):null}}).call(this,n(4))},,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupUseSnippetSwitch}));var a=n(0),r=n(2),i=n(3),o=n(31),c=n(383),s=n(162);function SetupUseSnippetSwitch(){var t,n,l=Object(i.useSelect)((function(e){return e(o.l).getOriginalUseSnippet()})),u=Object(i.useSelect)((function(e){return e(o.l).getExistingTag()})),d=Object(i.useSelect)((function(e){return e(o.l).getClientID()})),g=Object(i.useDispatch)(o.l),m=g.setUseSnippet,f=g.saveSettings,p=Boolean(u);if(Object(a.useEffect)((function(){p&&(m(!1),f())}),[p,f,m]),l&&!u||void 0===u||void 0===l)return null;var b=Object(r.__)("Make sure to remove the existing AdSense code to avoid conflicts with the code placed by Site Kit","google-site-kit");return u===d?(n=Object(r.__)("You’ve already got an AdSense code on your site for this account. We recommend you use Site Kit to place the code to get the most out of AdSense.","google-site-kit"),t="".concat(n," ").concat(b)):u?(n=Object(r.sprintf)(
/* translators: 1: existing account ID, 2: current account ID */
Object(r.__)("Site Kit detected AdSense code for a different account %1$s on your site. In order to configure AdSense for your current account %2$s, we recommend you use Site Kit to place the code instead.","google-site-kit"),Object(c.a)(u),Object(c.a)(d)),t="".concat(n," ").concat(b)):t=b,e.createElement(s.e,{checkedMessage:t,uncheckedMessage:n,saveOnChange:!0})}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var a=n(15),r=n.n(a),i=n(535),o=n(356),c=n(0),s=n(139),l=n(23),u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.sticky,n=void 0!==t&&t,a=Object(c.useContext)(s.a),u=Object(c.useState)(!1),d=r()(u,2),g=d[0],m=d[1],f=Object(o.a)((function(e){return e(l.b).getInViewResetCount()})),p=Object(o.a)((function(e){return e(l.b).getValue("forceInView")}));return Object(c.useEffect)((function(){a.value&&!g&&m(!0)}),[g,a,m]),Object(c.useEffect)((function(){p&&m(!0)}),[p]),Object(i.a)((function(){m(!1)}),[f]),!(!n||!g)||!!a.value}},,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupEnhancedConversionTrackingNotice}));var a=n(11),r=n.n(a),i=n(3),o=n(13);function SetupEnhancedConversionTrackingNotice(t){var n=t.className,a=t.message,c=Object(i.useSelect)((function(e){return e(o.c).isConversionTrackingEnabled()}));return c||void 0===c?null:e.createElement("p",{className:r()(n,"googlesitekit-color--surfaces-on-background-variant")},a)}}).call(this,n(4))},,,,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UseSnippetSwitch}));var a=n(1),r=n.n(a),i=n(0),o=n(2),c=n(3),s=n(10),l=n(8),u=n(9),d=n(18);function UseSnippetSwitch(t){var n=t.description,a=Object(d.a)(),r=Object(c.useSelect)((function(e){return e(l.r).getUseSnippet()})),g=Object(c.useDispatch)(l.r).setUseSnippet,m=Object(i.useCallback)((function(){var e=!r;g(e),Object(u.I)("".concat(a,"_analytics"),e?"enable_tag":"disable_tag","ga4")}),[r,g,a]);return void 0===r?null:e.createElement("div",{className:"googlesitekit-analytics-usesnippet"},e.createElement(s.Switch,{label:Object(o.__)("Place Google Analytics code","google-site-kit"),checked:r,onClick:m,hideLabel:!1}),n)}UseSnippetSwitch.propTypes={description:r.a.node}}).call(this,n(4))},,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeMetricsLink}));var a=n(0),r=n(2),i=n(3),o=n(23),c=n(7),s=n(26),l=n(20),u=n(310),d=n(449),g=n(9),m=n(18),f=n(511);function ChangeMetricsLink(){var t=Object(i.useSelect)((function(e){return e(c.a).getKeyMetrics()})),n=Object(m.a)(),p=Object(i.useDispatch)(o.b).setValue,b=Object(a.useCallback)((function(){p(s.k,!0),Object(g.I)("".concat(n,"_kmw"),"change_metrics")}),[p,n]),v=Array.isArray(t)&&(null==t?void 0:t.length)>0;return Object(f.a)(v),v?e.createElement(a.Fragment,null,e.createElement(l.a,{secondary:!0,linkButton:!0,className:"googlesitekit-widget-area__cta-link googlesitekit-km-change-metrics-cta",onClick:b,leadingIcon:e.createElement(u.a,{width:22,height:22})},Object(r.__)("Change metrics","google-site-kit")),e.createElement(d.a,null)):null}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InsufficientPermissionsError}));var a=n(1),r=n.n(a),i=n(0),o=n(38),c=n(2),s=n(3),l=n(13),u=n(20),d=n(303),g=n(9),m=n(18);function InsufficientPermissionsError(t){var n=t.moduleSlug,a=t.onRetry,r=t.infoTooltip,f=t.headerText,p=Object(m.a)(),b=Object(s.useSelect)((function(e){return e(l.c).getErrorTroubleshootingLinkURL({code:"".concat(n,"_insufficient_permissions")})}));Object(i.useEffect)((function(){Object(g.J)("".concat(p,"_kmw"),"insufficient_permissions_error")}),[p]);var v=Object(i.useCallback)((function(){Object(g.I)("".concat(p,"_kmw"),"insufficient_permissions_error_retry"),null==a||a()}),[a,p]);return e.createElement(d.a,{title:Object(c.__)("Insufficient permissions","google-site-kit"),headerText:f,infoTooltip:r},e.createElement("div",{className:"googlesitekit-report-error-actions"},e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(o.a)(Object(c.__)("Permissions updated? <a>Retry</a>","google-site-kit"),{a:e.createElement(u.a,{onClick:v})})),e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(o.a)(Object(c.__)("You’ll need to contact your administrator. <a>Learn more</a>","google-site-kit"),{a:e.createElement(u.a,{href:b,external:!0,hideExternalIndicator:!0})}))))}InsufficientPermissionsError.propTypes={moduleSlug:r.a.string.isRequired,onRetry:r.a.func.isRequired,headerText:r.a.string,infoTooltip:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(1),s=n.n(c),l=n(2),u=n(0),d=n(3),g=n(10),m=n(379),f=n(380),p=n(7),b=n(13),v=n(26),h=n(50),y=n(175),O=n(9),_=n(18),E=n(397),k=n(454),S=n(32);function KeyMetricsSetupCTAWidget(t){var n=t.Widget,a=t.WidgetNull,i=Object(_.a)(),c=Object(E.a)(),s=Object(d.useSelect)((function(e){return e(b.c).getAdminURL("googlesitekit-user-input")})),h=Object(d.useSelect)((function(e){return e(b.c).getAdminURL("googlesitekit-metric-selection")})),j={tooltipSlug:v.l,title:Object(l.__)("You can always set up goals in Settings later","google-site-kit"),content:Object(l.__)("The Key Metrics section will be added back to your dashboard once you set your goals in Settings","google-site-kit"),dismissLabel:Object(l.__)("Got it","google-site-kit")},T=Object(y.b)(j),A=Object(d.useDispatch)(p.a).dismissItem,N=function(){var e=o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(O.I)("".concat(i,"_kmw-cta-notification"),"dismiss_notification");case 2:return T(),e.next=5,A(v.l);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),C=Object(d.useDispatch)(S.a).navigateTo,w=Object(u.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(O.I)("".concat(i,"_kmw-cta-notification"),"confirm_pick_own_metrics");case 2:C(h);case 3:case"end":return e.stop()}}),e)}))),[C,h,i]),I=Object(u.useCallback)((function(){Object(O.I)("".concat(i,"_kmw-cta-notification"),"confirm_get_tailored_metrics")}),[i]);return c?e.createElement(n,{noPadding:!0,Footer:function Footer(){return e.createElement(f.a,{onActionClick:w})}},e.createElement(m.a,{title:Object(l.__)("Get personalized suggestions for user interaction metrics based on your goals","google-site-kit"),description:Object(l.__)("Answer 3 questions and we’ll suggest relevant metrics for your dashboard. These metrics will help you track how users interact with your site.","google-site-kit"),actions:e.createElement(u.Fragment,null,e.createElement(k.a,null),e.createElement(g.Button,{className:"googlesitekit-key-metrics-cta-button",href:s,onClick:I},Object(l.__)("Get tailored metrics","google-site-kit")),e.createElement(g.Button,{tertiary:!0,onClick:N},Object(l.__)("Maybe later","google-site-kit"))),ga4Connected:!0})):e.createElement(a,null)}KeyMetricsSetupCTAWidget.propTypes={Widget:s.a.elementType.isRequired,WidgetNull:s.a.elementType},t.a=Object(h.a)({moduleName:"analytics-4"})(KeyMetricsSetupCTAWidget)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileNumeric}));var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(9),u=n(184),d=n(202);function MetricTileNumeric(t){var n=t.metricValue,a=t.metricValueFormat,i=t.subText,c=t.previousValue,s=t.currentValue,g=o()(t,["metricValue","metricValueFormat","subText","previousValue","currentValue"]),m=Object(l.m)(a);return e.createElement(d.a,r()({className:"googlesitekit-km-widget-tile--numeric"},g),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-container"},e.createElement("div",{className:"googlesitekit-km-widget-tile__metric"},Object(l.B)(n,m)),e.createElement("p",{className:"googlesitekit-km-widget-tile__subtext"},i)),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-change-container"},e.createElement(u.a,{previousValue:c,currentValue:s,isAbsolute:"percent"===(null==m?void 0:m.style)})))}MetricTileNumeric.propTypes={metricValue:s.a.oneOfType([s.a.string,s.a.number]),metricValueFormat:s.a.oneOfType([s.a.string,s.a.object]),subtext:s.a.string,previousValue:s.a.number,currentValue:s.a.number}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileTable}));var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(14),u=n(11),d=n.n(u),g=n(202);function MetricTileTable(t){var n=t.rows,a=void 0===n?[]:n,i=t.columns,c=void 0===i?[]:i,s=t.limit,u=t.ZeroState,m=o()(t,["rows","columns","limit","ZeroState"]),f=null;return(null==a?void 0:a.length)>0?f=a.slice(0,s||a.length).map((function(t,n){return e.createElement("div",{key:n,className:"googlesitekit-table__body-row"},c.map((function(n,a){var r=n.Component,i=n.field,o=n.className,c=void 0!==i?Object(l.get)(t,i):void 0;return e.createElement("div",{key:a,className:d()("googlesitekit-table__body-item",o)},r&&e.createElement(r,{row:t,fieldValue:c}),!r&&c)})))})):u&&(f=e.createElement("div",{className:"googlesitekit-table__body-row googlesitekit-table__body-row--no-data"},e.createElement("div",{className:"googlesitekit-table__body-zero-data"},e.createElement(u,null)))),e.createElement(g.a,r()({className:"googlesitekit-km-widget-tile--table"},m),e.createElement("div",{className:"googlesitekit-km-widget-tile__table"},f))}MetricTileTable.propTypes={rows:s.a.array,columns:s.a.array,limit:s.a.number,ZeroState:s.a.elementType}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileTablePlainText}));var a=n(1),r=n.n(a);function MetricTileTablePlainText(t){var n=t.content;return e.createElement("p",{className:"googlesitekit-km-widget-tile__table-plain-text"},n)}MetricTileTablePlainText.propTypes={content:r.a.string.isRequired}}).call(this,n(4))},,,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupCompletedSurveyTrigger}));var a=n(0),r=n(3),i=n(13),o=n(7),c=n(9),s=n(251);function SetupCompletedSurveyTrigger(){var t=Object(r.useSelect)((function(e){return e(i.c).isKeyMetricsSetupCompleted()})),n=Object(r.useSelect)((function(e){return e(i.c).getKeyMetricsSetupCompletedBy()})),l=Object(r.useSelect)((function(e){return e(o.a).getID()}));return t?e.createElement(a.Fragment,null,e.createElement(s.a,{triggerID:"view_kmw",ttl:c.f}),n===l&&e.createElement(s.a,{triggerID:"view_kmw_setup_completed",ttl:c.f})):null}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsSetupDesktopSVG}));var a=n(0),r=n(2),i=n(44),o=n(151),c=Object(a.lazy)((function(){return n.e(38).then(n.bind(null,814))}));function KeyMetricsSetupDesktopSVG(){return e.createElement(a.Suspense,{fallback:e.createElement(i.a,{width:"100%",height:"235px"})},e.createElement(o.a,{errorMessage:Object(r.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsSetupSmallDesktopSVG}));var a=n(0),r=n(2),i=n(44),o=n(151),c=Object(a.lazy)((function(){return n.e(40).then(n.bind(null,815))}));function KeyMetricsSetupSmallDesktopSVG(){return e.createElement(a.Suspense,{fallback:e.createElement(i.a,{width:"100%",height:"235px"})},e.createElement(o.a,{errorMessage:Object(r.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsSetupTabletSVG}));var a=n(0),r=n(2),i=n(44),o=n(151),c=Object(a.lazy)((function(){return n.e(41).then(n.bind(null,816))}));function KeyMetricsSetupTabletSVG(){return e.createElement(a.Suspense,{fallback:e.createElement(i.a,{width:"100%",height:"235px"})},e.createElement(o.a,{errorMessage:Object(r.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsSetupMobileSVG}));var a=n(0),r=n(2),i=n(44),o=n(151),c=Object(a.lazy)((function(){return n.e(39).then(n.bind(null,817))}));function KeyMetricsSetupMobileSVG(){return e.createElement(a.Suspense,{fallback:e.createElement(i.a,{width:"100%",height:"235px"})},e.createElement(o.a,{errorMessage:Object(r.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return KeyMetricsSetupCTARenderedEffect}));var a=n(265),r=n(3),i=n(23);function KeyMetricsSetupCTARenderedEffect(){var e=Object(r.useDispatch)(i.b).setValue;return Object(a.a)((function(){e("KEY_METRICS_SETUP_CTA_RENDERED",!0)})),null}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GetHelpLink}));var a=n(1),r=n.n(a),i=n(38),o=n(2),c=n(20);function GetHelpLink(t){var n=t.linkURL;return Object(i.a)(
/* translators: %s: get help text. */
Object(o.__)("Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(c.a,{href:n,external:!0,hideExternalIndicator:!0},Object(o.__)("Get help","google-site-kit"))})}GetHelpLink.propTypes={linkURL:r.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileLoader}));var a=n(44);function MetricTileLoader(){return e.createElement("div",{className:"googlesitekit-km-widget-tile__loading"},e.createElement(a.a,{className:"googlesitekit-km-widget-tile__loading-header",width:"100%",height:"14px"}),e.createElement(a.a,{className:"googlesitekit-km-widget-tile__loading-body",width:"100%",height:"53px"}))}}).call(this,n(4))},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(44);function PreviewTable(t){for(var n=t.rows,a=t.rowHeight,r=t.padding,i=[],s=0;n>s;s++)i.push(e.createElement("div",{className:"googlesitekit-preview-table__row",key:"table-row-"+s},e.createElement(c.a,{width:"100%",height:a+"px"})));return e.createElement("div",{className:o()("googlesitekit-preview-table",{"googlesitekit-preview-table--padding":r})},i)}PreviewTable.propTypes={rows:r.a.number,rowHeight:r.a.number,padding:r.a.bool},PreviewTable.defaultProps={rows:11,rowHeight:35,padding:!1},t.a=PreviewTable}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportTable}));var a=n(15),r=n.n(a),i=n(11),o=n.n(i),c=n(12),s=n.n(c),l=n(1),u=n.n(l),d=n(14),g=n(0),m=n(10),f=n(114);function ReportTable(t){var n=t.rows,a=t.columns,i=t.className,c=t.limit,l=t.zeroState,u=t.gatheringData,p=void 0!==u&&u,b=t.tabbedLayout,v=void 0!==b&&b;function h(e){return!v&&e}s()(Array.isArray(n),"rows must be an array."),s()(Array.isArray(a),"columns must be an array."),a.forEach((function(e){var t=e.Component,n=e.field,a=void 0===n?null:n;s()(t||null!==a,"each column must define a Component and/or a field.")})),s()(Number.isInteger(c)||void 0===c,"limit must be an integer, if provided.");var y=a.some((function(e){return!!e.badge})),O=Object(g.useState)(0),_=r()(O,2),E=_[0],k=_[1],S=v&&a.slice(1),j=v?[a[0],S[E]]:a,T=j.filter((function(e){return!h(e.hideOnMobile)}));return e.createElement("div",{className:i},v&&e.createElement(m.TabBar,{className:"googlesitekit-tab-bar--start-aligned-high-contrast",activeIndex:E,handleActiveIndexUpdate:k},S.map((function(t){var n=t.title,a=t.badge;return e.createElement(m.Tab,{key:n,"aria-label":n},n,a)}))),e.createElement("div",{className:o()("googlesitekit-table","googlesitekit-table--with-list",{"googlesitekit-table--gathering-data":p})},e.createElement("table",{className:o()("googlesitekit-table__wrapper","googlesitekit-table__wrapper--".concat(j.length,"-col"),"googlesitekit-table__wrapper--mobile-".concat(T.length,"-col"),{"googlesitekit-table__wrapper--tabbed-layout":v})},!v&&e.createElement("thead",{className:"googlesitekit-table__head"},y&&e.createElement("tr",{className:o()("googlesitekit-table__head-badges",{"hidden-on-mobile":!a.some((function(e){var t=e.badge,n=e.hideOnMobile;return!!t&&!h(n)}))})},a.map((function(t,n){var a=t.badge,r=t.primary,i=t.hideOnMobile,c=t.className;return e.createElement("th",{className:o()("googlesitekit-table__head-item","googlesitekit-table__head-item--badge",{"googlesitekit-table__head-item--primary":r,"hidden-on-mobile":h(i)},c),key:"googlesitekit-table__head-row-badge-".concat(n)},a)}))),e.createElement("tr",{className:"googlesitekit-table__head-row"},a.map((function(t,n){var a=t.title,r=t.description,i=t.primary,c=t.hideOnMobile,s=t.className;return e.createElement("th",{className:o()("googlesitekit-table__head-item",{"googlesitekit-table__head-item--primary":i,"hidden-on-mobile":h(c)},s),"data-tooltip":r,key:"googlesitekit-table__head-row-".concat(n)},a)})))),e.createElement("tbody",{className:"googlesitekit-table__body"},p&&e.createElement("tr",{className:"googlesitekit-table__body-row googlesitekit-table__body-row--no-data"},e.createElement("td",{className:"googlesitekit-table__body-item",colSpan:j.length},e.createElement(f.b,null))),!p&&!(null==n?void 0:n.length)&&l&&e.createElement("tr",{className:"googlesitekit-table__body-row googlesitekit-table__body-row--no-data"},e.createElement("td",{className:"googlesitekit-table__body-item",colSpan:j.length},e.createElement(l,null))),!p&&n.slice(0,c).map((function(t,n){return e.createElement("tr",{className:"googlesitekit-table__body-row",key:"googlesitekit-table__body-row-".concat(n)},j.map((function(n,a){var r=n.Component,i=n.field,c=n.hideOnMobile,s=n.className,l=void 0!==i?Object(d.get)(t,i):void 0;return e.createElement("td",{key:"googlesitekit-table__body-item-".concat(a),className:o()("googlesitekit-table__body-item",{"hidden-on-mobile":h(c)},s)},e.createElement("div",{className:"googlesitekit-table__body-item-content"},r&&e.createElement(r,{row:t,fieldValue:l}),!r&&l))})))}))))))}ReportTable.propTypes={rows:u.a.arrayOf(u.a.oneOfType([u.a.array,u.a.object])).isRequired,columns:u.a.arrayOf(u.a.shape({title:u.a.string,description:u.a.string,primary:u.a.bool,className:u.a.string,field:u.a.string,hideOnMobile:u.a.bool,Component:u.a.componentType,badge:u.a.node})).isRequired,className:u.a.string,limit:u.a.number,zeroState:u.a.func,gatheringData:u.a.bool,tabbedLayout:u.a.bool}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,a){var r=n(15),i=n.n(r),o=n(1),c=n.n(o),s=n(14),l=n(11),u=n.n(l),d=n(0);function TableOverflowContainer(t){var n=t.children,r=Object(d.useState)(!1),o=i()(r,2),c=o[0],l=o[1],g=Object(d.useRef)();Object(d.useEffect)((function(){m();var t=Object(s.debounce)(m,100);return e.addEventListener("resize",t),function(){return e.removeEventListener("resize",t)}}),[]);var m=function(){if(g.current){var e=g.current,t=e.scrollLeft,n=e.scrollWidth-e.offsetWidth;l(t<n-16&&0<n-16)}};return a.createElement("div",{onScroll:Object(s.debounce)(m,100),className:u()("googlesitekit-table-overflow",{"googlesitekit-table-overflow--gradient":c})},a.createElement("div",{ref:g,className:"googlesitekit-table-overflow__container"},n))}TableOverflowContainer.propTypes={children:c.a.element},t.a=TableOverflowContainer}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccessibleWarningIcon}));var a=n(1),r=n.n(a),i=n(0),o=n(2),c=n(105),s=n(364);function AccessibleWarningIcon(t){var n=t.height,a=void 0===n?12:n,r=t.screenReaderText,l=void 0===r?Object(o.__)("Error","google-site-kit"):r,u=t.width,d=void 0===u?14:u;return e.createElement(i.Fragment,null,e.createElement(c.a,null,l),e.createElement(s.a,{width:d,height:a}))}AccessibleWarningIcon.propTypes={height:r.a.number,screenReaderText:r.a.string,width:r.a.number}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return DataBlockGroup}));var r=n(81),i=n(420),o=n(0),c=n(121);function DataBlockGroup(t){var n=t.className,s=t.children,l=Object(o.useRef)(),u=function(){var t,n,a,r,i=null==l||null===(t=l.current)||void 0===t?void 0:t.querySelectorAll(".googlesitekit-data-block");if(i){var o=null===(n=i[0])||void 0===n?void 0:n.querySelector(".googlesitekit-data-block__datapoint");if(o){d(i,"");var c=parseInt(null===(a=e)||void 0===a||null===(r=a.getComputedStyle(o))||void 0===r?void 0:r.fontSize,10),s=c;i.forEach((function(t){var n,a,r,i=t.querySelector(".googlesitekit-data-block__datapoint");if(i){var o=parseInt(null===(n=e)||void 0===n||null===(a=n.getComputedStyle(i))||void 0===a?void 0:a.fontSize,10),c=null==i||null===(r=i.parentElement)||void 0===r?void 0:r.offsetWidth;if(i.scrollWidth>c&&o>14){for(;i.scrollWidth>c&&o>14;)o-=1,i.style.fontSize="".concat(o,"px");s=o}}})),c!==s&&d(i,"".concat(s,"px"))}}},d=function(e,t){e.forEach((function(e){var n=null==e?void 0:e.querySelector(".googlesitekit-data-block__datapoint");n&&(n.style.fontSize=t)}))},g=Object(c.a)(u,50);return Object(r.a)((function(){u(),e.addEventListener("resize",g)})),Object(i.a)((function(){return e.removeEventListener("resize",g)})),a.createElement("div",{ref:l,className:n},s)}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e,a){var r=n(51),i=n.n(r),o=n(53),c=n.n(o),s=n(237),l=n.n(s),u=n(68),d=n.n(u),g=n(69),m=n.n(g),f=n(49),p=n.n(f),b=n(195),v=n.n(b),h=n(1),y=n.n(h),O=n(0),_=n(2),E=n(95),k=n(172),S=n(61),j=n(9);function T(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=p()(e);if(t){var r=p()(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return m()(this,n)}}var A=function(t){d()(GoogleChartErrorHandler,t);var n=T(GoogleChartErrorHandler);function GoogleChartErrorHandler(e){var t;return i()(this,GoogleChartErrorHandler),(t=n.call(this,e)).state={error:null,info:null},t.onErrorClick=t.onErrorClick.bind(l()(t)),t}return c()(GoogleChartErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Google Charts error:",t,n),this.setState({error:t,info:n}),Object(j.I)("google_chart_error","handle_".concat(this.context||"unknown","_error"),"".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500))}},{key:"onErrorClick",value:function(){var e=this.state,t=e.error,n=e.info;v()("`".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack,"`"))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,r=t.info;return n?a.createElement("div",{className:"googlesitekit-googlechart-error-handler"},a.createElement(E.a,{description:a.createElement(O.Fragment,null,a.createElement("p",null,Object(_.__)("An error prevented this Google chart from being displayed properly. Report the exact contents of the error on the support forum to find out what caused it.","google-site-kit")),a.createElement(k.a,{message:n.message,componentStack:r.componentStack})),error:!0,onErrorClick:this.onErrorClick,onClick:this.onErrorClick,title:Object(_.__)("Error in Google Chart","google-site-kit")})):e}}]),GoogleChartErrorHandler}(O.Component);A.contextType=S.b,A.propTypes={children:y.a.node.isRequired},t.a=A}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DateMarker}));var a=n(0),r=n(266),i=n(589),o=n(10),c=n(18),s=n(121),l=n(9);function DateMarker(t){var n=t.id,u=t.text,d=Object(c.a)(),g="".concat(d,"_ga4-data-collection-line");Object(a.useEffect)((function(){Object(l.I)(g,"chart_line_view")}),[g]);var m=Object(a.useCallback)((function(){Object(l.I)(g,"chart_tooltip_view")}),[g]),f=Object(s.a)(m,5e3,{leading:!0,trailing:!1});return e.createElement(a.Fragment,null,e.createElement("div",{id:"googlesitekit-chart__date-marker-line--".concat(n),className:"googlesitekit-chart__date-marker-line"}),u&&e.createElement("div",{id:"googlesitekit-chart__date-marker-tooltip--".concat(n),className:"googlesitekit-chart__date-marker-tooltip"},e.createElement(o.Tooltip,{title:u,onOpen:f},e.createElement("span",null,e.createElement(r.a,{fill:"currentColor",icon:i.a,size:18})))))}}).call(this,n(4))},,function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var a=n(0),r=n(3),i=n(13),o=n(7),c=n(2),s=n(22),l={slug:"sharedKeyMetrics",contexts:[s.n,s.o,s.l,s.m],gaEventCategory:function(e){return"".concat(e,"_shared_key-metrics")},steps:[{target:".googlesitekit-km-change-metrics-cta",title:Object(c.__)("Personalize your key metrics","google-site-kit"),content:Object(c.__)("Another admin has set up these tailored metrics for your site. Click here to personalize them.","google-site-kit"),placement:"bottom-start"}]},u=function(e){var t=Object(r.useSelect)((function(e){return e(i.c).getKeyMetricsSetupCompletedBy()})),n=Object(r.useSelect)((function(e){return e(o.a).getID()})),c=Object(r.useDispatch)(o.a).triggerOnDemandTour,s=Number.isInteger(t)&&Number.isInteger(n)&&t>0&&n!==t;Object(a.useEffect)((function(){e&&s&&c(l)}),[e,s,c])}},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("path",{d:"M107.91 41.72c7.73-13.22 3.14-30.12-10.24-37.75C84.29-3.66 67.18.87 59.45 14.09c-.35.59-.66 1.2-.96 1.81l-26.1 44.66a27.172 27.172 0 00-1.6 2.75L3.67 110.1l48.45 27.16 26.98-46.4c.29-.44.57-.89.84-1.35.27-.46.52-.93.76-1.39l26.11-44.67c.38-.57.76-1.14 1.1-1.73z",fill:"#FBBC04"}),a.createElement("path",{d:"M52.34 137.11c-7.68 13.43-25 18.38-38.31 10.62-13.31-7.76-18.02-24.57-10.34-38s24.86-18.39 38.16-10.64c13.3 7.75 18.18 24.59 10.49 38.02z",fill:"#34A853"}),a.createElement("path",{d:"M158.79 51.86c-13.23-7.62-30.15-3.1-37.79 10.1l-27.66 47.8c-7.64 13.2-3.11 30.08 10.13 37.7 13.23 7.62 30.15 3.1 37.79-10.1l27.66-47.8c7.63-13.2 3.1-30.08-10.13-37.7z",fill:"#4285F4"}));t.a=function SvgAdsense(e){return a.createElement("svg",r({viewBox:"0 0 173 152"},e),i)}},,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return d})),n.d(t,"a",(function(){return TrackingExclusionSwitches}));var a,r=n(6),i=n.n(r),o=n(0),c=n(2),s=n(3),l=n(10),u=n(8),d=(a={},i()(a,"loggedinUsers",Object(c.__)("All logged-in users","google-site-kit")),i()(a,"contentCreators",Object(c.__)("Users that can write posts","google-site-kit")),a);function TrackingExclusionSwitches(){var t,n=Object(s.useSelect)((function(e){return e(u.r).getTrackingDisabled()})),a=Object(s.useDispatch)(u.r).setTrackingDisabled;t=n&&n.includes("loggedinUsers")?Object(c.__)("All logged-in users will be excluded from Analytics tracking","google-site-kit"):n&&n.includes("contentCreators")?Object(c.__)("Users that can write posts will be excluded from Analytics tracking","google-site-kit"):Object(c.__)("All logged-in users will be included in Analytics tracking","google-site-kit");var r=Object(o.useCallback)((function(e,t){var r=t?n.concat(e):n.filter((function(t){return t!==e}));a(r)}),[n,a]),i=Object(o.useCallback)((function(e){var t=e.target.checked;r("contentCreators",t)}),[r]),g=Object(o.useCallback)((function(e){var t=e.target.checked;r("loggedinUsers",t)}),[r]);return Array.isArray(n)?e.createElement("div",{className:"googlesitekit-settings-module__fields-group"},e.createElement("h4",{className:"googlesitekit-settings-module__fields-group-title"},Object(c.__)("Exclude Analytics","google-site-kit")),e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("div",{className:"googlesitekit-settings-module__inline-items"},e.createElement("div",{className:"googlesitekit-settings-module__inline-item"},e.createElement(l.Switch,{label:d.loggedinUsers,checked:n.includes("loggedinUsers"),onClick:g,hideLabel:!1})),!n.includes("loggedinUsers")&&e.createElement("div",{className:"googlesitekit-settings-module__inline-item"},e.createElement(l.Switch,{label:d.contentCreators,checked:n.includes("contentCreators"),onClick:i,hideLabel:!1}))),e.createElement("p",null,t))):null}}).call(this,n(4))},,,function(e,t,n){"use strict";n.d(t,"a",(function(){return g})),n.d(t,"b",(function(){return m}));var a=n(6),r=n.n(a),i=n(12),o=n.n(i),c=n(106),s=n(215),l=n(8);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e){var t=e.siteName,n=e.siteURL,a=e.timezone,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Intl.DateTimeFormat().resolvedOptions().timeZone;o()(Object(c.a)(n),"A valid siteURL is required.");var u=new URL(n),d=u.hostname,g=u.pathname;return r()({accountName:t||d,propertyName:"".concat(d).concat(g).replace(/\/$/,""),dataStreamName:d,countryCode:s.c[a]||s.c[i],timezone:s.c[a]?a:i},l.i,!0)}var m=function(e){return Array.isArray(e)?e.map((function(e){return d(d({},function(e){var t,n=null===(t=e.account)||void 0===t?void 0:t.match(/accounts\/([^/]+)/),a=null==n?void 0:n[1];return d(d({},e),{},{_id:a})}(e)),{},{propertySummaries:(e.propertySummaries||[]).map((function(e){return function(e){var t,n,a=null===(t=e.property)||void 0===t?void 0:t.match(/properties\/([^/]+)/),r=null==a?void 0:a[1],i=null===(n=e.parent)||void 0===n?void 0:n.match(/accounts\/([^/]+)/),o=null==i?void 0:i[1];return d(d({},e),{},{_id:r,_accountID:o})}(e)}))})})):e}},function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a);function WidgetHeaderTitle(t){var n=t.title;return e.createElement("h3",{className:"googlesitekit-subheading-1 googlesitekit-widget__header-title"},n)}WidgetHeaderTitle.propTypes={title:r.a.string.isRequired},t.a=WidgetHeaderTitle}).call(this,n(4))},,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConnectModuleCTATile}));var a=n(1),r=n.n(a),i=n(2),o=n(0),c=n(3),s=n(19),l=n(47),u=n(88),d=n(159),g=n(20),m=n(602),f=n(676),p=n(381),b=n(145);function ConnectModuleCTATile(t){var n,a=t.moduleSlug,r=Object(d.a)(a),v=Object(c.useSelect)((function(e){return e(s.a).getModule(a)})),h=Object(c.useSelect)((function(e){return e(s.a).getModuleIcon(a)})),y=Object(c.useSelect)((function(e){return e(l.a).getWidgets(u.AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY)||[]})).filter((function(e){return e.modules.includes(a)})),O=b.a[null===(n=y[0])||void 0===n?void 0:n.slug],_=y.length>1;return v?e.createElement("div",{className:"googlesitekit-widget--connectModuleCTATile googlesitekit-km-widget-tile ".concat(_&&" googlesitekit-km-widget-tile--combined")},!_&&e.createElement(p.a,{title:null==O?void 0:O.title,infoTooltip:null==O?void 0:O.description,loading:!1}),e.createElement("div",{className:"googlesitekit-km-widget-tile__body"},e.createElement("div",{className:"googlesitekit-km-connect-module-cta-tile"},h&&e.createElement("div",{className:"googlesitekit-km-connect-module-cta-tile__icon"},e.createElement(h,{width:"32",height:"32"})),e.createElement("div",{className:"googlesitekit-km-connect-module-cta-tile__content"},e.createElement("p",{className:"googlesitekit-km-connect-module-cta-tile__text"},_?Object(i.sprintf)(
/* translators: %s: module name */
Object(i.__)("%s is disconnected, some of your metrics can’t be displayed","google-site-kit"),v.name):Object(i.sprintf)(
/* translators: %s: module name */
Object(i.__)("%s is disconnected, metric can’t be displayed","google-site-kit"),v.name)),e.createElement(g.a,{secondary:!0,onClick:r},Object(i.sprintf)(
/* translators: %s: module name */
Object(i.__)("Connect %s","google-site-kit"),v.name))))),_&&e.createElement(o.Fragment,null,e.createElement("div",{className:"googlesitekit-km-connect-module-cta-tile__ghost-card"},e.createElement(m.a,null)),e.createElement("div",{className:"googlesitekit-km-connect-module-cta-tile__ghost-card"},e.createElement(m.a,null)),e.createElement("div",{className:"googlesitekit-km-connect-module-cta-tile__ghost-card"},e.createElement(f.a,null)))):null}ConnectModuleCTATile.propTypes={moduleSlug:r.a.string.isRequired}}).call(this,n(4))},,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupAccount}));var a=n(1),r=n.n(a),i=n(0),o=n(3),c=n(10),s=n(31),l=n(140),u=n(538),d=n(539),g=n(540),m=n(541);function SetupAccount(t){var n=t.account,a=t.finishSetup,r=n._id,f=n.state,p=Object(o.useSelect)((function(e){return e(s.l).getClientID()})),b=Object(o.useSelect)((function(e){return e(s.l).getCurrentSite(r)})),v=Object(o.useSelect)((function(e){return e(s.l).getAFCClient(r)})),h=Object(o.useDispatch)(s.l),y=h.setClientID,O=h.setAccountStatus,_=h.setSiteStatus;return Object(i.useEffect)((function(){(null==v?void 0:v._id)&&p!==v._id?y(v._id):null===v&&p&&y("")}),[v,p,y]),Object(i.useEffect)((function(){null===b&&_(l.o)}),[_,b]),Object(i.useEffect)((function(){void 0!==b&&(p?f===s.e?O(l.g):(null==v?void 0:v.state)===s.g?O(l.c):(null==v?void 0:v.state)===s.d?O(l.b):O(l.k):O(l.i))}),[f,v,p,O,b]),void 0===b?e.createElement(c.ProgressBar,null):p?null===b?e.createElement(g.a,null):f===s.e||(null==v?void 0:v.state)===s.g||(null==v?void 0:v.state)===s.d?e.createElement(m.a,null):e.createElement(u.a,{site:b,finishSetup:a}):e.createElement(d.a,null)}SetupAccount.propTypes={account:r.a.shape({_id:r.a.string,state:r.a.string}),finishSetup:r.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupAccountSite}));var a=n(1),r=n.n(a),i=n(0),o=n(2),c=n(3),s=n(31),l=n(140),u=n(671),d=n(672),g=n(673),m=n(674),f=n(54);function SetupAccountSite(t){var n=t.site,a=t.finishSetup,r=n.autoAdsEnabled,p=n.state,b=Object(c.useDispatch)(s.l).setSiteStatus;switch(Object(i.useEffect)((function(){var e;switch(p){case s.e:e=l.n;break;case s.g:e=l.r;break;case s.d:e=l.m;break;case s.f:e=r?l.p:l.q}e&&b(e)}),[r,b,p]),p){case s.e:return e.createElement(u.a,null);case s.g:return e.createElement(g.a,null);case s.d:return e.createElement(d.a,null);case s.f:return e.createElement(m.a,{site:n,finishSetup:a});default:return e.createElement(f.a,{message:Object(o.sprintf)(
/* translators: %s: invalid site state identifier */
Object(o.__)("Invalid site state %s","google-site-kit"),p)})}}SetupAccountSite.propTypes={site:r.a.shape({autoAdsEnabled:r.a.bool,state:r.a.string}).isRequired,finishSetup:r.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupAccountNoClient}));var a=n(0),r=n(38),i=n(2),o=n(10),c=n(199),s=n(162),l=n(9),u=n(18);function SetupAccountNoClient(){var t=Object(u.a)(),n=Object(a.useCallback)((function(){Object(l.I)("".concat(t,"_adsense"),"apply_afc")}),[t]);return e.createElement(a.Fragment,null,e.createElement("h3",{className:"googlesitekit-heading-4 googlesitekit-setup-module__title"},Object(i.__)("Looks like you need to upgrade your AdSense account","google-site-kit")),e.createElement(s.d,null),e.createElement("p",null,Object(r.a)(Object(i.__)("To start using AdSense on your website, you need to upgrade your account to add “AdSense for content”. <a>Learn more</a>","google-site-kit"),{a:e.createElement(c.a,{path:"/adsense/answer/6023158",external:!0,"aria-label":Object(i.__)("Learn more about updating your AdSense account","google-site-kit")})})),e.createElement("div",{className:"googlesitekit-setup-module__action"},e.createElement(o.Button,{href:"https://www.google.com/adsense",target:"_blank","aria-label":Object(i.__)("Learn more about updating your AdSense account","google-site-kit"),onClick:n},Object(i.__)("Apply now","google-site-kit"))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return SetupAccountCreateSite}));var r=n(0),i=n(2),o=n(3),c=n(10),s=n(31),l=n(162),u=n(9),d=n(18);function SetupAccountCreateSite(){var t=Object(d.a)(),n=Object(o.useSelect)((function(e){return e(s.l).getServiceAccountManageSiteURL()})),g=Object(r.useCallback)((function(a){a.preventDefault(),Object(u.I)("".concat(t,"_adsense"),"create_site"),e.open(n,"_blank")}),[n,t]);return a.createElement(r.Fragment,null,a.createElement("h3",{className:"googlesitekit-heading-4 googlesitekit-setup-module__title"},Object(i.__)("Add this site to your AdSense account","google-site-kit")),a.createElement(l.d,null),a.createElement("p",null,Object(i.__)("We’ve detected that you haven’t added this site to your AdSense account yet","google-site-kit")),a.createElement("div",{className:"googlesitekit-setup-module__action"},a.createElement(c.Button,{onClick:g,href:n},Object(i.__)("Add site to AdSense","google-site-kit"))))}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupAccountPendingTasks}));var a=n(0),r=n(2),i=n(3),o=n(10),c=n(410),s=n(31),l=n(162),u=n(9),d=n(18);function SetupAccountPendingTasks(){var t=Object(d.a)(),n=Object(a.useCallback)((function(){Object(u.I)("".concat(t,"_adsense"),"review_tasks")}),[t]),g=Object(i.useSelect)((function(e){return e(s.l).getServiceAccountURL()}));return e.createElement(a.Fragment,null,e.createElement("h3",{className:"googlesitekit-heading-4 googlesitekit-setup-module__title"},Object(r.__)("Your account isn’t ready to show ads yet","google-site-kit")),e.createElement(l.d,null),e.createElement("p",null,Object(r.__)("You need to fix some things before we can connect Site Kit to your AdSense account","google-site-kit")),e.createElement(c.a,null),e.createElement("div",{className:"googlesitekit-setup-module__action"},e.createElement(o.Button,{onClick:n,href:g},Object(r.__)("Review AdSense account","google-site-kit"))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return SetupCreateAccount}));var r=n(5),i=n.n(r),o=n(16),c=n.n(o),s=n(0),l=n(38),u=n(2),d=n(3),g=n(10),m=n(199),f=n(9),p=n(383),b=n(31),v=n(7),h=n(162),y=n(18);function SetupCreateAccount(){var t=Object(y.a)(),n="".concat(t,"_adsense"),r=Object(d.useSelect)((function(e){return e(v.a).getEmail()})),o=Object(d.useSelect)((function(e){return e(b.l).getExistingTag()})),O=Object(d.useSelect)((function(e){return e(b.l).getServiceCreateAccountURL()})),_=Object(s.useCallback)(function(){var t=c()(i.a.mark((function t(a){return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a.preventDefault(),t.next=3,Object(f.I)(n,"create_account");case 3:e.open(O,"_blank");case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),[O,n]);return a.createElement(s.Fragment,null,a.createElement("h3",{className:"googlesitekit-heading-4 googlesitekit-setup-module__title"},Object(u.__)("Create your AdSense account","google-site-kit")),a.createElement(h.d,null),a.createElement("p",null,Object(u.__)("Once you create your account, Site Kit will place AdSense code on every page across your site. This means your site will be automatically optimized to help you earn money from your content.","google-site-kit")),a.createElement(h.f,null),a.createElement("div",{className:"googlesitekit-setup-module__action"},a.createElement(g.Button,{onClick:_,href:O},Object(u.__)("Create AdSense account","google-site-kit"))),a.createElement("p",{className:"googlesitekit-setup-module__footer-text"},o&&Object(u.sprintf)(
/* translators: 1: client ID, 2: user email address, 3: account ID */
Object(u.__)("Site Kit detected AdSense code %1$s on your page. We recommend you remove that code or add %2$s as a user to the AdSense account %3$s.","google-site-kit"),o,r,Object(p.a)(o)),!o&&Object(l.a)(Object(u.sprintf)(
/* translators: %s: user email address */
Object(u.__)("Already use AdSense? Add %s as a user to an existing AdSense account. <a>Learn more</a>","google-site-kit"),r),{a:a.createElement(m.a,{path:"/adsense/answer/2659101",external:!0,"aria-label":Object(u.__)("Learn more about adding a user to an existing AdSense account","google-site-kit")})})))}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupSelectAccount}));var a=n(0),r=n(2),i=n(162);function SetupSelectAccount(){return e.createElement(a.Fragment,null,e.createElement("h3",{className:"googlesitekit-heading-4 googlesitekit-setup-module__title"},Object(r.__)("Select your AdSense account","google-site-kit")),e.createElement(i.d,null),e.createElement("p",null,Object(r.__)("Looks like you have multiple AdSense accounts associated with your Google account. Select the account to use with Site Kit below.","google-site-kit")),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(i.a,null)))}}).call(this,n(4))},,function(e,t,n){"use strict";function a(e){return"string"==typeof e&&/^pub-\d+$/.test(e)}function r(e){return"string"==typeof e&&/^ca-pub-\d+$/.test(e)}n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}))},,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccountCreate}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(15),s=n.n(c),l=n(2),u=n(0),d=n(45),g=n.n(d),m=n(3),f=n(10),p=n(8),b=n(13),v=n(7),h=n(29),y=n(32),O=n(35),_=n(9),E=n(522),k=n(17),S=n(141),j=n(563),T=n(564),A=n(565),N=n(566),C=n(567),w=n(352),I=n(18),M=n(414);function AccountCreate(){var t=Object(u.useState)(!1),n=s()(t,2),a=n[0],i=n[1],c=Object(m.useSelect)((function(e){return e(p.r).getAccountSummaries()})),d=Object(m.useSelect)((function(e){return e(p.r).hasFinishedResolution("getAccountSummaries")})),R=Object(m.useSelect)((function(e){return e(p.r).getAccountTicketTermsOfServiceURL()})),D=Object(m.useSelect)((function(e){return e(p.r).canSubmitAccountCreate()})),x=Object(m.useSelect)((function(e){return e(p.r).isDoingCreateAccount()})),L=Object(m.useSelect)((function(e){return e(v.a).hasScope(p.h)})),P=Object(m.useSelect)((function(e){return e(v.a).hasScope(p.p)})),G=Object(m.useSelect)((function(e){return e(h.a).hasForm(p.m)})),Z=Object(m.useSelect)((function(e){return e(h.a).getValue(p.m,"autoSubmit")})),B=Object(m.useSelect)((function(e){return e(b.c).getReferenceSiteURL()})),U=Object(m.useSelect)((function(e){return e(b.c).getSiteName()})),F=Object(m.useSelect)((function(e){return e(b.c).getTimezone()})),z=Object(I.a)(),V=Object(m.useDispatch)(h.a).setValues,H=Object(m.useDispatch)(y.a).navigateTo,W=Object(m.useDispatch)(p.r).createAccount,q=Object(m.useDispatch)(v.a).setPermissionScopeError,K=Object(m.useDispatch)(b.c),Y=K.setConversionTrackingEnabled,$=K.saveConversionTrackingSettings,X=L;Object(u.useEffect)((function(){R&&o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,g.a.invalidateCache("modules","analytics-4");case 2:H(R);case 3:case"end":return e.stop()}}),e)})))()}),[R,H]),Object(u.useEffect)((function(){G||V(p.m,Object(E.a)({siteName:U,siteURL:B,timezone:F}))}),[G,U,B,F,V]);var J=Object(u.useCallback)(o()(r.a.mark((function e(){var t,n;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=[],L||t.push(p.h),P||t.push(p.p),!(t.length>0)){e.next=7;break}return V(p.m,{autoSubmit:!0}),q({code:O.a,message:Object(l.__)("Additional permissions are required to create a new Analytics account.","google-site-kit"),data:{status:403,scopes:t,skipModal:!0}}),e.abrupt("return");case 7:return V(p.m,{autoSubmit:!1}),e.next=10,Object(_.I)("".concat(z,"_analytics"),"create_account","proxy");case 10:return e.next=12,W();case 12:if(n=e.sent,n.error){e.next=19;break}return Y(!0),e.next=18,$();case 18:i(!0);case 19:case"end":return e.stop()}}),e)}))),[L,P,V,z,W,q,Y,$]);Object(u.useEffect)((function(){X&&Z&&J()}),[X,Z,J]);var Q=Object(m.useDispatch)(p.r).rollbackSettings,ee=Object(u.useCallback)((function(){return Q()}),[Q]);return x||a||!d||void 0===X?e.createElement(f.ProgressBar,null):e.createElement("div",null,e.createElement(S.a,{moduleSlug:"analytics-4",storeName:p.r}),e.createElement("h3",{className:"googlesitekit-heading-4"},Object(l.__)("Create your Analytics account","google-site-kit")),e.createElement("p",null,Object(l.__)("We’ve pre-filled the required information for your new account. Confirm or edit any details:","google-site-kit")),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(k.a,{size:6},e.createElement(T.a,null)),e.createElement(k.a,{size:6},e.createElement(A.a,null)),e.createElement(k.a,{size:6},e.createElement(C.a,null))),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(N.a,null),e.createElement(j.a,null)),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(w.a,{formName:p.m,className:"googlesitekit-margin-bottom-0"}),e.createElement(M.a,{className:"googlesitekit-margin-top-0",message:Object(l.__)("To track how visitors interact with your site, Site Kit will enable enhanced conversion tracking. You can always disable it in settings.","google-site-kit")})),e.createElement("p",null,X&&e.createElement("span",null,Object(l.__)("You will be redirected to Google Analytics to accept the terms of service.","google-site-kit")),!X&&e.createElement("span",null,Object(l.__)("You will need to give Site Kit permission to create an Analytics account on your behalf and also accept the Google Analytics terms of service.","google-site-kit"))),e.createElement("div",{className:"googlesitekit-setup-module__action"},e.createElement(f.Button,{disabled:!D,onClick:J},Object(l.__)("Create Account","google-site-kit")),c&&!!c.length&&e.createElement(f.Button,{tertiary:!0,className:"googlesitekit-setup-module__sub-action",onClick:ee},Object(l.__)("Back","google-site-kit"))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccountSelect}));var a=n(1),r=n.n(a),i=n(0),o=n(2),c=n(10),s=n(3),l=n(8),u=n(9),d=n(18);function AccountSelect(t){var n=t.hasModuleAccess,a=t.onChange,r=Object(d.a)(),g=Object(s.useSelect)((function(e){return e(l.r).getAccountID()})),m=Object(s.useSelect)((function(e){return e(l.r).getAccountSummaries()})),f=Object(s.useSelect)((function(e){return e(l.r).hasFinishedResolution("getAccountSummaries")})),p=Object(s.useDispatch)(l.r).selectAccount,b=Object(i.useCallback)((function(e,t){var n=t.dataset.value;if(g!==n){p(n);var i=n===l.a?"change_account_new":"change_account";Object(u.I)("".concat(r,"_analytics"),i),a&&a()}}),[g,p,r,a]);return f?!1===n?e.createElement(c.Select,{className:"googlesitekit-analytics__select-account",label:Object(o.__)("Account","google-site-kit"),value:g,enhanced:!0,outlined:!0,disabled:!0},e.createElement(c.Option,{value:g},g)):e.createElement(c.Select,{className:"googlesitekit-analytics__select-account",label:Object(o.__)("Account","google-site-kit"),value:g,onEnhancedChange:b,enhanced:!0,outlined:!0},(m||[]).concat({_id:l.a,displayName:Object(o.__)("Set up a new account","google-site-kit")}).map((function(t,n){var a=t._id,r=t.displayName;return e.createElement(c.Option,{key:n,value:a},r)}))):e.createElement(c.ProgressBar,{small:!0})}AccountSelect.propTypes={hasModuleAccess:r.a.bool}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return AccountCreateLegacy}));var r=n(5),i=n.n(r),o=n(16),c=n.n(o),s=n(0),l=n(2),u=n(3),d=n(10),g=n(9),m=n(8),f=n(141),p=n(394),b=n(18);function AccountCreateLegacy(){var t=Object(u.useSelect)((function(e){return e(m.r).getAccountSummaries()})),n=Object(u.useSelect)((function(e){return e(m.r).hasFinishedResolution("getAccountSummaries")})),r=Object(u.useSelect)((function(e){return e(m.r).getAccountID()})),o=m.a===r,v=Object(u.useSelect)((function(e){return e(m.r).getServiceURL({path:"/provision/SignUp"})})),h=Object(b.a)(),y=Object(s.useCallback)(function(){var t=c()(i.a.mark((function t(n){return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n.preventDefault(),t.next=3,Object(g.I)("".concat(h,"_analytics"),"create_account","custom-oauth");case 3:e.open(v,"_blank");case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),[v,h]),O=Object(u.useDispatch)(m.r),_=O.resetAccountSummaries,E=O.resetAccountSettings,k=Object(s.useCallback)((function(){_(),E()}),[E,_]);return n?a.createElement("div",null,a.createElement(p.a,null),a.createElement(f.a,{moduleSlug:"analytics-4",storeName:m.r}),!o&&t&&0===t.length&&a.createElement("p",null,Object(l.__)('Looks like you don’t have an Analytics account yet. Once you create it, click on "Re-fetch my account" and Site Kit will locate it.',"google-site-kit")),o&&a.createElement(s.Fragment,null,a.createElement("p",null,Object(l.__)("To create a new account, click the button below which will open the Google Analytics account creation screen in a new window.","google-site-kit")),a.createElement("p",null,Object(l.__)("Once completed, click the link below to re-fetch your accounts to continue.","google-site-kit"))),a.createElement("div",{className:"googlesitekit-setup-module__action"},a.createElement(d.Button,{onClick:y},Object(l.__)("Create an account","google-site-kit")),a.createElement("div",{className:"googlesitekit-setup-module__sub-action"},a.createElement(d.Button,{tertiary:!0,onClick:k},Object(l.__)("Re-fetch My Account","google-site-kit"))))):a.createElement(d.ProgressBar,null)}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WebDataStreamSelect}));var a=n(11),r=n.n(a),i=n(1),o=n.n(i),c=n(0),s=n(2),l=n(10),u=n(3),d=n(8),g=n(67),m=n(9),f=n(18);function WebDataStreamSelect(t){var n=t.hasModuleAccess,a=t.isDisabled,i=t.className,o=t.onChange,p=Object(u.useSelect)((function(e){return e(d.r).getAccountID()})),b=Object(u.useSelect)((function(e){return e(d.r).getSettings()||{}})),v=b.propertyID,h=b.webDataStreamID,y=b.measurementID,O=Object(u.useSelect)((function(e){return Object(g.e)(v)&&!1!==n?e(d.r).getWebDataStreams(v):[]})),_=Object(u.useSelect)((function(e){return!a&&e(d.r).isLoadingWebDataStreams({hasModuleAccess:n})})),E=Object(f.a)(),k=Object(u.useDispatch)(d.r),S=k.setWebDataStreamID,j=k.updateSettingsForMeasurementID,T=Object(c.useCallback)((function(e,t){var n,a,r=t.dataset.value;h!==r&&(S(r),j((null===(n=O.find((function(e){return e._id===r})))||void 0===n||null===(a=n.webStreamData)||void 0===a?void 0:a.measurementId)||""),Object(m.I)("".concat(E,"_analytics"),r===d.z?"change_webdatastream_new":"change_webdatastream","ga4"),o&&o())}),[O,h,S,j,E,o]);if(!Object(g.a)(p))return null;if(_)return e.createElement(l.ProgressBar,{smallHeight:80,desktopHeight:88,small:!0});var A=void 0===h||""===h||Object(g.i)(h);return!1===n?e.createElement(l.Select,{className:r()("googlesitekit-analytics-4__select-webdatastream",i),label:Object(s.__)("Web Data Stream","google-site-kit"),value:y,enhanced:!0,outlined:!0,disabled:!0},e.createElement(l.Option,{value:y},y)):e.createElement(l.Select,{className:r()("googlesitekit-analytics-4__select-webdatastream",i,{"mdc-select--invalid":!A}),label:Object(s.__)("Web Data Stream","google-site-kit"),value:h,onEnhancedChange:T,disabled:a||!Object(g.f)(v),enhanced:!0,outlined:!0},(O||[]).concat({_id:d.z,displayName:Object(s.__)("Set up a new web data stream","google-site-kit")}).map((function(t,n){var a=t._id,r=t.displayName,i=t.webStreamData,o=void 0===i?{}:i;return e.createElement(l.Option,{key:n,value:a},a!==d.z&&(null==o?void 0:o.measurementId)?Object(s.sprintf)(
/* translators: 1: Data stream name. 2: Measurement ID. */
Object(s._x)("%1$s (%2$s)","Analytics data stream name and measurement ID","google-site-kit"),r,o.measurementId):r)})))}WebDataStreamSelect.propTypes={hasModuleAccess:o.a.bool,isDisabled:o.a.bool,className:o.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PropertySelect}));var a=n(11),r=n.n(a),i=n(1),o=n.n(i),c=n(0),s=n(2),l=n(10),u=n(3),d=n(8),g=n(67),m=n(9),f=n(18);function PropertySelect(t){var n=t.isDisabled,a=t.hasModuleAccess,i=t.className,o=t.onChange,p=void 0===o?function(){}:o,b=Object(u.useSelect)((function(e){return e(d.r).getAccountID()})),v=Object(u.useSelect)((function(e){return!1===a||n?null:e(d.r).getPropertySummaries(b)||[]})),h=Object(u.useSelect)((function(e){return e(d.r).getPropertyID()})),y=Object(u.useSelect)((function(e){return!n&&(e(d.r).isLoadingPropertySummaries()||e(d.r).isLoadingWebDataStreams({hasModuleAccess:a}))})),O=Object(f.a)(),_=Object(u.useDispatch)(d.r).selectProperty,E=Object(c.useCallback)((function(e,t){var n=t.dataset.value;h!==n&&(_(n),Object(m.I)("".concat(O,"_analytics"),n===d.s?"change_property_new":"change_property","ga4"),p())}),[p,h,_,O]);if(!Object(g.a)(b))return null;if(y)return e.createElement(l.ProgressBar,{smallHeight:80,desktopHeight:88,small:!0});var k=void 0===h||""===h||Object(g.f)(h);return!1===a?e.createElement(l.Select,{className:r()("googlesitekit-analytics-4__select-property",i),label:Object(s.__)("Property","google-site-kit"),value:h,enhanced:!0,outlined:!0,disabled:!0},e.createElement(l.Option,{value:h},h)):e.createElement(l.Select,{className:r()("googlesitekit-analytics-4__select-property",i,{"mdc-select--invalid":!k,"googlesitekit-analytics-4__select-property--loaded":!n&&!y}),label:Object(s.__)("Property","google-site-kit"),value:h,onEnhancedChange:E,disabled:n,enhanced:!0,outlined:!0},(v||[]).concat({_id:d.s,displayName:Object(s.__)("Set up a new property","google-site-kit")}).map((function(t){var n=t._id,a=t.displayName;return e.createElement(l.Option,{key:n,value:n},n===d.s?a:Object(s.sprintf)(
/* translators: 1: Property name. 2: Property ID. */
Object(s._x)("%1$s (%2$s)","Analytics property name and ID","google-site-kit"),a,n))})))}PropertySelect.propTypes={isDisabled:o.a.bool,hasModuleAccess:o.a.bool,className:o.a.string,onChange:o.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WebDataStreamNameInput}));var a=n(11),r=n.n(a),i=n(81),o=n(0),c=n(2),s=n(106),l=n(3),u=n(29),d=n(13),g=n(8),m=n(506),f=n(10),p=n(67);function WebDataStreamNameInput(){var t=Object(l.useSelect)((function(e){return e(g.r).getPropertyID()})),n=Object(l.useSelect)((function(e){return e(g.r).getWebDataStreamID()})),a=Object(l.useSelect)((function(e){return e(u.a).getValue(g.o,"webDataStreamName")})),b=Object(l.useSelect)((function(e){return!!Object(p.e)(t)&&e(g.r).doesWebDataStreamExist(t,a)})),v=Object(l.useSelect)((function(e){return e(d.c).getReferenceSiteURL()})),h=Object(l.useDispatch)(u.a).setValues,y=Object(o.useCallback)((function(e){var t=e.currentTarget;h(g.o,{webDataStreamName:t.value})}),[h]);if(Object(i.a)((function(){if(!a&&Object(s.a)(v)){var e=new URL(v).hostname;h(g.o,{webDataStreamName:e})}})),n!==g.z)return null;var O=b||!a||!Object(p.h)(a),_=!1;return b?_=Object(c.__)("A web data stream with this name already exists.","google-site-kit"):a?Object(p.h)(a)||(_=Object(c.__)("This is not a valid web data stream name.","google-site-kit")):_=Object(c.__)("A web data stream name is required.","google-site-kit"),e.createElement("div",{className:"googlesitekit-analytics-webdatastreamname"},e.createElement(f.TextField,{className:r()({"mdc-text-field--error":O}),label:Object(c.__)("Web Data Stream Name","google-site-kit"),outlined:!0,helperText:_,trailingIcon:O&&e.createElement("span",{className:"googlesitekit-text-field-icon--error"},e.createElement(m.a,null)),value:a,onChange:y}))}}).call(this,n(4))},,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleSettingsWarning}));var a=n(3),r=n(19),i=n(7),o=n(217),c=n(245);function ModuleSettingsWarning(t){var n=t.slug,s=Object(a.useSelect)((function(e){var t;return null===(t=e(r.a))||void 0===t?void 0:t.getCheckRequirementsError(n)}));return s?i.c===s.code?e.createElement(c.a,{moduleSlug:n}):e.createElement(o.a,null,s.message):null}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TimezoneSelect}));var a=n(0),r=n(2),i=n(10),o=n(3),c=n(215),s=n(8),l=n(29);function TimezoneSelect(){var t=Object(o.useSelect)((function(e){return e(l.a).getValue(s.m,"countryCode")})),n=Object(o.useSelect)((function(e){return e(l.a).getValue(s.m,"timezone")})),u=Object(o.useDispatch)(l.a).setValues,d=Object(a.useCallback)((function(e,t){u(s.m,{timezone:t.dataset.value})}),[u]);return e.createElement(i.Select,{className:"googlesitekit-analytics__select-timezone",label:Object(r.__)("Timezone","google-site-kit"),value:n,onEnhancedChange:d,disabled:!t,enhanced:!0,outlined:!0},(c.d[t]||[]).map((function(t,n){var a=t.timeZoneId,r=t.displayName;return e.createElement(i.Option,{key:n,value:a},r)})))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccountField}));var a=n(0),r=n(2),i=n(331),o=n(3),c=n(8),s=n(29);function AccountField(){var t=Object(o.useSelect)((function(e){return e(s.a).getValue(c.m,"accountName")})),n=Object(o.useDispatch)(s.a).setValues,l=Object(a.useCallback)((function(e){n(c.m,{accountName:e})}),[n]);return e.createElement(i.a,{label:Object(r.__)("Account","google-site-kit"),hasError:!t,value:t,setValue:l,name:"account"})}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PropertyField}));var a=n(0),r=n(2),i=n(331),o=n(3),c=n(8),s=n(29);function PropertyField(){var t=Object(o.useSelect)((function(e){return e(s.a).getValue(c.m,"propertyName")})),n=Object(o.useDispatch)(s.a).setValues,l=Object(a.useCallback)((function(e){n(c.m,{propertyName:e})}),[n]);return e.createElement(i.a,{label:Object(r.__)("Property","google-site-kit"),value:t,hasError:!t,setValue:l,name:"property"})}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CountrySelect}));var a=n(0),r=n(2),i=n(10),o=n(3),c=n(215),s=n(8),l=n(29);function CountrySelect(){var t=Object(o.useSelect)((function(e){return e(l.a).getValue(s.m,"countryCode")})),n=Object(o.useDispatch)(l.a).setValues,u=Object(a.useCallback)((function(e,a){var r=a.dataset.value;r!==t&&c.b[r]&&n(s.m,{countryCode:r,timezone:c.b[r].defaultTimeZoneId})}),[n,t]);return e.createElement(i.Select,{className:"googlesitekit-analytics__select-country",label:Object(r.__)("Country","google-site-kit"),value:t,onEnhancedChange:u,enhanced:!0,outlined:!0},c.a.map((function(t,n){var a=t.countryCode,r=t.displayName;return e.createElement(i.Option,{key:n,value:a},r)})))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WebDataStreamField}));var a=n(0),r=n(2),i=n(331),o=n(3),c=n(8),s=n(29);function WebDataStreamField(){var t=Object(o.useSelect)((function(e){return e(s.a).getValue(c.m,"dataStreamName")})),n=Object(o.useDispatch)(s.a).setValues,l=Object(a.useCallback)((function(e){n(c.m,{dataStreamName:e})}),[n]);return e.createElement(i.a,{label:Object(r.__)("Web Data Stream","google-site-kit"),value:t,hasError:!t,setValue:l,name:"dataStream"})}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var a=n(6),r=n.n(a),i=n(5),o=n.n(i),c=n(16),s=n.n(c),l=n(12),u=n.n(l),d=n(3),g=n(13),m=n(369);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function b(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return v(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return v(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,r=function(){};return{s:r,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.storeName,n=e.isValidTag,a=e.tagMatchers;u()("string"==typeof t&&t,"storeName is required."),u()("function"==typeof n,"isValidTag must be a function."),u()(Array.isArray(a),"tagMatchers must be an Array.");var i={existingTag:void 0},c={fetchGetExistingTag:function(){return{payload:{},type:"FETCH_GET_EXISTING_TAG"}},receiveGetExistingTag:function(e){return u()(null===e||"string"==typeof e,"existingTag must be a tag string or null."),{payload:{existingTag:n(e)?e:null},type:"RECEIVE_GET_EXISTING_TAG"}}},l=r()({},"FETCH_GET_EXISTING_TAG",Object(d.createRegistryControl)((function(e){return s()(o.a.mark((function t(){var n,r,i,c,s,l,u,d,f,p;return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.select(g.c).getHomeURL(),r=e.select(g.c).getAMPMode(),t.next=4,Object(m.b)({homeURL:n,ampMode:r});case 4:i=t.sent,c=e.resolveSelect(g.c),s=c.getHTMLForURL,l=b(i),t.prev=7,l.s();case 9:if((u=l.n()).done){t.next=19;break}return d=u.value,t.next=13,s(d);case 13:if(f=t.sent,!(p=Object(m.a)(f,a))){t.next=17;break}return t.abrupt("return",p);case 17:t.next=9;break;case 19:t.next=24;break;case 21:t.prev=21,t.t0=t.catch(7),l.e(t.t0);case 24:return t.prev=24,l.f(),t.finish(24);case 27:return t.abrupt("return",null);case 28:case"end":return t.stop()}}),t,null,[[7,21,24,27]])})))}))),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i,t=arguments.length>1?arguments[1]:void 0,n=t.type,a=t.payload;switch(n){case"RECEIVE_GET_EXISTING_TAG":var r=a.existingTag;return p(p({},e),{},{existingTag:r});default:return e}},v={getExistingTag:o.a.mark((function e(){var n,a;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,d.commonActions.getRegistry();case 2:if(void 0!==(n=e.sent).select(t).getExistingTag()){e.next=8;break}return e.next=6,c.fetchGetExistingTag();case 6:a=e.sent,n.dispatch(t).receiveGetExistingTag(a);case 8:case"end":return e.stop()}}),e)}))},h={getExistingTag:function(e){return e.existingTag},hasExistingTag:Object(d.createRegistrySelector)((function(e){return function(){var n=e(t).getExistingTag();if(void 0!==n)return!!n}}))},y={initialState:i,actions:c,controls:l,reducer:f,resolvers:v,selectors:h};return p(p({},y),{},{STORE_NAME:t})}},,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){(function(e){Object.prototype.hasOwnProperty.call(e,"google")||(e.google={})}).call(this,n(28))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WebStoriesAdUnitSelect}));var a=n(0),r=n(2),i=n(10),o=n(3),c=n(31);function WebStoriesAdUnitSelect(){var t=Object(o.useSelect)((function(e){return e(c.l).getAccountID()})),n=Object(o.useSelect)((function(e){return e(c.l).getClientID()})),s=Object(o.useSelect)((function(e){return e(c.l).getWebStoriesAdUnit()})),l=Object(o.useSelect)((function(e){return e(c.l).getAdUnits(t,n)})),u=Object(o.useSelect)((function(e){return e(c.l).hasFinishedResolution("getAdUnits",[t,n])})),d=Object(o.useDispatch)(c.l).setWebStoriesAdUnit,g=Object(a.useCallback)((function(e,t){var n=t.dataset.value;s!==n&&d(n)}),[s,d]);return u?e.createElement(i.Select,{className:"googlesitekit-adsense__select-field",label:Object(r.__)("Web Stories Ad Unit","google-site-kit"),value:s,onEnhancedChange:g,enhanced:!0,outlined:!0},e.createElement(i.Option,{value:""},Object(r.__)("Select ad unit","google-site-kit")),(l||[]).map((function(t){var n=t._id,a=t.displayName;return e.createElement(i.Option,{key:n,value:n},a)}))):e.createElement(i.ProgressBar,{small:!0})}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdSenseConnectCTA}));var a=n(21),r=n.n(a),i=n(5),o=n.n(i),c=n(16),s=n.n(c),l=n(15),u=n.n(l),d=n(240),g=n(1),m=n.n(g),f=n(2),p=n(0),b=n(38),v=n(3),h=n(10),y=n(31),O=n(17),_=n(13),E=n(19),k=n(32),S=n(37),j=n(9),T=n(668),A=n(199),N=n(18);function AdSenseConnectCTA(t){var n=t.onDismissModule,a=Object(v.useDispatch)(k.a).navigateTo,i=Object(v.useDispatch)(E.a).activateModule,c=Object(v.useDispatch)(_.c).setInternalServerError,l=Object(N.a)(),g=Object(p.useRef)(),m=Object(p.useState)(!1),C=u()(m,2),w=C[0],I=C[1],M=Object(d.a)(g,{threshold:.25}),R=!!(null==M?void 0:M.intersectionRatio);Object(p.useEffect)((function(){R&&!w&&(Object(j.I)("".concat(l,"_adsense-cta-widget"),"widget_view"),I(!0))}),[R,l,w]);var D=Object(v.useSelect)((function(e){return e(y.l).getAdminReauthURL()})),x=Object(v.useSelect)((function(e){return e(E.a).isModuleActive("adsense")})),L=Object(v.useSelect)((function(e){return e(E.a).isModuleConnected("adsense")})),P=Object(v.useSelect)((function(e){return!!e(E.a).isFetchingSetModuleActivation("adsense",!0)||!!D&&e(k.a).isNavigatingTo(D)})),G=Object(p.useCallback)(s()(o.a.mark((function e(){var t,n,r;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,i("adsense");case 2:if(t=e.sent,n=t.response,!(r=t.error)){e.next=8;break}return c({id:"setup-module-error",description:r.message}),e.abrupt("return",null);case 8:return e.next=10,Object(j.I)("".concat(l,"_adsense-cta-widget"),"activate_module","adsense");case 10:return e.next=12,Object(S.f)("module_setup","adsense",{ttl:300});case 12:a(n.moduleReauthURL);case 13:case"end":return e.stop()}}),e)}))),[i,a,c,l]),Z=Object(p.useCallback)((function(){return a(D)}),[D,a]),B=Object(p.useCallback)((function(){Object(j.I)("".concat(l,"_adsense-cta-widget"),"dismiss_widget"),n()}),[n,l]),U={smSize:4,mdSize:4,lgSize:6};return e.createElement("section",{ref:g,className:"googlesitekit-setup__wrapper googlesitekit-setup__wrapper--adsense-connect"},e.createElement(O.e,null,e.createElement(T.a,{hasBeenInView:w}),e.createElement(O.k,null,e.createElement(O.a,U,e.createElement("div",{className:"googlesitekit-setup-module__action"},!x&&e.createElement(h.SpinnerButton,{onClick:G,isSaving:P},Object(f.__)("Connect now","google-site-kit")),x&&!L&&e.createElement(h.SpinnerButton,{onClick:Z,isSaving:P},Object(f.__)("Complete setup","google-site-kit")),e.createElement(h.Button,{tertiary:!0,onClick:B},Object(f.__)("Maybe later","google-site-kit")))),e.createElement(O.a,r()({},U,{className:"googlesitekit-setup-module__footer-text"}),e.createElement("p",null,Object(b.a)(Object(f.__)("AdSense accounts are <a>subject to review and approval</a> by the Google AdSense team","google-site-kit"),{a:e.createElement(A.a,{path:"/adsense/answer/9724",external:!0,hideExternalIndicator:!0})}))))))}AdSenseConnectCTA.propTypes={onDismissModule:m.a.func.isRequired}}).call(this,n(4))},,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Step}));var a=n(1),r=n.n(a),i=n(307);function Step(t){var n=t.children,a=t.title,r=t.stepStatus;return e.createElement("div",{className:"googlesitekit-stepper__step-info"},e.createElement("h2",{className:"googlesitekit-stepper__step-title"},a),e.createElement("div",{className:"googlesitekit-stepper__step-content-container"},r===i.a.ACTIVE&&e.createElement("div",{className:"googlesitekit-stepper__step-content"},n)))}Step.propTypes={children:r.a.node.isRequired,title:r.a.string.isRequired,stepStatus:r.a.oneOf(Object.values(i.a))}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var a=n(1037),r=function(e){var t=new URL(e);return Object(a.parse)(t.hostname).domain}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GhostCardGreenSVG}));var a=n(0),r=n(2),i=n(44),o=n(151),c=Object(a.lazy)((function(){return n.e(36).then(n.bind(null,1220))}));function GhostCardGreenSVG(){return e.createElement(a.Suspense,{fallback:e.createElement(i.a,{width:"100%",height:"90px"})},e.createElement(o.a,{errorMessage:Object(r.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},,,,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i}));var a="adsense-connect-cta",r="adsense-connect-cta-tooltip-state",i="adsense-ga4-top-earnings-notice"},,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdBlockingRecoverySetupCTANotice}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(0),s=n(38),l=n(2),u=n(10),d=n(3),g=n(77),m=n(166),f=n(199),p=n(32),b=n(13),v=n(411),h=n(18),y=n(9),O=n(31),_=n(259),E=n(251);function AdBlockingRecoverySetupCTANotice(){var t=Object(v.a)(),n=Object(h.a)(),a=Object(d.useSelect)((function(e){return e(O.l).getAdBlockingRecoverySetupStatus()})),i=Object(d.useSelect)((function(e){return e(O.l).getAccountStatus()})),k=Object(d.useSelect)((function(e){return e(O.l).getSiteStatus()})),S=Object(d.useSelect)((function(e){return e(O.l).hasExistingAdBlockingRecoveryTag()})),j=Object(d.useSelect)((function(e){return e(b.c).getAdminURL("googlesitekit-ad-blocking-recovery")})),T=Object(d.useDispatch)(p.a).navigateTo,A=void 0===S||S||""!==a||i!==_.a||k!==_.c;Object(c.useEffect)((function(){t&&!A&&Object(y.I)("".concat(n,"_adsense-abr-cta-widget"),"view_notification")}),[t,A,n]);var N=function(){var e=o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(y.I)("".concat(n,"_adsense-abr-cta-widget"),"confirm_notification");case 2:return e.abrupt("return",T(j));case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return A?null:e.createElement(m.a,{notice:e.createElement(c.Fragment,null,Object(l.__)("Ad blocking recovery","google-site-kit"),e.createElement(g.a,{className:"googlesitekit-new-badge",label:Object(l.__)("New","google-site-kit")})),className:"googlesitekit-settings-notice-ad-blocking-recovery-cta",OuterCTA:function OuterCTA(){return e.createElement(u.Button,{onClick:N},Object(l.__)("Set up now","google-site-kit"))}},Object(s.a)(Object(l.__)("Start recovering revenue lost from ad blockers by deploying an ad blocking recovery message through Site Kit. <a>Learn more</a>","google-site-kit"),{a:e.createElement(f.a,{path:"/adsense/answer/********",external:!0,onClick:function(){Object(y.I)("".concat(n,"_adsense-abr-cta-widget"),"click_learn_more_link")}})}),e.createElement(E.a,{triggerID:"view_abr_setup_cta",ttl:y.a}))}}).call(this,n(4))},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdBlockingRecoveryApp}));var a=n(0),r=n(234),i=n(235),o=n(17),c=n(662);function AdBlockingRecoveryApp(){return e.createElement(a.Fragment,null,e.createElement(r.a,null,e.createElement(i.a,null)),e.createElement("div",{className:"googlesitekit-ad-blocking-recovery googlesitekit-module-page"},e.createElement(o.e,null,e.createElement(o.k,null,e.createElement(o.a,{size:12},e.createElement(c.a,null))))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccountSelect}));var a=n(0),r=n(2),i=n(10),o=n(3),c=n(9),s=n(31),l=n(18);function AccountSelect(){var t=Object(l.a)(),n="".concat(t,"_adsense"),u=Object(o.useSelect)((function(e){return e(s.l).getAccountID()})),d=Object(o.useSelect)((function(e){return e(s.l).getAccounts()})),g=Object(o.useSelect)((function(e){return e(s.l).hasFinishedResolution("getAccounts")})),m=Object(o.useDispatch)(s.l).setAccountID,f=Object(a.useCallback)((function(e,t){var a=t.dataset.value;u!==a&&(m(a),Object(c.I)(n,"change_account"))}),[u,n,m]);return g?e.createElement(i.Select,{className:"googlesitekit-adsense__select-account",label:Object(r.__)("Account","google-site-kit"),value:u,onEnhancedChange:f,enhanced:!0,outlined:!0},(d||[]).map((function(t,n){var a=t._id,r=t.displayName;return e.createElement(i.Option,{key:n,value:a},r)}))):e.createElement(i.ProgressBar,{small:!0})}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdSenseLinkCTA}));var a=n(2),r=n(3),i=n(95),o=n(13);function AdSenseLinkCTA(t){var n=t.onClick,c=void 0===n?function(){}:n,s=Object(r.useSelect)((function(e){return e(o.c).getGoogleSupportURL({path:"/adsense/answer/6084409"})}));return e.createElement(i.a,{title:Object(a.__)("Link Analytics and AdSense","google-site-kit"),description:Object(a.__)("Get reports for your top earning pages by linking your Analytics and AdSense accounts","google-site-kit"),ctaLink:s,ctaLabel:Object(a.__)("Learn more","google-site-kit"),ctaLinkExternal:!0,onClick:c})}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotices}));var a=n(31),r=n(141),i=n(1),o=n.n(i);function ErrorNotices(t){var n=t.hasButton,i=void 0!==n&&n;return e.createElement(r.a,{hasButton:i,moduleSlug:"adsense",storeName:a.l})}ErrorNotices.propTypes={hasButton:o.a.bool}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UserProfile}));var a=n(3),r=n(10),i=n(7);function UserProfile(){var t=Object(a.useSelect)((function(e){return e(i.a).getEmail()})),n=Object(a.useSelect)((function(e){return e(i.a).getPicture()}));return Object(a.useSelect)((function(e){return e(i.a).hasFinishedResolution("getUser")}))?e.createElement("p",{className:"googlesitekit-setup-module__user"},e.createElement("img",{className:"googlesitekit-setup-module__user-image",src:n,alt:""}),e.createElement("span",{className:"googlesitekit-setup-module__user-email"},t)):e.createElement(r.ProgressBar,{small:!0})}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UseSnippetSwitch}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(1),s=n.n(c),l=n(535),u=n(0),d=n(2),g=n(3),m=n(10),f=n(142),p=n(9),b=n(31),v=n(18),h=n(77);function UseSnippetSwitch(t){var n=t.label,a=void 0===n?Object(d.__)("Let Site Kit place AdSense code on your site","google-site-kit"):n,i=t.checkedMessage,c=t.uncheckedMessage,s=t.saveOnChange,y=Object(v.a)(),O="".concat(y,"_adsense"),_=Object(g.useSelect)((function(e){return e(b.l).getUseSnippet()})),E=Object(g.useSelect)((function(e){return e(b.l).isDoingSubmitChanges()})),k=Object(g.useDispatch)(b.l),S=k.setUseSnippet,j=k.saveSettings,T=Object(u.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(S(!_),!s){e.next=4;break}return e.next=4,j();case 4:case"end":return e.stop()}}),e)}))),[_,s,S,j]);return Object(l.a)((function(){Object(p.I)(O,_?"enable_tag":"disable_tag")}),[O,_]),void 0===_?null:e.createElement(u.Fragment,null,e.createElement("div",{className:"googlesitekit-setup-module__switch"},e.createElement(m.Switch,{label:a,onClick:T,checked:_,disabled:E,hideLabel:!1})," ",e.createElement(h.a,{className:"googlesitekit-badge--primary",label:Object(d.__)("Recommended","google-site-kit")})),_&&i&&e.createElement(f.c,{notice:i}),!_&&c&&e.createElement(f.c,{notice:c}))}UseSnippetSwitch.propTypes={label:s.a.string,checkedMessage:s.a.string,uncheckedMessage:s.a.string,saveOnChange:s.a.bool},UseSnippetSwitch.defaultProps={saveOnChange:!1}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupMain}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(15),s=n.n(c),l=n(535),u=n(1),d=n.n(u),g=n(2),m=n(0),f=n(3),p=n(10),b=n(512),v=n(537),h=n(542),y=n(543),O=n(9),_=n(162),E=n(245),k=n(31),S=n(7),j=n(13),T=n(140),A=n(18),N=n(353),C=n(23);function SetupMain(t){var n=t.finishSetup,a=Object(A.a)(),i="".concat(a,"_adsense"),c=Object(f.useDispatch)(k.l),u=c.resetAccounts,d=c.resetClients,w=c.resetSites,I=c.setAccountID,M=c.setAccountStatus,R=c.submitChanges,D=Object(m.useState)(!1),x=s()(D,2),L=x[0],P=x[1],G=Object(m.useState)(!1),Z=s()(G,2),B=Z[0],U=Z[1],F=Object(f.useSelect)((function(e){return!!e(C.b).getValue(k.h)})),z=Object(f.useSelect)((function(e){return e(S.a).isAdBlockerActive()})),V=Object(f.useSelect)((function(e){return e(k.l).getAccounts()})),H=Object(f.useSelect)((function(e){return e(k.l).getAccountID()})),W=Object(f.useSelect)((function(e){return e(k.l).hasSettingChanged("accountID")})),q=Object(f.useSelect)((function(e){return e(k.l).hasSettingChanged("clientID")})),K=Object(f.useSelect)((function(e){return e(k.l).canSubmitChanges()})),Y=Object(f.useSelect)((function(e){return e(k.l).getClientID()})),$=Object(f.useSelect)((function(e){return e(k.l).getAccountStatus()})),X=Object(f.useSelect)((function(e){return e(k.l).hasSettingChanged("accountStatus")})),J=Object(f.useSelect)((function(e){return e(k.l).getSiteStatus()})),Q=Object(f.useSelect)((function(e){return e(k.l).hasSettingChanged("siteStatus")})),ee=Object(f.useSelect)((function(e){return e(k.l).hasErrors()})),te=Object(f.useSelect)((function(e){return e(k.l).hasFinishedResolution("getAccounts")})),ne=Object(f.useSelect)((function(e){return e(S.a).getEmail()})),ae=Object(f.useSelect)((function(e){return e(j.c).getReferenceSiteURL()})),re=Object(f.useSelect)((function(e){return e(k.l).getExistingTag()})),ie=null==V?void 0:V.find((function(e){return e._id===H}));Object(l.a)((function(){(W&&void 0!==H||q&&void 0!==Y||X&&void 0!==$||Q&&void 0!==J)&&P(!0)}),[H,W,Y,q,$,X,J,Q]),Object(m.useEffect)((function(){var e;Array.isArray(V)&&(1!==V.length||H&&V[0]._id===H?0===V.length&&H&&(e=""):e=V[0]._id,void 0!==e&&(I(e),P(!0)))}),[V,H,I]),Object(m.useEffect)((function(){0===(null==V?void 0:V.length)?M(T.h):(null==V?void 0:V.length)>1&&!H&&M(T.f)}),[M,H,V]),Object(m.useEffect)((function(){L&&!B&&K&&!F&&(P(!1),o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return U(!0),e.next=3,R();case 3:U(!1);case 4:case"end":return e.stop()}}),e)})))())}),[L,B,K,R,F]);var oe,ce=Object(m.useCallback)((function(){void 0!==$&&T.k!==$&&(u(),d(),w())}),[$,u,d,w]);return Object(N.a)(ce,15e3),Object(m.useEffect)((function(){void 0!==$&&Object(O.I)(i,"receive_account_state",$)}),[i,$]),Object(m.useEffect)((function(){void 0!==J&&Object(O.I)(i,"receive_site_state",J)}),[i,J]),oe=te&&void 0!==H&&void 0!==ne&&void 0!==ae&&void 0!==re?ee?e.createElement(_.d,{hasButton:!0}):(null==V?void 0:V.length)?H?e.createElement(v.a,{account:ie,finishSetup:n}):e.createElement(y.a,null):e.createElement(h.a,null):e.createElement(p.ProgressBar,null),e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--adsense"},e.createElement("div",{className:"googlesitekit-setup-module__step"},e.createElement("div",{className:"googlesitekit-setup-module__logo"},e.createElement(b.a,{width:"40",height:"40"})),e.createElement("h2",{className:"googlesitekit-heading-3 googlesitekit-setup-module__title"},Object(g._x)("AdSense","Service name","google-site-kit"))),e.createElement("div",{className:"googlesitekit-setup-module__step"},e.createElement(E.a,{moduleSlug:"adsense"}),!z&&oe))}SetupMain.propTypes={finishSetup:d.a.func}}).call(this,n(4))},,,,,,,,,,function(e,t,n){"use strict";var a=n(645);n.d(t,"a",(function(){return a.a}));n(410);var r=n(651);n.d(t,"b",(function(){return r.a}));n(537),n(542),n(543),n(539),n(540),n(541),n(538)},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupMain}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(15),s=n.n(c),l=n(6),u=n.n(l),d=n(157),g=n(0),m=n(2),f=n(3),p=n(10),b=n(20),v=n(365),h=n(663),y=n(600),O=n(192),_=n(32),E=n(13),k=n(23),S=n(18),j=n(17),T=n(9),A=n(31),N=n(664),C=n(666),w=n(667);function SetupMain(){var t=Object(S.a)(),n=Object(f.useSelect)((function(e){return e(E.c).getAdminURL("googlesitekit-settings")})),a="".concat(n,"#/connected-services/adsense"),i=Object(f.useSelect)((function(e){return!!e(k.b).getValue(A.c)})),c=Object(f.useSelect)((function(e){return e(E.c).getAdminURL("googlesitekit-dashboard")})),l=Object(f.useSelect)((function(e){var t,n=(t={},u()(t,A.j.TAG_PLACED,A.k.CREATE_MESSAGE),u()(t,A.j.SETUP_CONFIRMED,A.k.COMPLETE),t),a=e(A.l).getAdBlockingRecoverySetupStatus();if(void 0!==a)return n[a]||A.k.PLACE_TAGS})),I=Object(d.a)(c,{notification:"ad_blocking_recovery_setup_success"}),M=Object(f.useSelect)((function(e){return e(A.l).getAccountID()})),R=Object(f.useSelect)((function(e){return e(A.l).getServiceURL({path:"/".concat(M,"/privacymessaging/ad_blocking")})})),D=Object(f.useDispatch)(A.l),x=D.saveSettings,L=D.setAdBlockingRecoverySetupStatus,P=D.setUseAdBlockingRecoverySnippet,G=D.setUseAdBlockingRecoveryErrorSnippet,Z=Object(f.useDispatch)(_.a).navigateTo,B=Object(g.useState)(l),U=s()(B,2),F=U[0],z=U[1],V=Object(g.useCallback)(o()(r.a.mark((function e(){var o,s;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==F){e.next=6;break}return e.next=3,Object(T.I)("".concat(t,"_adsense-abr"),"cancel_setup","on_place_tag_step");case 3:if(!document.referrer.includes(n)){e.next=5;break}return e.abrupt("return",Z(a));case 5:return e.abrupt("return",Z(c));case 6:if(!i){e.next=10;break}return e.next=9,Object(T.I)("".concat(t,"_adsense-abr"),"cancel_setup","on_final_step");case 9:return e.abrupt("return",Z(a));case 10:return L(""),P(!1),G(!1),e.next=15,x();case 15:return o=e.sent,s=o.error,e.next=19,Object(T.I)("".concat(t,"_adsense-abr"),"cancel_setup","on_create_message_step");case 19:s||(document.referrer.includes(n)?Z(a):Z(c));case 20:case"end":return e.stop()}}),e)}))),[F,a,i,c,Z,x,L,G,P,n,t]);return Object(g.useEffect)((function(){void 0===F&&void 0!==l&&z(l)}),[F,l]),e.createElement(O.a,{rounded:!0},e.createElement(j.e,null,e.createElement(j.k,null,e.createElement(j.a,{lgSize:6,mdSize:8,smSize:4},e.createElement(v.a,{className:"googlesitekit-heading-3 googlesitekit-ad-blocking-recovery__heading",title:Object(m.__)("Ad Blocking Recovery","google-site-kit"),fullWidth:!0})))),e.createElement(N.a,null,e.createElement(h.a,{activeStep:F,className:"googlesitekit-ad-blocking-recovery__steps"},e.createElement(y.a,{title:Object(m.__)("Enable ad blocking recovery message (required)","google-site-kit"),className:"googlesitekit-ad-blocking-recovery__step googlesitekit-ad-blocking-recovery__step-place-tags"},e.createElement(w.a,{setActiveStep:z})),e.createElement(y.a,{title:Object(m.__)("Create your site’s ad blocking recovery message (required)","google-site-kit"),className:"googlesitekit-ad-blocking-recovery__step googlesitekit-ad-blocking-recovery__step-create-message"},e.createElement(C.a,null))),A.k.COMPLETE===F&&e.createElement("div",{className:"googlesitekit-ad-blocking-recovery__complete-content"},e.createElement("p",null,Object(m.__)("Create and publish an ad blocking recovery message in AdSense","google-site-kit")),e.createElement("p",null,Object(m.__)("Site visitors will be given the option to allow ads on your site. You can also present them with other options to fund your site (optional)","google-site-kit")))),e.createElement("div",{className:"googlesitekit-ad-blocking-recovery__footer googlesitekit-ad-blocking-recovery__buttons"},e.createElement("div",{className:"googlesitekit-ad-blocking-recovery__footer-cancel"},A.k.COMPLETE===F?e.createElement("div",{className:"googlesitekit-ad-blocking-recovery__complete-actions"},e.createElement(p.SpinnerButton,{href:I},Object(m.__)("My message is ready","google-site-kit")),e.createElement(b.a,{href:R,external:!0,hideExternalIndicator:!0},Object(m.__)("Create message","google-site-kit"))):e.createElement(b.a,{onClick:V},Object(m.__)("Cancel","google-site-kit")))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Stepper}));var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(0),s=n(2),l=n(372),u=n(307);function Stepper(t){var n=t.children,a=t.activeStep,r=t.className,i=c.Children.count(n);function d(e,t){switch(t){case u.a.UPCOMING:return Object(s.sprintf)(
/* translators: 1: The number of the current step. 2: The total number of steps. */
Object(s.__)("Step %1$s of %2$s (upcoming).","google-site-kit"),e,i);case u.a.ACTIVE:return Object(s.sprintf)(
/* translators: 1: The number of the current step. 2: The total number of steps. */
Object(s.__)("Step %1$s of %2$s (active).","google-site-kit"),e,i);case u.a.COMPLETED:return Object(s.sprintf)(
/* translators: 1: The number of the current step. 2: The total number of steps. */
Object(s.__)("Step %1$s of %2$s (completed).","google-site-kit"),e,i)}}return e.createElement("ol",{className:o()("googlesitekit-stepper",r)},c.Children.map(n,(function(t,n){var r=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1;return e<a?u.a.COMPLETED:e===a?u.a.ACTIVE:u.a.UPCOMING}(n,a),s=n+1;return e.createElement("li",{className:o()("googlesitekit-stepper__step","googlesitekit-stepper__step--".concat(r),t.props.className)},e.createElement("div",{className:"googlesitekit-stepper__step-progress"},e.createElement("span",{className:"googlesitekit-stepper__step-number",title:d(s,r)},r===u.a.COMPLETED?e.createElement(l.a,null):s),s<i&&e.createElement("div",{className:"googlesitekit-stepper__step-progress-line"})),Object(c.cloneElement)(t,{stepStatus:r}))})))}Stepper.propTypes={children:r.a.node.isRequired,activeStep:r.a.number,className:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Content}));var a=n(1),r=n.n(a),i=n(17),o=n(665),c=n(24);function Content(t){var n=t.children,a=Object(c.e)(),r=![c.c,c.b].includes(a);return e.createElement(i.e,{className:"googlesitekit-ad-blocking-recovery__content"},e.createElement(i.k,null,e.createElement(i.a,{mdSize:8,lgSize:8},n),r&&e.createElement(i.a,{className:"googlesitekit-ad-blocking-recovery__hero-graphic",lgSize:4},e.createElement(o.a,null))))}Content.propTypes={children:r.a.node}}).call(this,n(4))},function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M22.56 28.885a85.319 85.319 0 018.606-7.899C60.044-2.097 83.903-2.06 116.934 2.153 139.265 5 158.642 18.5 182.13 17.185 205.617 15.871 238.088 7.743 264 9.5c29.5 2 37 5.5 63.227 19.385 24.273 12.85 46.312 35.426 57.388 65.284 18.985 51.177-14.865 143.285-60.259 147.628-32.893 3.148-60.039-33.973-93.239-25.376-20.452 5.297-32.583 27.515-48.362 42.177-18.409 17.107-48.199 16.032-69.755 6.902-20.542-8.699-35.63-25.926-42.338-51.32-5.107-19.338-4.595-38.709-16.86-53.857C36.497 138.947 9.432 134 1.281 94.168c-5.16-25.213 5.942-49.13 21.279-65.283z",fill:"#F3F5F7"}),o=a.createElement("g",{filter:"url(#ad-blocking-recovery-setup_svg__filter0_d_149_3117)"},a.createElement("rect",{x:50.837,y:42.913,width:257.906,height:176.087,rx:14.086,fill:"#fff"}),a.createElement("rect",{x:50.139,y:42.214,width:259.302,height:177.484,rx:14.785,stroke:"#CBD0D3",strokeWidth:1.396})),c=a.createElement("rect",{x:65,y:78.486,width:78,height:126.285,rx:5.282,fill:"#EE92DA"}),s=a.createElement("circle",{cx:104.5,cy:139.528,r:11,stroke:"#fff",strokeWidth:3}),l=a.createElement("path",{d:"M112 132.028l-15.5 15.5",stroke:"#fff",strokeWidth:3}),u=a.createElement("path",{d:"M50.837 56.999c0-7.78 6.307-14.086 14.087-14.086h229.733c7.78 0 14.086 6.306 14.086 14.086v7.258H50.837v-7.258z",fill:"#EBEEF0"}),d=a.createElement("rect",{x:61.509,y:50.027,width:7.115,height:7.115,rx:3.557,fill:"#CBD0D3"}),g=a.createElement("rect",{x:72.181,y:50.027,width:7.115,height:7.115,rx:3.557,fill:"#CBD0D3"}),m=a.createElement("rect",{x:164,y:78.486,width:125.396,height:42.688,rx:5.585,fill:"#EBEEF0"}),f=a.createElement("rect",{x:164,y:129,width:92.49,height:14.229,rx:7.115,fill:"#EBEEF0"}),p=a.createElement("rect",{x:164,y:151,width:108.498,height:5.585,rx:2.793,fill:"#EBEEF0"}),b=a.createElement("rect",{x:164,y:165,width:92.49,height:5.585,rx:2.793,fill:"#EBEEF0"}),v=a.createElement("rect",{x:164,y:179,width:125.396,height:5.585,rx:2.793,fill:"#EBEEF0"}),h=a.createElement("g",{filter:"url(#ad-blocking-recovery-setup_svg__filter1_d_149_3117)"},a.createElement("path",{d:"M199 163c0-6.075 4.925-11 11-11h126c6.075 0 11 4.925 11 11v83.038c0 6.076-4.925 11-11 11H210c-6.075 0-11-4.924-11-11V163z",fill:"#fff"}),a.createElement("path",{d:"M199.5 163c0-5.799 4.701-10.5 10.5-10.5h126c5.799 0 10.5 4.701 10.5 10.5v83.038c0 5.799-4.701 10.5-10.5 10.5H210c-5.799 0-10.5-4.701-10.5-10.5V163z",stroke:"#CBD0D3"})),y=a.createElement("rect",{x:224.247,y:192.918,width:96.635,height:5.224,rx:2.612,fill:"#EBEEF0"}),O=a.createElement("rect",{x:246.882,y:222.518,width:53.106,height:22.635,rx:11.318,fill:"#77AD8C"}),_=a.createElement("circle",{cx:273,cy:172.023,r:9.576,fill:"#EBEEF0"}),E=a.createElement("path",{d:"M266.906 233.539l5.003 5.003 9.495-9.495",stroke:"#fff",strokeWidth:3}),k=a.createElement("rect",{x:222.506,y:205.106,width:98.376,height:5.224,rx:2.612,fill:"#EBEEF0"}),S=a.createElement("defs",null,a.createElement("filter",{id:"ad-blocking-recovery-setup_svg__filter0_d_149_3117",x:49.441,y:41.516,width:264.699,height:183.88,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},a.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),a.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),a.createElement("feOffset",{dx:4,dy:5}),a.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),a.createElement("feColorMatrix",{values:"0 0 0 0 0.796078 0 0 0 0 0.815686 0 0 0 0 0.827451 0 0 0 1 0"}),a.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_149_3117"}),a.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_149_3117",result:"shape"})),a.createElement("filter",{id:"ad-blocking-recovery-setup_svg__filter1_d_149_3117",x:199,y:152,width:152,height:110.038,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},a.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),a.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),a.createElement("feOffset",{dx:4,dy:5}),a.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),a.createElement("feColorMatrix",{values:"0 0 0 0 0.796078 0 0 0 0 0.815686 0 0 0 0 0.827451 0 0 0 1 0"}),a.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_149_3117"}),a.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_149_3117",result:"shape"})));t.a=function SvgAdBlockingRecoverySetup(e){return a.createElement("svg",r({viewBox:"0 0 390 273",fill:"none"},e),i,o,c,s,l,u,d,g,m,f,p,b,v,h,y,O,_,E,k,S)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CreateMessageStep}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(81),s=n(0),l=n(2),u=n(157),d=n(10),g=n(3),m=n(120),f=n(20),p=n(32),b=n(13),v=n(23),h=n(18),y=n(9),O=n(31);function CreateMessageStep(){var t=Object(h.a)(),n=Object(g.useSelect)((function(e){return e(O.l).getAccountID()})),a=Object(g.useSelect)((function(e){return e(O.l).getServiceURL({path:"/".concat(n,"/privacymessaging/ad_blocking")})})),i=Object(g.useSelect)((function(e){return e(b.c).getAdminURL("googlesitekit-dashboard")})),_=Object(u.a)(i,{notification:"ad_blocking_recovery_setup_success"}),E=Object(g.useSelect)((function(e){return e(O.l).isDoingSaveSettings()||e(p.a).isNavigatingTo(_)})),k=Object(g.useSelect)((function(e){return!!e(v.b).getValue(O.c)})),S=Object(g.useSelect)((function(e){return e(O.l).getErrorForAction("saveSettings")})),j=Object(g.useDispatch)(O.l),T=j.saveSettings,A=j.setAdBlockingRecoverySetupStatus,N=Object(g.useDispatch)(p.a).navigateTo,C=Object(g.useDispatch)(v.b).setValue,w=Object(s.useCallback)(o()(r.a.mark((function e(){var n;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(k){e.next=5;break}return e.next=3,Object(y.I)("".concat(t,"_adsense-abr"),"create_message","primary_cta");case 3:return C(O.c,!0),e.abrupt("return");case 5:return A(O.j.SETUP_CONFIRMED),e.next=8,T();case 8:if(n=e.sent,n.error){e.next=14;break}return e.next=13,Object(y.I)("".concat(t,"_adsense-abr"),"confirm_message_ready");case 13:N(_);case 14:case"end":return e.stop()}}),e)}))),[k,N,T,A,C,_,t]),I=Object(s.useCallback)(o()(r.a.mark((function e(){var n;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return A(O.j.SETUP_CONFIRMED),e.next=3,T();case 3:if(n=e.sent,n.error){e.next=9;break}return e.next=8,Object(y.I)("".concat(t,"_adsense-abr"),"confirm_message_ready_secondary_cta");case 8:N(_);case 9:case"end":return e.stop()}}),e)}))),[A,T,N,_,t]);Object(c.a)((function(){Object(y.I)("".concat(t,"_adsense-abr"),"setup_create_message")})),Object(s.useEffect)((function(){k&&Object(y.I)("".concat(t,"_adsense-abr"),"setup_final_step")}),[k,t]);return e.createElement(s.Fragment,null,e.createElement("p",null,Object(l.__)("Create and publish an ad blocking recovery message in AdSense","google-site-kit")),e.createElement("p",null,Object(l.__)("Site visitors will be given the option to allow ads on your site. You can also present them with other options to fund your site (optional)","google-site-kit")),S&&e.createElement(m.a,{error:S}),e.createElement("div",{className:"googlesitekit-ad-blocking-recovery__create-message-footer"},e.createElement("div",{className:"googlesitekit-ad-blocking-recovery__create-message-footer-actions"},k?e.createElement(s.Fragment,null,e.createElement(d.SpinnerButton,{onClick:w,isSaving:E,disabled:E},Object(l.__)("My message is ready","google-site-kit")),e.createElement(f.a,{onClick:function(){Object(y.I)("".concat(t,"_adsense-abr"),"create_message","secondary_cta")},href:a,external:!0,hideExternalIndicator:!0},Object(l.__)("Create message","google-site-kit"))):e.createElement(s.Fragment,null,e.createElement(d.Button,{href:a,target:"_blank",onClick:w},Object(l.__)("Create message","google-site-kit")),e.createElement(f.a,{onClick:I,disabled:E},Object(l.__)("I published my message","google-site-kit")))),k&&e.createElement("p",{className:"googlesitekit-ad-blocking-recovery__create-message-footer-note"},Object(l.__)("Ad blocking recovery only works if you’ve created and published your message in AdSense","google-site-kit"))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PlaceTagsStep}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(1),s=n.n(c),l=n(81),u=n(0),d=n(38),g=n(2),m=n(10),f=n(3),p=n(120),b=n(20),v=n(13),h=n(18),y=n(9),O=n(31);function PlaceTagsStep(t){var n=t.setActiveStep,a=Object(h.a)(),i=Object(f.useSelect)((function(e){return e(O.l).getUseAdBlockingRecoveryErrorSnippet()})),c=Object(f.useSelect)((function(e){return e(O.l).isDoingSaveSettings()||e(O.l).isFetchingSyncAdBlockingRecoveryTags()})),s=Object(f.useSelect)((function(e){return e(O.l).getErrorForAction("syncAdBlockingRecoveryTags")||e(O.l).getErrorForAction("saveSettings")})),_=Object(f.useSelect)((function(e){return e(v.c).getDocumentationLinkURL("ad-blocking-recovery")})),E=Object(f.useDispatch)(O.l),k=E.saveSettings,S=E.setAdBlockingRecoverySetupStatus,j=E.setUseAdBlockingRecoverySnippet,T=E.setUseAdBlockingRecoveryErrorSnippet,A=E.syncAdBlockingRecoveryTags,N=Object(u.useCallback)((function(e){var t=!!e.target.checked;T(t),Object(y.I)("".concat(a,"_adsense-abr"),t?"check_box":"uncheck_box")}),[T,a]),C=Object(u.useCallback)(o()(r.a.mark((function e(){var t,i;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,A();case 2:if(t=e.sent,!t.error){e.next=6;break}return e.abrupt("return");case 6:return S(O.j.TAG_PLACED),j(!0),e.next=10,k();case 10:if(i=e.sent,!i.error){e.next=14;break}return e.abrupt("return");case 14:return e.next=16,Object(y.I)("".concat(a,"_adsense-abr"),"setup_enable_tag");case 16:n(O.k.CREATE_MESSAGE);case 17:case"end":return e.stop()}}),e)}))),[k,n,S,j,A,a]);return Object(l.a)((function(){i||T(!0),Object(y.I)("".concat(a,"_adsense-abr"),"setup_place_tag")})),e.createElement(u.Fragment,null,e.createElement("p",null,Object(g.__)("Identify site visitors that have an ad blocker browser extension installed. These site visitors will see the ad blocking recovery message created in AdSense.","google-site-kit")),e.createElement(m.Checkbox,{checked:i,id:"ad-blocking-recovery-error-protection-tag-checkbox",name:"ad-blocking-recovery-error-protection-tag-checkbox",value:"1",onChange:N,alignLeft:!0},Object(g.__)("Enable error protection code (optional)","google-site-kit")),e.createElement("p",{className:"googlesitekit-ad-blocking-recovery__error-protection-tag-info"},Object(d.a)(Object(g.__)("If a site visitor’s ad blocker browser extension blocks the message you create in AdSense, a default, non-customizable ad blocking recovery message will display instead. <a>Learn more</a>","google-site-kit"),{a:e.createElement(b.a,{href:_,external:!0})})),s&&e.createElement(p.a,{error:s}),e.createElement(m.SpinnerButton,{onClick:C,isSaving:c,disabled:c},Object(g.__)("Enable message","google-site-kit")))}PlaceTagsStep.propTypes={setActiveStep:s.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ContentAutoUpdate}));var a=n(15),r=n.n(a),i=n(1),o=n.n(i),c=n(0),s=n(669);function ContentAutoUpdate(t){var n=t.hasBeenInView,a=Object(c.useState)({stage:0,mode:"static"}),i=r()(a,2),o=i[0],l=o.stage,u=o.mode,d=i[1];return Object(c.useEffect)((function(){if(n){var e=setTimeout((function(){d({stage:0,mode:"leave"})}),7e3);return function(){clearTimeout(e)}}}),[n]),e.createElement(s.a,{stage:l,mode:u,onAnimationEnd:function(){"enter"===u?d({stage:l,mode:"leave"}):"leave"===u&&d({stage:2===l?0:l+1,mode:"enter"})}})}ContentAutoUpdate.propTypes={hasBeenInView:o.a.bool.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(6),o=n.n(i),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(2),g=n(0),m=n(17),f=n(670),p=n(512),b=Object(g.forwardRef)((function(t,n){var a=t.stage,i=t.mode,c=t.onAnimationEnd,s=[{title:Object(d.__)("Earn money from your site","google-site-kit"),description:Object(d.__)("Focus on writing good content and let AdSense help you make it profitable","google-site-kit")},{title:Object(d.__)("Save time with automated ads","google-site-kit"),description:Object(d.__)("Auto ads automatically place and optimize your ads for you so you don't have to spend time doing it yourself","google-site-kit")},{title:Object(d.__)("You’re in control","google-site-kit"),description:Object(d.__)("Block ads you don't like, customize where ads appear, and choose which types fit your site best","google-site-kit")}],l={smSize:4,mdSize:4,lgSize:6};return e.createElement(g.Fragment,null,e.createElement(m.k,null,e.createElement(m.a,{size:12},e.createElement("p",{className:"googlesitekit-setup__intro-title"},Object(d.__)("Connect Service","google-site-kit")),e.createElement("div",{className:"googlesitekit-setup-module"},e.createElement("div",{className:"googlesitekit-setup-module__logo"},e.createElement(p.a,{width:"33",height:"33"})),e.createElement("h2",{className:"googlesitekit-heading-3 googlesitekit-setup-module__title"},Object(d._x)("AdSense","Service name","google-site-kit"))))),e.createElement(m.k,{ref:n},e.createElement(m.a,r()({},l,{smOrder:2,mdOrder:1,className:"googlesitekit-setup-module--adsense__stage-captions"}),e.createElement("ul",{className:"googlesitekit-setup-module--adsense__stage-caption-container"},s.map((function(t,n){var r=t.title,c=t.description;return e.createElement("li",{key:n,className:u()("googlesitekit-setup-module--adsense__stage-caption",o()({},"googlesitekit-setup-module--adsense__stage-caption--current--".concat(i),a===n))},e.createElement("div",{className:"googlesitekit-setup-module--adsense__stage-caption-indicator"}),e.createElement("div",null,e.createElement("h4",null,r),e.createElement("p",null,c)))}))),e.createElement("ul",{className:"googlesitekit-setup-module--adsense__stage-indicator"},s.map((function(t,n){return e.createElement("li",{key:n,className:u()(o()({},"googlesitekit-setup-module--adsense__stage-indicator--current--".concat(i),a===n))})})))),e.createElement(m.a,r()({},l,{smOrder:1,mdOrder:2,className:"googlesitekit-setup-module--adsense__stage-images"}),e.createElement("div",{className:"googlesitekit-setup-module--adsense__stage-image-container"},s.map((function(t,n){return e.createElement("div",{key:n,className:u()("googlesitekit-setup-module--adsense__stage-image",o()({},"googlesitekit-setup-module--adsense__stage-image--current--".concat(i),a===n)),onAnimationEnd:a===n?c:void 0},e.createElement(f.a,{stage:n}))}))))))}));b.propTypes={stage:s.a.oneOf([0,1,2]),mode:s.a.oneOf(["static","enter","leave"]),onAnimationEnd:s.a.func},t.a=b}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ContentSVG}));var a=n(1),r=n(0),i=n(2),o=n(44),c=n(151),s=Object(r.lazy)((function(){return n.e(33).then(n.bind(null,1217))})),l=Object(r.lazy)((function(){return n.e(34).then(n.bind(null,1218))})),u=Object(r.lazy)((function(){return n.e(35).then(n.bind(null,1219))}));function LazyContentSVG(t){var n=t.stage,a={0:e.createElement(s,null),1:e.createElement(l,null),2:e.createElement(u,null)};return a[n]?e.createElement(c.a,{errorMessage:Object(i.__)("Failed to load graphic","google-site-kit")},a[n]):null}function ContentSVG(t){var n=t.stage;return e.createElement(r.Suspense,{fallback:e.createElement(o.a,{width:"100%",height:"100%"})},e.createElement(LazyContentSVG,{stage:n}))}ContentSVG.propTypes={stage:a.PropTypes.oneOf([0,1,2]).isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return NeedsAttention}));var r=n(0),i=n(2),o=n(3),c=n(61),s=n(9),l=n(31),u=n(368);function NeedsAttention(){var t=Object(r.useContext)(c.b),n=Object(o.useSelect)((function(e){return e(l.l).getServiceAccountManageSitesURL()})),d=Object(r.useCallback)((function(a){a.preventDefault(),Object(s.I)("".concat(t,"_adsense"),"review_site_state","needs_attention"),e.open(n,"_blank")}),[n,t]),g=Object(i.__)("Your site isn’t ready to show ads yet","google-site-kit"),m=Object(i.__)("You need to fix some things with this site before we can connect Site Kit to your AdSense account","google-site-kit"),f={label:Object(i.__)("Review site in AdSense","google-site-kit"),href:n,onClick:d};return a.createElement(u.a,{heading:g,description:m,primaryButton:f})}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return GettingReady}));var r=n(0),i=n(2),o=n(3),c=n(61),s=n(9),l=n(31),u=n(368);function GettingReady(){var t=Object(r.useContext)(c.b),n=Object(o.useSelect)((function(e){return e(l.l).getServiceAccountManageSitesURL()})),d=Object(r.useCallback)((function(a){a.preventDefault(),Object(s.I)("".concat(t,"_adsense"),"review_site_state","getting_ready"),e.open(n,"_blank")}),[n,t]),g=Object(i.__)("Your site is getting ready","google-site-kit"),m=Object(i.__)("This usually takes a few days, but in some cases can take a few weeks. You’ll get an email from AdSense as soon as they have run some checks on your site.","google-site-kit"),f={label:Object(i.__)("Review site in AdSense","google-site-kit"),href:n,onClick:d};return a.createElement(u.a,{heading:g,description:m,primaryButton:f})}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return RequiresReview}));var r=n(0),i=n(2),o=n(3),c=n(61),s=n(9),l=n(31),u=n(368);function RequiresReview(){var t=Object(r.useContext)(c.b),n=Object(o.useSelect)((function(e){return e(l.l).getServiceAccountManageSitesURL()})),d=Object(r.useCallback)((function(a){a.preventDefault(),Object(s.I)("".concat(t,"_adsense"),"review_site_state","requires_review"),e.open(n,"_blank")}),[n,t]),g=Object(i.__)("Your site requires review","google-site-kit"),m=Object(i.__)("To start serving ads, your site needs to be approved first. Go to AdSense to request the review.","google-site-kit"),f={label:Object(i.__)("Request review in AdSense","google-site-kit"),href:n,onClick:d};return a.createElement(u.a,{heading:g,description:m,primaryButton:f})}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return Ready}));var r=n(5),i=n.n(r),o=n(16),c=n.n(o),s=n(15),l=n.n(s),u=n(0),d=n(2),g=n(3),m=n(61),f=n(9),p=n(31),b=n(23),v=n(368);function Ready(t){var n=t.site,r=t.finishSetup,o=Object(u.useState)(!1),s=l()(o,2),h=s[0],y=s[1],O=Object(u.useContext)(m.b),_=Object(g.useSelect)((function(e){return e(p.l).getExistingTag()})),E=Object(g.useSelect)((function(e){return e(p.l).getServiceAccountSiteAdsPreviewURL()})),k=Object(g.useSelect)((function(e){return e(p.l).isDoingSubmitChanges()})),S=Object(g.useDispatch)(p.l),j=S.completeSiteSetup,T=S.completeAccountSetup,A=Object(g.useDispatch)(b.b).setValue,N=Object(u.useCallback)((function(t){t.preventDefault(),Object(f.I)("".concat(O,"_adsense"),"enable_auto_ads"),e.open(E,"_blank")}),[E,O]),C=Object(u.useCallback)((function(e){e.preventDefault(),Object(f.I)("".concat(O,"_adsense"),"disable_auto_ads"),y(!0)}),[O]),w=Object(u.useCallback)(c()(i.a.mark((function e(){var t,n;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!k){e.next=2;break}return e.abrupt("return");case 2:return A(p.h,!0),e.next=5,j();case 5:return t=e.sent,e.next=8,T();case 8:n=e.sent,A(p.h,!1),t&&n&&"function"==typeof r&&r();case 11:case"end":return e.stop()}}),e)}))),[k,A,j,T,r]),I={};return n.autoAdsEnabled||h?(I.heading=_?Object(d.__)("Your AdSense account is ready to connect to Site Kit","google-site-kit"):Object(d.__)("Your site is ready to use AdSense","google-site-kit"),I.description=_?Object(d.__)("Connect your AdSense account to see stats on your overall earnings, page CTR, and top earning pages","google-site-kit"):Object(d.__)("Site Kit has placed AdSense code on your site to connect your site to AdSense and help you get the most out of ads","google-site-kit"),I.primaryButton={label:Object(d.__)("Complete setup","google-site-kit"),onClick:w}):(I.heading=Object(d.__)("Enable auto ads for your site","google-site-kit"),I.description=Object(d.__)("To start serving ads via Site Kit, you need to activate auto ads first. Go to AdSense and enable auto ads for your site.","google-site-kit"),I.primaryButton={label:Object(d.__)("Enable auto ads","google-site-kit"),href:E,onClick:N},_&&(I.description=Object(d.__)("Site Kit recommends enabling auto ads. If your existing AdSense setup relies on individual ad units, you can proceed without enabling auto ads.","google-site-kit"),I.secondaryButton={label:Object(d.__)("Proceed without enabling auto ads","google-site-kit"),onClick:C})),a.createElement(v.a,I)}}).call(this,n(28),n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var a=n(12),r=n.n(a),i=n(9),o=function(e){var t=e.startDate,n=e.endDate,a=e.compareStartDate,o=e.compareEndDate;r()(Object(i.w)(t),"A valid startDate is required."),r()(Object(i.w)(n),"A valid endDate is required.");var c={"_u.date00":t.replace(/-/g,""),"_u.date01":n.replace(/-/g,"")};return(a||o)&&(r()(Object(i.w)(a)&&Object(i.w)(o),"Valid compareStartDate and compareEndDate values are required."),c["_u.date10"]=a.replace(/-/g,""),c["_u.date11"]=o.replace(/-/g,"")),c}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GhostCardRedSVG}));var a=n(0),r=n(2),i=n(44),o=n(151),c=Object(a.lazy)((function(){return n.e(37).then(n.bind(null,1221))}));function GhostCardRedSVG(){return e.createElement(a.Suspense,{fallback:e.createElement(i.a,{width:"100%",height:"90px"})},e.createElement(o.a,{errorMessage:Object(r.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"d",(function(){return s})),n.d(t,"b",(function(){return l}));var a=n(2),r=n(407),i=n(140),o=function(e){var t=Object(a.__)("Your site isn’t ready to show ads yet","google-site-kit");switch(e){case i.a:t=Object(a.__)("Your account has been approved","google-site-kit");break;case i.k:t=Object(a.__)("Your site is ready for ads","google-site-kit");break;case i.j:case i.e:t=Object(a.__)("We’re getting your site ready for ads. This usually takes less than a day, but it can sometimes take a bit longer","google-site-kit");break;case i.i:case i.d:case i.g:case i.c:case i.b:t=Object(a.__)("You need to fix some issues before your account is approved. Go to AdSense to find out how to fix it","google-site-kit")}return t},c=function(e){var t="";switch(e){case i.n:case i.r:t=Object(a.__)("You need to fix some things before your site is ready.","google-site-kit");break;case i.m:t=Object(a.__)("Your site is getting ready.","google-site-kit");break;case i.p:t=Object(a.__)("Your site is ready for ads.","google-site-kit");break;case i.q:t=Object(a.__)("Your site is ready, with auto-ads disabled.","google-site-kit")}return t},s=function(e,t,n){var r=Object(a.__)("The AdSense code has not been placed on your site","google-site-kit");return e?r=Object(a.__)("The AdSense code has been placed on your site","google-site-kit"):t&&t===n&&(r=Object(a.__)("The AdSense code has been placed by another plugin or theme","google-site-kit")),r},l=function(e){var t=Object(a.__)("Ads are currently displayed for all visitors","google-site-kit");return e.includes(r.b)&&e.includes(r.a)?t=Object(a.__)("All logged-in users and users who can write posts","google-site-kit"):e.includes(r.b)?t=r.d[r.b]:e.includes(r.a)&&(t=r.d[r.a]),t}},,,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdBlockingRecoveryToggle}));var a=n(81),r=n(38),i=n(2),o=n(10),c=n(3),s=n(20),l=n(166),u=n(29),d=n(13),g=n(18),m=n(9),f=n(31),p=n(259);function AdBlockingRecoveryToggle(){var t,n=Object(g.a)(),b=Object(c.useSelect)((function(e){return e(f.l).getUseAdBlockingRecoverySnippet()})),v=Object(c.useSelect)((function(e){return e(f.l).getUseAdBlockingRecoveryErrorSnippet()})),h=Object(c.useSelect)((function(e){return e(f.l).getAdBlockingRecoverySetupStatus()})),y=Object(c.useSelect)((function(e){return e(f.l).getExistingAdBlockingRecoveryTag()})),O=Object(c.useSelect)((function(e){return e(f.l).getAccountID()})),_=Object(c.useSelect)((function(e){return e(f.l).getServiceURL({path:"/".concat(O,"/privacymessaging/ad_blocking")})})),E=Object(c.useSelect)((function(e){return e(d.c).getDocumentationLinkURL("ad-blocking-recovery")})),k=Object(c.useSelect)((function(e){return e(u.a).getValue(f.a,"adBlockingRecoveryToggle")})),S=Object(c.useSelect)((function(e){return e(u.a).getValue(f.a,"adBlockingRecoveryErrorToggle")})),j=Object(c.useDispatch)(u.a).setValues,T=Object(c.useDispatch)(f.l),A=T.setUseAdBlockingRecoverySnippet,N=T.setUseAdBlockingRecoveryErrorSnippet;return Object(a.a)((function(){var e={adBlockingRecoveryToggle:b,adBlockingRecoveryErrorToggle:v};j(f.a,e)})),y&&y===O?t=Object(i.__)("You’ve already enabled an ad blocking recovery message on your site. We recommend using Site Kit to manage this to get the most out of AdSense.","google-site-kit"):y&&(t=Object(i.sprintf)(
/* translators: %s: account ID */
Object(i.__)("Site Kit detected Ad Blocking Recovery code for a different account %s on your site. For a better ad blocking recovery experience, you should remove Ad Blocking Recovery code that’s not linked to this AdSense account.","google-site-kit"),Object(p.i)(y))),h?e.createElement("fieldset",{className:"googlesitekit-settings-module__ad-blocking-recovery-toggles"},e.createElement("legend",{className:"googlesitekit-setup-module__text"},Object(i.__)("Ad blocking recovery","google-site-kit")),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement(o.Switch,{label:Object(i.__)("Enable ad blocking recovery message","google-site-kit"),checked:k,onClick:function(){var e=!k;j(f.a,{adBlockingRecoveryToggle:e}),A(e),Object(m.I)("".concat(n,"_adsense-abr"),e?"enable_tag":"disable_tag","abr_tag")},hideLabel:!1}),e.createElement("p",null,Object(r.a)(Object(i.__)("Identify site visitors that have an ad blocker browser extension installed. These site visitors will see the ad blocking recovery message created in AdSense. <a>Configure your message</a>","google-site-kit"),{a:e.createElement(s.a,{href:_,external:!0})}))),(k||b)&&e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement(o.Switch,{label:Object(i.__)("Place error protection code","google-site-kit"),checked:S,onClick:function(){var e=!S;j(f.a,{adBlockingRecoveryErrorToggle:e}),N(e),Object(m.I)("".concat(n,"_adsense-abr"),e?"enable_tag":"disable_tag","error_protection_tag")},hideLabel:!1}),e.createElement("p",null,Object(r.a)(Object(i.__)("If a site visitor’s ad blocker browser extension blocks the message you create in AdSense, a default, non-customizable ad blocking recovery message will display instead. <a>Learn more</a>","google-site-kit"),{a:e.createElement(s.a,{href:E,external:!0})})))),y&&e.createElement(l.a,{notice:t})):null}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsForm}));var a=n(2),r=n(0),i=n(38),o=n(3),c=n(10),s=n(31),l=n(383),u=n(162),d=n(595),g=n(20),m=n(13),f=n(612),p=n(740);function SettingsForm(){var t=Object(o.useSelect)((function(e){return e(m.c).isWebStoriesActive()})),n=Object(o.useSelect)((function(e){return e(s.l).getClientID()})),b=Object(o.useSelect)((function(e){return e(s.l).getExistingTag()})),v=Object(o.useSelect)((function(e){return e(s.l).hasFinishedResolution("getExistingTag")}));Object(o.useSelect)((function(e){return e(s.l).getExistingAdBlockingRecoveryTag()}));var h,y,O=Object(o.useSelect)((function(e){return e(s.l).hasFinishedResolution("getExistingAdBlockingRecoveryTag")}));if(!v||!O)return e.createElement(c.ProgressBar,null);b&&b===n?y=h=Object(a.__)("You’ve already got an AdSense code on your site for this account, we recommend you use Site Kit to place code to get the most out of AdSense","google-site-kit"):b?(h=Object(a.sprintf)(
/* translators: %s: account ID */
Object(a.__)("Site Kit detected AdSense code for a different account %s on your site. For a better ads experience, you should remove AdSense code that’s not linked to this AdSense account.","google-site-kit"),Object(l.a)(b)),y=Object(a.__)("Please note that AdSense will not show ads on your website unless you’ve already placed the code","google-site-kit")):y=Object(a.__)("Please note that AdSense will not show ads on your website unless you’ve already placed the code","google-site-kit");return e.createElement("div",{className:"googlesitekit-adsense-settings-fields"},e.createElement(u.d,null),e.createElement(u.e,{checkedMessage:h,uncheckedMessage:y}),t&&e.createElement(r.Fragment,null,e.createElement(d.a,null),e.createElement("p",null,Object(i.a)(Object(a.__)("This ad unit will be used for your Web Stories. <LearnMoreLink />","google-site-kit"),{LearnMoreLink:e.createElement(g.a,{href:"https://support.google.com/adsense/answer/********#create-an-ad-unit-for-web-stories",external:!0,"aria-label":Object(a.__)("Learn more about Ad Sense Web Stories.","google-site-kit")},Object(a.__)("Learn more","google-site-kit"))}))),e.createElement(u.c,null),e.createElement(r.Fragment,null,e.createElement(f.a,null),e.createElement(p.a,null)))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdBlockingRecoverySetupSuccessNotification}));var a=n(15),r=n.n(a),i=n(0),o=n(38),c=n(2),s=n(3),l=n(31),u=n(9),d=n(13),g=n(7),m=n(115),f=n(92),p=n(20),b=n(147);function AdBlockingRecoverySetupSuccessNotification(t){var n=t.id,a=t.Notification,v=Object(s.useDispatch)(g.a).triggerSurvey,h=Object(s.useSelect)((function(e){return e(d.c).isUsingProxy()})),y=Object(s.useSelect)((function(e){return e(l.l).getAccountID()})),O=Object(s.useSelect)((function(e){return e(l.l).getServiceURL({path:"/".concat(y,"/privacymessaging/ad_blocking")})})),_=Object(i.useCallback)((function(){h&&v("abr_setup_completed",{ttl:u.a})}),[v,h]),E=Object(b.a)("notification"),k=r()(E,2)[1],S=Object(i.useCallback)((function(){k(void 0)}),[k]);return e.createElement(a,{onView:_},e.createElement(m.a,{title:Object(c.__)("You successfully enabled the ad blocking recovery message","google-site-kit"),description:Object(o.a)(Object(c.__)("Make sure to also create the message in <a>AdSense</a>, otherwise this feature won’t work","google-site-kit"),{a:e.createElement(p.a,{href:O,external:!0,hideExternalIndicator:!0})}),dismissCTA:e.createElement(f.a,{onDismiss:S,id:n})}))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConnectAdSenseCTATileWidget}));var a=n(1),r=n.n(a),i=n(530),o=n(143),c={moduleSlug:"adsense"};function ConnectAdSenseCTATileWidget(t){var n=t.Widget,a=t.widgetSlug;return Object(o.a)(a,i.a,c),e.createElement(n,null,e.createElement(i.a,c))}ConnectAdSenseCTATileWidget.propTypes={Widget:r.a.elementType.isRequired,widgetSlug:r.a.string.isRequired}}).call(this,n(4))},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";(function(e){var a=n(2),r=n(7),i=n(523),o=n(3);t.a=function Header(){var t=Object(o.useSelect)((function(e){return e(r.a).getDateRangeNumberOfDays()}));return e.createElement(i.a,{title:Object(a.sprintf)(
/* translators: %s: number of days */
Object(a._n)("Performance over the last %s day","Performance over the last %s days",t,"google-site-kit"),t)})}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(2),r=n(31),i=n(7),o=n(1039),c=n(134),s=n(3),l=n(34);t.a=function Footer(){var t=Object(l.a)(),n=Object(s.useSelect)((function(e){return e(i.a).getDateRangeDates({offsetDays:r.i})})),u=Object(s.useSelect)((function(e){return t?null:e(r.l).getServiceReportURL(Object(o.a)(n))}));return e.createElement(c.a,{href:u,name:Object(a._x)("AdSense","Service name","google-site-kit"),external:!0})}}).call(this,n(4))},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsEdit}));var a=n(3),r=n(10),i=n(31),o=n(741);function SettingsEdit(){var t;return t=Object(a.useSelect)((function(e){return e(i.l).isDoingSubmitChanges()}))?e.createElement(r.ProgressBar,null):e.createElement(o.a,null),e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--adsense"},t)}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsSetupIncomplete}));var a=n(0),r=n(38),i=n(2),o=n(3),c=n(20),s=n(19),l=n(31),u=n(140),d=n(562);function SettingsSetupIncomplete(){var t,n,g=Object(o.useSelect)((function(e){return e(l.l).getAccountStatus()})),m=Object(u.s)(g),f=Object(o.useSelect)((function(e){return e(l.l).getAdminReauthURL()})),p=Object(o.useSelect)((function(e){var t;return null===(t=e(s.a))||void 0===t?void 0:t.getCheckRequirementsError("adsense")}));return m?(
/* translators: %s: link with next step */
t=Object(i.__)("Site Kit has placed AdSense code on your site: %s","google-site-kit"),n=Object(i.__)("check module page","google-site-kit")):(
/* translators: %s: link with next step */
t=Object(i.__)("Setup incomplete: %s","google-site-kit"),n=Object(i.__)("continue module setup","google-site-kit")),e.createElement(a.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-module__fields-group googlesitekit-settings-module__fields-group--no-border"},e.createElement(d.a,{slug:"adsense"})),e.createElement("div",{className:"googlesitekit-settings-module__fields-group-title"},Object(r.a)(Object(i.sprintf)(t,"<a>".concat(n,"</a>")),{a:e.createElement(c.a,{className:"googlesitekit-settings-module__edit-button",href:f,disabled:!!p})})))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsView}));var a=n(38),r=n(0),i=n(2),o=n(3),c=n(182),s=n(10),l=n(20),u=n(13),d=n(31),g=n(162),m=n(730),f=n(612),p=n(105);function SettingsView(){var t=Object(o.useSelect)((function(e){return e(d.l).getAccountID()})),n=Object(o.useSelect)((function(e){return e(d.l).getServiceAccountManageSitesURL()})),b=Object(o.useSelect)((function(e){return e(u.c).isWebStoriesActive()})),v=Object(o.useSelect)((function(e){return e(d.l).getWebStoriesAdUnit()})),h=Object(o.useSelect)((function(e){return e(d.l).getAccountStatus()})),y=Object(o.useSelect)((function(e){return e(d.l).getUseAdBlockingRecoverySnippet()})),O=Object(o.useSelect)((function(e){return e(d.l).getUseAdBlockingRecoveryErrorSnippet()})),_=Object(o.useSelect)((function(e){return e(d.l).getSiteStatus()})),E=Object(o.useSelect)((function(e){return e(d.l).getUseSnippet()})),k=Object(o.useSelect)((function(e){return e(d.l).getAdBlockingRecoverySetupStatus()})),S=Object(o.useSelect)((function(e){return e(d.l).getExistingTag()})),j=Object(o.useSelect)((function(e){return e(d.l).getClientID()})),T=Object(o.useSelect)((function(e){return e(d.l).getAutoAdsDisabled()||[]})),A=Object(o.useSelect)((function(e){return e(d.l).getServiceURL({path:"/".concat(t,"/privacymessaging/ad_blocking")})})),N=Object(m.a)(h),C=Object(m.c)(_),w=Object(a.a)(Object(i.__)("View <VisuallyHidden>site </VisuallyHidden>in AdSense","google-site-kit"),{VisuallyHidden:e.createElement(p.a,null)}),I=Object(m.d)(E,S,j),M=Object(m.b)(T),R=Object(o.useSelect)((function(e){return void 0===e(d.l).getSettings()||void 0===e(d.l).hasExistingAdBlockingRecoveryTag()}));return e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--adsense"},e.createElement(g.d,null),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(i.__)("Publisher ID","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(c.b,{value:t}))),e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(i.__)("Account Status","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},N)),e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(i.__)("Site Status","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},C+" ",e.createElement(l.a,{href:n,className:"googlesitekit-settings-module__cta-button",external:!0,disabled:void 0===n,hideExternalIndicator:void 0===n},w)))),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(i.__)("AdSense Code","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},I))),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(i.__)("Excluded from ads","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},M))),b&&e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(i.__)("Web Stories Ad Unit","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},!v&&e.createElement("span",null,Object(i.__)("None","google-site-kit")),v&&e.createElement(c.b,{value:v})))),(null==k?void 0:k.length)>0&&e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},R&&e.createElement(s.ProgressBar,{small:!0,height:90}),!R&&e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(i.__)("Ad blocking recovery","google-site-kit")),!y&&e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},Object(i.__)("Ad blocking recovery message is not placed","google-site-kit")),y&&e.createElement(r.Fragment,null,e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},O?Object(i.__)("Ad blocking recovery message enabled with error protection code","google-site-kit"):Object(i.__)("Ad blocking recovery message enabled without error protection code","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},Object(a.a)(Object(i.__)("Identify site visitors that have an ad blocker browser extension installed. These site visitors will see the ad blocking recovery message created in AdSense. <a>Configure your message</a>","google-site-kit"),{a:e.createElement(l.a,{href:A,external:!0})}))))),!(null==k?void 0:k.length)&&e.createElement(r.Fragment,null,R&&e.createElement(s.ProgressBar,{small:!0,height:135}),!R&&e.createElement(f.a,null)))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(3),o=n(245),c=n(50),s=n(7);function AdBlockerWarningWidget(t){var n=t.Widget,a=t.WidgetNull;return Object(i.useSelect)((function(e){return e(s.a).isAdBlockerActive()}))?e.createElement(n,{noPadding:!0},e.createElement(o.a,{moduleSlug:"adsense"})):e.createElement(a,null)}AdBlockerWarningWidget.propTypes={Widget:r.a.elementType.isRequired},t.a=Object(c.a)({moduleName:"adsense"})(AdBlockerWarningWidget)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(1),s=n.n(c),l=n(0),u=n(38),d=n(2),g=n(3),m=n(1038),f=n(175),p=n(20),b=n(211),v=n(212),h=n(213),y=n(32),O=n(13),_=n(7),E=n(24),k=n(411),S=n(18),j=n(34),T=n(17),A=n(9),N=n(50),C=n(31),w=n(259),I=n(251);function AdBlockingRecoverySetupCTAWidget(t){var n=t.Widget,a=t.WidgetNull,i=Object(E.e)(),c=Object(j.a)(),s=Object(k.a)(),N=Object(S.a)(),M={tooltipSlug:C.b,title:Object(d.__)("You can always set up ad blocking recovery in Settings later","google-site-kit"),dismissLabel:Object(d.__)("Got it","google-site-kit")},R=Object(f.b)(M),D=Object(g.useSelect)((function(e){return e(_.a).isPromptDismissed(C.b)})),x=Object(g.useSelect)((function(e){return e(_.a).getPromptDismissCount(C.b)})),L=Object(g.useSelect)((function(e){return e(_.a).isDismissingPrompt(C.b)})),P=Object(g.useSelect)((function(e){return c?null:e(C.l).getAdBlockingRecoverySetupStatus()})),G=Object(g.useSelect)((function(e){return c?null:e(C.l).getAccountStatus()})),Z=Object(g.useSelect)((function(e){return c?null:e(C.l).getSetupCompletedTimestamp()})),B=Object(g.useSelect)((function(e){return c?null:e(C.l).getSiteStatus()})),U=Object(g.useSelect)((function(e){return e(C.l).hasExistingAdBlockingRecoveryTag()})),F=Object(g.useSelect)((function(e){return e(O.c).getGoogleSupportURL({path:"/adsense/answer/********"})})),z=Object(g.useSelect)((function(e){return e(O.c).getAdminURL("googlesitekit-ad-blocking-recovery")})),V=Object(g.useSelect)((function(e){return e(_.a).getReferenceDate()})),H=Object(g.useDispatch)(_.a).dismissPrompt,W=Object(g.useDispatch)(y.a).navigateTo,q=Object(A.G)(V).getTime()-1e3*Z>=3*A.f*1e3,K=!c&&!1===U&&!1===D&&!1===L&&""===P&&G===w.a&&B===w.c&&(!Z||q);Object(l.useEffect)((function(){s&&K&&Object(A.I)("".concat(N,"_adsense-abr-cta-widget"),"view_notification")}),[s,K,N]);var Y=function(){var e=o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(A.I)("".concat(N,"_adsense-abr-cta-widget"),"confirm_notification");case 2:return e.abrupt("return",W(z));case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),$=function(){var e=o()(r.a.mark((function e(){var t;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Object(A.I)("".concat(N,"_adsense-abr-cta-widget"),"dismiss_notification"),R(),!(x<2)){e.next=8;break}return t=2*A.f,e.next=6,H(C.b,{expiresInSeconds:t});case 6:e.next=10;break;case 8:return e.next=10,H(C.b);case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return K?e.createElement(n,null,s&&K&&e.createElement(I.a,{triggerID:"view_abr_setup_cta",ttl:A.a}),e.createElement(b.a,null,e.createElement(T.a,{smSize:8,mdSize:4,lgSize:7},e.createElement(h.a,{title:Object(d.__)("Recover revenue lost to ad blockers","google-site-kit")}),e.createElement("div",{className:"googlesitekit-widget--adBlockingRecovery__content"},e.createElement("p",null,Object(u.a)(Object(d.__)("Display a message to give site visitors with an ad blocker the option to allow ads on your site. <a>Learn more</a>","google-site-kit"),{a:e.createElement(p.a,{onClick:function(){Object(A.I)("".concat(N,"_adsense-abr-cta-widget"),"click_learn_more_link")},href:F,external:!0})})),e.createElement("p",null,Object(d.__)("Publishers see up to 1 in 5 users choose to allow ads once they encounter an ad blocking recovery message*","google-site-kit"))),e.createElement(v.a,{ctaLink:"#",ctaLabel:Object(d.__)("Set up now","google-site-kit"),ctaCallback:Y,dismissCallback:$,dismissLabel:x<2?Object(d.__)("Maybe later","google-site-kit"):Object(d.__)("Don’t show again","google-site-kit")})),e.createElement(T.a,{className:"googlesitekit-widget--adBlockingRecovery__graphics",smSize:8,mdSize:4,lgSize:5},i!==E.b&&e.createElement(m.a,{style:{maxHeight:"172px"}}),e.createElement("p",null,Object(d.__)("*Average for publishers showing non-dismissible ad blocking recovery messages placed at the center of the page on desktop","google-site-kit"))))):e.createElement(a,null)}AdBlockingRecoverySetupCTAWidget.propTypes={Widget:s.a.elementType.isRequired,WidgetNull:s.a.elementType.isRequired},t.a=Object(N.a)({moduleName:"adsense"})(AdBlockingRecoverySetupCTAWidget)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(0),s=n(2),l=n(1),u=n.n(l),d=n(3),g=n(596),m=n(609),f=n(7),p=n(19),b=n(175);function AdSenseConnectCTAWidget(t){var n=t.Widget,a=t.WidgetNull,i=Object(d.useDispatch)(f.a).dismissItem,l={tooltipSlug:m.b,title:Object(s.__)("You can always connect AdSense from here later","google-site-kit"),content:Object(s.__)("The Monetization section will be added back to your dashboard if you connect AdSense in Settings later","google-site-kit"),dismissLabel:Object(s.__)("Got it","google-site-kit")},u=Object(b.b)(l),v=Object(d.useSelect)((function(e){return e(p.a).isModuleConnected("adsense")})),h=Object(d.useSelect)((function(e){return e(f.a).isItemDismissed(m.a)})),y=Object(d.useSelect)((function(e){return e(f.a).isDismissingItem(m.a)})),O=Object(c.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return u(),e.next=3,i(m.a);case 3:case"end":return e.stop()}}),e)}))),[i,u]);return!1===v&&!1===h&&!1===y?e.createElement(n,{noPadding:!0},e.createElement(g.a,{onDismissModule:O})):e.createElement(a,null)}AdSenseConnectCTAWidget.propTypes={Widget:u.a.elementType.isRequired,WidgetNull:u.a.elementType.isRequired},t.a=AdSenseConnectCTAWidget}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(15),r=n.n(a),i=n(1),o=n.n(i),c=n(240),s=n(377),l=n(2),u=n(0),d=n(3),g=n(609),m=n(7),f=n(8),p=n(31),b=n(675),v=n(9),h=n(18),y=n(34),O=n(50),_=n(134),E=n(20),k=n(503),S=n(504),j=n(505),T=n(150),A=n(162),N=n(245);function DashboardTopEarningPagesWidgetGA4(t){var n=t.WidgetNull,a=t.WidgetReportError,i=t.Widget,o=Object(y.a)(),s=Object(d.useInViewSelect)((function(e){return e(f.r).isGatheringData()})),O=Object(d.useSelect)((function(e){return e(m.a).getDateRangeDates({offsetDays:f.g})})),C=O.startDate,w=O.endDate,I=Object(d.useSelect)((function(e){return e(p.l).getAccountID()})),M={startDate:C,endDate:w,dimensions:["pagePath","adSourceName"],metrics:[{name:"totalAdRevenue"}],dimensionFilters:{adSourceName:"Google AdSense account (".concat(I,")")},orderby:[{metric:{metricName:"totalAdRevenue"},desc:!0}],limit:5},R=Object(d.useInViewSelect)((function(e){return e(f.r).getReport(M)}),[M]),D=Object(d.useSelect)((function(e){return e(f.r).getErrorForSelector("getReport",[M])})),x=Object(d.useInViewSelect)((function(e){return D?void 0:e(f.r).getPageTitles(R,M)}),[R,M]),L=Object(d.useSelect)((function(e){return!e(f.r).hasFinishedResolution("getReport",[M])||!D&&void 0===x})),P=Object(d.useSelect)((function(e){return e(m.a).isItemDismissed(g.c)})),G=Object(d.useSelect)((function(e){return o?null:e(f.r).getServiceReportURL("content-publisher-overview",Object(b.a)({startDate:C,endDate:w}))})),Z=Object(d.useSelect)((function(e){return e(f.r).getAdSenseLinked()})),B=Object(d.useSelect)((function(e){return e(m.a).isAdBlockerActive()})),U=Object(u.useRef)(),F=Object(u.useState)(!1),z=r()(F,2),V=z[0],H=z[1],W=function(e){U.current=e,e&&!V&&H(!0)},q=Object(h.a)(),K=Object(c.a)(U,{threshold:.25}),Y=Object(u.useState)(!1),$=r()(Y,2),X=$[0],J=$[1],Q=!!(null==K?void 0:K.intersectionRatio);Object(u.useEffect)((function(){Q&&!X&&(Z&&Object(v.I)("".concat(q,"_top-earning-pages-widget"),"view_widget"),Z||Object(v.I)("".concat(q,"_top-earning-pages-widget"),"view_notification"),J(!0))}),[Q,q,Z,X]);if(P)return e.createElement(n,null);if(!Z&&o)return e.createElement(n,null);if(B)return e.createElement(i,{Footer:Footer},e.createElement(N.a,{moduleSlug:"adsense"}));if(L||void 0===s)return e.createElement(i,{noPadding:!0,Footer:Footer},e.createElement(k.a,{rows:5,padding:!0}));if(!Z&&!o)return e.createElement(i,{Footer:Footer,ref:W},e.createElement(A.b,{onClick:function(){Object(v.I)("".concat(q,"_top-earning-pages-widget"),"click_learn_more_link")}}));if(D)return e.createElement(i,{Footer:Footer},e.createElement(a,{moduleSlug:"analytics-4",error:D}));function Footer(){return e.createElement(_.a,{className:"googlesitekit-data-block__source",name:Object(l._x)("Analytics","Service name","google-site-kit"),href:G,external:!0})}var ee=[{title:Object(l.__)("Top Earning Pages","google-site-kit"),tooltip:Object(l.__)("Top Earning Pages","google-site-kit"),primary:!0,Component:function Component(t){var n=t.row,a=r()(n.dimensionValues,1)[0].value,i=x[a],c=Object(d.useSelect)((function(e){return o?null:e(f.r).getServiceReportURL("all-pages-and-screens",{filters:{unifiedPagePathScreen:a},dates:{startDate:C,endDate:w}})}));return o?e.createElement("span",null,i):e.createElement(E.a,{href:c,title:i,external:!0,hideExternalIndicator:!0},i)}},{title:Object(l.__)("Earnings","google-site-kit"),tooltip:Object(l.__)("Earnings","google-site-kit"),field:"metricValues.0.value",Component:function Component(t){var n,a=t.fieldValue;return e.createElement("span",null,Object(v.B)(a,{style:"currency",currency:null==R||null===(n=R.metadata)||void 0===n?void 0:n.currencyCode}))}}];return e.createElement(i,{noPadding:!0,Footer:Footer,ref:W},e.createElement(j.a,null,e.createElement(S.a,{rows:(null==R?void 0:R.rows)||[],columns:ee,zeroState:T.h,gatheringData:s})))}DashboardTopEarningPagesWidgetGA4.propTypes={Widget:o.a.elementType.isRequired,WidgetNull:o.a.elementType.isRequired,WidgetReportError:o.a.elementType.isRequired},t.a=Object(s.a)(Object(O.a)({moduleName:"adsense"}),Object(O.a)({moduleName:"analytics-4"}))(DashboardTopEarningPagesWidgetGA4)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(6),r=n.n(a),i=n(15),o=n.n(i),c=n(1),s=n.n(c),l=n(0),u=n(2),d=n(31),g=n(7),m=n(259),f=n(44),p=n(50),b=n(797),v=n(798),h=n(1040),y=n(1041),O=n(3),_=n(1043),E=n(34);function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function S(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ModuleOverviewWidget(t){var n=t.Widget,a=t.WidgetReportError,r=Object(E.a)(),i=Object(l.useState)(0),c=o()(i,2),s=c[0],u=c[1],p=Object(O.useSelect)((function(e){return r?null:e(d.l).getAccountStatus()})),k=Object(O.useSelect)((function(e){return r?null:e(d.l).getSiteStatus()})),j=m.h.includes(p)||k===m.b,T=Object(O.useSelect)((function(e){return e(g.a).getDateRangeDates({compare:!0,offsetDays:d.i})})),A=T.startDate,N=T.endDate,C=T.compareStartDate,w=T.compareEndDate,I={metrics:Object.keys(ModuleOverviewWidget.metrics),startDate:A,endDate:N},M={metrics:Object.keys(ModuleOverviewWidget.metrics),startDate:C,endDate:w},R=S(S({},I),{},{dimensions:["DATE"]}),D=S(S({},M),{},{dimensions:["DATE"]}),x=Object(O.useInViewSelect)((function(e){return e(d.l).getReport(I)}),[I]),L=Object(O.useInViewSelect)((function(e){return e(d.l).getReport(M)}),[M]),P=Object(O.useInViewSelect)((function(e){return e(d.l).getReport(R)}),[R]),G=Object(O.useInViewSelect)((function(e){return e(d.l).getReport(D)}),[D]),Z=Object(O.useSelect)((function(e){return!(e(d.l).hasFinishedResolution("getReport",[I])&&e(d.l).hasFinishedResolution("getReport",[M])&&e(d.l).hasFinishedResolution("getReport",[R])&&e(d.l).hasFinishedResolution("getReport",[D]))})),B=Object(O.useSelect)((function(e){return[e(d.l).getErrorForSelector("getReport",[I])].concat([e(d.l).getErrorForSelector("getReport",[M])],[e(d.l).getErrorForSelector("getReport",[R])],[e(d.l).getErrorForSelector("getReport",[D])])})).filter(Boolean);return Z?e.createElement(n,{Header:b.a,Footer:v.a,noPadding:!0},e.createElement(f.a,{width:"100%",height:"190px",padding:!0}),e.createElement(f.a,{width:"100%",height:"270px",padding:!0})):B.length?e.createElement(n,{Header:b.a,Footer:v.a},e.createElement(a,{moduleSlug:"adsense",error:B})):e.createElement(n,{noPadding:!0,Header:b.a,Footer:v.a},!r&&j&&e.createElement(_.a,null),e.createElement(h.a,{metrics:ModuleOverviewWidget.metrics,currentRangeData:x,previousRangeData:L,selectedStats:s,handleStatsSelection:u}),e.createElement(y.a,{metrics:ModuleOverviewWidget.metrics,currentRangeData:P,previousRangeData:G,selectedStats:s}))}ModuleOverviewWidget.propTypes={Widget:s.a.elementType.isRequired,WidgetReportZero:s.a.elementType.isRequired,WidgetReportError:s.a.elementType.isRequired},ModuleOverviewWidget.metrics={ESTIMATED_EARNINGS:Object(u.__)("Earnings","google-site-kit"),PAGE_VIEWS_RPM:Object(u.__)("Page RPM","google-site-kit"),IMPRESSIONS:Object(u.__)("Impressions","google-site-kit"),PAGE_VIEWS_CTR:Object(u.__)("Page CTR","google-site-kit")},t.a=Object(p.a)({moduleName:"adsense"})(ModuleOverviewWidget)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var a=n(6),r=n.n(a),i=n(1),o=n.n(i),c=n(377),s=n(3),l=n(7),u=n(31),d=n(108),g=n(20),m=n(150),f=n(9),p=n(50),b=n(138),v=n(34),h=n(162),y=n(8),O=n(743);function _(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function E(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function TopEarningContentWidget(t){var n=t.Widget,a=Object(v.a)(),r=Object(s.useSelect)((function(e){return e(l.a).getDateRangeDates({offsetDays:u.i})})),i=Object(s.useSelect)((function(e){return e(u.l).getAccountID()})),o=E(E({},r),{},{dimensions:["pagePath","adSourceName"],metrics:[{name:"totalAdRevenue"}],dimensionFilters:{adSourceName:"Google AdSense account (".concat(i,")")},orderby:[{metric:{metricName:"totalAdRevenue"},desc:!0}],limit:3}),c=Object(s.useInViewSelect)((function(e){return e(y.r).getReport(o)}),[o]),p=Object(s.useSelect)((function(e){return e(y.r).getErrorForSelector("getReport",[o])})),b=Object(s.useInViewSelect)((function(e){return p?void 0:e(y.r).getPageTitles(c,o)}),[c,o]),O=Object(s.useSelect)((function(e){return!e(y.r).hasFinishedResolution("getReport",[o])||void 0===b}));if(!Object(s.useSelect)((function(e){if(!a||!O)return e(y.r).getAdSenseLinked()}))&&!a)return e.createElement(n,null,e.createElement(h.b,null));var _=(c||{}).rows,k=void 0===_?[]:_,S=[{field:"dimensionValues.0.value",Component:function Component(t){var n=t.fieldValue,i=b[n],o=Object(s.useSelect)((function(e){return a?null:e(y.r).getServiceReportURL("all-pages-and-screens",{filters:{unifiedPagePathScreen:n},dates:r})}));return a?e.createElement(d.f,{content:i}):e.createElement(g.a,{href:o,title:i,external:!0,hideExternalIndicator:!0},i)}},{field:"metricValues.0.value",Component:function Component(t){var n,a=t.fieldValue;return e.createElement("strong",null,Object(f.B)(a,{style:"currency",currency:null==c||null===(n=c.metadata)||void 0===n?void 0:n.currencyCode}))}}];return e.createElement(d.e,{Widget:n,widgetSlug:l.f,loading:O,rows:k,columns:S,ZeroState:m.h,error:p,moduleSlug:"analytics-4"})}TopEarningContentWidget.propTypes={Widget:o.a.elementType.isRequired},t.a=Object(c.a)(Object(p.a)({moduleName:"analytics-4",FallbackComponent:b.a}),Object(p.a)({moduleName:"adsense",FallbackComponent:O.a}))(TopEarningContentWidget)}).call(this,n(4))},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M4.844 147.762c2.194 6.077 5.317 11.655 9.223 16.472 15.35 18.759 38.032 20.349 58.833 15.655 21.062-4.784 40.919-14.928 58.106-29.684 9.89-8.504 19.66-19.057 32.756-19.528 4.917-.175 10.002 1.397 13.716 5.105 7.719 7.659 7.582 20.754 13.605 29.83 8.011 12.071 19.533 15.809 32.101 17.202 35.507 3.931 77.801-8.967 102.225-40.351 21.807-28.017 24.424-72.598-1.972-98.14-12.059-11.67-28.596-17.015-44.205-14.291-11.928 2.082-24.039 8.416-36.387 10.178-14.544 2.078-23.534-5.01-35.301-13.267-20.503-14.35-47.608-19.863-70.817-11.538-22.217 7.968-38.207 29.724-59.826 39.282-18.68 8.264-40.748 7.006-56.98 20.466C.716 91.073-4.112 123.22 4.844 147.762z",fill:"#F3F5F7"}),o=a.createElement("path",{d:"M82.612 161.531c9.217 0 16.69-2.74 16.69-6.12v-2.225h-33.38v2.225c0 3.38 7.472 6.12 16.69 6.12z",fill:"#C59539"}),c=a.createElement("ellipse",{cx:82.612,cy:153.185,rx:16.69,ry:6.12,fill:"#E1B155"}),s=a.createElement("path",{d:"M82.612 157.08c9.218 0 16.69-2.74 16.69-6.12v-2.225h-33.38v2.225c0 3.38 7.472 6.12 16.69 6.12z",fill:"#C59539"}),l=a.createElement("ellipse",{cx:82.612,cy:148.735,rx:16.69,ry:6.12,fill:"#E1B155"}),u=a.createElement("path",{d:"M82.612 152.629c9.217 0 16.69-2.74 16.69-6.12v-2.225h-33.38v2.225c0 3.38 7.472 6.12 16.69 6.12z",fill:"#C59539"}),d=a.createElement("ellipse",{cx:82.612,cy:144.284,rx:16.69,ry:6.12,fill:"#E1B155"}),g=a.createElement("path",{d:"M82.612 148.179c9.218 0 16.69-2.74 16.69-6.12v-2.225h-33.38v2.225c0 3.38 7.472 6.12 16.69 6.12z",fill:"#C59539"}),m=a.createElement("ellipse",{cx:82.612,cy:139.834,rx:16.69,ry:6.12,fill:"#E1B155"}),f=a.createElement("path",{d:"M82.612 143.727c9.218 0 16.69-2.74 16.69-6.119v-2.226h-33.38v2.226c0 3.379 7.472 6.119 16.69 6.119z",fill:"#C59539"}),p=a.createElement("ellipse",{cx:82.612,cy:135.382,rx:16.69,ry:6.12,fill:"#E1B155"}),b=a.createElement("path",{d:"M82.612 139.277c9.217 0 16.69-2.739 16.69-6.119v-2.226h-33.38v2.226c0 3.38 7.472 6.119 16.69 6.119z",fill:"#C59539"}),v=a.createElement("ellipse",{cx:82.612,cy:130.932,rx:16.69,ry:6.12,fill:"#E1B155"}),h=a.createElement("path",{d:"M82.107 132.861v-.372a3.584 3.584 0 01-.7-.12 2.263 2.263 0 01-.598-.266 1.101 1.101 0 01-.385-.415l.739-.162a.866.866 0 00.346.338c.173.1.372.164.598.192v-.95l-.024-.008a4.225 4.225 0 01-1.038-.334c-.273-.14-.409-.318-.409-.535a.51.51 0 01.173-.364c.12-.114.291-.208.511-.282a2.78 2.78 0 01.787-.137v-.368h.629v.376c.273.023.5.068.684.133.189.063.336.*************.***************.236l-.724.171a.669.669 0 00-.22-.188c-.1-.063-.236-.11-.41-.142v.899c.284.063.536.133.756.21.22.074.393.164.52.269a.473.473 0 01.196.386c0 .168-.071.311-.213.428a1.51 1.51 0 01-.542.278c-.22.068-.46.112-.716.132v.377h-.63zm-.66-2.632c0 .***************.*************.119.496.176v-.771a1.387 1.387 0 00-.48.133c-.12.066-.18.143-.18.231zm1.942 1.421c0-.097-.058-.176-.173-.236a2.23 2.23 0 00-.48-.171v.809a1.31 1.31 0 00.464-.141c.126-.069.189-.156.189-.261z",fill:"#AA7A1E"}),y=a.createElement("path",{d:"M96.446 130.932c0 .426-.248.923-.905 1.461-.653.535-1.637 1.044-2.907 1.484-2.535.877-6.079 1.432-10.022 1.432-3.943 0-7.487-.555-10.022-1.432-1.27-.44-2.254-.949-2.907-1.484-.657-.538-.905-1.035-.905-1.461 0-.425.248-.922.905-1.461.653-.534 1.637-1.044 2.907-1.483 2.535-.878 6.079-1.432 10.022-1.432 3.943 0 7.487.554 10.022 1.432 1.27.439 2.254.949 2.907 1.483.657.539.905 1.036.905 1.461z",stroke:"#C59539",strokeWidth:1.261}),O=a.createElement("g",{filter:"url(#adsense-ad-blocking-recovery_svg__filter0_d_368_3243)"},a.createElement("rect",{x:84.837,y:36.913,width:184.702,height:126.107,rx:10.088,fill:"#fff"}),a.createElement("rect",{x:84.337,y:36.413,width:185.702,height:127.107,rx:10.588,stroke:"#CBD0D3"})),_=a.createElement("rect",{x:94.851,y:62.389,width:56.047,height:90.44,rx:3.783,fill:"#EE92DA"}),E=a.createElement("circle",{cx:110.137,cy:77.675,r:7.643,fill:"#B75BA2"}),k=a.createElement("path",{d:"M110.964 99.01l-11.017 11.783h45.857L129.128 92.96l-11.911 12.738-6.253-6.688z",fill:"#B75BA2"}),S=a.createElement("rect",{x:112.685,y:137.543,width:20.381,height:10.19,rx:5.095,fill:"#FFB6EF"}),j=a.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M145.804 124.322H99.946v-6.676h45.858v6.676zM145.804 132.287H99.946v-2.226h45.858v2.226z",fill:"#FFB6EF"}),T=a.createElement("path",{d:"M84.837 47c0-5.57 4.517-10.087 10.088-10.087h164.526c5.572 0 10.088 4.516 10.088 10.088v5.197H84.837v-5.197z",fill:"#EBEEF0"}),A=a.createElement("rect",{x:92.48,y:42.008,width:5.095,height:5.095,rx:2.548,fill:"#CBD0D3"}),N=a.createElement("rect",{x:100.123,y:42.008,width:5.095,height:5.095,rx:2.548,fill:"#CBD0D3"}),C=a.createElement("rect",{x:166.062,y:62.389,width:89.803,height:30.571,rx:4,fill:"#EBEEF0"}),w=a.createElement("rect",{x:166.062,y:101.151,width:66.238,height:10.19,rx:5.095,fill:"#EBEEF0"}),I=a.createElement("rect",{x:166.062,y:117.436,width:77.702,height:4,rx:2,fill:"#EBEEF0"}),M=a.createElement("rect",{x:166.062,y:127.079,width:66.238,height:4,rx:2,fill:"#EBEEF0"}),R=a.createElement("rect",{x:166.062,y:136.722,width:89.803,height:4,rx:2,fill:"#EBEEF0"}),D=a.createElement("path",{d:"M39.294 137.925l-8.822 37.176H18.5M53.156 137.925l4.41 10.711-7.56 26.465h10.712",stroke:"#000",strokeWidth:2.857,strokeLinecap:"round"}),x=a.createElement("path",{d:"M75.21 116.786c5.111.591 25.876 5.298 20.323-12.962l4.881-1.186",stroke:"#161B18",strokeWidth:2.857,strokeLinecap:"round",strokeLinejoin:"round"}),L=a.createElement("path",{d:"M21.829 117.751c.62 8.27.75 17.811-1.615 25.784",stroke:"#161B18",strokeWidth:2.857,strokeMiterlimit:10,strokeLinecap:"round"}),P=a.createElement("circle",{cx:47.485,cy:112.72,r:27.725,fill:"#77AD8C"}),G=a.createElement("path",{d:"M56.307 122.802c-4.122 5.607-11.798 7.444-17.643.763",stroke:"#161B18",strokeWidth:2.857,strokeMiterlimit:10,strokeLinecap:"round"}),Z=a.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M44.334 137.924c15.66 0 28.355-12.695 28.355-28.355 0-5.58-1.612-10.785-4.396-15.172a27.62 27.62 0 016.917 18.323c0 15.312-12.413 27.724-27.725 27.724a27.619 27.619 0 01-18.32-6.914 28.223 28.223 0 0015.17 4.394z",fill:"#59866A"}),B=a.createElement("path",{d:"M275.102 167.093c9.218 0 16.69-2.739 16.69-6.119v-2.226h-33.38v2.226c0 3.38 7.473 6.119 16.69 6.119z",fill:"#C59539"}),U=a.createElement("ellipse",{cx:275.102,cy:158.749,rx:16.69,ry:6.12,fill:"#FCC934"}),F=a.createElement("path",{d:"M277.328 161.531c9.217 0 16.69-2.74 16.69-6.12v-2.225h-33.38v2.225c0 3.38 7.472 6.12 16.69 6.12z",fill:"#C59539"}),z=a.createElement("ellipse",{cx:277.328,cy:153.186,rx:16.69,ry:6.12,fill:"#FCC934"}),V=a.createElement("path",{d:"M273.99 157.08c9.217 0 16.69-2.74 16.69-6.12v-2.225H257.3v2.225c0 3.38 7.472 6.12 16.69 6.12z",fill:"#C59539"}),H=a.createElement("ellipse",{cx:273.99,cy:148.735,rx:16.69,ry:6.12,fill:"#FCC934"}),W=a.createElement("path",{d:"M287.824 148.735c0 .426-.248.923-.906 1.461-.653.534-1.636 1.044-2.906 1.484-2.536.877-6.079 1.431-10.022 1.431-3.944 0-7.487-.554-10.022-1.431-1.271-.44-2.254-.95-2.907-1.484-.658-.538-.905-1.035-.905-1.461 0-.426.247-.923.905-1.461.653-.534 1.636-1.044 2.907-1.484 2.535-.877 6.078-1.431 10.022-1.431 3.943 0 7.486.554 10.022 1.431 1.27.44 2.253.95 2.906 1.484.658.538.906 1.035.906 1.461z",stroke:"#C59539",strokeWidth:1.261}),q=a.createElement("defs",null,a.createElement("filter",{id:"adsense-ad-blocking-recovery_svg__filter0_d_368_3243",x:83.837,y:35.913,width:190.702,height:133.107,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},a.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),a.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),a.createElement("feOffset",{dx:4,dy:5}),a.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),a.createElement("feColorMatrix",{values:"0 0 0 0 0.796078 0 0 0 0 0.815686 0 0 0 0 0.827451 0 0 0 1 0"}),a.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_368_3243"}),a.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_368_3243",result:"shape"})));t.a=function SvgAdsenseAdBlockingRecovery(e){return a.createElement("svg",r({viewBox:"0 0 343 195",fill:"none"},e),i,o,c,s,l,u,d,g,m,f,p,b,v,h,y,O,_,E,k,S,j,T,A,N,C,w,I,M,R,D,x,L,P,G,Z,B,U,F,z,V,H,W,q)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var a=n(12),r=n.n(a),i=n(9),o=function(e){r()(e,"A dates object is required.");var t=e.startDate,n=e.endDate;return r()(Object(i.w)(t),"A valid startDate is required."),r()(Object(i.w)(n),"A valid endDate is required."),{d:"".concat(t.replace(/-/g,"/"),"-").concat(n.replace(/-/g,"/"))}}},function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(17),o=n(9),c=n(236),s=n(507);function Overview(t){var n,a,r=t.metrics,l=t.currentRangeData,u=t.previousRangeData,d=t.selectedStats,g=t.handleStatsSelection,m=l.totals,f=l.headers,p=u.totals,b={smSize:2,mdSize:2,lgSize:3};return e.createElement(i.e,null,e.createElement(s.a,{className:"mdc-layout-grid__inner"},e.createElement(i.a,b,e.createElement(c.a,{stat:0,className:"googlesitekit-data-block--page-rpm googlesitekit-data-block--button-1",title:r[f[0].name],datapoint:(null==m?void 0:m.cells[0].value)||0,datapointUnit:null===(n=f[0])||void 0===n?void 0:n.currencyCode,change:Object(o.g)((null==p?void 0:p.cells[0].value)||0,(null==m?void 0:m.cells[0].value)||0),changeDataUnit:"%",context:"button",selected:0===d,handleStatSelection:g})),e.createElement(i.a,b,e.createElement(c.a,{stat:1,className:"googlesitekit-data-block--page-rpm googlesitekit-data-block--button-2",title:r[f[1].name],datapoint:(null==m?void 0:m.cells[1].value)||0,datapointUnit:null===(a=f[1])||void 0===a?void 0:a.currencyCode,change:Object(o.g)((null==p?void 0:p.cells[1].value)||0,(null==m?void 0:m.cells[1].value)||0),changeDataUnit:"%",context:"button",selected:1===d,handleStatSelection:g})),e.createElement(i.a,b,e.createElement(c.a,{stat:2,className:"googlesitekit-data-block--page-rpm googlesitekit-data-block--button-3",title:r[f[2].name],datapoint:(null==m?void 0:m.cells[2].value)||0,change:Object(o.g)((null==p?void 0:p.cells[2].value)||0,(null==m?void 0:m.cells[2].value)||0),changeDataUnit:"%",context:"button",selected:2===d,handleStatSelection:g})),e.createElement(i.a,b,e.createElement(c.a,{stat:3,className:"googlesitekit-data-block--impression googlesitekit-data-block--button-4",title:r[f[3].name],datapoint:(null==m?void 0:m.cells[3].value)||0,datapointUnit:"%",change:Object(o.g)((null==p?void 0:p.cells[3].value)||0,(null==m?void 0:m.cells[3].value)||0),changeDataUnit:"%",context:"button",selected:3===d,handleStatSelection:g}))))}Overview.propTypes={metrics:r.a.object,currentRangeData:r.a.object,previousRangeData:r.a.object,selectedStats:r.a.number.isRequired,handleStatsSelection:r.a.func.isRequired},t.a=Overview}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Stats}));var a=n(15),r=n.n(a),i=n(526),o=n.n(i),c=n(1),s=n.n(c),l=n(263),u=n(259),d=n(17),g=n(366);function Stats(t){var n=t.metrics,a=t.currentRangeData,i=t.previousRangeData,c=t.selectedStats,s=Object(u.d)(a,i,Object.values(n)[c],c+1,a.headers[c+1]),m=["#6380b8","#4bbbbb","#3c7251","#8e68cb"];var f=s.slice(1).map((function(e){return r()(e,1)[0]})),p={curveType:"function",height:270,width:"100%",chartArea:{height:"80%",width:"100%",left:60},legend:{position:"top",textStyle:{color:"#616161",fontSize:12}},hAxis:{format:"MMM d",gridlines:{color:"#fff"},textStyle:{color:"#616161",fontSize:12},ticks:o()(f).slice(1)},vAxis:{format:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.type,n=e.currencyCode;if("METRIC_CURRENCY"===t)return Object(l.c)(n);var a={METRIC_TALLY:void 0,METRIC_RATIO:"percent",METRIC_DECIMAL:"decimal",METRIC_MILLISECONDS:void 0};return a[t]}(a.headers[c+1]),gridlines:{color:"#eee"},minorGridlines:{color:"#eee"},textStyle:{color:"#616161",fontSize:12},titleTextStyle:{color:"#616161",fontSize:12,italic:!1},viewWindow:{min:0}},focusTarget:"category",crosshair:{color:"gray",opacity:.1,orientation:"vertical",trigger:"both"},tooltip:{isHtml:!0,trigger:"both"},series:{0:{color:m[c],targetAxisIndex:0},1:{color:m[c],targetAxisIndex:0,lineDashStyle:[3,3],lineWidth:1}}};return Object(u.g)(a,c+1)&&Object(u.g)(i,c+1)?p.vAxis.viewWindow.max=100:p.vAxis.viewWindow.max=void 0,e.createElement(d.e,{className:"googlesitekit-adsense-site-stats"},e.createElement(d.k,null,e.createElement(d.a,{size:12},e.createElement(g.a,{chartType:"LineChart",data:s,loadingHeight:"270px",loadingWidth:"100%",options:p}))))}Stats.propTypes={metrics:s.a.object,currentRangeData:s.a.object,previousRangeData:s.a.object,selectedStats:s.a.number.isRequired}}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return StatusMigration}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(0),s=n(2),l=n(10),u=n(3),d=n(142),g=n(32),m=n(102),f=n(31),p=n(140);function StatusMigration(){var t,n=Object(u.useSelect)((function(e){return e(f.l).getAccountID()})),a=Object(u.useSelect)((function(e){return e(f.l).getAFCClient(n)})),i=Object(u.useSelect)((function(e){return e(f.l).getCurrentSite(n)})),b=Object(u.useSelect)((function(e){return e(f.l).getAdminReauthURL()})),v=Object(u.useSelect)((function(e){return e(g.a).isNavigating()})),h=Object(u.useDispatch)(f.l),y=h.setAccountStatus,O=h.setSiteStatus,_=h.setAccountSetupComplete,E=h.setSiteSetupComplete,k=h.saveSettings,S=Object(u.useDispatch)(g.a).navigateTo;a&&i&&(t=!(a.state!==f.f||i.state!==f.f||!i.autoAdsEnabled)),Object(c.useEffect)((function(){t&&o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,y(p.k);case 2:return e.next=4,O(p.p);case 4:return e.next=6,k();case 6:case"end":return e.stop()}}),e)})))()}),[t,k,y,O]);var j=function(){var e=o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,_(!1);case 2:return e.next=4,E(!1);case 4:return e.next=6,k();case 6:S(b);case 7:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return!0===t?null:e.createElement(m.b,null,e.createElement(m.c,null,e.createElement(m.a,{size:12},void 0===t&&e.createElement(l.ProgressBar,null),!1===t&&e.createElement(d.c,{className:"googlesitekit-settings-notice-adsense-status-migration",type:d.b,notice:Object(s.__)("You need to redo setup to complete AdSense configuration","google-site-kit"),CTA:function CTA(){return e.createElement(l.SpinnerButton,{onClick:j,disabled:v,isSaving:v},Object(s.__)("Redo setup","google-site-kit"))}}))))}}).call(this,n(4))},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";n.r(t);var a,r=n(3),i=n.n(r),o=n(190),c=n.n(o),s=n(401),l=n.n(s),u=n(363),d=n.n(u),g=n(5),m=n.n(g),f=n(16),p=n.n(f),b=n(2),v=n(409),h=n(88),y=n(661),O=(n(612),n(740),n(867)),_=(n(741),n(868)),E=n(869),k=n(870),S=n(871),j=n(742),T=n(872),A=n(873),N=n(874),C=n(512),w=n(31),I=(n(743),n(875)),M=n(7),R=n(8),D=n(41),x=n(22),L=n(19),P=n(6),G=n.n(P),Z=n(12),B=n.n(Z),U=n(200),F=n(259),z=n(62);function V(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function H(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?V(Object(n),!0).forEach((function(t){G()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):V(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var W={originalUseSnippet:void 0},q={completeAccountSetup:m.a.mark((function e(){var t;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"COMPLETE_ACCOUNT_SETUP"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})),completeSiteSetup:m.a.mark((function e(){var t;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"COMPLETE_SITE_SETUP"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})),receiveOriginalUseSnippet:function(e){return B()(e,"originalUseSnippet is required."),{payload:{originalUseSnippet:e},type:"RECEIVE_ORIGINAL_USE_SNIPPET"}}},K=(a={},G()(a,"COMPLETE_ACCOUNT_SETUP",Object(r.createRegistryControl)((function(e){return p()(m.a.mark((function t(){var n;return m.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.dispatch(w.l).setAccountSetupComplete(!0);case 2:if(e.select(w.l).canSubmitChanges()){t.next=6;break}return t.next=5,e.dispatch(w.l).setAccountSetupComplete(!1);case 5:return t.abrupt("return",!1);case 6:return t.next=8,e.dispatch(w.l).submitChanges();case 8:if(n=t.sent,!n.error){t.next=14;break}return t.next=13,e.dispatch(w.l).setAccountSetupComplete(!1);case 13:return t.abrupt("return",!1);case 14:return t.abrupt("return",!0);case 15:case"end":return t.stop()}}),t)})))}))),G()(a,"COMPLETE_SITE_SETUP",Object(r.createRegistryControl)((function(e){return p()(m.a.mark((function t(){var n;return m.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.dispatch(w.l).setSiteSetupComplete(!0);case 2:if(e.select(w.l).canSubmitChanges()){t.next=6;break}return t.next=5,e.dispatch(w.l).setSiteSetupComplete(!1);case 5:return t.abrupt("return",!1);case 6:return t.next=8,e.dispatch(w.l).submitChanges();case 8:if(n=t.sent,!n.error){t.next=14;break}return t.next=13,e.dispatch(w.l).setSiteSetupComplete(!1);case 13:return t.abrupt("return",!1);case 14:return t.abrupt("return",!0);case 15:case"end":return t.stop()}}),t)})))}))),a),Y={getOriginalUseSnippet:m.a.mark((function e(){var t;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:if(t=e.sent,void 0===t.select(w.l).getOriginalUseSnippet()){e.next=6;break}return e.abrupt("return");case 6:t.select(w.l).getSettings();case 7:case"end":return e.stop()}}),e)}))},$={isDoingSaveUseSnippet:function(e){return Object.values(e.isFetchingSaveUseSnippet).some(Boolean)},getOriginalUseSnippet:function(e){return e.originalUseSnippet}};var X=Object(r.combineStores)({initialState:W,actions:q,controls:K,reducer:function(e,t){var n=t.type,a=t.payload;switch(n){case"RECEIVE_ORIGINAL_USE_SNIPPET":var r=a.originalUseSnippet;return H(H({},e),{},{originalUseSnippet:r});case"RECEIVE_GET_SETTINGS":var i=a.response.useSnippet;return H(H({},e),void 0===e.originalUseSnippet&&{originalUseSnippet:i});default:return e}},resolvers:Y,selectors:$}),J=(X.initialState,X.actions,X.controls,X.reducer,X.resolvers,X.selectors,X),Q=c.a.createModuleStore("adsense",{ownedSettingsSlugs:["accountID","clientID"],storeName:w.l,settingSlugs:["accountID","clientID","useSnippet","accountStatus","siteStatus","accountSetupComplete","siteSetupComplete","ownerID","webStoriesAdUnit","autoAdsDisabled","setupCompletedTimestamp","useAdBlockingRecoverySnippet","useAdBlockingRecoveryErrorSnippet","adBlockingRecoverySetupStatus"],validateCanSubmitChanges:function(e){var t=Object(z.e)(e)(w.l),n=t.getAccountID,a=t.getClientID,r=t.getAccountStatus,i=t.haveSettingsChanged,o=t.isDoingSubmitChanges;B()(!o(),U.a),B()(i(),U.b),B()(r(),"require an account status to be present");var c=n();B()(""===c||Object(F.e)(c),"require account ID to be either empty (if impossible to determine) or valid");var s=a();B()(""===s||Object(F.f)(s),"require client ID to be either empty (if impossible to determine) or valid")},validateIsSetupBlocked:function(e){if(e(M.a).isAdBlockerActive())throw new Error("Ad blocker detected")}}),ee=n(27),te=n.n(ee),ne=n(45),ae=n.n(ne),re=n(48),ie=n(64);function oe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function ce(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?oe(Object(n),!0).forEach((function(t){G()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):oe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var se=Object(re.a)({baseName:"getAccounts",controlCallback:function(){return ae.a.get("modules","adsense","accounts",void 0,{useCache:!1})},reducerCallback:function(e,t){return ce(ce({},e),{},{accounts:te()(t)})}}),le={accounts:void 0},ue={resetAccounts:m.a.mark((function e(){var t,n;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:return t=e.sent,n=t.dispatch,e.next=6,{payload:{},type:"RESET_ACCOUNTS"};case 6:return e.next=8,ie.a.clearErrors("getAccounts");case 8:return e.abrupt("return",n(w.l).invalidateResolutionForStoreSelector("getAccounts"));case 9:case"end":return e.stop()}}),e)}))},de={getAccounts:m.a.mark((function e(){var t;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:if(t=e.sent,!t.select(w.l).getAccounts()){e.next=6;break}return e.abrupt("return");case 6:return e.next=8,se.actions.fetchGetAccounts();case 8:case"end":return e.stop()}}),e)}))},ge=Object(r.combineStores)(se,{initialState:le,actions:ue,reducer:function(e,t){switch(t.type){case"RESET_ACCOUNTS":var n=e.savedSettings||{},a=n.accountID,r=n.clientID,i=n.accountStatus,o=n.siteStatus,c=n.accountSetupComplete,s=n.siteSetupComplete;return ce(ce({},e),{},{accounts:le.accounts,settings:ce(ce({},e.settings||{}),{},{accountID:a,clientID:r,accountStatus:i,siteStatus:o,accountSetupComplete:c,siteSetupComplete:s})});default:return e}},resolvers:de,selectors:{getAccounts:function(e){return e.accounts}}}),me=(ge.initialState,ge.actions,ge.controls,ge.reducer,ge.resolvers,ge.selectors,ge);function fe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function pe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?fe(Object(n),!0).forEach((function(t){G()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var be=Object(re.a)({baseName:"getAdUnits",controlCallback:function(e){var t=e.accountID,n=e.clientID;return ae.a.get("modules","adsense","adunits",{accountID:t,clientID:n},{useCache:!1})},reducerCallback:function(e,t,n){var a=n.accountID,r=n.clientID;return pe(pe({},e),{},{adunits:pe(pe({},e.adunits),{},G()({},"".concat(a,"::").concat(r),t))})},argsToParams:function(e,t){return{accountID:e,clientID:t}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.accountID,n=e.clientID;B()(t,"accountID is required."),B()(n,"clientID is required.")}}),ve={getAdUnits:m.a.mark((function e(t,n){var a;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0!==t&&void 0!==n){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,r.commonActions.getRegistry();case 4:if(a=e.sent,!a.select(w.l).getAdUnits(t,n)){e.next=8;break}return e.abrupt("return");case 8:return e.next=10,be.actions.fetchGetAdUnits(t,n);case 10:case"end":return e.stop()}}),e)}))},he={getAdUnits:function(e,t,n){if(void 0!==t&&void 0!==n)return e.adunits["".concat(t,"::").concat(n)]}},ye=Object(r.combineStores)(be,{initialState:{adunits:{}},actions:{},reducer:function(e,t){t.type;return e},resolvers:ve,selectors:he}),Oe=(ye.initialState,ye.actions,ye.controls,ye.reducer,ye.resolvers,ye.selectors,ye);function _e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function Ee(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_e(Object(n),!0).forEach((function(t){G()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_e(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ke=Object(re.a)({baseName:"getClients",controlCallback:function(e){var t=e.accountID;return ae.a.get("modules","adsense","clients",{accountID:t},{useCache:!1})},reducerCallback:function(e,t,n){var a=n.accountID;return Array.isArray(t)?Ee(Ee({},e),{},{clients:Ee(Ee({},e.clients),{},G()({},a,te()(t)))}):e},argsToParams:function(e){return{accountID:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.accountID;B()(t,"accountID is required.")}}),Se={resetClients:m.a.mark((function e(){var t,n;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:return t=e.sent,n=t.dispatch,e.next=6,{payload:{},type:"RESET_CLIENTS"};case 6:return e.next=8,ie.a.clearErrors("getClients");case 8:return e.abrupt("return",n(w.l).invalidateResolutionForStoreSelector("getClients"));case 9:case"end":return e.stop()}}),e)}))},je={getClients:m.a.mark((function e(t){var n;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0!==t&&Object(F.e)(t)){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,r.commonActions.getRegistry();case 4:if(n=e.sent,!n.select(w.l).getClients(t)){e.next=8;break}return e.abrupt("return");case 8:return e.next=10,ke.actions.fetchGetClients(t);case 10:case"end":return e.stop()}}),e)}))},Te={getClients:function(e,t){if(void 0!==t)return e.clients[t]},getAFCClient:Object(r.createRegistrySelector)((function(e){return function(t,n){if(void 0!==n){var a=e(w.l).getClients(n);if(void 0!==a){var r=a.filter((function(e){return"AFC"===e.productCode}));return r.length?r[0]:null}}}}))},Ae=Object(r.combineStores)(ke,{initialState:{clients:{}},actions:Se,reducer:function(e,t){switch(t.type){case"RESET_CLIENTS":var n=e.savedSettings||{},a=n.clientID,r=n.accountStatus,i=n.siteStatus,o=n.accountSetupComplete,c=n.siteSetupComplete;return Ee(Ee({},e),{},{clients:Ne.clients,settings:Ee(Ee({},e.settings||{}),{},{clientID:a,accountStatus:r,siteStatus:i,accountSetupComplete:o,siteSetupComplete:c})});default:return e}},resolvers:je,selectors:Te}),Ne=Ae.initialState,Ce=(Ae.actions,Ae.controls,Ae.reducer,Ae.resolvers,Ae.selectors,Ae),we=n(14),Ie=n(9),Me=n(178),Re=["ACTIVE_VIEW_MEASURABILITY","ACTIVE_VIEW_TIME","ACTIVE_VIEW_VIEWABILITY","AD_REQUESTS_COVERAGE","AD_REQUESTS_CTR","AD_REQUESTS_RPM","AD_REQUESTS_SPAM_RATIO","AD_REQUESTS","ADS_PER_IMPRESSION","CLICKS_SPAM_RATIO","CLICKS","COST_PER_CLICK","ESTIMATED_EARNINGS","IMPRESSIONS_CTR","IMPRESSIONS_RPM","IMPRESSIONS_SPAM_RATIO","IMPRESSIONS","INDIVIDUAL_AD_IMPRESSIONS_CTR","INDIVIDUAL_AD_IMPRESSIONS_RPM","INDIVIDUAL_AD_IMPRESSIONS_SPAM_RATIO","INDIVIDUAL_AD_IMPRESSIONS","MATCHED_AD_REQUESTS_CTR","MATCHED_AD_REQUESTS_RPM","MATCHED_AD_REQUESTS_SPAM_RATIO","MATCHED_AD_REQUESTS","METRIC_UNSPECIFIED","PAGE_VIEWS_CTR","PAGE_VIEWS_RPM","PAGE_VIEWS_SPAM_RATIO","PAGE_VIEWS","TOTAL_EARNINGS","TOTAL_IMPRESSIONS","WEBSEARCH_RESULT_PAGES"],De=["ACCOUNT_NAME","AD_CLIENT_ID","AD_FORMAT_CODE","AD_FORMAT_NAME","AD_PLACEMENT_CODE","AD_PLACEMENT_NAME","AD_UNIT_ID","AD_UNIT_NAME","AD_UNIT_SIZE_CODE","AD_UNIT_SIZE_NAME","BID_TYPE_CODE","BID_TYPE_NAME","BUYER_NETWORK_ID","BUYER_NETWORK_NAME","CONTENT_PLATFORM_CODE","CONTENT_PLATFORM_NAME","COUNTRY_CODE","COUNTRY_NAME","CREATIVE_SIZE_CODE","CREATIVE_SIZE_NAME","CUSTOM_CHANNEL_ID","CUSTOM_CHANNEL_NAME","CUSTOM_SEARCH_STYLE_ID","CUSTOM_SEARCH_STYLE_NAME","DATE","DIMENSION_UNSPECIFIED","DOMAIN_CODE","DOMAIN_NAME","DOMAIN_REGISTRANT","MONTH","OWNED_SITE_DOMAIN_NAME","OWNED_SITE_ID","PLATFORM_TYPE_CODE","PLATFORM_TYPE_NAME","PRODUCT_CODE","PRODUCT_NAME","REQUESTED_AD_TYPE_CODE","REQUESTED_AD_TYPE_NAME","SERVED_AD_TYPE_CODE","SERVED_AD_TYPE_NAME","TARGETING_TYPE_CODE","TARGETING_TYPE_NAME","URL_CHANNEL_ID","URL_CHANNEL_NAME","WEBSEARCH_QUERY_STRING","WEEK"];function xe(e){var t=Object(we.castArray)(e);B()(t.length,"at least one metric is required.");var n=t.filter((function(e){return!Re.includes(e)}));B()(0===n.length,"invalid AdSense metrics requested: ".concat(n.toString()))}function Le(e){var t=Object(we.castArray)(e);B()(t.length,"at least one dimension is required.");var n=t.filter((function(e){return!De.includes(e)}));B()(0===n.length,"invalid AdSense dimensions requested: ".concat(n.toString()))}function Pe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function Ge(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Pe(Object(n),!0).forEach((function(t){G()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Ze=Object(re.a)({baseName:"getReport",controlCallback:function(e){var t=e.options;return ae.a.get("modules","adsense","report",t)},reducerCallback:function(e,t,n){var a=n.options;return Ge(Ge({},e),{},{reports:Ge(Ge({},e.reports),{},G()({},Object(Ie.H)(a),t))})},argsToParams:function(e){return{options:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.options;B()(Object(we.isPlainObject)(t),"options must be an object."),B()(Object(Me.a)(t),"Either date range or start/end dates must be provided for AdSense report.");var n=t.orderby,a=t.metrics,r=t.dimensions;B()(Object(Me.d)(a),"Metrics for an AdSense report must be either a string or an array of strings."),xe(a),r&&(B()(Object(Me.d)(r),"Dimensions for an AdSense report must be either a string or an array of strings."),Le(r)),n&&B()(Object(Me.b)(n),'Orders for an AdSense report must be either an object or an array of objects where each object should have "fieldName" and "sortOrder" properties.')}}),Be={getReport:m.a.mark((function e(){var t,n,a=arguments;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=a.length>0&&void 0!==a[0]?a[0]:{},e.next=3,r.commonActions.getRegistry();case 3:if(n=e.sent,!n.select(w.l).getReport(t)){e.next=7;break}return e.abrupt("return");case 7:return e.next=9,Ze.actions.fetchGetReport(t);case 9:case"end":return e.stop()}}),e)}))},Ue={getReport:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.reports;return n[Object(Ie.H)(t)]}},Fe=Object(r.combineStores)(Ze,{initialState:{reports:{}},resolvers:Be,selectors:Ue}),ze=(Fe.initialState,Fe.actions,Fe.controls,Fe.reducer,Fe.resolvers,Fe.selectors,Fe),Ve=n(568),He=Object(Ve.a)({storeName:w.l,tagMatchers:[/google_ad_client: ?["|'](.*?)["|']/,/<(?:script|amp-auto-ads) [^>]*data-ad-client="([^"]+)"/,/<(?:script|amp-auto-ads)[^>]*src="[^"]*\?client=(ca-pub-[^"]+)"[^>]*>/],isValidTag:F.f}),We={selectors:{getAdBlockerWarningMessage:Object(r.createRegistrySelector)((function(e){return function(){var t=e(M.a).isAdBlockerActive();if(void 0!==t)return t?e(L.a).isModuleConnected("adsense")?Object(b.__)("To get the latest AdSense data you will need to disable your Ad blocker","google-site-kit"):Object(b.__)("To set up AdSense you will need to disable your Ad blocker","google-site-kit"):null}}))}},qe=n(157),Ke=n(13),Ye=n(601);function $e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}var Xe={selectors:{getServiceURL:Object(r.createRegistrySelector)((function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=n.path,r=n.query,i="https://www.google.com/adsense/new/u/0";if(a){var o="/".concat(a.replace(/^\//,""));i="".concat(i).concat(o)}r&&(i=Object(qe.a)(i,r));var c=e(M.a).getAccountChooserURL(i);if(void 0!==c)return c}})),getServiceCreateAccountURL:Object(r.createRegistrySelector)((function(e){return function(){var t=e(Ke.c).getReferenceSiteURL(),n={source:"site-kit",utm_source:"site-kit",utm_medium:"wordpress_signup"};return void 0!==t&&(n.url=Object(Ye.a)(t)),Object(qe.a)("https://www.google.com/adsense/signup",n)}})),getServiceAccountURL:Object(r.createRegistrySelector)((function(e){return function(){var t=e(w.l).getAccountID();if(void 0!==t){return e(w.l).getServiceURL({accountID:t,query:{source:"site-kit"}})}}})),getServiceReportURL:Object(r.createRegistrySelector)((function(e){return function(t,n){var a=e(w.l).getAccountID();if(void 0!==a){var r=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$e(Object(n),!0).forEach((function(t){G()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$e(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},n),i=e(Ke.c).getReferenceSiteURL(),o=i&&Object(Ye.a)(i);o&&(r.dd="1YsiteY1Y".concat(o,"Y").concat(o));var c="".concat(a,"/reporting");return e(w.l).getServiceURL({path:c,query:r})}}})),getServiceAccountManageSiteURL:Object(r.createRegistrySelector)((function(e){return function(){var t=e(w.l).getAccountID(),n=e(Ke.c).getReferenceSiteURL();if(void 0!==t&&void 0!==n){var a="".concat(t,"/sites/my-sites"),r={source:"site-kit",url:Object(Ye.a)(n)||n};return e(w.l).getServiceURL({path:a,query:r})}}})),getServiceAccountManageSitesURL:Object(r.createRegistrySelector)((function(e){return function(){var t=e(w.l).getAccountID();if(void 0!==t){var n="".concat(t,"/sites/my-sites");return e(w.l).getServiceURL({path:n,query:{source:"site-kit"}})}}})),getServiceAccountSiteAdsPreviewURL:Object(r.createRegistrySelector)((function(e){return function(){var t=e(w.l).getAccountID(),n=e(Ke.c).getReferenceSiteURL();if(void 0!==t&&void 0!==n){var a="".concat(t,"/myads/sites/preview"),r={source:"site-kit",url:Object(Ye.a)(n)||n};return e(w.l).getServiceURL({path:a,query:r})}}})),getDetailsLinkURL:Object(r.createRegistrySelector)((function(e){return function(){return e(w.l).getServiceAccountManageSitesURL()}}))}};function Je(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function Qe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Je(Object(n),!0).forEach((function(t){G()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Je(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var et=Object(re.a)({baseName:"getSites",controlCallback:function(e){var t=e.accountID;return ae.a.get("modules","adsense","sites",{accountID:t},{useCache:!1})},reducerCallback:function(e,t,n){var a=n.accountID;return Array.isArray(t)?Qe(Qe({},e),{},{sites:Qe(Qe({},e.sites),{},G()({},a,te()(t)))}):e},argsToParams:function(e){return{accountID:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.accountID;B()(t,"accountID is required.")}}),tt={resetSites:m.a.mark((function e(){var t,n;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:return t=e.sent,n=t.dispatch,e.next=6,{payload:{},type:"RESET_SITES"};case 6:return e.next=8,ie.a.clearErrors("getSites");case 8:return e.abrupt("return",n(w.l).invalidateResolutionForStoreSelector("getSites"));case 9:case"end":return e.stop()}}),e)}))},nt={getSites:m.a.mark((function e(t){var n;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0!==t&&Object(F.e)(t)){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,r.commonActions.getRegistry();case 4:if(n=e.sent,!n.select(w.l).getSites(t)){e.next=8;break}return e.abrupt("return");case 8:return e.next=10,et.actions.fetchGetSites(t);case 10:case"end":return e.stop()}}),e)}))},at={getSites:function(e,t){if(void 0!==t)return e.sites[t]},getSite:Object(r.createRegistrySelector)((function(e){return function(t,n,a){return function(e,t){if(void 0!==e&&void 0!==t&&Array.isArray(e)){var n=e.filter((function(e){return!!e.state})),a=n.find((function(e){return e.domain===t.toLowerCase()}));return a||(n.find((function(e){return new RegExp("\\.".concat(Object(we.escapeRegExp)(e.domain),"$"),"i").test(t)}))||null)}}(e(w.l).getSites(n),a)}})),getCurrentSite:Object(r.createRegistrySelector)((function(e){return function(t,n){var a=e(Ke.c).getReferenceSiteURL(),r=new URL(a);return e(w.l).getSite(n,r.hostname)}}))},rt=Object(r.combineStores)(et,{initialState:{sites:{}},actions:tt,reducer:function(e,t){switch(t.type){case"RESET_SITES":var n=e.savedSettings||{},a=n.siteID,r=n.accountStatus,i=n.siteStatus,o=n.accountSetupComplete,c=n.siteSetupComplete;return Qe(Qe({},e),{},{sites:it.sites,settings:Qe(Qe({},e.settings||{}),{},{siteID:a,accountStatus:r,siteStatus:i,accountSetupComplete:o,siteSetupComplete:c})});default:return e}},resolvers:nt,selectors:at}),it=rt.initialState,ot=(rt.actions,rt.controls,rt.reducer,rt.resolvers,rt.selectors,rt),ct=n(369),st=[/<script async src="https:\/\/fundingchoicesmessages\.google\.com\/i\/(.*?)\?ers=/],lt=n(176);function ut(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function dt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ut(Object(n),!0).forEach((function(t){G()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ut(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function gt(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return mt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return mt(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,r=function(){};return{s:r,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function mt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var ft,pt,bt,vt=Object(re.a)({baseName:"syncAdBlockingRecoveryTags",controlCallback:function(){return ae.a.set("modules","adsense","sync-ad-blocking-recovery-tags")}}),ht={existingAdBlockingRecoveryTag:void 0},yt={fetchGetExistingAdBlockingRecoveryTag:function(){return{payload:{},type:"FETCH_GET_EXISTING_AD_BLOCKING_RECOVERY_TAG"}},receiveGetExistingAdBlockingRecoveryTag:function(e){return B()(null===e||"string"==typeof e,"existingAdBlockingRecoveryTag must be a tag string or null."),{payload:{existingAdBlockingRecoveryTag:Object(F.e)(e)?e:null},type:"RECEIVE_GET_EXISTING_AD_BLOCKING_RECOVERY_TAG"}},syncAdBlockingRecoveryTags:function(){return vt.actions.fetchSyncAdBlockingRecoveryTags()}},Ot=G()({},"FETCH_GET_EXISTING_AD_BLOCKING_RECOVERY_TAG",Object(r.createRegistryControl)((function(e){return p()(m.a.mark((function t(){var n,a,r,i,o,c,s,l,u;return m.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.select(Ke.c).getHomeURL(),t.next=3,Object(ct.b)({homeURL:n});case 3:a=t.sent,r=e.resolveSelect(Ke.c),i=r.getHTMLForURL,o=gt(a),t.prev=6,o.s();case 8:if((c=o.n()).done){t.next=18;break}return s=c.value,t.next=12,i(s);case 12:if(l=t.sent,!(u=Object(ct.a)(l,st))){t.next=16;break}return t.abrupt("return",u);case 16:t.next=8;break;case 18:t.next=23;break;case 20:t.prev=20,t.t0=t.catch(6),o.e(t.t0);case 23:return t.prev=23,o.f(),t.finish(23);case 26:return t.abrupt("return",null);case 27:case"end":return t.stop()}}),t,null,[[6,20,23,26]])})))}))),_t=Object(lt.a)((function(e,t){var n=t.type,a=t.payload;switch(n){case"RECEIVE_GET_EXISTING_AD_BLOCKING_RECOVERY_TAG":var r=a.existingAdBlockingRecoveryTag;return dt(dt({},e),{},{existingAdBlockingRecoveryTag:r});default:return e}})),Et={getExistingAdBlockingRecoveryTag:m.a.mark((function e(){var t,n;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:if(t=e.sent,void 0!==t.select(w.l).getExistingAdBlockingRecoveryTag()){e.next=10;break}return e.next=7,yt.fetchGetExistingAdBlockingRecoveryTag();case 7:return n=e.sent,e.next=10,yt.receiveGetExistingAdBlockingRecoveryTag(n);case 10:case"end":return e.stop()}}),e)}))},kt={getExistingAdBlockingRecoveryTag:function(e){return e.existingAdBlockingRecoveryTag},hasExistingAdBlockingRecoveryTag:Object(r.createRegistrySelector)((function(e){return function(){var t=e(w.l).getExistingAdBlockingRecoveryTag();if(void 0!==t)return!!t}}))},St=Object(r.combineStores)(vt,{initialState:ht,actions:yt,reducer:_t,controls:Ot,resolvers:Et,selectors:kt}),jt=Object(r.combineStores)(Q,me,Oe,Ce,ze,He,J,We,Xe,ot,St),Tt=(jt.initialState,jt.actions,jt.controls,jt.reducer,jt.resolvers,jt.selectors,{"adsense-abr-success-notification":{Component:j.a,priority:10,areaSlug:D.b.BANNERS_BELOW_NAV,viewContexts:[x.n],checkRequirements:(ft=p()(m.a.mark((function e(t){var n,a,r,i;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.select,a=t.resolveSelect,"ad_blocking_recovery_setup_success"===Object(v.a)(location.href,"notification")){e.next=4;break}return e.abrupt("return",!1);case 4:return r=a(L.a),i=r.isModuleConnected,e.next=7,i("adsense");case 7:if(e.sent){e.next=9;break}return e.abrupt("return",!1);case 9:return e.next=11,a(w.l).getSettings();case 11:if(n(w.l).getAdBlockingRecoverySetupStatus()!==w.j.SETUP_CONFIRMED){e.next=14;break}return e.abrupt("return",!0);case 14:return e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e)}))),function(e){return ft.apply(this,arguments)})}});i.a.registerStore(w.l,jt),c.a.registerModule("adsense",{storeName:w.l,SettingsEditComponent:O.a,SettingsViewComponent:E.a,SettingsSetupIncompleteComponent:_.a,SetupComponent:y.b,Icon:C.a,features:[Object(b.__)("Intelligent, automatic ad placement will be disabled","google-site-kit"),Object(b.__)("You will miss out on revenue from ads placed on your site","google-site-kit"),Object(b.__)("You will lose access to AdSense insights through Site Kit","google-site-kit")],checkRequirements:(pt=p()(m.a.mark((function e(t){var n;return m.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.resolveSelect(M.a).isAdBlockerActive();case 2:if(e.sent){e.next=5;break}return e.abrupt("return");case 5:throw n=t.select(w.l).getAdBlockerWarningMessage(),{code:M.c,message:n,data:null};case 7:case"end":return e.stop()}}),e)}))),function(e){return pt.apply(this,arguments)})}),(bt=l.a).registerWidget("adBlockingRecovery",{Component:S.a,width:bt.WIDGET_WIDTHS.FULL,priority:1,wrapWidget:!1,modules:["adsense"]},[h.AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY]),bt.registerWidget(M.f,{Component:I.a,width:bt.WIDGET_WIDTHS.QUARTER,priority:1,wrapWidget:!1,modules:["adsense","analytics-4"],isActive:function(e){var t=!e(M.a).isAuthenticated();if(!e(M.a).isKeyMetricActive(M.f))return!1;var n=e(R.r).getAdSenseLinked();return!(t&&!n)}},[h.AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY]),bt.registerWidget("adBlockerWarning",{Component:k.a,width:bt.WIDGET_WIDTHS.FULL,priority:1,wrapWidget:!1,modules:["adsense"]},[h.AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY]),bt.registerWidget("adsenseModuleOverview",{Component:N.a,width:bt.WIDGET_WIDTHS.FULL,priority:2,wrapWidget:!1,modules:["adsense"]},[h.AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY]),bt.registerWidget("adsenseConnectCTA",{Component:T.a,width:[bt.WIDGET_WIDTHS.FULL],priority:2,wrapWidget:!1,modules:["adsense"]},[h.AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY]),bt.registerWidget("adsenseTopEarningPagesGA4",{Component:A.a,width:[bt.WIDGET_WIDTHS.HALF,bt.WIDGET_WIDTHS.FULL],priority:3,wrapWidget:!1,modules:["adsense","analytics-4"]},[h.AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY]),function(e){for(var t in Tt)e.registerNotification(t,Tt[t])}(d.a)}],[[1291,1,0]]]);