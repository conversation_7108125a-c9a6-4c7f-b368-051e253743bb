(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[29],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(59),a=n(39),i=n(57);function o(t,n){var o,c=Object(r.a)(n),l=t.activeModules,s=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,f=void 0===d?[]:d,g=t.isAuthenticated,p=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(a.b,"]"))),!o){o=!0;var r=(null==f?void 0:f.length)?f.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:s,plugin_version:p||"",enabled_features:Array.from(i.a).join(","),active_modules:l.join(","),authenticated:g?"1":"0",user_properties:{user_roles:r,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(a.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(a.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(a.a)}}}}}).call(this,n(28))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var r=n(5),a=n.n(r),i=n(6),o=n.n(i),c=n(16),l=n.n(c),s=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function f(e,t,n,r){var i=Object(s.a)(t);return function(){var t=l()(a.a.mark((function t(o,c,l,s){var u;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:l,value:s},t.abrupt("return",new Promise((function(e){var t,n,a=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),l=function(){clearTimeout(a),e()};i("event",c,d(d({},u),{},{event_callback:l})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&l()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,a){return t.apply(this,arguments)}}()}},102:function(e,t,n){"use strict";var r=n(123);n.d(t,"a",(function(){return r.a}));var a=n(124);n.d(t,"c",(function(){return a.a}));var i=n(125);n.d(t,"b",(function(){return i.a}))},104:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return r.createElement("svg",a({viewBox:"0 0 14 14",fill:"none"},e),i)}},105:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),l=n.n(c),s=n(11),u=n.n(s);function VisuallyHidden(t){var n=t.className,r=t.children,i=o()(t,["className","children"]);return r?e.createElement("span",a()({},i,{className:u()("screen-reader-text",n)}),r):null}VisuallyHidden.propTypes={className:l.a.string,children:l.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(4))},107:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var r=n(21),a=n.n(r),i=n(152),o=n.n(i),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(2),f=n(10),g=n(154),p=n(104);function TourTooltip(t){var n=t.backProps,r=t.closeProps,c=t.index,s=t.primaryProps,u=t.size,m=t.step,h=t.tooltipProps,v=u>1?Object(g.a)(u):[],b=function(e){return l()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",a()({className:l()("googlesitekit-tour-tooltip",m.className)},h),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},m.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},m.content)),e.createElement(i.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},v.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:b(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(f.Button,a()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),m.cta,s.title&&e.createElement(f.Button,a()({className:"googlesitekit-tooltip-button",text:!0},s),s.title))),e.createElement(f.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(p.a,{width:"14",height:"14"}),onClick:r.onClick,"aria-label":Object(d.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(4))},109:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(208),l=n(38),s=n(2),u=n(10),d=n(58);function ModalDialog(t){var n=t.className,r=void 0===n?"":n,a=t.dialogActive,i=void 0!==a&&a,f=t.handleDialog,g=void 0===f?null:f,p=t.onOpen,m=void 0===p?null:p,h=t.onClose,v=void 0===h?null:h,b=t.title,k=void 0===b?null:b,E=t.provides,_=t.handleConfirm,y=t.subtitle,O=t.confirmButton,S=void 0===O?null:O,j=t.dependentModules,w=t.danger,C=void 0!==w&&w,N=t.inProgress,R=void 0!==N&&N,x=t.small,z=void 0!==x&&x,A=t.medium,T=void 0!==A&&A,L=t.buttonLink,M=void 0===L?null:L,D=Object(c.a)(ModalDialog),P="googlesitekit-dialog-description-".concat(D),I=!(!E||!E.length);return e.createElement(u.Dialog,{open:i,onOpen:m,onClose:v,"aria-describedby":I?P:void 0,tabIndex:"-1",className:o()(r,{"googlesitekit-dialog-sm":z,"googlesitekit-dialog-md":T})},e.createElement(u.DialogTitle,null,C&&e.createElement(d.a,{width:28,height:28}),k),y?e.createElement("p",{className:"mdc-dialog__lead"},y):[],e.createElement(u.DialogContent,null,I&&e.createElement("section",{id:P,className:"mdc-dialog__provides"},e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},E.map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t},e.createElement("span",{className:"mdc-list-item__text"},t))})))),j&&e.createElement("p",{className:"mdc-dialog__dependencies"},Object(l.a)(Object(s.sprintf)(
/* translators: %s is replaced with the dependent modules. */
Object(s.__)("<strong>Note:</strong> %s","google-site-kit"),j),{strong:e.createElement("strong",null)}))),e.createElement(u.DialogFooter,null,e.createElement(u.Button,{className:"mdc-dialog__cancel-button",tertiary:!0,onClick:g,disabled:R},Object(s.__)("Cancel","google-site-kit")),M?e.createElement(u.Button,{href:M,onClick:_,target:"_blank",danger:C},S):e.createElement(u.SpinnerButton,{onClick:_,danger:C,disabled:R,isSaving:R},S||Object(s.__)("Disconnect","google-site-kit"))))}ModalDialog.displayName="Dialog",ModalDialog.propTypes={className:a.a.string,dialogActive:a.a.bool,handleDialog:a.a.func,handleConfirm:a.a.func.isRequired,onOpen:a.a.func,onClose:a.a.func,title:a.a.string,confirmButton:a.a.string,danger:a.a.bool,small:a.a.bool,medium:a.a.bool,buttonLink:a.a.string},t.a=ModalDialog}).call(this,n(4))},111:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(1),a=n.n(r),i=n(0),o=n(9),c=n(54);function Description(t){var n=t.className,r=void 0===n?"googlesitekit-publisher-win__desc":n,a=t.text,l=t.learnMoreLink,s=t.errorText,u=t.children;return e.createElement(i.Fragment,null,e.createElement("div",{className:r},e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.F)(a,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",l)),s&&e.createElement(c.a,{message:s}),u)}Description.propTypes={className:a.a.string,text:a.a.string,learnMoreLink:a.a.node,errorText:a.a.string,children:a.a.node}}).call(this,n(4))},112:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(392),a=function(e,t,n){Object(r.a)((function(n){return e.includes(n.keyCode)&&t.current.contains(n.target)}),n)}},1179:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardSplashApp}));var r=n(3),a=n(13),i=n(7),o=n(1180),c=n(1189),l=n(1191);function DashboardSplashApp(){var t=Object(r.useSelect)((function(e){return e(a.c).isUsingProxy()})),n=Object(r.useSelect)((function(e){return e(i.a).hasCapability(i.H)}));return!0===t?n?e.createElement(o.a,null):e.createElement(c.a,null):!1===t?e.createElement(l.a,null):null}}).call(this,n(4))},1180:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupUsingProxyWithSignIn}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(820),l=n.n(c),s=n(0),u=n(38),d=n(2),f=n(157),g=n(409),p=n(3),m=n(10),h=n(1181),v=n(1182),b=n(9),k=n(234),E=n(694),_=n(192),y=n(197),O=n(218),S=n(1183),j=n(13),w=n(7),C=n(32),N=n(19),R=n(29),x=n(17),z=n(516),A=n(235),T=n(1187),L=n(18),M=n(20),D=n(37);function SetupUsingProxyWithSignIn(){var t,n,r=Object(L.a)(),i=Object(p.useSelect)((function(e){return e(N.a).isModuleAvailable("analytics-4")})),c=Object(p.useSelect)((function(e){return e(N.a).isModuleActive("analytics-4")})),P=Object(p.useSelect)((function(e){return e(R.a).getValue(z.b,z.a)})),I=Object(p.useSelect)((function(e){return e(j.c).hasConnectedAdmins()})),B=Object(p.useSelect)((function(e){return e(j.c).isResettable()})),F=Object(p.useSelect)((function(e){return e(j.c).getReferenceSiteURL()})),H=Object(p.useSelect)((function(e){return e(j.c).getProxySetupURL()})),V=Object(p.useSelect)((function(e){return Object(b.K)(e(j.c).getHomeURL())})),W=Object(p.useSelect)((function(e){return e(j.c).isConnected()})),U=Object(p.useSelect)((function(e){return e(j.c).hasMultipleAdmins()})),G=Object(p.useSelect)((function(e){return e(j.c).getDocumentationLinkURL("already-configured")})),K=Object(p.useSelect)((function(e){return e(w.a).getDisconnectedReason()})),q=Object(p.useSelect)((function(e){return Object(b.K)(e(w.a).getConnectedProxyURL())})),X=Object(p.useSelect)((function(e){return e(j.c).getAdminURL("googlesitekit-dashboard")})),Y=Object(p.useSelect)((function(e){return e(j.c).getDocumentationLinkURL("url-has-changed")})),$=Object(p.useSelect)((function(e){var t;return!!(null===(t=e(w.a).getViewableModules())||void 0===t?void 0:t.length)})),J=Object(p.useDispatch)(w.a).dismissItem,Z=Object(p.useDispatch)(C.a).navigateTo,Q=Object(p.useDispatch)(N.a).activateModule,ee=Object(s.useCallback)((function(){Promise.all([J(z.c),Object(b.I)(r,"skip_setup_to_viewonly")]).finally((function(){Z(X)}))}),[X,J,Z,r]),te=Object(s.useCallback)(function(){var e=o()(a.a.mark((function e(t){var n,i,o,c;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.preventDefault(),!P){e.next=11;break}return e.next=4,Q("analytics-4");case 4:if(i=e.sent,o=i.error,c=i.response,o){e.next=11;break}return e.next=10,Object(b.I)("".concat(r,"_setup"),"start_setup_with_analytics");case 10:n=c.moduleReauthURL;case 11:if(!H){e.next=14;break}return e.next=14,Promise.all([Object(D.f)("start_user_setup",!0),Object(b.I)("".concat(r,"_setup"),"start_user_setup","proxy")]);case 14:if(!H||W){e.next=17;break}return e.next=17,Promise.all([Object(D.f)("start_site_setup",!0),Object(b.I)("".concat(r,"_setup"),"start_site_setup","proxy")]);case 17:Z(n&&H?Object(f.a)(H,{redirect:n}):H);case 18:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[H,Z,W,Q,P,r]),ne=!1,re={smSize:4,mdSize:8,lgSize:6};c||(re={smSize:4,mdSize:8,lgSize:8});var ae=null;return"revoked"===Object(g.a)(location.href,"googlesitekit_context")?(t=Object(d.sprintf)(
/* translators: %s: is the site's hostname. (e.g. example.com) */
Object(d.__)("You revoked access to Site Kit for %s","google-site-kit"),l.a.toUnicode(new URL(F).hostname)),n=Object(d.__)("Site Kit will no longer have access to your account. If you’d like to reconnect Site Kit, click “Sign in with Google“ below to generate new credentials.","google-site-kit")):w.b===K?(t=Object(d.__)("Reconnect Site Kit","google-site-kit"),n=Object(d.__)("Looks like the URL of your site has changed. In order to continue using Site Kit, you’ll need to reconnect, so that your plugin settings are updated with the new URL.","google-site-kit"),ae=Y):I?(t=Object(d.__)("Connect your Google account to Site Kit","google-site-kit"),n=Object(d.__)("Site Kit has already been configured by another admin of this site. To use Site Kit as well, sign in with your Google account which has access to Google services for this site (e.g. Google Analytics). Once you complete the 3 setup steps, you’ll see stats from all activated Google services.","google-site-kit"),ne=!0):(t=Object(d.__)("Set up Site Kit","google-site-kit"),n=Object(d.__)("Get insights on how people find your site, as well as how to improve and monetize your site’s content, directly in your WordPress dashboard","google-site-kit")),e.createElement(s.Fragment,null,e.createElement(k.a,null,e.createElement(A.a,null)),"reset_success"===Object(g.a)(location.href,"notification")&&e.createElement(y.a,{id:"reset_success",title:Object(d.__)("Site Kit by Google was successfully reset.","google-site-kit"),isDismissible:!1}),e.createElement("div",{className:"googlesitekit-setup"},e.createElement(x.e,null,e.createElement(x.k,null,e.createElement(x.a,{size:12},e.createElement(_.a,{rounded:!0},e.createElement("section",{className:"googlesitekit-setup__splash"},e.createElement(x.e,null,e.createElement(x.k,{className:"googlesitekit-setup__content"},e.createElement(x.a,{smSize:4,mdSize:8,lgSize:c?6:4,className:"googlesitekit-setup__icon"},c&&e.createElement(h.a,{width:"570",height:"336"}),!c&&e.createElement(v.a,{height:"167",width:"175"})),e.createElement(x.a,re,e.createElement("h1",{className:"googlesitekit-setup__title"},t),e.createElement("p",{className:"googlesitekit-setup__description"},!ne&&n,ne&&Object(u.a)(Object(d.sprintf)(
/* translators: 1: The description. 2: The learn more link. */
Object(d.__)("%1$s <Link>%2$s</Link>","google-site-kit"),n,Object(d.__)("Learn more","google-site-kit")),{Link:e.createElement(M.a,{href:G,external:!0})})),ae&&e.createElement(M.a,{href:ae,external:!0},Object(d.__)("Get help","google-site-kit")),w.b===K&&q!==V&&e.createElement("p",null,Object(d.sprintf)(
/* translators: %s: Previous Connected Proxy URL */
Object(d.__)("— Old URL: %s","google-site-kit"),q),e.createElement("br",null),Object(d.sprintf)(
/* translators: %s: Connected Proxy URL */
Object(d.__)("— New URL: %s","google-site-kit"),V)),i&&!c&&e.createElement(T.a,null),e.createElement(S.a,null,(function(t){var n=t.complete,r=t.inProgressFeedback,a=t.ctaFeedback;return e.createElement(s.Fragment,null,a,e.createElement(O.a,null),e.createElement("div",{className:"googlesitekit-start-setup-wrap"},e.createElement(m.Button,{className:"googlesitekit-start-setup",href:H,onClick:te,disabled:!n},Object(d._x)("Sign in with Google","Prompt to authenticate Site Kit with Google Account","google-site-kit")),r,U&&I&&$&&n&&e.createElement(m.Button,{tertiary:!0,onClick:ee},Object(d.__)("Skip sign-in and view limited dashboard","google-site-kit")),!I&&B&&n&&e.createElement(E.a,null)))}))))))))))))}}).call(this,n(4))},1181:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M170.78 167.21c11.36-5.5 21.2 1.59 22.73 7",stroke:"#4285F4",strokeLinejoin:"round",strokeWidth:7}),o=r.createElement("ellipse",{cx:292.73,cy:307.44,rx:252.51,ry:10.01,fill:"#F1F3F4"}),c=r.createElement("path",{d:"M93.23 212.44c-6 6.78-17 9.12-25.62 8.24-14.77-1.52-23-11-28.38-24",stroke:"#4285F4",strokeLinejoin:"round",strokeWidth:7}),l=r.createElement("path",{d:"M93.23 212.44a24.66 24.66 0 01-11.81 7",stroke:"#1A73E8",strokeLinejoin:"round",strokeWidth:7}),s=r.createElement("path",{d:"M125.33 206.44L112.12 242a60 60 0 00.12 42.15l11.09 21.34h-14.22M125.33 206.44l-9.55 25.67M146.34 216.44v89h13.23M125.33 206.44a72.1 72.1 0 00-5.25 13.5M146.34 216.44v7",stroke:"#1967D2",strokeLinejoin:"round",strokeWidth:7}),u=r.createElement("path",{d:"M142.65 219.64a61.5 61.5 0 01-80.49-33l113.46-47.52a61.51 61.51 0 01-32.97 80.52z",fill:"#1A73E8"}),d=r.createElement("path",{d:"M146.93 202c7.74-4.34 12.25-12.92 12.66-22.39l-43.54 17.79",stroke:"#FFF",strokeMiterlimit:10,strokeWidth:5}),f=r.createElement("path",{d:"M385.07 137.93c18 7.48 43.08 2.83 56-12.14 11.22-13 11.51-31.12 6.34-46.75M322.39 152.05c-24.41 12-41.12 72.46-24.79 98M365.34 177.13c2.77 13.29 5.23 29.83 9.9 45.42",stroke:"#FBBC04",strokeLinejoin:"round",strokeWidth:7}),g=r.createElement("path",{d:"M366 239.9l3.21 28.92a29.93 29.93 0 01-3.14 17L356 305.44h14.22M341.55 234.9v70.54h-13.23",stroke:"#F29900",strokeLinejoin:"round",strokeWidth:7}),p=r.createElement("path",{stroke:"#EA8600",strokeLinejoin:"round",strokeWidth:7,d:"M366.02 239.9l1.16 10.44M341.55 234.9v15.44"}),m=r.createElement("path",{fill:"#F9AB00",d:"M396.25 159.92l3.18-81.94-81.94-3.17-3.18 81.94-3.18 81.94 81.95 3.18 3.17-81.95z"}),h=r.createElement("path",{d:"M335.08 118.92c5 15.13 20.44 24.7 38.06 14.51M340 115c-2.94 3.32-5.56 4.45-11.13 5.07",stroke:"#FFF",strokeMiterlimit:10,strokeWidth:5}),v=r.createElement("path",{d:"M326.51 226.26c9.21 1.09 46.63 9.81 36.62-24",stroke:"#34A853",strokeLinejoin:"round",strokeWidth:7}),b=r.createElement("path",{d:"M255.19 243.91c-1.95 22.25-1.06 43.25 6 61.53H277M227.09 243.91c-2 21.75-.54 42.43 6 61.53h-14.62",stroke:"#188038",strokeLinejoin:"round",strokeWidth:7}),k=r.createElement("path",{d:"M255.19 243.91a212.54 212.54 0 00-.86 22.78M227.09 243.91q-.75 8.16-.82 16.11",stroke:"#137333",strokeLinejoin:"round",strokeWidth:7}),E=r.createElement("path",{d:"M193.58 180c-2.07 8.2-14.68 10.84-20.68 15.6-1.52 1.21-3.64 2.28-4.66 4a5 5 0 00-.6 1.84",stroke:"#4285F4",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:7}),_=r.createElement("path",{d:"M163.46 270.46c-9.77-6.25-16-20.14-13.11-31.58 2.53-9.9 14-22.34 23.45-25.13",stroke:"#34A853",strokeLinejoin:"round",strokeWidth:7}),y=r.createElement("path",{fill:"#1E8E3E",d:"M256.35 176.56l-81.51-8.96-8.96 81.52 81.51 8.96 81.52 8.96 8.96-81.51-81.52-8.97z"}),O=r.createElement("path",{d:"M221.29 221.77C234.34 238 260 245 280.76 228.31",stroke:"#FFF",strokeMiterlimit:10,strokeWidth:5}),S=r.createElement("path",{d:"M486.65 220.21c17.35 10.79 35.85 29.69 49.2-6.29",stroke:"#E94235",strokeLinejoin:"round",strokeWidth:7}),j=r.createElement("path",{d:"M486.65 220.21c4.33 2.7 8.74 5.9 13.12 8.63",stroke:"#D93025",strokeLinejoin:"round",strokeWidth:7}),w=r.createElement("path",{d:"M392.08 212.43c2.38 17.25 3.91 37.24 0 54.26",stroke:"#E94235",strokeLinejoin:"round",strokeWidth:7}),C=r.createElement("path",{d:"M392.08 212.43c1 7.16 1.82 14.78 2.19 22.47",stroke:"#D93025",strokeLinejoin:"round",strokeWidth:7}),N=r.createElement("path",{stroke:"#C5221F",strokeLinejoin:"round",strokeWidth:7,d:"M430.17 218.39l-8.54 87.05h-22.3M469.93 218.39l-8.54 87.05h20.74"}),R=r.createElement("path",{stroke:"#B31412",strokeLinejoin:"round",strokeWidth:7,d:"M430.17 218.39l-3.67 37.38M469.93 218.39l-3.33 33.95"}),x=r.createElement("circle",{cx:444.5,cy:188.84,r:61.5,fill:"#D93025"}),z=r.createElement("path",{d:"M464.88 219.51c-9.5 12.78-27.2 17-40.68 1.74M430.17 219.55c-4.42-.27-7.08.79-11.45 4.29",stroke:"#FFF",strokeMiterlimit:10,strokeWidth:5}),A=r.createElement("path",{d:"M167.64 201.39a8.07 8.07 0 002.46 7.06c3.38 3 10.33 1.86 12-2.55"}),T=r.createElement("path",{d:"M517.93 197.07l-.44 2.26a3 3 0 00.93 2.81l4.27 3.85a6 6 0 011.53 6.75l-4.56 11.07a20.08 20.08 0 00-1.5 8.14l.31 12.64a2 2 0 001.62 1.91l2.75.54a2 2 0 002.22-1.16l5.06-11.58a20 20 0 001.68-8.11v-12a6 6 0 014-5.68l5.4-1.95a3 3 0 001.93-2.25l.44-2.26z",fill:"#80868B"}),L=r.createElement("path",{fill:"#DADCE0",d:"M520.641 183.266l25.565 5.006-2.697 13.769-25.564-5.007z"}),M=r.createElement("path",{stroke:"#BDC1C6",strokeMiterlimit:10,strokeWidth:.44,d:"M520.05 186.25l25.56 5.01M518.51 194.12l25.56 5.01"}),D=r.createElement("path",{d:"M525.462 161.77l24.446 4.787a.59.59 0 01.465.693l-4.114 21.01-25.604-5.013 4.12-21.04a.59.59 0 01.693-.466z",fill:"#5F6368"}),P=r.createElement("path",{stroke:"#BDC1C6",strokeMiterlimit:10,strokeWidth:.44,d:"M523.58 183.88l4.23-21.63M527.51 184.65l4.24-21.63M531.44 185.42l4.24-21.63M535.37 186.19l4.24-21.63M539.31 186.96l4.24-21.63M543.24 187.73l4.24-21.63"}),I=r.createElement("path",{d:"M549.45 166.49l-23.6-4.63a1 1 0 00-1.18.79l-1.35 6.89 2.56 9.24a3 3 0 004.38 1.82l8.63-4.89a6 6 0 017.33 1.1l1.84 1.95 2.18-11.1a1 1 0 00-.79-1.17z",fill:"#24C1E0"}),B=r.createElement("path",{d:"M91.3 137.33l-14.21-11.47-57 70.65a9.12 9.12 0 001.38 12.84A9.12 9.12 0 0034.33 208zm-60.91 61a5 5 0 11-7.09.76 5 5 0 017.09-.78z",fill:"#BDC1C6"}),F=r.createElement("path",{d:"M83.68 117.3l15.58 9.54 10-16.37a22.82 22.82 0 11-15.38-9.85z",fill:"#DADCE0"}),H=r.createElement("path",{fill:"#DADCE0",d:"M81.24 122.25l-6.01 5.62 16.25 9.95 5.94-5.65-16.18-9.92z"}),V=r.createElement("path",{fill:"#F8F9FA",d:"M95.68 131.1l-6.01 5.61 1.75 1.07 6-5.61-1.74-1.07zM92.07 128.89l-6.01 5.61 1.75 1.07 6-5.61-1.74-1.07zM88.46 126.67l-6.01 5.62 1.75 1.07 6-5.62-1.74-1.07zM84.85 124.46l-6.01 5.62 1.75 1.07 6-5.62-1.74-1.07zM81.24 122.25l-6.01 5.62 1.75 1.07 6-5.62-1.74-1.07z"}),W=r.createElement("path",{fill:"#BDC1C6",d:"M466.08 97.09l-4.91 2.54-33.57-70.67 4.91-2.54 33.57 70.67z"}),U=r.createElement("path",{fill:"#DADCE0",d:"M437.63 33.71l-7.57 3.92-5.62-15.68 4.63-2.4 8.56 14.16z"}),G=r.createElement("path",{d:"M466.83 105.11a7.34 7.34 0 01-10.14-3.11l-15.25-32.1 13.88-7.18 15.24 32.1a8 8 0 01-3.73 10.29z"}),K=r.createElement("path",{d:"M464.56 100.33A4 4 0 01459 98.6l-14.41-30.38 7.58-3.92 14.43 30.38a4.35 4.35 0 01-2.04 5.65z",fill:"#5F6368"}),q=r.createElement("path",{fill:"#BDC1C6",d:"M145.57 277.902l47.608-38.744 8.01 9.842-47.607 38.745z"}),X=r.createElement("path",{fill:"#9AA0A6",d:"M189.085 242.485l4.087-3.326 8.01 9.842-4.087 3.327z"}),Y=r.createElement("path",{fill:"#BDC1C6",d:"M211.607 241.354l8.143 10.005-7.415 6.035-8.143-10.006z"}),$=r.createElement("rect",{x:209.99,y:252.12,width:16.52,height:9.9,rx:1.53,transform:"rotate(140.86 218.257 257.06)",fill:"#DADCE0"}),J=r.createElement("path",{d:"M175.87 220.73c4.56-.39 13.4-.24 24.73 4.4a14.61 14.61 0 015.8 4.3l9 11.11a1.53 1.53 0 01-.22 2.16l-10.44 8.5a1.53 1.53 0 01-2.15-.23l-8-9.81a44.24 44.24 0 01-4.69-7.16c-1.21-2.28-4.66-6.23-14.42-11.22a1.06 1.06 0 01.39-2.05z",fill:"#DADCE0"}),Z=r.createElement("rect",{x:158.98,y:236.29,width:3.47,height:74.9,rx:1.73,transform:"rotate(50.86 160.713 273.711)",fill:"#BDC1C6"}),Q=r.createElement("path",{d:"M166.85 260.56l-38.79 31.57a6.34 6.34 0 008 9.84l38.79-31.57z",fill:"#5F6368"}),ee=r.createElement("path",{d:"M163.46 270.46c-9.77-6.25-16-20.14-13.11-31.58",stroke:"#34A853",strokeLinejoin:"round",strokeWidth:7});t.a=function SvgWelcome(e){return r.createElement("svg",a({viewBox:"0 0 570 336",fill:"none"},e),i,o,c,l,s,u,d,f,g,p,m,h,v,b,k,E,_,y,O,S,j,w,C,N,R,x,z,A,T,L,M,D,P,I,B,F,H,V,W,U,G,K,q,X,Y,$,J,Z,Q,ee)}},1182:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M158.121 0H0v87.697h158.121V0z",fill:"#F1F3F4"}),o=r.createElement("path",{d:"M40.314 67.99c13.594 0 24.614-11.007 24.614-24.586 0-13.58-11.02-24.587-24.614-24.587-13.593 0-24.613 11.008-24.613 24.587S26.72 67.99 40.314 67.99z",stroke:"#fff",strokeWidth:8,strokeMiterlimit:10}),c=r.createElement("path",{d:"M54.075 23.024c6.546 4.417 10.853 11.903 10.853 20.386 0 13.58-11.019 24.588-24.614 24.588S15.701 56.984 15.701 43.404c0-13.58 11.018-24.58 24.613-24.58",stroke:"#D9DBDF",strokeWidth:8,strokeMiterlimit:10}),l=r.createElement("path",{d:"M115.103 68.029c13.594 0 24.613-11.008 24.613-24.587 0-13.58-11.019-24.587-24.613-24.587S90.489 29.863 90.489 43.442s11.02 24.587 24.614 24.587z",stroke:"#fff",strokeWidth:8,strokeMiterlimit:10}),s=r.createElement("path",{d:"M119.085 19.179c11.7 1.9 20.631 12.043 20.631 24.263 0 13.58-11.018 24.587-24.613 24.587-13.595 0-24.614-11.007-24.614-24.587s11.019-24.587 24.614-24.587",stroke:"#D9DBDF",strokeWidth:8,strokeMiterlimit:10}),u=r.createElement("path",{d:"M115.332 167c32.405 0 58.674-2.726 58.674-6.088s-26.269-6.088-58.674-6.088-58.674 2.726-58.674 6.088S82.928 167 115.332 167z",fill:"#F1F3F4"}),d=r.createElement("path",{d:"M125.237 111.026c-1.584 18.042-.865 35.066 4.899 49.886h12.787m-40.499-49.886c-1.622 17.635-.439 34.406 4.898 49.886H95.426",stroke:"#148E3A",strokeWidth:6,strokeMiterlimit:10,strokeLinejoin:"round"}),f=r.createElement("path",{d:"M125.237 111.026c-.394 4.512-.649 8.954-.712 13.32m-22.101-13.32c-.534 5.815-.763 11.534-.642 17.146",stroke:"#0D8034",strokeWidth:6,strokeMiterlimit:10,strokeLinejoin:"round"}),g=r.createElement("path",{d:"M107.539 67.374l-54.756 5.192 5.191 54.697 54.762-5.186 54.756-5.192-5.198-54.696-54.755 5.185z",fill:"#1E8E3E"}),p=r.createElement("path",{d:"M106.406 93.01c5.815 4.798 15.402 5.376 21.541-2.04",stroke:"#fff",strokeWidth:5,strokeMiterlimit:10}),m=r.createElement("path",{d:"M163.427 77.631c-65.246 6.006-90.973-8.801-101.07-23.297m-3.454 41.453c-12.959-1.652-26.248-17.412-29.626-30.014",stroke:"#34A853",strokeWidth:6,strokeMiterlimit:10}),h=r.createElement("path",{d:"M58.903 95.787c-3.88-.496-7.793-2.256-11.457-4.817",stroke:"#148E3A",strokeWidth:6,strokeMiterlimit:10,strokeLinejoin:"round"});t.a=function SvgWelcomeAnalytics(e){return r.createElement("svg",a({fill:"none",viewBox:"0 0 174 167"},e),i,o,c,l,s,u,d,f,g,p,m,h)}},1183:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CompatibilityChecks}));var r=n(25),a=n.n(r),i=n(2),o=n(1),c=n.n(o),l=n(3),s=n(10),u=n(58),d=n(17),f=n(1184),g=n(1185),p=n(13),m=n(731);function CompatibilityChecks(t){var n=t.children,r=a()(t,["children"]),o=Object(l.useRegistry)(),c=Object(f.a)(function(e){return e.select(p.c).isConnected()?[]:[m.c,m.b,Object(m.d)(e),m.a]}(o)),h=c.complete,v=c.error,b=v&&e.createElement(d.e,{alignLeft:!0,className:"googlesitekit-setup-compat"},e.createElement("div",{className:"googlesitekit-setup__warning"},e.createElement(u.a,null),e.createElement("div",{className:"googlesitekit-heading-4"},Object(i.__)("Your site may not be ready for Site Kit","google-site-kit"))),e.createElement(g.a,{error:v}));return n({props:r,complete:h,error:v,inProgressFeedback:!h&&e.createElement("div",{className:"googlesitekit-margin-left-1rem googlesitekit-align-self-center"},e.createElement("small",null,Object(i.__)("Checking Compatibility…","google-site-kit")),e.createElement(s.ProgressBar,{small:!0,compress:!0})),ctaFeedback:b})}CompatibilityChecks.propTypes={children:c.a.func.isRequired}}).call(this,n(4))},1184:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(15),l=n.n(c),s=n(81),u=n(0);function d(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return f(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function g(e){var t=Object(u.useState)(e),n=l()(t,1)[0],r=Object(u.useState)(!(null==n?void 0:n.length)),i=l()(r,2),c=i[0],f=i[1],g=Object(u.useState)(void 0),p=l()(g,2),m=p[0],h=p[1];return Object(s.a)((function(){c||function(){var e=o()(a.a.mark((function e(){var t,r,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:e.prev=0,t=d(n),e.prev=2,t.s();case 4:if((r=t.n()).done){e.next=10;break}return i=r.value,e.next=8,i();case 8:e.next=4;break;case 10:e.next=15;break;case 12:e.prev=12,e.t0=e.catch(2),t.e(e.t0);case 15:return e.prev=15,t.f(),e.finish(15);case 18:e.next=23;break;case 20:e.prev=20,e.t1=e.catch(0),h(e.t1);case 23:f(!0);case 24:case"end":return e.stop()}}),e,null,[[0,20],[2,12,15,18]])})));return function(){return e.apply(this,arguments)}}()()})),{complete:c,error:m}}},1185:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CompatibilityErrorNotice}));var r=n(38),a=n(2),i=n(3),o=n(75),c=n(20),l=n(13),s=n(264),u=n(808),d=function(e,t){var n=e.installed,r=e.active,i=e.installURL,o=e.activateURL,c=e.configureURL;return!n&&i?{"aria-label":Object(a.__)("Install the helper plugin","google-site-kit"),children:Object(a.__)("Install","google-site-kit"),href:i,external:!1}:n&&!r&&o?{"aria-label":Object(a.__)("Activate the helper plugin","google-site-kit"),children:Object(a.__)("Activate","google-site-kit"),href:o,external:!1}:n&&r&&c?{"aria-label":Object(a.__)("Configure the helper plugin","google-site-kit"),children:Object(a.__)("Configure","google-site-kit"),href:c,external:!1}:{"aria-label":Object(a.__)("Learn how to install and use the helper plugin","google-site-kit"),children:Object(a.__)("Learn how","google-site-kit"),href:t,external:!0}};function CompatibilityErrorNotice(t){var n=t.error,f=Object(i.useSelect)((function(e){return e(l.c).getDeveloperPluginState()}))||{},g=f.installed,p=Object(i.useSelect)((function(e){return e(l.c).getDocumentationLinkURL("staging")})),m=Object(i.useSelect)((function(e){return e(l.c).getErrorTroubleshootingLinkURL({code:s.e})}));switch(n){case s.c:return e.createElement("p",null,Object(r.a)(Object(a.__)("Site Kit cannot access the WordPress REST API. Please ensure it is enabled on your site. <GetHelpLink />","google-site-kit"),{GetHelpLink:e.createElement(u.a,{errorCode:n})}));case s.f:case s.d:return e.createElement("p",null,!g&&e.createElement("span",null,Object(r.a)(Object(a.__)("Looks like this may be a staging environment. If so, you’ll need to install a helper plugin and verify your production site in Search Console. <GetHelpLink />","google-site-kit"),{GetHelpLink:e.createElement(c.a,d(f,p))})),g&&e.createElement("span",null,Object(r.a)(Object(a.__)("Looks like this may be a staging environment and you already have the helper plugin. Before you can use Site Kit, please make sure you’ve provided the necessary credentials in the Authentication section and verified your production site in Search Console. <GetHelpLink />","google-site-kit"),{GetHelpLink:e.createElement(c.a,d(f,p))})));case s.h:return e.createElement("p",null,Object(r.a)(Object(a.__)("Looks like Site Kit is unable to place or detect tags on your site. This can be caused by using certain caching or maintenance mode plugins or your site’s frontend is configured on a different host or infrastructure than your administration dashboard. <GetHelpLink />","google-site-kit"),{GetHelpLink:e.createElement(u.a,{errorCode:n})}));case s.e:return e.createElement("p",{dangerouslySetInnerHTML:Object(o.a)("\n\t\t\t\t\t\t".concat(Object(a.__)("Looks like your site is having a technical issue with requesting data from Google services.","google-site-kit"),"\n\t\t\t\t\t\t<br/>\n\t\t\t\t\t\t").concat(Object(a.sprintf)(
/* translators: 1: Help URL, 2: Support Forum URL, 3: Error message */
Object(a.__)('<a href="%1$s">Click here</a> for more information, or to get more help, ask a question on our <a href="%2$s">support forum</a> and include the text of the original error message: %3$s',"google-site-kit"),m,"https://wordpress.org/support/plugin/google-site-kit/","<br/>".concat(n)),"\n\t\t\t\t\t\t"),{ALLOWED_TAGS:["a","br"],ALLOWED_ATTR:["href"]})});case s.b:return e.createElement("p",null,Object(r.a)(Object(a.__)("Looks like the AMP CDN is restricted in your region, which could interfere with setup on the Site Kit service. <GetHelpLink />","google-site-kit"),{GetHelpLink:e.createElement(u.a,{errorCode:n})}));case s.g:return e.createElement("p",{dangerouslySetInnerHTML:Object(o.a)("\n\t\t\t\t\t\t".concat(Object(a.__)("Looks like your site is having a technical issue with connecting to the Site Kit authentication service.","google-site-kit"),"\n\t\t\t\t\t\t<br/>\n\t\t\t\t\t\t").concat(Object(a.sprintf)(
/* translators: 1: Support Forum URL, 2: Error message */
Object(a.__)('To get more help, ask a question on our <a href="%1$s">support forum</a> and include the text of the original error message: %2$s',"google-site-kit"),"https://wordpress.org/support/plugin/google-site-kit/","<br/>".concat(n)),"\n\t\t\t\t\t\t"),{ALLOWED_TAGS:["a","br"],ALLOWED_ATTR:["href"]})})}}}).call(this,n(4))},1186:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=function(e){var t=e.split(".");return(+t[0]<<24)+(+t[1]<<16)+(+t[2]<<8)+ +t[3]},a=function(e,t,n){return(r(e)&-1<<32-n)===r(t)}},1187:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActivateAnalyticsNotice}));var r=n(6),a=n.n(r),i=n(0),o=n(38),c=n(2),l=n(3),s=n(10),u=n(516),d=n(29),f=n(1188);function ActivateAnalyticsNotice(){var t=Object(l.useDispatch)(d.a).setValues,n=Object(l.useSelect)((function(e){return e(d.a).getValue(u.b,u.a)})),r=Object(i.useCallback)((function(e){t(u.b,a()({},u.a,e.target.checked))}),[t]);return e.createElement("div",{className:"googlesitekit-setup-analytics-notice"},e.createElement("div",{className:"googlesitekit-setup-analytics-notice__opt-in"},e.createElement(s.Checkbox,{id:"googlesitekit-analytics-setup-opt-in",name:"googlesitekit-analytics-setup-opt-in",value:"1",checked:n,onChange:r},Object(o.a)(Object(c.__)("<strong>Connect Google Analytics as part of your setup.</strong> Activate Google Analytics to track how much traffic you’re getting and how people navigate your site.","google-site-kit"),{strong:e.createElement("strong",null)}))),e.createElement("div",{className:"googlesitekit-setup-analytics-notice__icon"},e.createElement(f.a,null)))}}).call(this,n(4))},1188:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{filter:"url(#analytics-setup-sidekick_svg__a)"},r.createElement("rect",{x:135,y:2,width:148,height:122,rx:3.976,fill:"#fff"})),o=r.createElement("path",{d:"M147.754 25.902v-7.98h1.939l2.118 5.607h.089l2.118-5.606h1.95v7.98h-1.494v-3.935l.09-1.326h-.09l-2.028 5.26h-1.17l-2.04-5.26h-.089l.089 1.326v3.934h-1.482zm12.456.179c-.595 0-1.122-.13-1.583-.39a2.93 2.93 0 01-1.07-1.081 3.164 3.164 0 01-.379-1.55 3.1 3.1 0 01.379-1.537c.26-.461.617-.822 1.07-1.082.461-.267.988-.4 1.583-.4.594 0 1.118.133 1.571.4a2.85 2.85 0 011.059 1.082c.26.453.39.965.39 1.537 0 .573-.13 1.089-.39 1.55a2.85 2.85 0 01-1.059 1.08c-.453.26-.977.39-1.571.39zm0-1.36c.282 0 .538-.063.769-.19a1.49 1.49 0 00.568-.568c.148-.252.223-.553.223-.903 0-.349-.075-.646-.223-.891a1.494 1.494 0 00-.568-.569 1.506 1.506 0 00-.769-.2 1.61 1.61 0 00-.781.2 1.573 1.573 0 00-.579.569c-.141.245-.212.542-.212.891 0 .35.071.65.212.903.148.245.342.435.579.569.246.126.506.189.781.189zm6.244 1.36c-.49 0-.91-.07-1.259-.212a2.36 2.36 0 01-.836-.557 2.299 2.299 0 01-.479-.758l1.304-.569c.245.543.669.814 1.27.814.238 0 .454-.037.647-.111a.438.438 0 00.29-.435.414.414 0 00-.156-.346.933.933 0 00-.368-.2 3.213 3.213 0 00-.457-.123l-.669-.145a2.524 2.524 0 01-.78-.312 2.04 2.04 0 01-.602-.58 1.508 1.508 0 01-.223-.813c0-.342.097-.639.29-.891.201-.253.468-.45.803-.591a2.916 2.916 0 011.125-.212c.565 0 1.048.1 1.449.301.401.2.702.513.903.936l-1.249.546a1.01 1.01 0 00-.468-.468 1.369 1.369 0 00-.601-.133c-.253 0-.454.048-.602.144-.149.097-.223.212-.223.346 0 .141.074.264.223.368.148.104.327.182.535.234l.836.2c.564.134.988.35 1.27.647.29.297.435.654.435 1.07 0 .371-.108.698-.323.98-.216.275-.506.49-.87.647a3.187 3.187 0 01-1.215.223zm5.885-.09c-.587 0-1.048-.167-1.382-.501-.334-.342-.502-.81-.502-1.404v-2.62h-.991V20.22h.991v-1.605h1.46v1.605h1.393v1.248h-1.393v2.351c0 .26.056.46.168.602.111.134.297.2.557.2a.884.884 0 00.368-.066 3.06 3.06 0 00.345-.178v1.426a2.74 2.74 0 01-1.014.19zm4.751 2.319v-8.091h1.371v.69h.089c.141-.222.357-.423.647-.601.297-.179.65-.268 1.058-.268.521 0 .985.13 1.394.39.416.253.742.606.98 1.059.245.453.368.977.368 1.571 0 .595-.123 1.119-.368 1.572-.238.453-.564.81-.98 1.07a2.596 2.596 0 01-1.394.379c-.408 0-.761-.086-1.058-.257-.29-.178-.506-.379-.647-.602h-.089l.089.803v2.285h-1.46zm2.909-3.578a1.481 1.481 0 001.326-.791 1.7 1.7 0 00.223-.88c0-.342-.074-.636-.223-.881a1.488 1.488 0 00-.568-.568 1.464 1.464 0 00-1.516 0c-.23.133-.42.327-.568.58a1.714 1.714 0 00-.212.868c0 .335.071.628.212.88.148.246.338.44.568.58.238.142.491.212.758.212zm6.842 1.349c-.594 0-1.121-.13-1.582-.39a2.93 2.93 0 01-1.07-1.081 3.164 3.164 0 01-.379-1.55 3.1 3.1 0 01.379-1.537c.26-.461.617-.822 1.07-1.082.461-.267.988-.4 1.582-.4.595 0 1.119.133 1.572.4a2.85 2.85 0 011.059 1.082c.26.453.39.965.39 1.537 0 .573-.13 1.089-.39 1.55a2.85 2.85 0 01-1.059 1.08c-.453.26-.977.39-1.572.39zm0-1.36c.283 0 .539-.063.769-.19a1.49 1.49 0 00.569-.568c.148-.252.223-.553.223-.903 0-.349-.075-.646-.223-.891a1.49 1.49 0 00-.569-.569 1.604 1.604 0 00-1.549 0 1.573 1.573 0 00-.579.569c-.141.245-.212.542-.212.891 0 .35.071.65.212.903.148.245.342.435.579.569.245.126.505.189.78.189zm4.039 3.589v-8.091h1.37v.69h.09c.141-.222.356-.423.646-.601.297-.179.65-.268 1.059-.268.52 0 .984.13 1.393.39.416.253.743.606.981 1.059.245.453.367.977.367 1.571 0 .595-.122 1.119-.367 1.572-.238.453-.565.81-.981 1.07a2.595 2.595 0 01-1.393.379c-.409 0-.762-.086-1.059-.257-.29-.178-.505-.379-.646-.602h-.09l.09.803v2.285h-1.46zm2.908-3.578a1.481 1.481 0 001.327-.791 1.71 1.71 0 00.223-.88c0-.342-.075-.636-.223-.881a1.49 1.49 0 00-.569-.568 1.457 1.457 0 00-1.515 0 1.57 1.57 0 00-.569.58 1.714 1.714 0 00-.212.868c0 .335.071.628.212.88.149.246.338.44.569.58.237.142.49.212.757.212zm6.12 1.349c-.699 0-1.223-.216-1.572-.647-.349-.43-.524-1.018-.524-1.76v-3.455h1.46v3.276c0 .386.093.691.279.914a.944.944 0 00.736.323c.431 0 .765-.156 1.003-.468.237-.312.356-.695.356-1.148V20.22h1.46v5.683h-1.371v-.713h-.089a1.83 1.83 0 01-.724.658 2.194 2.194 0 01-1.014.234zm4.632-.179v-7.98H206v7.98h-1.46zm4.579.179c-.402 0-.758-.078-1.07-.234a1.88 1.88 0 01-.725-.68 1.877 1.877 0 01-.267-1.003c0-.394.1-.732.301-1.014.208-.283.483-.502.825-.658a2.806 2.806 0 011.159-.234c.349 0 .65.03.902.09.253.051.454.107.602.166v-.245a.93.93 0 00-.345-.746c-.231-.201-.546-.301-.948-.301a1.73 1.73 0 00-.78.189 1.788 1.788 0 00-.613.48l-.936-.736c.275-.357.62-.632 1.037-.825a3.19 3.19 0 011.359-.29c.869 0 1.531.2 1.984.602.453.401.68.988.68 1.76v3.5h-1.438v-.58h-.089a1.955 1.955 0 01-.646.536c-.268.148-.599.223-.992.223zm.345-1.137c.297 0 .546-.063.747-.19.208-.133.364-.304.468-.512.111-.208.167-.427.167-.658a2.331 2.331 0 00-.557-.19 2.61 2.61 0 00-.635-.077c-.431 0-.728.081-.892.245a.82.82 0 00-.245.602c0 .223.078.408.234.557.163.149.401.223.713.223zm4.163.958V20.22h1.371v.757h.089a1.76 1.76 0 01.647-.657 1.89 1.89 0 01.991-.268c.149 0 .283.012.402.034.119.022.223.052.312.089v1.46a3.776 3.776 0 00-.468-.145 1.67 1.67 0 00-.48-.067c-.438 0-.783.16-1.036.48-.245.319-.368.709-.368 1.17v2.83h-1.46zm-63.399 16.179c-.579 0-1.096-.127-1.549-.38a2.874 2.874 0 01-1.059-1.07c-.252-.46-.378-.984-.378-1.57 0-.587.126-1.108.378-1.56a2.87 2.87 0 011.059-1.07c.453-.26.97-.39 1.549-.39.632 0 1.174.133 1.627.4.454.268.781.654.981 1.16l-1.337.557a1.183 1.183 0 00-.502-.569 1.49 1.49 0 00-.769-.19c-.431 0-.795.153-1.092.458-.29.297-.435.698-.435 1.203 0 .513.145.922.435 1.226.297.298.661.446 1.092.446.327 0 .598-.07.814-.212.215-.148.39-.349.524-.601l1.304.58a2.698 2.698 0 01-1.015 1.147c-.453.29-.995.435-1.627.435zm6.379 0c-.594 0-1.122-.13-1.582-.39a2.922 2.922 0 01-1.07-1.081 3.164 3.164 0 01-.379-1.55 3.1 3.1 0 01.379-1.538c.26-.46.616-.82 1.07-1.08.46-.268.988-.402 1.582-.402.594 0 1.118.134 1.571.401a2.84 2.84 0 011.059 1.081c.26.454.39.966.39 1.538a3.1 3.1 0 01-.39 1.55 2.835 2.835 0 01-1.059 1.08c-.453.26-.977.39-1.571.39zm0-1.36a1.483 1.483 0 001.337-.758c.149-.252.223-.553.223-.903 0-.349-.074-.646-.223-.891a1.487 1.487 0 00-.568-.569 1.604 1.604 0 00-1.549 0 1.575 1.575 0 00-.58.569c-.141.245-.211.542-.211.891 0 .35.07.65.211.903.149.245.342.435.58.569.245.126.505.189.78.189zm4.029 1.181V36.22h1.37v.713h.089c.171-.275.413-.49.725-.647.312-.163.65-.245 1.014-.245.698 0 1.222.212 1.571.636.35.423.524.988.524 1.694v3.532h-1.46v-3.354c0-.372-.096-.654-.289-.847-.186-.2-.443-.301-.769-.301-.409 0-.732.156-.97.468-.23.312-.346.69-.346 1.137v2.897h-1.459zm8.862.09c-.587 0-1.047-.168-1.382-.502-.334-.342-.501-.81-.501-1.404v-2.62h-.992V36.22h.992v-1.605h1.46v1.605h1.393v1.248h-1.393v2.351c0 .26.055.46.167.602.111.134.297.2.557.2a.88.88 0 00.368-.066 3.06 3.06 0 00.345-.178v1.426a2.73 2.73 0 01-1.014.19zm4.652.089c-.572 0-1.085-.13-1.538-.39a2.868 2.868 0 01-1.059-1.07 3.134 3.134 0 01-.379-1.55c0-.542.127-1.043.379-1.504.253-.46.598-.828 1.037-1.103.438-.283.94-.424 1.504-.424.609 0 1.122.13 1.538.39.416.26.732.61.947 1.048.216.43.324.91.324 1.438 0 .118-.004.223-.011.312a1.825 1.825 0 01-.023.178h-4.257c.067.446.245.788.535 1.025.29.238.631.357 1.025.357.349 0 .643-.074.881-.223a1.74 1.74 0 00.557-.58l1.181.58c-.267.468-.616.84-1.047 1.114-.431.268-.963.402-1.594.402zm-.045-4.848c-.334 0-.631.1-.891.3a1.58 1.58 0 00-.546.837h2.83a1.108 1.108 0 00-.189-.513 1.344 1.344 0 00-.468-.446 1.422 1.422 0 00-.736-.178zm3.922 4.67v-5.684h1.371v.713h.089c.171-.275.413-.49.725-.647.312-.163.65-.245 1.014-.245.698 0 1.222.212 1.571.636.349.423.524.988.524 1.694v3.532h-1.46v-3.354c0-.372-.096-.654-.29-.847-.185-.2-.442-.301-.769-.301-.408 0-.731.156-.969.468-.231.312-.346.69-.346 1.137v2.897h-1.46zm8.863.089c-.587 0-1.048-.168-1.382-.502-.334-.342-.501-.81-.501-1.404v-2.62h-.992V36.22h.992v-1.605h1.46v1.605h1.393v1.248h-1.393v2.351c0 .26.055.46.167.602.111.134.297.2.557.2a.88.88 0 00.368-.066 3.06 3.06 0 00.345-.178v1.426a2.73 2.73 0 01-1.014.19z",fill:"#202124"}),c=r.createElement("path",{d:"M149.749 64.817h-.742v-4.92l-1.488.546v-.67l2.115-.794h.115v5.838zm1.381 15h-3.807v-.53l2.011-2.236c.298-.338.503-.612.615-.822.114-.213.172-.432.172-.658 0-.303-.092-.552-.276-.746-.183-.195-.428-.292-.734-.292-.367 0-.653.105-.858.316-.202.207-.303.497-.303.87h-.739c0-.535.172-.968.515-1.297.346-.33.808-.495 1.385-.495.54 0 .967.142 1.281.427.314.282.471.658.471 1.13 0 .571-.365 1.252-1.094 2.042l-1.556 1.688h2.917v.603zm-2.733 11.736h.554c.349-.006.623-.097.822-.276.2-.178.3-.419.3-.722 0-.681-.339-1.022-1.018-1.022-.319 0-.575.092-.766.276-.189.18-.283.421-.283.722h-.739c0-.46.168-.842.503-1.145.338-.306.766-.46 1.285-.46.548 0 .978.146 1.289.436.311.29.467.693.467 1.209 0 .253-.082.497-.247.734a1.475 1.475 0 01-.667.53c.319.102.565.27.738.504.176.234.264.52.264.858 0 .521-.17.935-.511 1.24-.341.307-.784.46-1.329.46-.545 0-.99-.148-1.333-.443-.34-.296-.511-.685-.511-1.17h.743c0 .306.099.551.299.735.2.183.467.275.802.275.357 0 .629-.093.818-.28.189-.185.284-.453.284-.801 0-.338-.104-.598-.312-.778-.207-.181-.507-.275-.898-.28h-.554v-.602zm2.043 16.312h.806v.603h-.806v1.349h-.742v-1.349h-2.646v-.435l2.602-4.026h.786v3.858zm-2.55 0h1.808v-2.849l-.088.16-1.72 2.689z",fill:"#C7C7C7"}),l=r.createElement("path",{d:"M149 62.227c0-1.23 1.104-2.227 2.465-2.227h51.464c1.362 0 2.465.997 2.465 2.227s-1.103 2.227-2.465 2.227h-51.464c-1.361 0-2.465-.997-2.465-2.227zm0 14.849c0-1.23 1.104-2.227 2.465-2.227h80.07c1.361 0 2.465.997 2.465 2.227s-1.104 2.227-2.465 2.227h-80.07c-1.361 0-2.465-.997-2.465-2.227zm0 14.848c0-1.23 1.104-2.227 2.465-2.227h44.109c1.361 0 2.464.997 2.464 2.227s-1.103 2.227-2.464 2.227h-44.109c-1.361 0-2.465-.997-2.465-2.227zm0 14.849c0-1.23 1.104-2.228 2.465-2.228h59.637c1.362 0 2.465.998 2.465 2.228 0 1.23-1.103 2.227-2.465 2.227h-59.637c-1.361 0-2.465-.997-2.465-2.227z",fill:"#C7C7C7"}),s=r.createElement("g",{filter:"url(#analytics-setup-sidekick_svg__b)"},r.createElement("rect",{x:4,y:52,width:148,height:122,rx:3.976,fill:"#fff"})),u=r.createElement("path",{d:"M19.707 75.08c-.624 0-1.17-.133-1.638-.4a2.783 2.783 0 01-1.08-1.149c-.253-.505-.38-1.107-.38-1.805v-4.803h1.494v4.892c0 .528.133.966.4 1.315.276.35.677.524 1.204.524.528 0 .925-.174 1.193-.524.275-.349.412-.787.412-1.315v-4.892h1.494v4.803c0 .661-.127 1.245-.38 1.75a2.834 2.834 0 01-1.069 1.181c-.46.282-1.01.424-1.65.424zm4.585-.178V69.22h1.37v.713h.09c.17-.275.412-.49.724-.647.312-.163.65-.245 1.014-.245.699 0 1.223.212 1.572.635.349.424.523.989.523 1.695v3.532h-1.46v-3.354c0-.372-.096-.654-.29-.847-.185-.2-.441-.301-.768-.301-.409 0-.732.156-.97.468-.23.312-.345.69-.345 1.137v2.897h-1.46zm7.496-6.308a.912.912 0 01-.669-.278.912.912 0 01-.278-.669c0-.26.093-.48.278-.657a.912.912 0 01.669-.279c.26 0 .48.093.658.279a.876.876 0 01.278.657c0 .26-.093.483-.278.669a.876.876 0 01-.658.278zm-.736 6.308V69.22h1.46v5.683h-1.46zm7.072 2.408v-2.285l.089-.803h-.09c-.14.223-.356.424-.645.602-.29.171-.643.257-1.06.257-.52 0-.987-.127-1.404-.38a2.811 2.811 0 01-.991-1.07c-.238-.452-.357-.976-.357-1.57 0-.595.119-1.119.357-1.572a2.73 2.73 0 01.991-1.059 2.6 2.6 0 011.405-.39c.416 0 .769.09 1.059.268.29.178.505.379.646.601h.089v-.69h1.36v8.09h-1.45zm-1.46-3.578c.275 0 .527-.07.758-.212.238-.14.427-.334.568-.58.149-.252.223-.545.223-.88a1.64 1.64 0 00-.223-.869 1.471 1.471 0 00-.568-.58 1.46 1.46 0 00-1.516 0c-.23.134-.42.324-.568.569a1.736 1.736 0 00-.212.88c0 .335.07.628.212.88.148.254.338.45.568.591.238.134.49.201.758.201zm6.352 1.349c-.698 0-1.222-.216-1.572-.647-.349-.43-.523-1.018-.523-1.76v-3.455h1.46v3.276c0 .386.093.691.278.914a.945.945 0 00.736.323c.43 0 .765-.156 1.003-.468.238-.312.356-.695.356-1.148V69.22h1.46v5.683h-1.37v-.713h-.09a1.83 1.83 0 01-.724.658 2.192 2.192 0 01-1.014.234zm7.23 0c-.572 0-1.085-.13-1.538-.39a2.868 2.868 0 01-1.059-1.07c-.252-.453-.379-.97-.379-1.55 0-.542.127-1.043.38-1.504.252-.46.597-.828 1.036-1.103.438-.283.94-.424 1.504-.424.61 0 1.122.13 1.538.39.416.26.732.61.947 1.048.216.43.324.91.324 1.438 0 .118-.004.222-.011.312a1.913 1.913 0 01-.023.178h-4.257c.067.446.245.787.535 1.025.29.238.631.357 1.025.357.35 0 .643-.074.88-.223.238-.156.424-.35.558-.58l1.181.58c-.267.468-.616.84-1.047 1.114-.431.268-.963.402-1.594.402zm-.045-4.848c-.334 0-.631.1-.891.3a1.58 1.58 0 00-.546.837h2.83a1.114 1.114 0 00-.189-.513 1.35 1.35 0 00-.468-.446 1.42 1.42 0 00-.736-.178zm8.306 4.67L56.1 69.218h1.66l1.438 3.7h.09l1.46-3.7h1.638l-2.43 5.683h-1.449zm5.496-6.309a.912.912 0 01-.669-.278.912.912 0 01-.278-.669c0-.26.093-.48.279-.657a.912.912 0 01.668-.279c.26 0 .48.093.658.279a.876.876 0 01.278.657c0 .26-.093.483-.278.669a.876.876 0 01-.658.278zm-.735 6.308V69.22h1.46v5.683h-1.46zm5.142.179c-.49 0-.91-.07-1.26-.212a2.356 2.356 0 01-.836-.557 2.293 2.293 0 01-.479-.758l1.304-.569c.245.543.669.814 1.27.814.238 0 .454-.037.647-.111a.437.437 0 00.29-.435.414.414 0 00-.156-.346.925.925 0 00-.368-.2 3.198 3.198 0 00-.457-.123l-.669-.145a2.526 2.526 0 01-.78-.312 2.04 2.04 0 01-.602-.58 1.504 1.504 0 01-.222-.813c0-.341.096-.639.29-.891.2-.253.467-.45.802-.591a2.92 2.92 0 011.125-.212c.565 0 1.048.1 1.45.301.4.2.701.513.902.936l-1.248.546a1.019 1.019 0 00-.468-.468 1.373 1.373 0 00-.602-.133c-.253 0-.453.048-.602.145-.149.096-.223.211-.223.345 0 .141.074.264.223.368.149.104.327.182.535.234l.836.2c.564.134.988.35 1.27.647.29.297.435.654.435 1.07 0 .371-.108.698-.323.98-.216.275-.505.49-.87.647a3.186 3.186 0 01-1.214.223zm4.235-6.487a.912.912 0 01-.669-.278.912.912 0 01-.279-.669c0-.26.093-.48.279-.657a.912.912 0 01.669-.279c.26 0 .479.093.657.279a.876.876 0 01.279.657c0 .26-.093.483-.279.669a.876.876 0 01-.657.278zm-.736 6.308V69.22h1.46v5.683h-1.46zm5.4.09c-.587 0-1.048-.168-1.382-.502-.335-.342-.502-.81-.502-1.404v-2.62h-.992V69.22h.992v-1.605h1.46v1.605h1.393v1.248h-1.393v2.351c0 .26.056.46.167.602.112.134.298.2.558.2a.88.88 0 00.368-.066 3.09 3.09 0 00.345-.178v1.426c-.32.126-.658.19-1.014.19zm4.707.089c-.594 0-1.122-.13-1.582-.39a2.929 2.929 0 01-1.07-1.081 3.168 3.168 0 01-.38-1.55c0-.572.127-1.084.38-1.538.26-.46.616-.82 1.07-1.08.46-.268.988-.402 1.582-.402.595 0 1.118.134 1.572.401.453.268.806.628 1.058 1.081.26.454.39.966.39 1.538a3.1 3.1 0 01-.39 1.55 2.844 2.844 0 01-1.058 1.08c-.454.26-.977.39-1.572.39zm0-1.36c.282 0 .539-.063.77-.19a1.49 1.49 0 00.567-.568 1.75 1.75 0 00.223-.903c0-.349-.074-.646-.222-.891a1.49 1.49 0 00-.569-.569 1.605 1.605 0 00-1.55 0 1.573 1.573 0 00-.579.569c-.14.245-.211.542-.211.891 0 .35.07.65.212.903.148.245.341.435.58.569.244.126.504.189.78.189zm4.029 1.181V69.22h1.37v.757h.09c.14-.26.356-.479.646-.657a1.89 1.89 0 01.992-.268c.148 0 .282.011.401.034.119.022.223.052.312.089v1.46a3.772 3.772 0 00-.468-.145 1.663 1.663 0 00-.48-.067c-.438 0-.783.16-1.036.48-.245.319-.367.709-.367 1.17v2.83h-1.46zm6.7.179c-.49 0-.91-.07-1.26-.212a2.356 2.356 0 01-.835-.557 2.293 2.293 0 01-.48-.758l1.305-.569c.245.543.668.814 1.27.814.238 0 .453-.037.646-.111a.437.437 0 00.29-.435.414.414 0 00-.156-.346.924.924 0 00-.368-.2 3.196 3.196 0 00-.456-.123l-.67-.145a2.528 2.528 0 01-.78-.312 2.038 2.038 0 01-.601-.58 1.503 1.503 0 01-.223-.813c0-.341.097-.639.29-.891.2-.253.468-.45.802-.591a2.92 2.92 0 011.126-.212c.565 0 1.047.1 1.449.301.4.2.702.513.902.936l-1.248.546a1.018 1.018 0 00-.468-.468 1.374 1.374 0 00-.602-.133c-.252 0-.453.048-.602.145-.148.096-.222.211-.222.345 0 .141.074.264.222.368.149.104.327.182.535.234l.836.2c.565.134.988.35 1.27.647.29.297.435.654.435 1.07 0 .371-.107.698-.323.98a2.16 2.16 0 01-.87.647 3.187 3.187 0 01-1.214.223zM17.166 88.902v-4.435h-1.014v-1.248h1.014v-.313c0-.66.197-1.17.591-1.526.394-.364.895-.546 1.505-.546.186 0 .36.01.524.033.163.022.297.052.4.09v1.426a5.375 5.375 0 00-.322-.112 1.206 1.206 0 00-.424-.067.847.847 0 00-.59.212c-.15.141-.224.335-.224.58v.223h1.394v1.248h-1.393v4.435h-1.46zm3.991 0V83.22h1.371v.757h.09c.14-.26.356-.479.646-.657a1.89 1.89 0 01.992-.268c.148 0 .282.011.4.034.12.022.224.052.313.089v1.46a3.778 3.778 0 00-.468-.145 1.663 1.663 0 00-.48-.067c-.438 0-.783.16-1.036.48-.245.319-.368.709-.368 1.17v2.83h-1.46zm7.138.179c-.594 0-1.122-.13-1.582-.39a2.927 2.927 0 01-1.07-1.081 3.169 3.169 0 01-.38-1.55c0-.572.127-1.084.38-1.538.26-.46.616-.82 1.07-1.08.46-.268.988-.402 1.582-.402.595 0 1.118.134 1.572.401.453.268.806.628 1.058 1.081.26.454.39.966.39 1.538a3.1 3.1 0 01-.39 1.55 2.844 2.844 0 01-1.058 1.08c-.454.26-.977.39-1.572.39zm0-1.36c.283 0 .539-.063.77-.19.237-.133.426-.323.568-.568a1.75 1.75 0 00.223-.903c0-.349-.075-.646-.223-.891a1.492 1.492 0 00-.569-.569 1.606 1.606 0 00-1.55 0 1.572 1.572 0 00-.578.569c-.142.245-.212.542-.212.891 0 .35.07.65.212.903.148.245.341.435.58.569.244.126.504.189.78.189zm4.029 1.181V83.22h1.37v.713h.09c.17-.268.405-.483.702-.647a2.093 2.093 0 011.025-.245c.416 0 .77.097 1.059.29.297.193.505.43.624.713.17-.275.412-.509.724-.702.32-.2.714-.3 1.182-.3.661 0 1.155.2 1.482.601.334.401.502.936.502 1.605v3.655h-1.45v-3.388c0-.364-.085-.639-.256-.824-.17-.194-.4-.29-.69-.29-.387 0-.692.152-.915.457-.222.304-.334.698-.334 1.181v2.864h-1.46v-3.388c0-.364-.089-.639-.267-.824-.171-.194-.416-.29-.736-.29-.364 0-.654.152-.87.457-.215.304-.322.698-.322 1.181v2.864h-1.46zm14.917.179c-.49 0-.91-.07-1.26-.212a2.356 2.356 0 01-.835-.557 2.291 2.291 0 01-.48-.758l1.305-.569c.245.543.669.814 1.27.814a1.8 1.8 0 00.647-.111.438.438 0 00.29-.435.414.414 0 00-.157-.346.925.925 0 00-.367-.2 3.198 3.198 0 00-.457-.123l-.669-.145a2.527 2.527 0 01-.78-.312 2.038 2.038 0 01-.602-.58 1.503 1.503 0 01-.223-.813c0-.341.097-.639.29-.891.2-.253.468-.45.802-.591a2.92 2.92 0 011.126-.212c.565 0 1.048.1 1.449.301.401.2.702.513.902.936l-1.248.546a1.019 1.019 0 00-.468-.468 1.373 1.373 0 00-.602-.133c-.252 0-.453.048-.601.145-.149.096-.223.211-.223.345 0 .141.074.264.223.368.148.104.327.182.535.234l.835.2c.565.134.989.35 1.27.647.29.297.435.654.435 1.07 0 .371-.107.698-.323.98-.215.275-.505.49-.869.647a3.186 3.186 0 01-1.215.223zm6.183 0c-.572 0-1.085-.13-1.538-.39a2.868 2.868 0 01-1.059-1.07c-.252-.453-.379-.97-.379-1.55 0-.542.127-1.043.38-1.504.252-.46.597-.828 1.036-1.103.438-.283.94-.424 1.504-.424.61 0 1.122.13 1.538.39.416.26.732.61.947 1.048.216.43.324.91.324 1.438 0 .118-.004.222-.011.312a1.913 1.913 0 01-.023.178h-4.257c.067.446.245.787.535 1.025.29.238.632.357 1.025.357.35 0 .643-.074.88-.223.238-.156.424-.35.558-.58l1.181.58c-.267.468-.616.84-1.047 1.114-.431.268-.962.402-1.594.402zm-.045-4.848c-.334 0-.631.1-.891.3a1.58 1.58 0 00-.546.837h2.83a1.114 1.114 0 00-.189-.513 1.351 1.351 0 00-.468-.446 1.42 1.42 0 00-.736-.178zm5.715 4.848a2.36 2.36 0 01-1.07-.234 1.876 1.876 0 01-.724-.68 1.875 1.875 0 01-.267-1.003c0-.394.1-.732.3-1.014a2.07 2.07 0 01.825-.658c.35-.156.736-.234 1.16-.234.348 0 .65.03.902.09.253.051.453.107.602.166v-.245a.93.93 0 00-.346-.747c-.23-.2-.546-.3-.947-.3-.268 0-.528.063-.78.189-.245.119-.45.279-.613.48l-.936-.736a2.75 2.75 0 011.036-.825c.416-.193.87-.29 1.36-.29.87 0 1.53.2 1.984.602.453.401.68.988.68 1.76v3.5h-1.438v-.58h-.09a1.948 1.948 0 01-.646.536c-.267.148-.598.223-.992.223zm.346-1.137c.297 0 .546-.063.747-.19.208-.133.364-.304.468-.512.111-.208.167-.427.167-.658a2.34 2.34 0 00-.558-.19 2.609 2.609 0 00-.635-.077c-.43 0-.728.082-.891.245a.818.818 0 00-.245.602c0 .223.078.408.234.557.163.149.4.223.713.223zm4.163.958V83.22h1.37v.757h.09c.141-.26.356-.479.646-.657a1.89 1.89 0 01.992-.268c.149 0 .282.011.401.034.12.022.223.052.312.089v1.46a3.778 3.778 0 00-.468-.145 1.663 1.663 0 00-.479-.067c-.438 0-.784.16-1.036.48-.246.319-.368.709-.368 1.17v2.83h-1.46zm7.093.179c-.58 0-1.096-.127-1.549-.38a2.868 2.868 0 01-1.059-1.07c-.252-.46-.378-.984-.378-1.57 0-.588.126-1.108.379-1.56a2.86 2.86 0 011.058-1.07c.453-.26.97-.39 1.55-.39.63 0 1.173.133 1.626.4.454.268.78.654.981 1.16l-1.337.557a1.184 1.184 0 00-.502-.569 1.494 1.494 0 00-.769-.19c-.43 0-.795.153-1.092.458-.29.297-.434.698-.434 1.203 0 .513.144.922.434 1.226.297.298.661.446 1.092.446.327 0 .598-.07.814-.212.215-.148.39-.349.524-.601l1.304.58a2.7 2.7 0 01-1.015 1.147c-.453.29-.995.435-1.627.435zm3.714-.179v-7.98h1.46v2.018l-.089.992h.09a1.82 1.82 0 01.701-.635 2.1 2.1 0 011.026-.257c.705 0 1.237.212 1.593.635.364.417.546.981.546 1.695v3.532h-1.46v-3.354c0-.372-.1-.654-.3-.847-.201-.2-.461-.301-.78-.301-.26 0-.491.074-.692.223-.2.14-.356.334-.468.58a1.944 1.944 0 00-.167.813v2.886h-1.46z",fill:"#202124"}),d=r.createElement("path",{d:"M22.5 144.244l5.55-4.092 4.44 1.86 5.92-1.86 8.14 5.58 6.66-1.488 3.33-4.092 5.18 5.58 3.7-1.488 8.51 3.347 8.51-7.439 4.81 1.86 5.92-2.976 2.22 1.116h6.66l9.62-13.762 2.96 5.579 2.59-1.487 2.96 4.463 13.32-4.463",stroke:"#CCC",strokeWidth:1.486}),f=r.createElement("g",{filter:"url(#analytics-setup-sidekick_svg__c)"},r.createElement("rect",{x:226,y:69,width:148,height:122,rx:3.976,fill:"#fff"})),g=r.createElement("path",{d:"M331.315 120.007a23.387 23.387 0 016.546-1.154l.216 8.328a15.2 15.2 0 00-4.255.75l-2.507-7.924z",fill:"#DEDEDE"}),p=r.createElement("path",{d:"M319.491 128.654a23.758 23.758 0 0111.122-8.405l2.753 7.839a15.44 15.44 0 00-7.229 5.464l-6.646-4.898z",fill:"#C6C6C6"}),m=r.createElement("path",{d:"M319.348 156.639a23.871 23.871 0 01-4.426-13.619 24.068 24.068 0 014.141-13.754l6.795 4.683a15.646 15.646 0 00-2.691 8.94 15.51 15.51 0 002.877 8.852l-6.696 4.898z",fill:"#F1F1F1"}),h=r.createElement("path",{d:"M352.229 161.904a23.453 23.453 0 01-17.188 4.31 23.358 23.358 0 01-15.25-8.976l6.541-5.107a15.182 15.182 0 009.912 5.834 15.24 15.24 0 0011.173-2.801l4.812 6.74z",fill:"#959595"}),v=r.createElement("path",{d:"M338.601 118.842c4.912 0 9.693 1.551 13.674 4.437a23.611 23.611 0 018.532 11.657 24.032 24.032 0 01.208 14.531 23.977 23.977 0 01-8.192 11.989l-5.021-6.583a15.626 15.626 0 005.19-17.239 15.35 15.35 0 00-5.545-7.577 15.145 15.145 0 00-8.889-2.884l.043-8.331z",fill:"#C7C7C7"}),b=r.createElement("ellipse",{cx:327.009,cy:174.262,rx:1.85,ry:1.86,fill:"#959595"}),k=r.createElement("ellipse",{cx:334.41,cy:174.262,rx:1.85,ry:1.86,fill:"#C7C7C7"}),E=r.createElement("ellipse",{cx:341.81,cy:174.262,rx:1.85,ry:1.86,fill:"#DEDEDE"}),_=r.createElement("ellipse",{cx:349.209,cy:174.262,rx:1.85,ry:1.86,fill:"#F1F1F1"}),y=r.createElement("path",{stroke:"#ECE9F1",strokeWidth:.994,strokeLinecap:"round",d:"M238.336 175.624h58.206"}),O=r.createElement("path",{stroke:"#ECE9F1",strokeWidth:.497,strokeLinecap:"round",d:"M238.088 160.995h58.703m-58.703-14.878h58.703m-58.703-14.877h58.703"}),S=r.createElement("path",{opacity:.08,d:"M264.829 164.08c-4.374-1.37-6.29-5.044-10.433-5.568-4.285-.542-7.612 13.315-16.557 14.64v2.97h59.2v-25.431c-2.378-.516-4.616-19.012-9.777-17.643-5.162 1.369-8.961 22.202-13.073 15.493-4.111-6.71-4.985 16.908-9.36 15.539z",fill:"url(#analytics-setup-sidekick_svg__d)"}),j=r.createElement("path",{d:"M241.722 119.374h-2.434l-.547 1.516h-.79l2.219-5.81h.67l2.223 5.81h-.786l-.555-1.516zm-2.203-.631h1.976l-.99-2.717-.986 2.717zm5.013 2.147h-.739v-6.129h.739v6.129zm1.987 0h-.738v-6.129h.738v6.129zm5.878-.427c-.287.338-.709.507-1.265.507-.46 0-.811-.133-1.053-.399-.24-.269-.361-.665-.364-1.189v-2.809h.739v2.789c0 .654.266.982.798.982.564 0 .939-.211 1.125-.631v-3.14h.738v4.317h-.702l-.016-.427zm4.358-.718a.537.537 0 00-.228-.463c-.149-.112-.411-.207-.786-.287a3.813 3.813 0 01-.89-.287 1.29 1.29 0 01-.487-.4.966.966 0 01-.155-.55c0-.354.149-.653.447-.898.3-.245.684-.367 1.149-.367.49 0 .886.126 1.189.379.306.253.459.576.459.97h-.742a.666.666 0 00-.259-.523.956.956 0 00-.647-.22c-.269 0-.479.059-.63.176a.552.552 0 00-.228.459c0 .178.071.312.212.403.141.09.395.177.762.259.37.083.669.181.898.296.228.114.397.252.507.415a1 1 0 01.167.586c0 .386-.154.696-.463.93-.308.231-.709.347-1.201.347a2.18 2.18 0 01-.918-.183 1.514 1.514 0 01-.626-.511 1.254 1.254 0 01-.224-.714h.738a.773.773 0 00.296.59c.186.144.431.216.734.216.279 0 .503-.056.67-.168a.524.524 0 00.256-.455zm3.492 1.225c-.586 0-1.062-.191-1.429-.574-.367-.386-.551-.901-.551-1.545v-.136c0-.428.081-.81.244-1.145.165-.338.393-.601.686-.79.295-.191.615-.287.958-.287.561 0 .998.185 1.309.554.311.37.467.9.467 1.589v.307h-2.925c.01.426.134.77.371 1.034.239.26.542.391.91.391.26 0 .481-.054.662-.16.181-.106.339-.247.475-.423l.451.351c-.362.556-.905.834-1.628.834zm-.092-3.871a.979.979 0 00-.75.328c-.203.215-.328.518-.376.909h2.163v-.055c-.021-.376-.122-.666-.303-.87-.181-.208-.426-.312-.734-.312zm4.721.136a2.175 2.175 0 00-.363-.028c-.485 0-.813.206-.986.619v3.064h-.738v-4.317h.718l.012.498c.242-.385.585-.578 1.03-.578.143 0 .252.018.327.056v.686zm3.196 2.51a.539.539 0 00-.227-.463c-.149-.112-.411-.207-.786-.287a3.797 3.797 0 01-.89-.287 1.283 1.283 0 01-.487-.4.958.958 0 01-.156-.55c0-.354.149-.653.447-.898.301-.245.684-.367 1.149-.367.49 0 .886.126 1.19.379.306.253.459.576.459.97h-.743a.663.663 0 00-.259-.523.954.954 0 00-.647-.22c-.268 0-.478.059-.63.176a.552.552 0 00-.228.459c0 .178.071.312.212.403.141.09.395.177.762.259.37.083.669.181.898.296.229.114.398.252.507.415a.993.993 0 01.167.586c0 .386-.154.696-.462.93-.309.231-.709.347-1.202.347-.345 0-.651-.061-.917-.183a1.524 1.524 0 01-.627-.511 1.253 1.253 0 01-.223-.714h.738a.772.772 0 00.295.59c.186.144.431.216.734.216.28 0 .503-.056.671-.168a.525.525 0 00.255-.455z",fill:"#B8B8B8"}),w=r.createElement("path",{d:"M240.358 92.903V86.35h-2.229v-1.427h5.963v1.427h-2.229v6.553h-1.505zm4.072 0v-5.684h1.37v.758h.09c.141-.26.356-.48.646-.658.297-.178.628-.267.992-.267.149 0 .282.01.401.033.119.022.223.052.312.09v1.46a3.743 3.743 0 00-.468-.146 1.654 1.654 0 00-.479-.067c-.438 0-.784.16-1.037.48-.245.32-.367.71-.367 1.17v2.83h-1.46zm6.32.178c-.401 0-.758-.078-1.07-.234a1.877 1.877 0 01-.724-.68 1.878 1.878 0 01-.268-1.003c0-.394.101-.732.301-1.014.208-.282.483-.502.825-.658a2.81 2.81 0 011.159-.234c.349 0 .65.03.903.09.252.051.453.107.602.167v-.246a.931.931 0 00-.346-.746c-.23-.2-.546-.301-.947-.301-.268 0-.528.063-.78.19-.245.118-.45.278-.613.478l-.936-.735a2.75 2.75 0 011.036-.825 3.19 3.19 0 011.36-.29c.869 0 1.53.201 1.983.602.454.401.68.989.68 1.761v3.5h-1.437v-.58h-.09a1.946 1.946 0 01-.646.535c-.267.149-.598.223-.992.223zm.346-1.137c.297 0 .546-.063.746-.19a1.29 1.29 0 00.468-.512c.112-.208.168-.427.168-.657a2.355 2.355 0 00-.558-.19 2.604 2.604 0 00-.635-.078c-.431 0-.728.082-.891.245a.817.817 0 00-.246.602c0 .223.078.409.234.557.164.149.402.223.714.223zm12.814-5.294a.932.932 0 01-.669-.267.933.933 0 01-.268-.669c0-.26.09-.479.268-.657a.913.913 0 01.669-.279c.26 0 .479.093.657.279a.875.875 0 01.279.657c0 .26-.093.483-.279.669a.892.892 0 01-.657.267zm-8.147 6.253v-4.436h-1.014v-1.248h1.014v-.312c0-.661.197-1.17.591-1.527.393-.364.895-.546 1.504-.546a2.603 2.603 0 01.624.067v1.404a6.55 6.55 0 00-.212-.056.961.961 0 00-.323-.044.713.713 0 00-.524.211c-.133.142-.2.335-.2.58v.223h2.251v-.312c0-.661.197-1.17.591-1.527.394-.364.895-.546 1.504-.546a2.593 2.593 0 01.624.067v1.404a7.12 7.12 0 00-.211-.056.972.972 0 00-.324-.044.71.71 0 00-.523.211c-.134.142-.201.335-.201.58v.223h3.7v5.684h-1.449v-4.436h-2.251v4.436h-1.46v-4.436h-2.251v4.436h-1.46zm13.01.178c-.579 0-1.095-.126-1.549-.379a2.873 2.873 0 01-1.058-1.07c-.253-.46-.379-.984-.379-1.571s.126-1.107.379-1.56c.26-.454.613-.81 1.058-1.07.454-.26.97-.39 1.549-.39.632 0 1.174.133 1.628.4.453.268.78.654.98 1.16l-1.337.557a1.183 1.183 0 00-.502-.568 1.49 1.49 0 00-.769-.19c-.43 0-.795.152-1.092.457-.29.297-.434.698-.434 1.204 0 .512.144.921.434 1.226.297.297.662.445 1.092.445.327 0 .599-.07.814-.211.215-.149.39-.35.524-.602l1.304.58a2.703 2.703 0 01-1.014 1.147c-.454.29-.996.435-1.628.435z",fill:"#202124"}),C=r.createElement("defs",null,r.createElement("filter",{id:"analytics-setup-sidekick_svg__a",x:132.515,y:.012,width:152.97,height:126.97,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feMorphology",{radius:.497,operator:"dilate",in:"SourceAlpha",result:"effect1_dropShadow_532_12388"}),r.createElement("feOffset",{dy:.497}),r.createElement("feGaussianBlur",{stdDeviation:.994}),r.createElement("feColorMatrix",{values:"0 0 0 0 0.423529 0 0 0 0 0.443137 0 0 0 0 0.458824 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_532_12388"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:.994}),r.createElement("feGaussianBlur",{stdDeviation:.746}),r.createElement("feColorMatrix",{values:"0 0 0 0 0.337255 0 0 0 0 0.352941 0 0 0 0 0.360784 0 0 0 0.25 0"}),r.createElement("feBlend",{in2:"effect1_dropShadow_532_12388",result:"effect2_dropShadow_532_12388"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect2_dropShadow_532_12388",result:"shape"})),r.createElement("filter",{id:"analytics-setup-sidekick_svg__b",x:.784,y:49.588,width:154.431,height:128.431,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:.804}),r.createElement("feGaussianBlur",{stdDeviation:1.608}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_532_12388"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_532_12388",result:"shape"})),r.createElement("filter",{id:"analytics-setup-sidekick_svg__c",x:222.784,y:66.588,width:154.431,height:128.431,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:.804}),r.createElement("feGaussianBlur",{stdDeviation:1.608}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_532_12388"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_532_12388",result:"shape"})),r.createElement("linearGradient",{id:"analytics-setup-sidekick_svg__d",x1:258.024,y1:132.976,x2:258.024,y2:184.15,gradientUnits:"userSpaceOnUse"},r.createElement("stop",{stopColor:"#4F4F4F"}),r.createElement("stop",{offset:1,stopColor:"#4F4F4F",stopOpacity:0})));t.a=function SvgAnalyticsSetupSidekick(e){return r.createElement("svg",a({fill:"none",viewBox:"0 0 378 196"},e),i,o,c,l,s,u,d,f,g,p,m,h,v,b,k,E,_,y,O,S,j,w,C)}},1189:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupUsingProxyViewOnly}));var r=n(2),a=n(0),i=n(38),o=n(3),c=n(10),l=n(218),s=n(234),u=n(192),d=n(20),f=n(235),g=n(1190),p=n(516),m=n(7),h=n(13),v=n(32),b=n(17),k=n(9),E=n(18);function SetupUsingProxyViewOnly(){var t=Object(E.a)(),n=Object(o.useDispatch)(m.a).dismissItem,_=Object(o.useDispatch)(v.a).navigateTo,y=Object(o.useSelect)((function(e){return e(h.c).getAdminURL("googlesitekit-dashboard")})),O=Object(o.useSelect)((function(e){return e(h.c).getDocumentationLinkURL("dashboard-sharing")})),S=Object(a.useCallback)((function(){Promise.all([n(p.c),Object(k.I)(t,"confirm_viewonly")]).finally((function(){_(y)}))}),[y,n,_,t]);return y?e.createElement(a.Fragment,null,e.createElement(s.a,null,e.createElement(f.a,null)),e.createElement("div",{className:"googlesitekit-setup"},e.createElement(b.e,null,e.createElement(b.k,null,e.createElement(b.a,{size:12},e.createElement(u.a,{rounded:!0},e.createElement("section",{className:"googlesitekit-setup__splash"},e.createElement(b.e,null,e.createElement(b.k,{className:"googlesitekit-setup__content"},e.createElement(b.a,{smSize:4,mdSize:8,lgSize:4,lgOrder:2,className:"googlesitekit-setup__icon"},e.createElement(g.a,{width:398,height:280})),e.createElement(b.a,{smSize:4,mdSize:8,lgSize:8,lgOrder:1},e.createElement("h1",{className:"googlesitekit-setup__title"},Object(r.__)("View-only Dashboard Access","google-site-kit")),e.createElement("p",{className:"googlesitekit-setup__description"},Object(i.a)(Object(r.__)("An administrator has granted you access to view this site's dashboard to view stats from all shared Google services. <a>Learn more</a>","google-site-kit"),{a:e.createElement(d.a,{"aria-label":Object(r.__)("Learn more about dashboard sharing","google-site-kit"),href:O,external:!0})})),e.createElement("p",null,Object(r.__)("Get insights about how people find and use your site as well as how to improve and monetize your content, directly in your WordPress dashboard","google-site-kit")),e.createElement(l.a,null),e.createElement("div",{className:"googlesitekit-start-setup-wrap"},e.createElement(c.Button,{onClick:S},Object(r.__)("Go to Dashboard","google-site-kit"))))))))))))):null}}).call(this,n(4))},1190:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M357.758 250.137H101.493V60.904h260.113v185.384a3.849 3.849 0 01-3.848 3.849z",fill:"#F1F3F4"}),o=r.createElement("path",{d:"M31.5 60.908h69.997v189.234H35.349a3.849 3.849 0 01-3.849-3.849V60.908z",fill:"#5F6368"}),c=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M119.339 7.235a12.14 12.14 0 011.813 6.392l-1.283-.001h1.283v.001c0 .285-.01.569-.029.852l-.96-.338-.427 1.21 1.267.196c.056-.354.095-.71.12-1.068l5.497 1.94a8.564 8.564 0 015.215 10.925l-1.21-.427-1.209-.428v.001a6 6 0 00-3.653-7.652l-7.459-2.632.164-1.053a9.884 9.884 0 00.118-1.527v-.001a9.56 9.56 0 00-1.43-5.041l2.183-1.35zm2.425 22.906l-.427 1.21.428-1.21h-.001zm0 0a6 6 0 007.652-3.651l1.209.427 1.21.429a8.567 8.567 0 01-10.925 5.215L91.611 22.184l.857-2.419 29.296 10.376z",fill:"#FBBC04"}),l=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M84.252 8.566a9.53 9.53 0 00-1.434 5.039v.003c0 .508.04 1.015.122 1.516l.173 1.059-7.464 2.636a5.995 5.995 0 00-3.656 7.668l-1.209.431 1.21-.428a5.999 5.999 0 007.652 3.652l29.296-10.376.857 2.419L80.5 32.562a8.565 8.565 0 01-10.924-5.214 8.56 8.56 0 015.221-10.949l5.485-1.937c-.02-.285-.03-.57-.03-.856v-.001l1.284.001-1.283.002v-.002a12.095 12.095 0 011.82-6.393l2.18 1.353z",fill:"#FBBC04"}),s=r.createElement("path",{d:"M85.738 0v46.35a22.272 22.272 0 0029.934 0V0H85.738z",fill:"#F9AB00"}),u=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M106.634 26.66c-4.462 2.815-8.852 2.28-11.968-.222l1.286-1.6c2.406 1.93 5.836 2.452 9.586.085l1.096 1.736z",fill:"#fff"}),d=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M84.43 12.376c.69.158 1.12.847.962 1.537l-4.3 18.717a1.319 1.319 0 001.284 1.615m2.053-21.87a1.283 1.283 0 00-1.538.964l1.538-.963zm-1.538.964l-4.3 18.714 4.3-18.714zm-4.3 18.715v-.001zm0 0a3.886 3.886 0 107.67.872V25.64a1.283 1.283 0 00-2.566 0v7.287a1.318 1.318 0 01-1.319 1.32",fill:"#3C4043"}),f=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M116.979 12.376a1.283 1.283 0 011.539.961l4.321 18.716a3.881 3.881 0 01-3.785 4.758 3.883 3.883 0 01-3.885-3.885V25.64a1.283 1.283 0 112.566 0v7.287a1.32 1.32 0 001.319 1.32m0 0a1.321 1.321 0 001.285-1.616l-4.321-18.715c-.16-.69.271-1.38.961-1.54",fill:"#202124"}),g=r.createElement("path",{d:"M115.338 13.625c0-5.373-6.43-9.581-14.64-9.581-8.211 0-14.636 4.213-14.636 9.58h29.276z",fill:"#3C4043"}),p=r.createElement("path",{d:"M92.425 21.905a8.282 8.282 0 008.282-8.283 8.282 8.282 0 10-16.565 0 8.283 8.283 0 008.283 8.283z",fill:"#8AB4F8"}),m=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M92.425 6.624a7 7 0 100 14 7 7 0 000-14zm-9.566 7a9.566 9.566 0 019.566-9.566 9.566 9.566 0 010 19.131 9.566 9.566 0 01-9.566-9.566z",fill:"#202124"}),h=r.createElement("path",{d:"M108.99 21.905a8.283 8.283 0 100-16.567 8.283 8.283 0 000 16.567z",fill:"#8AB4F8"}),v=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M108.991 6.624a7 7 0 100 14 7 7 0 000-14zm-9.566 7a9.566 9.566 0 1119.132.002 9.566 9.566 0 01-19.132-.003z",fill:"#202124"}),b=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M92.445 9.236a4.392 4.392 0 00-4.393 4.388h-1.026a5.42 5.42 0 015.419-5.414v1.026zm16.545 0a4.395 4.395 0 00-4.393 4.388h-1.026a5.417 5.417 0 015.419-5.414v1.026z",fill:"#D0E2FC"}),k=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M93.066 77.73H39.988V72.6h53.078v5.131z",fill:"#639AF8"}),E=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M93.066 95.556H39.988v-5.132h53.078v5.132zm0 17.83H39.988v-5.131h53.078v5.131zm0 17.828H39.988v-5.132h53.078v5.132z",fill:"#80858A"}),_=r.createElement("path",{d:"M35.35 44.416h322.412a3.85 3.85 0 013.849 3.848V60.91H31.501V48.285a3.847 3.847 0 013.848-3.87z",fill:"#3C4043"}),y=r.createElement("path",{d:"M114.61 74.35a5.29 5.29 0 00-.088-.965h-4.315v1.77h2.478a2.132 2.132 0 01-.924 1.427v1.15h1.478a4.483 4.483 0 001.371-3.382z",fill:"#3E81F6"}),O=r.createElement("path",{d:"M111.772 76.58a2.784 2.784 0 01-3.488-.385 2.79 2.79 0 01-.659-1.067h-1.539v1.18a4.585 4.585 0 004.105 2.535 4.38 4.38 0 003.043-1.113l-1.462-1.15z",fill:"#2DA84F"}),S=r.createElement("path",{d:"M107.482 74.246c0-.299.049-.595.143-.878v-1.185h-1.539a4.612 4.612 0 000 4.105l1.539-1.18a2.837 2.837 0 01-.143-.862z",fill:"#FCBC00"}),j=r.createElement("path",{d:"M110.206 71.469a2.525 2.525 0 011.766.688l1.308-1.314a4.397 4.397 0 00-3.079-1.196 4.587 4.587 0 00-4.105 2.53l1.539 1.186a2.749 2.749 0 012.571-1.894z",fill:"#EB402D"}),w=r.createElement("path",{d:"M42.975 55.648a2.987 2.987 0 100-5.974 2.987 2.987 0 000 5.974zm11.947 0a2.987 2.987 0 100-5.974 2.987 2.987 0 000 5.974zm11.948 0a2.987 2.987 0 100-5.974 2.987 2.987 0 000 5.974z",fill:"#fff"}),C=r.createElement("path",{d:"M105.991 75.162l-4.492-4.492-4.493 4.492 4.493 4.493 4.492-4.493z",fill:"#F1F3F4"}),N=r.createElement("path",{d:"M225.394 87.245h-107.11v67.226h107.11V87.245z",fill:"#fff"}),R=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M213.036 107.626h-79.891V97.362h79.891v10.264zm-10.761 19.102h-69.13v-10.264h69.13v10.264zm-15.929 19.815h-53.201v-10.264h53.201v10.264z",fill:"#E7E9EC"}),x=r.createElement("path",{d:"M344.826 87.245H237.715v67.226h107.111V87.245z",fill:"#fff"}),z=r.createElement("path",{d:"M74.187 280c20.486 0 37.092-1.725 37.092-3.854 0-2.128-16.606-3.854-37.092-3.854-20.485 0-37.092 1.726-37.092 3.854 0 2.129 16.607 3.854 37.092 3.854z",fill:"#F1F3F4"}),A=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M312.629 54.763c2.286.74 4.947.381 7.318-.968l1.015 1.785c-2.791 1.587-6.047 2.081-8.966 1.136-2.95-.956-5.42-3.34-6.667-7.232l1.955-.626c1.068 3.334 3.091 5.175 5.345 5.905z",fill:"#fff"}),T=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M306.584 47.852c.884.047 1.659.3 2.485.725l-.938 1.826c-.642-.33-1.134-.474-1.655-.501-.544-.03-1.194.064-2.163.323l-.53-1.983c1.048-.28 1.94-.436 2.801-.39z",fill:"#fff"}),L=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M281.545 27.907l.243 1.023c.7 2.945 1.41 5.798 2.586 8.418 1.164 2.595 2.874 4.915 5.156 6.346 2.284 1.432 5.162 1.851 7.463.85l1.024 2.352c-3.24 1.41-7.013.752-9.851-1.028-2.84-1.782-4.834-4.573-6.133-7.47-1.139-2.538-1.863-5.238-2.496-7.852l-7.573.27-.091-2.564 9.672-.345zm13.133-4.467l.243 1.023c.699 2.949 1.41 5.793 2.586 8.42 1.164 2.599 2.874 4.918 5.155 6.349 2.283 1.432 5.162 1.847 7.466.849l1.02 2.354c-3.238 1.403-7.01.752-9.849-1.03-2.84-1.78-4.835-4.573-6.134-7.474-1.139-2.543-1.863-5.235-2.496-7.852l-7.572.27-.092-2.564 9.673-.345z",fill:"#C5221F"}),M=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M248.148 97.601h4.105v44.082h83.002v4.105h-87.107V97.601z",fill:"#E7E9EC"}),D=r.createElement("path",{d:"M317.853 64.668c12.666 0 22.933-10.267 22.933-22.933S330.519 18.8 317.853 18.8s-22.934 10.268-22.934 22.934 10.268 22.933 22.934 22.933z",fill:"#D93025"}),P=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M311.83 51.34c2.281.74 4.934.38 7.307-.97l1.014 1.785c-2.789 1.586-6.038 2.083-8.954 1.139-2.947-.955-5.415-3.337-6.677-7.226l1.952-.634c1.083 3.337 3.107 5.178 5.358 5.907z",fill:"#fff"}),I=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M305.773 44.424c.884.047 1.661.302 2.488.73l-.944 1.823c-.64-.332-1.131-.475-1.652-.503-.544-.029-1.194.065-2.165.322l-.525-1.985c1.046-.277 1.938-.432 2.798-.387z",fill:"#fff"}),B=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M319.709 102.472c7.531-11.92 10.7-28.343 8.132-42.188l2.522-.468c2.687 14.483-.61 31.56-8.485 44.026l-2.169-1.37z",fill:"#D93025"}),F=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M297.648 117.474c3.936-7.731 9.27-16.845 20.308-16.845 6.889 0 11.512 4.768 14.266 8.997 1.403 2.154 2.408 4.293 3.061 5.885.327.8.57 1.469.732 1.944a24.745 24.745 0 01.232.717l.***************.002.007c0 .001 0 .002-1.972.572l-1.971.572-.001-.002-.007-.024-.015-.049-.02-.063c-.032-.103-.082-.26-.152-.464a30.225 30.225 0 00-.645-1.711c-.587-1.432-1.479-3.325-2.702-5.203-2.499-3.836-6.046-7.132-10.826-7.132-8.263 0-12.579 6.609-16.65 14.603-.392.769-.782 1.553-1.174 2.34-1.544 3.099-3.111 6.246-4.87 8.726-2.23 3.144-5.152 5.778-9.333 5.778-3.211 0-5.678-.646-7.723-1.676-2.012-1.012-3.517-2.351-4.798-3.525l-.266-.244c-1.205-1.106-2.188-2.008-3.384-2.694-1.212-.695-2.689-1.185-4.823-1.185-1.27 0-2.659.682-4.147 2.127-1.472 1.429-2.829 3.405-3.998 5.488-1.16 2.066-2.086 4.148-2.724 5.721a52.117 52.117 0 00-.908 2.407l-.044.131-.011.031-.002.006-1.95-.639-1.951-.639.002-.006.004-.012.015-.044.054-.159c.046-.137.115-.333.204-.581a58.52 58.52 0 01.782-2.037c.679-1.675 1.678-3.924 2.949-6.189 1.262-2.248 2.845-4.604 4.718-6.423 1.857-1.803 4.219-3.287 7.007-3.287 2.848 0 5.029.675 6.865 1.729 1.614.925 2.924 2.131 4.066 3.182l.315.29c1.264 1.157 2.409 2.15 3.871 2.885 1.427.719 3.255 1.238 5.877 1.238 2.182 0 4.032-1.296 5.985-4.048 1.556-2.194 2.933-4.957 4.459-8.016.412-.826.835-1.674 1.275-2.538z",fill:"#E7E9EC"}),H=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M302.748 110.621c-9.086-16.362-9.222-37.328-.347-53.806l2.259 1.217c-8.46 15.708-8.33 35.746.331 51.344l-2.243 1.245z",fill:"#D93025"}),V=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M49.792 239.931c-2.575-.265-4.53-1.209-6.084-2.629l6.084 2.629zm-6.084-2.629c-1.575-1.439-2.794-3.417-3.786-5.821l3.786 5.821zm-3.786-5.821l-2.372.978 2.372-.978zm-2.372.978c1.077 2.61 2.478 4.957 4.427 6.737l-4.427-6.737zm4.427 6.737c1.97 1.799 4.443 2.967 7.554 3.287l-7.554-3.287zm7.816.735c3.095.316 6.85-.569 8.764-2.743l1.926 1.695c-2.674 3.04-7.417 3.961-10.952 3.6",fill:"#4285F4"}),W=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M70.674 235.279l2.405.894-5.085 13.671a21.812 21.812 0 00.017 15.251l5.21 10.017h-7.59v-2.566h3.363l-3.33-6.403-.026-.071a24.374 24.374 0 01-.049-17.122l5.085-13.671z",fill:"#1967D2"}),U=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M66.749 245.824l3.926-10.545 2.404.895-3.925 10.545-2.405-.895z",fill:"#185ABC"}),G=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M78.682 239.576h2.566v32.971h3.807v2.566h-6.373v-35.537z",fill:"#1967D2"}),K=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M78.682 243.671v-4.095h2.566v4.095h-2.566z",fill:"#185ABC"}),q=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M123.654 199.8a44.213 44.213 0 01-31.383 23.942l-.482-2.521a41.642 41.642 0 0029.562-22.552l2.303 1.131z",fill:"#4285F4"}),X=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M128.988 149.77V92.992h4.106v56.778h-4.106z",fill:"#E7E9EC"}),Y=r.createElement("path",{d:"M27.012 229.683c-3.233 2.643-6.31 5.096-9.265 7.221-1.544 1.129-4.354-.4-4.683-2.494-.566-3.926-.921-7.995-1.112-12.162a365.303 365.303 0 00-10.86-6.826c-1.821-1.134-1.265-3.818.927-4.726 4.076-1.601 8.168-3.192 12.255-4.701 1.296-4.223 2.573-8.488 3.71-12.65.608-2.257 3.191-2.73 4.56-.831 2.527 3.495 5.255 7.026 7.967 10.515 4.36-.031 8.75 0 13.13.067 2.367.056 3.87 2.376 2.51 4.074a373.058 373.058 0 00-7.72 10.223c1.303 3.967 2.415 7.893 3.28 11.767.437 2.053-1.637 4.495-3.49 3.987-3.5-.928-7.247-2.14-11.21-3.464z",fill:"#F9AB00"}),$=r.createElement("path",{d:"M344.825 165.928H118.284v66.738h226.541v-66.738z",fill:"#fff"}),J=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M147.858 182.147c-6.991 0-12.66 5.668-12.66 12.66s5.669 12.66 12.66 12.66c6.992 0 12.66-5.668 12.66-12.66s-5.668-12.66-12.66-12.66zm-16.765 12.66c0-9.259 7.506-16.765 16.765-16.765 9.26 0 16.766 7.506 16.766 16.765 0 9.26-7.506 16.766-16.766 16.766-9.259 0-16.765-7.506-16.765-16.766zm31.668 25.745h-29.805v-4.105h29.805v4.105zm55.761 0h-29.805v-4.105h29.805v4.105zm-14.902-38.405c-6.992 0-12.66 5.668-12.66 12.66s5.668 12.66 12.66 12.66 12.66-5.668 12.66-12.66-5.668-12.66-12.66-12.66zm-16.766 12.66c0-9.259 7.507-16.765 16.766-16.765 9.259 0 16.765 7.506 16.765 16.765 0 9.26-7.506 16.766-16.765 16.766s-16.766-7.506-16.766-16.766zm87.43 25.745h-29.805v-4.105h29.805v4.105zm-14.902-38.405c-6.992 0-12.66 5.668-12.66 12.66s5.668 12.66 12.66 12.66 12.66-5.668 12.66-12.66-5.668-12.66-12.66-12.66zm-16.766 12.66c0-9.259 7.506-16.765 16.766-16.765 9.259 0 16.765 7.506 16.765 16.765 0 9.26-7.506 16.766-16.765 16.766-9.26 0-16.766-7.506-16.766-16.766z",fill:"#F0F2F3"}),Z=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M147.867 182.147a12.658 12.658 0 00-12.527 10.776 12.656 12.656 0 1024.725-1.448 12.656 12.656 0 00-5.134-7.164l2.296-3.404a16.76 16.76 0 11-9.357-2.865l-.003 4.105zm55.752 0a12.654 12.654 0 108.39 3.184l2.722-3.073a16.757 16.757 0 013.226 21.217 16.764 16.764 0 01-8.877 7.172 16.765 16.765 0 01-20.064-7.614 16.758 16.758 0 013.873-21.109 16.763 16.763 0 0110.731-3.882l-.001 4.105zm55.766 0a12.681 12.681 0 102.049.164l.657-4.053a16.783 16.783 0 0113.979 18.582 16.786 16.786 0 11-16.692-18.798l.007 4.105z",fill:"#D9DBDF"}),Q=r.createElement("path",{d:"M360.265 277.44c20.619 0 37.333-1.726 37.333-3.854 0-2.129-16.714-3.854-37.333-3.854s-37.333 1.725-37.333 3.854c0 2.128 16.714 3.854 37.333 3.854z",fill:"#F1F3F4"}),ee=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M368.44 274.877c-3.725-9.645-4.194-20.648-3.16-32.153l2.556.23c-.988 10.992-.552 21.25 2.69 30.178h7.199v2.565h-8.088c-.53 0-1.006-.326-1.197-.82zm-14.99-1.744c-2.955-9.536-3.569-19.756-2.589-30.41l2.555.235c-1.015 11.035-.267 21.456 3.015 31.043a1.282 1.282 0 01-1.214 1.698h-7.497v-2.566h5.73z",fill:"#148E3A"}),te=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M364.824 251.252c.042-2.823.203-5.646.457-8.525l2.556.225c-.25 2.828-.407 5.588-.447 8.338l-2.566-.038zm-14.374 2.467a97.088 97.088 0 01.411-10.999l2.555.241a94.514 94.514 0 00-.4 10.708l-2.566.05z",fill:"#0D8034"}),ne=r.createElement("path",{d:"M355.371 215.214l-34.619 3.285 3.285 34.618 34.618-3.284 34.619-3.284-3.284-34.619-34.619 3.284z",fill:"#1E8E3E"}),re=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M369.015 230.76c-4.262 5.173-10.96 4.737-14.975 1.418l1.226-1.483c3.334 2.756 8.788 3.06 12.264-1.159l1.485 1.224z",fill:"#fff"}),ae=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M328.231 204.378c5.679 8.162 23.612 20.419 62.34 16.852l.235 2.555c-39.275 3.618-58.291-8.758-64.681-17.941l2.106-1.466zm-19.218 14.118a19.03 19.03 0 0015.577 13.611l-.367 2.539a21.592 21.592 0 01-17.677-15.446l2.467-.704z",fill:"#34A853"}),ie=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M317.868 229.814a17.951 17.951 0 006.705 2.29l-.334 2.544a20.518 20.518 0 01-7.663-2.617l1.292-2.217z",fill:"#1E8E3E"}),oe=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M312.465 192.451c-6.992 0-12.66 5.668-12.66 12.66s5.668 12.66 12.66 12.66c6.991 0 12.66-5.668 12.66-12.66s-5.669-12.66-12.66-12.66zm-16.766 12.66c0-9.26 7.506-16.766 16.766-16.766 9.259 0 16.765 7.506 16.765 16.766 0 9.259-7.506 16.765-16.765 16.765-9.26 0-16.766-7.506-16.766-16.765z",fill:"#F0F2F3"}),ce=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M312.464 192.454a12.66 12.66 0 00-8.079 2.916 12.66 12.66 0 00-2.983 15.881 12.651 12.651 0 0015.056 5.867 12.655 12.655 0 004.572-21.326l2.779-3.022a16.758 16.758 0 012.88 21.206 16.755 16.755 0 01-8.937 7.038 16.754 16.754 0 01-11.371-.288 16.763 16.763 0 01-4.617-28.515 16.758 16.758 0 0110.7-3.862v4.105z",fill:"#D9DBDF"}),le=r.createElement("path",{d:"M78.538 240.807a23.669 23.669 0 01-30.98-12.691l43.67-18.289a23.665 23.665 0 01-12.69 30.98z",fill:"#1A73E8"}),se=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M86.154 223.843l-.068 1.599c-.166 3.926-2.048 7.594-5.4 9.468l-1.001-1.791c2.174-1.216 3.644-3.458 4.158-6.114l-15.149 6.194-.777-1.9 18.237-7.456z",fill:"#fff"});t.a=function SvgViewOnlySetupSidekick(e){return r.createElement("svg",a({viewBox:"0 0 398 280",fill:"none"},e),i,o,c,l,s,u,d,f,g,p,m,h,v,b,k,E,_,y,O,S,j,w,C,N,R,x,z,A,T,L,M,D,P,I,B,F,H,V,W,U,G,K,q,X,Y,$,J,Z,Q,ee,te,ne,re,ae,ie,oe,ce,le,se)}},1191:function(e,t,n){"use strict";(function(e,r){var a=n(5),i=n.n(a),o=n(16),c=n.n(o),l=n(51),s=n.n(l),u=n(53),d=n.n(u),f=n(237),g=n.n(f),p=n(68),m=n.n(p),h=n(69),v=n.n(h),b=n(49),k=n.n(b),E=n(14),_=n(2),y=n(0),O=n(377),S=n(3),j=n(45),w=n.n(j),C=n(10),N=n(13),R=n(7),x=n(17),z=n(234),A=n(192),T=n(37),L=n(1192),M=n(1198),D=n(235);function P(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=k()(e);if(t){var a=k()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return v()(this,n)}}var I=function(t){m()(SetupUsingGCP,t);var n,a=P(SetupUsingGCP);function SetupUsingGCP(t){var n;s()(this,SetupUsingGCP),n=a.call(this,t);var r=e._googlesitekitLegacyData.admin.connectURL,i=e._googlesitekitLegacyData.setup,o=i.isAuthenticated,c=i.hasSearchConsoleProperty,l=i.isSiteKitConnected,u=i.isVerified,d=i.needReauthenticate;return n.state={isAuthenticated:o,isVerified:u,needReauthenticate:d,hasSearchConsoleProperty:c,hasSearchConsolePropertyFromTheStart:c,connectURL:r,errorMsg:"",isSiteKitConnected:l,completeSetup:!1},n.siteConnectedSetup=n.siteConnectedSetup.bind(g()(n)),n.siteVerificationSetup=n.siteVerificationSetup.bind(g()(n)),n.searchConsoleSetup=n.searchConsoleSetup.bind(g()(n)),n.resetAndRestart=n.resetAndRestart.bind(g()(n)),n.completeSetup=n.completeSetup.bind(g()(n)),n.setErrorMessage=n.setErrorMessage.bind(g()(n)),n.onButtonClick=n.onButtonClick.bind(g()(n)),n}return d()(SetupUsingGCP,[{key:"resetAndRestart",value:(n=c()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,w.a.set("core","site","reset");case 2:return e.next=4,Object(T.b)();case 4:this.setState({isSiteKitConnected:!1,isAuthenticated:!1,isVerified:!1,hasSearchConsoleProperty:!1,completeSetup:!1,errorMsg:""});case 5:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"completeSetup",value:function(){this.setState({completeSetup:!0})}},{key:"siteConnectedSetup",value:function(e){this.setState({isSiteKitConnected:e})}},{key:"siteVerificationSetup",value:function(e){this.setState({isVerified:e})}},{key:"searchConsoleSetup",value:function(e){this.setState({hasSearchConsoleProperty:e})}},{key:"isSetupFinished",value:function(){var e=this.state,t=e.isSiteKitConnected,n=e.isAuthenticated,r=e.isVerified,a=e.hasSearchConsoleProperty,i=e.completeSetup;return t&&n&&r&&a&&i}},{key:"setErrorMessage",value:function(e){this.setState({errorMsg:e})}},{key:"getApplicableSteps",value:function(){var e,t=L.a,n=Object.keys(t);for(e=0;e<n.length;e++)t[n[e]].isApplicable(this.state)||delete t[n[e]];return t}},{key:"currentStep",value:function(e){var t,n=Object.keys(e);for(t=0;t<n.length-1;t++)if(!e[n[t]].isCompleted(this.state))return n[t];return n[t]}},{key:"stepStatus",value:function(e,t){return e[t].isCompleted(this.state)?"completed":t===this.currentStep(e)?"inprogress":""}},{key:"onButtonClick",value:function(){var e=this.state.connectURL;document.location=e}},{key:"render",value:function(){var t=this,n=this.state,a=n.isAuthenticated,i=n.isVerified,o=n.needReauthenticate,c=n.hasSearchConsoleProperty,l=n.connectURL,s=n.isSiteKitConnected,u=this.props,d=u.canSetup,f=u.redirectURL;this.isSetupFinished()&&Object(E.delay)((function(){e.location.replace(f)}),500,"later");var g=this.getApplicableSteps(),p=this.currentStep(g),m=g[p].Component,h=r.createElement(m,{siteConnectedSetup:this.siteConnectedSetup,connectURL:l,siteVerificationSetup:this.siteVerificationSetup,searchConsoleSetup:this.searchConsoleSetup,completeSetup:this.completeSetup,isSiteKitConnected:s,isAuthenticated:a,isVerified:i,needReauthenticate:o,hasSearchConsoleProperty:c,setErrorMessage:this.setErrorMessage,resetAndRestart:g.clientCredentials?this.resetAndRestart:void 0}),v=d,b=!v&&!a;return r.createElement(y.Fragment,null,r.createElement(z.a,null,r.createElement(D.a,null)),r.createElement("div",{className:"googlesitekit-wizard"},r.createElement(x.e,null,r.createElement(x.k,null,r.createElement(x.a,{size:12},r.createElement(A.a,null,r.createElement("section",{className:"googlesitekit-wizard-progress"},r.createElement(x.e,null,r.createElement(x.k,null,v&&r.createElement(x.a,{size:12},r.createElement("div",{className:"googlesitekit-wizard-progress__steps"},Object.keys(g).map((function(e,n){return r.createElement(M.a,{key:g[e].title,currentStep:p===e,title:g[e].title,step:n+1,status:t.stepStatus(g,e),warning:g[e].warning,error:g[e].error,stepKey:e})})))))),b&&r.createElement("div",{className:"googlesitekit-setup__footer"},r.createElement(x.e,null,r.createElement(x.k,null,r.createElement(x.a,{size:12},r.createElement("h1",{className:"googlesitekit-setup__title"},Object(_.__)("Authenticate Site Kit","google-site-kit")),r.createElement("p",{className:"googlesitekit-setup__description"},Object(_.__)("Please sign into your Google account to begin.","google-site-kit")),r.createElement(C.Button,{href:"#",onClick:this.onButtonClick},Object(_._x)("Sign in with Google","Service name","google-site-kit"))))))),v&&h))))))}}]),SetupUsingGCP}(y.Component);t.a=Object(O.a)(Object(S.withSelect)((function(e){return{canSetup:e(R.a).hasCapability(R.M),redirectURL:e(N.c).getAdminURL("googlesitekit-dashboard",{notification:"authentication_success"})}})))(I)}).call(this,n(28),n(4))},1192:function(e,t,n){"use strict";var r=n(2),a=n(1193),i=n(1194),o=n(1196),c=n(1197),l={authentication:{title:Object(r.__)("Authenticate","google-site-kit"),required:!0,isApplicable:function(){return!0},isCompleted:function(e){return e.isSiteKitConnected&&e.isAuthenticated&&!e.needReauthenticate},Component:a.a},verification:{title:Object(r.__)("Verify URL","google-site-kit"),required:!0,isApplicable:function(){return!0},isCompleted:function(e){return e.isSiteKitConnected&&e.isAuthenticated&&e.isVerified},Component:i.a},seachConsoleProperty:{title:Object(r.__)("Connect Search Console","google-site-kit"),required:!0,isApplicable:function(){return!0},isCompleted:function(e){return e.isSiteKitConnected&&e.isAuthenticated&&e.isVerified&&e.hasSearchConsoleProperty},Component:o.a},completeSetup:{title:Object(r._x)("Finish","complete module setup","google-site-kit"),required:!1,isApplicable:function(){return!0},isCompleted:function(e){return e.isSiteKitConnected&&e.isAuthenticated&&e.isVerified&&e.hasSearchConsoleProperty},Component:c.a}};t.a=l},1193:function(e,t,n){"use strict";(function(e){var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(51),l=n.n(c),s=n(53),u=n.n(s),d=n(237),f=n.n(d),g=n(68),p=n.n(g),m=n(69),h=n.n(m),v=n(49),b=n.n(v),k=n(1),E=n.n(k),_=n(2),y=n(0),O=n(10),S=n(17),j=n(218),w=n(22),C=n(37),N=n(9);function R(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=b()(e);if(t){var a=b()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return h()(this,n)}}var x=function(t){p()(WizardStepAuthentication,t);var n,r=R(WizardStepAuthentication);function WizardStepAuthentication(e){var t;return l()(this,WizardStepAuthentication),(t=r.call(this,e)).onButtonClick=t.onButtonClick.bind(f()(t)),t}return u()(WizardStepAuthentication,[{key:"onButtonClick",value:(n=o()(a.a.mark((function e(){var t,n,r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.props,n=t.connectURL,r=t.isSiteKitConnected,e.next=3,Promise.all([Object(C.f)("start_user_setup",!0),Object(N.I)("".concat(w.s,"_setup"),"start_user_setup","custom-oauth")]);case 3:if(r){e.next=6;break}return e.next=6,Promise.all([Object(C.f)("start_site_setup",!0),Object(N.I)("".concat(w.s,"_setup"),"start_site_setup","custom-oauth")]);case 6:document.location=n;case 7:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"render",value:function(){var t=this.props,n=t.needReauthenticate,r=t.resetAndRestart;return e.createElement("section",{className:"googlesitekit-wizard-step googlesitekit-wizard-step--two"},e.createElement(S.e,null,e.createElement(S.k,null,e.createElement(S.a,{size:12},e.createElement("h2",{className:" googlesitekit-heading-3 googlesitekit-wizard-step__title "},Object(_.__)("Authenticate with Google","google-site-kit")),e.createElement("p",null,Object(_.__)("Please sign into your Google account to begin.","google-site-kit")),n&&e.createElement("p",{className:"googlesitekit-error-text"},Object(_.__)("You did not grant access to one or more of the requested scopes. Please grant all scopes that you are prompted for.","google-site-kit")),e.createElement("p",null,e.createElement(O.Button,{onClick:this.onButtonClick},Object(_._x)("Sign in with Google","Service name","google-site-kit")),r&&e.createElement(O.Button,{className:"googlesitekit-wizard-step__back",tertiary:!0,onClick:r},Object(_.__)("Back","google-site-kit"))),e.createElement("div",{className:"googlesitekit-wizard-step__action googlesitekit-wizard-step__action--justify"},e.createElement(j.a,null))))))}}]),WizardStepAuthentication}(y.Component);x.propTypes={connectURL:E.a.string.isRequired,resetAndRestart:E.a.func},t.a=x}).call(this,n(4))},1194:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(51),o=n.n(i),c=n(53),l=n.n(c),s=n(68),u=n.n(s),d=n(69),f=n.n(d),g=n(49),p=n.n(g),m=n(1),h=n.n(m),v=n(0),b=n(17),k=n(1195);function E(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=p()(e);if(t){var a=p()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return f()(this,n)}}var _=function(t){u()(WizardStepVerification,t);var n=E(WizardStepVerification);function WizardStepVerification(){return o()(this,WizardStepVerification),n.apply(this,arguments)}return l()(WizardStepVerification,[{key:"render",value:function(){var t=!this.props.isVerified;return e.createElement("section",{className:"googlesitekit-wizard-step googlesitekit-wizard-step--three"},e.createElement(b.e,null,e.createElement(b.k,null,e.createElement(b.a,{size:12},e.createElement(k.a,a()({shouldSetup:t},this.props))))))}}]),WizardStepVerification}(v.Component);_.propTypes={siteVerificationSetup:h.a.func.isRequired},t.a=_}).call(this,n(4))},1195:function(e,t,n){"use strict";(function(e,r){var a=n(5),i=n.n(a),o=n(16),c=n.n(o),l=n(51),s=n.n(l),u=n(53),d=n.n(u),f=n(237),g=n.n(f),p=n(68),m=n.n(p),h=n(69),v=n.n(h),b=n(49),k=n.n(b),E=n(1),_=n.n(E),y=n(2),O=n(0),S=n(45),j=n.n(S),w=n(10),C=n(9);function N(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=k()(e);if(t){var a=k()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return v()(this,n)}}var R=function(t){m()(SiteVerification,t);var n,a,o=N(SiteVerification);function SiteVerification(e){var t;s()(this,SiteVerification);var n=(t=o.call(this,e)).props,r=n.isAuthenticated,a=n.shouldSetup;return t.state={loading:r&&a,loadingMsg:Object(y.__)("Getting your verified sites…","google-site-kit"),siteURL:" ",selectedURL:"",errorCode:!1,errorMsg:""},t.onProceed=t.onProceed.bind(g()(t)),t}return d()(SiteVerification,[{key:"componentDidMount",value:function(){var e=this.props,t=e.isAuthenticated,n=e.shouldSetup;t&&n&&this.requestSitePropertyList()}},{key:"requestSitePropertyList",value:function(){var t=this,n=this.props.setErrorMessage;c()(i.a.mark((function r(){var a,o,c,l,s;return i.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,j.a.get("modules","site-verification","verification",void 0,{useCache:!1});case 3:if(a=r.sent,o=a.verified,c=a.identifier,!o){r.next=17;break}return r.next=9,Object(C.I)("verification_setup","verification_check_true");case 9:return r.next=11,t.insertSiteVerification(c);case 11:if(!0!==r.sent.verified){r.next=15;break}return t.props.siteVerificationSetup(!0),r.abrupt("return",!0);case 15:r.next=19;break;case 17:return r.next=19,Object(C.I)("verification_setup","verification_check_false");case 19:t.setState({loading:!1,siteURL:c}),r.next=28;break;case 22:r.prev=22,r.t0=r.catch(0),l=r.t0.message,Object(C.L)(r.t0.message)&&(s=JSON.parse(r.t0.message),l=s.error.message||r.t0.message),n(l),t.setState({loading:!1,errorCode:r.t0.code,errorMsg:l,siteURL:e._googlesitekitLegacyData.admin.siteURL});case 28:case"end":return r.stop()}}),r,null,[[0,22]])})))()}},{key:"insertSiteVerification",value:(a=c()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,j.a.set("modules","site-verification","verification",{siteURL:t});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)}))),function(e){return a.apply(this,arguments)})},{key:"onProceed",value:(n=c()(i.a.mark((function t(){var n,r,a,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=this.props.setErrorMessage,r=this.state.siteURL?this.state.siteURL:e._googlesitekitLegacyData.admin.siteURL,n(""),this.setState({loading:!0,loadingMsg:Object(y.__)("Verifying…","google-site-kit"),errorCode:!1,errorMsg:""}),t.prev=4,t.next=7,this.insertSiteVerification(r);case 7:!0===t.sent.verified&&(Object(C.I)("verification_setup","verification_insert_tag"),this.props.siteVerificationSetup(!0)),t.next=17;break;case 11:t.prev=11,t.t0=t.catch(4),a=t.t0.message,Object(C.L)(t.t0.message)&&(o=JSON.parse(t.t0.message),a=o.error.message||t.t0.message),n(a),this.setState({loading:!1,errorCode:t.t0.code,errorMsg:a});case 17:case"end":return t.stop()}}),t,this,[[4,11]])}))),function(){return n.apply(this,arguments)})},{key:"renderForm",value:function(){var e=this.state,t=e.loading,n=e.loadingMsg,a=e.siteURL,i=r.createElement(O.Fragment,null,n&&r.createElement("p",null,n),r.createElement(w.ProgressBar,null));return t?i:r.createElement(O.Fragment,null,r.createElement("div",{className:"googlesitekit-wizard-step__inputs"},r.createElement(w.TextField,{label:Object(y.__)("Website Address","google-site-kit"),name:"siteProperty",outlined:!0,value:a})),r.createElement("div",{className:"googlesitekit-wizard-step__action googlesitekit-wizard-step__action--justify"},r.createElement(w.Button,{onClick:this.onProceed},Object(y.__)("Continue","google-site-kit"))))}},{key:"render",value:function(){var e=this.props,t=e.isAuthenticated,n=e.shouldSetup,a=this.state.errorMsg;return n?r.createElement(O.Fragment,null,r.createElement("h2",{className:" googlesitekit-heading-3 googlesitekit-wizard-step__title "},Object(y.__)("Verify URL","google-site-kit")),r.createElement("p",{className:"googlesitekit-wizard-step__text"},Object(y.__)("We will need to verify your URL for Site Kit.","google-site-kit")),a&&0<a.length&&r.createElement("p",{className:"googlesitekit-error-text"},a),t&&this.renderForm()):SiteVerification.renderSetupDone()}}],[{key:"renderSetupDone",value:function(){return r.createElement(O.Fragment,null,r.createElement("h2",{className:" googlesitekit-heading-3 googlesitekit-wizard-step__title "},Object(y.__)("Verify URL","google-site-kit")),r.createElement("p",{className:"googlesitekit-wizard-step__text"},Object(y.__)("Congratulations, your site has been verified!","google-site-kit")))}}]),SiteVerification}(O.Component);R.propTypes={isAuthenticated:_.a.bool.isRequired,shouldSetup:_.a.bool.isRequired,siteVerificationSetup:_.a.func.isRequired,completeSetup:_.a.func,setErrorMessage:_.a.func.isRequired},t.a=R}).call(this,n(28),n(4))},1196:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(51),o=n.n(i),c=n(53),l=n.n(c),s=n(68),u=n.n(s),d=n(69),f=n.n(d),g=n(49),p=n.n(g),m=n(1),h=n.n(m),v=n(0),b=n(17),k=n(836);function E(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=p()(e);if(t){var a=p()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return f()(this,n)}}var _=function(t){u()(WizardStepSearchConsoleProperty,t);var n=E(WizardStepSearchConsoleProperty);function WizardStepSearchConsoleProperty(){return o()(this,WizardStepSearchConsoleProperty),n.apply(this,arguments)}return l()(WizardStepSearchConsoleProperty,[{key:"render",value:function(){var t=this.props,n=t.isVerified,r=t.hasSearchConsoleProperty,i=n&&!r;return e.createElement("section",{className:"googlesitekit-wizard-step googlesitekit-wizard-step--four"},e.createElement(b.e,null,e.createElement(b.k,null,e.createElement(b.a,{size:12},i?e.createElement(k.a,a()({shouldSetup:i},this.props)):k.a.connected()))))}}]),WizardStepSearchConsoleProperty}(v.Component);_.propTypes={searchConsoleSetup:h.a.func.isRequired},t.a=_}).call(this,n(4))},1197:function(e,t,n){"use strict";(function(e,r){var a=n(51),i=n.n(a),o=n(53),c=n.n(o),l=n(68),s=n.n(l),u=n(69),d=n.n(u),f=n(49),g=n.n(f),p=n(1),m=n.n(p),h=n(2),v=n(0),b=n(10),k=n(17),E=n(9);function _(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=g()(e);if(t){var a=g()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return d()(this,n)}}var y=function(t){s()(WizardStepCompleteSetup,t);var n=_(WizardStepCompleteSetup);function WizardStepCompleteSetup(t){var r;return i()(this,WizardStepCompleteSetup),r=n.call(this,t),e._googlesitekitLegacyData.setup.hasSearchConsoleProperty?Object(E.I)("plugin_setup","user_verified"):Object(E.I)("plugin_setup","site_verified"),r}return c()(WizardStepCompleteSetup,[{key:"render",value:function(){return r.createElement("section",{className:"googlesitekit-wizard-step googlesitekit-wizard-step--five"},r.createElement(k.e,null,r.createElement(k.k,null,r.createElement(k.a,{size:12},r.createElement("h2",{className:" googlesitekit-heading-3 googlesitekit-wizard-step__title "},Object(h.__)("Congratulations!","google-site-kit")),r.createElement("p",null,Object(h.__)("You successfully completed the Site Kit setup and connected Search Console. Check the dashboard for more services to connect.","google-site-kit")),r.createElement("div",{className:"googlesitekit-wizard-step__action"},r.createElement(b.Button,{id:"wizard-step-five-proceed",onClick:this.props.completeSetup},Object(h.__)("Go to Dashboard","google-site-kit")))))))}}]),WizardStepCompleteSetup}(v.Component);y.propTypes={completeSetup:m.a.func.isRequired},t.a=y}).call(this,n(28),n(4))},1198:function(e,t,n){"use strict";(function(e){var r=n(51),a=n.n(r),i=n(53),o=n.n(i),c=n(68),l=n.n(c),s=n(69),u=n.n(s),d=n(49),f=n.n(d),g=n(1),p=n.n(g),m=n(11),h=n.n(m),v=n(0),b=n(312),k=n(579);function E(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var a=f()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return u()(this,n)}}var _=function(t){l()(WizardProgressStep,t);var n=E(WizardProgressStep);function WizardProgressStep(){return a()(this,WizardProgressStep),n.apply(this,arguments)}return o()(WizardProgressStep,[{key:"render",value:function(){var t=this.props,n=t.currentStep,r=t.step,a=t.title,i=t.status,o=t.warning,c=t.error,l=t.stepKey,s=i;o?s="warning":c&&(s="error");var u=!1;switch(s){case"warning":case"error":u=e.createElement(b.a,{height:"12",width:"2"});break;case"completed":u=e.createElement(k.a,{height:"12",width:"16"})}return e.createElement("div",{className:h()("googlesitekit-wizard-progress-step","googlesitekit-wizard-progress-step--".concat(r),"googlesitekit-wizard-progress-step--".concat(l),{"googlesitekit-wizard-progress-step--current":n})},e.createElement("div",{className:"googlesitekit-wizard-progress-step__number-wrapper"},e.createElement("div",{className:h()("googlesitekit-wizard-progress-step__number","googlesitekit-wizard-progress-step__number--".concat(s))},e.createElement("span",{className:h()("googlesitekit-wizard-progress-step__number-text","googlesitekit-wizard-progress-step__number-text--".concat(s))},r),u&&e.createElement("span",{className:h()("googlesitekit-wizard-progress-step__number-icon","googlesitekit-wizard-progress-step__number-icon--".concat(s))},u))),e.createElement("p",{className:"googlesitekit-wizard-progress-step__text"},a))}}]),WizardProgressStep}(v.Component);_.propTypes={currentStep:p.a.bool.isRequired,step:p.a.number.isRequired,title:p.a.string,status:p.a.string,warning:p.a.bool,error:p.a.bool},_.defaultProps={title:"",status:"",warning:!1,error:!1,removeFirstStep:!1},t.a=_}).call(this,n(4))},121:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(219),a=n(14),i=n(0);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=Object(r.b)((function(){return a.debounce.apply(void 0,t)}),t);return Object(i.useEffect)((function(){return function(){return o.cancel()}}),[o]),o}},123:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(21),a=n.n(r),i=n(6),o=n.n(i),c=n(25),l=n.n(c),s=n(1),u=n.n(s),d=n(11),f=n.n(d);function Cell(t){var n,r=t.className,i=t.alignTop,c=t.alignMiddle,s=t.alignBottom,u=t.alignRight,d=t.alignLeft,g=t.smAlignRight,p=t.mdAlignRight,m=t.lgAlignRight,h=t.smSize,v=t.smStart,b=t.smOrder,k=t.mdSize,E=t.mdStart,_=t.mdOrder,y=t.lgSize,O=t.lgStart,S=t.lgOrder,j=t.size,w=t.children,C=l()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",a()({},C,{className:f()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":i,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":s,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":g,"mdc-layout-grid__cell--align-right-tablet":p,"mdc-layout-grid__cell--align-right-desktop":m},o()(n,"mdc-layout-grid__cell--span-".concat(j),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(y,"-desktop"),12>=y&&y>0),o()(n,"mdc-layout-grid__cell--start-".concat(O,"-desktop"),12>=O&&O>0),o()(n,"mdc-layout-grid__cell--order-".concat(S,"-desktop"),12>=S&&S>0),o()(n,"mdc-layout-grid__cell--span-".concat(k,"-tablet"),8>=k&&k>0),o()(n,"mdc-layout-grid__cell--start-".concat(E,"-tablet"),8>=E&&E>0),o()(n,"mdc-layout-grid__cell--order-".concat(_,"-tablet"),8>=_&&_>0),o()(n,"mdc-layout-grid__cell--span-".concat(h,"-phone"),4>=h&&h>0),o()(n,"mdc-layout-grid__cell--start-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--order-".concat(b,"-phone"),4>=b&&b>0),n))}),w)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},124:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.className,i=t.children,c=o()(t,["className","children"]);return e.createElement("div",a()({ref:n,className:u()("mdc-layout-grid__inner",r)},c),i)}));f.displayName="Row",f.propTypes={className:l.a.string,children:l.a.node},f.defaultProps={className:""},t.a=f}).call(this,n(4))},125:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.alignLeft,i=t.fill,c=t.className,l=t.children,s=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",a()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":s,"mdc-layout-grid--fill":i})},d,{ref:n}),l)}));f.displayName="Grid",f.propTypes={alignLeft:l.a.bool,fill:l.a.bool,className:l.a.string,collapsed:l.a.bool,children:l.a.node},f.defaultProps={className:""},t.a=f}).call(this,n(4))},126:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",a({viewBox:"0 0 13 13"},e),i)}},127:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",a({viewBox:"0 0 13 13"},e),i)}},128:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},1288:function(e,t,n){"use strict";n.r(t),function(e,t){var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(332),l=n(144),s=n(37),u=n(224),d=n(1179),f=n(22);Object(c.a)(o()(a.a.mark((function n(){var r;return a.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!e._googlesitekitLegacyData.admin.resetSession){n.next=3;break}return n.next=3,Object(s.b)();case 3:(r=document.getElementById("js-googlesitekit-splash"))&&Object(l.render)(t.createElement(u.a,{viewContext:f.s},t.createElement(d.a,null)),r);case 5:case"end":return n.stop()}}),n)}))))}.call(this,n(28),n(4))},129:function(e,t,n){"use strict";n.d(t,"a",(function(){return m})),n.d(t,"c",(function(){return v})),n.d(t,"b",(function(){return b}));var r=n(25),a=n.n(r),i=n(6),o=n.n(i),c=n(5),l=n.n(c),s=n(12),u=n.n(s),d=n(3),f=n.n(d),g=n(37),p=n(9),m=function(e){var t;u()(e,"storeName is required to create a snapshot store.");var n={},r={deleteSnapshot:l.a.mark((function e(){var t;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"DELETE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})),restoreSnapshot:l.a.mark((function e(){var t,n,r,a,i,o,c=arguments;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},n=t.clearAfterRestore,r=void 0===n||n,e.next=4,{payload:{},type:"RESTORE_SNAPSHOT"};case 4:if(a=e.sent,i=a.cacheHit,o=a.value,!i){e.next=13;break}return e.next=10,{payload:{snapshot:o},type:"SET_STATE_FROM_SNAPSHOT"};case 10:if(!r){e.next=13;break}return e.next=13,{payload:{},type:"DELETE_SNAPSHOT"};case 13:return e.abrupt("return",i);case 14:case"end":return e.stop()}}),e)})),createSnapshot:l.a.mark((function e(){var t;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"CREATE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))},i=(t={},o()(t,"DELETE_SNAPSHOT",(function(){return Object(g.c)("datastore::cache::".concat(e))})),o()(t,"CREATE_SNAPSHOT",Object(d.createRegistryControl)((function(t){return function(){return Object(g.f)("datastore::cache::".concat(e),t.stores[e].store.getState())}}))),o()(t,"RESTORE_SNAPSHOT",(function(){return Object(g.d)("datastore::cache::".concat(e),p.b)})),t);return{initialState:n,actions:r,controls:i,reducer:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1?arguments[1]:void 0,r=t.type,i=t.payload;switch(r){case"SET_STATE_FROM_SNAPSHOT":var o=i.snapshot,c=(o.error,a()(o,["error"]));return c;default:return e}}}},h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.a;return Object.values(e.stores).filter((function(e){return Object.keys(e.getActions()).includes("restoreSnapshot")}))},v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.a;return Promise.all(h(e).map((function(e){return e.getActions().createSnapshot()})))},b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.a;return Promise.all(h(e).map((function(e){return e.getActions().restoreSnapshot()})))}},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var r="core/site",a="primary",i="secondary"},130:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(14),a=function(e){return Object(r.isFinite)(e)?e:0}},134:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(38),l=n(2),s=n(20),u=n(34);function SourceLink(t){var n=t.name,r=t.href,a=t.className,i=t.external;return Object(u.a)()?null:e.createElement("div",{className:o()("googlesitekit-source-link",a)},Object(c.a)(Object(l.sprintf)(
/* translators: %s: source link */
Object(l.__)("Source: %s","google-site-kit"),"<a>".concat(n,"</a>")),{a:e.createElement(s.a,{key:"link",href:r,external:i})}))}SourceLink.propTypes={name:a.a.string,href:a.a.string,className:a.a.string,external:a.a.bool},SourceLink.defaultProps={name:"",href:"",className:"",external:!1},t.a=SourceLink}).call(this,n(4))},136:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"d",(function(){return i})),n.d(t,"c",(function(){return o}));function r(e){var t=e.format,n=void 0===t?"small":t,r=e.hasErrorOrWarning,a=e.hasSmallImageSVG,o=e.hasWinImageSVG,c={smSize:4,mdSize:8,lgSize:12},l=i(n);return Object.keys(c).forEach((function(e){var t=c[e];r&&(t-=1),a&&(t-=1),o&&0<t-l[e]&&(t-=l[e]),c[e]=t})),c}var a=function(e){switch(e){case"small":return{};case"larger":return{smOrder:2,mdOrder:2,lgOrder:1};default:return{smOrder:2,mdOrder:1}}},i=function(e){switch(e){case"smaller":return{smSize:4,mdSize:2,lgSize:2};case"larger":return{smSize:4,mdSize:8,lgSize:7};default:return{smSize:4,mdSize:2,lgSize:4}}},o=function(e){switch(e){case"larger":return{smOrder:1,mdOrder:1,lgOrder:2};default:return{smOrder:1,mdOrder:2}}}},139:function(e,t,n){"use strict";var r=n(0),a=Object(r.createContext)(!1);t.a=a},148:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M3.332 24.75h21.335c2.182 0 3.542-2.366 2.451-4.25L16.451 2.07C15.36.184 12.64.184 11.549 2.07L.882 20.5c-1.091 1.884.269 4.25 2.45 4.25zM14 14.833a1.42 1.42 0 01-1.417-1.416v-2.834c0-.779.638-1.416 1.417-1.416.78 0 1.417.637 1.417 1.416v2.834A1.42 1.42 0 0114 14.833zm1.417 5.667h-2.834v-2.833h2.834V20.5z",fill:"currentColor",fillRule:"nonzero"});t.a=function SvgError(e){return r.createElement("svg",a({viewBox:"0 0 28 25"},e),i)}},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},155:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("path",{d:"M2.253 12.252l7.399 5.658A13.055 13.055 0 009 22c0 1.43.229 2.805.652 4.09l-7.4 5.658A22.02 22.02 0 010 22c0-3.506.81-6.814 2.253-9.748z",fill:"#FBBC05"}),r.createElement("path",{d:"M9.652 17.91l-7.4-5.658A21.935 21.935 0 0122 0c5.6 0 10.6 2.1 14.5 5.5l-6.4 6.4C27.9 10.1 25.1 9 22 9c-5.77 0-10.64 3.725-12.348 8.91z",fill:"#EA4335"}),r.createElement("path",{d:"M2.25 31.742l7.396-5.67A12.975 12.975 0 0022 35c6.1 0 10.7-3.1 11.8-8.5H22V18h20.5c.3 1.3.5 2.7.5 4 0 14-10 22-21 22A21.935 21.935 0 012.25 31.742z",fill:"#34A853"}),r.createElement("path",{d:"M36.34 38.52l-7.025-5.437c2.297-1.45 3.895-3.685 4.485-6.583H22V18h20.5c.3 1.3.5 2.7.5 4 0 7.17-2.623 12.767-6.66 16.52z",fill:"#4285F4"}));t.a=function SvgLogoG(e){return r.createElement("svg",a({viewBox:"0 0 43 44"},e),i)}},158:function(e,t,n){"use strict";var r=n(0),a=n(57),i=Object(r.createContext)(a.a);t.a=i},160:function(e,t,n){"use strict";var r=n(139),a=(r.a.Consumer,r.a.Provider);t.a=a},161:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(3),a=n(23),i=function(e){return"notification/".concat(e,"/viewed")};function o(e){return Object(r.useSelect)((function(t){return!!t(a.b).getValue(i(e))}),[e])}o.getKey=i},164:function(e,t,n){"use strict";(function(e){var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(1),l=n.n(c),s=n(0),u=n(20),d=n(9),f=n(18);function HelpMenuLink(t){var n=t.children,r=t.href,i=t.gaEventLabel,c=Object(f.a)(),l=Object(s.useCallback)(o()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!i){e.next=3;break}return e.next=3,Object(d.I)("".concat(c,"_headerbar_helpmenu"),"click_outgoing_link",i);case 3:case"end":return e.stop()}}),e)}))),[i,c]);return e.createElement("li",{className:"googlesitekit-help-menu-link mdc-list-item",role:"none"},e.createElement(u.a,{className:"mdc-list-item__text",href:r,external:!0,hideExternalIndicator:!0,role:"menuitem",onClick:l},n))}HelpMenuLink.propTypes={children:l.a.node.isRequired,href:l.a.string.isRequired,gaEventLabel:l.a.string},t.a=HelpMenuLink}).call(this,n(4))},167:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notifications}));var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(3),l=n(18),s=n(41),u=n(283);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Notifications(t){var n,r=t.areaSlug,i=t.groupID,o=void 0===i?s.c.DEFAULT:i,f=Object(l.a)(),g=Object(c.useSelect)((function(e){return e(s.a).getQueuedNotifications(f,o)}));if(void 0===(null==g?void 0:g[0])||(null==g||null===(n=g[0])||void 0===n?void 0:n.areaSlug)!==r)return null;var p=g[0],m=p.id,h=p.Component,v=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},Object(u.a)(m));return e.createElement(h,v)}Notifications.propTypes={viewContext:o.a.string,areaSlug:o.a.string}}).call(this,n(4))},17:function(e,t,n){"use strict";var r=n(254);n.d(t,"i",(function(){return r.a}));var a=n(319);n.d(t,"f",(function(){return a.a}));var i=n(320);n.d(t,"h",(function(){return i.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var l=n(91),s=n.n(l);n.d(t,"b",(function(){return s.a})),n.d(t,"c",(function(){return l.DialogContent})),n.d(t,"d",(function(){return l.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},172:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GenericErrorHandlerActions}));var r=n(1),a=n.n(r),i=n(2),o=n(20),c=n(194);function GenericErrorHandlerActions(t){var n=t.message,r=t.componentStack;return e.createElement("div",{className:"googlesitekit-generic-error-handler-actions"},e.createElement(c.a,{message:n,componentStack:r}),e.createElement(o.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0},Object(i.__)("Report this problem","google-site-kit")))}GenericErrorHandlerActions.propTypes={message:a.a.string,componentStack:a.a.string}}).call(this,n(4))},173:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(22),a=function(e){return r.f.includes(e)}},18:function(e,t,n){"use strict";var r=n(0),a=n(61);t.a=function(){return Object(r.useContext)(a.b)}},183:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LoadingWrapper}));var r=n(6),a=n.n(r),i=n(25),o=n.n(i),c=n(1),l=n.n(c),s=n(44);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function LoadingWrapper(t){var n=t.loading,r=t.children,a=o()(t,["loading","children"]);return n?e.createElement(s.a,a):r}LoadingWrapper.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({loading:l.a.bool,children:l.a.node},s.a.propTypes)}).call(this,n(4))},189:function(e,t,n){"use strict";(function(e){function Title(t){var n=t.title;return e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n)}n.d(t,"a",(function(){return Title}))}).call(this,n(4))},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r="core/modules",a="insufficient_module_dependencies"},191:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notification}));var r=n(15),a=n.n(r),i=n(1),o=n.n(i),c=n(0),l=n(282),s=n(161),u=n(73);function Notification(t){var n=t.id,r=t.className,i=t.gaTrackingEventArgs,o=t.children,d=t.onView,f=Object(c.useRef)(),g=Object(s.a)(n),p=Object(u.a)(n,null==i?void 0:i.category),m=Object(c.useState)(!1),h=a()(m,2),v=h[0],b=h[1];return Object(c.useEffect)((function(){!v&&g&&(p.view(null==i?void 0:i.label,null==i?void 0:i.value),null==d||d(),b(!0))}),[g,p,v,i,d]),e.createElement("section",{id:n,ref:f,className:r},o,!g&&e.createElement(l.a,{id:n,observeRef:f,threshold:.5}))}Notification.propTypes={id:o.a.string,className:o.a.string,gaTrackingEventArgs:o.a.shape({category:o.a.string,label:o.a.string,value:o.a.string}),children:o.a.node,onView:o.a.func}}).call(this,n(4))},192:function(e,t,n){"use strict";(function(e){var r=n(51),a=n.n(r),i=n(53),o=n.n(i),c=n(68),l=n.n(c),s=n(69),u=n.n(s),d=n(49),f=n.n(d),g=n(1),p=n.n(g),m=n(11),h=n.n(m),v=n(0),b=n(329),k=n(330);function E(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var a=f()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return u()(this,n)}}var _=function(t){l()(Layout,t);var n=E(Layout);function Layout(){return a()(this,Layout),n.apply(this,arguments)}return o()(Layout,[{key:"render",value:function(){var t=this.props,n=t.header,r=t.footer,a=t.children,i=t.title,o=t.badge,c=t.headerCTALabel,l=t.headerCTALink,s=t.footerCTALabel,u=t.footerCTALink,d=t.footerContent,f=t.className,g=t.fill,p=t.relative,m=t.rounded,v=void 0!==m&&m,E=t.transparent,_=void 0!==E&&E;return e.createElement("div",{className:h()("googlesitekit-layout",f,{"googlesitekit-layout--fill":g,"googlesitekit-layout--relative":p,"googlesitekit-layout--rounded":v,"googlesitekit-layout--transparent":_})},n&&e.createElement(b.a,{title:i,badge:o,ctaLabel:c,ctaLink:l}),a,r&&e.createElement(k.a,{ctaLabel:s,ctaLink:u,footerContent:d}))}}]),Layout}(v.Component);_.propTypes={header:p.a.bool,footer:p.a.bool,children:p.a.node.isRequired,title:p.a.string,badge:p.a.node,headerCTALabel:p.a.string,headerCTALink:p.a.string,footerCTALabel:p.a.string,footerCTALink:p.a.string,footerContent:p.a.node,className:p.a.string,fill:p.a.bool,relative:p.a.bool,rounded:p.a.bool,transparent:p.a.bool},_.defaultProps={header:!1,footer:!1,title:"",badge:null,headerCTALabel:"",headerCTALink:"",footerCTALabel:"",footerCTALink:"",footerContent:null,className:"",fill:!1,relative:!1},t.a=_}).call(this,n(4))},194:function(e,t,n){"use strict";(function(e){var r=n(15),a=n.n(r),i=n(195),o=n.n(i),c=n(1),l=n.n(c),s=n(0),u=n(2),d=n(266),f=n(423),g=n(424),p=n(10);function ReportErrorButton(t){var n=t.message,r=t.componentStack,i=Object(s.useState)(!1),c=a()(i,2),l=c[0],m=c[1];return e.createElement(p.Button,{"aria-label":l?Object(u.__)("Error message copied to clipboard. Click to copy the error message again.","google-site-kit"):void 0,onClick:function(){o()("`".concat(n,"\n").concat(r,"`")),m(!0)},trailingIcon:e.createElement(d.a,{className:"mdc-button__icon",icon:l?f.a:g.a})},l?Object(u.__)("Copied to clipboard","google-site-kit"):Object(u.__)("Copy error contents","google-site-kit"))}ReportErrorButton.propTypes={message:l.a.string,componentStack:l.a.string},t.a=ReportErrorButton}).call(this,n(4))},196:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationError}));var r=n(17),a=n(222),i=n(189);function NotificationError(t){var n=t.title,o=t.description,c=t.actions;return e.createElement(r.e,null,e.createElement(r.k,null,e.createElement(r.a,{smSize:3,mdSize:7,lgSize:11,className:"googlesitekit-publisher-win__content"},e.createElement(i.a,{title:n}),o,c),e.createElement(a.a,{type:"win-error"})))}}).call(this,n(4))},197:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerNotification}));var r=n(21),a=n.n(r),i=n(6),o=n.n(i),c=n(5),l=n.n(c),s=n(16),u=n.n(s),d=n(15),f=n.n(d),g=n(1),p=n.n(g),m=n(11),h=n.n(m),v=n(206),b=n(240),k=n(81),E=n(0),_=n(106),y=n(3),O=n(17),S=n(93),j=n(37),w=n(24),C=n(211),N=n(213),R=n(212),x=n(226),z=n(227),A=n(86),T=n(136),L=n(130),M=n(32),D=n(228),P=n(79);function BannerNotification(t){var n,r=t.badgeLabel,i=t.children,c=t.className,s=void 0===c?"":c,d=t.ctaLabel,g=t.ctaLink,p=t.ctaTarget,m=t.description,I=t.dismiss,B=t.dismissExpires,F=void 0===B?0:B,H=t.format,V=void 0===H?"":H,W=t.id,U=t.isDismissible,G=void 0===U||U,K=t.learnMoreDescription,q=t.learnMoreLabel,X=t.learnMoreURL,Y=t.learnMoreTarget,$=void 0===Y?A.a.EXTERNAL:Y,J=t.logo,Z=t.module,Q=t.moduleName,ee=t.onCTAClick,te=t.onView,ne=t.onDismiss,re=t.onLearnMoreClick,ae=t.showOnce,ie=void 0!==ae&&ae,oe=t.SmallImageSVG,ce=t.title,le=t.type,se=t.WinImageSVG,ue=t.showSmallWinImage,de=void 0===ue||ue,fe=t.smallWinImageSVGWidth,ge=void 0===fe?75:fe,pe=t.smallWinImageSVGHeight,me=void 0===pe?75:pe,he=t.mediumWinImageSVGWidth,ve=void 0===he?105:he,be=t.mediumWinImageSVGHeight,ke=void 0===be?105:be,Ee=t.rounded,_e=void 0!==Ee&&Ee,ye=t.footer,Oe=t.secondaryPane,Se=t.ctaComponent,je=Object(E.useState)(!1),we=f()(je,2),Ce=we[0],Ne=we[1],Re=Object(E.useState)(!1),xe=f()(Re,2),ze=xe[0],Ae=xe[1],Te="notification::dismissed::".concat(W),Le=function(){return Object(j.f)(Te,new Date,{ttl:null})},Me=Object(P.a)(),De=Object(w.e)(),Pe=Object(v.a)(),Ie=Object(E.useState)(!1),Be=f()(Ie,2),Fe=Be[0],He=Be[1],Ve=Object(E.useRef)(),We=Object(b.a)(Ve,{rootMargin:"".concat(-Object(L.a)(Object(S.c)(De)),"px 0px 0px 0px"),threshold:0});Object(E.useEffect)((function(){!Fe&&(null==We?void 0:We.isIntersecting)&&("function"==typeof te&&te(),He(!0))}),[W,te,Fe,We]);var Ue=Me>=600;Object(k.a)(u()(l.a.mark((function e(){var t,n;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(F>0)){e.next=3;break}return e.next=3,Je();case 3:if(!G){e.next=9;break}return e.next=6,Object(j.d)(Te);case 6:t=e.sent,n=t.cacheHit,Ae(n);case 9:if(!ie){e.next=12;break}return e.next=12,Le();case 12:case"end":return e.stop()}}),e)}))));var Ge=function(){var e=u()(l.a.mark((function e(t){return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),t.preventDefault(),!ne){e.next=5;break}return e.next=5,ne(t);case 5:qe();case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Ke=Object(_.a)(g)&&"_blank"!==p,qe=function(){return Ke||Ne(!0),new Promise((function(e){setTimeout(u()(l.a.mark((function t(){var n;return l.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Le();case 2:Pe()&&Ae(!0),n=new Event("notificationDismissed"),document.dispatchEvent(n),e();case 6:case"end":return t.stop()}}),t)}))),350)}))},Xe=Object(y.useSelect)((function(e){return!!g&&e(M.a).isNavigatingTo(g)})),Ye=Object(y.useDispatch)(M.a).navigateTo,$e=function(){var e=u()(l.a.mark((function e(t){var n,r,a;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),Ke&&!t.defaultPrevented&&t.preventDefault(),n=!0,!ee){e.next=12;break}return e.next=6,ee(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:r=e.t0,a=r.dismissOnCTAClick,n=void 0===a||a;case 12:if(!G||!n){e.next=15;break}return e.next=15,qe();case 15:Ke&&Ye(g);case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Je=function(){var e=u()(l.a.mark((function e(){var t,n,r;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(j.d)(Te);case 2:if(t=e.sent,!(n=t.value)){e.next=10;break}if((r=new Date(n)).setSeconds(r.getSeconds()+parseInt(F,10)),!(r<new Date)){e.next=10;break}return e.next=10,Object(j.c)(Te);case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();if(!Xe&&G&&(void 0===ze||ze))return null;var Ze=!Xe&&Ce?"is-closed":"is-open",Qe=Object(T.d)(V),et=Object(T.c)(V),tt=Object(T.a)(V),nt=Object(T.b)({format:V,hasErrorOrWarning:"win-error"===le||"win-warning"===le,hasSmallImageSVG:!!oe,hasWinImageSVG:!!se});return e.createElement(C.a,{id:W,className:h()(s,(n={},o()(n,"googlesitekit-publisher-win--".concat(V),V),o()(n,"googlesitekit-publisher-win--".concat(le),le),o()(n,"googlesitekit-publisher-win--".concat(Ze),Ze),o()(n,"googlesitekit-publisher-win--rounded",_e),n)),secondaryPane:Oe,ref:Ve},J&&e.createElement(z.a,{module:Z,moduleName:Q}),oe&&e.createElement(O.a,{size:1,className:"googlesitekit-publisher-win__small-media"},e.createElement(oe,null)),e.createElement(O.a,a()({},nt,tt,{className:"googlesitekit-publisher-win__content"}),e.createElement(N.a,{title:ce,badgeLabel:r,smallWinImageSVGHeight:me,smallWinImageSVGWidth:ge,winImageFormat:V,WinImageSVG:!Ue&&de?se:void 0}),e.createElement(D.a,{description:m,learnMoreURL:X,learnMoreLabel:q,learnMoreTarget:$,learnMoreDescription:K,onLearnMoreClick:re}),i,e.createElement(R.a,{ctaLink:g,ctaLabel:d,ctaComponent:Se,ctaTarget:p,ctaCallback:$e,dismissLabel:G?I:void 0,dismissCallback:Ge}),ye&&e.createElement("div",{className:"googlesitekit-publisher-win__footer"},ye)),se&&(Ue||!de)&&e.createElement(O.a,a()({},Qe,et,{alignBottom:"larger"===V,className:"googlesitekit-publisher-win__image"}),e.createElement("div",{className:"googlesitekit-publisher-win__image-".concat(V)},e.createElement(se,{style:{maxWidth:ve,maxHeight:ke}}))),e.createElement(x.a,{type:le}))}BannerNotification.propTypes={id:p.a.string.isRequired,className:p.a.string,title:p.a.string.isRequired,description:p.a.node,learnMoreURL:p.a.string,learnMoreDescription:p.a.string,learnMoreLabel:p.a.string,learnMoreTarget:p.a.oneOf(Object.values(A.a)),WinImageSVG:p.a.elementType,SmallImageSVG:p.a.elementType,format:p.a.string,ctaLink:p.a.string,ctaLabel:p.a.string,type:p.a.string,dismiss:p.a.string,isDismissible:p.a.bool,logo:p.a.bool,module:p.a.string,moduleName:p.a.string,dismissExpires:p.a.number,showOnce:p.a.bool,onCTAClick:p.a.func,onView:p.a.func,onDismiss:p.a.func,onLearnMoreClick:p.a.func,badgeLabel:p.a.string,rounded:p.a.bool,footer:p.a.node,secondaryPane:p.a.node,showSmallWinImage:p.a.bool,smallWinImageSVGWidth:p.a.number,smallWinImageSVGHeight:p.a.number,mediumWinImageSVGWidth:p.a.number,mediumWinImageSVGHeight:p.a.number}}).call(this,n(4))},198:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleIcon}));var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),l=n.n(c),s=n(3),u=n(19);function ModuleIcon(t){var n=t.slug,r=t.size,i=o()(t,["slug","size"]),c=Object(s.useSelect)((function(e){return e(u.a).getModuleIcon(n)}));return c?e.createElement(c,a()({width:r,height:r},i)):null}ModuleIcon.propTypes={slug:l.a.string.isRequired,size:l.a.number},ModuleIcon.defaultProps={size:33}}).call(this,n(4))},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(146),f=n(0),g=n(2),p=n(126),m=n(127),h=n(128),v=n(70),b=n(76),k=Object(f.forwardRef)((function(t,n){var r,i=t["aria-label"],c=t.secondary,s=void 0!==c&&c,u=t.arrow,f=void 0!==u&&u,k=t.back,E=void 0!==k&&k,_=t.caps,y=void 0!==_&&_,O=t.children,S=t.className,j=void 0===S?"":S,w=t.danger,C=void 0!==w&&w,N=t.disabled,R=void 0!==N&&N,x=t.external,z=void 0!==x&&x,A=t.hideExternalIndicator,T=void 0!==A&&A,L=t.href,M=void 0===L?"":L,D=t.inverse,P=void 0!==D&&D,I=t.noFlex,B=void 0!==I&&I,F=t.onClick,H=t.small,V=void 0!==H&&H,W=t.standalone,U=void 0!==W&&W,G=t.linkButton,K=void 0!==G&&G,q=t.to,X=t.leadingIcon,Y=t.trailingIcon,$=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),J=M||q||!F?q?"ROUTER_LINK":z?"EXTERNAL_LINK":"LINK":R?"BUTTON_DISABLED":"BUTTON",Z="BUTTON"===J||"BUTTON_DISABLED"===J?"button":"ROUTER_LINK"===J?d.b:"a",Q=("EXTERNAL_LINK"===J&&(r=Object(g._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===J&&(r=Object(g._x)("(disabled)","screen reader text","google-site-kit")),r?i?"".concat(i," ").concat(r):"string"==typeof O?"".concat(O," ").concat(r):void 0:i),ee=X,te=Y;return E&&(ee=e.createElement(h.a,{width:14,height:14})),z&&!T&&(te=e.createElement(v.a,{width:14,height:14})),f&&!P&&(te=e.createElement(p.a,{width:14,height:14})),f&&P&&(te=e.createElement(m.a,{width:14,height:14})),e.createElement(Z,a()({"aria-label":Q,className:l()("googlesitekit-cta-link",j,{"googlesitekit-cta-link--secondary":s,"googlesitekit-cta-link--inverse":P,"googlesitekit-cta-link--small":V,"googlesitekit-cta-link--caps":y,"googlesitekit-cta-link--danger":C,"googlesitekit-cta-link--disabled":R,"googlesitekit-cta-link--standalone":U,"googlesitekit-cta-link--link-button":K,"googlesitekit-cta-link--no-flex":!!B}),disabled:R,href:"LINK"!==J&&"EXTERNAL_LINK"!==J||R?void 0:M,onClick:F,rel:"EXTERNAL_LINK"===J?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===J?"_blank":void 0,to:q},$),!!ee&&e.createElement(b.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},O),!!te&&e.createElement(b.a,{marginLeft:5},te))}));k.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=k}).call(this,n(4))},211:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(0),l=n(17),s=Object(c.forwardRef)((function(t,n){var r=t.id,a=t.className,i=t.children,s=t.secondaryPane;return e.createElement("section",{id:r,className:o()(a,"googlesitekit-publisher-win"),ref:n},e.createElement(l.e,null,e.createElement(l.k,null,i)),s&&e.createElement(c.Fragment,null,e.createElement("div",{className:"googlesitekit-publisher-win__secondary-pane-divider"}),e.createElement(l.e,{className:"googlesitekit-publisher-win__secondary-pane"},e.createElement(l.k,null,e.createElement(l.a,{className:"googlesitekit-publisher-win__secondary-pane",size:12},s)))))}));s.displayName="Banner",s.propTypes={id:a.a.string,className:a.a.string,secondaryPane:a.a.node},t.a=s}).call(this,n(4))},212:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerActions}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(15),l=n.n(c),s=n(1),u=n.n(s),d=n(206),f=n(0),g=n(3),p=n(10),m=n(32);function BannerActions(t){var n=t.ctaLink,r=t.ctaLabel,i=t.ctaComponent,c=t.ctaTarget,s=t.ctaCallback,u=t.dismissLabel,h=t.dismissCallback,v=Object(f.useState)(!1),b=l()(v,2),k=b[0],E=b[1],_=Object(d.a)(),y=Object(g.useSelect)((function(e){return!!n&&e(m.a).isNavigatingTo(n)})),O=function(){var e=o()(a.a.mark((function e(){var t,n,r,i=arguments;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(E(!0),t=i.length,n=new Array(t),r=0;r<t;r++)n[r]=i[r];return e.next=4,null==s?void 0:s.apply(void 0,n);case 4:_()&&E(!1);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return n||u||i?e.createElement("div",{className:"googlesitekit-publisher-win__actions"},i,r&&e.createElement(p.SpinnerButton,{className:"googlesitekit-notification__cta",href:n,target:c,onClick:O,disabled:k||y,isSaving:k||y},r),u&&e.createElement(p.Button,{tertiary:n||i,onClick:h,disabled:k||y},u)):null}BannerActions.propTypes={ctaLink:u.a.string,ctaLabel:u.a.string,ctaComponent:u.a.element,ctaTarget:u.a.string,ctaCallback:u.a.func,dismissLabel:u.a.string,dismissCallback:u.a.func}}).call(this,n(4))},213:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerTitle}));var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(11),l=n.n(c),s=n(77);function BannerTitle(t){var n=t.title,r=t.badgeLabel,i=t.WinImageSVG,o=t.winImageFormat,c=void 0===o?"":o,u=t.smallWinImageSVGWidth,d=void 0===u?75:u,f=t.smallWinImageSVGHeight,g=void 0===f?75:f;return n?e.createElement("div",{className:"googlesitekit-publisher-win__title-image-wrapper"},e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n,r&&e.createElement(s.a,{label:r})),i&&e.createElement("div",{className:l()(a()({},"googlesitekit-publisher-win__image-".concat(c),c))},e.createElement(i,{width:d,height:g}))):null}BannerTitle.propTypes={title:o.a.string,badgeLabel:o.a.string,WinImageSVG:o.a.elementType,winImageFormat:o.a.string,smallWinImageSVGWidth:o.a.number,smallWinImageSVGHeight:o.a.number}}).call(this,n(4))},218:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OptIn}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),f=n(38),g=n(2),p=n(3),m=n(10),h=n(7),v=n(36),b=n(20),k=n(18);function OptIn(t){var n=t.id,r=void 0===n?"googlesitekit-opt-in":n,i=t.name,c=void 0===i?"optIn":i,l=t.className,s=t.trackEventCategory,E=t.alignLeftCheckbox,_=void 0!==E&&E,y=Object(p.useSelect)((function(e){return e(h.a).isTrackingEnabled()})),O=Object(p.useSelect)((function(e){return e(h.a).isSavingTrackingEnabled()})),S=Object(p.useSelect)((function(e){return e(h.a).getErrorForAction("setTrackingEnabled",[!y])})),j=Object(p.useDispatch)(h.a).setTrackingEnabled,w=Object(k.a)(),C=Object(d.useCallback)(function(){var e=o()(a.a.mark((function e(t){var n,r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,j(!!t.target.checked);case 2:n=e.sent,r=n.response,n.error||(Object(v.a)(r.enabled),r.enabled&&Object(v.b)(s||w,"tracking_optin"));case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[j,s,w]);return e.createElement("div",{className:u()("googlesitekit-opt-in",l)},e.createElement(m.Checkbox,{id:r,name:c,value:"1",checked:y,disabled:O,onChange:C,loading:void 0===y,alignLeft:_},Object(f.a)(Object(g.__)("<span>Help us improve Site Kit by sharing anonymous usage data.</span> <span>All collected data is treated in accordance with the <a>Google Privacy Policy.</a></span>","google-site-kit"),{a:e.createElement(b.a,{key:"link",href:"https://policies.google.com/privacy",external:!0}),span:e.createElement("span",null)})),(null==S?void 0:S.message)&&e.createElement("div",{className:"googlesitekit-error-text"},null==S?void 0:S.message))}OptIn.propTypes={id:l.a.string,name:l.a.string,className:l.a.string,trackEventCategory:l.a.string,alignLeftCheckbox:l.a.bool}}).call(this,n(4))},22:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return a})),n.d(t,"o",(function(){return i})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return l})),n.d(t,"s",(function(){return s})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return f})),n.d(t,"k",(function(){return g})),n.d(t,"u",(function(){return p})),n.d(t,"v",(function(){return m})),n.d(t,"q",(function(){return h})),n.d(t,"p",(function(){return v})),n.d(t,"b",(function(){return b})),n.d(t,"e",(function(){return k})),n.d(t,"a",(function(){return E})),n.d(t,"d",(function(){return _})),n.d(t,"c",(function(){return y})),n.d(t,"f",(function(){return O})),n.d(t,"g",(function(){return S}));var r="mainDashboard",a="entityDashboard",i="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",l="activation",s="splash",u="adminBar",d="adminBarViewOnly",f="settings",g="adBlockingRecovery",p="wpDashboard",m="wpDashboardViewOnly",h="moduleSetup",v="metricSelection",b="key-metrics",k="traffic",E="content",_="speed",y="monetization",O=[r,a,i,o,c,s,f,h,v],S=[i,o,d,m]},220:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Item}));var r=n(1),a=n.n(r);function Item(t){var n=t.icon,r=t.label;return e.createElement("div",{className:"googlesitekit-user-menu__item"},e.createElement("div",{className:"googlesitekit-user-menu__item-icon"},n),e.createElement("span",{className:"googlesitekit-user-menu__item-label"},r))}Item.propTypes={icon:a.a.node,label:a.a.string}}).call(this,n(4))},222:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var r=n(1),a=n.n(r),i=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var r="win-warning"===n?e.createElement(i.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},r))}BannerIcon.propTypes={type:a.a.string}}).call(this,n(4))},224:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Root}));var r=n(15),a=n.n(r),i=n(1),o=n.n(i),c=n(432),l=n(534),s=n(0),u=n(3),d=n.n(u),f=n(225),g=n(229),p=n(57),m=n(230),h=n(232),v=n(233),b=n(61),k=n(160),E=n(173);function Root(t){var n=t.children,r=t.registry,i=t.viewContext,o=void 0===i?null:i,d=c.a,_=Object(s.useState)({key:"Root",value:!0}),y=a()(_,1)[0];return e.createElement(s.StrictMode,null,e.createElement(k.a,{value:y},e.createElement(u.RegistryProvider,{value:r},e.createElement(g.a,{value:p.a},e.createElement(b.a,{value:o},e.createElement(l.a,{theme:d()},e.createElement(f.a,null,e.createElement(h.a,null,n,o&&e.createElement(v.a,null)),Object(E.a)(o)&&e.createElement(m.a,null))))))))}Root.propTypes={children:o.a.node,registry:o.a.object,viewContext:o.a.string.isRequired},Root.defaultProps={registry:d.a}}).call(this,n(4))},225:function(e,t,n){"use strict";(function(e,r){var a=n(51),i=n.n(a),o=n(53),c=n.n(o),l=n(68),s=n.n(l),u=n(69),d=n.n(u),f=n(49),g=n.n(f),p=n(1),m=n.n(p),h=n(0),v=n(2),b=n(172),k=n(61),E=n(197),_=n(9);function y(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=g()(e);if(t){var a=g()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return d()(this,n)}}var O=function(t){s()(ErrorHandler,t);var n=y(ErrorHandler);function ErrorHandler(e){var t;return i()(this,ErrorHandler),(t=n.call(this,e)).state={error:null,info:null,copied:!1},t}return c()(ErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t,info:n}),Object(_.I)("react_error","handle_".concat(this.context||"unknown","_error"),"".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,a=t.info;return n?r.createElement(E.a,{id:"googlesitekit-error",className:"googlesitekit-error-handler",title:Object(v.__)("Site Kit encountered an error","google-site-kit"),description:r.createElement(b.a,{message:n.message,componentStack:a.componentStack}),isDismissible:!1,format:"small",type:"win-error"},r.createElement("pre",{className:"googlesitekit-overflow-auto"},n.message,a.componentStack)):e}}]),ErrorHandler}(h.Component);O.contextType=k.b,O.propTypes={children:m.a.node.isRequired},t.a=O}).call(this,n(28),n(4))},226:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var r=n(1),a=n.n(r),i=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var r="win-warning"===n?e.createElement(i.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},r))}BannerIcon.propTypes={type:a.a.string}}).call(this,n(4))},227:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerLogo}));var r=n(1),a=n.n(r),i=n(17),o=n(155),c=n(198);function BannerLogo(t){var n=t.module,r=t.moduleName;return e.createElement(i.a,{size:12},e.createElement("div",{className:"googlesitekit-publisher-win__logo"},n&&e.createElement(c.a,{slug:n,size:19}),!n&&e.createElement(o.a,{height:"34",width:"32"})),r&&e.createElement("div",{className:"googlesitekit-publisher-win__module-name"},r))}BannerLogo.propTypes={module:a.a.string,moduleName:a.a.string}}).call(this,n(4))},228:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerDescription}));var r=n(1),a=n.n(r),i=n(0),o=n(75),c=n(20),l=n(86);function BannerDescription(t){var n=t.description,r=t.learnMoreLabel,a=t.learnMoreURL,s=t.learnMoreTarget,u=t.learnMoreDescription,d=t.onLearnMoreClick;if(!n)return null;var f;return r&&(f=e.createElement(i.Fragment,null,e.createElement(c.a,{onClick:function(e){e.persist(),null==d||d()},href:a,external:s===l.a.EXTERNAL},r),u)),e.createElement("div",{className:"googlesitekit-publisher-win__desc"},Object(i.isValidElement)(n)?e.createElement(i.Fragment,null,n,f&&e.createElement("p",null,f)):e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.a)(n,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",f))}BannerDescription.propTypes={description:a.a.node,learnMoreURL:a.a.string,learnMoreDescription:a.a.string,learnMoreLabel:a.a.string,learnMoreTarget:a.a.oneOf(Object.values(l.a)),onLearnMoreClick:a.a.func}}).call(this,n(4))},229:function(e,t,n){"use strict";var r=n(158),a=(r.a.Consumer,r.a.Provider);t.a=a},23:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a}));var r="core/ui",a="activeContextID"},230:function(e,t,n){"use strict";(function(e){var r=n(3),a=n(231),i=n(7);t.a=function PermissionsModal(){return Object(r.useSelect)((function(e){return e(i.a).isAuthenticated()}))?e.createElement(a.a,null):null}}).call(this,n(4))},231:function(e,t,n){"use strict";(function(e,r){var a=n(5),i=n.n(a),o=n(16),c=n.n(o),l=n(2),s=n(0),u=n(3),d=n(109),f=n(29),g=n(32),p=n(7),m=n(129),h=n(72);t.a=function AuthenticatedPermissionsModal(){var t,n,a,o,v=Object(u.useRegistry)(),b=Object(u.useSelect)((function(e){return e(p.a).getPermissionScopeError()})),k=Object(u.useSelect)((function(e){return e(p.a).getUnsatisfiedScopes()})),E=Object(u.useSelect)((function(t){var n,r,a;return t(p.a).getConnectURL({additionalScopes:null==b||null===(n=b.data)||void 0===n?void 0:n.scopes,redirectURL:(null==b||null===(r=b.data)||void 0===r?void 0:r.redirectURL)||e.location.href,errorRedirectURL:null==b||null===(a=b.data)||void 0===a?void 0:a.errorRedirectURL})})),_=Object(u.useDispatch)(p.a).clearPermissionScopeError,y=Object(u.useDispatch)(g.a).navigateTo,O=Object(u.useDispatch)(f.a).setValues,S=Object(s.useCallback)((function(){_()}),[_]),j=Object(s.useCallback)(c()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return O(p.d,{permissionsError:b}),e.next=3,Object(m.c)(v);case 3:y(E);case 4:case"end":return e.stop()}}),e)}))),[v,E,y,b,O]);return Object(s.useEffect)((function(){(function(){var e=c()(i.a.mark((function e(){var t,n,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(null==b||null===(t=b.data)||void 0===t?void 0:t.skipModal)||!(null==b||null===(n=b.data)||void 0===n||null===(r=n.scopes)||void 0===r?void 0:r.length)){e.next=3;break}return e.next=3,j();case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[j,b]),b?(null==b||null===(t=b.data)||void 0===t||null===(n=t.scopes)||void 0===n?void 0:n.length)?(null==b||null===(a=b.data)||void 0===a?void 0:a.skipModal)||k&&(null==b||null===(o=b.data)||void 0===o?void 0:o.scopes.every((function(e){return k.includes(e)})))?null:r.createElement(h.a,null,r.createElement(d.a,{title:Object(l.__)("Additional Permissions Required","google-site-kit"),subtitle:b.message,confirmButton:Object(l.__)("Proceed","google-site-kit"),dialogActive:!0,handleConfirm:j,handleDialog:S,medium:!0})):(e.console.warn("permissionsError lacks scopes array to use for redirect, so not showing the PermissionsModal. permissionsError was:",b),null):null}}).call(this,n(28),n(4))},232:function(e,t,n){"use strict";var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(15),l=n.n(c),s=n(0),u=n(3),d=n(129);t.a=function RestoreSnapshots(e){var t=e.children,n=Object(u.useRegistry)(),r=Object(s.useState)(!1),i=l()(r,2),c=i[0],f=i[1];return Object(s.useEffect)((function(){c||o()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(d.b)(n);case 2:f(!0);case 3:case"end":return e.stop()}}),e)})))()}),[n,c]),c?t:null}},233:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return FeatureTours}));var a=n(81),i=n(0),o=n(3),c=n(7),l=n(18),s=n(90);function FeatureTours(){var t=Object(l.a)(),n=Object(o.useDispatch)(c.a).triggerTourForView;Object(a.a)((function(){n(t)}));var u=Object(o.useSelect)((function(e){return e(c.a).getCurrentTour()}));return Object(i.useEffect)((function(){if(u){var t=document.getElementById("js-googlesitekit-main-dashboard");if(t){var n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}}),[u]),u?r.createElement(s.a,{tourID:u.slug,steps:u.steps,gaEventCategory:u.gaEventCategory,callback:u.callback}):null}}).call(this,n(28),n(4))},234:function(e,t,n){"use strict";(function(e){var r=n(15),a=n.n(r),i=n(1),o=n.n(i),c=n(11),l=n.n(c),s=n(590),u=n(2),d=n(0),f=n(3),g=n(256),p=n(276),m=n(280),h=n(7),v=n(17),b=n(284),k=n(291),E=n(293),_=n(34),y=n(52),O=n(20),S=n(299),j=n(13),w=n(300);function Header(t){var n,r=t.children,i=t.subHeader,o=t.showNavigation,c=!!Object(y.c)(),C=Object(_.a)();Object(w.a)();var N=Object(f.useSelect)((function(e){return e(j.c).getAdminURL("googlesitekit-dashboard")})),R=Object(f.useSelect)((function(e){return e(h.a).isAuthenticated()})),x=Object(s.a)({childList:!0}),z=a()(x,2),A=z[0],T=!!(null===(n=z[1].target)||void 0===n?void 0:n.childElementCount);return e.createElement(d.Fragment,null,e.createElement("header",{className:l()("googlesitekit-header",{"googlesitekit-header--has-subheader":T,"googlesitekit-header--has-navigation":o})},e.createElement(v.e,null,e.createElement(v.k,null,e.createElement(v.a,{smSize:1,mdSize:2,lgSize:4,className:"googlesitekit-header__logo",alignMiddle:!0},e.createElement(O.a,{"aria-label":Object(u.__)("Go to dashboard","google-site-kit"),className:"googlesitekit-header__logo-link",href:N},e.createElement(g.a,null))),e.createElement(v.a,{smSize:3,mdSize:6,lgSize:8,className:"googlesitekit-header__children",alignMiddle:!0},r,!R&&c&&C&&e.createElement(E.a,null),R&&!C&&e.createElement(p.a,null))))),e.createElement("div",{className:"googlesitekit-subheader",ref:A},e.createElement(m.a,null),i),o&&e.createElement(b.a,null),c&&e.createElement(S.a,null),e.createElement(k.a,null))}Header.displayName="Header",Header.propTypes={children:o.a.node,subHeader:o.a.element,showNavigation:o.a.bool},Header.defaultProps={children:null,subHeader:null},t.a=Header}).call(this,n(4))},235:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return HelpMenu}));var r=n(15),a=n.n(r),i=n(1),o=n.n(i),c=n(209),l=n(0),s=n(56),u=n(2),d=n(3),f=n(10),g=n(301),p=n(112),m=n(9),h=n(164),v=n(19),b=n(18),k=n(13);function HelpMenu(t){var n=t.children,r=Object(l.useState)(!1),i=a()(r,2),o=i[0],E=i[1],_=Object(l.useRef)(),y=Object(b.a)();Object(c.a)(_,(function(){return E(!1)})),Object(p.a)([s.c,s.f],_,(function(){return E(!1)}));var O=Object(d.useSelect)((function(e){return e(v.a).isModuleActive("adsense")})),S=Object(l.useCallback)((function(){o||Object(m.I)("".concat(y,"_headerbar"),"open_helpmenu"),E(!o)}),[o,y]),j=Object(l.useCallback)((function(){E(!1)}),[]),w=Object(d.useSelect)((function(e){return e(k.c).getDocumentationLinkURL("fix-common-issues")}));return e.createElement("div",{ref:_,className:"googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},e.createElement(f.Button,{"aria-controls":"googlesitekit-help-menu","aria-expanded":o,"aria-label":Object(u.__)("Help","google-site-kit"),"aria-haspopup":"menu",className:"googlesitekit-header__dropdown googlesitekit-border-radius-round googlesitekit-button-icon googlesitekit-help-menu__button mdc-button--dropdown",icon:e.createElement(g.a,{width:"20",height:"20"}),onClick:S,text:!0,tooltipEnterDelayInMS:500}),e.createElement(f.Menu,{className:"googlesitekit-width-auto",menuOpen:o,id:"googlesitekit-help-menu",onSelected:j},n,e.createElement(h.a,{gaEventLabel:"fix_common_issues",href:w},Object(u.__)("Fix common issues","google-site-kit")),e.createElement(h.a,{gaEventLabel:"documentation",href:"https://sitekit.withgoogle.com/documentation/"},Object(u.__)("Read help docs","google-site-kit")),e.createElement(h.a,{gaEventLabel:"support_forum",href:"https://wordpress.org/support/plugin/google-site-kit/"},Object(u.__)("Get support","google-site-kit")),O&&e.createElement(h.a,{gaEventLabel:"adsense_help",href:"https://support.google.com/adsense/"},Object(u.__)("Get help with AdSense","google-site-kit"))))}HelpMenu.propTypes={children:o.a.node}}).call(this,n(4))},24:function(e,t,n){"use strict";n.d(t,"d",(function(){return a})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return l}));var r=n(79),a="xlarge",i="desktop",o="tablet",c="small";function l(){var e=Object(r.a)();return e>1280?a:e>960?i:e>600?o:c}},249:function(e,t,n){"use strict";(function(e){var r=n(15),a=n.n(r),i=n(0);t.a=function(t,n){var r=Object(i.useState)(null),o=a()(r,2),c=o[0],l=o[1];return Object(i.useEffect)((function(){if(t.current&&"function"==typeof e.IntersectionObserver){var r=new e.IntersectionObserver((function(e){l(e[e.length-1])}),n);return r.observe(t.current),function(){l(null),r.disconnect()}}return function(){}}),[t.current,n.threshold,n.root,n.rootMargin]),c}}).call(this,n(28))},256:function(e,t,n){"use strict";(function(e){var r=n(2),a=n(155),i=n(257),o=n(105);t.a=function Logo(){return e.createElement("div",{className:"googlesitekit-logo","aria-hidden":"true"},e.createElement(a.a,{className:"googlesitekit-logo__logo-g",height:"34",width:"32"}),e.createElement(i.a,{className:"googlesitekit-logo__logo-sitekit",height:"26",width:"99"}),e.createElement(o.a,null,Object(r.__)("Site Kit by Google Logo","google-site-kit")))}}).call(this,n(4))},257:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M62.09 1.664h3.038v.1L58.34 9.593l7.241 10.224v.1H62.7L56.755 11.4 53.95 14.64v5.278h-2.351V1.664h2.35v9.415h.1l8.04-9.415zM69.984 3.117c0 .454-.166.853-.487 1.175-.322.322-.71.488-1.176.488-.455 0-.854-.166-1.175-.488a1.599 1.599 0 01-.488-1.175c0-.466.166-.854.488-1.176.321-.322.71-.488 1.175-.488.455 0 .854.166 1.176.488.332.333.487.72.487 1.176zm-.476 4.313v12.498h-2.351V7.43h2.35zM77.016 20.128c-1.02 0-1.864-.31-2.54-.943-.676-.632-1.02-1.508-1.031-2.628V9.57h-2.196V7.43h2.196V3.603h2.35V7.43h3.061v2.14h-3.06v6.222c0 .831.166 1.397.488 1.696.321.3.687.444 1.097.444.189 0 .366-.022.555-.067.188-.044.344-.1.499-.166l.743 2.096c-.632.222-1.342.333-2.162.333zM2.673 18.952C1.375 18.009.488 16.678 0 14.97l2.883-1.176c.289 1.076.799 1.94 1.542 2.628.732.677 1.619 1.02 2.65 1.02.965 0 1.774-.244 2.45-.742.677-.5 1.01-1.187 1.01-2.052 0-.798-.3-1.453-.887-1.974-.588-.521-1.62-1.042-3.094-1.564l-1.22-.432C4.025 10.224 2.928 9.57 2.04 8.716 1.153 7.862.71 6.742.71 5.346c0-.966.266-1.853.787-2.673C2.018 1.852 2.75 1.209 3.693.72 4.624.244 5.678 0 6.864 0c1.708 0 3.072.41 4.081 1.242 1.02.832 1.697 1.752 2.04 2.795L10.236 5.2c-.2-.621-.576-1.164-1.142-1.63-.565-.477-1.286-.71-2.173-.71s-1.641.222-2.251.676c-.61.455-.91 1.032-.91 1.742 0 .676.278 1.22.82 1.663.544.432 1.398.854 2.563 1.253l1.22.41c1.674.577 2.96 1.342 3.88 2.274.921.931 1.376 2.184 1.376 3.748 0 1.275-.322 2.34-.976 3.193a6.01 6.01 0 01-2.495 1.919 8.014 8.014 0 01-3.116.621c-1.62 0-3.072-.466-4.358-1.408zM15.969 3.449a1.95 1.95 0 01-.588-1.43c0-.566.2-1.043.588-1.431A1.95 1.95 0 0117.399 0c.566 0 1.043.2 1.43.588.389.388.588.865.588 1.43 0 .566-.2 1.043-.587 1.43a1.95 1.95 0 01-1.43.589c-.566-.012-1.043-.2-1.431-.588zm-.067 2.595h2.994v13.883h-2.994V6.044zM25.405 19.85c-.543-.2-.986-.466-1.33-.788-.776-.776-1.176-1.84-1.176-3.182V8.683h-2.428v-2.64h2.428V2.13h2.994v3.926h3.372v2.639h-3.372v6.531c0 .743.145 1.276.433 1.575.277.366.743.543 1.42.543.31 0 .576-.044.82-.122.233-.077.488-.21.765-.399v2.917c-.599.277-1.32.41-2.173.41a5.01 5.01 0 01-1.753-.3zM33.623 19.407a6.63 6.63 0 01-2.529-2.628c-.61-1.12-.909-2.373-.909-3.77 0-1.332.3-2.551.887-3.693.588-1.132 1.409-2.04 2.462-2.706 1.053-.666 2.251-1.01 3.593-1.01 1.397 0 2.606.311 3.637.921a6.123 6.123 0 012.34 2.528c.532 1.076.799 2.274.799 3.627 0 .255-.023.576-.078.953H33.179c.111 1.287.566 2.285 1.375 2.983a4.162 4.162 0 002.817 1.043c.854 0 1.597-.189 2.218-.588a4.266 4.266 0 001.508-1.597l2.528 1.198c-.654 1.142-1.508 2.04-2.561 2.694-1.054.655-2.318.976-3.782.976-1.364.022-2.584-.288-3.66-.931zm7.23-8.051a3.332 3.332 0 00-.466-1.453c-.277-.477-.687-.887-1.242-1.208-.554-.322-1.23-.488-2.03-.488-.964 0-1.773.288-2.439.853-.665.566-1.12 1.342-1.375 2.296h7.552z",fill:"#5F6368"});t.a=function SvgLogoSitekit(e){return r.createElement("svg",a({viewBox:"0 0 80 21",fill:"none"},e),i)}},264:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"e",(function(){return c})),n.d(t,"f",(function(){return l})),n.d(t,"h",(function(){return s})),n.d(t,"g",(function(){return u}));var r="https://cdn.ampproject.org/v0.js",a="amp_cdn_restricted",i="check_api_unavailable",o="check_fetch_failed",c="google_api_connection_fail",l="invalid_hostname",s="setup_token_mismatch",u="google_sk_service_connection_fail"},276:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return UserMenu}));var a=n(5),i=n.n(a),o=n(16),c=n.n(o),l=n(15),s=n.n(l),u=n(209),d=n(0),f=n(2),g=n(56),p=n(3),m=n(10),h=n(109),v=n(9),b=n(37),k=n(72),E=n(277),_=n(220),y=n(278),O=n(279),S=n(29),j=n(13),w=n(7),C=n(32),N=n(8),R=n(112),x=n(18);function UserMenu(){var t=Object(p.useSelect)((function(e){return e(j.c).getProxyPermissionsURL()})),n=Object(p.useSelect)((function(e){return e(w.a).getEmail()})),a=Object(p.useSelect)((function(e){return e(w.a).getPicture()})),o=Object(p.useSelect)((function(e){return e(w.a).getFullName()})),l=Object(p.useSelect)((function(e){return e(j.c).getAdminURL("googlesitekit-splash",{googlesitekit_context:"revoked"})})),z=Object(p.useSelect)((function(e){return e(S.a).getValue(N.d,"isAutoCreatingCustomDimensionsForAudience")})),A=Object(d.useState)(!1),T=s()(A,2),L=T[0],M=T[1],D=Object(d.useState)(!1),P=s()(D,2),I=P[0],B=P[1],F=Object(d.useRef)(),H=Object(d.useRef)(),V=Object(x.a)(),W=Object(p.useDispatch)(C.a).navigateTo;Object(u.a)(F,(function(){return B(!1)})),Object(R.a)([g.c,g.f],F,(function(){var e;B(!1),null===(e=H.current)||void 0===e||e.focus()})),Object(d.useEffect)((function(){var t=function(e){g.c===e.keyCode&&(M(!1),B(!1))};return e.addEventListener("keyup",t),function(){e.removeEventListener("keyup",t)}}),[]);var U,G=Object(d.useCallback)((function(){I||Object(v.I)("".concat(V,"_headerbar"),"open_usermenu"),B(!I)}),[I,V]),K=Object(d.useCallback)((function(){M(!L),B(!1)}),[L]),q=Object(d.useCallback)(function(){var e=c()(i.a.mark((function e(n,r){var a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:a=r.detail.item,e.t0=null==a?void 0:a.id,e.next="manage-sites"===e.t0?4:"disconnect"===e.t0?9:11;break;case 4:if(!t){e.next=8;break}return e.next=7,Object(v.I)("".concat(V,"_headerbar_usermenu"),"manage_sites");case 7:W(t);case 8:return e.abrupt("break",12);case 9:return K(),e.abrupt("break",12);case 11:G();case 12:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),[t,G,K,W,V]),X=Object(d.useCallback)(c()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return M(!1),e.next=3,Object(b.b)();case 3:return e.next=5,Object(v.I)("".concat(V,"_headerbar_usermenu"),"disconnect_user");case 5:W(l);case 6:case"end":return e.stop()}}),e)}))),[l,W,V]);return n?(o&&n&&(U=Object(f.sprintf)(
/* translators: Account info text. 1: User's (full) name 2: User's email address. */
Object(f.__)("Google Account for %1$s (Email: %2$s)","google-site-kit"),o,n)),o&&!n&&(U=Object(f.sprintf)(
/* translators: Account info text. 1: User's (full) name. */
Object(f.__)("Google Account for %1$s","google-site-kit"),o)),!o&&n&&(U=Object(f.sprintf)(
/* translators: Account info text. 1: User's email address. */
Object(f.__)("Google Account (Email: %1$s)","google-site-kit"),n)),r.createElement(d.Fragment,null,r.createElement("div",{ref:F,className:"googlesitekit-user-selector googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},r.createElement(m.Button,{disabled:z,ref:H,className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--tablet googlesitekit-border-radius-round--phone googlesitekit-border-radius-round googlesitekit-button-icon",text:!0,onClick:G,icon:!!a&&r.createElement("i",{className:"mdc-button__icon mdc-button__account","aria-hidden":"true"},r.createElement("img",{className:"mdc-button__icon--image",src:a,alt:Object(f.__)("User Avatar","google-site-kit")})),"aria-haspopup":"menu","aria-expanded":I,"aria-controls":"user-menu","aria-label":z?void 0:Object(f.__)("Account","google-site-kit"),tooltip:!0,tooltipEnterDelayInMS:500,customizedTooltip:z?null:r.createElement("span",{"aria-label":U},r.createElement("strong",null,Object(f.__)("Google Account","google-site-kit")),r.createElement("br",null),r.createElement("br",null),o,o&&r.createElement("br",null),n)}),r.createElement(m.Menu,{className:"googlesitekit-user-menu",menuOpen:I,onSelected:q,id:"user-menu"},r.createElement("li",null,r.createElement(E.a,null)),!!t&&r.createElement("li",{id:"manage-sites",className:"mdc-list-item",role:"menuitem"},r.createElement(_.a,{icon:r.createElement(O.a,{width:"22"}),label:Object(f.__)("Manage Sites","google-site-kit")})),r.createElement("li",{id:"disconnect",className:"mdc-list-item",role:"menuitem"},r.createElement(_.a,{icon:r.createElement(y.a,{width:"22"}),label:Object(f.__)("Disconnect","google-site-kit")})))),r.createElement(k.a,null,r.createElement(h.a,{dialogActive:L,handleConfirm:X,handleDialog:K,title:Object(f.__)("Disconnect","google-site-kit"),subtitle:Object(f.__)("Disconnecting Site Kit by Google will remove your access to all services. After disconnecting, you will need to re-authorize to restore service.","google-site-kit"),confirmButton:Object(f.__)("Disconnect","google-site-kit"),danger:!0,small:!0})))):null}}).call(this,n(28),n(4))},277:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Details}));var r=n(2),a=n(3),i=n(7);function Details(){var t=Object(a.useSelect)((function(e){return e(i.a).getPicture()})),n=Object(a.useSelect)((function(e){return e(i.a).getFullName()})),o=Object(a.useSelect)((function(e){return e(i.a).getEmail()}));return e.createElement("div",{className:"googlesitekit-user-menu__details","aria-label":Object(r.__)("Google account","google-site-kit")},!!t&&e.createElement("img",{className:"googlesitekit-user-menu__details-avatar",src:t,alt:""}),e.createElement("div",{className:"googlesitekit-user-menu__details-info"},e.createElement("p",{className:"googlesitekit-user-menu__details-info__name"},n),e.createElement("p",{className:"googlesitekit-user-menu__details-info__email","aria-label":Object(r.__)("Email","google-site-kit")},o)))}}).call(this,n(4))},278:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M6.83 2H20a2 2 0 012 2v12c0 .34-.09.66-.23.94L20 15.17V6h-9.17l-4-4zm13.66 19.31L17.17 18H4a2 2 0 01-2-2V4c0-.34.08-.66.23-.94L.69 1.51 2.1.1l19.8 19.8-1.41 1.41zM15.17 16l-10-10H4v10h11.17z",fill:"currentColor"});t.a=function SvgDisconnect(e){return r.createElement("svg",a({viewBox:"0 0 22 22",fill:"none"},e),i)}},279:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M20 0H2C.9 0 0 .9 0 2v12c0 1.1.9 2 2 2h5v2h8v-2h5c1.1 0 2-.9 2-2V2c0-1.1-.9-2-2-2zm0 14H2V2h18v12zm-2-9H7v2h11V5zm0 4H7v2h11V9zM6 5H4v2h2V5zm0 4H4v2h2V9z",fill:"currentColor"});t.a=function SvgManageSites(e){return r.createElement("svg",a({viewBox:"0 0 22 18",fill:"none"},e),i)}},280:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotifications}));var r=n(0),a=n(281),i=n(167),o=n(41);function ErrorNotifications(){return e.createElement(r.Fragment,null,e.createElement(a.a,null),e.createElement(i.a,{areaSlug:o.b.ERRORS}))}}).call(this,n(4))},281:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InternalServerError}));var r=n(3),a=n(13),i=n(196),o=n(191),c=n(111);function InternalServerError(){var t=Object(r.useSelect)((function(e){return e(a.c).getInternalServerError()}));return t?e.createElement(o.a,{className:"googlesitekit-publisher-win googlesitekit-publisher-win--win-error"},e.createElement(i.a,{title:t.title,description:e.createElement(c.a,{text:t.description})})):null}}).call(this,n(4))},282:function(e,t,n){"use strict";n.d(t,"a",(function(){return ViewedStateObserver}));var r=n(1),a=n.n(r),i=n(0),o=n(3),c=n(23),l=n(249),s=n(161);function ViewedStateObserver(e){var t=e.id,n=e.observeRef,r=e.threshold,a=Object(l.a)(n,{threshold:r}),u=Object(o.useDispatch)(c.b).setValue,d=!!(null==a?void 0:a.isIntersecting),f=Object(s.a)(t);return Object(i.useEffect)((function(){!f&&d&&u(s.a.getKey(t),!0)}),[f,d,u,t]),null}ViewedStateObserver.propTypes={id:a.a.string,observeRef:a.a.object,threshold:a.a.number}},283:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return l}));var r=n(21),a=n.n(r),i=n(63),o=n.n(i),c=n(191),l=o()((function(e){return{id:e,Notification:s(e)(c.a)}}));function s(t){return function(n){function WithNotificationID(r){return e.createElement(n,a()({},r,{id:t}))}return WithNotificationID.displayName="WithNotificationID",(n.displayName||n.name)&&(WithNotificationID.displayName+="(".concat(n.displayName||n.name,")")),WithNotificationID}}}).call(this,n(4))},284:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardNavigation}));var r=n(3),a=n(7),i=n(34),o=n(183),c=n(285);function DashboardNavigation(){var t=Object(i.a)(),n=Object(r.useSelect)((function(e){return t?e(a.a).getViewableModules():null})),l=Object(r.useSelect)((function(e){return e(a.a).getKeyMetrics()}));return e.createElement(o.a,{loading:void 0===n||void 0===l,width:"100%",smallHeight:"59px",height:"71px"},e.createElement(c.a,null))}}).call(this,n(4))},285:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return Navigation}));var a=n(27),i=n.n(a),o=n(15),c=n.n(o),l=n(11),s=n.n(l),u=n(14),d=n(81),f=n(153),g=n(0),p=n(2),m=n(3),h=n(286),v=n(287),b=n(288),k=n(289),E=n(290),_=n(22),y=n(7),O=n(47),S=n(23),j=n(71),w=n(52),C=n(24),N=n(93),R=n(9),x=n(18),z=n(34);function A(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return T(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return T(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Navigation(){var t,n=Object(w.c)(),a=Object(g.useRef)(),o=Object(C.e)(),l=null===(t=e.location.hash)||void 0===t?void 0:t.substring(1),T=Object(g.useState)(l),L=c()(T,2),M=L[0],D=L[1],P=Object(g.useState)(l||void 0),I=c()(P,2),B=I[0],F=I[1],H=Object(g.useState)(!1),V=c()(H,2),W=V[0],U=V[1],G=Object(x.a)(),K=Object(z.a)(),q=Object(m.useDispatch)(S.b).setValue,X=Object(m.useSelect)((function(e){return K?e(y.a).getViewableModules():null})),Y=Object(m.useSelect)((function(e){return e(y.a).isKeyMetricsWidgetHidden()})),$={modules:X||void 0},J=Object(m.useSelect)((function(e){return n===w.b&&!0!==Y&&e(O.a).isWidgetContextActive(j.CONTEXT_MAIN_DASHBOARD_KEY_METRICS,$)})),Z=Object(m.useSelect)((function(e){return e(O.a).isWidgetContextActive(n===w.b?j.CONTEXT_MAIN_DASHBOARD_TRAFFIC:j.CONTEXT_ENTITY_DASHBOARD_TRAFFIC,$)})),Q=Object(m.useSelect)((function(e){return e(O.a).isWidgetContextActive(n===w.b?j.CONTEXT_MAIN_DASHBOARD_CONTENT:j.CONTEXT_ENTITY_DASHBOARD_CONTENT,$)})),ee=Object(m.useSelect)((function(e){return e(O.a).isWidgetContextActive(n===w.b?j.CONTEXT_MAIN_DASHBOARD_SPEED:j.CONTEXT_ENTITY_DASHBOARD_SPEED,$)})),te=Object(m.useSelect)((function(e){return e(O.a).isWidgetContextActive(n===w.b?j.CONTEXT_MAIN_DASHBOARD_MONETIZATION:j.CONTEXT_ENTITY_DASHBOARD_MONETIZATION,$)})),ne=Object(g.useCallback)((function(){return J?_.b:K?Z?_.e:Q?_.a:ee?_.d:te?_.c:"":_.e}),[J,Z,Q,ee,te,K]),re=Object(g.useCallback)((function(t){var n,r=t.target.closest(".mdc-chip"),a=null==r||null===(n=r.dataset)||void 0===n?void 0:n.contextId;e.history.replaceState({},"","#".concat(a)),F(a),Object(R.I)("".concat(G,"_navigation"),"tab_select",a),e.scrollTo({top:a!==ne()?Object(N.a)("#".concat(a),o):0,behavior:"smooth"}),setTimeout((function(){q(S.a,a)}),50)}),[o,G,q,ne]);return Object(d.a)((function(){var t=ne();if(!l)return D(t),void setTimeout((function(){return e.history.replaceState({},"","#".concat(t))}));var n=l;(function(e){return!(!J||e!==_.b)||(!(!Z||e!==_.e)||(!(!Q||e!==_.a)||(!(!ee||e!==_.d)||!(!te||e!==_.c))))})(n)||(n=t),q(S.a,n),D(n),setTimeout((function(){var r=n!==t?Object(N.a)("#".concat(n),o):0;e.scrollY!==r?e.scrollTo({top:r,behavior:"smooth"}):q(S.a,void 0)}),50)})),Object(g.useEffect)((function(){var t=function(e){q(S.a,void 0),D(e),F(void 0)},n=Object(u.throttle)((function(n){var r,o,c,l,s=e.scrollY,u=null===(r=document.querySelector(".googlesitekit-entity-header"))||void 0===r||null===(o=r.getBoundingClientRect())||void 0===o?void 0:o.bottom,d=null==a||null===(c=a.current)||void 0===c?void 0:c.getBoundingClientRect(),f=d.bottom,g=d.top,p=[].concat(i()(J?[_.b]:[]),i()(Z?[_.e]:[]),i()(Q?[_.a]:[]),i()(ee?[_.d]:[]),i()(te?[_.c]:[])),m=ne();if(0===s)U(!1);else{var h,v=null===(h=document.querySelector(".googlesitekit-header"))||void 0===h?void 0:h.getBoundingClientRect().bottom;U(g===v)}var b,k=A(p);try{for(k.s();!(b=k.n()).done;){var E=b.value,y=document.getElementById(E);if(y){var O=y.getBoundingClientRect().top-20-(u||f||0);O<0&&(void 0===l||l<O)&&(l=O,m=E)}}}catch(e){k.e(e)}finally{k.f()}if(B)B===m&&t(m);else{var S=e.location.hash;m!==(null==S?void 0:S.substring(1))&&(n&&Object(R.I)("".concat(G,"_navigation"),"tab_scroll",m),e.history.replaceState({},"","#".concat(m)),t(m))}}),150);return e.addEventListener("scroll",n),function(){e.removeEventListener("scroll",n)}}),[B,J,Z,Q,ee,te,G,q,ne]),r.createElement("nav",{className:s()("mdc-chip-set","googlesitekit-navigation","googlesitekit-navigation--".concat(n),{"googlesitekit-navigation--is-sticky":W}),ref:a},J&&r.createElement(f.Chip,{id:_.b,label:Object(p.__)("Key metrics","google-site-kit"),leadingIcon:r.createElement(h.a,{width:"18",height:"16"}),onClick:re,selected:M===_.b,"data-context-id":_.b}),Z&&r.createElement(f.Chip,{id:_.e,label:Object(p.__)("Traffic","google-site-kit"),leadingIcon:r.createElement(v.a,{width:"18",height:"16"}),onClick:re,selected:M===_.e,"data-context-id":_.e}),Q&&r.createElement(f.Chip,{id:_.a,label:Object(p.__)("Content","google-site-kit"),leadingIcon:r.createElement(b.a,{width:"18",height:"18"}),onClick:re,selected:M===_.a,"data-context-id":_.a}),ee&&r.createElement(f.Chip,{id:_.d,label:Object(p.__)("Speed","google-site-kit"),leadingIcon:r.createElement(k.a,{width:"20",height:"16"}),onClick:re,selected:M===_.d,"data-context-id":_.d}),te&&r.createElement(f.Chip,{id:_.c,label:Object(p.__)("Monetization","google-site-kit"),leadingIcon:r.createElement(E.a,{width:"18",height:"16"}),onClick:re,selected:M===_.c,"data-context-id":_.c}))}}).call(this,n(28),n(4))},286:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("rect",{x:.5,width:5,height:5,rx:1,fill:"currentColor"}),o=r.createElement("rect",{x:7.5,width:5,height:5,rx:1,fill:"currentColor"}),c=r.createElement("rect",{x:.5,y:7,width:5,height:5,rx:1,fill:"currentColor"}),l=r.createElement("rect",{x:7.5,y:7,width:5,height:5,rx:1,fill:"currentColor"});t.a=function SvgNavKeyMetricsIcon(e){return r.createElement("svg",a({viewBox:"0 0 13 12",fill:"none"},e),i,o,c,l)}},287:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 0h3.971v16H7V0zM0 8h4v8H0V8zm18-3h-4v11h4V5z",fill:"currentColor"});t.a=function SvgNavTrafficIcon(e){return r.createElement("svg",a({viewBox:"0 0 18 16",fill:"none"},e),i)}},288:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 16V2c0-1.1-1-2-2.222-2H2.222C1 0 0 .9 0 2v14c0 1.1 1 2 2.222 2h13.556C17 18 18 17.1 18 16zM9 7h5V5H9v2zm7-5H2v14h14V2zM4 4h4v4H4V4zm10 7H9v2h5v-2zM4 10h4v4H4v-4z",fill:"currentColor"});t.a=function SvgNavContentIcon(e){return r.createElement("svg",a({viewBox:"0 0 18 18",fill:"none"},e),i)}},289:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M18.378 4.543l-1.232 1.854a8.024 8.024 0 01-.22 7.598H3.043A8.024 8.024 0 014.154 4.49 8.011 8.011 0 0113.57 2.82l1.853-1.233A10.01 10.01 0 003.117 2.758a10.026 10.026 0 00-1.797 12.24A2.004 2.004 0 003.043 16h13.873a2.003 2.003 0 001.742-1.002 10.03 10.03 0 00-.27-10.465l-.01.01z",fill:"currentColor"}),o=r.createElement("path",{d:"M8.572 11.399a2.003 2.003 0 002.835 0l5.669-8.51-8.504 5.673a2.005 2.005 0 000 2.837z",fill:"currentColor"});t.a=function SvgNavSpeedIcon(e){return r.createElement("svg",a({viewBox:"0 0 20 16",fill:"none"},e),i,o)}},29:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/forms"},290:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M16.1 0v2h2.967l-5.946 5.17-4.6-4L0 10.59 1.621 12l6.9-6 4.6 4L20.7 3.42V6H23V0h-6.9z",fill:"currentColor"});t.a=function SvgNavMonetizationIcon(e){return r.createElement("svg",a({viewBox:"0 0 23 12",fill:"none"},e),i)}},291:function(e,t,n){"use strict";(function(e,r){var a=n(15),i=n.n(a),o=n(14),c=n(2),l=n(0),s=n(3),u=n(10),d=n(13),f=n(292),g=n(32),p=n(20),m=n(80),h=n(9),v=n(52),b=n(18);t.a=function EntityHeader(){var t=Object(b.a)(),n=Object(v.c)(),a=Object(s.useSelect)((function(e){return e(d.c).getCurrentEntityTitle()})),k=Object(s.useSelect)((function(e){return e(d.c).getCurrentEntityURL()})),E=Object(l.useRef)(),_=Object(l.useState)(k),y=i()(_,2),O=y[0],S=y[1];Object(l.useEffect)((function(){var t=function(){if(E.current){var t=E.current.clientWidth-40,n=e.getComputedStyle(E.current.lastChild,null).getPropertyValue("font-size"),r=2*t/parseFloat(n);S(Object(m.d)(k,r))}},n=Object(o.throttle)(t,100);return t(),e.addEventListener("resize",n),function(){e.removeEventListener("resize",n)}}),[k,E,S]);var j=Object(s.useDispatch)(g.a).navigateTo,w=Object(s.useSelect)((function(e){return e(d.c).getAdminURL("googlesitekit-dashboard")})),C=Object(l.useCallback)((function(){Object(h.I)("".concat(t,"_navigation"),"return_to_dashboard"),j(w)}),[w,j,t]);return v.a!==n||null===k||null===a?null:r.createElement("div",{className:"googlesitekit-entity-header"},r.createElement("div",{className:"googlesitekit-entity-header__back"},r.createElement(u.Button,{icon:r.createElement(f.a,{width:24,height:24}),"aria-label":Object(c.__)("Back to dashboard","google-site-kit"),onClick:C,text:!0,tertiary:!0},Object(c.__)("Back to dashboard","google-site-kit"))),r.createElement("div",{ref:E,className:"googlesitekit-entity-header__details"},r.createElement("p",null,a),r.createElement(p.a,{secondary:!0,href:k,"aria-label":k,external:!0},O)))}}).call(this,n(28),n(4))},292:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M21 11H6.83l3.58-3.59L9 6l-6 6 6 6 1.41-1.41L6.83 13H21z",fill:"currentColor"});t.a=function SvgKeyboardBackspace(e){return r.createElement("svg",a({viewBox:"0 0 24 24"},e),i,o)}},293:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ViewOnlyMenu}));var r=n(15),a=n.n(r),i=n(209),o=n(11),c=n.n(o),l=n(0),s=n(2),u=n(56),d=n(10),f=n(18),g=n(112),p=n(9),m=n(294),h=n(295),v=n(296),b=n(298),k=n(3),E=n(7);function ViewOnlyMenu(){var t=Object(l.useState)(!1),n=a()(t,2),r=n[0],o=n[1],_=Object(l.useRef)(),y=Object(f.a)();Object(i.a)(_,(function(){return o(!1)})),Object(g.a)([u.c,u.f],_,(function(){return o(!1)}));var O=Object(l.useCallback)((function(){r||Object(p.I)("".concat(y,"_headerbar"),"open_viewonly"),o(!r)}),[r,y]),S=Object(k.useSelect)((function(e){return e(E.a).hasCapability(E.H)}));return e.createElement("div",{ref:_,className:c()("googlesitekit-view-only-menu","googlesitekit-dropdown-menu","googlesitekit-dropdown-menu__icon-menu","mdc-menu-surface--anchor",{"googlesitekit-view-only-menu--user-can-authenticate":S})},e.createElement(d.Button,{className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--phone googlesitekit-button-icon",text:!0,onClick:O,icon:e.createElement("span",{className:"mdc-button__icon","aria-hidden":"true"},e.createElement(m.a,{className:"mdc-button__icon--image"})),"aria-haspopup":"menu","aria-expanded":r,"aria-controls":"view-only-menu","aria-label":Object(s.__)("View only","google-site-kit"),tooltip:!0,tooltipEnterDelayInMS:500},Object(s.__)("View only","google-site-kit")),e.createElement(d.Menu,{menuOpen:r,nonInteractive:!0,onSelected:O,id:"view-only-menu"},e.createElement(h.a,null),e.createElement(v.a,null),e.createElement("li",{className:"mdc-list-divider",role:"separator"}),e.createElement(b.a,null)))}}).call(this,n(4))},294:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M8 1.333c2.756 0 5.214 1.42 6.415 3.667-1.2 2.247-3.659 3.667-6.415 3.667-2.756 0-5.215-1.42-6.415-3.667C2.785 2.753 5.244 1.333 8 1.333zM8 0C4.364 0 1.258 2.073 0 5c1.258 2.927 4.364 5 8 5s6.742-2.073 8-5c-1.258-2.927-4.364-5-8-5zm0 3.333c1.004 0 1.818.747 1.818 1.667S9.004 6.667 8 6.667 6.182 5.92 6.182 5 6.996 3.333 8 3.333zM8 2C6.196 2 4.727 3.347 4.727 5S6.197 8 8 8c1.804 0 3.273-1.347 3.273-3S9.803 2 8 2z",fill:"currentColor"});t.a=function SvgView(e){return r.createElement("svg",a({viewBox:"0 0 16 10",fill:"none"},e),i)}},295:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(0),l=n(38),s=n(2),u=n(3),d=n(10),f=n(32),g=n(13),p=n(7),m=n(9),h=n(20),v=n(18),b=n(37);function Description(){var t=Object(v.a)(),n=Object(u.useSelect)((function(e){return e(p.a).hasCapability(p.H)})),r=Object(u.useSelect)((function(e){return e(g.c).getProxySetupURL()})),i=Object(u.useSelect)((function(e){return e(g.c).getDocumentationLinkURL("dashboard-sharing")})),k=Object(u.useDispatch)(f.a).navigateTo,E=Object(c.useCallback)(function(){var e=o()(a.a.mark((function e(n){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.preventDefault(),e.next=3,Promise.all([Object(b.f)("start_user_setup",!0),Object(m.I)("".concat(t,"_headerbar_viewonly"),"start_user_setup",r?"proxy":"custom-oauth")]);case 3:k(r);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[r,k,t]),_=Object(c.useCallback)((function(){Object(m.I)("".concat(t,"_headerbar_viewonly"),"click_learn_more_link")}),[t]),y=n?Object(l.a)(Object(s.__)("You can see stats from all shared Google services, but you can't make any changes. <strong>Sign in to connect more services and control sharing access.</strong>","google-site-kit"),{strong:e.createElement("strong",null)}):Object(l.a)(Object(s.__)("You can see stats from all shared Google services, but you can't make any changes. <a>Learn more</a>","google-site-kit"),{a:e.createElement(h.a,{href:i,external:!0,onClick:_,"aria-label":Object(s.__)("Learn more about dashboard sharing","google-site-kit")})});return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item googlesitekit-view-only-menu__description"},e.createElement("p",null,y),n&&e.createElement(d.Button,{onClick:E},Object(s._x)("Sign in with Google","Service name","google-site-kit")))}}).call(this,n(4))},296:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SharedServices}));var r=n(2),a=n(3),i=n(7),o=n(297);function SharedServices(){var t=Object(a.useSelect)((function(e){return e(i.a).getViewableModules()}));return void 0===t?null:e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("h4",null,Object(r.__)("Shared services","google-site-kit")),e.createElement("ul",null,t.map((function(t){return e.createElement(o.a,{key:t,module:t})}))))}}).call(this,n(4))},297:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Service}));var r=n(1),a=n.n(r),i=n(38),o=n(2),c=n(3),l=n(19),s=n(7);function Service(t){var n=t.module,r=Object(c.useSelect)((function(e){return e(s.a).hasCapability(s.H)})),a=Object(c.useSelect)((function(e){return e(l.a).getModule(n)||{}})),u=a.name,d=a.owner,f=Object(c.useSelect)((function(e){return e(l.a).getModuleIcon(n)}));return e.createElement("li",{className:"googlesitekit-view-only-menu__service"},e.createElement("span",{className:"googlesitekit-view-only-menu__service--icon"},e.createElement(f,{height:26})),e.createElement("span",{className:"googlesitekit-view-only-menu__service--name"},u),r&&(null==d?void 0:d.login)&&e.createElement("span",{className:"googlesitekit-view-only-menu__service--owner"},Object(i.a)(Object(o.sprintf)(
/* translators: %s: module owner Google Account email address */
Object(o.__)("Shared by <strong>%s</strong>","google-site-kit"),d.login),{strong:e.createElement("strong",{title:d.login})})))}Service.propTypes={module:a.a.string.isRequired}}).call(this,n(4))},298:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Tracking}));var r=n(38),a=n(2),i=n(218),o=n(18);function Tracking(){var t=Object(o.a)();return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("p",null,Object(r.a)(Object(a.__)("Thanks for using Site Kit!<br />Help us make it even better","google-site-kit"),{br:e.createElement("br",null)})),e.createElement(i.a,{trackEventCategory:"".concat(t,"_headerbar_viewonly"),alignCheckboxLeft:!0}))}}).call(this,n(4))},299:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SubtleNotifications}));var r=n(167),a=n(41);function SubtleNotifications(){return e.createElement(r.a,{areaSlug:a.b.BANNERS_BELOW_NAV})}}).call(this,n(4))},3:function(e,t){e.exports=googlesitekit.data},300:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(0),l=n(3),s=n(13),u=n(18),d=n(37),f=n(9),g=function(){var e=Object(u.a)(),t=Object(l.useSelect)((function(e){return e(s.c).isUsingProxy()})),n=Object(l.useSelect)((function(e){return e(s.c).getSetupErrorMessage()}));Object(c.useEffect)((function(){n||void 0===t||function(){var n=o()(a.a.mark((function n(){var r,i;return a.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,Object(d.d)("start_user_setup");case 2:return r=n.sent,n.next=5,Object(d.d)("start_site_setup");case 5:if(i=n.sent,!r.cacheHit){n.next=10;break}return n.next=9,Object(d.c)("start_user_setup");case 9:Object(f.I)("".concat(e,"_setup"),"complete_user_setup",t?"proxy":"custom-oauth");case 10:if(!i.cacheHit){n.next=14;break}return n.next=13,Object(d.c)("start_site_setup");case 13:Object(f.I)("".concat(e,"_setup"),"complete_site_setup",t?"proxy":"custom-oauth");case 14:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}()()}),[e,t,n])}},301:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M9 16h2v-2H9v2zm1-16C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14C7.79 4 6 5.79 6 8h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z",fill:"currentColor"});t.a=function SvgHelp(e){return r.createElement("svg",a({viewBox:"0 0 20 20",fill:"none"},e),i)}},312:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"currentColor",fillRule:"evenodd"},r.createElement("path",{d:"M0 0h2v7H0zM0 10h2v2H0z"}));t.a=function SvgExclamation(e){return r.createElement("svg",a({viewBox:"0 0 2 12"},e),i)}},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},329:function(e,t,n){"use strict";(function(e){var r=n(51),a=n.n(r),i=n(53),o=n.n(i),c=n(68),l=n.n(c),s=n(69),u=n.n(s),d=n(49),f=n.n(d),g=n(1),p=n.n(g),m=n(0),h=n(17),v=n(20);function b(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var a=f()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return u()(this,n)}}var k=function(t){l()(LayoutHeader,t);var n=b(LayoutHeader);function LayoutHeader(){return a()(this,LayoutHeader),n.apply(this,arguments)}return o()(LayoutHeader,[{key:"render",value:function(){var t=this.props,n=t.title,r=t.badge,a=t.ctaLabel,i=t.ctaLink,o=i?{alignMiddle:!0,smSize:4,lgSize:6}:{alignMiddle:!0,smSize:4,mdSize:8,lgSize:12};return e.createElement("header",{className:"googlesitekit-layout__header"},e.createElement(h.e,null,e.createElement(h.k,null,n&&e.createElement(h.a,o,e.createElement("h3",{className:"googlesitekit-subheading-1 googlesitekit-layout__header-title"},n,r)),i&&e.createElement(h.a,{alignMiddle:!0,mdAlignRight:!0,smSize:4,lgSize:6},e.createElement(v.a,{href:i,external:!0},a)))))}}]),LayoutHeader}(m.Component);k.propTypes={title:p.a.string,badge:p.a.node,ctaLabel:p.a.string,ctaLink:p.a.string},k.defaultProps={title:"",badge:null,ctaLabel:"",ctaLink:""},t.a=k}).call(this,n(4))},330:function(e,t,n){"use strict";(function(e){var r=n(51),a=n.n(r),i=n(53),o=n.n(i),c=n(68),l=n.n(c),s=n(69),u=n.n(s),d=n(49),f=n.n(d),g=n(1),p=n.n(g),m=n(0),h=n(17),v=n(134);function b(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var a=f()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return u()(this,n)}}var k=function(t){l()(LayoutFooter,t);var n=b(LayoutFooter);function LayoutFooter(){return a()(this,LayoutFooter),n.apply(this,arguments)}return o()(LayoutFooter,[{key:"render",value:function(){var t=this.props,n=t.ctaLabel,r=t.ctaLink,a=t.footerContent;return e.createElement("footer",{className:"googlesitekit-layout__footer"},e.createElement(h.e,null,e.createElement(h.k,null,e.createElement(h.a,{size:12},r&&n&&e.createElement(v.a,{className:"googlesitekit-data-block__source",name:n,href:r,external:!0}),a))))}}]),LayoutFooter}(m.Component);k.propTypes={ctaLabel:p.a.string,ctaLink:p.a.string},t.a=k}).call(this,n(4))},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(22),a=n(18);function i(){var e=Object(a.a)();return r.g.includes(e)}},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return E})),n.d(t,"b",(function(){return b})),n.d(t,"c",(function(){return k}));var r=n(99),a=e._googlesitekitTrackingData||{},i=a.activeModules,o=void 0===i?[]:i,c=a.isSiteKitScreen,l=a.trackingEnabled,s=a.trackingID,u=a.referenceSiteURL,d=a.userIDHash,f=a.isAuthenticated,g={activeModules:o,trackingEnabled:l,trackingID:s,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:a.userRoles,isAuthenticated:f,pluginVersion:"1.151.0"},p=Object(r.a)(g),m=p.enableTracking,h=p.disableTracking,v=(p.isTrackingEnabled,p.initializeSnippet),b=p.trackEvent,k=p.trackEventOnce;function E(e){e?m():h()}c&&l&&v()}).call(this,n(28))},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return b})),n.d(t,"f",(function(){return k})),n.d(t,"c",(function(){return E})),n.d(t,"e",(function(){return _})),n.d(t,"b",(function(){return y}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=(n(27),n(9));function l(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u,d="googlesitekit_",f="".concat(d).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),g=["sessionStorage","localStorage"],p=[].concat(g),m=function(){var t=o()(a.a.mark((function t(n){var r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,i="__storage_test__",r.setItem(i,i),r.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function h(){return v.apply(this,arguments)}function v(){return(v=o()(a.a.mark((function t(){var n,r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=l(p),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(i=r.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,m(i);case 11:if(!t.sent){t.next=13;break}u=e[i];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var b=function(){var e=o()(a.a.mark((function e(t){var n,r,i,o,c,l,s;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(f).concat(t)))){e.next=10;break}if(i=JSON.parse(r),o=i.timestamp,c=i.ttl,l=i.value,s=i.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:l,isError:s});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),k=function(){var t=o()(a.a.mark((function t(n,r){var i,o,l,s,u,d,g,p,m=arguments;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=m.length>2&&void 0!==m[2]?m[2]:{},o=i.ttl,l=void 0===o?c.b:o,s=i.timestamp,u=void 0===s?Math.round(Date.now()/1e3):s,d=i.isError,g=void 0!==d&&d,t.next=3,h();case 3:if(!(p=t.sent)){t.next=14;break}return t.prev=5,p.setItem("".concat(f).concat(n),JSON.stringify({timestamp:u,ttl:l,value:r,isError:g})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),E=function(){var t=o()(a.a.mark((function t(n){var r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,h();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,i=n.startsWith(d)?n:"".concat(f).concat(n),r.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),_=function(){var t=o()(a.a.mark((function t(){var n,r,i,o;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,h();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],i=0;i<n.length;i++)0===(o=n.key(i)).indexOf(d)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),y=function(){var e=o()(a.a.mark((function e(){var t,n,r,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h();case 2:if(!e.sent){e.next=25;break}return e.next=6,_();case 6:t=e.sent,n=l(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return i=r.value,e.next=14,E(i);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r="_googlesitekitDataLayer",a="data-googlesitekit-gtag"},41:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(22),a="core/notifications",i={ERRORS:"notification-area-errors",BANNERS_ABOVE_NAV:"notification-area-banners-above-nav",BANNERS_BELOW_NAV:"notification-area-banners-below-nav"},o={DEFAULT:"default",SETUP_CTAS:"setup-ctas"},c=[r.s,r.n,r.l,r.o,r.m]},44:function(e,t,n){"use strict";(function(e){var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(11),l=n.n(c),s=n(24);function PreviewBlock(t){var n,r,i=t.className,o=t.width,c=t.height,u=t.shape,d=t.padding,f=t.smallWidth,g=t.smallHeight,p=t.tabletWidth,m=t.tabletHeight,h=t.desktopWidth,v=t.desktopHeight,b=Object(s.e)(),k={width:(n={},a()(n,s.b,f),a()(n,s.c,p),a()(n,s.a,h),a()(n,s.d,h),n),height:(r={},a()(r,s.b,g),a()(r,s.c,m),a()(r,s.a,v),a()(r,s.d,h),r)};return e.createElement("div",{className:l()("googlesitekit-preview-block",i,{"googlesitekit-preview-block--padding":d}),style:{width:k.width[b]||o,height:k.height[b]||c}},e.createElement("div",{className:l()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(4))},45:function(e,t){e.exports=googlesitekit.api},47:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return i}));var r={BOXES:"boxes",COMPOSITE:"composite"},a={QUARTER:"quarter",HALF:"half",FULL:"full"},i="core/widgets"},516:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return i}));var r="connect-analytics-setup-splash",a="analytics-setup-opt-in",i="shared_dashboard_splash"},52:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(22),a=n(18),i=r.n,o=r.l;function c(){var e=Object(a.a)();return e===r.n||e===r.o?i:e===r.l||e===r.m?o:null}},54:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,r=t.reconnectURL,a=t.noPrefix;if(!n)return null;var l=n;void 0!==a&&a||(l=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),r&&Object(i.a)(r)&&(l=l+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),r));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(l,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:a.a.string.isRequired,reconnectURL:a.a.string,noPrefix:a.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},57:function(e,t,n){"use strict";(function(e){var r,a;n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var i=new Set((null===(r=e)||void 0===r||null===(a=r._googlesitekitBaseData)||void 0===a?void 0:a.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;return t instanceof Set&&t.has(e)}}).call(this,n(28))},579:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"currentColor",fillRule:"evenodd"},r.createElement("path",{d:"M0 6.414L1.415 5l5.292 5.292-1.414 1.415z"}),r.createElement("path",{d:"M14.146.146l1.415 1.414L5.414 11.707 4 10.292z"}));t.a=function SvgCheck(e){return r.createElement("svg",a({viewBox:"0 0 16 12"},e),i)}},58:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return r.createElement("svg",a({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),i,o)}},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(39);function a(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(0),a=Object(r.createContext)(""),i=(a.Consumer,a.Provider);t.b=a},694:function(e,t,n){"use strict";(function(e,r){var a=n(5),i=n.n(a),o=n(16),c=n.n(o),l=n(15),s=n.n(l),u=n(2),d=n(0),f=n(38),g=n(56),p=n(121),m=n(3),h=n(109),v=n(37),b=n(72),k=n(20),E=n(13),_=n(32),y=n(36),O=n(18);t.a=function ResetButton(t){var n=t.children,a=Object(m.useSelect)((function(e){return e(E.c).getAdminURL("googlesitekit-splash",{notification:"reset_success"})})),o=Object(m.useSelect)((function(e){return e(E.c).isDoingReset()})),l=Object(m.useSelect)((function(e){return e(_.a).isNavigatingTo(a||"")})),S=Object(d.useState)(!1),j=s()(S,2),w=j[0],C=j[1],N=Object(d.useState)(!1),R=s()(N,2),x=R[0],z=R[1],A=Object(p.a)(C,3e3);Object(d.useEffect)((function(){o||l?C(!0):A(!1)}),[o,l,A]),Object(d.useEffect)((function(){var t=function(e){g.c===e.keyCode&&z(!1)};return x&&e.addEventListener("keyup",t,!1),function(){x&&e.removeEventListener("keyup",t)}}),[x]);var T=Object(m.useDispatch)(E.c).reset,L=Object(m.useDispatch)(_.a).navigateTo,M=Object(O.a)(),D=Object(d.useCallback)(c()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,T();case 2:return e.next=4,Object(v.b)();case 4:return e.next=6,Object(y.b)(M,"reset_plugin");case 6:L(a);case 7:case"end":return e.stop()}}),e)}))),[L,a,T,M]),P=Object(d.useCallback)((function(){z(!x)}),[x]),I=Object(d.useCallback)((function(){z(!0)}),[]);return r.createElement(d.Fragment,null,r.createElement(k.a,{className:"googlesitekit-reset-button",onClick:I},n||Object(u.__)("Reset Site Kit","google-site-kit")),r.createElement(b.a,null,r.createElement(h.a,{dialogActive:x,handleConfirm:D,handleDialog:P,title:Object(u.__)("Reset Site Kit","google-site-kit"),subtitle:Object(f.a)(Object(u.__)("Resetting will disconnect all users and remove all Site Kit settings and data within WordPress. <br />You and any other users who wish to use Site Kit will need to reconnect to restore access.","google-site-kit"),{br:r.createElement("br",null)}),confirmButton:Object(u.__)("Reset","google-site-kit"),danger:!0,small:!0,inProgress:w})))}}).call(this,n(28),n(4))},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a})),n.d(t,"e",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return l})),n.d(t,"M",(function(){return s})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return f})),n.d(t,"J",(function(){return g})),n.d(t,"I",(function(){return p})),n.d(t,"N",(function(){return m})),n.d(t,"f",(function(){return h})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return b})),n.d(t,"j",(function(){return k})),n.d(t,"l",(function(){return E})),n.d(t,"m",(function(){return _})),n.d(t,"n",(function(){return y})),n.d(t,"o",(function(){return O})),n.d(t,"q",(function(){return S})),n.d(t,"s",(function(){return j})),n.d(t,"r",(function(){return w})),n.d(t,"t",(function(){return C})),n.d(t,"w",(function(){return N})),n.d(t,"u",(function(){return R})),n.d(t,"v",(function(){return x})),n.d(t,"x",(function(){return z})),n.d(t,"y",(function(){return A})),n.d(t,"A",(function(){return T})),n.d(t,"B",(function(){return L})),n.d(t,"C",(function(){return M})),n.d(t,"D",(function(){return D})),n.d(t,"k",(function(){return P})),n.d(t,"F",(function(){return I})),n.d(t,"z",(function(){return B})),n.d(t,"G",(function(){return F})),n.d(t,"E",(function(){return H})),n.d(t,"i",(function(){return V})),n.d(t,"p",(function(){return W})),n.d(t,"Q",(function(){return U})),n.d(t,"P",(function(){return G}));var r="core/user",a="connected_url_mismatch",i="__global",o="temporary_persist_permission_error",c="adblocker_active",l="googlesitekit_authenticate",s="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",f="googlesitekit_read_shared_module_data",g="googlesitekit_manage_module_sharing_options",p="googlesitekit_delegate_module_sharing_management",m="googlesitekit_update_plugins",h="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",b="kmAnalyticsLeastEngagingPages",k="kmAnalyticsNewVisitors",E="kmAnalyticsPopularAuthors",_="kmAnalyticsPopularContent",y="kmAnalyticsPopularProducts",O="kmAnalyticsReturningVisitors",S="kmAnalyticsTopCities",j="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",C="kmAnalyticsTopCitiesDrivingPurchases",N="kmAnalyticsTopDeviceDrivingPurchases",R="kmAnalyticsTopConvertingTrafficSource",x="kmAnalyticsTopCountries",z="kmAnalyticsTopPagesDrivingLeads",A="kmAnalyticsTopRecentTrendingPages",T="kmAnalyticsTopTrafficSource",L="kmAnalyticsTopTrafficSourceDrivingAddToCart",M="kmAnalyticsTopTrafficSourceDrivingLeads",D="kmAnalyticsTopTrafficSourceDrivingPurchases",P="kmAnalyticsPagesPerVisit",I="kmAnalyticsVisitLength",B="kmAnalyticsTopReturningVisitorPages",F="kmSearchConsolePopularKeywords",H="kmAnalyticsVisitsPerVisitor",V="kmAnalyticsMostEngagingPages",W="kmAnalyticsTopCategories",U=[h,v,b,k,E,_,y,O,W,S,j,w,C,N,R,x,A,T,L,P,I,B,H,V,W],G=[].concat(U,[F])},70:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},71:function(e,t,n){"use strict";n.r(t),n.d(t,"CONTEXT_MAIN_DASHBOARD_KEY_METRICS",(function(){return r})),n.d(t,"CONTEXT_MAIN_DASHBOARD_TRAFFIC",(function(){return a})),n.d(t,"CONTEXT_MAIN_DASHBOARD_CONTENT",(function(){return i})),n.d(t,"CONTEXT_MAIN_DASHBOARD_SPEED",(function(){return o})),n.d(t,"CONTEXT_MAIN_DASHBOARD_MONETIZATION",(function(){return c})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_TRAFFIC",(function(){return l})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_CONTENT",(function(){return s})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_SPEED",(function(){return u})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_MONETIZATION",(function(){return d}));var r="mainDashboardKeyMetrics",a="mainDashboardTraffic",i="mainDashboardContent",o="mainDashboardSpeed",c="mainDashboardMonetization",l="entityDashboardTraffic",s="entityDashboardContent",u="entityDashboardSpeed",d="entityDashboardMonetization";t.default={CONTEXT_MAIN_DASHBOARD_KEY_METRICS:r,CONTEXT_MAIN_DASHBOARD_TRAFFIC:a,CONTEXT_MAIN_DASHBOARD_CONTENT:i,CONTEXT_MAIN_DASHBOARD_SPEED:o,CONTEXT_MAIN_DASHBOARD_MONETIZATION:c,CONTEXT_ENTITY_DASHBOARD_TRAFFIC:l,CONTEXT_ENTITY_DASHBOARD_CONTENT:s,CONTEXT_ENTITY_DASHBOARD_SPEED:u,CONTEXT_ENTITY_DASHBOARD_MONETIZATION:d}},72:function(e,t,n){"use strict";var r=n(15),a=n.n(r),i=n(265),o=n(1),c=n.n(o),l=n(0),s=n(144);function Portal(e){var t=e.children,n=e.slug,r=Object(l.useState)(document.createElement("div")),o=a()(r,1)[0];return Object(i.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(s.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},73:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),a=n(18),i=n(9);function o(e,t){var n=Object(a.a)(),o=null!=t?t:"".concat(n,"_").concat(e);return{view:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.I.apply(void 0,[o,"view_notification"].concat(t))}),[o]),confirm:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.I.apply(void 0,[o,"confirm_notification"].concat(t))}),[o]),dismiss:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.I.apply(void 0,[o,"dismiss_notification"].concat(t))}),[o]),clickLearnMore:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.I.apply(void 0,[o,"click_learn_more_link"].concat(t))}),[o])}}},731:function(e,t,n){"use strict";(function(e){n.d(t,"c",(function(){return m})),n.d(t,"d",(function(){return h})),n.d(t,"b",(function(){return v})),n.d(t,"a",(function(){return b}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(45),l=n.n(c),s=n(13),u=n(1186),d=n(264),f=/^(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}$/,g=/\.(example|invalid|localhost|test)$/,p=[{subnet:"10.0.0.0",mask:8},{subnet:"*********",mask:8},{subnet:"**********",mask:12},{subnet:"***********",mask:16}],m=function(){var t=o()(a.a.mark((function t(){var n,r,i,o,c,l,s;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.location,r=n.hostname,!n.port){t.next=3;break}throw d.f;case 3:if(!f.test(r)){t.next=14;break}i=0,o=p;case 5:if(!(i<o.length)){t.next=12;break}if(c=o[i],l=c.mask,s=c.subnet,!Object(u.a)(r,s,l)){t.next=9;break}throw d.f;case 9:i++,t.next=5;break;case 12:t.next=16;break;case 14:if(r.includes(".")&&!r.match(g)){t.next=16;break}throw d.f;case 16:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),h=function(e){return o()(a.a.mark((function t(){return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.dispatch(s.c).checkForSetupTag();case 2:if(!t.sent.error){t.next=5;break}throw d.h;case 5:case"end":return t.stop()}}),t)})))},v=function(){var e=o()(a.a.mark((function e(){var t,n,r,i,o;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,l.a.get("core","site","health-checks",void 0,{useCache:!1}).catch((function(e){if("fetch_error"===e.code)throw d.d;throw d.c}));case 2:if(null==(o=e.sent)||null===(t=o.checks)||void 0===t||null===(n=t.googleAPI)||void 0===n?void 0:n.pass){e.next=5;break}throw d.e;case 5:if(null==o||null===(r=o.checks)||void 0===r||null===(i=r.skService)||void 0===i?void 0:i.pass){e.next=7;break}throw d.g;case 7:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),b=function(){var e=o()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,fetch(d.a).catch((function(){throw d.b}));case 2:if(e.sent.ok){e.next=5;break}throw d.b;case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(33),a=n.n(r),i=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:i.a.sanitize(e,t)}};function c(e){var t,n="object"===a()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(1),a=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,a=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:a}},n)}IconWrapper.propTypes={children:a.a.node.isRequired,marginLeft:a.a.number,marginRight:a.a.number}}).call(this,n(4))},77:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.label,i=t.className,c=t.hasLeftSpacing,s=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",a()({ref:n},u,{className:l()("googlesitekit-badge",i,{"googlesitekit-badge--has-left-spacing":s})}),r)}));f.displayName="Badge",f.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=f}).call(this,n(4))},79:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(15),a=n.n(r),i=n(188),o=n(133),c={},l=void 0===e?null:e,s=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,u=void 0===r?0:r,d=e.initialHeight,f=void 0===d?0:d,g=Object(i.a)("undefined"==typeof document?[u,f]:s,t,n),p=a()(g,2),m=p[0],h=p[1],v=function(){return h(s)};return Object(o.a)(l,"resize",v),Object(o.a)(l,"orientationchange",v),m},d=function(e){return u(e)[0]}}).call(this,n(28))},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"s",(function(){return i})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return l})),n.d(t,"g",(function(){return s})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return f})),n.d(t,"k",(function(){return g})),n.d(t,"m",(function(){return p})),n.d(t,"n",(function(){return m})),n.d(t,"h",(function(){return h})),n.d(t,"x",(function(){return v})),n.d(t,"w",(function(){return b})),n.d(t,"y",(function(){return k})),n.d(t,"u",(function(){return E})),n.d(t,"v",(function(){return _})),n.d(t,"f",(function(){return y})),n.d(t,"l",(function(){return O})),n.d(t,"e",(function(){return S})),n.d(t,"t",(function(){return j})),n.d(t,"c",(function(){return w})),n.d(t,"d",(function(){return C})),n.d(t,"b",(function(){return N}));var r="modules/analytics-4",a="account_create",i="property_create",o="webdatastream_create",c="analyticsSetup",l=10,s=1,u="https://www.googleapis.com/auth/tagmanager.readonly",d="enhanced-measurement-form",f="enhanced-measurement-enabled",g="enhanced-measurement-should-dismiss-activation-banner",p="analyticsAccountCreate",m="analyticsCustomDimensionsCreate",h="https://www.googleapis.com/auth/analytics.edit",v="dashboardAllTrafficWidgetDimensionName",b="dashboardAllTrafficWidgetDimensionColor",k="dashboardAllTrafficWidgetDimensionValue",E="dashboardAllTrafficWidgetActiveRowIndex",_="dashboardAllTrafficWidgetLoaded",y={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},O={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},S=[O.CONTACT,O.GENERATE_LEAD,O.SUBMIT_LEAD_FORM],j={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},w="audiencePermissionsSetup",C="audienceTileCustomDimensionCreate",N="audience-selection-panel-expirable-new-badge-"},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(106);function a(e){try{return new URL(e).pathname}catch(e){}return null}function i(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),a=e.replace(n.origin,"");if(a.length<t)return a;var i=a.length-Math.floor(t)+1;return"…"+a.substr(i)}},808:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GetHelpLink}));var r,a=n(6),i=n.n(a),o=n(1),c=n.n(o),l=n(2),s=n(3),u=n(20),d=n(13),f=n(264),g=(r={},i()(r,f.b,"amp_cdn_restricted"),i()(r,f.c,"check_api_unavailable"),i()(r,f.h,"setup_token_mismatch"),r);function GetHelpLink(t){var n=t.errorCode,r=Object(s.useSelect)((function(e){return e(d.c).getErrorTroubleshootingLinkURL({code:g[n]})}));return r?e.createElement(u.a,{href:r,external:!0},Object(l.__)("Get help","google-site-kit")):null}GetHelpLink.propTypes={errorCode:c.a.string.isRequired}}).call(this,n(4))},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return O})),n.d(t,"d",(function(){return S})),n.d(t,"e",(function(){return w})),n.d(t,"c",(function(){return C})),n.d(t,"b",(function(){return N}));var r=n(15),a=n.n(r),i=n(33),o=n.n(i),c=n(6),l=n.n(c),s=n(25),u=n.n(s),d=n(14),f=n(63),g=n.n(f),p=n(2);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=E(e,t),r=n.formatUnit,a=n.formatDecimal;try{return r()}catch(e){return a()}},b=function(e){var t=k(e),n=t.hours,r=t.minutes,a=t.seconds;return a=("0"+a).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(a):"".concat(n,":").concat(r,":").concat(a)},k=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},E=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=k(e),r=n.hours,a=n.minutes,i=n.seconds;return{hours:r,minutes:a,seconds:i,formatUnit:function(){var n=t.unitDisplay,o=h(h({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(i,h(h({},o),{},{unit:"second"})):Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?w(i,h(h({},o),{},{unit:"second"})):"",a?w(a,h(h({},o),{},{unit:"minute"})):"",r?w(r,h(h({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(p.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(p.__)("%ds","google-site-kit"),i);if(0===e)return t;var n=Object(p.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(p.__)("%dm","google-site-kit"),a),o=Object(p.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(p.__)("%dh","google-site-kit"),r);return Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?t:"",a?n:"",r?o:"").trim()}}},_=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},y=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in millions.
Object(p.__)("%sM","google-site-kit"),w(_(e),e%10==0?{}:t)):1e4<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(_(e))):1e3<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(_(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function O(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=h({},e)),t}function S(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=O(t),r=n.style,a=void 0===r?"metric":r;return"metric"===a?y(e):"duration"===a?v(e,n):"durationISO"===a?b(e):w(e,n)}var j=g()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?N():n,i=u()(t,["locale"]);try{return new Intl.NumberFormat(r,i).format(e)}catch(t){j("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(i)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},l=["signDisplay","compactDisplay"],s={},d=0,f=Object.entries(i);d<f.length;d++){var g=a()(f[d],2),p=g[0],m=g[1];c[p]&&m===c[p]||(l.includes(p)||(s[p]=m))}try{return new Intl.NumberFormat(r,s).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},C=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?N():n,a=t.style,i=void 0===a?"long":a,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var l=new Intl.ListFormat(r,{style:i,type:c});return l.format(e)}
/* translators: used between list items, there is a space after the comma. */var s=Object(p.__)(", ","google-site-kit");return e.join(s)},N=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},836:function(e,t,n){"use strict";(function(e,r){var a=n(5),i=n.n(a),o=n(16),c=n.n(o),l=n(51),s=n.n(l),u=n(53),d=n.n(u),f=n(237),g=n.n(f),p=n(68),m=n.n(p),h=n(69),v=n.n(h),b=n(49),k=n.n(b),E=n(1),_=n.n(E),y=n(2),O=n(0),S=n(45),j=n.n(S),w=n(10),C=n(9);function N(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=k()(e);if(t){var a=k()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return v()(this,n)}}var R=function(t){m()(SearchConsole,t);var n,a=N(SearchConsole);function SearchConsole(t){var n;s()(this,SearchConsole),n=a.call(this,t);var r=e._googlesitekitLegacyData.admin.siteURL;return n.state={loading:!0,sites:!1,selectedURL:r,siteURL:r,connected:!1,errorCode:!1,errorMsg:""},n.handleURLSelect=n.handleURLSelect.bind(g()(n)),n.insertPropertyToSearchConsole=n.insertPropertyToSearchConsole.bind(g()(n)),n.submitPropertyEventHandler=n.submitPropertyEventHandler.bind(g()(n)),n}return d()(SearchConsole,[{key:"componentDidMount",value:function(){var e=this.props,t=e.isAuthenticated,n=e.shouldSetup;t&&n&&this.requestSearchConsoleSiteList()}},{key:"requestSearchConsoleSiteList",value:function(){var e=this,t=this.props.setErrorMessage;c()(i.a.mark((function n(){var r;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,j.a.get("modules","search-console","matched-sites",void 0,{useCache:!1});case 3:if(1!==(r=n.sent).length){n.next=9;break}return n.next=7,e.insertPropertyToSearchConsole(r[0].siteURL);case 7:return e.props.searchConsoleSetup(r[0].siteURL),n.abrupt("return");case 9:if(r.length){n.next=11;break}throw{code:"no_property_matched",message:Object(y.__)("Your site has not been added to Search Console yet. Would you like to add it now?","google-site-kit")};case 11:throw t(""),e.setState({loading:!1,selectedURL:r[0].siteURL,sites:r}),{code:"multiple_properties_matched",message:Object(y.sprintf)(
/* translators: %s: the number of matching properties */
Object(y.__)("We found %d existing accounts. Please choose which one to use below.","google-site-kit"),r.length)};case 16:n.prev=16,n.t0=n.catch(0),t(n.t0.message),e.setState({loading:!1,errorCode:n.t0.code,errorMsg:n.t0.message});case 20:case"end":return n.stop()}}),n,null,[[0,16]])})))()}},{key:"insertPropertyToSearchConsole",value:(n=c()(i.a.mark((function e(t){var n,r=arguments;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.length>1&&void 0!==r[1]&&r[1],e.next=3,j.a.set("modules","search-console","site",{siteURL:t});case 3:if(!n){e.next=6;break}return e.next=6,Object(C.I)("search_console_setup","add_new_sc_property");case 6:this.setState({loading:!1,connected:!0});case 7:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"submitPropertyEventHandler",value:function(){var e=this,t=this.state,n=t.selectedURL,r=t.errorCode,a=this.props.setErrorMessage;c()(i.a.mark((function t(){return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.insertPropertyToSearchConsole(n,"no_property_matched"===r);case 3:a(""),e.props.searchConsoleSetup(n),t.next=11;break;case 7:t.prev=7,t.t0=t.catch(0),a(t.t0.message[0].message),e.setState({loading:!1,errorCode:t.t0.code,errorMsg:t.t0.message[0].message});case 11:case"end":return t.stop()}}),t,null,[[0,7]])})))()}},{key:"handleURLSelect",value:function(e,t){this.setState({selectedURL:t.getAttribute("data-value")})}},{key:"matchedForm",value:function(){var e=this.state,t=e.sites,n=e.selectedURL;if(!t)return null;var a=t.map((function(e){var t=e.siteURL;return t.startsWith("sc-domain:")&&(t=Object(y.sprintf)(
/* translators: %s: Search Console property domain name */
Object(y.__)("%s (domain property)","google-site-kit"),t.replace(/^sc-domain:/,""))),{label:t,value:e.siteURL}}));return r.createElement(O.Fragment,null,r.createElement("div",{className:"googlesitekit-setup-module__inputs"},r.createElement(w.Select,{enhanced:!0,name:"siteProperty",label:Object(y.__)("Choose URL","google-site-kit"),outlined:!0,onEnhancedChange:this.handleURLSelect,options:a,value:n})),r.createElement("div",{className:"googlesitekit-wizard-step__action googlesitekit-wizard-step__action--justify"},r.createElement(w.Button,{onClick:this.submitPropertyEventHandler},Object(y.__)("Continue","google-site-kit"))))}},{key:"noSiteForm",value:function(){var e=this.state.siteURL;return r.createElement(O.Fragment,null,r.createElement("div",{className:"googlesitekit-setup-module__inputs"},r.createElement(w.TextField,{label:Object(y.__)("Website Address","google-site-kit"),name:"siteProperty",outlined:!0,value:e})),r.createElement("div",{className:"googlesitekit-wizard-step__action googlesitekit-wizard-step__action--justify"},r.createElement(w.Button,{onClick:this.submitPropertyEventHandler},Object(y.__)("Continue","google-site-kit"))))}},{key:"renderForm",value:function(){var e=this.state,t=e.loading,n=e.sites;return t?r.createElement(O.Fragment,null,r.createElement("p",null,Object(y.__)("We’re locating your Search Console account.","google-site-kit")),r.createElement(w.ProgressBar,null)):0===n.length?this.noSiteForm():this.matchedForm()}},{key:"render",value:function(){var e=this.props,t=e.isAuthenticated,n=e.shouldSetup,a=this.state,i=a.errorMsg,o=a.connected;return!n||o?SearchConsole.connected():r.createElement("section",{className:"googlesitekit-setup-module googlesitekit-setup-module--search-console"},r.createElement("h2",{className:" googlesitekit-heading-3 googlesitekit-setup-module__title "},Object(y._x)("Search Console","Service name","google-site-kit")),i&&0<i.length&&r.createElement("p",{className:"googlesitekit-error-text"},i),t&&n&&this.renderForm())}}],[{key:"connected",value:function(){return r.createElement("section",{className:"googlesitekit-setup-module googlesitekit-setup-module--search-console"},r.createElement("h2",{className:" googlesitekit-heading-3 googlesitekit-setup-module__title "},Object(y._x)("Search Console","Service name","google-site-kit")),r.createElement("p",{className:"googlesitekit-setup-module__text--no-margin"},Object(y.__)("Your Search Console is set up with Site Kit.","google-site-kit")))}}]),SearchConsole}(O.Component);R.propTypes={isAuthenticated:_.a.bool.isRequired,shouldSetup:_.a.bool.isRequired,searchConsoleSetup:_.a.func.isRequired,setErrorMessage:_.a.func.isRequired},t.a=R}).call(this,n(28),n(4))},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a}));var r=n(149),a=n.n(r)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i);function ChangeArrow(t){var n=t.direction,r=t.invertColor,a=t.width,i=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:a,height:i,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:a.a.string,invertColor:a.a.bool,width:a.a.number,height:a.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},86:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={EXTERNAL:"external",INTERNAL:"internal"}},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(12),a=n.n(r),i=function(e,t){var n=t.dateRangeLength;a()(Array.isArray(e),"report must be an array to partition."),a()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return a.b})),n.d(t,"J",(function(){return a.c})),n.d(t,"F",(function(){return i.a})),n.d(t,"K",(function(){return i.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return m})),n.d(t,"j",(function(){return h})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return O})),n.d(t,"c",(function(){return S})),n.d(t,"e",(function(){return j})),n.d(t,"b",(function(){return w})),n.d(t,"a",(function(){return C})),n.d(t,"f",(function(){return N})),n.d(t,"n",(function(){return R})),n.d(t,"w",(function(){return x})),n.d(t,"p",(function(){return z})),n.d(t,"G",(function(){return A})),n.d(t,"s",(function(){return T})),n.d(t,"v",(function(){return L})),n.d(t,"k",(function(){return M})),n.d(t,"o",(function(){return D.b})),n.d(t,"h",(function(){return D.a})),n.d(t,"t",(function(){return P.b})),n.d(t,"q",(function(){return P.a})),n.d(t,"A",(function(){return P.c})),n.d(t,"x",(function(){return I})),n.d(t,"u",(function(){return B})),n.d(t,"E",(function(){return V})),n.d(t,"D",(function(){return W.a})),n.d(t,"g",(function(){return U})),n.d(t,"L",(function(){return G})),n.d(t,"l",(function(){return K}));var r=n(14),a=n(36),i=n(75),o=n(33),c=n.n(o),l=n(96),s=n.n(l),u=function(e){return s()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var a=t[r];a&&"object"===c()(a)&&!Array.isArray(a)&&(a=e(a)),n[r]=a})),n}(e)))};n(97);var d=n(83);function f(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function g(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function p(e){return e.replace(/\n/gi,"<br>")}function m(e){for(var t=e,n=0,r=[f,g,p];n<r.length;n++){t=(0,r[n])(t)}return t}var h=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},b=n(15),k=n.n(b),E=n(12),_=n.n(E),y=n(2),O="Invalid dateString parameter, it must be a string.",S='Invalid date range, it must be a string with the format "last-x-days".',j=60,w=60*j,C=24*w,N=7*C;function R(){var e=function(e){return Object(y.sprintf)(
/* translators: %s: number of days */
Object(y._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function x(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function z(e){_()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function A(e){_()(x(e),O);var t=e.split("-"),n=k()(t,3),r=n[0],a=n[1],i=n[2];return new Date(r,a-1,i)}function T(e,t){return z(M(e,t*C))}function L(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function M(e,t){_()(x(e)||Object(r.isDate)(e)&&!isNaN(e),O);var n=x(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var D=n(98),P=n(80);function I(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function B(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var F=n(27),H=n.n(F),V=function(e){return Array.isArray(e)?H()(e).sort():e},W=n(89);function U(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var G=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},K=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},90:function(e,t,n){"use strict";(function(e,r){n.d(t,"c",(function(){return b})),n.d(t,"b",(function(){return E})),n.d(t,"a",(function(){return TourTooltips}));var a=n(6),i=n.n(a),o=n(81),c=n(30),l=n(1),s=n.n(l),u=n(2),d=n(3),f=n(23),g=n(7),p=n(36),m=n(107),h=n(18);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var b={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},k={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},E={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},_="feature_tooltip_view",y="feature_tooltip_advance",O="feature_tooltip_return",S="feature_tooltip_dismiss",j="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,a=t.tourID,l=t.gaEventCategory,s=t.callback,u="".concat(a,"-step"),w="".concat(a,"-run"),C=Object(d.useDispatch)(f.b).setValue,N=Object(d.useDispatch)(g.a).dismissTour,R=Object(d.useRegistry)(),x=Object(h.a)(),z=Object(d.useSelect)((function(e){return e(f.b).getValue(u)})),A=Object(d.useSelect)((function(e){return e(f.b).getValue(w)&&!1===e(g.a).isTourDismissed(a)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(a)),C(w,!0)}));var T=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return r.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,r=e.lifecycle,a=e.size,i=e.status,o=e.type,s=t+1,u="function"==typeof l?l(x):l;o===c.b.TOOLTIP&&r===c.c.TOOLTIP?Object(p.b)(u,_,s):n===c.a.CLOSE&&r===c.c.COMPLETE?Object(p.b)(u,S,s):n===c.a.NEXT&&i===c.d.FINISHED&&o===c.b.TOUR_END&&a===s&&Object(p.b)(u,j,s),r===c.c.COMPLETE&&i!==c.d.FINISHED&&(n===c.a.PREV&&Object(p.b)(u,O,s),n===c.a.NEXT&&Object(p.b)(u,y,s))}(t);var n=t.action,r=t.index,i=t.status,o=t.step,d=t.type,f=n===c.a.CLOSE,g=!f&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),m=[c.d.FINISHED,c.d.SKIPPED].includes(i),h=f&&d===c.b.STEP_AFTER,v=m||h;if(c.b.STEP_BEFORE===d){var b,k,E=o.target;"string"==typeof o.target&&(E=e.document.querySelector(o.target)),null===(b=E)||void 0===b||null===(k=b.scrollIntoView)||void 0===k||k.call(b,{block:"center"})}g?function(e,t){C(u,e+(t===c.a.PREV?-1:1))}(r,n):v&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(a)),N(a)),s&&s(t,R)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:E,locale:k,run:A,showProgress:!0,stepIndex:z,steps:T,styles:b,tooltipComponent:m.a})}TourTooltips.propTypes={steps:s.a.arrayOf(s.a.object).isRequired,tourID:s.a.string.isRequired,gaEventCategory:s.a.oneOfType([s.a.string,s.a.func]).isRequired,callback:s.a.func}}).call(this,n(28),n(4))},93:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(24),a=n(130);function i(t,n){var r=document.querySelector(t);if(!r)return 0;var a=r.getBoundingClientRect().top,i=o(n);return a+e.scrollY-i}function o(e){var t=c(e),n=document.querySelectorAll(".googlesitekit-navigation, .googlesitekit-entity-header");return t+=Array.from(n).reduce((function(e,t){return e+t.offsetHeight}),0)}function c(t){var n=0,i=document.querySelector(".googlesitekit-header");return n=!!i&&"sticky"===e.getComputedStyle(i).position?function(e){var t=document.querySelector(".googlesitekit-header");if(t){if(e===r.b)return t.offsetHeight;var n=t.getBoundingClientRect().bottom;return n<0?0:n}return 0}(t):function(e){var t=document.querySelector("#wpadminbar");return t&&e!==r.b?t.offsetHeight:0}(t),(n=Object(a.a)(n))<0?0:n}}).call(this,n(28))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o}));var r=n(239),a=n(85),i=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var i=n.invertColor,o=void 0!==i&&i;return Object(r.a)(e.createElement(a.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(6),a=n.n(r),i=n(14),o=n(100),c=n(101);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=s(s({},u),t);a.referenceSiteURL&&(a.referenceSiteURL=a.referenceSiteURL.toString().replace(/\/+$/,""));var l=Object(o.a)(a,n),d=Object(c.a)(a,n,l,r),f={},g=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);f[r]||(f[r]=Object(i.once)(d)),f[r].apply(f,t)};return{enableTracking:function(){a.trackingEnabled=!0},disableTracking:function(){a.trackingEnabled=!1},initializeSnippet:l,isTrackingEnabled:function(){return!!a.trackingEnabled},trackEvent:d,trackEventOnce:g}}}).call(this,n(28))}},[[1288,1,0]]]);