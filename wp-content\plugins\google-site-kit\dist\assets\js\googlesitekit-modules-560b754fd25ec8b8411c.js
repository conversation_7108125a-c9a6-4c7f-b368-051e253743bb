(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[17],{100:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return o}));var n=r(59),i=r(39),a=r(57);function o(t,r){var o,c=Object(n.a)(r),s=t.activeModules,u=t.referenceSiteURL,l=t.userIDHash,d=t.userRoles,g=void 0===d?[]:d,f=t.isAuthenticated,v=t.pluginVersion;return function(){var r=e.document;if(void 0===o&&(o=!!r.querySelector("script[".concat(i.b,"]"))),!o){o=!0;var n=(null==g?void 0:g.length)?g.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:u,plugin_version:v||"",enabled_features:Array.from(a.a).join(","),active_modules:s.join(","),authenticated:f?"1":"0",user_properties:{user_roles:n,user_identifier:l}});var d=r.createElement("script");return d.setAttribute(i.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a),r.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a)}}}}}).call(this,r(28))},101:function(e,t,r){"use strict";r.d(t,"a",(function(){return g}));var n=r(5),i=r.n(n),a=r(6),o=r.n(a),c=r(16),s=r.n(c),u=r(59);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach((function(t){o()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function g(e,t,r,n){var a=Object(u.a)(t);return function(){var t=s()(i.a.mark((function t(o,c,s,u){var l;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return r(),l={send_to:"site_kit",event_category:o,event_label:s,value:u},t.abrupt("return",new Promise((function(e){var t,r,i=setTimeout((function(){n.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(i),e()};a("event",c,d(d({},l),{},{event_callback:s})),(null===(t=n._gaUserPrefs)||void 0===t||null===(r=t.ioo)||void 0===r?void 0:r.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,r,n,i){return t.apply(this,arguments)}}()}},102:function(e,t,r){"use strict";var n=r(123);r.d(t,"a",(function(){return n.a}));var i=r(124);r.d(t,"c",(function(){return i.a}));var a=r(125);r.d(t,"b",(function(){return a.a}))},123:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return Cell}));var n=r(21),i=r.n(n),a=r(6),o=r.n(a),c=r(25),s=r.n(c),u=r(1),l=r.n(u),d=r(11),g=r.n(d);function Cell(t){var r,n=t.className,a=t.alignTop,c=t.alignMiddle,u=t.alignBottom,l=t.alignRight,d=t.alignLeft,f=t.smAlignRight,v=t.mdAlignRight,p=t.lgAlignRight,b=t.smSize,h=t.smStart,m=t.smOrder,S=t.mdSize,O=t.mdStart,y=t.mdOrder,E=t.lgSize,j=t.lgStart,R=t.lgOrder,k=t.size,w=t.children,_=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",i()({},_,{className:g()(n,"mdc-layout-grid__cell",(r={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":u,"mdc-layout-grid__cell--align-right":l,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":f,"mdc-layout-grid__cell--align-right-tablet":v,"mdc-layout-grid__cell--align-right-desktop":p},o()(r,"mdc-layout-grid__cell--span-".concat(k),12>=k&&k>0),o()(r,"mdc-layout-grid__cell--span-".concat(E,"-desktop"),12>=E&&E>0),o()(r,"mdc-layout-grid__cell--start-".concat(j,"-desktop"),12>=j&&j>0),o()(r,"mdc-layout-grid__cell--order-".concat(R,"-desktop"),12>=R&&R>0),o()(r,"mdc-layout-grid__cell--span-".concat(S,"-tablet"),8>=S&&S>0),o()(r,"mdc-layout-grid__cell--start-".concat(O,"-tablet"),8>=O&&O>0),o()(r,"mdc-layout-grid__cell--order-".concat(y,"-tablet"),8>=y&&y>0),o()(r,"mdc-layout-grid__cell--span-".concat(b,"-phone"),4>=b&&b>0),o()(r,"mdc-layout-grid__cell--start-".concat(h,"-phone"),4>=h&&h>0),o()(r,"mdc-layout-grid__cell--order-".concat(m,"-phone"),4>=m&&m>0),r))}),w)}Cell.propTypes={smSize:l.a.number,smStart:l.a.number,smOrder:l.a.number,mdSize:l.a.number,mdStart:l.a.number,mdOrder:l.a.number,lgSize:l.a.number,lgStart:l.a.number,lgOrder:l.a.number,size:l.a.number,alignTop:l.a.bool,alignMiddle:l.a.bool,alignBottom:l.a.bool,alignRight:l.a.bool,alignLeft:l.a.bool,smAlignRight:l.a.bool,mdAlignRight:l.a.bool,lgAlignRight:l.a.bool,className:l.a.string,children:l.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,r(4))},124:function(e,t,r){"use strict";(function(e){var n=r(21),i=r.n(n),a=r(25),o=r.n(a),c=r(1),s=r.n(c),u=r(11),l=r.n(u),d=r(0),g=Object(d.forwardRef)((function(t,r){var n=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",i()({ref:r,className:l()("mdc-layout-grid__inner",n)},c),a)}));g.displayName="Row",g.propTypes={className:s.a.string,children:s.a.node},g.defaultProps={className:""},t.a=g}).call(this,r(4))},125:function(e,t,r){"use strict";(function(e){var n=r(21),i=r.n(n),a=r(25),o=r.n(a),c=r(1),s=r.n(c),u=r(11),l=r.n(u),d=r(0),g=Object(d.forwardRef)((function(t,r){var n=t.alignLeft,a=t.fill,c=t.className,s=t.children,u=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",i()({className:l()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":n,"mdc-layout-grid--collapsed":u,"mdc-layout-grid--fill":a})},d,{ref:r}),s)}));g.displayName="Grid",g.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},g.defaultProps={className:""},t.a=g}).call(this,r(4))},1257:function(e,t,r){"use strict";r.r(t),function(e){var n=r(3),i=r.n(n),a=r(838);Object(a.b)(i.a);var o=Object(a.a)(i.a);void 0===e.googlesitekit&&(e.googlesitekit={}),e.googlesitekit.modules=o,t.default=o}.call(this,r(28))},126:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("g",{fill:"none",fillRule:"evenodd"},n.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),n.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return n.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},127:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("g",{fill:"none",fillRule:"evenodd"},n.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),n.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return n.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},128:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return n.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},13:function(e,t,r){"use strict";r.d(t,"c",(function(){return n})),r.d(t,"a",(function(){return i})),r.d(t,"b",(function(){return a}));var n="core/site",i="primary",a="secondary"},19:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return i}));var n="core/modules",i="insufficient_module_dependencies"},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,r){"use strict";(function(e){var n=r(21),i=r.n(n),a=r(25),o=r.n(a),c=r(11),s=r.n(c),u=r(1),l=r.n(u),d=r(146),g=r(0),f=r(2),v=r(126),p=r(127),b=r(128),h=r(70),m=r(76),S=Object(g.forwardRef)((function(t,r){var n,a=t["aria-label"],c=t.secondary,u=void 0!==c&&c,l=t.arrow,g=void 0!==l&&l,S=t.back,O=void 0!==S&&S,y=t.caps,E=void 0!==y&&y,j=t.children,R=t.className,k=void 0===R?"":R,w=t.danger,_=void 0!==w&&w,A=t.disabled,C=void 0!==A&&A,N=t.external,M=void 0!==N&&N,D=t.hideExternalIndicator,T=void 0!==D&&D,I=t.href,x=void 0===I?"":I,P=t.inverse,L=void 0!==P&&P,q=t.noFlex,U=void 0!==q&&q,H=t.onClick,G=t.small,B=void 0!==G&&G,F=t.standalone,V=void 0!==F&&F,K=t.linkButton,z=void 0!==K&&K,W=t.to,$=t.leadingIcon,J=t.trailingIcon,Q=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),X=x||W||!H?W?"ROUTER_LINK":M?"EXTERNAL_LINK":"LINK":C?"BUTTON_DISABLED":"BUTTON",Y="BUTTON"===X||"BUTTON_DISABLED"===X?"button":"ROUTER_LINK"===X?d.b:"a",Z=("EXTERNAL_LINK"===X&&(n=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===X&&(n=Object(f._x)("(disabled)","screen reader text","google-site-kit")),n?a?"".concat(a," ").concat(n):"string"==typeof j?"".concat(j," ").concat(n):void 0:a),ee=$,te=J;return O&&(ee=e.createElement(b.a,{width:14,height:14})),M&&!T&&(te=e.createElement(h.a,{width:14,height:14})),g&&!L&&(te=e.createElement(v.a,{width:14,height:14})),g&&L&&(te=e.createElement(p.a,{width:14,height:14})),e.createElement(Y,i()({"aria-label":Z,className:s()("googlesitekit-cta-link",k,{"googlesitekit-cta-link--secondary":u,"googlesitekit-cta-link--inverse":L,"googlesitekit-cta-link--small":B,"googlesitekit-cta-link--caps":E,"googlesitekit-cta-link--danger":_,"googlesitekit-cta-link--disabled":C,"googlesitekit-cta-link--standalone":V,"googlesitekit-cta-link--link-button":z,"googlesitekit-cta-link--no-flex":!!U}),disabled:C,href:"LINK"!==X&&"EXTERNAL_LINK"!==X||C?void 0:x,onClick:H,rel:"EXTERNAL_LINK"===X?"noopener noreferrer":void 0,ref:r,target:"EXTERNAL_LINK"===X?"_blank":void 0,to:W},Q),!!ee&&e.createElement(m.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},j),!!te&&e.createElement(m.a,{marginLeft:5},te))}));S.propTypes={arrow:l.a.bool,back:l.a.bool,caps:l.a.bool,children:l.a.node,className:l.a.string,danger:l.a.bool,disabled:l.a.bool,external:l.a.bool,hideExternalIndicator:l.a.bool,href:l.a.string,inverse:l.a.bool,leadingIcon:l.a.node,linkButton:l.a.bool,noFlex:l.a.bool,onClick:l.a.func,small:l.a.bool,standalone:l.a.bool,to:l.a.string,trailingIcon:l.a.node},t.a=S}).call(this,r(4))},200:function(e,t,r){"use strict";r.d(t,"a",(function(){return j})),r.d(t,"b",(function(){return R})),r.d(t,"c",(function(){return k})),r.d(t,"g",(function(){return w})),r.d(t,"f",(function(){return _})),r.d(t,"d",(function(){return A})),r.d(t,"e",(function(){return C}));var n=r(16),i=r.n(n),a=r(5),o=r.n(a),c=r(6),s=r.n(c),u=r(12),l=r.n(u),d=r(14),g=r(45),f=r.n(g),v=r(3),p=r(62),b=r(82),h=r(48),m=r(64);function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function O(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var y=m.a.clearError,E=m.a.receiveError,j="cannot submit changes while submitting changes",R="cannot submit changes if settings have not changed",k=function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=n.ownedSettingsSlugs,a=void 0===i?void 0:i,c=n.storeName,u=void 0===c?void 0:c,g=n.settingSlugs,m=void 0===g?[]:g,S=n.initialSettings,j=void 0===S?void 0:S,R=n.validateHaveSettingsChanged,k=void 0===R?C():R;l()(e,"type is required."),l()(t,"identifier is required."),l()(r,"datapoint is required.");var w=u||"".concat(e,"/").concat(t),_={ownedSettingsSlugs:a,settings:j,savedSettings:void 0},A=Object(h.a)({baseName:"getSettings",controlCallback:function(){return f.a.get(e,t,r,{},{useCache:!1})},reducerCallback:function(e,t){return O(O({},e),{},{savedSettings:O({},t),settings:O(O({},t),e.settings||{})})}}),N=Object(h.a)({baseName:"saveSettings",controlCallback:function(n){var i=n.values;return f.a.set(e,t,r,i)},reducerCallback:function(e,t){return O(O({},e),{},{savedSettings:O({},t),settings:O({},t)})},argsToParams:function(e){return{values:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.values;l()(Object(d.isPlainObject)(t),"values is required.")}}),M={},D={setSettings:function(e){return l()(Object(d.isPlainObject)(e),"values is required."),{payload:{values:e},type:"SET_SETTINGS"}},rollbackSettings:function(){return{payload:{},type:"ROLLBACK_SETTINGS"}},rollbackSetting:function(e){return l()(e,"setting is required."),{payload:{setting:e},type:"ROLLBACK_SETTING"}},saveSettings:o.a.mark((function e(){var t,r,n,i,a;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,y("saveSettings",[]);case 5:return r=t.select(w).getSettings(),e.next=8,N.actions.fetchSaveSettings(r);case 8:if(n=e.sent,i=n.response,!(a=n.error)){e.next=14;break}return e.next=14,E(a,"saveSettings",[]);case 14:return e.abrupt("return",{response:i,error:a});case 15:case"end":return e.stop()}}),e)}))},T={},I=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_,t=arguments.length>1?arguments[1]:void 0,r=t.type,n=t.payload;switch(r){case"SET_SETTINGS":var i=n.values;return O(O({},e),{},{settings:O(O({},e.settings||{}),i)});case"ROLLBACK_SETTINGS":return O(O({},e),{},{settings:e.savedSettings});case"ROLLBACK_SETTING":var a=n.setting;return e.savedSettings[a]?O(O({},e),{},{settings:O(O({},e.settings||{}),{},s()({},a,e.savedSettings[a]))}):O({},e);default:return void 0!==M[r]?M[r](e,{type:r,payload:n}):e}},x={getSettings:o.a.mark((function e(){var t;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v.commonActions.getRegistry();case 2:if(t=e.sent,t.select(w).getSettings()){e.next=7;break}return e.next=7,A.actions.fetchGetSettings();case 7:case"end":return e.stop()}}),e)}))},P=Object(p.g)(k),L=P.safeSelector,q=P.dangerousSelector,U={haveSettingsChanged:L,__dangerousHaveSettingsChanged:q,getSettings:function(e){return e.settings},hasSettingChanged:function(e,t){l()(t,"setting is required.");var r=e.settings,n=e.savedSettings;return!(!r||!n)&&!Object(d.isEqual)(r[t],n[t])},isDoingSaveSettings:function(e){return Object.values(e.isFetchingSaveSettings).some(Boolean)},getOwnedSettingsSlugs:function(e){return e.ownedSettingsSlugs},haveOwnedSettingsChanged:Object(v.createRegistrySelector)((function(e){return function(){var t=e(w).getOwnedSettingsSlugs();return e(w).haveSettingsChanged(t)}}))};m.forEach((function(e){var t=Object(b.b)(e),r=Object(b.a)(e);D["set".concat(t)]=function(e){return l()(void 0!==e,"value is required for calls to set".concat(t,"().")),{payload:{value:e},type:"SET_".concat(r)}},M["SET_".concat(r)]=function(t,r){var n=r.payload.value;return O(O({},t),{},{settings:O(O({},t.settings||{}),{},s()({},e,n))})},U["get".concat(t)]=Object(v.createRegistrySelector)((function(t){return function(){return(t(w).getSettings()||{})[e]}}))}));var H=Object(v.combineStores)(v.commonStore,A,N,{initialState:_,actions:D,controls:T,reducer:I,resolvers:x,selectors:U});return O(O({},H),{},{STORE_NAME:w})};function w(e,t){return function(){var r=i()(o.a.mark((function r(n){var i,a,c,s;return o.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(i=n.select,a=n.dispatch,!i(t).haveSettingsChanged()){r.next=8;break}return r.next=4,a(t).saveSettings();case 4:if(c=r.sent,!(s=c.error)){r.next=8;break}return r.abrupt("return",{error:s});case 8:return r.next=10,f.a.invalidateCache("modules",e);case 10:return r.abrupt("return",{});case 11:case"end":return r.stop()}}),r)})));return function(e){return r.apply(this,arguments)}}()}function _(e){return function(t){var r=t.select,n=t.dispatch;return r(e).haveSettingsChanged()?n(e).rollbackSettings():{}}}function A(e){return function(t){var r=Object(p.e)(t)(e),n=r.haveSettingsChanged,i=r.isDoingSubmitChanges;l()(!i(),j),l()(n(),R)}}function C(){return function(e,t,r){var n=t.settings,i=t.savedSettings;r&&l()(!Object(d.isEqual)(Object(d.pick)(n,r),Object(d.pick)(i,r)),R),l()(!Object(d.isEqual)(n,i),R)}}},217:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return WarningNotice}));var n=r(11),i=r.n(n),a=r(1),o=r.n(a);function WarningNotice(t){var r=t.children,n=t.className;return e.createElement("div",{className:i()("googlesitekit-warning-notice",n)},r)}WarningNotice.propTypes={children:o.a.node.isRequired,className:o.a.string}}).call(this,r(4))},245:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return AdBlockerWarning}));var n=r(1),i=r.n(n),a=r(3),o=r(13),c=r(19),s=r(395);function AdBlockerWarning(t){var r=t.moduleSlug,n=t.className,i=Object(a.useSelect)((function(e){return e(c.a).getModuleStoreName(r)})),u=Object(a.useSelect)((function(e){var t;return null===(t=e(i))||void 0===t?void 0:t.getAdBlockerWarningMessage()})),l=Object(a.useSelect)((function(e){return e(o.c).getDocumentationLinkURL("".concat(r,"-ad-blocker-detected"))}));return e.createElement(s.a,{className:n,getHelpLink:l,warningMessage:u})}AdBlockerWarning.propTypes={className:i.a.string,moduleSlug:i.a.string.isRequired}}).call(this,r(4))},3:function(e,t){e.exports=googlesitekit.data},36:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return O})),r.d(t,"b",(function(){return m})),r.d(t,"c",(function(){return S}));var n=r(99),i=e._googlesitekitTrackingData||{},a=i.activeModules,o=void 0===a?[]:a,c=i.isSiteKitScreen,s=i.trackingEnabled,u=i.trackingID,l=i.referenceSiteURL,d=i.userIDHash,g=i.isAuthenticated,f={activeModules:o,trackingEnabled:s,trackingID:u,referenceSiteURL:l,userIDHash:d,isSiteKitScreen:c,userRoles:i.userRoles,isAuthenticated:g,pluginVersion:"1.151.0"},v=Object(n.a)(f),p=v.enableTracking,b=v.disableTracking,h=(v.isTrackingEnabled,v.initializeSnippet),m=v.trackEvent,S=v.trackEventOnce;function O(e){e?p():b()}c&&s&&h()}).call(this,r(28))},39:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return i}));var n="_googlesitekitDataLayer",i="data-googlesitekit-gtag"},395:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return AdBlockerWarningMessage}));var n=r(1),i=r.n(n),a=r(2),o=r(38),c=r(20),s=r(217),u=r(396);function AdBlockerWarningMessage(t){var r=t.className,n=void 0===r?"":r,i=t.getHelpLink,l=void 0===i?"":i,d=t.warningMessage,g=void 0===d?null:d;return g?e.createElement(s.a,{className:n},Object(o.a)(Object(a.sprintf)(
/* translators: 1: The warning message. 2: "Get help" text. */
Object(a.__)("%1$s. <Link>%2$s</Link>","google-site-kit"),g,Object(a.__)("Get help","google-site-kit")),{Link:e.createElement(c.a,{href:l,external:!0,hideExternalIndicator:!0,trailingIcon:e.createElement(u.a,{width:15,height:15})})})):null}AdBlockerWarningMessage.propTypes={className:i.a.string,getHelpLink:i.a.string,warningMessage:i.a.string}}).call(this,r(4))},396:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("path",{d:"M4.5 1.5H3a2 2 0 00-2 2v7a2 2 0 002 2h7a2 2 0 002-2V9M7 1.5h5v5M5 8.5L11.5 2",stroke:"currentColor",strokeWidth:1.5});t.a=function SvgExternalRounded(e){return n.createElement("svg",i({viewBox:"0 0 13 14",fill:"none"},e),a)}},45:function(e,t){e.exports=googlesitekit.api},48:function(e,t,r){"use strict";r.d(t,"a",(function(){return O}));var n=r(5),i=r.n(n),a=r(6),o=r.n(a),c=r(12),s=r.n(c),u=r(14),l=r(64),d=r(82),g=r(9);function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){o()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var p=function(e){return e},b=function(){return{}},h=function(){},m=l.a.clearError,S=l.a.receiveError,O=function(e){var t,r,n=i.a.mark(x),a=e.baseName,c=e.controlCallback,l=e.reducerCallback,f=void 0===l?p:l,O=e.argsToParams,y=void 0===O?b:O,E=e.validateParams,j=void 0===E?h:E;s()(a,"baseName is required."),s()("function"==typeof c,"controlCallback is required and must be a function."),s()("function"==typeof f,"reducerCallback must be a function."),s()("function"==typeof y,"argsToParams must be a function."),s()("function"==typeof j,"validateParams must be a function.");try{j(y()),r=!1}catch(e){r=!0}var R=Object(d.b)(a),k=Object(d.a)(a),w="FETCH_".concat(k),_="START_".concat(w),A="FINISH_".concat(w),C="CATCH_".concat(w),N="RECEIVE_".concat(k),M="fetch".concat(R),D="receive".concat(R),T="isFetching".concat(R),I=o()({},T,{});function x(e,t){var r,o;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,{payload:{params:e},type:_};case 2:return n.next=4,m(a,t);case 4:return n.prev=4,n.next=7,{payload:{params:e},type:w};case 7:return r=n.sent,n.next=10,P[D](r,e);case 10:return n.next=12,{payload:{params:e},type:A};case 12:n.next=21;break;case 14:return n.prev=14,n.t0=n.catch(4),o=n.t0,n.next=19,S(o,a,t);case 19:return n.next=21,{payload:{params:e},type:C};case 21:return n.abrupt("return",{response:r,error:o});case 22:case"end":return n.stop()}}),n,null,[[4,14]])}var P=(t={},o()(t,M,(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=y.apply(void 0,t);return j(n),x(n,t)})),o()(t,D,(function(e,t){return s()(void 0!==e,"response is required."),r?(s()(Object(u.isPlainObject)(t),"params is required."),j(t)):t={},{payload:{response:e,params:t},type:N}})),t),L=o()({},w,(function(e){var t=e.payload;return c(t.params)})),q=o()({},T,(function(e){if(void 0===e[T])return!1;var t;try{for(var r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];t=y.apply(void 0,n),j(t)}catch(e){return!1}return!!e[T][Object(g.H)(t)]}));return{initialState:I,actions:P,controls:L,reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case _:var i=n.params;return v(v({},e),{},o()({},T,v(v({},e[T]),{},o()({},Object(g.H)(i),!0))));case N:var a=n.response,c=n.params;return f(e,a,c);case A:var s=n.params;return v(v({},e),{},o()({},T,v(v({},e[T]),{},o()({},Object(g.H)(s),!1))));case C:var u=n.params;return v(v({},e),{},o()({},T,v(v({},e[T]),{},o()({},Object(g.H)(u),!1))));default:return e}},resolvers:{},selectors:q}}},561:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return p}));var n=r(5),i=r.n(n),a=r(6),o=r.n(a),c=r(12),s=r.n(c),u=r(45),l=r.n(u),d=r(3),g=r(48);function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){o()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var p=function(t,r,n){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},c=a.client,u=void 0===c||c,f=a.server,p=void 0===f||f,b=a.storeName,h=void 0===b?void 0:b;s()(t,"type is required."),s()(r,"identifier is required."),s()(n,"datapoint is required.");var m=h||"".concat(t,"/").concat(r),S={serverNotifications:p?void 0:{},clientNotifications:u?void 0:{}},O=Object(g.a)({baseName:"getNotifications",controlCallback:function(){return l.a.get(t,r,n)},reducerCallback:function(e,t){return v(v({},e),{},{serverNotifications:t.reduce((function(e,t){return v(v({},e),{},o()({},t.id,t))}),{})})}}),y={addNotification:function(e){return s()(e,"notification is required."),{payload:{notification:e},type:"ADD_NOTIFICATION"}},removeNotification:function(e){return s()(e,"id is required."),{payload:{id:e},type:"REMOVE_NOTIFICATION"}}},E={},j=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:S,r=arguments.length>1?arguments[1]:void 0,n=r.type,i=r.payload;switch(n){case"ADD_NOTIFICATION":var a=i.notification;return v(v({},t),{},{clientNotifications:v(v({},t.clientNotifications||{}),{},o()({},a.id,a))});case"REMOVE_NOTIFICATION":var c=i.id;if(void 0===t.clientNotifications||void 0===t.clientNotifications[c])return void 0!==t.serverNotifications&&void 0!==t.serverNotifications[c]&&e.console.warn('Cannot remove server-side notification with ID "'.concat(c,'"; this may be changed in a future release.')),t;var s=v({},t.clientNotifications);return delete s[c],v(v({},t),{},{clientNotifications:s});default:return t}},R={getNotifications:i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,d.commonActions.getRegistry();case 2:if(t=e.sent,t.select(m).getNotifications()){e.next=7;break}return e.next=7,O.actions.fetchGetNotifications();case 7:case"end":return e.stop()}}),e)}))};p||delete R.getNotifications;var k={getNotifications:function(e){var t=e.serverNotifications,r=e.clientNotifications;return void 0===t&&void 0===r?t:Object.values(v(v({},t||{}),r||{}))}},w=Object(d.combineStores)(O,{initialState:S,actions:y,controls:E,reducer:j,resolvers:R,selectors:k});return v(v({},w),{},{STORE_NAME:m})}}).call(this,r(28))},562:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return ModuleSettingsWarning}));var n=r(3),i=r(19),a=r(7),o=r(217),c=r(245);function ModuleSettingsWarning(t){var r=t.slug,s=Object(n.useSelect)((function(e){var t;return null===(t=e(i.a))||void 0===t?void 0:t.getCheckRequirementsError(r)}));return s?a.c===s.code?e.createElement(c.a,{moduleSlug:r}):e.createElement(o.a,null,s.message):null}}).call(this,r(4))},57:function(e,t,r){"use strict";(function(e){var n,i;r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return o}));var a=new Set((null===(n=e)||void 0===n||null===(i=n._googlesitekitBaseData)||void 0===i?void 0:i.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,r(28))},59:function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));var n=r(39);function i(e){return function(){e[n.a]=e[n.a]||[],e[n.a].push(arguments)}}},62:function(e,t,r){"use strict";r.d(t,"a",(function(){return w})),r.d(t,"b",(function(){return _})),r.d(t,"c",(function(){return A})),r.d(t,"d",(function(){return N})),r.d(t,"e",(function(){return M})),r.d(t,"g",(function(){return T})),r.d(t,"f",(function(){return I}));var n,i=r(5),a=r.n(i),o=r(27),c=r.n(o),s=r(6),u=r.n(s),l=r(12),d=r.n(l),g=r(63),f=r.n(g),v=r(14),p=r(116);function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){u()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var m=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.reduce((function(e,t){return h(h({},e),t)}),{}),i=t.reduce((function(e,t){return[].concat(c()(e),c()(Object.keys(t)))}),[]),a=C(i);return d()(0===a.length,"collect() cannot accept collections with duplicate keys. Your call to collect() contains the following duplicated functions: ".concat(a.join(", "),". Check your data stores for duplicates.")),n},S=m,O=m,y=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n,i=[].concat(t);return"function"!=typeof i[0]&&(n=i.shift()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i.reduce((function(e,r){return r(e,t)}),e)}},E=m,j=m,R=m,k=function(e){return e},w=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=R.apply(void 0,c()(t.map((function(e){return e.initialState||{}}))));return{initialState:n,controls:O.apply(void 0,c()(t.map((function(e){return e.controls||{}})))),actions:S.apply(void 0,c()(t.map((function(e){return e.actions||{}})))),reducer:y.apply(void 0,[n].concat(c()(t.map((function(e){return e.reducer||k}))))),resolvers:E.apply(void 0,c()(t.map((function(e){return e.resolvers||{}})))),selectors:j.apply(void 0,c()(t.map((function(e){return e.selectors||{}}))))}},_={getRegistry:function(){return{payload:{},type:"GET_REGISTRY"}},await:a.a.mark((function e(t){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{payload:{value:t},type:"AWAIT"});case 1:case"end":return e.stop()}}),e)}))},A=(n={},u()(n,"GET_REGISTRY",Object(p.a)((function(e){return function(){return e}}))),u()(n,"AWAIT",(function(e){return e.payload.value})),n),C=function(e){for(var t=[],r={},n=0;n<e.length;n++){var i=e[n];r[i]=r[i]>=1?r[i]+1:1,r[i]>1&&t.push(i)}return t},N={actions:_,controls:A,reducer:k},M=function(e){return function(t){return D(e(t))}},D=f()((function(e){return Object(v.mapValues)(e,(function(e,t){return function(){var r=e.apply(void 0,arguments);return d()(void 0!==r,"".concat(t,"(...) is not resolved")),r}}))}));function T(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.negate,n=void 0!==r&&r,i=Object(p.b)((function(t){return function(r){var i=!n,a=!!n;try{for(var o=arguments.length,c=new Array(o>1?o-1:0),s=1;s<o;s++)c[s-1]=arguments[s];return e.apply(void 0,[t,r].concat(c)),i}catch(e){return a}}})),a=Object(p.b)((function(t){return function(r){for(var n=arguments.length,i=new Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];e.apply(void 0,[t,r].concat(i))}}));return{safeSelector:i,dangerousSelector:a}}function I(e,t){return d()("function"==typeof e,"a validator function is required."),d()("function"==typeof t,"an action creator function is required."),d()("Generator"!==e[Symbol.toStringTag]&&"GeneratorFunction"!==e[Symbol.toStringTag],"an action’s validator function must not be a generator."),function(){return e.apply(void 0,arguments),t.apply(void 0,arguments)}}},64:function(e,t,r){"use strict";r.d(t,"a",(function(){return b})),r.d(t,"b",(function(){return h}));var n=r(6),i=r.n(n),a=r(33),o=r.n(a),c=r(116),s=r(12),u=r.n(s),l=r(96),d=r.n(l),g=r(9);function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){i()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function p(e,t){if(t&&Array.isArray(t)){var r=t.map((function(e){return"object"===o()(e)?Object(g.H)(e):e}));return"".concat(e,"::").concat(d()(JSON.stringify(r)))}return e}var b={receiveError:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(e,"error is required."),u()(t,"baseName is required."),u()(r&&Array.isArray(r),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:r}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return u()(e,"baseName is required."),u()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function h(e){u()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(r,"selectorName is required."),t.getError(e,r,n)},getErrorForAction:function(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(r,"actionName is required."),t.getError(e,r,n)},getError:function(e,t,r){var n=e.errors;return u()(t,"baseName is required."),n[p(t,r)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var r=Object.keys(e.errors).find((function(r){return e.errors[r]===t}));return r?{baseName:r.substring(0,r.indexOf("::")),args:e.errorArgs[r]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(r,n){var i=t(e).getMetaDataForError(n);if(i){var a=i.baseName,o=i.args;if(!!t(e)[a])return{storeName:e,name:a,args:o}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:b,controls:{},reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"RECEIVE_ERROR":var a=n.baseName,o=n.args,c=n.error,s=p(a,o);return v(v({},e),{},{errors:v(v({},e.errors||{}),{},i()({},s,c)),errorArgs:v(v({},e.errorArgs||{}),{},i()({},s,o))});case"CLEAR_ERROR":var u=n.baseName,l=n.args,d=v({},e),g=p(u,l);return d.errors=v({},e.errors||{}),d.errorArgs=v({},e.errorArgs||{}),delete d.errors[g],delete d.errorArgs[g],d;case"CLEAR_ERRORS":var f=n.baseName,b=v({},e);if(f)for(var h in b.errors=v({},e.errors||{}),b.errorArgs=v({},e.errorArgs||{}),b.errors)(h===f||h.startsWith("".concat(f,"::")))&&(delete b.errors[h],delete b.errorArgs[h]);else b.errors={},b.errorArgs={};return b;default:return e}},resolvers:{},selectors:t}}},7:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return i})),r.d(t,"e",(function(){return a})),r.d(t,"d",(function(){return o})),r.d(t,"c",(function(){return c})),r.d(t,"H",(function(){return s})),r.d(t,"M",(function(){return u})),r.d(t,"O",(function(){return l})),r.d(t,"K",(function(){return d})),r.d(t,"L",(function(){return g})),r.d(t,"J",(function(){return f})),r.d(t,"I",(function(){return v})),r.d(t,"N",(function(){return p})),r.d(t,"f",(function(){return b})),r.d(t,"g",(function(){return h})),r.d(t,"h",(function(){return m})),r.d(t,"j",(function(){return S})),r.d(t,"l",(function(){return O})),r.d(t,"m",(function(){return y})),r.d(t,"n",(function(){return E})),r.d(t,"o",(function(){return j})),r.d(t,"q",(function(){return R})),r.d(t,"s",(function(){return k})),r.d(t,"r",(function(){return w})),r.d(t,"t",(function(){return _})),r.d(t,"w",(function(){return A})),r.d(t,"u",(function(){return C})),r.d(t,"v",(function(){return N})),r.d(t,"x",(function(){return M})),r.d(t,"y",(function(){return D})),r.d(t,"A",(function(){return T})),r.d(t,"B",(function(){return I})),r.d(t,"C",(function(){return x})),r.d(t,"D",(function(){return P})),r.d(t,"k",(function(){return L})),r.d(t,"F",(function(){return q})),r.d(t,"z",(function(){return U})),r.d(t,"G",(function(){return H})),r.d(t,"E",(function(){return G})),r.d(t,"i",(function(){return B})),r.d(t,"p",(function(){return F})),r.d(t,"Q",(function(){return V})),r.d(t,"P",(function(){return K}));var n="core/user",i="connected_url_mismatch",a="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",u="googlesitekit_setup",l="googlesitekit_view_dashboard",d="googlesitekit_manage_options",g="googlesitekit_read_shared_module_data",f="googlesitekit_manage_module_sharing_options",v="googlesitekit_delegate_module_sharing_management",p="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",h="kmAnalyticsEngagedTrafficSource",m="kmAnalyticsLeastEngagingPages",S="kmAnalyticsNewVisitors",O="kmAnalyticsPopularAuthors",y="kmAnalyticsPopularContent",E="kmAnalyticsPopularProducts",j="kmAnalyticsReturningVisitors",R="kmAnalyticsTopCities",k="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",_="kmAnalyticsTopCitiesDrivingPurchases",A="kmAnalyticsTopDeviceDrivingPurchases",C="kmAnalyticsTopConvertingTrafficSource",N="kmAnalyticsTopCountries",M="kmAnalyticsTopPagesDrivingLeads",D="kmAnalyticsTopRecentTrendingPages",T="kmAnalyticsTopTrafficSource",I="kmAnalyticsTopTrafficSourceDrivingAddToCart",x="kmAnalyticsTopTrafficSourceDrivingLeads",P="kmAnalyticsTopTrafficSourceDrivingPurchases",L="kmAnalyticsPagesPerVisit",q="kmAnalyticsVisitLength",U="kmAnalyticsTopReturningVisitorPages",H="kmSearchConsolePopularKeywords",G="kmAnalyticsVisitsPerVisitor",B="kmAnalyticsMostEngagingPages",F="kmAnalyticsTopCategories",V=[b,h,m,S,O,y,E,j,F,R,k,w,_,A,C,N,D,T,I,L,q,U,G,B,F],K=[].concat(V,[H])},70:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return n.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},75:function(e,t,r){"use strict";r.d(t,"a",(function(){return o})),r.d(t,"b",(function(){return c}));var n=r(33),i=r.n(n),a=r(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,r="object"===i()(e)?e.toString():e;return null==r||null===(t=r.replace)||void 0===t?void 0:t.call(r,/\/+$/,"")}},76:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return IconWrapper}));var n=r(1),i=r.n(n);function IconWrapper(t){var r=t.children,n=t.marginLeft,i=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:n,marginRight:i}},r)}IconWrapper.propTypes={children:i.a.node.isRequired,marginLeft:i.a.number,marginRight:i.a.number}}).call(this,r(4))},80:function(e,t,r){"use strict";r.d(t,"b",(function(){return i})),r.d(t,"a",(function(){return a})),r.d(t,"c",(function(){return o})),r.d(t,"d",(function(){return c}));var n=r(106);function i(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(n.a)(e))return e;if(e.length<=t)return e;var r=new URL(e),i=e.replace(r.origin,"");if(i.length<t)return i;var a=i.length-Math.floor(t)+1;return"…"+i.substr(a)}},82:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"a",(function(){return i})),r.d(t,"c",(function(){return a}));var n=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},i=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function a(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},83:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return j})),r.d(t,"d",(function(){return R})),r.d(t,"e",(function(){return w})),r.d(t,"c",(function(){return _})),r.d(t,"b",(function(){return A}));var n=r(15),i=r.n(n),a=r(33),o=r.n(a),c=r(6),s=r.n(c),u=r(25),l=r.n(u),d=r(14),g=r(63),f=r.n(g),v=r(2);function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var h=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=O(e,t),n=r.formatUnit,i=r.formatDecimal;try{return n()}catch(e){return i()}},m=function(e){var t=S(e),r=t.hours,n=t.minutes,i=t.seconds;return i=("0"+i).slice(-2),n=("0"+n).slice(-2),"00"===(r=("0"+r).slice(-2))?"".concat(n,":").concat(i):"".concat(r,":").concat(n,":").concat(i)},S=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},O=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=S(e),n=r.hours,i=r.minutes,a=r.seconds;return{hours:n,minutes:i,seconds:a,formatUnit:function(){var r=t.unitDisplay,o=b(b({unitDisplay:void 0===r?"short":r},l()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(a,b(b({},o),{},{unit:"second"})):Object(v.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(v._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?w(a,b(b({},o),{},{unit:"second"})):"",i?w(i,b(b({},o),{},{unit:"minute"})):"",n?w(n,b(b({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(v.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(v.__)("%ds","google-site-kit"),a);if(0===e)return t;var r=Object(v.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(v.__)("%dm","google-site-kit"),i),o=Object(v.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(v.__)("%dh","google-site-kit"),n);return Object(v.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(v._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",i?r:"",n?o:"").trim()}}},y=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},E=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(v.sprintf)(// translators: %s: an abbreviated number in millions.
Object(v.__)("%sM","google-site-kit"),w(y(e),e%10==0?{}:t)):1e4<=e?Object(v.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(v.__)("%sK","google-site-kit"),w(y(e))):1e3<=e?Object(v.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(v.__)("%sK","google-site-kit"),w(y(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function j(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=b({},e)),t}function R(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var r=j(t),n=r.style,i=void 0===n?"metric":n;return"metric"===i?E(e):"duration"===i?h(e,r):"durationISO"===i?m(e):w(e,r)}var k=f()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.locale,n=void 0===r?A():r,a=l()(t,["locale"]);try{return new Intl.NumberFormat(n,a).format(e)}catch(t){k("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(n),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],u={},d=0,g=Object.entries(a);d<g.length;d++){var f=i()(g[d],2),v=f[0],p=f[1];c[v]&&p===c[v]||(s.includes(v)||(u[v]=p))}try{return new Intl.NumberFormat(n,u).format(e)}catch(t){return new Intl.NumberFormat(n).format(e)}},_=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.locale,n=void 0===r?A():r,i=t.style,a=void 0===i?"long":i,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(n,{style:a,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var u=Object(v.__)(", ","google-site-kit");return e.join(u)},A=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,r=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(r){var n=r.match(/^(\w{2})?(_)?(\w{2})/);if(n&&n[0])return n[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,r(28))},838:function(e,t,r){"use strict";r.d(t,"b",(function(){return L})),r.d(t,"a",(function(){return q}));var n=r(12),i=r.n(n),a=r(3),o=r(561),c=r(200),s=r(64),u=r(6),l=r.n(u),d=r(5),g=r.n(d),f=r(16),v=r.n(f),p=r(157),b=r(13),h=r(7);function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function S(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach((function(t){l()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var O=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.storeName,n=void 0===r?void 0:r,o=t.requiresSetup,c=void 0===o||o;i()(n,"storeName is required.");var s={},u={},d=l()({},"WAIT_FOR_REAUTH_RESOLVERS",Object(a.createRegistryControl)((function(e){return v()(g.a.mark((function t(){var r,n,i,a,o,c;return g.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.resolveSelect,n=r(h.a),i=n.getAuthentication,a=n.getConnectURL,o=r(b.c),c=o.getSiteInfo,t.next=5,Promise.all([i(),c(),a()]);case 5:case"end":return t.stop()}}),t)})))}))),f=function(e){return e},m={getAdminReauthURL:g.a.mark((function e(){return g.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{type:"WAIT_FOR_REAUTH_RESOLVERS"};case 2:case"end":return e.stop()}}),e)}))},O={getAdminScreenURL:Object(a.createRegistrySelector)((function(e){return function(t,r){return e(b.c).getAdminURL("googlesitekit-dashboard",r)}})),getAdminReauthURL:Object(a.createRegistrySelector)((function(t){return function(r){var i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],a=t(h.a).needsReauthentication();if(void 0!==a){var o={};c||!0!==i||(o.notification="authentication_success",o.reAuth=void 0);var s=t(n).getAdminScreenURL(S({slug:e,reAuth:i},o));if(void 0!==s){if(!a)return s;var u=t(h.a).getConnectURL({redirectURL:s});return Object(p.a)(u,{status:i})}}}}))};return{STORE_NAME:n,initialState:s,actions:u,controls:d,reducer:f,resolvers:m,selectors:O}},y=r(62);function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach((function(t){l()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var R=s.a.clearError,k=s.a.receiveError;function w(e){var t,r=e||{},n=r.submitChanges,i=void 0===n?function(){return{}}:n,o=r.rollbackChanges,c=void 0===o?function(){return{}}:o,s=r.validateCanSubmitChanges,u=void 0===s?function(){}:s,d={submitChanges:g.a.mark((function e(){var t;return g.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,R("submitChanges",[]);case 2:return e.next=4,{type:"START_SUBMIT_CHANGES",payload:{}};case 4:return e.next=6,{type:"SUBMIT_CHANGES",payload:{}};case 6:if(!(null==(t=e.sent)?void 0:t.error)){e.next=10;break}return e.next=10,k(t.error,"submitChanges",[]);case 10:return e.next=12,{type:"FINISH_SUBMIT_CHANGES",payload:{}};case 12:return e.abrupt("return",t);case 13:case"end":return e.stop()}}),e)})),rollbackChanges:g.a.mark((function e(){var t;return g.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{type:"ROLLBACK_CHANGES",payload:{}};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))},f=(t={},l()(t,"SUBMIT_CHANGES",Object(a.createRegistryControl)((function(e){return function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return i.apply(void 0,[e].concat(r))}}))),l()(t,"ROLLBACK_CHANGES",Object(a.createRegistryControl)((function(e){return function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return c.apply(void 0,[e].concat(r))}}))),t),v=Object(y.g)(u);return{initialState:{isDoingSubmitChanges:!1},actions:d,controls:f,reducer:function(e,t){switch(t.type){case"START_SUBMIT_CHANGES":return j(j({},e),{},{isDoingSubmitChanges:!0});case"FINISH_SUBMIT_CHANGES":return j(j({},e),{},{isDoingSubmitChanges:!1});default:return e}},resolvers:{},selectors:{canSubmitChanges:v.safeSelector,__dangerousCanSubmitChanges:v.dangerousSelector,isDoingSubmitChanges:function(e){return!!e.isDoingSubmitChanges}}}}function _(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.storeName,n=t.settingSlugs,u=t.ownedSettingsSlugs,l=void 0===u?void 0:u,d=t.initialSettings,g=void 0===d?void 0:d,f=t.requiresSetup,v=void 0===f||f,p=t.submitChanges,b=t.rollbackChanges,h=t.validateHaveSettingsChanged,m=void 0===h?null:h,S=t.validateCanSubmitChanges,E=t.validateIsSetupBlocked,j=void 0===E?void 0:E;i()(e,"slug is required."),i()(r,"storeName is required.");var R=Object(o.a)("modules",e,"notifications",{storeName:r}),k=O(e,{storeName:r,requiresSetup:v}),_={};if(v&&j){var A=Object(y.g)(j,{negate:!0}),C=A.safeSelector,N=A.dangerousSelector;_.selectors={isSetupBlocked:C,__dangerousIsSetupBlocked:N}}var M={};if(void 0!==n){var D=Object(c.c)("modules",e,"settings",{ownedSettingsSlugs:l,storeName:r,settingSlugs:n,initialSettings:g,validateHaveSettingsChanged:m||Object(c.e)()}),T=w({submitChanges:p||Object(c.g)(e,r),rollbackChanges:b||Object(c.f)(r),validateCanSubmitChanges:S||Object(c.d)(r)});M=Object(a.combineStores)(R,D,T,k,Object(s.b)(r),_)}else M=Object(a.combineStores)(a.commonStore,R,k,_,Object(s.b)(r),w({submitChanges:p,validateCanSubmitChanges:S}));return M.STORE_NAME=r,M}var A=r(19);function C(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function N(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?C(Object(r),!0).forEach((function(t){l()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var M,D={actions:{setModuleSettingsPanelState:function(e,t){i()(e,"slug is required.");var r=["closed","edit","view"];return i()(r.includes(t),"value should be one of ".concat(r.join()," ")),{payload:{slug:e,value:t},type:"SET_MODULE_SETTINGS_PANEL_STATE"}}},initialState:{settingsPanel:{currentModule:null,isEditing:!1}},reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"SET_MODULE_SETTINGS_PANEL_STATE":var i=n.slug,a=n.value,o=N({},e.settingsPanel);return o.currentModule="closed"===a?null:i,o.isEditing="edit"===a,N(N({},e),{},{settingsPanel:o});default:return e}},selectors:{getModuleSettingsPanelState:function(e,t){i()(t,"slug is required.");var r=e.settingsPanel,n=r.currentModule,a=r.isEditing;return n===t?a?"edit":"view":"closed"},isModuleSettingsPanelOpen:function(e,t){return i()(t,"slug is required."),t===e.settingsPanel.currentModule},isModuleSettingsPanelClosed:function(e,t){return i()(t,"slug is required."),t!==e.settingsPanel.currentModule},isModuleSettingsPanelEdit:function(e,t){i()(t,"slug is required.");var r=e.settingsPanel,n=r.currentModule,a=r.isEditing;return t===n&&a},isModuleSettingsPanelLocked:function(e,t){i()(t,"slug is required.");var r=e.settingsPanel,n=r.currentModule,a=r.isEditing;return t!==n&&a}}},T={actions:{submitChanges:Object(y.f)((function(e){i()(e,"slug is required.")}),g.a.mark((function e(t){return g.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{type:"SUBMIT_MODULE_CHANGES",payload:{slug:t}};case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)}))),rollbackChanges:Object(y.f)((function(e){i()(e,"slug is required.")}),g.a.mark((function e(t){return g.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{type:"ROLLBACK_MODULE_CHANGES",payload:{slug:t}};case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))},controls:(M={},l()(M,"SUBMIT_MODULE_CHANGES",Object(a.createRegistryControl)((function(e){return function(t){var r=t.payload.slug,n=e.select(A.a).getModuleStoreName(r);if(!n)return{error:"The module '".concat(r,"' does not have a store.")};var i=e.dispatch(n).submitChanges;return i?i(r):{error:"The module '".concat(r,"' does not have a submitChanges() action.")}}}))),l()(M,"ROLLBACK_MODULE_CHANGES",Object(a.createRegistryControl)((function(e){return function(t){var r=t.payload.slug,n=e.select(A.a).getModuleStoreName(r);if(!n)return{error:"The module '".concat(r,"' does not have a store.")};var i=e.dispatch(n).rollbackChanges;return i?i(r):void 0}}))),M),selectors:{areSettingsEditDependenciesLoaded:Object(a.createRegistrySelector)((function(e){return function(t,r){i()(r,"slug is required.");var n=e(A.a).getModuleStoreName(r),a=e(n);if(a)return!a.areSettingsEditDependenciesLoaded||!!a.areSettingsEditDependenciesLoaded()}})),isDoingSubmitChanges:Object(a.createRegistrySelector)((function(e){return function(t,r){var n,a;i()(r,"slug is required.");var o=e(A.a).getModuleStoreName(r);return!!(null===(n=e(o))||void 0===n||null===(a=n.isDoingSubmitChanges)||void 0===a?void 0:a.call(n))}})),canSubmitChanges:Object(a.createRegistrySelector)((function(e){return function(t,r){var n,a;i()(r,"slug is required.");var o=e(A.a).getModuleStoreName(r);return!!(null===(n=e(o))||void 0===n||null===(a=n.canSubmitChanges)||void 0===a?void 0:a.call(n))}})),haveSettingsChanged:Object(a.createRegistrySelector)((function(e){return function(t,r){var n,a;i()(r,"slug is required.");var o=e(A.a).getModuleStoreName(r);return!!(null===(n=e(o))||void 0===n||null===(a=n.haveSettingsChanged)||void 0===a?void 0:a.call(n))}}))}},I=r(979),x=r(982),P=Object(a.combineStores)(a.commonStore,I.a,Object(s.b)(A.a),D,T,x.a),L=(P.initialState,P.actions,P.controls,P.reducer,P.resolvers,P.selectors,function(e){e.registerStore(A.a,P)});function q(e){var t=e.dispatch;return{createModuleStore:_,activateModule:function(e){return t(A.a).activateModule(e)},deactivateModule:function(e){return t(A.a).deactivateModule(e)},registerModule:function(e,r){return t(A.a).registerModule(e,r)}}}},84:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return i}));var n=r(149),i=r.n(n)()(e)}).call(this,r(28))},85:function(e,t,r){"use strict";(function(e){var n=r(1),i=r.n(n),a=r(11),o=r.n(a);function ChangeArrow(t){var r=t.direction,n=t.invertColor,i=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(r),{"googlesitekit-change-arrow--inverted-color":n}),width:i,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:i.a.string,invertColor:i.a.bool,width:i.a.number,height:i.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,r(4))},89:function(e,t,r){"use strict";r.d(t,"a",(function(){return a}));var n=r(12),i=r.n(n),a=function(e,t){var r=t.dateRangeLength;i()(Array.isArray(e),"report must be an array to partition."),i()(Number.isInteger(r)&&r>0,"dateRangeLength must be a positive integer.");var n=-1*r;return{currentRange:e.slice(n),compareRange:e.slice(2*n,n)}}},9:function(e,t,r){"use strict";r.d(t,"I",(function(){return i.b})),r.d(t,"J",(function(){return i.c})),r.d(t,"F",(function(){return a.a})),r.d(t,"K",(function(){return a.b})),r.d(t,"H",(function(){return l})),r.d(t,"m",(function(){return d.a})),r.d(t,"B",(function(){return d.d})),r.d(t,"C",(function(){return d.e})),r.d(t,"y",(function(){return d.c})),r.d(t,"r",(function(){return d.b})),r.d(t,"z",(function(){return p})),r.d(t,"j",(function(){return b})),r.d(t,"i",(function(){return h})),r.d(t,"d",(function(){return j})),r.d(t,"c",(function(){return R})),r.d(t,"e",(function(){return k})),r.d(t,"b",(function(){return w})),r.d(t,"a",(function(){return _})),r.d(t,"f",(function(){return A})),r.d(t,"n",(function(){return C})),r.d(t,"w",(function(){return N})),r.d(t,"p",(function(){return M})),r.d(t,"G",(function(){return D})),r.d(t,"s",(function(){return T})),r.d(t,"v",(function(){return I})),r.d(t,"k",(function(){return x})),r.d(t,"o",(function(){return P.b})),r.d(t,"h",(function(){return P.a})),r.d(t,"t",(function(){return L.b})),r.d(t,"q",(function(){return L.a})),r.d(t,"A",(function(){return L.c})),r.d(t,"x",(function(){return q})),r.d(t,"u",(function(){return U})),r.d(t,"E",(function(){return B})),r.d(t,"D",(function(){return F.a})),r.d(t,"g",(function(){return V})),r.d(t,"L",(function(){return K})),r.d(t,"l",(function(){return z}));var n=r(14),i=r(36),a=r(75),o=r(33),c=r.n(o),s=r(96),u=r.n(s),l=function(e){return u()(JSON.stringify(function e(t){var r={};return Object.keys(t).sort().forEach((function(n){var i=t[n];i&&"object"===c()(i)&&!Array.isArray(i)&&(i=e(i)),r[n]=i})),r}(e)))};r(97);var d=r(83);function g(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function f(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function v(e){return e.replace(/\n/gi,"<br>")}function p(e){for(var t=e,r=0,n=[g,f,v];r<n.length;r++){t=(0,n[r])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},h=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},m=r(15),S=r.n(m),O=r(12),y=r.n(O),E=r(2),j="Invalid dateString parameter, it must be a string.",R='Invalid date range, it must be a string with the format "last-x-days".',k=60,w=60*k,_=24*w,A=7*_;function C(){var e=function(e){return Object(E.sprintf)(
/* translators: %s: number of days */
Object(E._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function N(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(n.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var r=new Date(e);return Object(n.isDate)(r)&&!isNaN(r)}function M(e){y()(Object(n.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),r="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,r.length<2?"0".concat(r):r].join("-")}function D(e){y()(N(e),j);var t=e.split("-"),r=S()(t,3),n=r[0],i=r[1],a=r[2];return new Date(n,i-1,a)}function T(e,t){return M(x(e,t*_))}function I(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function x(e,t){y()(N(e)||Object(n.isDate)(e)&&!isNaN(e),j);var r=N(e)?Date.parse(e):e.getTime();return new Date(r-1e3*t)}var P=r(98),L=r(80);function q(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function U(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var H=r(27),G=r.n(H),B=function(e){return Array.isArray(e)?G()(e).sort():e},F=r(89);function V(e,t){var r=function(e){return"0"===e||0===e};if(r(e)&&r(t))return 0;if(r(e)||Number.isNaN(e))return null;var n=(t-e)/e;return Number.isNaN(n)||!Number.isFinite(n)?null:n}var K=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},z=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(n.unescape)(t)}},97:function(e,t,r){"use strict";(function(e){r(51),r(53)}).call(this,r(28))},979:function(e,t,r){"use strict";(function(e){var n,i=r(15),a=r.n(i),o=r(16),c=r.n(o),s=r(5),u=r.n(s),l=r(6),d=r.n(l),g=r(63),f=r.n(g),v=r(12),p=r.n(v),b=r(14),h=r(2),m=r(45),S=r.n(m),O=r(3),y=r(19),E=r(13),j=r(7),R=r(48),k=r(9),w=r(980),_=r(62),A=u.a.mark(F);function C(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return N(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return N(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==r.return||r.return()}finally{if(c)throw a}}}}function N(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function D(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach((function(t){d()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var T={slug:"",storeName:null,name:"",description:"",homepage:null,internal:!1,active:!1,connected:!1,dependencies:[],dependants:[],order:10,features:[],Icon:null,SettingsEditComponent:null,SettingsViewComponent:null,SettingsSetupIncompleteComponent:w.a,SetupComponent:null,onCompleteSetup:void 0,checkRequirements:function(){return!0},DashboardMainEffectComponent:null,DashboardEntityEffectComponent:null},I=f()((function(e,t){var r=Object(b.merge)({},e,t);return Object.keys(r).map((function(e){var t=D(D({},r[e]),{},{slug:e});return Object(b.defaults)(t,{name:e},T),t})).sort((function(e,t){var r;return e.order-t.order||(null===(r=e.name)||void 0===r?void 0:r.localeCompare(t.name))})).reduce((function(e,t){return D(D({},e),{},d()({},t.slug,t))}),{})})),x=f()((function(e,t){return Object.values(e).reduce((function(e,r){return t.includes(r.slug)?D(D({},e),{},d()({},r.slug,r)):e}),{})})),P=Object(R.a)({baseName:"getModules",controlCallback:function(){return S.a.get("core","modules","list",null,{useCache:!1})},reducerCallback:function(e,t){return D(D({},e),{},{isAwaitingModulesRefresh:!1,serverDefinitions:t.reduce((function(e,t){return D(D({},e),{},d()({},t.slug,t))}),{})})}}),L=Object(R.a)({baseName:"setModuleActivation",controlCallback:function(e){var t=e.slug,r=e.active;return S.a.set("core","modules","activation",{slug:t,active:r})},reducerCallback:function(e){return D(D({},e),{},{isAwaitingModulesRefresh:!0})},argsToParams:function(e,t){return{slug:e,active:t}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.slug,r=e.active;p()(t,"slug is required."),p()(void 0!==r,"active is required.")}}),q=Object(R.a)({baseName:"checkModuleAccess",controlCallback:function(e){var t=e.slug;return S.a.set("core","modules","check-access",{slug:t})},reducerCallback:function(e,t,r){var n=t.access,i=r.slug;return D(D({},e),{},{moduleAccess:D(D({},e.moduleAccess),{},d()({},i,n))})},argsToParams:function(e){return{slug:e}},validateParams:function(e){var t=e.slug;p()(t,"slug is required.")}}),U=Object(R.a)({baseName:"recoverModules",controlCallback:function(e){var t=e.slugs;return S.a.set("core","modules","recover-modules",{slugs:t})},reducerCallback:function(e,t){return D(D({},e),{},{recoveredModules:t})},argsToParams:function(e){return{slugs:e}},validateParams:function(e){var t=e.slugs;p()(t,"slugs is required.")}}),H={clientDefinitions:{},serverDefinitions:void 0,isAwaitingModulesRefresh:!1,checkRequirementsResults:{},moduleAccess:{},recoverableModules:void 0,sharedOwnershipModules:void 0,recoveredModules:void 0},G={activateModule:u.a.mark((function e(t){var r,n,i,a;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,G.setModuleActivation(t,!0);case 2:if(r=e.sent,n=r.response,i=r.error,!0!==(null==n?void 0:n.success)){e.next=10;break}return e.next=8,{payload:{slug:t},type:"SELECT_MODULE_REAUTH_URL"};case 8:return a=e.sent,e.abrupt("return",{response:D(D({},n),{},{moduleReauthURL:a}),error:i});case 10:return e.abrupt("return",{response:n,error:i});case 11:case"end":return e.stop()}}),e)})),deactivateModule:u.a.mark((function e(t){var r,n,i;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,G.setModuleActivation(t,!1);case 2:return r=e.sent,n=r.response,i=r.error,e.abrupt("return",{response:n,error:i});case 6:case"end":return e.stop()}}),e)})),setModuleActivation:Object(_.f)((function(e,t){p()(e,"slug is required."),p()(void 0!==t,"active is required.")}),u.a.mark((function e(t,r){var n,i,a;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,L.actions.fetchSetModuleActivation(t,r);case 2:if(n=e.sent,i=n.response,a=n.error,!0!==(null==i?void 0:i.success)){e.next=8;break}return e.next=8,{payload:{},type:"REFETCH_AUTHENTICATION"};case 8:return e.abrupt("return",{response:i,error:a});case 9:case"end":return e.stop()}}),e)}))),registerModule:Object(_.f)((function(e){p()(e,"module slug is required")}),u.a.mark((function e(t){var r,n,i,a,o,c,s,l,d,g,f,v,p,b,h,m,S,E,j,R,k=arguments;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=k.length>1&&void 0!==k[1]?k[1]:{},n=r.storeName,i=r.name,a=r.description,o=r.features,c=r.Icon,s=r.order,l=r.homepage,d=r.SettingsEditComponent,g=r.SettingsViewComponent,f=r.SettingsSetupIncompleteComponent,v=r.SetupComponent,p=r.overrideSetupSuccessNotification,b=void 0!==p&&p,h=r.onCompleteSetup,m=r.checkRequirements,S=r.DashboardMainEffectComponent,E=r.DashboardEntityEffectComponent,j={storeName:n,name:i,description:a,features:o,Icon:c,order:s,homepage:l,SettingsEditComponent:d,SettingsViewComponent:g,SettingsSetupIncompleteComponent:f,SetupComponent:v,overrideSetupSuccessNotification:b,onCompleteSetup:h,checkRequirements:m,DashboardMainEffectComponent:S,DashboardEntityEffectComponent:E},e.next=4,{payload:{settings:j,slug:t},type:"REGISTER_MODULE"};case 4:return e.next=6,O.commonActions.getRegistry();case 6:(R=e.sent).dispatch(y.a).invalidateResolution("canActivateModule",[t]),R.dispatch(y.a).invalidateResolution("getCheckRequirementsError",[t]);case 9:case"end":return e.stop()}}),e)}))),receiveCheckRequirementsError:function(e,t){return p()(e,"slug is required"),p()(Object(b.isPlainObject)(t),"error is required and must be an object"),{payload:{slug:e,error:t},type:"RECEIVE_CHECK_REQUIREMENTS_ERROR"}},receiveCheckRequirementsSuccess:function(e){return p()(e,"slug is required"),{payload:{slug:e},type:"RECEIVE_CHECK_REQUIREMENTS_SUCCESS"}},receiveRecoverableModules:function(e){return p()(e,"recoverableModules is required."),{payload:{recoverableModules:e},type:"RECEIVE_RECOVERABLE_MODULES"}},recoverModules:Object(_.f)((function(e){p()(Array.isArray(e),"slugs must be an array")}),u.a.mark((function e(t){var r,n,i,a,o,c,s,l,d,g,f;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,O.commonActions.getRegistry();case 2:return r=e.sent,n=r.dispatch,i=r.select,e.next=7,U.actions.fetchRecoverModules(t);case 7:a=e.sent,o=a.response,c=o.success,s=Object.keys(c).filter((function(e){return!!c[e]})),l=C(s),e.prev=12,l.s();case 14:if((d=l.n()).done){e.next=21;break}return g=d.value,f=i(y.a).getModuleStoreName(g),e.next=19,O.commonActions.await(n(f).fetchGetSettings());case 19:e.next=14;break;case 21:e.next=26;break;case 23:e.prev=23,e.t0=e.catch(12),l.e(e.t0);case 26:return e.prev=26,l.f(),e.finish(26);case 29:if(!s.length){e.next=35;break}return e.next=32,P.actions.fetchGetModules();case 32:return n(y.a).invalidateResolution("getRecoverableModules",[]),e.next=35,O.commonActions.await(n(j.a).refreshCapabilities());case 35:return e.abrupt("return",{response:o});case 36:case"end":return e.stop()}}),e,null,[[12,23,26,29]])}))),receiveSharedOwnershipModules:function(e){return p()(e,"sharedOwnershipModules is required."),{payload:{sharedOwnershipModules:e},type:"RECEIVE_SHARED_OWNERSHIP_MODULES"}},clearRecoveredModules:function(){return{payload:{},type:"CLEAR_RECOVERED_MODULES"}}},B=(n={},d()(n,"REFETCH_AUTHENTICATION",Object(O.createRegistryControl)((function(e){var t=e.dispatch;return function(){return t(j.a).fetchGetAuthentication()}}))),d()(n,"SELECT_MODULE_REAUTH_URL",Object(O.createRegistryControl)((function(e){var t=e.select,r=e.resolveSelect;return function(){var e=c()(u.a.mark((function e(n){var i,a,o,c;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=n.payload,o=a.slug,e.next=4,r(y.a).getModule(o);case 4:if(c=t(y.a).getModuleStoreName(o)){e.next=7;break}return e.abrupt("return");case 7:if(!(null===(i=t(c))||void 0===i?void 0:i.getAdminReauthURL)){e.next=11;break}return e.next=10,r(c).getAdminReauthURL();case 10:return e.abrupt("return",e.sent);case 11:return e.abrupt("return",t(E.c).getAdminURL("googlesitekit-dashboard"));case 12:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()}))),n);function F(){var e,t;return u.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,O.commonActions.getRegistry();case 2:return e=r.sent,t=e.resolveSelect,r.next=6,O.commonActions.await(t(y.a).getModules());case 6:case"end":return r.stop()}}),A)}var V={getModules:u.a.mark((function e(){var t;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,O.commonActions.getRegistry();case 2:if(t=e.sent,t.select(y.a).getModules()){e.next=7;break}return e.next=7,P.actions.fetchGetModules();case 7:case"end":return e.stop()}}),e)})),canActivateModule:u.a.mark((function e(t){var r,n,i,a,o,c,s;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,O.commonActions.getRegistry();case 2:return r=e.sent,n=r.select,i=r.resolveSelect,e.next=6,O.commonActions.await(i(y.a).getModule(t));case 6:if(a=e.sent){e.next=9;break}return e.abrupt("return");case 9:if(o=[],a.dependencies.forEach((function(e){var t=n(y.a).getModule(e);(null==t?void 0:t.active)||o.push(t.name)})),!o.length){e.next=18;break}
/* translators: Error message text. 1: A flattened list of module names. 2: A module name. */return c=Object(h.__)("You need to set up %1$s to gain access to %2$s.","google-site-kit"),s=Object(h.sprintf)(c,Object(k.y)(o),a.name),e.next=16,G.receiveCheckRequirementsError(t,{code:y.b,message:s,data:{inactiveModules:o}});case 16:e.next=29;break;case 18:return e.prev=18,e.next=21,O.commonActions.await(a.checkRequirements(r));case 21:return e.next=23,G.receiveCheckRequirementsSuccess(t);case 23:e.next=29;break;case 25:return e.prev=25,e.t0=e.catch(18),e.next=29,G.receiveCheckRequirementsError(t,e.t0);case 29:case"end":return e.stop()}}),e,null,[[18,25]])})),hasModuleAccess:u.a.mark((function e(t){var r;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,O.commonActions.getRegistry();case 2:if(r=e.sent,void 0!==r.select(y.a).hasModuleAccess(t)){e.next=7;break}return e.next=7,q.actions.fetchCheckModuleAccess(t);case 7:case"end":return e.stop()}}),e)})),getRecoverableModules:u.a.mark((function e(){var t,r,n;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,O.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,O.commonActions.await(t.resolveSelect(y.a).getModules());case 5:return r=e.sent,n=Object.entries(r||{}).reduce((function(e,t){var r=a()(t,2),n=r[0],i=r[1];return i.recoverable&&!i.internal&&e.push(n),e}),[]),e.next=9,G.receiveRecoverableModules(n);case 9:case"end":return e.stop()}}),e)})),getSharedOwnershipModules:u.a.mark((function t(){var r;return u.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,O.commonActions.getRegistry();case 2:if(!t.sent.select(y.a).getSharedOwnershipModules()){t.next=5;break}return t.abrupt("return");case 5:if(e._googlesitekitDashboardSharingData){t.next=8;break}return e.console.error("Could not load core/modules dashboard sharing."),t.abrupt("return");case 8:return r=e._googlesitekitDashboardSharingData.sharedOwnershipModules,t.next=11,G.receiveSharedOwnershipModules(r);case 11:case"end":return t.stop()}}),t)})),getModule:F,isModuleActive:F,isModuleConnected:F},K={getModules:function(e){var t=e.clientDefinitions,r=e.serverDefinitions;if(void 0!==r)return I(r,t)},getModule:Object(O.createRegistrySelector)((function(e){return function(t,r){var n=e(y.a).getModules();if(void 0!==n)return void 0===n[r]?null:n[r]}})),getModuleIcon:Object(O.createRegistrySelector)((function(e){return function(t,r){var n=e(y.a).getModule(r);if(void 0!==n)return null===n||null===n.Icon?null:n.Icon}})),getModuleDependencyNames:Object(O.createRegistrySelector)((function(e){return function(t,r){var n=e(y.a).getModule(r);if(void 0!==n){if(null===n)return[];var i=e(y.a).getModules();return n.dependencies.map((function(e){var t;return(null===(t=i[e])||void 0===t?void 0:t.name)||e}))}}})),getModuleDependantNames:Object(O.createRegistrySelector)((function(e){return function(t,r){var n=e(y.a).getModule(r);if(void 0!==n){if(null===n)return[];var i=e(y.a).getModules();return n.dependants.map((function(e){var t;return(null===(t=i[e])||void 0===t?void 0:t.name)||e}))}}})),getModuleStoreName:Object(O.createRegistrySelector)((function(e){return function(t,r){var n=e(y.a).getModule(r);if(void 0!==n)return null===n?null:n.storeName}})),isModuleAvailable:Object(O.createRegistrySelector)((function(e){return function(t,r){var n=e(y.a).getModule(r);if(void 0!==n)return null!==n}})),isModuleActive:Object(O.createRegistrySelector)((function(e){return function(t,r){var n=e(y.a).getModule(r);if(void 0!==n)return null===n?null:n.active}})),isModuleConnected:Object(O.createRegistrySelector)((function(e){return function(t,r){var n=e(y.a).getModule(r);if(void 0!==n)return null===n?null:n.active&&n.connected}})),isDoingSetModuleActivation:Object(O.createRegistrySelector)((function(e){return function(t,r){if(e(y.a).getModule(r))return!!e(y.a).isFetchingSetModuleActivation(r,!0)||(!!e(y.a).isFetchingSetModuleActivation(r,!1)||t.isAwaitingModulesRefresh)}})),canActivateModule:function(e,t){p()(t,"slug is required");var r=e.checkRequirementsResults[t];if(void 0!==r)return!0===r||!0===(null==r?void 0:r.canActivate)},getCheckRequirementsError:Object(O.createRegistrySelector)((function(e){return function(t,r){p()(r,"slug is required.");var n=t.checkRequirementsResults;return void 0===e(y.a).canActivateModule(r)||!0===n[r]?null:n[r]}})),getModuleFeatures:Object(O.createRegistrySelector)((function(e){return function(t,r){var n,i=e(y.a).getModules();if(void 0!==i)return Array.isArray(null===(n=i[r])||void 0===n?void 0:n.features)?i[r].features:[]}})),hasModuleAccess:function(e,t){return e.moduleAccess[t]},hasModuleOwnership:Object(O.createRegistrySelector)((function(e){return function(t,r){var n=e(y.a).getModuleStoreName(r);if(void 0!==n){if(null===e(n))return null;var i=e(n).getOwnerID(),a=e(j.a).getID();if(void 0!==i&&void 0!==a)return i===a}}})),hasModuleOwnershipOrAccess:Object(O.createRegistrySelector)((function(e){return function(t,r){var n=e(y.a).hasModuleOwnership(r);return!0===n||(void 0!==n?null!==n&&e(y.a).hasModuleAccess(r):void 0)}})),getRecoverableModules:Object(O.createRegistrySelector)((function(e){return function(t){var r=e(y.a).getModules();if(void 0!==t.recoverableModules&&void 0!==r)return x(r,t.recoverableModules)}})),hasRecoverableModules:function(e){if(void 0!==e.recoverableModules)return Object.keys(e.recoverableModules).length>0},getSharedOwnershipModules:Object(O.createRegistrySelector)((function(e){return function(t){var r=e(y.a).getModules();if(void 0!==t.sharedOwnershipModules&&void 0!==r)return Object.values(r).reduce((function(e,r){return t.sharedOwnershipModules.includes(r.slug)?D(D({},e),{},d()({},r.slug,r)):e}),{})}})),getShareableModules:Object(O.createRegistrySelector)((function(e){return function(){var t=e(y.a).getModules();if(void 0!==t)return Object.keys(t).reduce((function(e,r){return t[r].shareable?D(d()({},r,t[r]),e):e}),{})}})),getRecoveredModules:function(e){return e.recoveredModules},getDetailsLinkURL:Object(O.createRegistrySelector)((function(e){return function(t,r){var n=e(y.a).getModule(r);if(void 0!==n){if(null===n)return null;var i=e(y.a).getModuleStoreName(r),a=(e(i)||{}).getDetailsLinkURL;return"function"==typeof a?a():n.homepage?e(j.a).getAccountChooserURL(n.homepage):null}}}))},z=Object(O.combineStores)(P,L,q,U,{initialState:H,actions:G,controls:B,reducer:function(t,r){var n=r.type,i=r.payload;switch(n){case"REGISTER_MODULE":var a=i.slug,o=i.settings;return t.clientDefinitions[a]?(e.console.warn('Could not register module with slug "'.concat(a,'". Module "').concat(a,'" is already registered.')),t):D(D({},t),{},{clientDefinitions:D(D({},t.clientDefinitions),{},d()({},a,o))});case"RECEIVE_CHECK_REQUIREMENTS_ERROR":var c=i.slug,s=i.error;return D(D({},t),{},{checkRequirementsResults:D(D({},t.checkRequirementsResults),{},d()({},c,s))});case"RECEIVE_CHECK_REQUIREMENTS_SUCCESS":var u=i.slug;return D(D({},t),{},{checkRequirementsResults:D(D({},t.checkRequirementsResults),{},d()({},u,!0))});case"RECEIVE_RECOVERABLE_MODULES":var l=i.recoverableModules;return D(D({},t),{},{recoverableModules:l});case"RECEIVE_SHARED_OWNERSHIP_MODULES":var g=i.sharedOwnershipModules;return D(D({},t),{},{sharedOwnershipModules:g});case"CLEAR_RECOVERED_MODULES":return D(D({},t),{},{recoveredModules:void 0});default:return t}},resolvers:V,selectors:K});z.initialState,z.actions,z.controls,z.reducer,z.resolvers,z.selectors;t.a=z}).call(this,r(28))},98:function(e,t,r){"use strict";(function(e){r.d(t,"b",(function(){return a})),r.d(t,"a",(function(){return o}));var n=r(239),i=r(85),a=function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=r.invertColor,o=void 0!==a&&a;return Object(n.a)(e.createElement(i.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,r(4))},980:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return DefaultSettingsSetupIncomplete}));var n=r(1),i=r.n(n),a=r(38),o=r(2),c=r(3),s=r(20),u=r(562),l=r(102),d=r(19);function DefaultSettingsSetupIncomplete(t){var r=t.slug,n=Object(c.useSelect)((function(e){return e(d.a).getModuleStoreName(r)})),i=Object(c.useSelect)((function(e){var t,r;return null===(t=e(n))||void 0===t||null===(r=t.getAdminReauthURL)||void 0===r?void 0:r.call(t)})),g=Object(c.useSelect)((function(e){var t;return null===(t=e(d.a))||void 0===t?void 0:t.getCheckRequirementsError(r)}));return e.createElement(l.a,{size:12},e.createElement("div",{className:"googlesitekit-settings-module__fields-group googlesitekit-settings-module__fields-group--no-border"},e.createElement(u.a,{slug:r})),e.createElement("div",{className:"googlesitekit-settings-module__fields-group-title"},Object(a.a)(Object(o.__)("Setup incomplete: <a>continue module setup</a>","google-site-kit"),{a:e.createElement(s.a,{className:"googlesitekit-settings-module__edit-button",href:i,disabled:!!g})})))}DefaultSettingsSetupIncomplete.propTypes={slug:i.a.string.isRequired}}).call(this,r(4))},982:function(e,t,r){"use strict";(function(e){var n=r(5),i=r.n(n),a=r(15),o=r.n(a),c=r(983),s=r.n(c),u=r(6),l=r.n(u),d=r(12),g=r.n(d),f=r(14),v=r(45),p=r.n(v),b=r(3),h=r(48),m=r(19),S=r(62);function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach((function(t){l()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var E=["all_admins","owner"],j={sharingSettings:void 0,savedSharingSettings:void 0,shareableRoles:void 0,isDoingSubmitSharingChanges:void 0,defaultSharedOwnershipModuleSettings:void 0},R=Object(h.a)({baseName:"saveSharingSettings",controlCallback:function(e){var t=e.savedSharingSettings;return p.a.set("core","modules","sharing-settings",t)},reducerCallback:function(e,t){var r=t.settings;return y(y({},e),{},{savedSharingSettings:r,sharingSettings:r})},argsToParams:function(e){return{savedSharingSettings:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.savedSharingSettings;g()(t,"savedSharingSettings is required.")}}),k=Object(h.a)({baseName:"resetSharingSettings",controlCallback:function(e){return s()(e),p.a.set("core","modules","sharing-settings",{},{method:"DELETE"})},reducerCallback:function(e){return y(y({},e),{},{savedSharingSettings:{},sharingSettings:{}})}}),w={setSharingManagement:function(e,t){return g()(e,"moduleSlug is required."),g()(E.includes(t),"management must be one of: ".concat(E.join(", "),".")),{payload:{moduleSlug:e,management:t},type:"SET_SHARING_MANAGEMENT"}},setSharedRoles:function(e,t){return g()(e,"moduleSlug is required."),g()(Array.isArray(t)&&t.every((function(e){return"string"==typeof e})),"roles must be an array of strings."),{payload:{moduleSlug:e,roles:t},type:"SET_SHARED_ROLES"}},saveSharingSettings:i.a.mark((function e(){var t,r,n,a,c,s,u,l,d,g,f;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,{type:"START_SUBMIT_SHARING_CHANGES",payload:{}};case 5:return r=t.select(m.a).getSharingSettings(),e.next=8,R.actions.fetchSaveSharingSettings(r);case 8:if(n=e.sent,a=n.response,!(c=n.error)&&Object.keys(a.newOwnerIDs).length)for(s=0,u=Object.entries(a.newOwnerIDs);s<u.length;s++)l=o()(u[s],2),d=l[0],g=l[1],f=t.select(m.a).getModuleStoreName(d),t.dispatch(f).setOwnerID(g);return e.next=14,{type:"FINISH_SUBMIT_SHARING_CHANGES",payload:{}};case 14:return e.abrupt("return",{response:a,error:c});case 15:case"end":return e.stop()}}),e)})),resetSharingSettings:i.a.mark((function e(){var t,r,n;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{type:"START_SUBMIT_SHARING_CHANGES",payload:{}};case 2:return e.next=4,k.actions.fetchResetSharingSettings();case 4:return t=e.sent,r=t.response,n=t.error,e.next=9,{type:"FINISH_SUBMIT_SHARING_CHANGES",payload:{}};case 9:return e.abrupt("return",{response:r,error:n});case 10:case"end":return e.stop()}}),e)})),receiveGetSharingSettings:function(e){return g()(e,"sharingSettings is required."),{payload:{sharingSettings:e},type:"RECEIVE_GET_SHARING_SETTINGS"}},receiveShareableRoles:function(e){return g()(e,"shareableRoles is required."),{payload:{shareableRoles:e},type:"RECEIVE_SHAREABLE_ROLES"}},rollbackSharingSettings:function(){return{payload:{},type:"ROLLBACK_SHARING_SETTINGS"}},receiveDefaultSharedOwnershipModuleSettings:function(e){return g()(e,"defaultSharedOwnershipModuleSettings is required."),{payload:{defaultSharedOwnershipModuleSettings:e},type:"RECEIVE_DEFAULT_SHARED_OWNERSHIP_MODULE_SETTINGS"}}},_={getSharingSettings:i.a.mark((function t(){var r;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b.commonActions.getRegistry();case 2:if(!t.sent.select(m.a).getSharingSettings()){t.next=5;break}return t.abrupt("return");case 5:if(e._googlesitekitDashboardSharingData){t.next=8;break}return e.console.error("Could not load core/modules dashboard sharing settings."),t.abrupt("return");case 8:return r=e._googlesitekitDashboardSharingData.settings,t.next=11,M.receiveGetSharingSettings(r);case 11:case"end":return t.stop()}}),t)})),getShareableRoles:i.a.mark((function t(){var r;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b.commonActions.getRegistry();case 2:if(!t.sent.select(m.a).getShareableRoles()){t.next=5;break}return t.abrupt("return");case 5:if(e._googlesitekitDashboardSharingData){t.next=8;break}return e.console.error("Could not load core/modules dashboard sharing roles."),t.abrupt("return");case 8:return r=e._googlesitekitDashboardSharingData.roles,t.next=11,M.receiveShareableRoles(r);case 11:case"end":return t.stop()}}),t)})),getDefaultSharedOwnershipModuleSettings:i.a.mark((function t(){var r;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b.commonActions.getRegistry();case 2:if(!t.sent.select(m.a).getDefaultSharedOwnershipModuleSettings()){t.next=5;break}return t.abrupt("return");case 5:if(e._googlesitekitDashboardSharingData){t.next=8;break}return e.console.error("Could not load core/modules dashboard sharing."),t.abrupt("return");case 8:return r=e._googlesitekitDashboardSharingData.defaultSharedOwnershipModuleSettings,t.next=11,w.receiveDefaultSharedOwnershipModuleSettings(r);case 11:case"end":return t.stop()}}),t)}))};var A=Object(S.g)((function(e){var t=Object(S.e)(e)(m.a),r=t.isDoingSubmitSharingChanges,n=t.haveSharingSettingsChanged;g()(!r(),"cannot submit sharing changes while submitting changes"),g()(n(),"cannot submit changes if sharing settings have not changed")})),C={canSubmitSharingChanges:A.safeSelector,__dangerousCanSubmitSharingChanges:A.dangerousSelector,getSharingSettings:function(e){return e.sharingSettings},getShareableRoles:function(e){return e.shareableRoles},getSharingManagement:Object(b.createRegistrySelector)((function(e){return function(t,r){var n;g()(r,"moduleSlug is required.");var i=e(m.a).getSharingSettings();if(void 0!==i)return(null===(n=i[r])||void 0===n?void 0:n.management)||null}})),getSharedRoles:Object(b.createRegistrySelector)((function(e){return function(t,r){var n;g()(r,"moduleSlug is required.");var i=e(m.a).getSharingSettings();if(void 0!==i)return(null===(n=i[r])||void 0===n?void 0:n.sharedRoles)||null}})),haveSharingSettingsChanged:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=e.sharingSettings,n=e.savedSharingSettings;return t?!Object(f.isEqual)(Object(f.pick)(r,t),Object(f.pick)(n,t)):!Object(f.isEqual)(r,n)},haveSharingSettingsExpanded:function(e,t){var r=["management","sharedRoles"];g()(r.includes(t),"key must be one of: ".concat(r.join(", "),"."));var n=e.sharingSettings,i=e.savedSharingSettings;if(void 0!==n&&void 0!==i)return"management"===t?Object.keys(n).some((function(e){var t,r,a;return(null===(t=i[e])||void 0===t?void 0:t.management)!==(null===(r=n[e])||void 0===r?void 0:r.management)&&"all_admins"===(null===(a=n[e])||void 0===a?void 0:a.management)})):"sharedRoles"===t&&Object.keys(n).some((function(e){var t,r;return(null===(t=n[e])||void 0===t||null===(r=t.sharedRoles)||void 0===r?void 0:r.filter((function(t){var r,n;return!(null===(r=i[e])||void 0===r||null===(n=r.sharedRoles)||void 0===n?void 0:n.includes(t))})).length)>0}))},haveModuleSharingSettingsChanged:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;g()(t,"moduleSlug is required.");var n=e.sharingSettings,i=e.savedSharingSettings;if(void 0!==n&&void 0!==i)return r?!Object(f.isEqual)(Object(f.pick)(n[t],r),Object(f.pick)(i[t],r)):!Object(f.isEqual)(n[t],i[t])},isDoingSubmitSharingChanges:function(e){return!!e.isDoingSubmitSharingChanges},getDefaultSharedOwnershipModuleSettings:function(e){return e.defaultSharedOwnershipModuleSettings},haveSharingSettingsUpdated:function(e){var t=e.savedSharingSettings,r=e.sharedOwnershipModules;return!Object(f.isEmpty)(t)&&!Object(f.isEmpty)(r)&&Object.keys(t).some((function(e){var n=t[e],i=n.sharedRoles,a=n.management,o=r.includes(e)?"all_admins":"owner";return i.length>0||a!==o}))}},N=Object(b.combineStores)(R,k,{initialState:j,actions:w,selectors:C,reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"SET_SHARING_MANAGEMENT":var i=n.moduleSlug,a=n.management;return y(y({},e),{},{sharingSettings:y(y({},e.sharingSettings),{},l()({},i,y(y({},e.sharingSettings[i]),{},{management:a})))});case"SET_SHARED_ROLES":var o=n.moduleSlug,c=n.roles;return y(y({},e),{},{sharingSettings:y(y({},e.sharingSettings),{},l()({},o,y(y({},e.sharingSettings[o]),{},{sharedRoles:c})))});case"RECEIVE_GET_SHARING_SETTINGS":var s=n.sharingSettings;return y(y({},e),{},{sharingSettings:s,savedSharingSettings:s});case"RECEIVE_SHAREABLE_ROLES":var u=n.shareableRoles;return y(y({},e),{},{shareableRoles:u});case"START_SUBMIT_SHARING_CHANGES":return y(y({},e),{},{isDoingSubmitSharingChanges:!0});case"FINISH_SUBMIT_SHARING_CHANGES":return y(y({},e),{},{isDoingSubmitSharingChanges:!1});case"ROLLBACK_SHARING_SETTINGS":return y(y({},e),{},{sharingSettings:e.savedSharingSettings});case"RECEIVE_DEFAULT_SHARED_OWNERSHIP_MODULE_SETTINGS":var d=n.defaultSharedOwnershipModuleSettings;return y(y({},e),{},{defaultSharedOwnershipModuleSettings:d});default:return e}},resolvers:_}),M=(N.initialState,N.actions);N.selectors,N.reducer;t.a=N}).call(this,r(28))},99:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return d}));var n=r(6),i=r.n(n),a=r(14),o=r(100),c=r(101);function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach((function(t){i()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var l={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=u(u({},l),t);i.referenceSiteURL&&(i.referenceSiteURL=i.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(i,r),d=Object(c.a)(i,r,s,n),g={},f=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=JSON.stringify(t);g[n]||(g[n]=Object(a.once)(d)),g[n].apply(g,t)};return{enableTracking:function(){i.trackingEnabled=!0},disableTracking:function(){i.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!i.trackingEnabled},trackEvent:d,trackEventOnce:f}}}).call(this,r(28))}},[[1257,1,0]]]);