(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[18],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(59),i=n(39),a=n(57);function o(t,n){var o,c=Object(r.a)(n),s=t.activeModules,l=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,f=void 0===d?[]:d,g=t.isAuthenticated,p=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(i.b,"]"))),!o){o=!0;var r=(null==f?void 0:f.length)?f.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:l,plugin_version:p||"",enabled_features:Array.from(a.a).join(","),active_modules:s.join(","),authenticated:g?"1":"0",user_properties:{user_roles:r,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(i.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a)}}}}}).call(this,n(28))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function f(e,t,n,r){var a=Object(l.a)(t);return function(){var t=s()(i.a.mark((function t(o,c,s,l){var u;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:s,value:l},t.abrupt("return",new Promise((function(e){var t,n,i=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(i),e()};a("event",c,d(d({},u),{},{event_callback:s})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,i){return t.apply(this,arguments)}}()}},102:function(e,t,n){"use strict";var r=n(123);n.d(t,"a",(function(){return r.a}));var i=n(124);n.d(t,"c",(function(){return i.a}));var a=n(125);n.d(t,"b",(function(){return a.a}))},1030:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M45.718 8.472c-2.54 0-4.194.827-5.67 3.603l-6.734 12.7V13.491c0-3.367-1.595-5.02-4.549-5.02-2.953 0-4.193 1.004-5.67 3.839l-6.38 12.463V13.61c0-3.603-1.476-5.138-5.079-5.138H4.312C1.536 8.472 0 9.772 0 12.134s1.477 3.78 4.194 3.78h3.012V30.15c0 4.017 2.717 6.38 6.616 6.38 3.898 0 5.67-1.536 7.62-5.14l4.252-7.973v6.733c0 3.958 2.599 6.38 6.556 6.38 3.958 0 5.434-1.36 7.679-5.14l9.805-16.538c2.127-3.603.65-6.38-4.076-6.38h.06zM64.147 8.472c-8.033 0-14.117 5.965-14.117 14.057 0 8.093 6.143 14 14.117 14 7.974 0 14.058-5.966 14.117-14 0-8.092-6.143-14.057-14.117-14.057zm0 19.433c-3.012 0-5.08-2.245-5.08-5.376 0-3.13 2.068-5.434 5.08-5.434 3.013 0 5.08 2.304 5.08 5.434 0 3.131-2.008 5.376-5.08 5.376zM94.329 8.472c-7.974 0-14.117 5.965-14.117 14.057 0 8.093 6.143 14 14.117 14 7.974 0 14.117-5.966 14.117-14 0-8.033-6.143-14.057-14.117-14.057zm0 19.433c-3.072 0-5.02-2.245-5.02-5.376 0-3.13 2.007-5.434 5.02-5.434 3.012 0 5.08 2.304 5.08 5.434 0 3.131-2.009 5.376-5.08 5.376z",fill:"#873EFF"});t.a=function SvgWooLogo(e){return r.createElement("svg",i({viewBox:"0 0 109 46",fill:"none"},e),a)}},1031:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M488.3 39.95c3.755 43.505-7.512 62.738-36.012 114.738s11.58 113.449-32.5 172c-52.439 69.655-235.682 75.339-314.739 46.339-79.057-29.001-99.443-88.945-93.761-143.839 7.712-74.5 73.457-98.668 93.761-135.18 30.149-54.21-14.084-110.233 44.276-153.705 65.645-48.9 111.456 18.941 174.394 14.523 36.312-2.55 71.974-10.77 104.207 1.853 32.583 12.76 57.432 48.666 60.374 83.271z",fill:"#B8E6CA"}),o=r.createElement("rect",{x:204,y:107,width:119,height:8,rx:4,fill:"#F3F5F7"}),c=r.createElement("g",{filter:"url(#ads-setup_svg__filter0_d_850_1860)"},r.createElement("rect",{x:186,y:41,width:155,height:252,rx:20,fill:"#fff"}),r.createElement("rect",{x:204,y:58,width:119,height:21,rx:4,fill:"#F3F5F7"}),r.createElement("rect",{x:204,y:115,width:119,height:130,rx:4,fill:"#F3F5F7"}),r.createElement("rect",{x:204,y:253,width:119,height:8,rx:4,fill:"#F3F5F7"}),r.createElement("rect",{x:226,y:89,width:75,height:12,rx:6,fill:"#F3F5F7"}),r.createElement("rect",{x:204,y:90,width:15,height:2,rx:1,fill:"#DEE3E6"}),r.createElement("rect",{x:204,y:94,width:15,height:2,rx:1,fill:"#DEE3E6"}),r.createElement("rect",{x:204,y:98,width:15,height:2,rx:1,fill:"#DEE3E6"}),r.createElement("circle",{cx:378.245,cy:41.245,r:18,fill:"#6380B8"}),r.createElement("circle",{cx:376.822,cy:39.382,r:4.842,stroke:"#fff",strokeWidth:2.065}),r.createElement("path",{d:"M380.174 43.479l4.071 4.807",stroke:"#fff",strokeWidth:2.065,strokeLinecap:"round",strokeLinejoin:"round"}),r.createElement("circle",{cx:400.245,cy:91.245,r:14,fill:"#6380B8"}),r.createElement("circle",{cx:399.139,cy:89.796,r:3.766,stroke:"#fff",strokeWidth:2.065}),r.createElement("path",{d:"M401.746 92.983l3.166 3.738",stroke:"#fff",strokeWidth:2.065,strokeLinecap:"round",strokeLinejoin:"round"}),r.createElement("circle",{cx:96.245,cy:215.245,r:25,fill:"#6380B8"}),r.createElement("circle",{cx:94.269,cy:212.657,r:6.725,stroke:"#fff",strokeWidth:2.065}),r.createElement("path",{d:"M98.925 218.347l5.654 6.676",stroke:"#fff",strokeWidth:2.065,strokeLinecap:"round",strokeLinejoin:"round"}),r.createElement("circle",{cx:425.245,cy:.245,r:22,fill:"#6380B8"}),r.createElement("circle",{cx:423.506,cy:-2.033,r:5.918,stroke:"#fff",strokeWidth:2.065}),r.createElement("path",{d:"M427.604 2.975l4.975 5.875",stroke:"#fff",strokeWidth:2.065,strokeLinecap:"round",strokeLinejoin:"round"})),s=r.createElement("g",{filter:"url(#ads-setup_svg__filter1_d_850_1860)"},r.createElement("path",{d:"M159 139a8 8 0 018-8h83a8 8 0 018 8v37h-99v-37z",fill:"#9BB8F0"}),r.createElement("path",{d:"M159 182.5h99V226a8 8 0 01-8 8h-83a8 8 0 01-8-8v-43.5z",fill:"#fff"}),r.createElement("path",{fill:"#F3F5F7",d:"M159 176h99v19h-99z"}),r.createElement("rect",{x:168,y:182,width:62,height:7,rx:3.5,fill:"#DEE3E6"}),r.createElement("rect",{x:168,y:206,width:54,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("rect",{x:168,y:215,width:36,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("circle",{cx:241,cy:213,r:8,fill:"#EBEEF0"}),r.createElement("path",{d:"M239.857 209.571l3.429 3.429-3.429 3.429",stroke:"#fff",strokeWidth:2.286,strokeLinecap:"round",strokeLinejoin:"round"})),l=r.createElement("g",{filter:"url(#ads-setup_svg__filter2_d_850_1860)"},r.createElement("path",{d:"M269 139a8 8 0 018-8h83a8 8 0 018 8v37h-99v-37z",fill:"#9BB8F0"}),r.createElement("path",{d:"M269 182.5h99V226a8 8 0 01-8 8h-83a8 8 0 01-8-8v-43.5z",fill:"#fff"}),r.createElement("path",{fill:"#F3F5F7",d:"M269 176h99v19h-99z"}),r.createElement("rect",{x:278,y:182,width:62,height:7,rx:3.5,fill:"#DEE3E6"}),r.createElement("rect",{x:278,y:206,width:54,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("rect",{x:278,y:215,width:36,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("circle",{cx:351,cy:213,r:8,fill:"#EBEEF0"}),r.createElement("path",{d:"M349.857 209.571l3.429 3.429-3.429 3.429",stroke:"#fff",strokeWidth:2.286,strokeLinecap:"round",strokeLinejoin:"round"})),u=r.createElement("defs",null,r.createElement("filter",{id:"ads-setup_svg__filter0_d_850_1860",x:55.245,y:-33.755,width:408,height:346.755,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_850_1860"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_850_1860",result:"shape"})),r.createElement("filter",{id:"ads-setup_svg__filter1_d_850_1860",x:143,y:123,width:131,height:135,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:8}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_850_1860"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_850_1860",result:"shape"})),r.createElement("filter",{id:"ads-setup_svg__filter2_d_850_1860",x:253,y:123,width:131,height:135,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:8}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_850_1860"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_850_1860",result:"shape"})));t.a=function SvgAdsSetup(e){return r.createElement("svg",i({viewBox:"0 0 499 272",fill:"none"},e),a,o,c,s,l,u)}},1032:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#ads-setup-tablet_svg__clip0_1458_3805)"},r.createElement("path",{d:"M433.584 69.664c2.236 25.805-8.393 44.336-21.445 68.058-13.052 23.722 16.896 67.293-9.353 102.023-31.227 41.316-190.345 44.688-237.422 27.486s-79.247-50.17-75.863-82.731c4.592-44.19 19.727-49 35.863-83.5S128.501 40 158 20c29.499-20 63.494-8.136 84.5-5 28.261 4.219 37 3 88-7.5C363 .809 381.368 5.729 403 17c18 9.379 28.967 34 30.584 52.664z",fill:"#B8E6CA"}),r.createElement("rect",{x:234.898,y:35.166,width:81.26,height:5.463,rx:2.731,fill:"#F3F5F7"}),r.createElement("g",{filter:"url(#ads-setup-tablet_svg__filter0_d_1458_3805)"},r.createElement("rect",{x:230.711,y:5.275,width:89.632,height:145.725,rx:11.566,fill:"#fff"}),r.createElement("rect",{x:241.119,y:15.106,width:68.814,height:12.144,rx:2.313,fill:"#F3F5F7"}),r.createElement("rect",{x:241.119,y:48.067,width:68.814,height:75.175,rx:2.313,fill:"#F3F5F7"}),r.createElement("rect",{x:241.119,y:127.869,width:68.814,height:4.626,rx:2.313,fill:"#F3F5F7"}),r.createElement("rect",{x:253.842,y:33.032,width:43.37,height:6.939,rx:3.47,fill:"#F3F5F7"}),r.createElement("rect",{x:241.119,y:33.611,width:8.674,height:1.157,rx:.578,fill:"#DEE3E6"}),r.createElement("rect",{x:241.119,y:35.924,width:8.674,height:1.157,rx:.578,fill:"#DEE3E6"}),r.createElement("rect",{x:241.119,y:38.237,width:8.674,height:1.157,rx:.578,fill:"#DEE3E6"}),r.createElement("circle",{cx:416.882,cy:30.265,r:12.291,fill:"#6380B8"}),r.createElement("circle",{cx:415.91,cy:28.993,r:3.307,stroke:"#fff",strokeWidth:1.41}),r.createElement("path",{d:"M418.199 31.79l2.78 3.283",stroke:"#fff",strokeWidth:1.41,strokeLinecap:"round",strokeLinejoin:"round"}),r.createElement("circle",{cx:395.906,cy:70.408,r:9.56,fill:"#6380B8"}),r.createElement("circle",{cx:395.15,cy:69.418,r:2.572,stroke:"#fff",strokeWidth:1.41}),r.createElement("path",{d:"M396.93 71.594l2.162 2.553",stroke:"#fff",strokeWidth:1.41,strokeLinecap:"round",strokeLinejoin:"round"}),r.createElement("circle",{cx:131.318,cy:107.082,r:17.071,fill:"#6380B8"}),r.createElement("circle",{cx:129.968,cy:105.315,r:4.592,stroke:"#fff",strokeWidth:1.41}),r.createElement("path",{d:"M133.147 109.201l3.861 4.558",stroke:"#fff",strokeWidth:1.41,strokeLinecap:"round",strokeLinejoin:"round"})),r.createElement("g",{filter:"url(#ads-setup-tablet_svg__filter1_d_1458_3805)"},r.createElement("path",{d:"M188.841 45.636A6.636 6.636 0 01195.477 39h68.853a6.636 6.636 0 016.636 6.636V76.33h-82.125V45.636z",fill:"#9BB8F0"}),r.createElement("path",{d:"M188.841 81.722h82.125v36.085a6.636 6.636 0 01-6.636 6.637h-68.853a6.636 6.636 0 01-6.636-6.637V81.722z",fill:"#fff"}),r.createElement("path",{fill:"#F3F5F7",d:"M188.841 76.33h82.126v15.761h-82.126z"}),r.createElement("rect",{x:196.307,y:81.307,width:51.432,height:5.807,rx:2.903,fill:"#DEE3E6"}),r.createElement("rect",{x:196.307,y:101.216,width:44.796,height:4.148,rx:2.074,fill:"#EBEEF0"}),r.createElement("rect",{x:196.307,y:108.682,width:29.864,height:4.148,rx:2.074,fill:"#EBEEF0"}),r.createElement("circle",{cx:256.864,cy:107.023,r:6.636,fill:"#EBEEF0"}),r.createElement("path",{d:"M255.916 104.179l2.844 2.844-2.844 2.844",stroke:"#fff",strokeWidth:1.896,strokeLinecap:"round",strokeLinejoin:"round"})),r.createElement("g",{filter:"url(#ads-setup-tablet_svg__filter2_d_1458_3805)"},r.createElement("path",{d:"M280.091 45.636A6.636 6.636 0 01286.727 39h68.853a6.636 6.636 0 016.636 6.636V76.33h-82.125V45.636z",fill:"#9BB8F0"}),r.createElement("path",{d:"M280.091 81.722h82.125v36.085a6.636 6.636 0 01-6.636 6.637h-68.853a6.636 6.636 0 01-6.636-6.637V81.722z",fill:"#fff"}),r.createElement("path",{fill:"#F3F5F7",d:"M280.091 76.33h82.126v15.761h-82.126z"}),r.createElement("rect",{x:287.557,y:81.307,width:51.432,height:5.807,rx:2.903,fill:"#DEE3E6"}),r.createElement("rect",{x:287.557,y:101.216,width:44.796,height:4.148,rx:2.074,fill:"#EBEEF0"}),r.createElement("rect",{x:287.557,y:108.682,width:29.864,height:4.148,rx:2.074,fill:"#EBEEF0"}),r.createElement("circle",{cx:348.114,cy:107.023,r:6.636,fill:"#EBEEF0"}),r.createElement("path",{d:"M347.166 104.179l2.844 2.844-2.844 2.844",stroke:"#fff",strokeWidth:1.896,strokeLinecap:"round",strokeLinejoin:"round"}))),o=r.createElement("defs",null,r.createElement("filter",{id:"ads-setup-tablet_svg__filter0_d_1458_3805",x:103.32,y:-60.949,width:336.779,height:225.606,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:2.731}),r.createElement("feGaussianBlur",{stdDeviation:5.463}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1458_3805"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1458_3805",result:"shape"})),r.createElement("filter",{id:"ads-setup-tablet_svg__filter1_d_1458_3805",x:175.568,y:32.364,width:108.672,height:111.989,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:6.636}),r.createElement("feGaussianBlur",{stdDeviation:6.636}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1458_3805"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1458_3805",result:"shape"})),r.createElement("filter",{id:"ads-setup-tablet_svg__filter2_d_1458_3805",x:266.818,y:32.364,width:108.672,height:111.989,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:6.636}),r.createElement("feGaussianBlur",{stdDeviation:6.636}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1458_3805"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1458_3805",result:"shape"})),r.createElement("clipPath",{id:"ads-setup-tablet_svg__clip0_1458_3805"},r.createElement("path",{fill:"#fff",d:"M0 0h553v143H0z"})));t.a=function SvgAdsSetupTablet(e){return r.createElement("svg",i({viewBox:"0 0 553 143",fill:"none"},e),a,o)}},1033:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#ads-setup-mobile_svg__clip0_1458_2897)"},r.createElement("path",{d:"M313.084 67.664c2.235 25.805-8.393 44.336-21.445 68.058-13.052 23.722 16.896 67.293-9.353 102.023-31.227 41.316-175.845 43.188-205.922 29.486C46.287 253.529 20.617 219.561 24 187c4.592-44.19 16.363-47.5 32.499-82 16.136-34.5 0-54.5 25-79.5s50.494-10.136 71.5-7c28.261 4.219 34.5-8.368 57-13 32.5-6.691 50.868-1.771 72.5 9.5 18 9.379 28.967 34 30.584 52.664z",fill:"#B8E6CA"}),r.createElement("rect",{x:131.898,y:35.166,width:81.26,height:5.463,rx:2.731,fill:"#F3F5F7"}),r.createElement("g",{filter:"url(#ads-setup-mobile_svg__filter0_d_1458_2897)"},r.createElement("rect",{x:127.711,y:5.275,width:89.632,height:145.725,rx:11.566,fill:"#fff"}),r.createElement("rect",{x:138.119,y:15.106,width:68.814,height:12.144,rx:2.313,fill:"#F3F5F7"}),r.createElement("rect",{x:138.119,y:48.067,width:68.814,height:75.175,rx:2.313,fill:"#F3F5F7"}),r.createElement("rect",{x:138.119,y:127.869,width:68.814,height:4.626,rx:2.313,fill:"#F3F5F7"}),r.createElement("rect",{x:150.842,y:33.032,width:43.37,height:6.939,rx:3.47,fill:"#F3F5F7"}),r.createElement("rect",{x:138.119,y:33.611,width:8.674,height:1.157,rx:.578,fill:"#DEE3E6"}),r.createElement("rect",{x:138.119,y:35.924,width:8.674,height:1.157,rx:.578,fill:"#DEE3E6"}),r.createElement("rect",{x:138.119,y:38.237,width:8.674,height:1.157,rx:.578,fill:"#DEE3E6"}),r.createElement("circle",{cx:293.882,cy:30.265,r:12.291,fill:"#6380B8"}),r.createElement("circle",{cx:292.91,cy:28.993,r:3.307,stroke:"#fff",strokeWidth:1.41}),r.createElement("path",{d:"M295.199 31.79l2.78 3.283",stroke:"#fff",strokeWidth:1.41,strokeLinecap:"round",strokeLinejoin:"round"}),r.createElement("circle",{cx:272.906,cy:70.408,r:9.56,fill:"#6380B8"}),r.createElement("circle",{cx:272.15,cy:69.418,r:2.572,stroke:"#fff",strokeWidth:1.41}),r.createElement("path",{d:"M273.93 71.594l2.162 2.553",stroke:"#fff",strokeWidth:1.41,strokeLinecap:"round",strokeLinejoin:"round"}),r.createElement("circle",{cx:58.318,cy:107.082,r:17.071,fill:"#6380B8"}),r.createElement("circle",{cx:56.968,cy:105.315,r:4.592,stroke:"#fff",strokeWidth:1.41}),r.createElement("path",{d:"M60.148 109.201l3.86 4.558",stroke:"#fff",strokeWidth:1.41,strokeLinecap:"round",strokeLinejoin:"round"})),r.createElement("g",{filter:"url(#ads-setup-mobile_svg__filter1_d_1458_2897)"},r.createElement("path",{d:"M101.17 52.018a5.463 5.463 0 015.463-5.463h56.677a5.463 5.463 0 015.463 5.463v25.265H101.17V52.018z",fill:"#9BB8F0"}),r.createElement("path",{d:"M101.17 81.722h67.603v29.704a5.463 5.463 0 01-5.463 5.463h-56.677a5.463 5.463 0 01-5.463-5.463V81.722z",fill:"#fff"}),r.createElement("path",{fill:"#F3F5F7",d:"M101.17 77.283h67.603v12.974H101.17z"}),r.createElement("rect",{x:107.315,y:81.38,width:42.337,height:4.78,rx:2.39,fill:"#DEE3E6"}),r.createElement("rect",{x:107.315,y:97.769,width:36.874,height:3.414,rx:1.707,fill:"#EBEEF0"}),r.createElement("rect",{x:107.315,y:103.915,width:24.583,height:3.414,rx:1.707,fill:"#EBEEF0"}),r.createElement("circle",{cx:157.164,cy:102.549,r:5.463,fill:"#EBEEF0"}),r.createElement("path",{d:"M156.384 100.208l2.341 2.341-2.341 2.341",stroke:"#fff",strokeWidth:1.561,strokeLinecap:"round",strokeLinejoin:"round"})),r.createElement("g",{filter:"url(#ads-setup-mobile_svg__filter2_d_1458_2897)"},r.createElement("path",{d:"M176.284 52.018a5.463 5.463 0 015.463-5.463h56.677a5.463 5.463 0 015.463 5.463v25.265h-67.603V52.018z",fill:"#9BB8F0"}),r.createElement("path",{d:"M176.284 81.722h67.603v29.704a5.463 5.463 0 01-5.463 5.463h-56.677a5.463 5.463 0 01-5.463-5.463V81.722z",fill:"#fff"}),r.createElement("path",{fill:"#F3F5F7",d:"M176.284 77.283h67.603v12.974h-67.603z"}),r.createElement("rect",{x:182.43,y:81.38,width:42.337,height:4.78,rx:2.39,fill:"#DEE3E6"}),r.createElement("rect",{x:182.43,y:97.769,width:36.874,height:3.414,rx:1.707,fill:"#EBEEF0"}),r.createElement("rect",{x:182.43,y:103.915,width:24.583,height:3.414,rx:1.707,fill:"#EBEEF0"}),r.createElement("circle",{cx:232.278,cy:102.549,r:5.463,fill:"#EBEEF0"}),r.createElement("path",{d:"M231.498 100.208l2.341 2.341-2.341 2.341",stroke:"#fff",strokeWidth:1.561,strokeLinecap:"round",strokeLinejoin:"round"}))),o=r.createElement("defs",null,r.createElement("filter",{id:"ads-setup-mobile_svg__filter0_d_1458_2897",x:30.32,y:-60.949,width:286.779,height:225.606,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:2.731}),r.createElement("feGaussianBlur",{stdDeviation:5.463}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1458_2897"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1458_2897",result:"shape"})),r.createElement("filter",{id:"ads-setup-mobile_svg__filter1_d_1458_2897",x:90.244,y:41.092,width:89.454,height:92.186,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:5.463}),r.createElement("feGaussianBlur",{stdDeviation:5.463}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1458_2897"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1458_2897",result:"shape"})),r.createElement("filter",{id:"ads-setup-mobile_svg__filter2_d_1458_2897",x:165.358,y:41.092,width:89.454,height:92.186,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:5.463}),r.createElement("feGaussianBlur",{stdDeviation:5.463}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1458_2897"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1458_2897",result:"shape"})),r.createElement("clipPath",{id:"ads-setup-mobile_svg__clip0_1458_2897"},r.createElement("path",{fill:"#fff",d:"M0 0h343v143H0z"})));t.a=function SvgAdsSetupMobile(e){return r.createElement("svg",i({viewBox:"0 0 343 143",fill:"none"},e),a,o)}},1034:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(12),s=n.n(c),l=n(3),u=n(94),d=n(653);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e){return Object(l.createRegistrySelector)((function(t){return function(){return(t(u.e).getModuleData()||[])[e]}}))}function m(e,t){return s()(e,"propName is required."),s()(t,"plugin is required."),s()(u.c.includes(t),"Invalid plugin."),Object(l.createRegistrySelector)((function(n){return function(){return(((0,n(u.e).getPluginsData)()||[])[t]||[])[e]}}))}var v={moduleData:{supportedConversionEvents:void 0,plugins:void 0}},b={receiveModuleData:function(e){return s()(e,"moduleData is required."),{payload:e,type:"RECEIVE_MODULE_DATA"}}},h={getModuleData:i.a.mark((function t(){var n,r;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=null===(n=e._googlesitekitModulesData)||void 0===n?void 0:n.ads){t.next=3;break}return t.abrupt("return");case 3:return t.next=5,b.receiveModuleData(r);case 5:case"end":return t.stop()}}),t)}))},E={getModuleData:function(e){return e.moduleData},getSupportedConversionEvents:p("supportedConversionEvents"),getPluginsData:p("plugins"),isWooCommerceInstalled:m("installed",u.f.WOOCOMMERCE),isWooCommerceActivated:m("active",u.f.WOOCOMMERCE),isGoogleForWooCommerceInstalled:m("installed",u.f.GOOGLE_FOR_WOOCOMMERCE),isGoogleForWooCommerceActivated:m("active",u.f.GOOGLE_FOR_WOOCOMMERCE),hasGoogleForWooCommerceAdsAccount:m("adsConnected",u.f.GOOGLE_FOR_WOOCOMMERCE),getGoogleForWooCommerceConversionID:m("conversionID",u.f.GOOGLE_FOR_WOOCOMMERCE)};t.a={initialState:v,actions:b,controls:d.a,reducer:function(e,t){var n=t.payload;switch(t.type){case"RECEIVE_MODULE_DATA":var r={supportedConversionEvents:n.supportedConversionEvents,plugins:n.plugins};return g(g({},e),{},{moduleData:r});default:return e}},resolvers:h,selectors:E}}).call(this,n(28))},104:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return r.createElement("svg",i({viewBox:"0 0 14 14",fill:"none"},e),a)}},105:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l);function VisuallyHidden(t){var n=t.className,r=t.children,a=o()(t,["className","children"]);return r?e.createElement("span",i()({},a,{className:u()("screen-reader-text",n)}),r):null}VisuallyHidden.propTypes={className:s.a.string,children:s.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(4))},107:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var r=n(21),i=n.n(r),a=n(152),o=n.n(a),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(2),f=n(10),g=n(154),p=n(104);function TourTooltip(t){var n=t.backProps,r=t.closeProps,c=t.index,l=t.primaryProps,u=t.size,m=t.step,v=t.tooltipProps,b=u>1?Object(g.a)(u):[],h=function(e){return s()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",i()({className:s()("googlesitekit-tour-tooltip",m.className)},v),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},m.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},m.content)),e.createElement(a.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},b.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:h(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(f.Button,i()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),m.cta,l.title&&e.createElement(f.Button,i()({className:"googlesitekit-tooltip-button",text:!0},l),l.title))),e.createElement(f.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(p.a,{width:"14",height:"14"}),onClick:r.onClick,"aria-label":Object(d.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(4))},109:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(208),s=n(38),l=n(2),u=n(10),d=n(58);function ModalDialog(t){var n=t.className,r=void 0===n?"":n,i=t.dialogActive,a=void 0!==i&&i,f=t.handleDialog,g=void 0===f?null:f,p=t.onOpen,m=void 0===p?null:p,v=t.onClose,b=void 0===v?null:v,h=t.title,E=void 0===h?null:h,y=t.provides,O=t.handleConfirm,k=t.subtitle,_=t.confirmButton,S=void 0===_?null:_,j=t.dependentModules,w=t.danger,C=void 0!==w&&w,x=t.inProgress,A=void 0!==x&&x,T=t.small,N=void 0!==T&&T,D=t.medium,L=void 0!==D&&D,R=t.buttonLink,P=void 0===R?null:R,M=Object(c.a)(ModalDialog),I="googlesitekit-dialog-description-".concat(M),F=!(!y||!y.length);return e.createElement(u.Dialog,{open:a,onOpen:m,onClose:b,"aria-describedby":F?I:void 0,tabIndex:"-1",className:o()(r,{"googlesitekit-dialog-sm":N,"googlesitekit-dialog-md":L})},e.createElement(u.DialogTitle,null,C&&e.createElement(d.a,{width:28,height:28}),E),k?e.createElement("p",{className:"mdc-dialog__lead"},k):[],e.createElement(u.DialogContent,null,F&&e.createElement("section",{id:I,className:"mdc-dialog__provides"},e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},y.map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t},e.createElement("span",{className:"mdc-list-item__text"},t))})))),j&&e.createElement("p",{className:"mdc-dialog__dependencies"},Object(s.a)(Object(l.sprintf)(
/* translators: %s is replaced with the dependent modules. */
Object(l.__)("<strong>Note:</strong> %s","google-site-kit"),j),{strong:e.createElement("strong",null)}))),e.createElement(u.DialogFooter,null,e.createElement(u.Button,{className:"mdc-dialog__cancel-button",tertiary:!0,onClick:g,disabled:A},Object(l.__)("Cancel","google-site-kit")),P?e.createElement(u.Button,{href:P,onClick:O,target:"_blank",danger:C},S):e.createElement(u.SpinnerButton,{onClick:O,danger:C,disabled:A,isSaving:A},S||Object(l.__)("Disconnect","google-site-kit"))))}ModalDialog.displayName="Dialog",ModalDialog.propTypes={className:i.a.string,dialogActive:i.a.bool,handleDialog:i.a.func,handleConfirm:i.a.func.isRequired,onOpen:i.a.func,onClose:i.a.func,title:i.a.string,confirmButton:i.a.string,danger:i.a.bool,small:i.a.bool,medium:i.a.bool,buttonLink:i.a.string},t.a=ModalDialog}).call(this,n(4))},111:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(1),i=n.n(r),a=n(0),o=n(9),c=n(54);function Description(t){var n=t.className,r=void 0===n?"googlesitekit-publisher-win__desc":n,i=t.text,s=t.learnMoreLink,l=t.errorText,u=t.children;return e.createElement(a.Fragment,null,e.createElement("div",{className:r},e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.F)(i,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",s)),l&&e.createElement(c.a,{message:l}),u)}Description.propTypes={className:i.a.string,text:i.a.string,learnMoreLink:i.a.node,errorText:i.a.string,children:i.a.node}}).call(this,n(4))},113:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return v}));var r=n(6),i=n.n(r),a=n(21),o=n.n(a),c=n(15),s=n.n(c),l=n(25),u=n.n(l),d=n(240),f=n(1),g=n.n(f),p=n(0);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(t){function WithIntersectionObserverComponent(n){var r=n.onInView,i=u()(n,["onInView"]),a=Object(p.useRef)(),c=Object(d.a)(a,{root:null,threshold:.45}),l=Object(p.useState)(!1),f=s()(l,2),g=f[0],m=f[1],v=!!(null==c?void 0:c.isIntersecting)&&!!(null==c?void 0:c.intersectionRatio);return Object(p.useEffect)((function(){c&&v&&!g&&(r(),m(!0))}),[g,v,c,r]),e.createElement(t,o()({ref:a},i))}return WithIntersectionObserverComponent.displayName="WithIntersectionObserverComponent",(t.displayName||t.name)&&(WithIntersectionObserverComponent.displayName+="(".concat(t.displayName||t.name,")")),WithIntersectionObserverComponent.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({onInView:g.a.func.isRequired},t.propTypes),WithIntersectionObserverComponent}}).call(this,n(4))},115:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(0),s=n(137),l=n(58),u=n(131),d=n(17),f=Object(c.forwardRef)((function(t,n){var r=t.className,i=t.title,a=t.description,c=t.dismissCTA,f=t.additionalCTA,g=t.reverseCTAs,p=void 0!==g&&g,m=t.type,v=void 0===m?"success":m,b=t.icon;return e.createElement(d.e,{ref:n},e.createElement(d.k,null,e.createElement(d.a,{alignMiddle:!0,size:12,className:o()("googlesitekit-subtle-notification",r,{"googlesitekit-subtle-notification--success":"success"===v,"googlesitekit-subtle-notification--warning":"warning"===v,"googlesitekit-subtle-notification--new-feature":"new-feature"===v})},e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},b,"success"===v&&!b&&e.createElement(s.a,{width:24,height:24}),"warning"===v&&!b&&e.createElement(l.a,{width:24,height:24}),"new-feature"===v&&!b&&e.createElement(u.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,i),e.createElement("p",{className:"googlesitekit-subtle-notification__secondary_description"},a)),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},!p&&c,p&&f,!p&&f,p&&c))))}));f.propTypes={className:i.a.string,title:i.a.node,description:i.a.node,dismissCTA:i.a.node,additionalCTA:i.a.node,reverseCTAs:i.a.bool,type:i.a.oneOf(["success","warning","new-feature"]),icon:i.a.object},t.a=f}).call(this,n(4))},120:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var r=n(1),i=n.n(r),a=n(0),o=n(2),c=n(3),s=n(10),l=n(35),u=n(54);function ErrorNotice(t){var n,r=t.error,i=t.hasButton,d=void 0!==i&&i,f=t.storeName,g=t.message,p=void 0===g?r.message:g,m=t.noPrefix,v=void 0!==m&&m,b=t.skipRetryMessage,h=t.Icon,E=Object(c.useDispatch)(),y=Object(c.useSelect)((function(e){return f?e(f).getSelectorDataForError(r):null})),O=Object(a.useCallback)((function(){E(y.storeName).invalidateResolution(y.name,y.args)}),[E,y]);if(!r||Object(l.f)(r))return null;var k=d&&Object(l.d)(r,y);return d||b||(p=Object(o.sprintf)(
/* translators: %1$s: Error message from Google API. */
Object(o.__)("%1$s%2$s Please try again.","google-site-kit"),p,p.endsWith(".")?"":".")),e.createElement(a.Fragment,null,h&&e.createElement("div",{className:"googlesitekit-error-notice__icon"},e.createElement(h,{width:"24",height:"24"})),e.createElement(u.a,{message:p,reconnectURL:null===(n=r.data)||void 0===n?void 0:n.reconnectURL,noPrefix:v}),k&&e.createElement(s.Button,{className:"googlesitekit-error-notice__retry-button",onClick:O},Object(o.__)("Retry","google-site-kit")))}ErrorNotice.propTypes={error:i.a.shape({message:i.a.string}),hasButton:i.a.bool,storeName:i.a.string,message:i.a.string,noPrefix:i.a.bool,skipRetryMessage:i.a.bool,Icon:i.a.elementType}}).call(this,n(4))},121:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(219),i=n(14),a=n(0);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=Object(r.b)((function(){return i.debounce.apply(void 0,t)}),t);return Object(a.useEffect)((function(){return function(){return o.cancel()}}),[o]),o}},123:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(21),i=n.n(r),a=n(6),o=n.n(a),c=n(25),s=n.n(c),l=n(1),u=n.n(l),d=n(11),f=n.n(d);function Cell(t){var n,r=t.className,a=t.alignTop,c=t.alignMiddle,l=t.alignBottom,u=t.alignRight,d=t.alignLeft,g=t.smAlignRight,p=t.mdAlignRight,m=t.lgAlignRight,v=t.smSize,b=t.smStart,h=t.smOrder,E=t.mdSize,y=t.mdStart,O=t.mdOrder,k=t.lgSize,_=t.lgStart,S=t.lgOrder,j=t.size,w=t.children,C=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",i()({},C,{className:f()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":l,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":g,"mdc-layout-grid__cell--align-right-tablet":p,"mdc-layout-grid__cell--align-right-desktop":m},o()(n,"mdc-layout-grid__cell--span-".concat(j),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--start-".concat(_,"-desktop"),12>=_&&_>0),o()(n,"mdc-layout-grid__cell--order-".concat(S,"-desktop"),12>=S&&S>0),o()(n,"mdc-layout-grid__cell--span-".concat(E,"-tablet"),8>=E&&E>0),o()(n,"mdc-layout-grid__cell--start-".concat(y,"-tablet"),8>=y&&y>0),o()(n,"mdc-layout-grid__cell--order-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--span-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--start-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--order-".concat(h,"-phone"),4>=h&&h>0),n))}),w)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},124:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",i()({ref:n,className:u()("mdc-layout-grid__inner",r)},c),a)}));f.displayName="Row",f.propTypes={className:s.a.string,children:s.a.node},f.defaultProps={className:""},t.a=f}).call(this,n(4))},125:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.alignLeft,a=t.fill,c=t.className,s=t.children,l=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",i()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":l,"mdc-layout-grid--fill":a})},d,{ref:n}),s)}));f.displayName="Grid",f.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},f.defaultProps={className:""},t.a=f}).call(this,n(4))},126:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},127:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},128:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},1294:function(e,t,n){"use strict";n.r(t);var r=n(3),i=n.n(r),a=n(190),o=n.n(a),c=n(401),s=n.n(c),l=n(363),u=n.n(l),d=n(15),f=n.n(d),g=n(5),p=n.n(g),m=n(16),v=n.n(m),b=n(2),h=n(409),E=n(619),y=n(856),O=(n(736),n(860)),k=(n(738),n(739),n(861)),_=n(862),S=n(94),j=n(19),w=n(7),C=n(57),x=n(863),A=n(864),T=n(865),N=n(866),D=n(41),L=n(22),R=n(408),P=n(205),M=n(12),I=n.n(M),F=n(14),B=n(45),U=n.n(B),W=n(62),G=n(200),z=n(13),V=n(548);function q(){return(q=v()(p.a.mark((function e(t){var n,r,i,a,o,c,s,l,u,d;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.select,r=t.dispatch,!n(S.e).haveSettingsChanged()){e.next=9;break}return e.next=5,r(S.e).saveSettings();case 5:if(i=e.sent,!(a=i.error)){e.next=9;break}return e.abrupt("return",{error:a});case 9:if(!n(z.c).haveConversionTrackingSettingsChanged()){e.next=17;break}return e.next=13,r(z.c).saveConversionTrackingSettings();case 13:if(o=e.sent,!(c=o.error)){e.next=17;break}return e.abrupt("return",{error:c});case 17:if(!n(z.c).haveFirstPartyModeSettingsChanged()){e.next=35;break}return e.next=21,r(z.c).saveFirstPartyModeSettings();case 21:if(s=e.sent,!(l=s.error)){e.next=25;break}return e.abrupt("return",{error:l});case 25:if(!n(z.c).isFirstPartyModeEnabled()||n(D.a).isNotificationDismissed(P.b)){e.next=35;break}return e.next=28,r(D.a).dismissNotification(P.b);case 28:if(e.t0=e.sent,e.t0){e.next=31;break}e.t0={};case 31:if(u=e.t0,!(d=u.error)){e.next=35;break}return e.abrupt("return",{error:d});case 35:return e.next=37,U.a.invalidateCache("modules","ads");case 37:return e.next=39,U.a.invalidateCache("core","site","ads-measurement-status");case 39:return e.abrupt("return",{});case 40:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var H,K,Y,X=o.a.createModuleStore("ads",{ownedSettingsSlugs:["conversionID","paxConversionID","extCustomerID","customerID","userID"],storeName:S.e,settingSlugs:["conversionID","ownerID","paxConversionID","customerID","extCustomerID","formattedExtCustomerID","userID","accountOverviewURL"],requiresSetup:!0,submitChanges:function(e){return q.apply(this,arguments)},rollbackChanges:function(e){var t=e.select,n=e.dispatch;t(S.e).haveSettingsChanged()&&(n(S.e).rollbackSettings(),n(z.c).resetConversionTrackingSettings(),n(z.c).resetFirstPartyModeSettings())},validateCanSubmitChanges:function(e){var t=Object(W.e)(e)(S.e),n=t.isDoingSubmitChanges,r=t.haveSettingsChanged,i=t.getConversionID,a=t.getPaxConversionID;I()(!n(),G.a),I()(r(),G.b),I()(Object(V.a)(i())||Object(V.a)(a()),"a valid conversionID is required to submit changes")},validateHaveSettingsChanged:function(e,t,n){var r=t.settings,i=t.savedSettings,a=e(z.c).haveConversionTrackingSettingsChanged(),o=e(z.c).haveFirstPartyModeSettingsChanged();n&&I()(Object(F.isEqual)(Object(F.pick)(r,n),Object(F.pick)(i,n))||!a||o,G.b),I()(!Object(F.isEqual)(r,i)||a||o,G.b)}}),$={selectors:{getAdBlockerWarningMessage:Object(r.createRegistrySelector)((function(e){return function(){var t=e(w.a).isAdBlockerActive();if(void 0!==t)return t?e(j.a).isModuleConnected("ads")?Object(b.__)("To get the latest Ads data you will need to disable your Ad blocker","google-site-kit"):Object(b.__)("To set up Ads you will need to disable your Ad blocker","google-site-kit"):null}}))}},J=n(1034),Q=n(37),Z={receiveIsWooCommerceRedirectModalDismissed:function(e){return I()(void 0!==e,"A cacheHit is required."),{type:"RECEIVE_WOOCOMMERCE_MODAL_CACHE_KEY",payload:{cacheHit:e}}}},ee=Object(r.createReducer)((function(e,t){var n=t.type,r=t.payload;switch(n){case"RECEIVE_WOOCOMMERCE_MODAL_CACHE_KEY":e.woocommerceModalDismissed=r.cacheHit}})),te={isWooCommerceRedirectModalDismissed:p.a.mark((function e(){var t,n;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.await(Object(Q.d)(S.a));case 2:return t=e.sent,n=t.cacheHit,e.next=6,Z.receiveIsWooCommerceRedirectModalDismissed(n||!1);case 6:case"end":return e.stop()}}),e)}))},ne={initialState:{woocommerceModalDismissed:!1},actions:Z,reducer:ee,resolvers:te,selectors:{isWooCommerceRedirectModalDismissed:function(e){return e.woocommerceModalDismissed}}},re={selectors:{getDetailsLinkURL:Object(r.createRegistrySelector)((function(e){return function(){var t=e(S.e).getAccountOverviewURL();if(t)return e(w.a).getAccountChooserURL(t);var n=e(j.a).getModule("ads");return void 0!==n?null!==n&&n.homepage?e(w.a).getAccountChooserURL(n.homepage):null:void 0}}))}},ie=Object(r.combineStores)(X,$,J.a,ne,re),ae=(ie.initialState,ie.actions,ie.controls,ie.reducer,ie.resolvers,ie.selectors,{"setup-success-notification-ads":{Component:A.a,areaSlug:D.b.BANNERS_BELOW_NAV,viewContexts:[L.n,L.o],checkRequirements:function(){var e=Object(h.a)(location.href,"notification"),t=Object(h.a)(location.href,"slug");return"authentication_success"===e&&"ads"===t}},"setup-success-notification-pax":{Component:x.a,areaSlug:D.b.BANNERS_BELOW_NAV,viewContexts:[L.n,L.o],checkRequirements:function(){var e=Object(h.a)(location.href,"notification");return R.d===e}},"account-linked-via-google-for-woocommerce":{Component:T.a,areaSlug:D.b.BANNERS_BELOW_NAV,viewContexts:[L.n],checkRequirements:(K=v()(p.a.mark((function e(t){var n,r,i,a,o,c,s,l,u;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.select,r=t.resolveSelect,e.next=3,Promise.all([r(S.e).getModuleData(),r(j.a).isModuleConnected("ads")]);case 3:return i=e.sent,a=f()(i,2),o=a[1],c=n(S.e),s=c.isWooCommerceActivated,l=c.isGoogleForWooCommerceActivated,u=c.hasGoogleForWooCommerceAdsAccount,e.abrupt("return",!o&&s()&&l()&&u());case 8:case"end":return e.stop()}}),e)}))),function(e){return K.apply(this,arguments)}),featureFlag:"adsPax",isDismissible:!0},"ads-setup-cta":{Component:N.a,priority:P.c.SETUP_CTA_HIGH,areaSlug:D.b.BANNERS_BELOW_NAV,groupID:D.c.SETUP_CTAS,viewContexts:[L.n],checkRequirements:(H=v()(p.a.mark((function e(t){var n,r,i,a,o,c,s,l,u,d;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.select,r=t.resolveSelect,e.next=3,Promise.all([r(w.a).getDismissedPrompts(),r(S.e).getModuleData(),r(j.a).isModuleConnected("ads"),r(j.a).canActivateModule("ads")]);case 3:return i=n(j.a),a=i.isModuleConnected,o=n(w.a),c=o.isPromptDismissed,s=n(S.e),l=s.hasGoogleForWooCommerceAdsAccount,u=a("ads"),d=c("ads-setup-cta"),e.abrupt("return",!1===u&&!1===d&&!1===l());case 9:case"end":return e.stop()}}),e)}))),function(e){return H.apply(this,arguments)}),isDismissible:!0,dismissRetries:1,featureFlag:"adsPax"}});i.a.registerStore(S.e,ie),o.a.registerModule("ads",{storeName:S.e,SettingsEditComponent:O.a,SettingsViewComponent:y.a,SetupComponent:Object(C.b)("adsPax")?_.a:k.a,Icon:E.a,features:[Object(b.__)("Tagging necessary for your ads campaigns to work will be disabled","google-site-kit"),Object(b.__)("Conversion tracking for your ads campaigns will be disabled","google-site-kit")],overrideSetupSuccessNotification:!0,checkRequirements:(Y=v()(p.a.mark((function e(t){var n;return p.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.resolveSelect(w.a).isAdBlockerActive();case 2:if(e.sent){e.next=5;break}return e.abrupt("return");case 5:throw n=t.select(S.e).getAdBlockerWarningMessage(),{code:w.c,message:n,data:null};case 7:case"end":return e.stop()}}),e)}))),function(e){return Y.apply(this,arguments)})}),s.a,function(e){for(var t in ae)e.registerNotification(t,ae[t])}(u.a)},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var r="core/site",i="primary",a="secondary"},131:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},137:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},141:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return StoreErrorNotices}));var r=n(1),i=n.n(r),a=n(3),o=n(120),c=n(19),s=n(35),l=n(169);function StoreErrorNotices(t){var n=t.hasButton,r=void 0!==n&&n,i=t.moduleSlug,u=t.storeName,d=Object(a.useSelect)((function(e){return e(u).getErrors()})),f=Object(a.useSelect)((function(e){return e(c.a).getModule(i)})),g=[];return d.filter((function(e){return!(!(null==e?void 0:e.message)||g.includes(e.message))&&(g.push(e.message),!0)})).map((function(t,n){var i=t.message;return Object(s.e)(t)&&(i=Object(l.a)(i,f)),e.createElement(o.a,{key:n,error:t,hasButton:r,storeName:u,message:i})}))}StoreErrorNotices.propTypes={hasButton:i.a.bool,storeName:i.a.string.isRequired,moduleSlug:i.a.string}}).call(this,n(4))},147:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(15),o=n.n(a),c=n(0),s=n(409),l=n(157);t.a=function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=Object(c.useState)(Object(s.a)(r.location.href,t)||n),u=o()(a,2),d=u[0],f=u[1],g=function(e){f(e);var n=Object(l.a)(r.location.href,i()({},t,e));r.history.replaceState(null,"",n)};return[d,g]}}).call(this,n(28))},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},158:function(e,t,n){"use strict";var r=n(0),i=n(57),a=Object(r.createContext)(i.a);t.a=a},159:function(e,t,n){"use strict";n.d(t,"a",(function(){return v}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(0),s=n(3),l=n(13),u=n(7),d=n(19),f=n(32),g=n(37),p=n(36),m=n(18);function v(e){var t=Object(m.a)(),n=Object(s.useSelect)((function(t){return t(d.a).getModule(e)})),r=Object(s.useSelect)((function(e){return e(u.a).hasCapability(u.K)})),a=Object(s.useDispatch)(d.a).activateModule,v=Object(s.useDispatch)(f.a).navigateTo,b=Object(s.useDispatch)(l.c).setInternalServerError,h=Object(c.useCallback)(o()(i.a.mark((function n(){var r,o,c;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,a(e);case 2:if(r=n.sent,o=r.error,c=r.response,o){n.next=13;break}return n.next=8,Object(p.b)("".concat(t,"_widget-activation-cta"),"activate_module",e);case 8:return n.next=10,Object(g.f)("module_setup",e,{ttl:300});case 10:v(c.moduleReauthURL),n.next=14;break;case 13:b({id:"".concat(e,"-setup-error"),description:o.message});case 14:case"end":return n.stop()}}),n)}))),[a,e,v,b,t]);return(null==n?void 0:n.name)&&r?h:null}},168:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALinkSubtle}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(73),f=n(10),g=n(70);function CTALinkSubtle(t){var n=t.id,r=t.ctaLink,a=t.ctaLabel,c=t.onCTAClick,s=t.isCTALinkExternal,l=void 0!==s&&s,p=t.gaTrackingEventArgs,m=t.tertiary,v=void 0!==m&&m,b=t.isSaving,h=void 0!==b&&b,E=Object(d.a)(n,null==p?void 0:p.category),y=function(){var e=o()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==c?void 0:c(t);case 2:E.confirm(null==p?void 0:p.label,null==p?void 0:p.value);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(f.Button,{className:u()("googlesitekit-subtle-notification__cta",{"googlesitekit-subtle-notification__cta--spinner__running":h}),href:r,onClick:y,target:l?"_blank":"_self",trailingIcon:l?e.createElement(g.a,{width:14,height:14}):void 0,icon:h?e.createElement(f.CircularProgress,{size:14}):void 0,tertiary:v},a)}CTALinkSubtle.propTypes={id:s.a.string,ctaLink:s.a.string,ctaLabel:s.a.string,onCTAClick:s.a.func,isCTALinkExternal:s.a.bool,gaTrackingEventArgs:s.a.shape({label:s.a.string,value:s.a.string}),tertiary:s.a.bool,isSaving:s.a.bool}}).call(this,n(4))},169:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(2);function i(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},i=n.slug,a=void 0===i?"":i,o=n.name,c=void 0===o?"":o,s=n.owner,l=void 0===s?{}:s;if(!a||!c)return e;var u="",d="";return"analytics-4"===a?e.match(/account/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):"search-console"===a&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),u||(u=Object(r.sprintf)(
/* translators: %s: module name */
Object(r.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),c)),l&&l.login&&(d=Object(r.sprintf)(
/* translators: %s: owner name */
Object(r.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),l.login)),d||(d=Object(r.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(u," ").concat(d)}},17:function(e,t,n){"use strict";var r=n(254);n.d(t,"i",(function(){return r.a}));var i=n(319);n.d(t,"f",(function(){return i.a}));var a=n(320);n.d(t,"h",(function(){return a.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var s=n(91),l=n.n(s);n.d(t,"b",(function(){return l.a})),n.d(t,"c",(function(){return s.DialogContent})),n.d(t,"d",(function(){return s.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},175:function(e,t,n){"use strict";var r=n(216);n.d(t,"b",(function(){return r.a}));var i=n(221);n.d(t,"a",(function(){return i.a}))},179:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActionsCTALinkDismiss}));var r=n(1),i=n.n(r),a=n(2),o=n(0),c=n(3),s=n(32),l=n(92),u=n(187);function ActionsCTALinkDismiss(t){var n=t.id,r=t.className,i=void 0===r?"googlesitekit-publisher-win__actions":r,d=t.ctaLink,f=t.ctaLabel,g=t.ctaDisabled,p=void 0!==g&&g,m=t.onCTAClick,v=t.ctaDismissOptions,b=t.isSaving,h=void 0!==b&&b,E=t.onDismiss,y=void 0===E?function(){}:E,O=t.dismissLabel,k=void 0===O?Object(a.__)("OK, Got it!","google-site-kit"):O,_=t.dismissOnCTAClick,S=void 0===_||_,j=t.dismissExpires,w=void 0===j?0:j,C=t.dismissOptions,x=void 0===C?{}:C,A=t.gaTrackingEventArgs,T=void 0===A?{}:A,N=Object(c.useSelect)((function(e){return!!d&&e(s.a).isNavigatingTo(d)}));return e.createElement(o.Fragment,null,e.createElement("div",{className:i},e.createElement(u.a,{id:n,ctaLink:d,ctaLabel:f,onCTAClick:m,dismissOnCTAClick:S,dismissExpires:w,dismissOptions:v,gaTrackingEventArgs:T,isSaving:h,isDisabled:p}),e.createElement(l.a,{id:n,primary:!1,dismissLabel:k,dismissExpires:w,disabled:N,onDismiss:y,dismissOptions:x,gaTrackingEventArgs:T})))}ActionsCTALinkDismiss.propTypes={id:i.a.string,className:i.a.string,ctaDisabled:i.a.bool,ctaLink:i.a.string,ctaLabel:i.a.string,onCTAClick:i.a.func,isSaving:i.a.bool,onDismiss:i.a.func,ctaDismissOptions:i.a.object,dismissLabel:i.a.string,dismissOnCTAClick:i.a.bool,dismissExpires:i.a.number,dismissOptions:i.a.object,gaTrackingEventArgs:i.a.object}}).call(this,n(4))},18:function(e,t,n){"use strict";var r=n(0),i=n(61);t.a=function(){return Object(r.useContext)(i.b)}},180:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LearnMoreLink}));var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(20),u=n(73);function LearnMoreLink(t){var n=t.id,r=t.label,a=t.url,c=t.ariaLabel,s=t.gaTrackingEventArgs,d=t.external,f=void 0===d||d,g=o()(t,["id","label","url","ariaLabel","gaTrackingEventArgs","external"]),p=Object(u.a)(n);return e.createElement(l.a,i()({onClick:function(e){e.persist(),p.clickLearnMore(null==s?void 0:s.label,null==s?void 0:s.value)},href:a,"aria-label":c,external:f},g),r)}LearnMoreLink.propTypes={id:s.a.string,label:s.a.string,url:s.a.string,ariaLabel:s.a.string,gaTrackingEventArgs:s.a.shape({label:s.a.string,value:s.a.string}),external:s.a.bool}}).call(this,n(4))},182:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(1),i=n.n(r),a=" ";function DisplaySetting(e){return e.value||a}DisplaySetting.propTypes={value:i.a.oneOfType([i.a.string,i.a.bool,i.a.number])},t.b=DisplaySetting},183:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LoadingWrapper}));var r=n(6),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(44);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function LoadingWrapper(t){var n=t.loading,r=t.children,i=o()(t,["loading","children"]);return n?e.createElement(l.a,i):r}LoadingWrapper.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({loading:s.a.bool,children:s.a.node},l.a.propTypes)}).call(this,n(4))},187:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALink}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(15),u=n.n(l),d=n(1),f=n.n(d),g=n(206),p=n(0),m=n(3),v=n(41),b=n(32),h=n(13),E=n(73),y=n(10);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function CTALink(t){var n=t.id,r=t.ctaLink,a=t.ctaLabel,o=t.onCTAClick,c=t.isSaving,l=t.dismissOnCTAClick,d=void 0!==l&&l,f=t.dismissExpires,O=void 0===f?0:f,_=t.dismissOptions,S=void 0===_?{}:_,j=t.gaTrackingEventArgs,w=t.isDisabled,C=void 0!==w&&w,x=Object(p.useState)(!1),A=u()(x,2),T=A[0],N=A[1],D=Object(g.a)(),L=Object(E.a)(n,null==j?void 0:j.category),R=Object(m.useSelect)((function(e){return!!r&&e(b.a).isNavigatingTo(r)})),P=Object(m.useDispatch)(h.c),M=P.clearError,I=P.receiveError,F=Object(m.useDispatch)(v.a).dismissNotification,B=Object(m.useDispatch)(b.a).navigateTo,U=function(){var e=s()(i.a.mark((function e(t){var a,c,s;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return M("notificationAction",[n]),t.persist(),!t.defaultPrevented&&r&&t.preventDefault(),N(!0),e.next=6,null==o?void 0:o(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:if(a=e.t0,c=a.error,D()&&N(!1),!c){e.next=15;break}return I(c,"notificationAction",[n]),e.abrupt("return");case 15:return s=[L.confirm()],d&&s.push(F(n,k(k({},S),{},{expiresInSeconds:O}))),e.next=19,Promise.all(s);case 19:r&&B(r);case 20:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(y.SpinnerButton,{className:"googlesitekit-notification__cta",href:r,onClick:U,disabled:T||R||C,isSaving:T||R||c},a)}CTALink.propTypes={id:f.a.string,ctaLink:f.a.string,ctaLabel:f.a.string,onCTAClick:f.a.func,dismissOnCTAClick:f.a.bool,dismissExpires:f.a.number,dismissOptions:f.a.object,isDisabled:f.a.bool}}).call(this,n(4))},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="core/modules",i="insufficient_module_dependencies"},190:function(e,t){e.exports=googlesitekit.modules},199:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SupportLink}));var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(3),u=n(13),d=n(20);function SupportLink(t){var n=t.path,r=t.query,a=t.hash,c=o()(t,["path","query","hash"]),s=Object(l.useSelect)((function(e){return e(u.c).getGoogleSupportURL({path:n,query:r,hash:a})}));return e.createElement(d.a,i()({},c,{href:s}))}SupportLink.propTypes={path:s.a.string.isRequired,query:s.a.object,hash:s.a.string}}).call(this,n(4))},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(146),f=n(0),g=n(2),p=n(126),m=n(127),v=n(128),b=n(70),h=n(76),E=Object(f.forwardRef)((function(t,n){var r,a=t["aria-label"],c=t.secondary,l=void 0!==c&&c,u=t.arrow,f=void 0!==u&&u,E=t.back,y=void 0!==E&&E,O=t.caps,k=void 0!==O&&O,_=t.children,S=t.className,j=void 0===S?"":S,w=t.danger,C=void 0!==w&&w,x=t.disabled,A=void 0!==x&&x,T=t.external,N=void 0!==T&&T,D=t.hideExternalIndicator,L=void 0!==D&&D,R=t.href,P=void 0===R?"":R,M=t.inverse,I=void 0!==M&&M,F=t.noFlex,B=void 0!==F&&F,U=t.onClick,W=t.small,G=void 0!==W&&W,z=t.standalone,V=void 0!==z&&z,q=t.linkButton,H=void 0!==q&&q,K=t.to,Y=t.leadingIcon,X=t.trailingIcon,$=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),J=P||K||!U?K?"ROUTER_LINK":N?"EXTERNAL_LINK":"LINK":A?"BUTTON_DISABLED":"BUTTON",Q="BUTTON"===J||"BUTTON_DISABLED"===J?"button":"ROUTER_LINK"===J?d.b:"a",Z=("EXTERNAL_LINK"===J&&(r=Object(g._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===J&&(r=Object(g._x)("(disabled)","screen reader text","google-site-kit")),r?a?"".concat(a," ").concat(r):"string"==typeof _?"".concat(_," ").concat(r):void 0:a),ee=Y,te=X;return y&&(ee=e.createElement(v.a,{width:14,height:14})),N&&!L&&(te=e.createElement(b.a,{width:14,height:14})),f&&!I&&(te=e.createElement(p.a,{width:14,height:14})),f&&I&&(te=e.createElement(m.a,{width:14,height:14})),e.createElement(Q,i()({"aria-label":Z,className:s()("googlesitekit-cta-link",j,{"googlesitekit-cta-link--secondary":l,"googlesitekit-cta-link--inverse":I,"googlesitekit-cta-link--small":G,"googlesitekit-cta-link--caps":k,"googlesitekit-cta-link--danger":C,"googlesitekit-cta-link--disabled":A,"googlesitekit-cta-link--standalone":V,"googlesitekit-cta-link--link-button":H,"googlesitekit-cta-link--no-flex":!!B}),disabled:A,href:"LINK"!==J&&"EXTERNAL_LINK"!==J||A?void 0:P,onClick:U,rel:"EXTERNAL_LINK"===J?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===J?"_blank":void 0,to:K},$),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},_),!!te&&e.createElement(h.a,{marginLeft:5},te))}));E.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=E}).call(this,n(4))},200:function(e,t,n){"use strict";n.d(t,"a",(function(){return _})),n.d(t,"b",(function(){return S})),n.d(t,"c",(function(){return j})),n.d(t,"g",(function(){return w})),n.d(t,"f",(function(){return C})),n.d(t,"d",(function(){return x})),n.d(t,"e",(function(){return A}));var r=n(16),i=n.n(r),a=n(5),o=n.n(a),c=n(6),s=n.n(c),l=n(12),u=n.n(l),d=n(14),f=n(45),g=n.n(f),p=n(3),m=n(62),v=n(82),b=n(48),h=n(64);function E(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?E(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):E(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var O=h.a.clearError,k=h.a.receiveError,_="cannot submit changes while submitting changes",S="cannot submit changes if settings have not changed",j=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=r.ownedSettingsSlugs,a=void 0===i?void 0:i,c=r.storeName,l=void 0===c?void 0:c,f=r.settingSlugs,h=void 0===f?[]:f,E=r.initialSettings,_=void 0===E?void 0:E,S=r.validateHaveSettingsChanged,j=void 0===S?A():S;u()(e,"type is required."),u()(t,"identifier is required."),u()(n,"datapoint is required.");var w=l||"".concat(e,"/").concat(t),C={ownedSettingsSlugs:a,settings:_,savedSettings:void 0},x=Object(b.a)({baseName:"getSettings",controlCallback:function(){return g.a.get(e,t,n,{},{useCache:!1})},reducerCallback:function(e,t){return y(y({},e),{},{savedSettings:y({},t),settings:y(y({},t),e.settings||{})})}}),T=Object(b.a)({baseName:"saveSettings",controlCallback:function(r){var i=r.values;return g.a.set(e,t,n,i)},reducerCallback:function(e,t){return y(y({},e),{},{savedSettings:y({},t),settings:y({},t)})},argsToParams:function(e){return{values:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.values;u()(Object(d.isPlainObject)(t),"values is required.")}}),N={},D={setSettings:function(e){return u()(Object(d.isPlainObject)(e),"values is required."),{payload:{values:e},type:"SET_SETTINGS"}},rollbackSettings:function(){return{payload:{},type:"ROLLBACK_SETTINGS"}},rollbackSetting:function(e){return u()(e,"setting is required."),{payload:{setting:e},type:"ROLLBACK_SETTING"}},saveSettings:o.a.mark((function e(){var t,n,r,i,a;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,O("saveSettings",[]);case 5:return n=t.select(w).getSettings(),e.next=8,T.actions.fetchSaveSettings(n);case 8:if(r=e.sent,i=r.response,!(a=r.error)){e.next=14;break}return e.next=14,k(a,"saveSettings",[]);case 14:return e.abrupt("return",{response:i,error:a});case 15:case"end":return e.stop()}}),e)}))},L={},R=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:C,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.payload;switch(n){case"SET_SETTINGS":var i=r.values;return y(y({},e),{},{settings:y(y({},e.settings||{}),i)});case"ROLLBACK_SETTINGS":return y(y({},e),{},{settings:e.savedSettings});case"ROLLBACK_SETTING":var a=r.setting;return e.savedSettings[a]?y(y({},e),{},{settings:y(y({},e.settings||{}),{},s()({},a,e.savedSettings[a]))}):y({},e);default:return void 0!==N[n]?N[n](e,{type:n,payload:r}):e}},P={getSettings:o.a.mark((function e(){var t;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.commonActions.getRegistry();case 2:if(t=e.sent,t.select(w).getSettings()){e.next=7;break}return e.next=7,x.actions.fetchGetSettings();case 7:case"end":return e.stop()}}),e)}))},M=Object(m.g)(j),I=M.safeSelector,F=M.dangerousSelector,B={haveSettingsChanged:I,__dangerousHaveSettingsChanged:F,getSettings:function(e){return e.settings},hasSettingChanged:function(e,t){u()(t,"setting is required.");var n=e.settings,r=e.savedSettings;return!(!n||!r)&&!Object(d.isEqual)(n[t],r[t])},isDoingSaveSettings:function(e){return Object.values(e.isFetchingSaveSettings).some(Boolean)},getOwnedSettingsSlugs:function(e){return e.ownedSettingsSlugs},haveOwnedSettingsChanged:Object(p.createRegistrySelector)((function(e){return function(){var t=e(w).getOwnedSettingsSlugs();return e(w).haveSettingsChanged(t)}}))};h.forEach((function(e){var t=Object(v.b)(e),n=Object(v.a)(e);D["set".concat(t)]=function(e){return u()(void 0!==e,"value is required for calls to set".concat(t,"().")),{payload:{value:e},type:"SET_".concat(n)}},N["SET_".concat(n)]=function(t,n){var r=n.payload.value;return y(y({},t),{},{settings:y(y({},t.settings||{}),{},s()({},e,r))})},B["get".concat(t)]=Object(p.createRegistrySelector)((function(t){return function(){return(t(w).getSettings()||{})[e]}}))}));var U=Object(p.combineStores)(p.commonStore,x,T,{initialState:C,actions:D,controls:L,reducer:R,resolvers:P,selectors:B});return y(y({},U),{},{STORE_NAME:w})};function w(e,t){return function(){var n=i()(o.a.mark((function n(r){var i,a,c,s;return o.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(i=r.select,a=r.dispatch,!i(t).haveSettingsChanged()){n.next=8;break}return n.next=4,a(t).saveSettings();case 4:if(c=n.sent,!(s=c.error)){n.next=8;break}return n.abrupt("return",{error:s});case 8:return n.next=10,g.a.invalidateCache("modules",e);case 10:return n.abrupt("return",{});case 11:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}function C(e){return function(t){var n=t.select,r=t.dispatch;return n(e).haveSettingsChanged()?r(e).rollbackSettings():{}}}function x(e){return function(t){var n=Object(m.e)(t)(e),r=n.haveSettingsChanged,i=n.isDoingSubmitChanges;u()(!i(),_),u()(r(),S)}}function A(){return function(e,t,n){var r=t.settings,i=t.savedSettings;n&&u()(!Object(d.isEqual)(Object(d.pick)(r,n),Object(d.pick)(i,n)),S),u()(!Object(d.isEqual)(r,i),S)}}},205:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a}));var r="warning-notification-fpm",i="fpm-setup-cta",a={ERROR_HIGH:30,ERROR_LOW:60,WARNING:100,INFO:150,SETUP_CTA_HIGH:150,SETUP_CTA_LOW:200}},207:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5 22c-1.522 0-2.952-.284-4.29-.852a11.303 11.303 0 01-3.493-2.366 11.303 11.303 0 01-2.365-3.492A10.86 10.86 0 01.5 11c0-1.522.284-2.952.853-4.29a11.302 11.302 0 012.364-3.493A10.92 10.92 0 017.21.88 10.567 10.567 0 0111.5 0c1.522 0 2.952.293 4.29.88a10.92 10.92 0 013.492 2.337c.99.99 1.77 2.155 2.338 3.493.587 1.338.88 2.768.88 4.29 0 1.522-.293 2.952-.88 4.29a10.92 10.92 0 01-2.338 3.492c-.99.99-2.154 1.779-3.492 2.366A10.86 10.86 0 0111.5 22zm0-14.3c.312 0 .569-.1.77-.303.22-.22.33-.485.33-.797a.999.999 0 00-.33-.77.999.999 0 00-.77-.33c-.311 0-.577.11-.797.33a1.043 1.043 0 00-.303.77c0 .312.101.578.303.798.22.201.486.302.797.302zm-1.1 8.8V9.9h2.2v6.6h-2.2z",fill:"currentColor"});t.a=function SvgInfoCircle(e){return r.createElement("svg",i({viewBox:"0 0 23 22",fill:"none"},e),a)}},216:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(0),u=n(3),d=n(13),f=n(23);function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e){var t=Object(u.useDispatch)(f.b).setValue,n=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.2")})),r=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.4")}));return Object(l.useCallback)(s()(i.a.mark((function a(){var o,c,s,l;return i.a.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(o=document.querySelector("#adminmenu").offsetHeight>0){i.next=7;break}if(!(c=document.getElementById("wp-admin-bar-menu-toggle"))){i.next=7;break}return c.firstChild.click(),i.next=7,new Promise((function(e){setTimeout(e,0)}));case 7:"#adminmenu [href*='page=googlesitekit-dashboard']",(s=!!document.querySelector("".concat("#adminmenu [href*='page=googlesitekit-dashboard']","[aria-haspopup=true]")))&&document.querySelector("#adminmenu [href*='page=googlesitekit-dashboard']").click(),n&&!r&&(l=document.hasFocus,document.hasFocus=function(){return document.hasFocus=l,!1}),t("admin-menu-tooltip",p({isTooltipVisible:!0,rehideAdminMenu:!o,rehideAdminSubMenu:s},e));case 12:case"end":return i.stop()}}),a)}))),[n,r,t,e])}},217:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WarningNotice}));var r=n(11),i=n.n(r),a=n(1),o=n.n(a);function WarningNotice(t){var n=t.children,r=t.className;return e.createElement("div",{className:i()("googlesitekit-warning-notice",r)},n)}WarningNotice.propTypes={children:o.a.node.isRequired,className:o.a.string}}).call(this,n(4))},22:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return i})),n.d(t,"o",(function(){return a})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return s})),n.d(t,"s",(function(){return l})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return f})),n.d(t,"k",(function(){return g})),n.d(t,"u",(function(){return p})),n.d(t,"v",(function(){return m})),n.d(t,"q",(function(){return v})),n.d(t,"p",(function(){return b})),n.d(t,"b",(function(){return h})),n.d(t,"e",(function(){return E})),n.d(t,"a",(function(){return y})),n.d(t,"d",(function(){return O})),n.d(t,"c",(function(){return k})),n.d(t,"f",(function(){return _})),n.d(t,"g",(function(){return S}));var r="mainDashboard",i="entityDashboard",a="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",s="activation",l="splash",u="adminBar",d="adminBarViewOnly",f="settings",g="adBlockingRecovery",p="wpDashboard",m="wpDashboardViewOnly",v="moduleSetup",b="metricSelection",h="key-metrics",E="traffic",y="content",O="speed",k="monetization",_=[r,i,a,o,c,l,f,v,b],S=[a,o,d,m]},221:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdminMenuTooltip}));var r=n(0),i=n(3),a=n(223),o=n(23),c=n(9),s=n(18);function AdminMenuTooltip(){var t=Object(s.a)(),n=Object(i.useDispatch)(o.b).setValue,l=Object(i.useSelect)((function(e){return e(o.b).getValue("admin-menu-tooltip")||{isTooltipVisible:!1}})),u=l.isTooltipVisible,d=void 0!==u&&u,f=l.rehideAdminMenu,g=void 0!==f&&f,p=l.rehideAdminSubMenu,m=void 0!==p&&p,v=l.tooltipSlug,b=l.title,h=l.content,E=l.dismissLabel,y=Object(r.useCallback)((function(){var e;g&&(document.querySelector("#adminmenu").offsetHeight>0&&(null===(e=document.getElementById("wp-admin-bar-menu-toggle"))||void 0===e||e.click()));m&&document.querySelector("body").click(),v&&Object(c.I)("".concat(t,"_").concat(v),"tooltip_dismiss"),n("admin-menu-tooltip",void 0)}),[g,m,n,v,t]);return d?e.createElement(a.a,{target:'#adminmenu [href*="page=googlesitekit-settings"]',slug:"ga4-activation-banner-admin-menu-tooltip",title:b,content:h,dismissLabel:E,onView:function(){Object(c.I)("".concat(t,"_").concat(v),"tooltip_view")},onDismiss:y}):null}}).call(this,n(4))},223:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return JoyrideTooltip}));var i=n(6),a=n.n(i),o=n(15),c=n.n(o),s=n(1),l=n(30),u=n(421),d=n(0),f=n(107),g=n(72),p=n(90);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function JoyrideTooltip(t){var n=t.title,i=t.content,a=t.dismissLabel,o=t.target,s=t.cta,m=void 0!==s&&s,b=t.className,h=t.styles,E=void 0===h?{}:h,y=t.slug,O=void 0===y?"":y,k=t.onDismiss,_=void 0===k?function(){}:k,S=t.onView,j=void 0===S?function(){}:S,w=t.onTourStart,C=void 0===w?function(){}:w,x=t.onTourEnd,A=void 0===x?function(){}:x,T=function(){return!!e.document.querySelector(o)},N=Object(d.useState)(T),D=c()(N,2),L=D[0],R=D[1];if(Object(u.a)((function(){T()&&R(!0)}),L?null:250),Object(d.useEffect)((function(){if(L&&e.ResizeObserver){var t=e.document.querySelector(o),n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}),[o,L]),!L)return null;var P=[{title:n,target:o,content:i,disableBeacon:!0,isFixed:!0,placement:"auto",cta:m,className:b}],M={close:a,last:a};return r.createElement(g.a,{slug:O},r.createElement(l.e,{callback:function(t){switch(t.type){case l.b.TOUR_START:C(),e.document.body.classList.add("googlesitekit-showing-tooltip");break;case l.b.TOUR_END:A(),e.document.body.classList.remove("googlesitekit-showing-tooltip");break;case l.b.STEP_AFTER:_();break;case l.b.TOOLTIP:j()}},disableOverlay:!0,disableScrolling:!0,spotlightPadding:0,floaterProps:p.b,locale:M,steps:P,styles:v(v(v({},p.c),E),{},{options:v(v({},p.c.options),null==E?void 0:E.options),spotlight:v(v({},p.c.spotlight),null==E?void 0:E.spotlight)}),tooltipComponent:f.a,run:!0}))}JoyrideTooltip.propTypes={title:s.PropTypes.node,content:s.PropTypes.string,dismissLabel:s.PropTypes.string,target:s.PropTypes.string.isRequired,onDismiss:s.PropTypes.func,onShow:s.PropTypes.func,className:s.PropTypes.string,styles:s.PropTypes.object,slug:s.PropTypes.string,onView:s.PropTypes.func}}).call(this,n(28),n(4))},23:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r="core/ui",i="activeContextID"},24:function(e,t,n){"use strict";n.d(t,"d",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return s}));var r=n(79),i="xlarge",a="desktop",o="tablet",c="small";function s(){var e=Object(r.a)();return e>1280?i:e>960?a:e>600?o:c}},242:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationWithSVG}));var r=n(21),i=n.n(r),a=n(11),o=n.n(a),c=n(24),s=n(17),l=n(255);function NotificationWithSVG(t){var n=t.id,r=t.title,a=t.description,u=t.actions,d=t.SVG,f=t.primaryCellSizes,g=t.SVGCellSizes,p=Object(c.e)(),m={mdSize:(null==g?void 0:g.md)||8,lgSize:(null==g?void 0:g.lg)||6};return p===c.c&&(m={mdSize:(null==g?void 0:g.md)||8}),p===c.b&&(m={smSize:(null==g?void 0:g.sm)||12}),e.createElement("div",{className:"googlesitekit-widget-context"},e.createElement(s.e,{className:"googlesitekit-widget-area"},e.createElement(s.k,null,e.createElement(s.a,{size:12},e.createElement("div",{className:o()("googlesitekit-widget","googlesitekit-widget--no-padding","googlesitekit-setup-cta-banner","googlesitekit-setup-cta-banner--".concat(n))},e.createElement("div",{className:"googlesitekit-widget__body"},e.createElement(s.e,{collapsed:!0},e.createElement(s.k,null,e.createElement(s.a,{smSize:(null==f?void 0:f.sm)||12,mdSize:(null==f?void 0:f.md)||8,lgSize:(null==f?void 0:f.lg)||6,className:"googlesitekit-setup-cta-banner__primary-cell"},e.createElement("h3",{className:"googlesitekit-setup-cta-banner__title"},r),a,e.createElement(l.a,{id:n}),u),e.createElement(s.a,i()({alignBottom:!0,className:"googlesitekit-setup-cta-banner__svg-wrapper--".concat(n)},m),e.createElement(d,null))))))))))}}).call(this,n(4))},245:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdBlockerWarning}));var r=n(1),i=n.n(r),a=n(3),o=n(13),c=n(19),s=n(395);function AdBlockerWarning(t){var n=t.moduleSlug,r=t.className,i=Object(a.useSelect)((function(e){return e(c.a).getModuleStoreName(n)})),l=Object(a.useSelect)((function(e){var t;return null===(t=e(i))||void 0===t?void 0:t.getAdBlockerWarningMessage()})),u=Object(a.useSelect)((function(e){return e(o.c).getDocumentationLinkURL("".concat(n,"-ad-blocker-detected"))}));return e.createElement(s.a,{className:r,getHelpLink:u,warningMessage:l})}AdBlockerWarning.propTypes={className:i.a.string,moduleSlug:i.a.string.isRequired}}).call(this,n(4))},255:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Error}));var r=n(1),i=n.n(r),a=n(0),o=n(3),c=n(13),s=n(54);function Error(t){var n=t.id,r=Object(o.useSelect)((function(e){return e(c.c).getError("notificationAction",[n])})),i=Object(o.useDispatch)(c.c).clearError;return Object(a.useEffect)((function(){return function(){i("notificationAction",[n])}}),[i,n]),r?e.createElement(s.a,{message:r.message}):null}Error.propTypes={id:i.a.string}}).call(this,n(4))},258:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g}));var r=n(11),i=n.n(r),a=n(1),o=n.n(a),c=n(0),s=n(10),l=n(137),u=n(70),d=n(58),f=n(207),g={SUCCESS:"success",WARNING:"warning",INFO:"info"},p=Object(c.forwardRef)((function(t,n){var r=t.title,a=t.description,o=t.Icon,c=t.ctaLink,p=t.ctaLabel,m=t.className,v=t.onCTAClick,b=t.isCTALinkExternal,h=t.dismissLabel,E=t.onDismiss,y=t.variant,O=void 0===y?g.SUCCESS:y,k=t.hideIcon,_=void 0!==k&&k;return e.createElement("div",{ref:n,className:i()("googlesitekit-subtle-notification",{"googlesitekit-subtle-notification--success":O===g.SUCCESS,"googlesitekit-subtle-notification--warning":O===g.WARNING,"googlesitekit-subtle-notification--info":O===g.INFO},m)},!_&&e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},o&&e.createElement(o,{width:24,height:24}),!o&&O===g.SUCCESS&&e.createElement(l.a,{width:24,height:24}),!o&&O===g.WARNING&&e.createElement(d.a,{width:24,height:24}),!o&&O===g.INFO&&e.createElement(f.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,r),a&&e.createElement("p",{className:"googlesitekit-subtle-notification__secondary_description"},a)),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},h&&e.createElement(s.Button,{tertiary:!0,onClick:E},h),p&&e.createElement(s.Button,{className:"googlesitekit-subtle-notification__cta",href:c,onClick:v,target:b?"_blank":"_self",trailingIcon:b?e.createElement(u.a,{width:14,height:14}):void 0},p)))}));p.propTypes={title:o.a.node.isRequired,description:o.a.string,Icon:o.a.elementType,ctaLink:o.a.string,ctaLabel:o.a.string,className:o.a.string,onCTAClick:o.a.func,isCTALinkExternal:o.a.bool,dismissLabel:o.a.string,onDismiss:o.a.func,variant:o.a.oneOf(Object.values(g)),hideIcon:o.a.bool},t.b=p}).call(this,n(4))},273:function(e,t,n){"use strict";(function(e){var r=n(55),i=n.n(r),a=n(274),o=e._googlesitekitAPIFetchData||{},c=o.nonce,s=o.nonceEndpoint,l=o.preloadedData,u=o.rootURL;i.a.nonceEndpoint=s,i.a.nonceMiddleware=i.a.createNonceMiddleware(c),i.a.rootURLMiddleware=i.a.createRootURLMiddleware(u),i.a.preloadingMiddleware=Object(a.a)(l),i.a.use(i.a.nonceMiddleware),i.a.use(i.a.mediaUploadMiddleware),i.a.use(i.a.rootURLMiddleware),i.a.use(i.a.preloadingMiddleware),t.default=i.a}).call(this,n(28))},274:function(e,t,n){"use strict";var r=n(262);t.a=function(e){var t=Object.keys(e).reduce((function(t,n){return t[Object(r.getStablePath)(n)]=e[n],t}),{}),n=!1;return function(e,i){if(n)return i(e);setTimeout((function(){n=!0}),3e3);var a=e.parse,o=void 0===a||a,c=e.path;if("string"==typeof e.path){var s,l=(null===(s=e.method)||void 0===s?void 0:s.toUpperCase())||"GET",u=Object(r.getStablePath)(c);if(o&&"GET"===l&&t[u]){var d=Promise.resolve(t[u].body);return delete t[u],d}if("OPTIONS"===l&&t[l]&&t[l][u]){var f=Promise.resolve(t[l][u]);return delete t[l][u],f}}return i(e)}}},3:function(e,t){e.exports=googlesitekit.data},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},35:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return d}));n(14);var r=n(2),i="missing_required_scopes",a="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===i}function s(e){var t;return[a,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function l(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||s(e)||c(e)||l(e))}function d(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(r.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(r.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return y})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return E}));var r=n(99),i=e._googlesitekitTrackingData||{},a=i.activeModules,o=void 0===a?[]:a,c=i.isSiteKitScreen,s=i.trackingEnabled,l=i.trackingID,u=i.referenceSiteURL,d=i.userIDHash,f=i.isAuthenticated,g={activeModules:o,trackingEnabled:s,trackingID:l,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:i.userRoles,isAuthenticated:f,pluginVersion:"1.151.0"},p=Object(r.a)(g),m=p.enableTracking,v=p.disableTracking,b=(p.isTrackingEnabled,p.initializeSnippet),h=p.trackEvent,E=p.trackEventOnce;function y(e){e?m():v()}c&&s&&b()}).call(this,n(28))},363:function(e,t){e.exports=googlesitekit.notifications},364:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 19h22L11 0 0 19zm12-3h-2v-2h2v2zm0-4h-2V8h2v4z",fill:"currentColor"});t.a=function SvgWarningV2(e){return r.createElement("svg",i({viewBox:"0 0 22 19"},e),a)}},367:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),i=n(158),a=n(57),o=function(e){var t=Object(r.useContext)(i.a);return Object(a.b)(e,t)}},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return E})),n.d(t,"c",(function(){return y})),n.d(t,"e",(function(){return O})),n.d(t,"b",(function(){return k}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u,d="googlesitekit_",f="".concat(d).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),g=["sessionStorage","localStorage"],p=[].concat(g),m=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,a="__storage_test__",r.setItem(a,a),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function v(){return b.apply(this,arguments)}function b(){return(b=o()(i.a.mark((function t(){var n,r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=s(p),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(a=r.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,m(a);case 11:if(!t.sent){t.next=13;break}u=e[a];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=o()(i.a.mark((function e(t){var n,r,a,o,c,s,l;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(f).concat(t)))){e.next=10;break}if(a=JSON.parse(r),o=a.timestamp,c=a.ttl,s=a.value,l=a.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:l});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),E=function(){var t=o()(i.a.mark((function t(n,r){var a,o,s,l,u,d,g,p,m=arguments;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=m.length>2&&void 0!==m[2]?m[2]:{},o=a.ttl,s=void 0===o?c.b:o,l=a.timestamp,u=void 0===l?Math.round(Date.now()/1e3):l,d=a.isError,g=void 0!==d&&d,t.next=3,v();case 3:if(!(p=t.sent)){t.next=14;break}return t.prev=5,p.setItem("".concat(f).concat(n),JSON.stringify({timestamp:u,ttl:s,value:r,isError:g})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),y=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,v();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,a=n.startsWith(d)?n:"".concat(f).concat(n),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),O=function(){var t=o()(i.a.mark((function t(){var n,r,a,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,v();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],a=0;a<n.length;a++)0===(o=n.key(a)).indexOf(d)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),k=function(){var e=o()(i.a.mark((function e(){var t,n,r,a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v();case 2:if(!e.sent){e.next=25;break}return e.next=6,O();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return a=r.value,e.next=14,y(a);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="_googlesitekitDataLayer",i="data-googlesitekit-gtag"},395:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdBlockerWarningMessage}));var r=n(1),i=n.n(r),a=n(2),o=n(38),c=n(20),s=n(217),l=n(396);function AdBlockerWarningMessage(t){var n=t.className,r=void 0===n?"":n,i=t.getHelpLink,u=void 0===i?"":i,d=t.warningMessage,f=void 0===d?null:d;return f?e.createElement(s.a,{className:r},Object(o.a)(Object(a.sprintf)(
/* translators: 1: The warning message. 2: "Get help" text. */
Object(a.__)("%1$s. <Link>%2$s</Link>","google-site-kit"),f,Object(a.__)("Get help","google-site-kit")),{Link:e.createElement(c.a,{href:u,external:!0,hideExternalIndicator:!0,trailingIcon:e.createElement(l.a,{width:15,height:15})})})):null}AdBlockerWarningMessage.propTypes={className:i.a.string,getHelpLink:i.a.string,warningMessage:i.a.string}}).call(this,n(4))},396:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M4.5 1.5H3a2 2 0 00-2 2v7a2 2 0 002 2h7a2 2 0 002-2V9M7 1.5h5v5M5 8.5L11.5 2",stroke:"currentColor",strokeWidth:1.5});t.a=function SvgExternalRounded(e){return r.createElement("svg",i({viewBox:"0 0 13 14",fill:"none"},e),a)}},401:function(e,t){e.exports=googlesitekit.widgets},408:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"d",(function(){return o}));var r="pax",i="_googlesitekitPAXConfig",a={LAUNCH:1,FINISHED:2},o="pax_setup_success_notification"},41:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(22),i="core/notifications",a={ERRORS:"notification-area-errors",BANNERS_ABOVE_NAV:"notification-area-banners-above-nav",BANNERS_BELOW_NAV:"notification-area-banners-below-nav"},o={DEFAULT:"default",SETUP_CTAS:"setup-ctas"},c=[r.s,r.n,r.l,r.o,r.m]},414:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupEnhancedConversionTrackingNotice}));var r=n(11),i=n.n(r),a=n(3),o=n(13);function SetupEnhancedConversionTrackingNotice(t){var n=t.className,r=t.message,c=Object(a.useSelect)((function(e){return e(o.c).isConversionTrackingEnabled()}));return c||void 0===c?null:e.createElement("p",{className:i()(n,"googlesitekit-color--surfaces-on-background-variant")},r)}}).call(this,n(4))},44:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(1),o=n.n(a),c=n(11),s=n.n(c),l=n(24);function PreviewBlock(t){var n,r,a=t.className,o=t.width,c=t.height,u=t.shape,d=t.padding,f=t.smallWidth,g=t.smallHeight,p=t.tabletWidth,m=t.tabletHeight,v=t.desktopWidth,b=t.desktopHeight,h=Object(l.e)(),E={width:(n={},i()(n,l.b,f),i()(n,l.c,p),i()(n,l.a,v),i()(n,l.d,v),n),height:(r={},i()(r,l.b,g),i()(r,l.c,m),i()(r,l.a,b),i()(r,l.d,v),r)};return e.createElement("div",{className:s()("googlesitekit-preview-block",a,{"googlesitekit-preview-block--padding":d}),style:{width:E.width[h]||o,height:E.height[h]||c}},e.createElement("div",{className:s()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(4))},45:function(e,t){e.exports=googlesitekit.api},48:function(e,t,n){"use strict";n.d(t,"a",(function(){return y}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(12),s=n.n(c),l=n(14),u=n(64),d=n(82),f=n(9);function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var m=function(e){return e},v=function(){return{}},b=function(){},h=u.a.clearError,E=u.a.receiveError,y=function(e){var t,n,r=i.a.mark(P),a=e.baseName,c=e.controlCallback,u=e.reducerCallback,g=void 0===u?m:u,y=e.argsToParams,O=void 0===y?v:y,k=e.validateParams,_=void 0===k?b:k;s()(a,"baseName is required."),s()("function"==typeof c,"controlCallback is required and must be a function."),s()("function"==typeof g,"reducerCallback must be a function."),s()("function"==typeof O,"argsToParams must be a function."),s()("function"==typeof _,"validateParams must be a function.");try{_(O()),n=!1}catch(e){n=!0}var S=Object(d.b)(a),j=Object(d.a)(a),w="FETCH_".concat(j),C="START_".concat(w),x="FINISH_".concat(w),A="CATCH_".concat(w),T="RECEIVE_".concat(j),N="fetch".concat(S),D="receive".concat(S),L="isFetching".concat(S),R=o()({},L,{});function P(e,t){var n,o;return i.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,{payload:{params:e},type:C};case 2:return r.next=4,h(a,t);case 4:return r.prev=4,r.next=7,{payload:{params:e},type:w};case 7:return n=r.sent,r.next=10,M[D](n,e);case 10:return r.next=12,{payload:{params:e},type:x};case 12:r.next=21;break;case 14:return r.prev=14,r.t0=r.catch(4),o=r.t0,r.next=19,E(o,a,t);case 19:return r.next=21,{payload:{params:e},type:A};case 21:return r.abrupt("return",{response:n,error:o});case 22:case"end":return r.stop()}}),r,null,[[4,14]])}var M=(t={},o()(t,N,(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=O.apply(void 0,t);return _(r),P(r,t)})),o()(t,D,(function(e,t){return s()(void 0!==e,"response is required."),n?(s()(Object(l.isPlainObject)(t),"params is required."),_(t)):t={},{payload:{response:e,params:t},type:T}})),t),I=o()({},w,(function(e){var t=e.payload;return c(t.params)})),F=o()({},L,(function(e){if(void 0===e[L])return!1;var t;try{for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];t=O.apply(void 0,r),_(t)}catch(e){return!1}return!!e[L][Object(f.H)(t)]}));return{initialState:R,actions:M,controls:I,reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case C:var i=r.params;return p(p({},e),{},o()({},L,p(p({},e[L]),{},o()({},Object(f.H)(i),!0))));case T:var a=r.response,c=r.params;return g(e,a,c);case x:var s=r.params;return p(p({},e),{},o()({},L,p(p({},e[L]),{},o()({},Object(f.H)(s),!1))));case A:var l=r.params;return p(p({},e),{},o()({},L,p(p({},e[L]),{},o()({},Object(f.H)(l),!1))));default:return e}},resolvers:{},selectors:F}}},525:function(e,t,n){"use strict";var r=n(857);n.d(t,"a",(function(){return r.a}));n(737);var i=n(859);n.d(t,"b",(function(){return i.a}))},54:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,r=t.reconnectURL,i=t.noPrefix;if(!n)return null;var s=n;void 0!==i&&i||(s=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),r&&Object(a.a)(r)&&(s=s+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),r));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(s,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:i.a.string.isRequired,reconnectURL:i.a.string,noPrefix:i.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},548:function(e,t,n){"use strict";function r(e){return"string"==typeof e&&""!==e&&/^AW-[0-9]+$/.test(e)}n.d(t,"a",(function(){return r}))},57:function(e,t,n){"use strict";(function(e){var r,i;n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=new Set((null===(r=e)||void 0===r||null===(i=r._googlesitekitBaseData)||void 0===i?void 0:i.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,n(28))},58:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return r.createElement("svg",i({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),a,o)}},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(39);function i(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},598:function(e,t,n){"use strict";function r(e){if(void 0!==e)return!e}n.d(t,"a",(function(){return r}))},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(0),i=Object(r.createContext)(""),a=(i.Consumer,i.Provider);t.b=i},619:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#ads_svg__clip0_13_5816)"},r.createElement("path",{d:"M13.612 5.602c.386-1.015.918-1.95 1.707-2.706C18.477-.181 23.697.592 25.84 4.458c1.61 2.932 3.318 5.8 4.978 8.7 2.77 4.816 5.573 9.632 8.312 14.465 2.303 4.044-.193 9.15-4.768 9.843-2.803.419-5.43-.87-6.88-3.383-2.432-4.237-4.88-8.473-7.313-12.694a1.466 1.466 0 00-.177-.258c-.258-.21-.37-.515-.531-.79-1.08-1.9-2.191-3.785-3.27-5.67-.693-1.224-1.418-2.432-2.11-3.656a6.67 6.67 0 01-.887-3.544c.048-.645.129-1.29.419-1.87z",fill:"#3C8BD9"}),r.createElement("path",{d:"M13.612 5.602c-.145.58-.274 1.16-.306 1.772-.048 1.353.29 2.61.966 3.786 1.772 3.044 3.544 6.105 5.3 9.166.161.274.29.547.451.805a804.94 804.94 0 01-2.915 5.01c-1.354 2.336-2.707 4.688-4.076 7.024-.064 0-.08-.032-.097-.08-.016-.13.033-.242.065-.371.66-2.417.113-4.56-1.547-6.396-1.015-1.111-2.303-1.74-3.785-1.949-1.933-.274-3.641.226-5.171 1.434-.274.21-.451.515-.774.677-.064 0-.096-.033-.112-.081.773-1.337 1.53-2.674 2.303-4.011 3.19-5.542 6.38-11.083 9.585-16.609.032-.064.08-.113.113-.177z",fill:"#FABC04"}),r.createElement("path",{d:"M1.675 26.447c.306-.274.596-.564.918-.821 3.915-3.093 9.795-.854 10.648 4.043.21 1.176.097 2.304-.257 3.431a1.697 1.697 0 01-.065.274c-.145.258-.274.532-.435.79-1.434 2.368-3.544 3.544-6.315 3.366C2.996 37.305.5 34.92.064 31.763c-.21-1.53.097-2.964.886-4.285.161-.29.354-.548.532-.838.08-.064.048-.193.193-.193z",fill:"#34A852"}),r.createElement("path",{d:"M1.675 26.447c-.064.065-.064.177-.177.194-.016-.113.048-.178.113-.258l.064.064z",fill:"#FABC04"}),r.createElement("path",{d:"M12.92 33.374c-.065-.113 0-.193.064-.274l.064.065-.129.21z",fill:"#E1C025"})),o=r.createElement("defs",null,r.createElement("clipPath",{id:"ads_svg__clip0_13_5816"},r.createElement("path",{fill:"#fff",d:"M0 0h40v40H0z"})));t.a=function SvgAds(e){return r.createElement("svg",i({viewBox:"0 0 40 40",fill:"none"},e),a,o)}},62:function(e,t,n){"use strict";n.d(t,"a",(function(){return w})),n.d(t,"b",(function(){return C})),n.d(t,"c",(function(){return x})),n.d(t,"d",(function(){return T})),n.d(t,"e",(function(){return N})),n.d(t,"g",(function(){return L})),n.d(t,"f",(function(){return R}));var r,i=n(5),a=n.n(i),o=n(27),c=n.n(o),s=n(6),l=n.n(s),u=n(12),d=n.n(u),f=n(63),g=n.n(f),p=n(14),m=n(116);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.reduce((function(e,t){return b(b({},e),t)}),{}),i=t.reduce((function(e,t){return[].concat(c()(e),c()(Object.keys(t)))}),[]),a=A(i);return d()(0===a.length,"collect() cannot accept collections with duplicate keys. Your call to collect() contains the following duplicated functions: ".concat(a.join(", "),". Check your data stores for duplicates.")),r},E=h,y=h,O=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i=[].concat(t);return"function"!=typeof i[0]&&(r=i.shift()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i.reduce((function(e,n){return n(e,t)}),e)}},k=h,_=h,S=h,j=function(e){return e},w=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=S.apply(void 0,c()(t.map((function(e){return e.initialState||{}}))));return{initialState:r,controls:y.apply(void 0,c()(t.map((function(e){return e.controls||{}})))),actions:E.apply(void 0,c()(t.map((function(e){return e.actions||{}})))),reducer:O.apply(void 0,[r].concat(c()(t.map((function(e){return e.reducer||j}))))),resolvers:k.apply(void 0,c()(t.map((function(e){return e.resolvers||{}})))),selectors:_.apply(void 0,c()(t.map((function(e){return e.selectors||{}}))))}},C={getRegistry:function(){return{payload:{},type:"GET_REGISTRY"}},await:a.a.mark((function e(t){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{payload:{value:t},type:"AWAIT"});case 1:case"end":return e.stop()}}),e)}))},x=(r={},l()(r,"GET_REGISTRY",Object(m.a)((function(e){return function(){return e}}))),l()(r,"AWAIT",(function(e){return e.payload.value})),r),A=function(e){for(var t=[],n={},r=0;r<e.length;r++){var i=e[r];n[i]=n[i]>=1?n[i]+1:1,n[i]>1&&t.push(i)}return t},T={actions:C,controls:x,reducer:j},N=function(e){return function(t){return D(e(t))}},D=g()((function(e){return Object(p.mapValues)(e,(function(e,t){return function(){var n=e.apply(void 0,arguments);return d()(void 0!==n,"".concat(t,"(...) is not resolved")),n}}))}));function L(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.negate,r=void 0!==n&&n,i=Object(m.b)((function(t){return function(n){var i=!r,a=!!r;try{for(var o=arguments.length,c=new Array(o>1?o-1:0),s=1;s<o;s++)c[s-1]=arguments[s];return e.apply(void 0,[t,n].concat(c)),i}catch(e){return a}}})),a=Object(m.b)((function(t){return function(n){for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];e.apply(void 0,[t,n].concat(i))}}));return{safeSelector:i,dangerousSelector:a}}function R(e,t){return d()("function"==typeof e,"a validator function is required."),d()("function"==typeof t,"an action creator function is required."),d()("Generator"!==e[Symbol.toStringTag]&&"GeneratorFunction"!==e[Symbol.toStringTag],"an action’s validator function must not be a generator."),function(){return e.apply(void 0,arguments),t.apply(void 0,arguments)}}},64:function(e,t,n){"use strict";n.d(t,"a",(function(){return v})),n.d(t,"b",(function(){return b}));var r=n(6),i=n.n(r),a=n(33),o=n.n(a),c=n(116),s=n(12),l=n.n(s),u=n(96),d=n.n(u),f=n(9);function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e,t){if(t&&Array.isArray(t)){var n=t.map((function(e){return"object"===o()(e)?Object(f.H)(e):e}));return"".concat(e,"::").concat(d()(JSON.stringify(n)))}return e}var v={receiveError:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(e,"error is required."),l()(t,"baseName is required."),l()(n&&Array.isArray(n),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:n}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return l()(e,"baseName is required."),l()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function b(e){l()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(n,"selectorName is required."),t.getError(e,n,r)},getErrorForAction:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(n,"actionName is required."),t.getError(e,n,r)},getError:function(e,t,n){var r=e.errors;return l()(t,"baseName is required."),r[m(t,n)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var n=Object.keys(e.errors).find((function(n){return e.errors[n]===t}));return n?{baseName:n.substring(0,n.indexOf("::")),args:e.errorArgs[n]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(n,r){var i=t(e).getMetaDataForError(r);if(i){var a=i.baseName,o=i.args;if(!!t(e)[a])return{storeName:e,name:a,args:o}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:v,controls:{},reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case"RECEIVE_ERROR":var a=r.baseName,o=r.args,c=r.error,s=m(a,o);return p(p({},e),{},{errors:p(p({},e.errors||{}),{},i()({},s,c)),errorArgs:p(p({},e.errorArgs||{}),{},i()({},s,o))});case"CLEAR_ERROR":var l=r.baseName,u=r.args,d=p({},e),f=m(l,u);return d.errors=p({},e.errors||{}),d.errorArgs=p({},e.errorArgs||{}),delete d.errors[f],delete d.errorArgs[f],d;case"CLEAR_ERRORS":var g=r.baseName,v=p({},e);if(g)for(var b in v.errors=p({},e.errors||{}),v.errorArgs=p({},e.errorArgs||{}),v.errors)(b===g||b.startsWith("".concat(g,"::")))&&(delete v.errors[b],delete v.errorArgs[b]);else v.errors={},v.errorArgs={};return v;default:return e}},resolvers:{},selectors:t}}},653:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return w}));var r=n(15),i=n.n(r),a=n(25),o=n.n(a),c=n(5),s=n.n(c),l=n(6),u=n.n(l),d=n(12),f=n.n(d),g=n(775),p=n.n(g),m=n(157),v=n(409),b=n(3),h=n(13),E=n(9),y=n(598);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){u()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function _(e){return Object(b.createRegistrySelector)((function(t){return function(){return(t(h.c).getSiteInfo()||{})[e]}}))}var S={siteInfo:void 0,permaLink:!1},j={receiveSiteInfo:function(e){return f()(e,"siteInfo is required."),{payload:{siteInfo:e},type:"RECEIVE_SITE_INFO"}},receivePermaLinkParam:function(e){return f()(e,"permaLink is required."),{payload:{permaLink:e},type:"RECEIVE_PERMALINK_PARAM"}},setSiteKitAutoUpdatesEnabled:function(e){return f()("boolean"==typeof e,"siteKitAutoUpdatesEnabled must be a boolean."),{payload:{siteKitAutoUpdatesEnabled:e},type:"SET_SITE_KIT_AUTO_UPDATES_ENABLED"}},setKeyMetricsSetupCompletedBy:function(e){return f()("number"==typeof e,"keyMetricsSetupCompletedBy must be a number."),{payload:{keyMetricsSetupCompletedBy:e},type:"SET_KEY_METRICS_SETUP_COMPLETED_BY"}},setSetupErrorCode:function(e){return f()("string"==typeof e||null===e,"setupErrorCode must be a string or null."),{payload:{setupErrorCode:e},type:"SET_SETUP_ERROR_CODE"}}},w={},C={getSiteInfo:s.a.mark((function t(){var n,r,i,a,o,c,l,u,d,f,g,p,m,v,E,y,O,k,_,S,w,C,x,A,T,N,D,L,R,P,M,I,F,B;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b.commonActions.getRegistry();case 2:if(!t.sent.select(h.c).getSiteInfo()){t.next=5;break}return t.abrupt("return");case 5:if(e._googlesitekitBaseData&&e._googlesitekitEntityData){t.next=8;break}return e.console.error("Could not load core/site info."),t.abrupt("return");case 8:return n=e._googlesitekitBaseData,r=n.adminURL,i=n.ampMode,a=n.homeURL,o=n.proxyPermissionsURL,c=n.proxySetupURL,l=n.referenceSiteURL,u=n.setupErrorCode,d=n.setupErrorMessage,f=n.setupErrorRedoURL,g=n.siteName,p=n.siteLocale,m=n.timezone,v=n.usingProxy,E=n.webStoriesActive,y=n.proxySupportLinkURL,O=n.widgetsAdminURL,k=n.postTypes,_=n.wpVersion,S=n.updateCoreURL,w=n.changePluginAutoUpdatesCapacity,C=n.siteKitAutoUpdatesEnabled,x=n.pluginBasename,A=n.productPostType,T=n.keyMetricsSetupCompletedBy,N=n.keyMetricsSetupNew,D=n.consentModeRegions,L=n.anyoneCanRegister,R=n.isMultisite,P=e._googlesitekitEntityData,M=P.currentEntityID,I=P.currentEntityTitle,F=P.currentEntityType,B=P.currentEntityURL,t.next=12,j.receiveSiteInfo({adminURL:r,ampMode:i,currentEntityID:M,currentEntityTitle:I,currentEntityType:F,currentEntityURL:B,homeURL:a,proxyPermissionsURL:o,proxySetupURL:c,referenceSiteURL:l,setupErrorCode:u,setupErrorMessage:d,setupErrorRedoURL:f,siteName:g,siteLocale:p,timezone:m,postTypes:k,usingProxy:!!v,webStoriesActive:E,proxySupportLinkURL:y,widgetsAdminURL:O,wpVersion:_,updateCoreURL:S,changePluginAutoUpdatesCapacity:w,siteKitAutoUpdatesEnabled:C,pluginBasename:x,productPostType:A,keyMetricsSetupCompletedBy:T,keyMetricsSetupNew:N,consentModeRegions:D,anyoneCanRegister:L,isMultisite:R});case 12:case"end":return t.stop()}}),t)}))},x={getSiteInfo:function(e){return e.siteInfo},getAdminURL:Object(b.createRegistrySelector)((function(e){return function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=e(h.c).getSiteInfo()||{},a=i.adminURL;if(void 0===a||void 0===n)return a;var c="/"===a[a.length-1]?a:"".concat(a,"/"),s=n,l="admin.php";if(-1!==n.indexOf(".php?")){var u=n.split("?");if(!(s=p.a.parse(u.pop()).page))return a;l=u.shift()}r.page;var d=o()(r,["page"]);return Object(m.a)("".concat(c).concat(l),k({page:s},d))}})),getAMPMode:_("ampMode"),getCurrentEntityID:_("currentEntityID"),getCurrentEntityTitle:_("currentEntityTitle"),getCurrentEntityType:_("currentEntityType"),getCurrentEntityURL:_("currentEntityURL"),getHomeURL:_("homeURL"),getReferenceSiteURL:_("referenceSiteURL"),getProxySetupURL:_("proxySetupURL"),getProxyPermissionsURL:_("proxyPermissionsURL"),getCurrentReferenceURL:Object(b.createRegistrySelector)((function(e){return function(){var t=e(h.c).getCurrentEntityURL();return null!==t?t:e(h.c).getReferenceSiteURL()}})),isAMP:Object(b.createRegistrySelector)((function(e){return function(){var t=e(h.c).getAMPMode();if(void 0!==t)return!!t}})),isPrimaryAMP:Object(b.createRegistrySelector)((function(e){return function(){var t=e(h.c).getAMPMode();if(void 0!==t)return t===h.a}})),isSecondaryAMP:Object(b.createRegistrySelector)((function(e){return function(){var t=e(h.c).getAMPMode();if(void 0!==t)return t===h.b}})),getAdminSettingsURL:Object(b.createRegistrySelector)((function(e){return function(){var t=e(h.c).getAdminURL(),n=e(h.c).isMultisite();if(void 0!==t&&void 0!==n)return new URL(!0===n?"network/settings.php":"options-general.php",t).href}})),getTimezone:_("timezone"),isUsingProxy:_("usingProxy"),getSiteName:_("siteName"),getSiteLocale:Object(b.createRegistrySelector)((function(e){return function(){var t,n;return null===(t=e(h.c).getSiteInfo())||void 0===t||null===(n=t.siteLocale)||void 0===n?void 0:n.replace("_","-")}})),getSetupErrorCode:_("setupErrorCode"),getSetupErrorMessage:_("setupErrorMessage"),getSetupErrorRedoURL:_("setupErrorRedoURL"),getProxySupportLinkURL:_("proxySupportLinkURL"),getWidgetsAdminURL:_("widgetsAdminURL"),getPostTypes:_("postTypes"),getPermaLinkParam:function(t){if(t.permaLink)return t.permaLink;var n=Object(v.a)(e.location.href,"permaLink");return n||!1},isWebStoriesActive:_("webStoriesActive"),isSiteURLMatch:Object(b.createRegistrySelector)((function(e){return function(t,n){var r=e(h.c).getReferenceSiteURL();return Object(E.A)(r)===Object(E.A)(n)}})),getSiteURLPermutations:Object(b.createRegistrySelector)((function(e){return function(){var t=e(h.c).getReferenceSiteURL(),n=[],r=new URL(t);return r.hostname=r.hostname.replace(/^www\./i,""),r.protocol="http",n.push(Object(E.K)(r)),r.protocol="https",n.push(Object(E.K)(r)),r.hostname="www."+r.hostname,n.push(Object(E.K)(r)),r.protocol="http",n.push(Object(E.K)(r)),n}})),getWPVersion:_("wpVersion"),getUpdateCoreURL:_("updateCoreURL"),hasChangePluginAutoUpdatesCapacity:_("changePluginAutoUpdatesCapacity"),getSiteKitAutoUpdatesEnabled:_("siteKitAutoUpdatesEnabled"),getPluginBasename:_("pluginBasename"),getKeyMetricsSetupCompletedBy:_("keyMetricsSetupCompletedBy"),getKeyMetricsSetupNew:_("keyMetricsSetupNew"),hasMinimumWordPressVersion:Object(b.createRegistrySelector)((function(e){return function(t,n){f()(n,"minimumWPVersion is required.");var r=e(h.c).getWPVersion()||{},a=r.major,o=r.minor;if(void 0!==a&&void 0!==o){var c=n.split(".").map((function(e){return parseInt(e,10)})),s=i()(c,2),l=s[0],u=s[1];return l<a||l===a&&(void 0===u?0:u)<=o}}})),getProductPostType:_("productPostType"),isKeyMetricsSetupCompleted:function(e){return Object(y.a)(Object(y.a)(x.getKeyMetricsSetupCompletedBy(e)))},getConsentModeRegions:_("consentModeRegions"),getAnyoneCanRegister:_("anyoneCanRegister"),isMultisite:_("isMultisite")};t.b={initialState:S,actions:j,controls:w,reducer:function(e,t){var n=t.payload;switch(t.type){case"RECEIVE_SITE_INFO":var r=n.siteInfo,i=r.adminURL,a=r.ampMode,o=r.currentEntityID,c=r.currentEntityTitle,s=r.currentEntityType,l=r.currentEntityURL,u=r.homeURL,d=r.proxyPermissionsURL,f=r.proxySetupURL,g=r.referenceSiteURL,p=r.setupErrorCode,m=r.setupErrorMessage,v=r.setupErrorRedoURL,b=r.siteName,h=r.siteLocale,E=r.timezone,y=r.usingProxy,O=r.webStoriesActive,_=r.proxySupportLinkURL,S=r.widgetsAdminURL,j=r.postTypes,w=r.wpVersion,C=r.updateCoreURL,x=r.changePluginAutoUpdatesCapacity,A=r.siteKitAutoUpdatesEnabled,T=r.pluginBasename,N=r.productPostType,D=r.keyMetricsSetupCompletedBy,L=r.keyMetricsSetupNew,R=r.consentModeRegions,P=r.anyoneCanRegister,M=r.isMultisite;return k(k({},e),{},{siteInfo:{adminURL:i,ampMode:a,currentEntityID:parseInt(o,10),currentEntityTitle:c,currentEntityType:s,currentEntityURL:l,homeURL:u,proxyPermissionsURL:d,proxySetupURL:f,referenceSiteURL:g,setupErrorCode:p,setupErrorMessage:m,setupErrorRedoURL:v,siteName:b,siteLocale:h,timezone:E,usingProxy:y,webStoriesActive:O,proxySupportLinkURL:_,widgetsAdminURL:S,postTypes:j,wpVersion:w,updateCoreURL:C,changePluginAutoUpdatesCapacity:x,siteKitAutoUpdatesEnabled:A,pluginBasename:T,productPostType:N,keyMetricsSetupCompletedBy:D,keyMetricsSetupNew:L,consentModeRegions:R,anyoneCanRegister:P,isMultisite:M}});case"RECEIVE_PERMALINK_PARAM":var I=n.permaLink;return k(k({},e),{},{permaLink:I});case"SET_SITE_KIT_AUTO_UPDATES_ENABLED":var F=n.siteKitAutoUpdatesEnabled;return k(k({},e),{},{siteInfo:k(k({},e.siteInfo),{},{siteKitAutoUpdatesEnabled:F})});case"SET_KEY_METRICS_SETUP_COMPLETED_BY":var B=n.keyMetricsSetupCompletedBy;return k(k({},e),{},{siteInfo:k(k({},e.siteInfo),{},{keyMetricsSetupCompletedBy:B})});case"SET_SETUP_ERROR_CODE":var U=n.setupErrorCode;return k(k({},e),{},{siteInfo:k(k({},e.siteInfo),{},{setupErrorCode:U})});default:return e}},resolvers:C,selectors:x}}).call(this,n(28))},656:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsStatuses}));var r=n(1),i=n.n(r),a=n(2),o=n(10);function SettingsStatuses(t){var n=t.statuses;if(!n||0===n.length)return null;return e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},n.map((function(t){var n=t.label,r=t.status;return e.createElement("div",{key:n,className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},n),function(t){return void 0===t?e.createElement("div",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(o.ProgressBar,null)):e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},t?Object(a.__)("Enabled","google-site-kit"):Object(a.__)("Disabled","google-site-kit"))}(r))})))}SettingsStatuses.propTypes={statuses:i.a.arrayOf(i.a.shape({label:i.a.string.isRequired,status:i.a.oneOf([void 0,!0,!1])}))}}).call(this,n(4))},657:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConversionTrackingToggle}));var r=n(15),i=n.n(r),a=n(0),o=n(2),c=n(10),s=n(3),l=n(13),u=n(54),d=n(183),f=n(658),g=n(18),p=n(9),m=n(1),v=n.n(m);function ConversionTrackingToggle(t){var n=t.children,r=t.loading,m=Object(g.a)(),v=Object(a.useState)(null),b=i()(v,1)[0],h=Object(a.useState)(!1),E=i()(h,2),y=E[0],O=E[1],k=Object(s.useSelect)((function(e){return e(l.c).isConversionTrackingEnabled()})),_=Object(s.useSelect)((function(e){return e(l.c).isFetchingSaveConversionTrackingSettings()})),S=Object(s.useDispatch)(l.c).setConversionTrackingEnabled;return e.createElement("div",null,e.createElement(d.a,{loading:r,width:"180px",height:"21.3px"},e.createElement("div",{className:"googlesitekit-module-settings-group__switch"},e.createElement(c.Switch,{label:Object(o.__)("Enhanced conversion tracking","google-site-kit"),checked:k,disabled:_||r,onClick:function(){k?(Object(p.I)("".concat(m),"ect_disable"),O(!0)):(Object(p.I)("".concat(m),"ect_enable"),S(!0))},hideLabel:!1}))),!!b&&e.createElement(u.a,{message:b.message}),e.createElement(d.a,{className:"googlesitekit-settings-conversion-tracking-switch-description--loading",loading:r,width:"750px",height:"42px",smallWidth:"386px",smallHeight:"84px",tabletWidth:"540px",tabletHeight:"84px"},e.createElement("p",{className:"googlesitekit-module-settings-group__helper-text"},n)),y&&e.createElement(f.a,{onConfirm:function(){Object(p.I)("".concat(m),"ect_confirm_disable"),S(!1),O(!1)},onCancel:function(){Object(p.I)("".concat(m),"ect_cancel_disable"),O(!1)}}))}ConversionTrackingToggle.propTypes={children:v.a.node.isRequired,loading:v.a.bool}}).call(this,n(4))},658:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConfirmDisableConversionTrackingDialog}));var r=n(1),i=n.n(r),a=n(81),o=n(2),c=n(109),s=n(9),l=n(18);function ConfirmDisableConversionTrackingDialog(t){var n=t.onConfirm,r=t.onCancel,i=Object(l.a)(),u=Object(o.__)("By disabling enhanced conversion tracking, you will no longer have access to:","google-site-kit"),d=[Object(o.__)("Performance of your Ad campaigns","google-site-kit"),Object(o.__)("Tracking additional conversion-related events via Analytics","google-site-kit")];return Object(a.a)((function(){Object(s.I)("".concat(i),"ect_view_modal")})),e.createElement(c.a,{className:"googlesitekit-settings-module__confirm-disconnect-modal",dialogActive:!0,title:Object(o.__)("Disable enhanced conversion tracking","google-site-kit"),subtitle:u,handleConfirm:n,handleDialog:r,onClose:r,provides:d,confirmButton:Object(o.__)("Disable","google-site-kit"),danger:!0})}ConfirmDisableConversionTrackingDialog.propTypes={onConfirm:i.a.func.isRequired,onCancel:i.a.func.isRequired}}).call(this,n(4))},659:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsGroup}));var r=n(1),i=n.n(r);function SettingsGroup(t){var n=t.title,r=t.children;return e.createElement("div",{className:"googlesitekit-module-settings-group"},e.createElement("h4",null,n),r)}SettingsGroup.propTypes={title:i.a.string.isRequired,children:i.a.node.isRequired}}).call(this,n(4))},660:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return FirstPartyModeToggle}));var r=n(11),i=n.n(r),a=n(81),o=n(0),c=n(38),s=n(2),l=n(10),u=n(3),d=n(13),f=n(77),g=n(258),p=n(20),m=n(18),v=n(9),b=n(113),h=Object(b.a)(g.b);function FirstPartyModeToggle(t){var n=t.className,r=Object(m.a)(),g=Object(u.useSelect)((function(e){return e(d.c).isFirstPartyModeEnabled()})),b=Object(u.useSelect)((function(e){return e(d.c).isFetchingGetFPMServerRequirementStatus()})),E=Object(u.useSelect)((function(e){var t=e(d.c),n=t.isFPMHealthy,r=t.isScriptAccessEnabled;return!1!==n()&&!1!==r()})),y=Object(u.useDispatch)(d.c),O=y.fetchGetFPMServerRequirementStatus,k=y.setFirstPartyModeEnabled,_=Object(u.useSelect)((function(e){return e(d.c).getDocumentationLinkURL("first-party-mode-introduction")})),S=Object(u.useSelect)((function(e){return e(d.c).getDocumentationLinkURL("first-party-mode-server-requirements")}));Object(a.a)(O);var j=Object(o.useCallback)((function(){var e=g?"deactivate_first_party_mode":"activate_first_party_mode";Object(v.I)("".concat(r,"_fpm-settings-toggle"),e).finally((function(){k(!g)}))}),[g,k,r]);return e.createElement("div",{className:i()("googlesitekit-first-party-mode-toggle",n)},b&&e.createElement(l.ProgressBar,{small:!0,className:"googlesitekit-first-party-mode-toggle__progress"}),!b&&e.createElement("div",{className:"googlesitekit-module-settings-group__switch"},e.createElement(l.Switch,{label:Object(s.__)("First-party mode","google-site-kit"),checked:!!g&&E,disabled:!E,onClick:j,hideLabel:!1}),e.createElement("div",{className:"googlesitekit-first-party-mode-toggle__switch-badge"},e.createElement(f.a,{className:"googlesitekit-badge--beta",hasLeftSpacing:!0,label:Object(s.__)("Beta","google-site-kit")}))),e.createElement("p",{className:"googlesitekit-module-settings-group__helper-text"},Object(c.a)(Object(s.__)("Your tag data will be sent through your own domain to improve data quality and help you recover measurement signals. <a>Learn more</a>","google-site-kit"),{a:e.createElement(p.a,{href:_,onClick:function(){Object(v.I)("".concat(r,"_fpm-settings-toggle"),"click_learn_more_link")},external:!0,"aria-label":Object(s.__)("Learn more about First-party mode","google-site-kit")})})),!b&&!E&&e.createElement(h,{title:Object(c.a)(Object(s.__)("Your server’s current settings prevent First-party mode from working. To enable it, please contact your hosting provider and request access to external resources and plugin files. <a>Learn more</a>","google-site-kit"),{a:e.createElement(p.a,{href:S,onClick:function(){Object(v.I)("".concat(r,"_fpm-settings-toggle-disabled"),"click_learn_more_link")},external:!0,"aria-label":Object(s.__)("Learn more about First-party mode server requirements","google-site-kit")})}),variant:"warning",onInView:function(){Object(v.I)("".concat(r,"_fpm-settings-toggle-disabled"),"view_notice")}}))}}).call(this,n(4))},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return l})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return f})),n.d(t,"J",(function(){return g})),n.d(t,"I",(function(){return p})),n.d(t,"N",(function(){return m})),n.d(t,"f",(function(){return v})),n.d(t,"g",(function(){return b})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return E})),n.d(t,"l",(function(){return y})),n.d(t,"m",(function(){return O})),n.d(t,"n",(function(){return k})),n.d(t,"o",(function(){return _})),n.d(t,"q",(function(){return S})),n.d(t,"s",(function(){return j})),n.d(t,"r",(function(){return w})),n.d(t,"t",(function(){return C})),n.d(t,"w",(function(){return x})),n.d(t,"u",(function(){return A})),n.d(t,"v",(function(){return T})),n.d(t,"x",(function(){return N})),n.d(t,"y",(function(){return D})),n.d(t,"A",(function(){return L})),n.d(t,"B",(function(){return R})),n.d(t,"C",(function(){return P})),n.d(t,"D",(function(){return M})),n.d(t,"k",(function(){return I})),n.d(t,"F",(function(){return F})),n.d(t,"z",(function(){return B})),n.d(t,"G",(function(){return U})),n.d(t,"E",(function(){return W})),n.d(t,"i",(function(){return G})),n.d(t,"p",(function(){return z})),n.d(t,"Q",(function(){return V})),n.d(t,"P",(function(){return q}));var r="core/user",i="connected_url_mismatch",a="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",l="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",f="googlesitekit_read_shared_module_data",g="googlesitekit_manage_module_sharing_options",p="googlesitekit_delegate_module_sharing_management",m="googlesitekit_update_plugins",v="kmAnalyticsAdSenseTopEarningContent",b="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",E="kmAnalyticsNewVisitors",y="kmAnalyticsPopularAuthors",O="kmAnalyticsPopularContent",k="kmAnalyticsPopularProducts",_="kmAnalyticsReturningVisitors",S="kmAnalyticsTopCities",j="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",C="kmAnalyticsTopCitiesDrivingPurchases",x="kmAnalyticsTopDeviceDrivingPurchases",A="kmAnalyticsTopConvertingTrafficSource",T="kmAnalyticsTopCountries",N="kmAnalyticsTopPagesDrivingLeads",D="kmAnalyticsTopRecentTrendingPages",L="kmAnalyticsTopTrafficSource",R="kmAnalyticsTopTrafficSourceDrivingAddToCart",P="kmAnalyticsTopTrafficSourceDrivingLeads",M="kmAnalyticsTopTrafficSourceDrivingPurchases",I="kmAnalyticsPagesPerVisit",F="kmAnalyticsVisitLength",B="kmAnalyticsTopReturningVisitorPages",U="kmSearchConsolePopularKeywords",W="kmAnalyticsVisitsPerVisitor",G="kmAnalyticsMostEngagingPages",z="kmAnalyticsTopCategories",V=[v,b,h,E,y,O,k,_,z,S,j,w,C,x,A,T,D,L,R,I,F,B,W,G,z],q=[].concat(V,[U])},70:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},72:function(e,t,n){"use strict";var r=n(15),i=n.n(r),a=n(265),o=n(1),c=n.n(o),s=n(0),l=n(144);function Portal(e){var t=e.children,n=e.slug,r=Object(s.useState)(document.createElement("div")),o=i()(r,1)[0];return Object(a.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(l.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},73:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),i=n(18),a=n(9);function o(e,t){var n=Object(i.a)(),o=null!=t?t:"".concat(n,"_").concat(e);return{view:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"view_notification"].concat(t))}),[o]),confirm:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"confirm_notification"].concat(t))}),[o]),dismiss:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"dismiss_notification"].concat(t))}),[o]),clickLearnMore:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"click_learn_more_link"].concat(t))}),[o])}}},732:function(e,t,n){"use strict";n.d(t,"a",(function(){return r.a})),n.d(t,"b",(function(){return O})),n.d(t,"c",(function(){return m}));var r=n(858),i=n(5),a=n.n(i),o=n(16),c=n.n(o),s=n(14),l=n(63),u=n.n(l),d=n(273),f=n(13),g=n(94),p=n(9);function m(e){var t=Object(p.G)(e);return{year:t.getFullYear(),month:t.getMonth()+1,day:t.getDate()}}var v=n(7),b=n(45),h=n.n(b),E=function(){var e=c()(a.a.mark((function e(){var t;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(d.default)({path:"/wp/v2/pages?per_page=100"});case 3:return t=e.sent,e.abrupt("return",t.map((function(e){return{title:e.title.rendered,path:new URL(e.link).pathname}})));case 7:return e.prev=7,e.t0=e.catch(0),e.abrupt("return",[]);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();function y(){var e=u()((function(){return h.a.set("core","user","get-token")})),t=Object(s.debounce)(e.clear,3e4,{leading:!1,trailing:!0,maxWait:24e4});function n(){return t(),e()}return n.clear=function(){t.cancel(),e.clear()},n}function O(e){var t,n,r,i,o,s,l,u,d,p,b,h=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},O=h.onCampaignCreated,k=void 0===O?null:O,_=h.onFinishAndCloseSignUpFlow,S=void 0===_?null:_,j=e.select,w=e.resolveSelect,C=y(),x=function(){var e=c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,w(g.e).getModuleData();case 2:return e.abrupt("return",j(g.e).getSupportedConversionEvents()||[]);case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),A={authenticationService:{get:(b=c()(a.a.mark((function e(){var t;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,C();case 2:return t=e.sent,e.abrupt("return",{accessToken:t.token});case 4:case"end":return e.stop()}}),e)}))),function(){return b.apply(this,arguments)}),fix:(p=c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return C.clear(),e.abrupt("return",{retryReady:!0});case 2:case"end":return e.stop()}}),e)}))),function(){return p.apply(this,arguments)})},businessService:{getBusinessInfo:(d=c()(a.a.mark((function e(){var t,n;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,w(f.c).getSiteInfo();case 2:return t=j(f.c).getSiteName(),n=j(f.c).getHomeURL(),e.abrupt("return",{businessName:t,businessUrl:n});case 5:case"end":return e.stop()}}),e)}))),function(){return d.apply(this,arguments)}),fixBusinessInfo:(u=c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{retryReady:!0});case 1:case"end":return e.stop()}}),e)}))),function(){return u.apply(this,arguments)})},campaignService:{notifyNewCampaignCreated:(l=c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!k){e.next=3;break}return e.next=3,k();case 3:return e.abrupt("return",{});case 4:case"end":return e.stop()}}),e)}))),function(){return l.apply(this,arguments)})},conversionTrackingService:{getSupportedConversionLabels:(s=c()(a.a.mark((function e(){var t;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,x();case 2:return t=e.sent,e.abrupt("return",{conversionLabels:t});case 4:case"end":return e.stop()}}),e)}))),function(){return s.apply(this,arguments)}),getPageViewConversionSetting:(o=c()(a.a.mark((function e(){var t;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,E();case 2:return t=e.sent,e.abrupt("return",{websitePages:t});case 4:case"end":return e.stop()}}),e)}))),function(){return o.apply(this,arguments)}),getSupportedConversionTrackingTypes:(i=c()(a.a.mark((function e(){var t,n;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,x();case 2:return t=e.sent,n=["TYPE_PAGE_VIEW"],t.length>0&&n.push("TYPE_CONVERSION_EVENT"),e.abrupt("return",{conversionTrackingTypes:n});case 6:case"end":return e.stop()}}),e)}))),function(){return i.apply(this,arguments)})},termsAndConditionsService:{notify:(r=c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{});case 1:case"end":return e.stop()}}),e)}))),function(){return r.apply(this,arguments)})},partnerDateRangeService:{get:(n=c()(a.a.mark((function t(){var n,r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.select(v.a).getDateRangeDates({offsetDays:g.d}),r=n.startDate,i=n.endDate,t.abrupt("return",{startDate:m(r),endDate:m(i)});case 2:case"end":return t.stop()}}),t)}))),function(){return n.apply(this,arguments)})},userActionService:{finishAndCloseSignUpFlow:(t=c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!S){e.next=3;break}return e.next=3,S();case 3:return e.abrupt("return",{});case 4:case"end":return e.stop()}}),e)}))),function(){return t.apply(this,arguments)})}};return A}},736:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsForm}));var r=n(2),i=n(0),a=n(38),o=n(3),c=n(94),s=n(657),l=n(141),u=n(525),d=n(367),f=n(182),g=n(13),p=n(20),m=n(659),v=n(660);function SettingsForm(){var t=Object(d.a)("adsPax"),n=Object(d.a)("firstPartyMode"),b=Object(o.useSelect)((function(e){return e(c.e).getConversionID()})),h=Object(o.useSelect)((function(e){return e(c.e).getPaxConversionID()})),E=Object(o.useSelect)((function(e){return e(c.e).getExtCustomerID()})),y=Object(o.useSelect)((function(e){return e(g.c).getDocumentationLinkURL("enhanced-conversion-tracking")})),O=t&&h?h:b,k=t&&(h||E);return e.createElement(i.Fragment,null,e.createElement("div",{className:"googlesitekit-ads-settings-fields"},e.createElement(l.a,{moduleSlug:"ads",storeName:c.e}),!k&&e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(u.a,{helperText:Object(r.__)("The Conversion ID will help track the performance of ad campaigns for the corresponding account","google-site-kit")})),k&&e.createElement("div",null,e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Conversion ID","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},""===O&&Object(r.__)("None","google-site-kit"),O||void 0===O&&e.createElement(f.b,{value:O}))),e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Customer ID","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},""===E&&Object(r.__)("None","google-site-kit"),E||void 0===E&&e.createElement(f.b,{value:E})))),e.createElement(m.a,{title:Object(r.__)("Improve your measurement","google-site-kit")},e.createElement(s.a,null,Object(a.a)(Object(r.__)("To track the performance of your campaigns, Site Kit will enable enhanced conversion tracking. <a>Learn more</a>","google-site-kit"),{a:e.createElement(p.a,{href:y,external:!0,"aria-label":Object(r.__)("Learn more about conversion tracking","google-site-kit")})})),n&&e.createElement(v.a,null))))}}).call(this,n(4))},737:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return PAXEmbeddedApp}));var i=n(5),a=n.n(i),o=n(16),c=n.n(o),s=n(15),l=n.n(s),u=n(1),d=n.n(u),f=n(421),g=n(208),p=n(0),m=n(2),v=n(3),b=n(44),h=n(95),E=n(7),y=n(8),O=n(732);function PAXEmbeddedApp(t){var n,i,o,s,u,d=t.displayMode,k=void 0===d?"default":d,_=t.onLaunch,S=t.onCampaignCreated,j=t.onFinishAndCloseSignUpFlow,w=Object(p.useState)("function"==typeof(null===(n=e)||void 0===n||null===(i=n.google)||void 0===i||null===(o=i.ads)||void 0===o||null===(s=o.integration)||void 0===s||null===(u=s.integrator)||void 0===u?void 0:u.launchGoogleAds)),C=l()(w,2),x=C[0],A=C[1],T=Object(p.useState)(!1),N=l()(T,2),D=N[0],L=N[1],R=Object(p.useState)(!0),P=l()(R,2),M=P[0],I=P[1],F=Object(p.useState)(void 0),B=l()(F,2),U=B[0],W=B[1],G=Object(v.useSelect)((function(e){return"reporting"!==k?{}:e(E.a).getDateRangeDates({offsetDays:y.g})})),z=Object(v.useSelect)((function(e){return e(E.a).isAdBlockerActive()})),V=Object(g.a)(PAXEmbeddedApp,"PAXEmbeddedApp"),q="googlesitekit-pax-embedded-app-".concat(V),H=Object(p.useRef)(),K=Object(p.useCallback)((function(){"reporting"===k&&(null==H?void 0:H.current)&&G.startDate&&G.endDate&&H.current.getServices().adsDateRangeService.update({startDate:Object(O.c)(G.startDate),endDate:Object(O.c)(G.endDate)})}),[k,G.endDate,G.startDate]),Y=Object(v.useRegistry)(),X=Object(p.useCallback)(c()(a.a.mark((function t(){var n,r;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!D){t.next=2;break}return t.abrupt("return");case 2:return L(!0),n=Object(O.a)({contentContainer:"#".concat(q),reportingStyle:"reporting"===k?"REPORTING_STYLE_MINI":"REPORTING_STYLE_FULL"}),r=Object(O.b)(Y,{onCampaignCreated:S,onFinishAndCloseSignUpFlow:j}),t.prev=5,t.next=8,e.google.ads.integration.integrator.launchGoogleAds(n,r);case 8:H.current=t.sent,K(),null==_||_(H.current),t.next=17;break;case 13:t.prev=13,t.t0=t.catch(5),W(t.t0),e.console.error("Google Ads Partner Experience Error:",t.t0);case 17:I(!1);case 18:case"end":return t.stop()}}),t,null,[[5,13]])}))),[k,q,D,S,j,_,Y,K]);return Object(f.a)((function(){var t,n,r,i,a;x||D||"function"==typeof(null===(t=e)||void 0===t||null===(n=t.google)||void 0===n||null===(r=n.ads)||void 0===r||null===(i=r.integration)||void 0===i||null===(a=i.integrator)||void 0===a?void 0:a.launchGoogleAds)&&A(!0)}),D?null:50),Object(p.useEffect)((function(){x&&!D&&X()}),[D,M,x,X]),Object(p.useEffect)((function(){K()}),[K,G.startDate,G.endDate]),r.createElement("div",{className:"googlesitekit-pax-embedded-app"},!!U&&!z&&r.createElement(h.a,{title:Object(m.__)("Google Ads error","google-site-kit"),description:Object(m.__)("Could not load Google Ads content.","google-site-kit"),error:!0}),M&&r.createElement(b.a,{width:"100%",height:"240px"}),r.createElement("div",{id:q}))}PAXEmbeddedApp.propTypes={displayMode:d.a.oneOf(["default","reporting","setup"]),onLaunch:d.a.func,onCampaignCreated:d.a.func,onFinishAndCloseSignUpFlow:d.a.func}}).call(this,n(28),n(4))},738:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupForm}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(1),s=n.n(c),l=n(0),u=n(2),d=n(3),f=n(10),g=n(94),p=n(141),m=n(525),v=n(13),b=n(414);function SetupForm(t){var n=t.finishSetup,r=t.createAccountCTA,a=t.isNavigatingToOAuthURL,c=Object(d.useSelect)((function(e){return e(g.e).canSubmitChanges()})),s=Object(d.useSelect)((function(e){return e(g.e).isDoingSubmitChanges()&&!a})),h=Object(d.useDispatch)(g.e).submitChanges,E=Object(d.useDispatch)(v.c),y=E.setConversionTrackingEnabled,O=E.saveConversionTrackingSettings,k=Object(l.useCallback)(function(){var e=o()(i.a.mark((function e(t){var r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.preventDefault(),e.next=3,h();case 3:if(r=e.sent,r.error){e.next=10;break}return y(!0),e.next=9,O();case 9:n();case 10:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[n,O,y,h]);return e.createElement("form",{className:"googlesitekit-ads-setup__form",onSubmit:k},e.createElement(p.a,{moduleSlug:"ads",storeName:g.e}),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(m.a,null)),r&&e.createElement("div",{className:"googlesitekit-setup-module__create-account"},r),e.createElement(b.a,{className:"googlesitekit-margin-top-1",message:Object(u.__)("To track the performance of your campaigns, Site Kit will enable enhanced conversion tracking. You can always disable it in settings.","google-site-kit")}),e.createElement("div",{className:"googlesitekit-setup-module__action"},e.createElement(f.SpinnerButton,{disabled:!c||s,isSaving:s},Object(u.__)("Complete setup","google-site-kit"))))}SetupForm.propTypes={finishSetup:s.a.func,createAccountCTA:s.a.node,isNavigatingToOAuthURL:s.a.bool},SetupForm.defaultProps={finishSetup:function(){},createAccountCTA:null,isNavigatingToOAuthURL:!1}}).call(this,n(4))},739:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupFormPAX}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(1),s=n.n(c),l=n(0),u=n(2),d=n(3),f=n(10),g=n(94),p=n(141),m=n(525),v=n(13),b=n(217);function SetupFormPAX(t){var n=t.finishSetup,r=t.isNavigatingToOAuthURL,a=Object(d.useSelect)((function(e){return e(g.e).canSubmitChanges()})),c=Object(d.useSelect)((function(e){return e(g.e).isDoingSubmitChanges()&&!r})),s=Object(d.useDispatch)(g.e).submitChanges,h=Object(d.useDispatch)(v.c),E=h.setConversionTrackingEnabled,y=h.saveConversionTrackingSettings,O=Object(d.useSelect)((function(e){return e(g.e).getConversionID()})),k=Object(d.useSelect)((function(e){return e(g.e).getGoogleForWooCommerceConversionID()})),_=!!O&&O===k,S=Object(l.useCallback)(function(){var e=o()(i.a.mark((function e(t){var r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.preventDefault(),e.next=3,s();case 3:if(r=e.sent,r.error){e.next=10;break}return E(!0),e.next=9,y();case 9:n();case 10:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[n,y,E,s]);return e.createElement("div",{className:"googlesitekit-ads-setup__form googlesitekit-ads-setup__form--pax"},e.createElement(p.a,{moduleSlug:"ads",storeName:g.e}),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(m.a,{hideHeading:!0})),_&&e.createElement(b.a,{className:"googlesitekit-ads-setup__ads-id-conflict-warning"},Object(u.__)("This Conversion ID is already in use via the Google for WooCommerce plugin. We don’t recommend adding it in Site Kit, as it may result in inaccurate measurement of your Ads campaign conversions.","google-site-kit")),e.createElement("div",{className:"googlesitekit-setup-module__action"},e.createElement(f.SpinnerButton,{disabled:!a||c||_,isSaving:c,onClick:S},Object(u.__)("Complete manual setup","google-site-kit"))))}SetupFormPAX.propTypes={finishSetup:s.a.func,isNavigatingToOAuthURL:s.a.bool}}).call(this,n(4))},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(33),i=n.n(r),a=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,n="object"===i()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(1),i=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,i=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:i}},n)}IconWrapper.propTypes={children:i.a.node.isRequired,marginLeft:i.a.number,marginRight:i.a.number}}).call(this,n(4))},77:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.label,a=t.className,c=t.hasLeftSpacing,l=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",i()({ref:n},u,{className:s()("googlesitekit-badge",a,{"googlesitekit-badge--has-left-spacing":l})}),r)}));f.displayName="Badge",f.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=f}).call(this,n(4))},79:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(15),i=n.n(r),a=n(188),o=n(133),c={},s=void 0===e?null:e,l=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,u=void 0===r?0:r,d=e.initialHeight,f=void 0===d?0:d,g=Object(a.a)("undefined"==typeof document?[u,f]:l,t,n),p=i()(g,2),m=p[0],v=p[1],b=function(){return v(l)};return Object(o.a)(s,"resize",b),Object(o.a)(s,"orientationchange",b),m},d=function(e){return u(e)[0]}}).call(this,n(28))},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"s",(function(){return a})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return s})),n.d(t,"g",(function(){return l})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return f})),n.d(t,"k",(function(){return g})),n.d(t,"m",(function(){return p})),n.d(t,"n",(function(){return m})),n.d(t,"h",(function(){return v})),n.d(t,"x",(function(){return b})),n.d(t,"w",(function(){return h})),n.d(t,"y",(function(){return E})),n.d(t,"u",(function(){return y})),n.d(t,"v",(function(){return O})),n.d(t,"f",(function(){return k})),n.d(t,"l",(function(){return _})),n.d(t,"e",(function(){return S})),n.d(t,"t",(function(){return j})),n.d(t,"c",(function(){return w})),n.d(t,"d",(function(){return C})),n.d(t,"b",(function(){return x}));var r="modules/analytics-4",i="account_create",a="property_create",o="webdatastream_create",c="analyticsSetup",s=10,l=1,u="https://www.googleapis.com/auth/tagmanager.readonly",d="enhanced-measurement-form",f="enhanced-measurement-enabled",g="enhanced-measurement-should-dismiss-activation-banner",p="analyticsAccountCreate",m="analyticsCustomDimensionsCreate",v="https://www.googleapis.com/auth/analytics.edit",b="dashboardAllTrafficWidgetDimensionName",h="dashboardAllTrafficWidgetDimensionColor",E="dashboardAllTrafficWidgetDimensionValue",y="dashboardAllTrafficWidgetActiveRowIndex",O="dashboardAllTrafficWidgetLoaded",k={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},_={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},S=[_.CONTACT,_.GENERATE_LEAD,_.SUBMIT_LEAD_FORM],j={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},w="audiencePermissionsSetup",C="audienceTileCustomDimensionCreate",x="audience-selection-panel-expirable-new-badge-"},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(106);function i(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),i=e.replace(n.origin,"");if(i.length<t)return i;var a=i.length-Math.floor(t)+1;return"…"+i.substr(a)}},82:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return a}));var r=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},i=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function a(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return _})),n.d(t,"d",(function(){return S})),n.d(t,"e",(function(){return w})),n.d(t,"c",(function(){return C})),n.d(t,"b",(function(){return x}));var r=n(15),i=n.n(r),a=n(33),o=n.n(a),c=n(6),s=n.n(c),l=n(25),u=n.n(l),d=n(14),f=n(63),g=n.n(f),p=n(2);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var b=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=y(e,t),r=n.formatUnit,i=n.formatDecimal;try{return r()}catch(e){return i()}},h=function(e){var t=E(e),n=t.hours,r=t.minutes,i=t.seconds;return i=("0"+i).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(i):"".concat(n,":").concat(r,":").concat(i)},E=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},y=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=E(e),r=n.hours,i=n.minutes,a=n.seconds;return{hours:r,minutes:i,seconds:a,formatUnit:function(){var n=t.unitDisplay,o=v(v({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(a,v(v({},o),{},{unit:"second"})):Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?w(a,v(v({},o),{},{unit:"second"})):"",i?w(i,v(v({},o),{},{unit:"minute"})):"",r?w(r,v(v({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(p.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(p.__)("%ds","google-site-kit"),a);if(0===e)return t;var n=Object(p.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(p.__)("%dm","google-site-kit"),i),o=Object(p.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(p.__)("%dh","google-site-kit"),r);return Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",i?n:"",r?o:"").trim()}}},O=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},k=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in millions.
Object(p.__)("%sM","google-site-kit"),w(O(e),e%10==0?{}:t)):1e4<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(O(e))):1e3<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(O(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function _(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=v({},e)),t}function S(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=_(t),r=n.style,i=void 0===r?"metric":r;return"metric"===i?k(e):"duration"===i?b(e,n):"durationISO"===i?h(e):w(e,n)}var j=g()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?x():n,a=u()(t,["locale"]);try{return new Intl.NumberFormat(r,a).format(e)}catch(t){j("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],l={},d=0,f=Object.entries(a);d<f.length;d++){var g=i()(f[d],2),p=g[0],m=g[1];c[p]&&m===c[p]||(s.includes(p)||(l[p]=m))}try{return new Intl.NumberFormat(r,l).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},C=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?x():n,i=t.style,a=void 0===i?"long":i,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(r,{style:a,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var l=Object(p.__)(", ","google-site-kit");return e.join(l)},x=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i}));var r=n(149),i=n.n(r)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a);function ChangeArrow(t){var n=t.direction,r=t.invertColor,i=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:i,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:i.a.string,invertColor:i.a.bool,width:i.a.number,height:i.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},856:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsView}));var r=n(11),i=n.n(r),a=n(2),o=n(3),c=n(94),s=n(13),l=n(7),u=n(182),d=n(245),f=n(367),g=n(656);function SettingsView(){var t=Object(f.a)("adsPax"),n=Object(f.a)("firstPartyMode"),r=Object(o.useSelect)((function(e){return e(c.e).getConversionID()})),p=Object(o.useSelect)((function(e){return e(c.e).getPaxConversionID()})),m=Object(o.useSelect)((function(e){return e(c.e).getExtCustomerID()})),v=Object(o.useSelect)((function(e){return e(l.a).isAdBlockerActive()})),b=t&&p?p:r,h=t&&(p||m),E=Object(o.useSelect)((function(e){return e(s.c).isConversionTrackingEnabled()})),y=Object(o.useSelect)((function(e){if(!n)return!1;var t=e(s.c),r=t.isFirstPartyModeEnabled,i=t.isFPMHealthy,a=t.isScriptAccessEnabled;return r()&&i()&&a()}));return e.createElement("div",{className:"googlesitekit-setup-module"},e.createElement("div",{className:i()({"googlesitekit-settings-module__meta-item":v})},e.createElement(d.a,{moduleSlug:"ads"})),!v&&e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(a.__)("Conversion ID","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},""===b&&Object(a.__)("None","google-site-kit"),b||void 0===b&&e.createElement(u.b,{value:b}))),!v&&h&&e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(a.__)("Customer ID","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},""===m&&Object(a.__)("None","google-site-kit"),m||void 0===m&&e.createElement(u.b,{value:m}))),e.createElement(g.a,{statuses:n?[{label:Object(a.__)("Enhanced Conversion Tracking","google-site-kit"),status:E},{label:Object(a.__)("First-party mode","google-site-kit"),status:y}]:[{label:Object(a.__)("Conversion Tracking","google-site-kit"),status:E}]}))}}).call(this,n(4))},857:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConversionIDTextField}));var r=n(15),i=n.n(r),a=n(11),o=n.n(a),c=n(0),s=n(2),l=n(121),u=n(3),d=n(10),f=n(94),g=n(105),p=n(548),m=n(364);function ConversionIDTextField(t){var n=t.helperText,r=t.hideHeading,a=void 0!==r&&r,v=Object(u.useSelect)((function(e){return e(f.e).getConversionID()})),b=Object(c.useState)(!v||Object(p.a)(v)),h=i()(b,2),E=h[0],y=h[1],O=Object(l.a)(y,500),k=Object(u.useDispatch)(f.e).setConversionID,_=Object(c.useCallback)((function(e){var t=e.currentTarget.value.trim().toUpperCase();""===t||/^AW-/.test(t)||(t="AW-".concat(t)),t!==v&&k(t),O(Object(p.a)(t))}),[O,v,k]);return e.createElement("div",{className:"googlesitekit-settings-module__fields-group"},!a&&e.createElement("h4",{className:"googlesitekit-settings-module__fields-group-title"},Object(s.__)("Conversion ID","google-site-kit")),n&&e.createElement("p",{className:"googlesitekit-settings-module__fields-group-helper-text"},n),e.createElement(d.TextField,{label:Object(s.__)("Conversion ID","google-site-kit"),className:o()("googlesitekit-text-field-conversion-tracking-id",{"mdc-text-field--error":!E}),helperText:!E&&Object(s.__)("Tracking for your Ads campaigns won’t work until you insert a valid ID","google-site-kit"),leadingIcon:e.createElement("span",{className:"googlesitekit-text-field-conversion-tracking-id-prefix"},"AW-"),trailingIcon:!E&&e.createElement("span",{className:"googlesitekit-text-field-icon--error"},e.createElement(g.a,null,Object(s.__)("Error","google-site-kit")),e.createElement(m.a,{width:14,height:12})),outlined:!0,value:null==v?void 0:v.replace(/^(AW)?-?/,""),onChange:_,maxLength:20}))}}).call(this,n(4))},858:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return c}));var r=n(12),i=n.n(r),a=n(14),o=n(408);function c(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.contentContainer,r=t.reportingStyle,c=t._global,s=void 0===c?e:c,l=null==s?void 0:s[o.a];return i()(Object(a.isPlainObject)(l),"base PAX config must be a plain object"),Object(a.merge)(l,n?{clientConfig:{contentContainer:n}}:{},r?{contentConfig:{partnerAdsExperienceConfig:{reportingStyle:r}}}:{})}}).call(this,n(28))},859:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WooCommerceRedirectModal}));var r=n(15),i=n.n(r),a=n(1),o=n.n(a),c=n(11),s=n.n(c),l=n(2),u=n(0),d=n(157),f=n(3),g=n(10),p=n(94),m=n(13),v=n(32),b=n(41),h=n(1030),E=n(70),y=n(159),O=n(7);function WooCommerceRedirectModal(t){var n=t.dialogActive,r=t.onClose,a=t.onDismiss,o=void 0===a?null:a,c=t.onContinue,k=void 0===c?null:c,_=t.onBeforeSetupCallback,S=void 0===_?null:_,j=Object(u.useState)(""),w=i()(j,2),C=w[0],x=w[1],A=Object(f.useSelect)((function(e){return e(m.c).getAdminURL()})),T=Object(f.useSelect)((function(e){return e(p.e).isWooCommerceActivated()})),N=Object(f.useSelect)((function(e){return e(p.e).isGoogleForWooCommerceActivated()})),D=Object(f.useSelect)((function(e){var t=e(p.e).hasGoogleForWooCommerceAdsAccount();return!!(T&&N&&t)})),L=Object(f.useSelect)((function(e){return e(p.e).isWooCommerceRedirectModalDismissed()})),R=Object(f.useSelect)((function(e){return e(O.a).isItemDismissed("account-linked-via-google-for-woocommerce")})),P=Object(u.useMemo)((function(){if(A&&T){if(!1===N)return Object(d.a)("".concat(A,"/plugin-install.php"),{s:p.f.GOOGLE_FOR_WOOCOMMERCE,tab:"search",type:"term"});var e=encodeURIComponent("/google/dashboard");return"".concat(A,"/admin.php?page=wc-admin&path=").concat(e)}}),[A,T,N]),M=Object(f.useDispatch)(v.a).navigateTo,I=Object(f.useDispatch)(b.a).dismissNotification,F=Object(u.useCallback)((function(){R||I("account-linked-via-google-for-woocommerce"),x("primary"),null==o||o(),M(P)}),[R,I,x,o,M,P]),B=Object(y.a)("ads"),U=Object(u.useCallback)((function(){if(k||(x("tertiary"),null==o||o()),k)return r(),void k();null==S||S(),B()}),[x,o,r,S,B,k]);return L&&!C?null:e.createElement(g.Dialog,{className:s()("googlesitekit-dialog-woocommerce-redirect",{"googlesitekit-dialog-woocommerce-redirect--ads-connected":D}),open:n,"aria-describedby":void 0,tabIndex:"-1",onClose:r},e.createElement("div",{className:"googlesitekit-dialog-woocommerce-redirect__svg-wrapper"},e.createElement(h.a,{width:110,height:46})),e.createElement(g.DialogTitle,null,D?Object(l.__)("Are you sure you want to create another Ads account for this site?","google-site-kit"):Object(l.__)("Using the WooCommerce plugin?","google-site-kit")),e.createElement(g.DialogContent,null,e.createElement("p",null,D?e.createElement(u.Fragment,null,Object(l.__)("Site Kit has detected an already existing Ads account connected to this site via the Google for WooCommerce extension.","google-site-kit"),e.createElement("br",null),Object(l.__)("Continue Ads setup with Site Kit only if you do want to create another account.","google-site-kit")):Object(l.__)("The Google for WooCommerce plugin can utilize your provided business information for advertising on Google and may be more suitable for your business.","google-site-kit"))),e.createElement(g.DialogFooter,null,e.createElement(g.Button,{className:"mdc-dialog__cancel-button",onClick:U,icon:"tertiary"===C?e.createElement(g.CircularProgress,{size:14}):void 0,tertiary:!0},D?Object(l.__)("Create another account","google-site-kit"):Object(l.__)("Continue with Site Kit","google-site-kit")),e.createElement(g.Button,{trailingIcon:D?void 0:e.createElement(E.a,{width:13,height:13}),icon:"primary"===C?e.createElement(g.CircularProgress,{size:14}):void 0,onClick:function(){D||T?F():r()},href:D?null:P,target:D?"_self":"_blank",tertiary:!0},D?Object(l.__)("View current Ads account","google-site-kit"):Object(l.__)("Use Google for WooCommerce","google-site-kit"))))}WooCommerceRedirectModal.propTypes={dialogActive:o.a.bool.isRequired,onDismiss:o.a.func,onClose:o.a.func.isRequired,onContinue:o.a.func,onBeforeSetupCallback:o.a.func}}).call(this,n(4))},860:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsEdit}));var r=n(3),i=n(10),a=n(94),o=n(7),c=n(736),s=n(245);function SettingsEdit(){var t,n=Object(r.useSelect)((function(e){return e(a.e).isDoingSubmitChanges()}));return t=Object(r.useSelect)((function(e){return e(o.a).isAdBlockerActive()}))?e.createElement(s.a,{moduleSlug:"ads"}):n?e.createElement(i.ProgressBar,null):e.createElement(c.a,null),e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--ads"},t)}}).call(this,n(4))},861:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupMain}));var r=n(1),i=n.n(r),a=n(0),o=n(38),c=n(2),s=n(3),l=n(619),u=n(738),d=n(199),f=n(245),g=n(7);function SetupMain(t){var n=t.finishSetup,r=Object(s.useSelect)((function(e){return e(g.a).isAdBlockerActive()}));return e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--ads"},e.createElement("div",{className:"googlesitekit-setup-module__step"},e.createElement("div",{className:"googlesitekit-setup-module__logo"},e.createElement(l.a,{width:"40",height:"40"})),e.createElement("h2",{className:"googlesitekit-heading-3 googlesitekit-setup-module__title"},Object(c._x)("Ads","Service name","google-site-kit"))),e.createElement("div",{className:"googlesitekit-setup-module__step"},e.createElement(f.a,{moduleSlug:"ads"}),!r&&e.createElement(a.Fragment,null,e.createElement("p",null,Object(o.a)(Object(c.__)("Add your conversion ID below. Site Kit will place it on your site so you can track the performance of your Google Ads campaigns. <a>Learn more</a>","google-site-kit"),{a:e.createElement(d.a,{path:"/google-ads/thread/108976144/where-i-can-find-google-conversion-id-begins-with-aw",external:!0})}),e.createElement("br",null),Object(c.__)("You can always change this later in Site Kit Settings.","google-site-kit")),e.createElement(u.a,{finishSetup:n}))))}SetupMain.propTypes={finishSetup:i.a.func},SetupMain.defaultProps={finishSetup:function(){}}}).call(this,n(4))},862:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return SetupMainPAX}));var i=n(5),a=n.n(i),o=n(16),c=n.n(o),s=n(6),l=n.n(s),u=n(15),d=n.n(u),f=n(11),g=n.n(f),p=n(219),m=n(81),v=n(0),b=n(38),h=n(2),E=n(157),y=n(3),O=n(10),k=n(619),_=n(739),S=n(245),j=n(7),w=n(32),C=n(94),x=n(147),A=n(737),T=n(13),N=n(408),D=n(17),L=n(525),R=n(20);function SetupMainPAX(t){var n=t.finishSetup,i=Object(v.useState)(!1),o=d()(i,2),s=o[0],u=o[1],f=Object(x.a)(N.b),P=d()(f,2),M=P[0],I=P[1],F=!!M&&parseInt(M,10),B=Object(v.useRef)(),U=Object(y.useSelect)((function(e){return e(j.a).isAdBlockerActive()})),W=Object(y.useSelect)((function(e){return e(j.a).hasScope(C.b)})),G=Object(y.useSelect)((function(t){var n=Object(E.a)(e.location.href,l()({},N.b,N.c.LAUNCH));return t(j.a).getConnectURL({additionalScopes:[C.b,C.g],redirectURL:n})})),z=Object(y.useSelect)((function(e){return!!G&&e(w.a).isNavigatingTo(G)})),V=Object(y.useDispatch)(w.a).navigateTo,q=Object(y.useDispatch)(C.e),H=q.setPaxConversionID,K=q.setCustomerID,Y=q.setExtCustomerID,X=q.setFormattedExtCustomerID,$=q.setUserID,J=q.setAccountOverviewURL,Q=q.submitChanges;Object(m.a)((function(){N.c.FINISHED===F&&I(N.c.LAUNCH)}));var Z=Object(y.useDispatch)(T.c),ee=Z.setConversionTrackingEnabled,te=Z.saveConversionTrackingSettings,ne=Object(p.a)(c()(a.a.mark((function e(){var t,n,r,i,o,c,s;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null==B?void 0:B.current){e.next=2;break}return e.abrupt("return");case 2:return t=B.current.getServices(),n=t.accountService,r=t.conversionTrackingIdService,e.next=5,n.getAccountId({});case 5:return i=e.sent,e.next=8,n.getGoogleAdsUrl({});case 8:return o=e.sent,e.next=11,r.getConversionTrackingId({});case 11:if(c=e.sent,i.externalCustomerId||c.conversionTrackingId){e.next=14;break}return e.abrupt("return");case 14:return $(i.userId),K(i.customerId),Y(i.externalCustomerId),X(i.formattedExternalCustomerId),H(c.conversionTrackingId),J(o.accountOverviewUrl),e.next=22,Q();case 22:if(s=e.sent,s.error){e.next=28;break}return ee(!0),e.next=28,te();case 28:case"end":return e.stop()}}),e)}))),[Y,H]),re=Object(y.useRegistry)(),ie=Object(p.a)(c()(a.a.mark((function e(){var t,r,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=re.select,r=re.resolveSelect,e.next=3,r(T.c).getSiteInfo();case 3:i=t(T.c).getAdminURL("googlesitekit-dashboard",{notification:N.d}),n(i);case 5:case"end":return e.stop()}}),e)}))),[re,n]),ae=Object(y.useSelect)((function(e){return e(C.e).isWooCommerceRedirectModalDismissed()})),oe=Object(y.useSelect)((function(e){return e(C.e).isWooCommerceActivated()})),ce=Object(v.useCallback)((function(){W?I(N.c.LAUNCH):V(G)}),[V,I,W,G]),se=Object(v.useCallback)((function(e){B.current=e}),[]),le=Object(v.useCallback)((function(){!oe||ae?ce():u(!0)}),[oe,ae,u,ce]),ue=Object(y.useSelect)((function(e){return e(T.c).getDocumentationLinkURL("ads-set-up-a-new-ads-account")})),de=Object(y.useSelect)((function(e){return e(T.c).getDocumentationLinkURL("ads-connect-an-existing-ads-account")}));return r.createElement("div",{className:g()("googlesitekit-setup-module","googlesitekit-setup-module--ads",{"has-pax-flow":!U&&N.c.LAUNCH===F&&W})},r.createElement("div",{className:"googlesitekit-setup-module__step"},r.createElement("div",{className:"googlesitekit-setup-module__logo"},r.createElement(k.a,{width:"40",height:"40"})),r.createElement("h2",{className:"googlesitekit-heading-3 googlesitekit-setup-module__title"},Object(h._x)("Ads","Service name","google-site-kit"))),r.createElement("div",{className:"googlesitekit-setup-module__step"},r.createElement(S.a,{moduleSlug:"ads"}),!U&&N.c.LAUNCH===F&&W&&r.createElement(D.k,null,r.createElement(D.a,{mdSize:12,lgSize:12},r.createElement(A.a,{displayMode:"setup",onLaunch:se,onCampaignCreated:ne,onFinishAndCloseSignUpFlow:ie}))),!U&&(!F||!W)&&r.createElement(v.Fragment,null,r.createElement(D.k,{className:"googlesitekit-setup-module--ads--setup-container"},r.createElement(D.a,{smSize:8,mdSize:8,lgSize:5,className:"align-top"},r.createElement("h3",null,Object(h.__)("Set up a new Ads account","google-site-kit")),r.createElement("p",{className:"instructions"},Object(b.a)(Object(h.__)("Create your first Ads campaign, add billing information, and choose your conversion goals. To create a new Ads account, you’ll need to grant Site Kit additional permissions during the account creation process. <a>Learn more</a>","google-site-kit"),{a:r.createElement(R.a,{href:ue,external:!0})})),r.createElement(v.Fragment,null,r.createElement(O.SpinnerButton,{onClick:le,disabled:z,isSaving:z},Object(h.__)("Start setup","google-site-kit")))),r.createElement(D.a,{className:"divider",smSize:8,mdSize:8,lgSize:2},r.createElement("span",{className:"divider-line"}),r.createElement("span",{className:"divider-label"},Object(h.__)("OR","google-site-kit"))),r.createElement(D.a,{smSize:8,mdSize:8,lgSize:5},r.createElement("h3",null,Object(h.__)("Connect an existing Ads account","google-site-kit")),r.createElement("p",{className:"instructions"},Object(b.a)(Object(h.__)("To track conversions for your Ads campaign, you need to add your Conversion ID to Site Kit. You can always change the Conversion ID later in Site Kit Settings. <a>Learn more</a>","google-site-kit"),{a:r.createElement(R.a,{href:de,external:!0}),br:r.createElement("br",null)})),r.createElement(_.a,{finishSetup:n,isNavigatingToOAuthURL:z}))))),s&&r.createElement(L.b,{onClose:function(){return u(!1)},onContinue:ce,dialogActive:!0}))}SetupMainPAX.defaultProps={finishSetup:function(){}}}).call(this,n(28),n(4))},863:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PAXSetupSuccessSubtleNotification}));var r=n(15),i=n.n(r),a=n(1),o=n.n(a),c=n(2),s=n(356),l=n(0),u=n(3),d=n(41),f=n(94),g=n(7),p=n(115),m=n(147),v=n(92),b=n(168);function PAXSetupSuccessSubtleNotification(t){var n=t.id,r=t.Notification,a=Object(u.useDispatch)(d.a).dismissNotification,o=Object(m.a)("notification"),h=i()(o,2)[1],E=Object(l.useCallback)((function(){h(void 0)}),[h]),y=Object(s.a)((function(e){var t=e(f.e).getAccountOverviewURL();if(t)return e(g.a).getAccountChooserURL(t)})),O=Object(l.useCallback)((function(){E(),a(n)}),[E,a,n]);return e.createElement(r,null,e.createElement(p.a,{title:Object(c.__)("Your Ads campaign was successfully set up!","google-site-kit"),description:Object(c.__)("Track your conversions, measure your campaign results and make the most of your ad spend","google-site-kit"),dismissCTA:e.createElement(v.a,{id:n,primary:!1,dismissLabel:Object(c.__)("Got it","google-site-kit"),onDismiss:E}),additionalCTA:e.createElement(b.a,{id:n,ctaLabel:Object(c.__)("Show me","google-site-kit"),ctaLink:y,onCTAClick:O,isCTALinkExternal:!0})}))}PAXSetupSuccessSubtleNotification.propTypes={id:o.a.string.isRequired,Notification:o.a.elementType.isRequired}}).call(this,n(4))},864:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupSuccessSubtleNotification}));var r=n(15),i=n.n(r),a=n(2),o=n(115),c=n(92),s=n(147);function SetupSuccessSubtleNotification(t){var n=t.id,r=t.Notification,l=Object(s.a)("notification"),u=i()(l,2)[1],d=Object(s.a)("slug"),f=i()(d,2)[1];return e.createElement(r,null,e.createElement(o.a,{title:Object(a.__)("Success! Your Conversion ID was added to your site","google-site-kit"),description:Object(a.__)("You can now track conversions for your Ads campaigns","google-site-kit"),dismissCTA:e.createElement(c.a,{id:n,primary:!1,dismissLabel:Object(a.__)("Got it","google-site-kit"),onDismiss:function(){u(void 0),f(void 0)}})}))}}).call(this,n(4))},865:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccountLinkedViaGoogleForWooCommerceSubtleNotification}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(15),s=n.n(c),l=n(1),u=n.n(l),d=n(2),f=n(0),g=n(3),p=n(41),m=n(13),v=n(94),b=n(9),h=n(115),E=n(92),y=n(168),O=n(159);function AccountLinkedViaGoogleForWooCommerceSubtleNotification(t){var n=t.id,r=t.Notification,a=Object(f.useState)(!1),c=s()(a,2),l=c[0],u=c[1],k=Object(O.a)("ads"),_=Object(g.useDispatch)(p.a).dismissNotification,S=Object(g.useDispatch)(m.c).setCacheItem,j=Object(f.useCallback)(o()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S(v.a,!0,{ttl:5*b.e});case 2:case"end":return e.stop()}}),e)}))),[S]),w=Object(f.useCallback)(o()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return u(!0),e.next=3,_(n,{skipHidingFromQueue:!0});case 3:return e.next=5,j();case 5:k();case 6:case"end":return e.stop()}}),e)}))),[u,k,j,_,n]);return e.createElement(r,null,e.createElement(h.a,{type:"new-feature",description:Object(d.__)("We’ve detected an existing Ads account via the Google for WooCommerce plugin. You can still create a new Ads account using Site Kit.","google-site-kit"),dismissCTA:e.createElement(E.a,{id:n,dismissLabel:Object(d.__)("Keep existing account","google-site-kit"),onDismiss:j}),additionalCTA:e.createElement(y.a,{id:n,ctaLabel:Object(d.__)("Create new account","google-site-kit"),onCTAClick:w,isSaving:l,tertiary:!0}),reverseCTAs:!0}))}AccountLinkedViaGoogleForWooCommerceSubtleNotification.propTypes={id:u.a.string.isRequired,Notification:u.a.elementType.isRequired}}).call(this,n(4))},866:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdsModuleSetupCTABanner}));var r,i=n(15),a=n.n(i),o=n(6),c=n.n(o),s=n(1),l=n.n(s),u=n(81),d=n(0),f=n(2),g=n(3),p=n(41),m=n(7),v=n(9),b=n(94),h=n(1031),E=n(1032),y=n(1033),O=n(242),k=n(111),_=n(180),S=n(179),j=n(159),w=n(24),C=n(525),x=n(245),A=n(175),T=n(13),N=(r={},c()(r,w.b,y.a),c()(r,w.c,E.a),r);function AdsModuleSetupCTABanner(t){var n=t.id,r=t.Notification,i=Object(w.e)(),o=Object(d.useState)(!1),c=a()(o,2),s=c[0],l=c[1],E=Object(d.useState)(!1),y=a()(E,2),D=y[0],L=y[1],R=Object(g.useSelect)((function(e){return e(T.c).getDocumentationLinkURL("set-up-ads")})),P=Object(g.useSelect)((function(e){return e(m.a).isAdBlockerActive()})),M=Object(g.useSelect)((function(e){return e(p.a).isNotificationDismissalFinal(n)})),I=Object(g.useSelect)((function(e){var t=e(b.e),n=t.isWooCommerceActivated,r=t.isGoogleForWooCommerceActivated,i=t.hasGoogleForWooCommerceAdsAccount;return n()&&r()&&!i()||n()&&!r()})),F=Object(g.useSelect)((function(e){return e(b.e).isWooCommerceRedirectModalDismissed()})),B=Object(g.useDispatch)(p.a).dismissNotification,U=Object(d.useCallback)((function(){B(n,{skipHidingFromQueue:!0,expiresInSeconds:2*v.f})}),[n,B]),W=Object(g.useDispatch)(T.c).setCacheItem,G=Object(d.useCallback)((function(){W(b.a,!0,{ttl:5*v.e})}),[W]),z=Object(j.a)("ads"),V=Object(d.useCallback)((function(){if(!I||F)return L(!0),U(),void z();l(!0)}),[I,z,U,F]),q=Object(d.useCallback)((function(){l(!1)}),[l]),H={tooltipSlug:"ads-setup-notification",content:Object(f.__)("You can always enable Ads in Settings later","google-site-kit"),dismissLabel:Object(f.__)("Got it","google-site-kit")},K=Object(A.b)(H),Y=Object(d.useState)(Object(f.__)("Maybe later","google-site-kit")),X=a()(Y,2),$=X[0],J=X[1];return Object(u.a)((function(){!0===M&&J(Object(f.__)("Don’t show again","google-site-kit"))})),e.createElement(r,null,e.createElement(O.a,{id:n,title:Object(f.__)("Get better quality leads and enhance conversions with Ads","google-site-kit"),description:e.createElement(k.a,{text:Object(f.__)("Help drive sales, leads, or site traffic by getting your business in front of people who are actively searching Google for products or services you offer.","google-site-kit"),learnMoreLink:e.createElement(_.a,{id:n,label:Object(f.__)("Learn more","google-site-kit"),url:R})},P&&e.createElement(x.a,{moduleSlug:"ads"})),actions:e.createElement(S.a,{id:n,className:"googlesitekit-setup-cta-banner__actions-wrapper",ctaLabel:Object(f.__)("Set up Ads","google-site-kit"),onCTAClick:V,dismissOnCTAClick:!1,isSaving:D,dismissLabel:$,ctaDismissOptions:{skipHidingFromQueue:!0},onDismiss:K,dismissExpires:2*v.f,ctaDisabled:P}),SVG:N[i]||h.a}),s&&e.createElement(C.b,{onDismiss:U,onClose:q,onBeforeSetupCallback:G,dialogActive:!0}))}AdsModuleSetupCTABanner.propTypes={id:l.a.string.isRequired,Notification:l.a.elementType.isRequired}}).call(this,n(4))},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(12),i=n.n(r),a=function(e,t){var n=t.dateRangeLength;i()(Array.isArray(e),"report must be an array to partition."),i()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return i.b})),n.d(t,"J",(function(){return i.c})),n.d(t,"F",(function(){return a.a})),n.d(t,"K",(function(){return a.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return m})),n.d(t,"j",(function(){return v})),n.d(t,"i",(function(){return b})),n.d(t,"d",(function(){return _})),n.d(t,"c",(function(){return S})),n.d(t,"e",(function(){return j})),n.d(t,"b",(function(){return w})),n.d(t,"a",(function(){return C})),n.d(t,"f",(function(){return x})),n.d(t,"n",(function(){return A})),n.d(t,"w",(function(){return T})),n.d(t,"p",(function(){return N})),n.d(t,"G",(function(){return D})),n.d(t,"s",(function(){return L})),n.d(t,"v",(function(){return R})),n.d(t,"k",(function(){return P})),n.d(t,"o",(function(){return M.b})),n.d(t,"h",(function(){return M.a})),n.d(t,"t",(function(){return I.b})),n.d(t,"q",(function(){return I.a})),n.d(t,"A",(function(){return I.c})),n.d(t,"x",(function(){return F})),n.d(t,"u",(function(){return B})),n.d(t,"E",(function(){return G})),n.d(t,"D",(function(){return z.a})),n.d(t,"g",(function(){return V})),n.d(t,"L",(function(){return q})),n.d(t,"l",(function(){return H}));var r=n(14),i=n(36),a=n(75),o=n(33),c=n.n(o),s=n(96),l=n.n(s),u=function(e){return l()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var i=t[r];i&&"object"===c()(i)&&!Array.isArray(i)&&(i=e(i)),n[r]=i})),n}(e)))};n(97);var d=n(83);function f(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function g(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function p(e){return e.replace(/\n/gi,"<br>")}function m(e){for(var t=e,n=0,r=[f,g,p];n<r.length;n++){t=(0,r[n])(t)}return t}var v=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},b=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(15),E=n.n(h),y=n(12),O=n.n(y),k=n(2),_="Invalid dateString parameter, it must be a string.",S='Invalid date range, it must be a string with the format "last-x-days".',j=60,w=60*j,C=24*w,x=7*C;function A(){var e=function(e){return Object(k.sprintf)(
/* translators: %s: number of days */
Object(k._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function T(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function N(e){O()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function D(e){O()(T(e),_);var t=e.split("-"),n=E()(t,3),r=n[0],i=n[1],a=n[2];return new Date(r,i-1,a)}function L(e,t){return N(P(e,t*C))}function R(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function P(e,t){O()(T(e)||Object(r.isDate)(e)&&!isNaN(e),_);var n=T(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var M=n(98),I=n(80);function F(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function B(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var U=n(27),W=n.n(U),G=function(e){return Array.isArray(e)?W()(e).sort():e},z=n(89);function V(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var q=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},H=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},90:function(e,t,n){"use strict";(function(e,r){n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return y})),n.d(t,"a",(function(){return TourTooltips}));var i=n(6),a=n.n(i),o=n(81),c=n(30),s=n(1),l=n.n(s),u=n(2),d=n(3),f=n(23),g=n(7),p=n(36),m=n(107),v=n(18);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var h={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},E={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},y={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},O="feature_tooltip_view",k="feature_tooltip_advance",_="feature_tooltip_return",S="feature_tooltip_dismiss",j="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,i=t.tourID,s=t.gaEventCategory,l=t.callback,u="".concat(i,"-step"),w="".concat(i,"-run"),C=Object(d.useDispatch)(f.b).setValue,x=Object(d.useDispatch)(g.a).dismissTour,A=Object(d.useRegistry)(),T=Object(v.a)(),N=Object(d.useSelect)((function(e){return e(f.b).getValue(u)})),D=Object(d.useSelect)((function(e){return e(f.b).getValue(w)&&!1===e(g.a).isTourDismissed(i)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),C(w,!0)}));var L=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return r.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,r=e.lifecycle,i=e.size,a=e.status,o=e.type,l=t+1,u="function"==typeof s?s(T):s;o===c.b.TOOLTIP&&r===c.c.TOOLTIP?Object(p.b)(u,O,l):n===c.a.CLOSE&&r===c.c.COMPLETE?Object(p.b)(u,S,l):n===c.a.NEXT&&a===c.d.FINISHED&&o===c.b.TOUR_END&&i===l&&Object(p.b)(u,j,l),r===c.c.COMPLETE&&a!==c.d.FINISHED&&(n===c.a.PREV&&Object(p.b)(u,_,l),n===c.a.NEXT&&Object(p.b)(u,k,l))}(t);var n=t.action,r=t.index,a=t.status,o=t.step,d=t.type,f=n===c.a.CLOSE,g=!f&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),m=[c.d.FINISHED,c.d.SKIPPED].includes(a),v=f&&d===c.b.STEP_AFTER,b=m||v;if(c.b.STEP_BEFORE===d){var h,E,y=o.target;"string"==typeof o.target&&(y=e.document.querySelector(o.target)),null===(h=y)||void 0===h||null===(E=h.scrollIntoView)||void 0===E||E.call(h,{block:"center"})}g?function(e,t){C(u,e+(t===c.a.PREV?-1:1))}(r,n):b&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),x(i)),l&&l(t,A)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:y,locale:E,run:D,showProgress:!0,stepIndex:N,steps:L,styles:h,tooltipComponent:m.a})}TourTooltips.propTypes={steps:l.a.arrayOf(l.a.object).isRequired,tourID:l.a.string.isRequired,gaEventCategory:l.a.oneOfType([l.a.string,l.a.func]).isRequired,callback:l.a.func}}).call(this,n(28),n(4))},92:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Dismiss}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(1),u=n.n(l),d=n(2),f=n(3),g=n(73),p=n(41),m=n(10);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Dismiss(t){var n=t.id,r=t.primary,a=void 0===r||r,o=t.dismissLabel,c=void 0===o?Object(d.__)("OK, Got it!","google-site-kit"):o,l=t.dismissExpires,u=void 0===l?0:l,v=t.disabled,h=t.onDismiss,E=void 0===h?function(){}:h,y=t.gaTrackingEventArgs,O=t.dismissOptions,k=Object(g.a)(n,null==y?void 0:y.category),_=Object(f.useDispatch)(p.a).dismissNotification,S=function(){var e=s()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==E?void 0:E(t);case 2:k.dismiss(null==y?void 0:y.label,null==y?void 0:y.value),_(n,b(b({},O),{},{expiresInSeconds:u}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(m.Button,{tertiary:!a,onClick:S,disabled:v},c)}Dismiss.propTypes={id:u.a.string,primary:u.a.bool,dismissLabel:u.a.string,dismissExpires:u.a.number,disabled:u.a.bool,onDismiss:u.a.func,gaTrackingEventArgs:u.a.shape({label:u.a.string,value:u.a.string})}}).call(this,n(4))},94:function(e,t,n){"use strict";n.d(t,"e",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"g",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"a",(function(){return c})),n.d(t,"f",(function(){return s})),n.d(t,"c",(function(){return l}));var r="modules/ads",i="https://www.googleapis.com/auth/adwords",a="https://www.googleapis.com/auth/supportcontent",o=1,c="wc-redirect-modal",s={WOOCOMMERCE:"woocommerce",GOOGLE_FOR_WOOCOMMERCE:"google-listings-and-ads"},l=[s.WOOCOMMERCE,s.GOOGLE_FOR_WOOCOMMERCE]},95:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(10),s=n(20);function CTA(t){var n=t.title,r=t.headerText,i=t.headerContent,a=t.description,l=t.ctaLink,u=t.ctaLabel,d=t.ctaLinkExternal,f=t.ctaType,g=t.error,p=t.onClick,m=t["aria-label"],v=t.children;return e.createElement("div",{className:o()("googlesitekit-cta",{"googlesitekit-cta--error":g})},(r||i)&&e.createElement("div",{className:"googlesitekit-cta__header"},r&&e.createElement("h2",{className:"googlesitekit-cta__header_text"},r),i),e.createElement("div",{className:"googlesitekit-cta__body"},n&&e.createElement("h3",{className:"googlesitekit-cta__title"},n),a&&"string"==typeof a&&e.createElement("p",{className:"googlesitekit-cta__description"},a),a&&"string"!=typeof a&&e.createElement("div",{className:"googlesitekit-cta__description"},a),u&&"button"===f&&e.createElement(c.Button,{"aria-label":m,href:l,onClick:p},u),u&&"link"===f&&e.createElement(s.a,{href:l,onClick:p,"aria-label":m,external:d,hideExternalIndicator:d,arrow:!0},u),v))}CTA.propTypes={title:i.a.string.isRequired,headerText:i.a.string,description:i.a.oneOfType([i.a.string,i.a.node]),ctaLink:i.a.string,ctaLinkExternal:i.a.bool,ctaLabel:i.a.string,ctaType:i.a.string,"aria-label":i.a.string,error:i.a.bool,onClick:i.a.func,children:i.a.node,headerContent:i.a.node},CTA.defaultProps={title:"",headerText:"",headerContent:"",description:"",ctaLink:"",ctaLabel:"",ctaType:"link",error:!1,onClick:function(){}},t.a=CTA}).call(this,n(4))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o}));var r=n(239),i=n(85),a=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=n.invertColor,o=void 0!==a&&a;return Object(r.a)(e.createElement(i.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(6),i=n.n(r),a=n(14),o=n(100),c=n(101);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=l(l({},u),t);i.referenceSiteURL&&(i.referenceSiteURL=i.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(i,n),d=Object(c.a)(i,n,s,r),f={},g=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);f[r]||(f[r]=Object(a.once)(d)),f[r].apply(f,t)};return{enableTracking:function(){i.trackingEnabled=!0},disableTracking:function(){i.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!i.trackingEnabled},trackEvent:d,trackEventOnce:g}}}).call(this,n(28))}},[[1294,1,0]]]);