(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[22],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(59),i=n(39),a=n(57);function o(t,n){var o,c=Object(r.a)(n),s=t.activeModules,l=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,f=void 0===d?[]:d,g=t.isAuthenticated,p=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(i.b,"]"))),!o){o=!0;var r=(null==f?void 0:f.length)?f.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:l,plugin_version:p||"",enabled_features:Array.from(a.a).join(","),active_modules:s.join(","),authenticated:g?"1":"0",user_properties:{user_roles:r,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(i.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a)}}}}}).call(this,n(28))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function f(e,t,n,r){var a=Object(l.a)(t);return function(){var t=s()(i.a.mark((function t(o,c,s,l){var u;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:s,value:l},t.abrupt("return",new Promise((function(e){var t,n,i=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(i),e()};a("event",c,d(d({},u),{},{event_callback:s})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,i){return t.apply(this,arguments)}}()}},102:function(e,t,n){"use strict";var r=n(123);n.d(t,"a",(function(){return r.a}));var i=n(124);n.d(t,"c",(function(){return i.a}));var a=n(125);n.d(t,"b",(function(){return a.a}))},104:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return r.createElement("svg",i({viewBox:"0 0 14 14",fill:"none"},e),a)}},107:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var r=n(21),i=n.n(r),a=n(152),o=n.n(a),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(2),f=n(10),g=n(154),p=n(104);function TourTooltip(t){var n=t.backProps,r=t.closeProps,c=t.index,l=t.primaryProps,u=t.size,m=t.step,b=t.tooltipProps,h=u>1?Object(g.a)(u):[],v=function(e){return s()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",i()({className:s()("googlesitekit-tour-tooltip",m.className)},b),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},m.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},m.content)),e.createElement(a.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},h.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:v(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(f.Button,i()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),m.cta,l.title&&e.createElement(f.Button,i()({className:"googlesitekit-tooltip-button",text:!0},l),l.title))),e.createElement(f.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(p.a,{width:"14",height:"14"}),onClick:r.onClick,"aria-label":Object(d.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(4))},1080:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChipCheckmark}));var r=n(214);function ChipCheckmark(){return e.createElement("div",{className:"mdc-chip__checkmark"},e.createElement(r.a,{className:"mdc-chip__checkmark-svg"}))}}).call(this,n(4))},115:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(0),s=n(137),l=n(58),u=n(131),d=n(17),f=Object(c.forwardRef)((function(t,n){var r=t.className,i=t.title,a=t.description,c=t.dismissCTA,f=t.additionalCTA,g=t.reverseCTAs,p=void 0!==g&&g,m=t.type,b=void 0===m?"success":m,h=t.icon;return e.createElement(d.e,{ref:n},e.createElement(d.k,null,e.createElement(d.a,{alignMiddle:!0,size:12,className:o()("googlesitekit-subtle-notification",r,{"googlesitekit-subtle-notification--success":"success"===b,"googlesitekit-subtle-notification--warning":"warning"===b,"googlesitekit-subtle-notification--new-feature":"new-feature"===b})},e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},h,"success"===b&&!h&&e.createElement(s.a,{width:24,height:24}),"warning"===b&&!h&&e.createElement(l.a,{width:24,height:24}),"new-feature"===b&&!h&&e.createElement(u.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,i),e.createElement("p",{className:"googlesitekit-subtle-notification__secondary_description"},a)),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},!p&&c,p&&f,!p&&f,p&&c))))}));f.propTypes={className:i.a.string,title:i.a.node,description:i.a.node,dismissCTA:i.a.node,additionalCTA:i.a.node,reverseCTAs:i.a.bool,type:i.a.oneOf(["success","warning","new-feature"]),icon:i.a.object},t.a=f}).call(this,n(4))},120:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var r=n(1),i=n.n(r),a=n(0),o=n(2),c=n(3),s=n(10),l=n(35),u=n(54);function ErrorNotice(t){var n,r=t.error,i=t.hasButton,d=void 0!==i&&i,f=t.storeName,g=t.message,p=void 0===g?r.message:g,m=t.noPrefix,b=void 0!==m&&m,h=t.skipRetryMessage,v=t.Icon,O=Object(c.useDispatch)(),E=Object(c.useSelect)((function(e){return f?e(f).getSelectorDataForError(r):null})),_=Object(a.useCallback)((function(){O(E.storeName).invalidateResolution(E.name,E.args)}),[O,E]);if(!r||Object(l.f)(r))return null;var y=d&&Object(l.d)(r,E);return d||h||(p=Object(o.sprintf)(
/* translators: %1$s: Error message from Google API. */
Object(o.__)("%1$s%2$s Please try again.","google-site-kit"),p,p.endsWith(".")?"":".")),e.createElement(a.Fragment,null,v&&e.createElement("div",{className:"googlesitekit-error-notice__icon"},e.createElement(v,{width:"24",height:"24"})),e.createElement(u.a,{message:p,reconnectURL:null===(n=r.data)||void 0===n?void 0:n.reconnectURL,noPrefix:b}),y&&e.createElement(s.Button,{className:"googlesitekit-error-notice__retry-button",onClick:_},Object(o.__)("Retry","google-site-kit")))}ErrorNotice.propTypes={error:i.a.shape({message:i.a.string}),hasButton:i.a.bool,storeName:i.a.string,message:i.a.string,noPrefix:i.a.bool,skipRetryMessage:i.a.bool,Icon:i.a.elementType}}).call(this,n(4))},123:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(21),i=n.n(r),a=n(6),o=n.n(a),c=n(25),s=n.n(c),l=n(1),u=n.n(l),d=n(11),f=n.n(d);function Cell(t){var n,r=t.className,a=t.alignTop,c=t.alignMiddle,l=t.alignBottom,u=t.alignRight,d=t.alignLeft,g=t.smAlignRight,p=t.mdAlignRight,m=t.lgAlignRight,b=t.smSize,h=t.smStart,v=t.smOrder,O=t.mdSize,E=t.mdStart,_=t.mdOrder,y=t.lgSize,k=t.lgStart,j=t.lgOrder,S=t.size,w=t.children,x=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",i()({},x,{className:f()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":l,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":g,"mdc-layout-grid__cell--align-right-tablet":p,"mdc-layout-grid__cell--align-right-desktop":m},o()(n,"mdc-layout-grid__cell--span-".concat(S),12>=S&&S>0),o()(n,"mdc-layout-grid__cell--span-".concat(y,"-desktop"),12>=y&&y>0),o()(n,"mdc-layout-grid__cell--start-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--order-".concat(j,"-desktop"),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--start-".concat(E,"-tablet"),8>=E&&E>0),o()(n,"mdc-layout-grid__cell--order-".concat(_,"-tablet"),8>=_&&_>0),o()(n,"mdc-layout-grid__cell--span-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--start-".concat(h,"-phone"),4>=h&&h>0),o()(n,"mdc-layout-grid__cell--order-".concat(v,"-phone"),4>=v&&v>0),n))}),w)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},124:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",i()({ref:n,className:u()("mdc-layout-grid__inner",r)},c),a)}));f.displayName="Row",f.propTypes={className:s.a.string,children:s.a.node},f.defaultProps={className:""},t.a=f}).call(this,n(4))},125:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.alignLeft,a=t.fill,c=t.className,s=t.children,l=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",i()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":l,"mdc-layout-grid--fill":a})},d,{ref:n}),s)}));f.displayName="Grid",f.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},f.defaultProps={className:""},t.a=f}).call(this,n(4))},126:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},127:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},128:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},1295:function(e,t,n){"use strict";n.r(t);var r=n(3),i=n.n(r),a=n(190),o=n.n(a),c=n(363),s=n.n(c),l=n(6),u=n.n(l),d=n(5),f=n.n(d),g=n(16),p=n.n(g),m=n(2),b=n(409),h=n(13),v=n(42),O=(n(756),n(920)),E=(n(757),n(921)),_=(n(758),n(922)),y=n(786),k=n(583),j=n(513),S=n(41),w=n(22),x=n(19),C=n(57),N=n(7),D=n(520),A=n(354),P=n(521),T=n(205),R=n(12),I=n.n(R),F=n(45),L=n.n(F),M=n(200),B=n(62);function G(){return(G=p()(f.a.mark((function e(t){var n,r,i,a,o,c,s;return f.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.dispatch,r=t.select,i=r(v.c),a=i.getSnippetMode,o=i.hasSettingChanged,!(0,i.haveSettingsChanged)()){e.next=12;break}if(!o("postTypes")||"post_types"===a()){e.next=6;break}return e.next=6,n(v.c).rollbackSetting("postTypes");case 6:return e.next=8,n(v.c).saveSettings();case 8:if(c=e.sent,!(s=c.error)){e.next=12;break}return e.abrupt("return",{error:s});case 12:return e.next=14,L.a.invalidateCache("modules","reader-revenue-manager");case 14:return e.abrupt("return",{});case 15:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var U=o.a.createModuleStore("reader-revenue-manager",{storeName:v.c,submitChanges:function(e){return G.apply(this,arguments)},validateCanSubmitChanges:function(e){var t=Object(B.e)(e)(v.c),n=t.haveSettingsChanged,r=t.isDoingSubmitChanges,i=t.getPublicationID,a=t.getPublicationOnboardingState,o=t.getSnippetMode,c=t.getPostTypes,s=t.getProductID,l=t.getProductIDs,u=t.getPaymentOption;I()(!r(),M.a),I()(n(),M.b);var d=i(),f=a(),g=o(),p=c(),m=s(),b=l(),h=u();I()(Object(k.c)(d),"a valid publicationID is required"),I()(Object(k.b)(f),"a valid publication onboarding state is required"),I()(Object(k.d)(g),"a valid snippet mode is required"),I()("post_types"!==g||Array.isArray(p)&&p.every((function(e){return"string"==typeof e}))&&p.length>0,"a valid post types array is required"),I()("string"==typeof m,"a valid product ID is required"),I()(Array.isArray(b)&&b.every((function(e){return"string"==typeof e})),"a valid product IDs array is required"),I()("string"==typeof h,"a valid payment option is required")},ownedSettingsSlugs:["publicationID"],settingSlugs:["ownerID","publicationID","publicationOnboardingState","publicationOnboardingStateChanged","snippetMode","postTypes","productID","productIDs","paymentOption"]}),z=n(27),q=n.n(z),V=n(14),H=n(48),W=n(64);function K(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?K(Object(n),!0).forEach((function(t){u()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):K(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var $=Object(H.a)({baseName:"getPublications",controlCallback:function(){return L.a.get("modules",v.e,"publications",{},{useCache:!1})},reducerCallback:function(e,t){return Y(Y({},e),{},{publications:t})}}),J=Object(H.a)({baseName:"getSyncPublicationOnboardingState",controlCallback:function(e){var t=e.publicationID,n=e.publicationOnboardingState;return L.a.set("modules",v.e,"sync-publication-onboarding-state",{publicationID:t,publicationOnboardingState:n})},argsToParams:function(e){return{publicationID:e.publicationID,publicationOnboardingState:e.publicationOnboardingState}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.publicationID,n=e.publicationOnboardingState;I()("string"==typeof t&&t.length>0,"publicationID is required and must be string."),I()("string"==typeof n&&n.length>0,"publicationOnboardingState is required and must be string.")},reducerCallback:Object(r.createReducer)((function(e,t){var n,r=t.publicationID,i=t.publicationOnboardingState;if(r){var a=Date.now();e.settings.publicationID===r&&(e.settings.publicationOnboardingState=i,e.settings.publicationOnboardingStateLastSyncedAtMs=a),e.savedSettings.publicationID===r&&(e.savedSettings.publicationOnboardingState=i,e.savedSettings.publicationOnboardingStateLastSyncedAtMs=a);var o=null===(n=e.publications)||void 0===n?void 0:n.find((function(e){return e.publicationId===r}));o&&(o.onboardingState=i)}}))}),Q={publications:void 0},X={syncPublicationOnboardingState:f.a.mark((function e(){var t,n,i;return f.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,r.commonActions.await(t.resolveSelect(v.c).getSettings());case 5:if(n=t.select(v.c).getPublicationID(),i=t.select(v.c).getPublicationOnboardingState(),void 0!==n&&void 0!==i){e.next=9;break}return e.abrupt("return",{});case 9:return e.next=11,J.actions.fetchGetSyncPublicationOnboardingState({publicationID:n,publicationOnboardingState:i});case 11:return e.abrupt("return",e.sent);case 12:case"end":return e.stop()}}),e)})),findMatchedPublication:f.a.mark((function e(){var t,n,i,a;return f.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:return t=e.sent,n=t.resolveSelect,e.next=6,r.commonActions.await(n(v.c).getPublications());case 6:if(0!==(i=e.sent).length){e.next=9;break}return e.abrupt("return",null);case 9:if(1!==i.length){e.next=11;break}return e.abrupt("return",i[0]);case 11:return a=i.find((function(e){return e.onboardingState===v.d.ONBOARDING_COMPLETE})),e.abrupt("return",a||i[0]);case 13:case"end":return e.stop()}}),e)})),resetPublications:f.a.mark((function e(){var t;return f.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,{type:"RESET_PUBLICATIONS"};case 5:return e.next=7,W.a.clearErrors("getPublications");case 7:return e.abrupt("return",t.dispatch(v.c).invalidateResolutionForStoreSelector("getPublications"));case 8:case"end":return e.stop()}}),e)})),selectPublication:Object(B.f)((function(e){I()(Object(V.isPlainObject)(e),"A valid publication object is required."),["publicationId","onboardingState"].forEach((function(t){I()(e.hasOwnProperty(t),"The publication object must contain ".concat(t))}))}),f.a.mark((function e(t){var n,i,a,o,c,s,l;return f.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.publicationId,i=t.onboardingState,a=t.paymentOptions,o=t.products,e.next=3,r.commonActions.getRegistry();case 3:return c=e.sent,s={publicationID:n,publicationOnboardingState:i,publicationOnboardingStateChanged:!1,productIDs:[],paymentOption:""},a&&(l=Object.keys(a).find((function(e){return!!a[e]})))&&(s.paymentOption=l),o&&(s.productIDs=o.reduce((function(e,t){var n=t.name;return n?[].concat(q()(e),[n]):e}),[])),s.productID="openaccess",e.abrupt("return",c.dispatch(v.c).setSettings(s));case 9:case"end":return e.stop()}}),e)})))},Z={getPublications:f.a.mark((function e(){var t;return f.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:if(t=e.sent,void 0!==t.select(v.c).getPublications()){e.next=7;break}return e.next=7,$.actions.fetchGetPublications();case 7:case"end":return e.stop()}}),e)}))},ee={getPublications:function(e){return e.publications},getCurrentProductIDs:Object(r.createRegistrySelector)((function(e){return function(t){if(void 0!==e(v.c).getPublications()){var n=e(v.c).getPublicationID();if(!n)return[];var r=t.publications.find((function(e){return e.publicationId===n}));return r&&r.products?r.products.map((function(e){return e.name})):[]}}}))},te=Object(r.combineStores)($,J,{initialState:Q,actions:X,controls:{},reducer:function(e,t){switch(t.type){case"RESET_PUBLICATIONS":return Y(Y({},e),{},{publications:Q.publications});default:return e}},resolvers:Z,selectors:ee}),ne=(te.initialState,te.actions,te.controls,te.reducer,te.resolvers,te.selectors,te),re=n(157);function ie(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ae(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ie(Object(n),!0).forEach((function(t){u()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ie(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var oe,ce={selectors:{getServiceURL:Object(r.createRegistrySelector)((function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.path,i=n.query,a="https://publishercenter.google.com";if(r){var o="/".concat(r.replace(/^\//,""));a="".concat(a).concat(o)}a=Object(re.a)(a,ae(ae({},i),{},{utm_source:"sitekit"}));var c=e(N.a).getAccountChooserURL(a);if(void 0!==c)return c}})),getDetailsLinkURL:Object(r.createRegistrySelector)((function(e){return function(){var t=e(v.c).getPublicationID();return e(v.c).getServiceURL({path:"reader-revenue-manager",query:{publication:t}})}})),getCreatePublicationLinkURL:Object(r.createRegistrySelector)((function(e){return function(){return e(v.c).getServiceURL({query:{prefill_canonical_domain:e(h.c).getReferenceSiteURL(),prefill_lang:e(h.c).getSiteLocale(),app_redirect:"rrm"}})}}))}},se=Object(r.combineStores)(U,ne,ce);se.initialState,se.actions,se.controls,se.reducer,se.resolvers,se.selectors;function le(e,t){return ue.apply(this,arguments)}function ue(){return(ue=p()(f.a.mark((function e(t,n){var r,i,a,o,c,s;return f.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.select,i=t.resolveSelect,e.next=3,i(v.c).getSettings();case 3:if(a=r(v.c).getPublicationOnboardingState(),o=r(v.c).getPaymentOption(),c=r(v.c).getProductIDs(),s=r(v.c).getProductID(),!(a===v.d.ONBOARDING_COMPLETE&&c.length>0&&"openaccess"===s&&o===n)){e.next=9;break}return e.abrupt("return",!0);case 9:return e.abrupt("return",!1);case 10:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var de,fe,ge,pe,me,be=(oe={},u()(oe,A.e,{Component:j.d,priority:T.c.SETUP_CTA_LOW,areaSlug:S.b.BANNERS_BELOW_NAV,groupID:S.c.SETUP_CTAS,viewContexts:[w.n],checkRequirements:(pe=p()(f.a.mark((function e(t){var n,r,i,a,o;return f.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.select,r=t.resolveSelect,e.next=3,Promise.all([r(N.a).getDismissedPrompts(),r(x.a).isModuleConnected(v.e),r(x.a).canActivateModule(v.e)]);case 3:if(i=n(N.a).isPromptDismissed(v.b),a=n(x.a).isModuleConnected(v.e),o=n(x.a).canActivateModule(v.e),!1!==i||!1!==a||!o){e.next=8;break}return e.abrupt("return",!0);case 8:return e.abrupt("return",!1);case 9:case"end":return e.stop()}}),e)}))),function(e){return pe.apply(this,arguments)}),isDismissible:!0,dismissRetries:1}),u()(oe,A.f,{Component:j.c,areaSlug:S.b.BANNERS_BELOW_NAV,viewContexts:[w.n],checkRequirements:(ge=p()(f.a.mark((function e(t){var n,r,i,a,o;return f.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.select,r=t.resolveSelect,e.next=3,r(x.a).isModuleConnected(v.e);case 3:if(e.sent){e.next=6;break}return e.abrupt("return",!1);case 6:return i=Object(b.a)(location.href,"notification"),a=Object(b.a)(location.href,"slug"),e.next=10,r(v.c).getSettings();case 10:return e.next=12,n(v.c).getPublicationOnboardingState();case 12:if(o=e.sent,"authentication_success"!==i||a!==v.e||void 0===o){e.next=15;break}return e.abrupt("return",!0);case 15:return e.abrupt("return",!1);case 16:case"end":return e.stop()}}),e)}))),function(e){return ge.apply(this,arguments)}),isDismissible:!1}),u()(oe,A.a,{Component:D.a,priority:20,areaSlug:S.b.BANNERS_BELOW_NAV,viewContexts:[w.n],isDismissible:!0,checkRequirements:(fe=p()(f.a.mark((function e(t){var n;return f.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,le(t,"contributions");case 2:return n=e.sent,e.abrupt("return",n);case 4:case"end":return e.stop()}}),e)}))),function(e){return fe.apply(this,arguments)})}),u()(oe,A.d,{Component:P.a,priority:20,areaSlug:S.b.BANNERS_BELOW_NAV,viewContexts:[w.n],isDismissible:!0,checkRequirements:(de=p()(f.a.mark((function e(t){var n;return f.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,le(t,"subscriptions");case 2:return n=e.sent,e.abrupt("return",n);case 4:case"end":return e.stop()}}),e)}))),function(e){return de.apply(this,arguments)})}),oe);i.a.registerStore(v.c,se),o.a.registerModule("reader-revenue-manager",{storeName:v.c,SettingsEditComponent:E.a,SettingsViewComponent:_.a,SetupComponent:O.a,Icon:y.a,features:[Object(m.__)("Reader Revenue Manager publication tracking will be disabled","google-site-kit")],overrideSetupSuccessNotification:!0,checkRequirements:(me=p()(f.a.mark((function e(t){var n;return f.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.resolveSelect(h.c).getSiteInfo();case 2:if(n=t.select(h.c).getHomeURL(),!Object(k.a)(n)){e.next=5;break}return e.abrupt("return");case 5:throw{code:v.a,message:Object(m.__)("The site should use HTTPS to set up Reader Revenue Manager","google-site-kit"),data:null};case 6:case"end":return e.stop()}}),e)}))),function(e){return me.apply(this,arguments)})}),function(e){if(Object(C.b)("rrmModule"))for(var t in be)e.registerNotification(t,be[t])}(s.a)},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var r="core/site",i="primary",a="secondary"},131:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},137:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},141:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return StoreErrorNotices}));var r=n(1),i=n.n(r),a=n(3),o=n(120),c=n(19),s=n(35),l=n(169);function StoreErrorNotices(t){var n=t.hasButton,r=void 0!==n&&n,i=t.moduleSlug,u=t.storeName,d=Object(a.useSelect)((function(e){return e(u).getErrors()})),f=Object(a.useSelect)((function(e){return e(c.a).getModule(i)})),g=[];return d.filter((function(e){return!(!(null==e?void 0:e.message)||g.includes(e.message))&&(g.push(e.message),!0)})).map((function(t,n){var i=t.message;return Object(s.e)(t)&&(i=Object(l.a)(i,f)),e.createElement(o.a,{key:n,error:t,hasButton:r,storeName:u,message:i})}))}StoreErrorNotices.propTypes={hasButton:i.a.bool,storeName:i.a.string.isRequired,moduleSlug:i.a.string}}).call(this,n(4))},142:function(e,t,n){"use strict";var r=n(166);n.d(t,"c",(function(){return r.a}));var i=n(65);n.d(t,"b",(function(){return i.c})),n.d(t,"a",(function(){return i.a}))},147:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(15),o=n.n(a),c=n(0),s=n(409),l=n(157);t.a=function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=Object(c.useState)(Object(s.a)(r.location.href,t)||n),u=o()(a,2),d=u[0],f=u[1],g=function(e){f(e);var n=Object(l.a)(r.location.href,i()({},t,e));r.history.replaceState(null,"",n)};return[d,g]}}).call(this,n(28))},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},159:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(0),s=n(3),l=n(13),u=n(7),d=n(19),f=n(32),g=n(37),p=n(36),m=n(18);function b(e){var t=Object(m.a)(),n=Object(s.useSelect)((function(t){return t(d.a).getModule(e)})),r=Object(s.useSelect)((function(e){return e(u.a).hasCapability(u.K)})),a=Object(s.useDispatch)(d.a).activateModule,b=Object(s.useDispatch)(f.a).navigateTo,h=Object(s.useDispatch)(l.c).setInternalServerError,v=Object(c.useCallback)(o()(i.a.mark((function n(){var r,o,c;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,a(e);case 2:if(r=n.sent,o=r.error,c=r.response,o){n.next=13;break}return n.next=8,Object(p.b)("".concat(t,"_widget-activation-cta"),"activate_module",e);case 8:return n.next=10,Object(g.f)("module_setup",e,{ttl:300});case 10:b(c.moduleReauthURL),n.next=14;break;case 13:h({id:"".concat(e,"-setup-error"),description:o.message});case 14:case"end":return n.stop()}}),n)}))),[a,e,b,h,t]);return(null==n?void 0:n.name)&&r?v:null}},166:function(e,t,n){"use strict";(function(e){var r=n(11),i=n.n(r),a=n(1),o=n.n(a),c=n(2),s=n(3),l=n(201),u=n(210),d=n(65),f=n(7),g=n(10),p=n(0),m=Object(p.forwardRef)((function(t,n){var r=t.className,a=t.children,o=t.type,p=t.dismiss,m=void 0===p?"":p,b=t.dismissCallback,h=t.dismissLabel,v=void 0===h?Object(c.__)("OK, Got it!","google-site-kit"):h,O=t.Icon,E=void 0===O?Object(d.d)(o):O,_=t.OuterCTA,y=Object(s.useDispatch)(f.a).dismissItem,k=Object(s.useSelect)((function(e){return m?e(f.a).isItemDismissed(m):void 0}));if(m&&k)return null;var j=a?u.a:l.a;return e.createElement("div",{ref:n,className:i()(r,"googlesitekit-settings-notice","googlesitekit-settings-notice--".concat(o),{"googlesitekit-settings-notice--single-row":!a,"googlesitekit-settings-notice--multi-row":a})},e.createElement("div",{className:"googlesitekit-settings-notice__icon"},e.createElement(E,{width:"20",height:"20"})),e.createElement("div",{className:"googlesitekit-settings-notice__body"},e.createElement(j,t)),m&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(g.Button,{tertiary:!0,onClick:function(){"string"==typeof m&&y(m),null==b||b()}},v)),_&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(_,null)))}));m.propTypes={className:o.a.string,children:o.a.node,notice:o.a.node.isRequired,type:o.a.oneOf([d.a,d.c,d.b]),Icon:o.a.elementType,LearnMore:o.a.elementType,CTA:o.a.elementType,OuterCTA:o.a.elementType,dismiss:o.a.string,dismissLabel:o.a.string,dismissCallback:o.a.func},m.defaultProps={type:d.a},t.a=m}).call(this,n(4))},168:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALinkSubtle}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(73),f=n(10),g=n(70);function CTALinkSubtle(t){var n=t.id,r=t.ctaLink,a=t.ctaLabel,c=t.onCTAClick,s=t.isCTALinkExternal,l=void 0!==s&&s,p=t.gaTrackingEventArgs,m=t.tertiary,b=void 0!==m&&m,h=t.isSaving,v=void 0!==h&&h,O=Object(d.a)(n,null==p?void 0:p.category),E=function(){var e=o()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==c?void 0:c(t);case 2:O.confirm(null==p?void 0:p.label,null==p?void 0:p.value);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(f.Button,{className:u()("googlesitekit-subtle-notification__cta",{"googlesitekit-subtle-notification__cta--spinner__running":v}),href:r,onClick:E,target:l?"_blank":"_self",trailingIcon:l?e.createElement(g.a,{width:14,height:14}):void 0,icon:v?e.createElement(f.CircularProgress,{size:14}):void 0,tertiary:b},a)}CTALinkSubtle.propTypes={id:s.a.string,ctaLink:s.a.string,ctaLabel:s.a.string,onCTAClick:s.a.func,isCTALinkExternal:s.a.bool,gaTrackingEventArgs:s.a.shape({label:s.a.string,value:s.a.string}),tertiary:s.a.bool,isSaving:s.a.bool}}).call(this,n(4))},169:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(2);function i(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},i=n.slug,a=void 0===i?"":i,o=n.name,c=void 0===o?"":o,s=n.owner,l=void 0===s?{}:s;if(!a||!c)return e;var u="",d="";return"analytics-4"===a?e.match(/account/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):"search-console"===a&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),u||(u=Object(r.sprintf)(
/* translators: %s: module name */
Object(r.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),c)),l&&l.login&&(d=Object(r.sprintf)(
/* translators: %s: owner name */
Object(r.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),l.login)),d||(d=Object(r.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(u," ").concat(d)}},17:function(e,t,n){"use strict";var r=n(254);n.d(t,"i",(function(){return r.a}));var i=n(319);n.d(t,"f",(function(){return i.a}));var a=n(320);n.d(t,"h",(function(){return a.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var s=n(91),l=n.n(s);n.d(t,"b",(function(){return l.a})),n.d(t,"c",(function(){return s.DialogContent})),n.d(t,"d",(function(){return s.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},175:function(e,t,n){"use strict";var r=n(216);n.d(t,"b",(function(){return r.a}));var i=n(221);n.d(t,"a",(function(){return i.a}))},179:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActionsCTALinkDismiss}));var r=n(1),i=n.n(r),a=n(2),o=n(0),c=n(3),s=n(32),l=n(92),u=n(187);function ActionsCTALinkDismiss(t){var n=t.id,r=t.className,i=void 0===r?"googlesitekit-publisher-win__actions":r,d=t.ctaLink,f=t.ctaLabel,g=t.ctaDisabled,p=void 0!==g&&g,m=t.onCTAClick,b=t.ctaDismissOptions,h=t.isSaving,v=void 0!==h&&h,O=t.onDismiss,E=void 0===O?function(){}:O,_=t.dismissLabel,y=void 0===_?Object(a.__)("OK, Got it!","google-site-kit"):_,k=t.dismissOnCTAClick,j=void 0===k||k,S=t.dismissExpires,w=void 0===S?0:S,x=t.dismissOptions,C=void 0===x?{}:x,N=t.gaTrackingEventArgs,D=void 0===N?{}:N,A=Object(c.useSelect)((function(e){return!!d&&e(s.a).isNavigatingTo(d)}));return e.createElement(o.Fragment,null,e.createElement("div",{className:i},e.createElement(u.a,{id:n,ctaLink:d,ctaLabel:f,onCTAClick:m,dismissOnCTAClick:j,dismissExpires:w,dismissOptions:b,gaTrackingEventArgs:D,isSaving:v,isDisabled:p}),e.createElement(l.a,{id:n,primary:!1,dismissLabel:y,dismissExpires:w,disabled:A,onDismiss:E,dismissOptions:C,gaTrackingEventArgs:D})))}ActionsCTALinkDismiss.propTypes={id:i.a.string,className:i.a.string,ctaDisabled:i.a.bool,ctaLink:i.a.string,ctaLabel:i.a.string,onCTAClick:i.a.func,isSaving:i.a.bool,onDismiss:i.a.func,ctaDismissOptions:i.a.object,dismissLabel:i.a.string,dismissOnCTAClick:i.a.bool,dismissExpires:i.a.number,dismissOptions:i.a.object,gaTrackingEventArgs:i.a.object}}).call(this,n(4))},18:function(e,t,n){"use strict";var r=n(0),i=n(61);t.a=function(){return Object(r.useContext)(i.b)}},180:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LearnMoreLink}));var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(20),u=n(73);function LearnMoreLink(t){var n=t.id,r=t.label,a=t.url,c=t.ariaLabel,s=t.gaTrackingEventArgs,d=t.external,f=void 0===d||d,g=o()(t,["id","label","url","ariaLabel","gaTrackingEventArgs","external"]),p=Object(u.a)(n);return e.createElement(l.a,i()({onClick:function(e){e.persist(),p.clickLearnMore(null==s?void 0:s.label,null==s?void 0:s.value)},href:a,"aria-label":c,external:f},g),r)}LearnMoreLink.propTypes={id:s.a.string,label:s.a.string,url:s.a.string,ariaLabel:s.a.string,gaTrackingEventArgs:s.a.shape({label:s.a.string,value:s.a.string}),external:s.a.bool}}).call(this,n(4))},182:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(1),i=n.n(r),a=" ";function DisplaySetting(e){return e.value||a}DisplaySetting.propTypes={value:i.a.oneOfType([i.a.string,i.a.bool,i.a.number])},t.b=DisplaySetting},186:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 0h2v7H0zm0 10h2v2H0z",fill:"currentColor",fillRule:"evenodd"});t.a=function SvgWarningIcon(e){return r.createElement("svg",i({viewBox:"0 0 2 12"},e),a)}},187:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALink}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(15),u=n.n(l),d=n(1),f=n.n(d),g=n(206),p=n(0),m=n(3),b=n(41),h=n(32),v=n(13),O=n(73),E=n(10);function _(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function CTALink(t){var n=t.id,r=t.ctaLink,a=t.ctaLabel,o=t.onCTAClick,c=t.isSaving,l=t.dismissOnCTAClick,d=void 0!==l&&l,f=t.dismissExpires,_=void 0===f?0:f,k=t.dismissOptions,j=void 0===k?{}:k,S=t.gaTrackingEventArgs,w=t.isDisabled,x=void 0!==w&&w,C=Object(p.useState)(!1),N=u()(C,2),D=N[0],A=N[1],P=Object(g.a)(),T=Object(O.a)(n,null==S?void 0:S.category),R=Object(m.useSelect)((function(e){return!!r&&e(h.a).isNavigatingTo(r)})),I=Object(m.useDispatch)(v.c),F=I.clearError,L=I.receiveError,M=Object(m.useDispatch)(b.a).dismissNotification,B=Object(m.useDispatch)(h.a).navigateTo,G=function(){var e=s()(i.a.mark((function e(t){var a,c,s;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return F("notificationAction",[n]),t.persist(),!t.defaultPrevented&&r&&t.preventDefault(),A(!0),e.next=6,null==o?void 0:o(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:if(a=e.t0,c=a.error,P()&&A(!1),!c){e.next=15;break}return L(c,"notificationAction",[n]),e.abrupt("return");case 15:return s=[T.confirm()],d&&s.push(M(n,y(y({},j),{},{expiresInSeconds:_}))),e.next=19,Promise.all(s);case 19:r&&B(r);case 20:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(E.SpinnerButton,{className:"googlesitekit-notification__cta",href:r,onClick:G,disabled:D||R||x,isSaving:D||R||c},a)}CTALink.propTypes={id:f.a.string,ctaLink:f.a.string,ctaLabel:f.a.string,onCTAClick:f.a.func,dismissOnCTAClick:f.a.bool,dismissExpires:f.a.number,dismissOptions:f.a.object,isDisabled:f.a.bool}}).call(this,n(4))},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="core/modules",i="insufficient_module_dependencies"},190:function(e,t){e.exports=googlesitekit.modules},199:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SupportLink}));var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(3),u=n(13),d=n(20);function SupportLink(t){var n=t.path,r=t.query,a=t.hash,c=o()(t,["path","query","hash"]),s=Object(l.useSelect)((function(e){return e(u.c).getGoogleSupportURL({path:n,query:r,hash:a})}));return e.createElement(d.a,i()({},c,{href:s}))}SupportLink.propTypes={path:s.a.string.isRequired,query:s.a.object,hash:s.a.string}}).call(this,n(4))},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(146),f=n(0),g=n(2),p=n(126),m=n(127),b=n(128),h=n(70),v=n(76),O=Object(f.forwardRef)((function(t,n){var r,a=t["aria-label"],c=t.secondary,l=void 0!==c&&c,u=t.arrow,f=void 0!==u&&u,O=t.back,E=void 0!==O&&O,_=t.caps,y=void 0!==_&&_,k=t.children,j=t.className,S=void 0===j?"":j,w=t.danger,x=void 0!==w&&w,C=t.disabled,N=void 0!==C&&C,D=t.external,A=void 0!==D&&D,P=t.hideExternalIndicator,T=void 0!==P&&P,R=t.href,I=void 0===R?"":R,F=t.inverse,L=void 0!==F&&F,M=t.noFlex,B=void 0!==M&&M,G=t.onClick,U=t.small,z=void 0!==U&&U,q=t.standalone,V=void 0!==q&&q,H=t.linkButton,W=void 0!==H&&H,K=t.to,Y=t.leadingIcon,$=t.trailingIcon,J=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),Q=I||K||!G?K?"ROUTER_LINK":A?"EXTERNAL_LINK":"LINK":N?"BUTTON_DISABLED":"BUTTON",X="BUTTON"===Q||"BUTTON_DISABLED"===Q?"button":"ROUTER_LINK"===Q?d.b:"a",Z=("EXTERNAL_LINK"===Q&&(r=Object(g._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===Q&&(r=Object(g._x)("(disabled)","screen reader text","google-site-kit")),r?a?"".concat(a," ").concat(r):"string"==typeof k?"".concat(k," ").concat(r):void 0:a),ee=Y,te=$;return E&&(ee=e.createElement(b.a,{width:14,height:14})),A&&!T&&(te=e.createElement(h.a,{width:14,height:14})),f&&!L&&(te=e.createElement(p.a,{width:14,height:14})),f&&L&&(te=e.createElement(m.a,{width:14,height:14})),e.createElement(X,i()({"aria-label":Z,className:s()("googlesitekit-cta-link",S,{"googlesitekit-cta-link--secondary":l,"googlesitekit-cta-link--inverse":L,"googlesitekit-cta-link--small":z,"googlesitekit-cta-link--caps":y,"googlesitekit-cta-link--danger":x,"googlesitekit-cta-link--disabled":N,"googlesitekit-cta-link--standalone":V,"googlesitekit-cta-link--link-button":W,"googlesitekit-cta-link--no-flex":!!B}),disabled:N,href:"LINK"!==Q&&"EXTERNAL_LINK"!==Q||N?void 0:I,onClick:G,rel:"EXTERNAL_LINK"===Q?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===Q?"_blank":void 0,to:K},J),!!ee&&e.createElement(v.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},k),!!te&&e.createElement(v.a,{marginLeft:5},te))}));O.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=O}).call(this,n(4))},200:function(e,t,n){"use strict";n.d(t,"a",(function(){return k})),n.d(t,"b",(function(){return j})),n.d(t,"c",(function(){return S})),n.d(t,"g",(function(){return w})),n.d(t,"f",(function(){return x})),n.d(t,"d",(function(){return C})),n.d(t,"e",(function(){return N}));var r=n(16),i=n.n(r),a=n(5),o=n.n(a),c=n(6),s=n.n(c),l=n(12),u=n.n(l),d=n(14),f=n(45),g=n.n(f),p=n(3),m=n(62),b=n(82),h=n(48),v=n(64);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function E(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var _=v.a.clearError,y=v.a.receiveError,k="cannot submit changes while submitting changes",j="cannot submit changes if settings have not changed",S=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=r.ownedSettingsSlugs,a=void 0===i?void 0:i,c=r.storeName,l=void 0===c?void 0:c,f=r.settingSlugs,v=void 0===f?[]:f,O=r.initialSettings,k=void 0===O?void 0:O,j=r.validateHaveSettingsChanged,S=void 0===j?N():j;u()(e,"type is required."),u()(t,"identifier is required."),u()(n,"datapoint is required.");var w=l||"".concat(e,"/").concat(t),x={ownedSettingsSlugs:a,settings:k,savedSettings:void 0},C=Object(h.a)({baseName:"getSettings",controlCallback:function(){return g.a.get(e,t,n,{},{useCache:!1})},reducerCallback:function(e,t){return E(E({},e),{},{savedSettings:E({},t),settings:E(E({},t),e.settings||{})})}}),D=Object(h.a)({baseName:"saveSettings",controlCallback:function(r){var i=r.values;return g.a.set(e,t,n,i)},reducerCallback:function(e,t){return E(E({},e),{},{savedSettings:E({},t),settings:E({},t)})},argsToParams:function(e){return{values:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.values;u()(Object(d.isPlainObject)(t),"values is required.")}}),A={},P={setSettings:function(e){return u()(Object(d.isPlainObject)(e),"values is required."),{payload:{values:e},type:"SET_SETTINGS"}},rollbackSettings:function(){return{payload:{},type:"ROLLBACK_SETTINGS"}},rollbackSetting:function(e){return u()(e,"setting is required."),{payload:{setting:e},type:"ROLLBACK_SETTING"}},saveSettings:o.a.mark((function e(){var t,n,r,i,a;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,_("saveSettings",[]);case 5:return n=t.select(w).getSettings(),e.next=8,D.actions.fetchSaveSettings(n);case 8:if(r=e.sent,i=r.response,!(a=r.error)){e.next=14;break}return e.next=14,y(a,"saveSettings",[]);case 14:return e.abrupt("return",{response:i,error:a});case 15:case"end":return e.stop()}}),e)}))},T={},R=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:x,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.payload;switch(n){case"SET_SETTINGS":var i=r.values;return E(E({},e),{},{settings:E(E({},e.settings||{}),i)});case"ROLLBACK_SETTINGS":return E(E({},e),{},{settings:e.savedSettings});case"ROLLBACK_SETTING":var a=r.setting;return e.savedSettings[a]?E(E({},e),{},{settings:E(E({},e.settings||{}),{},s()({},a,e.savedSettings[a]))}):E({},e);default:return void 0!==A[n]?A[n](e,{type:n,payload:r}):e}},I={getSettings:o.a.mark((function e(){var t;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.commonActions.getRegistry();case 2:if(t=e.sent,t.select(w).getSettings()){e.next=7;break}return e.next=7,C.actions.fetchGetSettings();case 7:case"end":return e.stop()}}),e)}))},F=Object(m.g)(S),L=F.safeSelector,M=F.dangerousSelector,B={haveSettingsChanged:L,__dangerousHaveSettingsChanged:M,getSettings:function(e){return e.settings},hasSettingChanged:function(e,t){u()(t,"setting is required.");var n=e.settings,r=e.savedSettings;return!(!n||!r)&&!Object(d.isEqual)(n[t],r[t])},isDoingSaveSettings:function(e){return Object.values(e.isFetchingSaveSettings).some(Boolean)},getOwnedSettingsSlugs:function(e){return e.ownedSettingsSlugs},haveOwnedSettingsChanged:Object(p.createRegistrySelector)((function(e){return function(){var t=e(w).getOwnedSettingsSlugs();return e(w).haveSettingsChanged(t)}}))};v.forEach((function(e){var t=Object(b.b)(e),n=Object(b.a)(e);P["set".concat(t)]=function(e){return u()(void 0!==e,"value is required for calls to set".concat(t,"().")),{payload:{value:e},type:"SET_".concat(n)}},A["SET_".concat(n)]=function(t,n){var r=n.payload.value;return E(E({},t),{},{settings:E(E({},t.settings||{}),{},s()({},e,r))})},B["get".concat(t)]=Object(p.createRegistrySelector)((function(t){return function(){return(t(w).getSettings()||{})[e]}}))}));var G=Object(p.combineStores)(p.commonStore,C,D,{initialState:x,actions:P,controls:T,reducer:R,resolvers:I,selectors:B});return E(E({},G),{},{STORE_NAME:w})};function w(e,t){return function(){var n=i()(o.a.mark((function n(r){var i,a,c,s;return o.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(i=r.select,a=r.dispatch,!i(t).haveSettingsChanged()){n.next=8;break}return n.next=4,a(t).saveSettings();case 4:if(c=n.sent,!(s=c.error)){n.next=8;break}return n.abrupt("return",{error:s});case 8:return n.next=10,g.a.invalidateCache("modules",e);case 10:return n.abrupt("return",{});case 11:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}function x(e){return function(t){var n=t.select,r=t.dispatch;return n(e).haveSettingsChanged()?r(e).rollbackSettings():{}}}function C(e){return function(t){var n=Object(m.e)(t)(e),r=n.haveSettingsChanged,i=n.isDoingSubmitChanges;u()(!i(),k),u()(r(),j)}}function N(){return function(e,t,n){var r=t.settings,i=t.savedSettings;n&&u()(!Object(d.isEqual)(Object(d.pick)(r,n),Object(d.pick)(i,n)),j),u()(!Object(d.isEqual)(r,i),j)}}},201:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeSingleRow}));var r=n(1),i=n.n(r),a=n(0);function SettingsNoticeSingleRow(t){var n=t.notice,r=t.LearnMore,i=t.CTA;return e.createElement(a.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),i&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(i,null)))}SettingsNoticeSingleRow.propTypes={notice:i.a.node.isRequired,LearnMore:i.a.elementType,CTA:i.a.elementType}}).call(this,n(4))},204:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OverlayNotification}));var r=n(591),i=n(11),a=n.n(i),o=n(1),c=n.n(o),s=n(0),l=n(3),u=n(23),d=n(24);function OverlayNotification(t){var n=t.className,i=t.children,o=t.GraphicDesktop,c=t.GraphicMobile,f=t.notificationID,g=t.onShow,p=t.shouldShowNotification,m=Object(d.e)(),b=Object(l.useSelect)((function(e){return e(u.b).isShowingOverlayNotification(f)})),h=Object(l.useDispatch)(u.b).setOverlayNotificationToShow;if(Object(s.useEffect)((function(){p&&!b&&(h(f),null==g||g())}),[b,f,g,h,p]),!p||!b)return null;var v=a()("googlesitekit-overlay-notification",n);return m===d.b?e.createElement("div",{className:v},i,c&&e.createElement(c,null)):e.createElement(r.a,{direction:"up",in:b},e.createElement("div",{className:v},o&&e.createElement(o,null),i))}OverlayNotification.propTypes={className:c.a.string,children:c.a.node,GraphicDesktop:c.a.elementType,GraphicMobile:c.a.elementType,onShow:c.a.func,notificationID:c.a.string.isRequired,shouldShowNotification:c.a.bool}}).call(this,n(4))},205:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a}));var r="warning-notification-fpm",i="fpm-setup-cta",a={ERROR_HIGH:30,ERROR_LOW:60,WARNING:100,INFO:150,SETUP_CTA_HIGH:150,SETUP_CTA_LOW:200}},207:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5 22c-1.522 0-2.952-.284-4.29-.852a11.303 11.303 0 01-3.493-2.366 11.303 11.303 0 01-2.365-3.492A10.86 10.86 0 01.5 11c0-1.522.284-2.952.853-4.29a11.302 11.302 0 012.364-3.493A10.92 10.92 0 017.21.88 10.567 10.567 0 0111.5 0c1.522 0 2.952.293 4.29.88a10.92 10.92 0 013.492 2.337c.99.99 1.77 2.155 2.338 3.493.587 1.338.88 2.768.88 4.29 0 1.522-.293 2.952-.88 4.29a10.92 10.92 0 01-2.338 3.492c-.99.99-2.154 1.779-3.492 2.366A10.86 10.86 0 0111.5 22zm0-14.3c.312 0 .569-.1.77-.303.22-.22.33-.485.33-.797a.999.999 0 00-.33-.77.999.999 0 00-.77-.33c-.311 0-.577.11-.797.33a1.043 1.043 0 00-.303.77c0 .312.101.578.303.798.22.201.486.302.797.302zm-1.1 8.8V9.9h2.2v6.6h-2.2z",fill:"currentColor"});t.a=function SvgInfoCircle(e){return r.createElement("svg",i({viewBox:"0 0 23 22",fill:"none"},e),a)}},210:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeMultiRow}));var r=n(1),i=n.n(r),a=n(0);function SettingsNoticeMultiRow(t){var n=t.notice,r=t.LearnMore,i=t.CTA,o=t.children;return e.createElement(a.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),e.createElement("div",{className:"googlesitekit-settings-notice__inner-row"},e.createElement("div",{className:"googlesitekit-settings-notice__children-container"},o),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),i&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(i,null))))}SettingsNoticeMultiRow.propTypes={children:i.a.node.isRequired,notice:i.a.node.isRequired,LearnMore:i.a.elementType,CTA:i.a.elementType}}).call(this,n(4))},214:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M2 5.309l1.474 2.14c.69 1.001 1.946 1.001 2.636 0L10 1.8",stroke:"currentColor",strokeWidth:1.6,strokeLinecap:"square"});t.a=function SvgCheck2(e){return r.createElement("svg",i({viewBox:"0 0 12 9",fill:"none"},e),a)}},216:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(0),u=n(3),d=n(13),f=n(23);function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e){var t=Object(u.useDispatch)(f.b).setValue,n=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.2")})),r=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.4")}));return Object(l.useCallback)(s()(i.a.mark((function a(){var o,c,s,l;return i.a.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(o=document.querySelector("#adminmenu").offsetHeight>0){i.next=7;break}if(!(c=document.getElementById("wp-admin-bar-menu-toggle"))){i.next=7;break}return c.firstChild.click(),i.next=7,new Promise((function(e){setTimeout(e,0)}));case 7:"#adminmenu [href*='page=googlesitekit-dashboard']",(s=!!document.querySelector("".concat("#adminmenu [href*='page=googlesitekit-dashboard']","[aria-haspopup=true]")))&&document.querySelector("#adminmenu [href*='page=googlesitekit-dashboard']").click(),n&&!r&&(l=document.hasFocus,document.hasFocus=function(){return document.hasFocus=l,!1}),t("admin-menu-tooltip",p({isTooltipVisible:!0,rehideAdminMenu:!o,rehideAdminSubMenu:s},e));case 12:case"end":return i.stop()}}),a)}))),[n,r,t,e])}},22:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return i})),n.d(t,"o",(function(){return a})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return s})),n.d(t,"s",(function(){return l})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return f})),n.d(t,"k",(function(){return g})),n.d(t,"u",(function(){return p})),n.d(t,"v",(function(){return m})),n.d(t,"q",(function(){return b})),n.d(t,"p",(function(){return h})),n.d(t,"b",(function(){return v})),n.d(t,"e",(function(){return O})),n.d(t,"a",(function(){return E})),n.d(t,"d",(function(){return _})),n.d(t,"c",(function(){return y})),n.d(t,"f",(function(){return k})),n.d(t,"g",(function(){return j}));var r="mainDashboard",i="entityDashboard",a="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",s="activation",l="splash",u="adminBar",d="adminBarViewOnly",f="settings",g="adBlockingRecovery",p="wpDashboard",m="wpDashboardViewOnly",b="moduleSetup",h="metricSelection",v="key-metrics",O="traffic",E="content",_="speed",y="monetization",k=[r,i,a,o,c,l,f,b,h],j=[a,o,d,m]},221:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdminMenuTooltip}));var r=n(0),i=n(3),a=n(223),o=n(23),c=n(9),s=n(18);function AdminMenuTooltip(){var t=Object(s.a)(),n=Object(i.useDispatch)(o.b).setValue,l=Object(i.useSelect)((function(e){return e(o.b).getValue("admin-menu-tooltip")||{isTooltipVisible:!1}})),u=l.isTooltipVisible,d=void 0!==u&&u,f=l.rehideAdminMenu,g=void 0!==f&&f,p=l.rehideAdminSubMenu,m=void 0!==p&&p,b=l.tooltipSlug,h=l.title,v=l.content,O=l.dismissLabel,E=Object(r.useCallback)((function(){var e;g&&(document.querySelector("#adminmenu").offsetHeight>0&&(null===(e=document.getElementById("wp-admin-bar-menu-toggle"))||void 0===e||e.click()));m&&document.querySelector("body").click(),b&&Object(c.I)("".concat(t,"_").concat(b),"tooltip_dismiss"),n("admin-menu-tooltip",void 0)}),[g,m,n,b,t]);return d?e.createElement(a.a,{target:'#adminmenu [href*="page=googlesitekit-settings"]',slug:"ga4-activation-banner-admin-menu-tooltip",title:h,content:v,dismissLabel:O,onView:function(){Object(c.I)("".concat(t,"_").concat(b),"tooltip_view")},onDismiss:E}):null}}).call(this,n(4))},223:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return JoyrideTooltip}));var i=n(6),a=n.n(i),o=n(15),c=n.n(o),s=n(1),l=n(30),u=n(421),d=n(0),f=n(107),g=n(72),p=n(90);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function JoyrideTooltip(t){var n=t.title,i=t.content,a=t.dismissLabel,o=t.target,s=t.cta,m=void 0!==s&&s,h=t.className,v=t.styles,O=void 0===v?{}:v,E=t.slug,_=void 0===E?"":E,y=t.onDismiss,k=void 0===y?function(){}:y,j=t.onView,S=void 0===j?function(){}:j,w=t.onTourStart,x=void 0===w?function(){}:w,C=t.onTourEnd,N=void 0===C?function(){}:C,D=function(){return!!e.document.querySelector(o)},A=Object(d.useState)(D),P=c()(A,2),T=P[0],R=P[1];if(Object(u.a)((function(){D()&&R(!0)}),T?null:250),Object(d.useEffect)((function(){if(T&&e.ResizeObserver){var t=e.document.querySelector(o),n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}),[o,T]),!T)return null;var I=[{title:n,target:o,content:i,disableBeacon:!0,isFixed:!0,placement:"auto",cta:m,className:h}],F={close:a,last:a};return r.createElement(g.a,{slug:_},r.createElement(l.e,{callback:function(t){switch(t.type){case l.b.TOUR_START:x(),e.document.body.classList.add("googlesitekit-showing-tooltip");break;case l.b.TOUR_END:N(),e.document.body.classList.remove("googlesitekit-showing-tooltip");break;case l.b.STEP_AFTER:k();break;case l.b.TOOLTIP:S()}},disableOverlay:!0,disableScrolling:!0,spotlightPadding:0,floaterProps:p.b,locale:F,steps:I,styles:b(b(b({},p.c),O),{},{options:b(b({},p.c.options),null==O?void 0:O.options),spotlight:b(b({},p.c.spotlight),null==O?void 0:O.spotlight)}),tooltipComponent:f.a,run:!0}))}JoyrideTooltip.propTypes={title:s.PropTypes.node,content:s.PropTypes.string,dismissLabel:s.PropTypes.string,target:s.PropTypes.string.isRequired,onDismiss:s.PropTypes.func,onShow:s.PropTypes.func,className:s.PropTypes.string,styles:s.PropTypes.object,slug:s.PropTypes.string,onView:s.PropTypes.func}}).call(this,n(28),n(4))},23:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r="core/ui",i="activeContextID"},24:function(e,t,n){"use strict";n.d(t,"d",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return s}));var r=n(79),i="xlarge",a="desktop",o="tablet",c="small";function s(){var e=Object(r.a)();return e>1280?i:e>960?a:e>600?o:c}},242:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationWithSVG}));var r=n(21),i=n.n(r),a=n(11),o=n.n(a),c=n(24),s=n(17),l=n(255);function NotificationWithSVG(t){var n=t.id,r=t.title,a=t.description,u=t.actions,d=t.SVG,f=t.primaryCellSizes,g=t.SVGCellSizes,p=Object(c.e)(),m={mdSize:(null==g?void 0:g.md)||8,lgSize:(null==g?void 0:g.lg)||6};return p===c.c&&(m={mdSize:(null==g?void 0:g.md)||8}),p===c.b&&(m={smSize:(null==g?void 0:g.sm)||12}),e.createElement("div",{className:"googlesitekit-widget-context"},e.createElement(s.e,{className:"googlesitekit-widget-area"},e.createElement(s.k,null,e.createElement(s.a,{size:12},e.createElement("div",{className:o()("googlesitekit-widget","googlesitekit-widget--no-padding","googlesitekit-setup-cta-banner","googlesitekit-setup-cta-banner--".concat(n))},e.createElement("div",{className:"googlesitekit-widget__body"},e.createElement(s.e,{collapsed:!0},e.createElement(s.k,null,e.createElement(s.a,{smSize:(null==f?void 0:f.sm)||12,mdSize:(null==f?void 0:f.md)||8,lgSize:(null==f?void 0:f.lg)||6,className:"googlesitekit-setup-cta-banner__primary-cell"},e.createElement("h3",{className:"googlesitekit-setup-cta-banner__title"},r),a,e.createElement(l.a,{id:n}),u),e.createElement(s.a,i()({alignBottom:!0,className:"googlesitekit-setup-cta-banner__svg-wrapper--".concat(n)},m),e.createElement(d,null))))))))))}}).call(this,n(4))},255:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Error}));var r=n(1),i=n.n(r),a=n(0),o=n(3),c=n(13),s=n(54);function Error(t){var n=t.id,r=Object(o.useSelect)((function(e){return e(c.c).getError("notificationAction",[n])})),i=Object(o.useDispatch)(c.c).clearError;return Object(a.useEffect)((function(){return function(){i("notificationAction",[n])}}),[i,n]),r?e.createElement(s.a,{message:r.message}):null}Error.propTypes={id:i.a.string}}).call(this,n(4))},258:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g}));var r=n(11),i=n.n(r),a=n(1),o=n.n(a),c=n(0),s=n(10),l=n(137),u=n(70),d=n(58),f=n(207),g={SUCCESS:"success",WARNING:"warning",INFO:"info"},p=Object(c.forwardRef)((function(t,n){var r=t.title,a=t.description,o=t.Icon,c=t.ctaLink,p=t.ctaLabel,m=t.className,b=t.onCTAClick,h=t.isCTALinkExternal,v=t.dismissLabel,O=t.onDismiss,E=t.variant,_=void 0===E?g.SUCCESS:E,y=t.hideIcon,k=void 0!==y&&y;return e.createElement("div",{ref:n,className:i()("googlesitekit-subtle-notification",{"googlesitekit-subtle-notification--success":_===g.SUCCESS,"googlesitekit-subtle-notification--warning":_===g.WARNING,"googlesitekit-subtle-notification--info":_===g.INFO},m)},!k&&e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},o&&e.createElement(o,{width:24,height:24}),!o&&_===g.SUCCESS&&e.createElement(l.a,{width:24,height:24}),!o&&_===g.WARNING&&e.createElement(d.a,{width:24,height:24}),!o&&_===g.INFO&&e.createElement(f.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,r),a&&e.createElement("p",{className:"googlesitekit-subtle-notification__secondary_description"},a)),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},v&&e.createElement(s.Button,{tertiary:!0,onClick:O},v),p&&e.createElement(s.Button,{className:"googlesitekit-subtle-notification__cta",href:c,onClick:b,target:h?"_blank":"_self",trailingIcon:h?e.createElement(u.a,{width:14,height:14}):void 0},p)))}));p.propTypes={title:o.a.node.isRequired,description:o.a.string,Icon:o.a.elementType,ctaLink:o.a.string,ctaLabel:o.a.string,className:o.a.string,onCTAClick:o.a.func,isCTALinkExternal:o.a.bool,dismissLabel:o.a.string,onDismiss:o.a.func,variant:o.a.oneOf(Object.values(g)),hideIcon:o.a.bool},t.b=p}).call(this,n(4))},29:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/forms"},3:function(e,t){e.exports=googlesitekit.data},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(22),i=n(18);function a(){var e=Object(i.a)();return r.g.includes(e)}},35:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return d}));n(14);var r=n(2),i="missing_required_scopes",a="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===i}function s(e){var t;return[a,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function l(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||s(e)||c(e)||l(e))}function d(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(r.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(r.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},353:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i}));var r=n(0);function i(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;Object(r.useEffect)((function(){var r,i=!1,a=function(){r=e.setTimeout((function(){i=!0}),n)},o=function(){e.clearTimeout(r),i&&(i=!1,t())};return e.addEventListener("focus",o),e.addEventListener("blur",a),function(){e.removeEventListener("focus",o),e.removeEventListener("blur",a),e.clearTimeout(r)}}),[n,t])}}).call(this,n(28))},354:function(e,t,n){"use strict";n.d(t,"g",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"b",(function(){return o})),n.d(t,"e",(function(){return c})),n.d(t,"f",(function(){return s})),n.d(t,"a",(function(){return l})),n.d(t,"d",(function(){return u}));var r=n(2),i={post_types:Object(r.__)("Specific content types","google-site-kit"),per_post:Object(r.__)("Specified pages","google-site-kit"),sitewide:Object(r.__)("Site wide","google-site-kit")},a="rrm-product-id-open-access-notice",o="rrm-product-id-info-notice",c="rrm-setup-notification",s="setup-success-notification-rrm",l="rrm-product-id-contributions-notification",u="rrm-product-id-subscriptions-notification"},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return E})),n.d(t,"b",(function(){return v})),n.d(t,"c",(function(){return O}));var r=n(99),i=e._googlesitekitTrackingData||{},a=i.activeModules,o=void 0===a?[]:a,c=i.isSiteKitScreen,s=i.trackingEnabled,l=i.trackingID,u=i.referenceSiteURL,d=i.userIDHash,f=i.isAuthenticated,g={activeModules:o,trackingEnabled:s,trackingID:l,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:i.userRoles,isAuthenticated:f,pluginVersion:"1.151.0"},p=Object(r.a)(g),m=p.enableTracking,b=p.disableTracking,h=(p.isTrackingEnabled,p.initializeSnippet),v=p.trackEvent,O=p.trackEventOnce;function E(e){e?m():b()}c&&s&&h()}).call(this,n(28))},363:function(e,t){e.exports=googlesitekit.notifications},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return v})),n.d(t,"f",(function(){return O})),n.d(t,"c",(function(){return E})),n.d(t,"e",(function(){return _})),n.d(t,"b",(function(){return y}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u,d="googlesitekit_",f="".concat(d).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),g=["sessionStorage","localStorage"],p=[].concat(g),m=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,a="__storage_test__",r.setItem(a,a),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function b(){return h.apply(this,arguments)}function h(){return(h=o()(i.a.mark((function t(){var n,r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=s(p),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(a=r.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,m(a);case 11:if(!t.sent){t.next=13;break}u=e[a];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var v=function(){var e=o()(i.a.mark((function e(t){var n,r,a,o,c,s,l;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(f).concat(t)))){e.next=10;break}if(a=JSON.parse(r),o=a.timestamp,c=a.ttl,s=a.value,l=a.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:l});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),O=function(){var t=o()(i.a.mark((function t(n,r){var a,o,s,l,u,d,g,p,m=arguments;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=m.length>2&&void 0!==m[2]?m[2]:{},o=a.ttl,s=void 0===o?c.b:o,l=a.timestamp,u=void 0===l?Math.round(Date.now()/1e3):l,d=a.isError,g=void 0!==d&&d,t.next=3,b();case 3:if(!(p=t.sent)){t.next=14;break}return t.prev=5,p.setItem("".concat(f).concat(n),JSON.stringify({timestamp:u,ttl:s,value:r,isError:g})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),E=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,a=n.startsWith(d)?n:"".concat(f).concat(n),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),_=function(){var t=o()(i.a.mark((function t(){var n,r,a,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],a=0;a<n.length;a++)0===(o=n.key(a)).indexOf(d)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),y=function(){var e=o()(i.a.mark((function e(){var t,n,r,a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!e.sent){e.next=25;break}return e.next=6,_();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return a=r.value,e.next=14,E(a);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},389:function(e,t,n){"use strict";var r=n(912);n.d(t,"d",(function(){return r.a}));var i=n(913);n.d(t,"c",(function(){return i.a}));var a=n(914);n.d(t,"e",(function(){return a.a}));var o=n(915);n.d(t,"a",(function(){return o.a}));var c=n(918);n.d(t,"b",(function(){return c.a}));var s=n(919);n.d(t,"f",(function(){return s.a}))},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="_googlesitekitDataLayer",i="data-googlesitekit-gtag"},41:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(22),i="core/notifications",a={ERRORS:"notification-area-errors",BANNERS_ABOVE_NAV:"notification-area-banners-above-nav",BANNERS_BELOW_NAV:"notification-area-banners-below-nav"},o={DEFAULT:"default",SETUP_CTAS:"setup-ctas"},c=[r.s,r.n,r.l,r.o,r.m]},42:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"k",(function(){return c})),n.d(t,"b",(function(){return s})),n.d(t,"g",(function(){return l})),n.d(t,"f",(function(){return u})),n.d(t,"i",(function(){return d})),n.d(t,"h",(function(){return f})),n.d(t,"j",(function(){return g}));var r="non_https_site",i="modules/reader-revenue-manager",a="reader-revenue-manager",o={ONBOARDING_COMPLETE:"ONBOARDING_COMPLETE",ONBOARDING_ACTION_REQUIRED:"ONBOARDING_ACTION_REQUIRED",PENDING_VERIFICATION:"PENDING_VERIFICATION",UNSPECIFIED:"ONBOARDING_STATE_UNSPECIFIED"},c="READER_REVENUE_MANAGER_SHOW_PUBLICATION_APPROVED_NOTIFICATION",s="rrm_module_setup_banner_dismissed_key",l="readerRevenueManagerSetupForm",u="readerRevenueManagerNoticesForm",d="showPublicationCreate",f="resetPublications",g="syncPublication"},45:function(e,t){e.exports=googlesitekit.api},48:function(e,t,n){"use strict";n.d(t,"a",(function(){return E}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(12),s=n.n(c),l=n(14),u=n(64),d=n(82),f=n(9);function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var m=function(e){return e},b=function(){return{}},h=function(){},v=u.a.clearError,O=u.a.receiveError,E=function(e){var t,n,r=i.a.mark(I),a=e.baseName,c=e.controlCallback,u=e.reducerCallback,g=void 0===u?m:u,E=e.argsToParams,_=void 0===E?b:E,y=e.validateParams,k=void 0===y?h:y;s()(a,"baseName is required."),s()("function"==typeof c,"controlCallback is required and must be a function."),s()("function"==typeof g,"reducerCallback must be a function."),s()("function"==typeof _,"argsToParams must be a function."),s()("function"==typeof k,"validateParams must be a function.");try{k(_()),n=!1}catch(e){n=!0}var j=Object(d.b)(a),S=Object(d.a)(a),w="FETCH_".concat(S),x="START_".concat(w),C="FINISH_".concat(w),N="CATCH_".concat(w),D="RECEIVE_".concat(S),A="fetch".concat(j),P="receive".concat(j),T="isFetching".concat(j),R=o()({},T,{});function I(e,t){var n,o;return i.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,{payload:{params:e},type:x};case 2:return r.next=4,v(a,t);case 4:return r.prev=4,r.next=7,{payload:{params:e},type:w};case 7:return n=r.sent,r.next=10,F[P](n,e);case 10:return r.next=12,{payload:{params:e},type:C};case 12:r.next=21;break;case 14:return r.prev=14,r.t0=r.catch(4),o=r.t0,r.next=19,O(o,a,t);case 19:return r.next=21,{payload:{params:e},type:N};case 21:return r.abrupt("return",{response:n,error:o});case 22:case"end":return r.stop()}}),r,null,[[4,14]])}var F=(t={},o()(t,A,(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=_.apply(void 0,t);return k(r),I(r,t)})),o()(t,P,(function(e,t){return s()(void 0!==e,"response is required."),n?(s()(Object(l.isPlainObject)(t),"params is required."),k(t)):t={},{payload:{response:e,params:t},type:D}})),t),L=o()({},w,(function(e){var t=e.payload;return c(t.params)})),M=o()({},T,(function(e){if(void 0===e[T])return!1;var t;try{for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];t=_.apply(void 0,r),k(t)}catch(e){return!1}return!!e[T][Object(f.H)(t)]}));return{initialState:R,actions:F,controls:L,reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case x:var i=r.params;return p(p({},e),{},o()({},T,p(p({},e[T]),{},o()({},Object(f.H)(i),!0))));case D:var a=r.response,c=r.params;return g(e,a,c);case C:var s=r.params;return p(p({},e),{},o()({},T,p(p({},e[T]),{},o()({},Object(f.H)(s),!1))));case N:var l=r.params;return p(p({},e),{},o()({},T,p(p({},e[T]),{},o()({},Object(f.H)(l),!1))));default:return e}},resolvers:{},selectors:M}}},50:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(3),i=n(19),a=n(82);function o(t){var n=t.moduleName,o=t.FallbackComponent,c=t.IncompleteComponent;return function(t){function WhenActiveComponent(a){var s=Object(r.useSelect)((function(e){return e(i.a).getModule(n)}),[n]);if(!s)return null;var l=o||a.WidgetNull||null;if(!1===s.active)return l&&e.createElement(l,a);if(!1===s.connected){var u=c||l;return u&&e.createElement(u,a)}return e.createElement(t,a)}return WhenActiveComponent.displayName="When".concat(Object(a.c)(n),"Active"),(t.displayName||t.name)&&(WhenActiveComponent.displayName+="(".concat(t.displayName||t.name,")")),WhenActiveComponent}}}).call(this,n(4))},513:function(e,t,n){"use strict";n(520),n(521);var r=n(557);n.d(t,"a",(function(){return r.a}));var i=n(558);n.d(t,"d",(function(){return i.a}));var a=n(559);n.d(t,"c",(function(){return a.a}));var o=n(560);n.d(t,"b",(function(){return o.a}))},515:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var r=n(2);function i(e,t){if(!Array.isArray(e)||0===e.length)return"";if(!Array.isArray(t)||0===t.length)return e.join(", ");var n=t.filter((function(t){return e.includes(t.slug)}));return n.length===t.length?Object(r.__)("All post types","google-site-kit"):n.map((function(e){return e.label})).join(", ")}function a(e){if(!e)return"";var t=e.indexOf(":");return-1===t?e:e.substring(t+1)}},52:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(22),i=n(18),a=r.n,o=r.l;function c(){var e=Object(i.a)();return e===r.n||e===r.o?a:e===r.l||e===r.m?o:null}},520:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ProductIDContributionsNotification}));var r=n(1),i=n.n(r),a=n(2),o=n(3),c=n(13),s=n(115),l=n(92),u=n(168);function ProductIDContributionsNotification(t){var n=t.id,r=t.Notification,i=Object(o.useSelect)((function(e){return e(c.c).getAdminURL("googlesitekit-settings")}));return e.createElement(r,null,e.createElement(s.a,{type:"new-feature",description:Object(a.__)("New! You can now select product IDs to use with your Reader Revenue Manager snippet","google-site-kit"),dismissCTA:e.createElement(l.a,{id:n,primary:!1,dismissLabel:Object(a.__)("Got it","google-site-kit")}),additionalCTA:e.createElement(u.a,{id:n,ctaLabel:Object(a.__)("Edit settings","google-site-kit"),ctaLink:"".concat(i,"#connected-services/reader-revenue-manager/edit")})}))}ProductIDContributionsNotification.propTypes={id:i.a.string.isRequired,Notification:i.a.elementType.isRequired}}).call(this,n(4))},521:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ProductIDSubscriptionsNotification}));var r=n(1),i=n.n(r),a=n(2),o=n(3),c=n(13),s=n(115),l=n(92),u=n(168);function ProductIDSubscriptionsNotification(t){var n=t.id,r=t.Notification,i=Object(o.useSelect)((function(e){return e(c.c).getAdminURL("googlesitekit-settings")}));return e.createElement(r,null,e.createElement(s.a,{type:"warning",description:Object(a.__)("To complete your Reader Revenue Manager paywall setup, add your product IDs in settings","google-site-kit"),dismissCTA:e.createElement(l.a,{id:n,primary:!1,dismissLabel:Object(a.__)("Got it","google-site-kit")}),additionalCTA:e.createElement(u.a,{id:n,ctaLabel:Object(a.__)("Edit settings","google-site-kit"),ctaLink:"".concat(i,"#connected-services/reader-revenue-manager/edit")})}))}ProductIDSubscriptionsNotification.propTypes={id:i.a.string.isRequired,Notification:i.a.elementType.isRequired}}).call(this,n(4))},54:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,r=t.reconnectURL,i=t.noPrefix;if(!n)return null;var s=n;void 0!==i&&i||(s=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),r&&Object(a.a)(r)&&(s=s+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),r));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(s,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:i.a.string.isRequired,reconnectURL:i.a.string,noPrefix:i.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},557:function(e,t,n){"use strict";(function(e){var r=n(0),i=n(2),a=n(204),o=n(569),c=n(570),s=n(52),l=n(18),u=n(34),d=n(70),f=n(9),g=n(10),p=n(3),m=n(7),b=n(23),h=n(22),v=n(42),O=n(50),E=v.d.ONBOARDING_COMPLETE;t.a=Object(O.a)({moduleName:v.e})((function PublicationApprovedOverlayNotification(){var t=Object(l.a)(),n=Object(u.a)(),O=Object(s.c)(),_=Object(p.useDispatch)(v.c),y=_.saveSettings,k=_.setPublicationOnboardingStateChanged,j=Object(p.useSelect)((function(e){return e(v.c).getSettings()||{}})),S=j.publicationID,w=j.publicationOnboardingState,x=j.publicationOnboardingStateChanged,C=Object(p.useSelect)((function(e){return e(v.c).hasFinishedResolution("getSettings")})),N=Object(r.useRef)(),D=Object(p.useSelect)((function(e){return e(m.a).isItemDismissed("rrmPublicationApprovedOverlayNotification")})),A=Object(p.useSelect)((function(e){return e(v.c).getServiceURL({path:"reader-revenue-manager",query:{publication:S}})})),P=Object(p.useSelect)((function(e){return e(b.b).getValue(v.k)})),T=!1===D&&!n&&O===h.n&&(!0===P||!0===N.current&&w===E),R=Object(p.useSelect)((function(e){return e(m.a).isDismissingItem("rrmPublicationApprovedOverlayNotification")})),I=Object(p.useDispatch)(b.b).dismissOverlayNotification,F=function(){I("rrmPublicationApprovedOverlayNotification")};return Object(r.useEffect)((function(){C&&void 0===N.current&&(N.current=x,!0===x&&(k(!1),y()))}),[x,y,k,C]),e.createElement(a.a,{className:"googlesitekit-reader-revenue-manager-overlay-notification googlesitekit-reader-revenue-manager-publication-approved-notification",GraphicDesktop:o.a,GraphicMobile:c.a,onShow:function(){Object(f.I)("".concat(t,"_rrm-publication-approved-notification"),"view_notification")},shouldShowNotification:T,notificationID:"rrmPublicationApprovedOverlayNotification"},e.createElement("div",{className:"googlesitekit-overlay-notification__body"},e.createElement("h3",null,Object(i.__)("Your Reader Revenue Manager publication is approved","google-site-kit")),e.createElement("p",null,Object(i.__)("Unlock your full reader opportunity by enabling features like paywall, subscriptions, contributions and newsletter sign ups.","google-site-kit"))),e.createElement("div",{className:"googlesitekit-overlay-notification__actions"},e.createElement(g.Button,{tertiary:!0,disabled:R,onClick:function(){Object(f.I)("".concat(t,"_rrm-publication-approved-notification"),"dismiss_notification").finally((function(){F()}))}},Object(i.__)("Maybe later","google-site-kit")),e.createElement(g.Button,{disabled:R,href:A,onClick:function(){Object(f.I)("".concat(t,"_rrm-publication-approved-notification"),"confirm_notification").finally((function(){F()}))},trailingIcon:e.createElement(d.a,{width:13,height:13}),target:"_blank"},Object(i.__)("Enable features","google-site-kit"))))}))}).call(this,n(4))},558:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReaderRevenueManagerSetupCTABanner}));var r=n(6),i=n.n(r),a=n(1),o=n.n(a),c=n(0),s=n(38),l=n(2),u=n(3),d=n(24),f=n(159),g=n(7),p=n(42),m=n(571),b=n(572),h=n(573),v=n(175),O=n(41),E=n(242),_=n(180),y=n(179),k=n(9);function ReaderRevenueManagerSetupCTABanner(t){var n,r=t.id,a=t.Notification,o=Object(d.e)(),j=Object(f.a)(p.e),S={tooltipSlug:"rrm-setup-notification",content:Object(l.__)("You can always enable Reader Revenue Manager in Settings later","google-site-kit"),dismissLabel:Object(l.__)("Got it","google-site-kit")},w=Object(v.b)(S),x=Object(u.useDispatch)(g.a).triggerSurvey,C=Object(u.useSelect)((function(e){return e(O.a).isNotificationDismissalFinal(r)}));Object(c.useEffect)((function(){x("view_reader_revenue_manager_cta")}),[x]);var N=(n={},i()(n,d.b,h.a),i()(n,d.c,b.a),n);return e.createElement(a,null,e.createElement(E.a,{id:r,title:Object(l.__)("Grow your revenue and deepen reader engagement","google-site-kit"),description:e.createElement("div",{className:"googlesitekit-setup-cta-banner__description"},e.createElement("p",null,Object(s.a)(Object(l.__)("Turn casual visitors into loyal readers and earn more from your content with paywalls, contributions, surveys, newsletter sign-ups and reader insight tools. <a>Learn more</a>","google-site-kit"),{a:e.createElement(_.a,{id:r,label:Object(l.__)("Learn more","google-site-kit"),url:"https://readerrevenue.withgoogle.com"})}))),actions:e.createElement(y.a,{id:r,className:"googlesitekit-setup-cta-banner__actions-wrapper",ctaLabel:Object(l.__)("Set up Reader Revenue Manager","google-site-kit"),onCTAClick:j,dismissLabel:C?Object(l.__)("Don’t show again","google-site-kit"):Object(l.__)("Maybe later","google-site-kit"),onDismiss:w,dismissExpires:2*k.f}),SVG:N[o]||m.a}))}ReaderRevenueManagerSetupCTABanner.propTypes={id:o.a.string,Notification:o.a.elementType}}).call(this,n(4))},559:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return RRMSetupSuccessSubtleNotification}));var i=n(21),a=n.n(i),o=n(5),c=n.n(o),s=n(16),l=n.n(s),u=n(6),d=n.n(u),f=n(15),g=n.n(f),p=n(0),m=n(38),b=n(2),h=n(3),v=n(147),O=n(353),E=n(29),_=n(13),y=n(23),k=n(42),j=n(180),S=n(115),w=n(168),x=n(92),C=k.d.ONBOARDING_COMPLETE,N=k.d.PENDING_VERIFICATION,D=k.d.ONBOARDING_ACTION_REQUIRED;function RRMSetupSuccessSubtleNotification(t){var n=t.id,i=t.Notification,o=Object(v.a)("notification"),s=g()(o,2),u=s[0],f=s[1],A=Object(v.a)("slug"),P=g()(A,2),T=P[0],R=P[1],I=[N,D],F=Object(h.useSelect)((function(e){return e(k.c).getPublicationOnboardingState()})),L=Object(h.useSelect)((function(e){return e(k.c).getPublicationID()})),M=Object(h.useSelect)((function(e){return e(k.c).getServiceURL({path:"reader-revenue-manager",query:{publication:L}})})),B=Object(h.useSelect)((function(e){return e(E.a).getValue(k.f,k.j)&&I.includes(F)})),G=Object(h.useSelect)((function(e){return e(k.c).getPublicationOnboardingState()})),U=Object(h.useSelect)((function(e){return e(k.c).getPaymentOption()})),z=Object(h.useSelect)((function(e){return e(k.c).getProductID()})),q=Object(h.useSelect)((function(e){return e(k.c).getProductIDs()})),V=Object(h.useSelect)((function(e){return e(_.c).getAdminURL("googlesitekit-settings")})),H=Object(h.useDispatch)(E.a).setValues,W=Object(h.useDispatch)(y.b).setValue,K=Object(h.useDispatch)(k.c).syncPublicationOnboardingState,Y=Object(p.useCallback)((function(){f(void 0),R(void 0)}),[f,R]),$=function(t){t.preventDefault(),I.includes(F)&&H(k.f,d()({},k.j,!0)),e.open(M,"_blank")},J=Object(p.useCallback)(l()(c.a.mark((function e(){var t,n,r;return c.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(B){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,K();case 4:t=e.sent,n=t.response,r=null==n?void 0:n.publicationOnboardingState,G&&r!==G&&r===k.d.ONBOARDING_COMPLETE&&W(k.k,!0);case 8:case"end":return e.stop()}}),e)}))),[G,W,B,K]);Object(O.a)(J,15e3);var Q="authentication_success"===u&&T===k.e;Object(p.useEffect)((function(){Q&&F===C&&""===U&&(W(k.k,!0),Y())}),[Y,U,F,W,Q]);var X=!!z&&"openaccess"!==z,Z={gaTrackingEventArgs:{label:"".concat(F,":").concat(U,":").concat(X?"yes":"no")}};if(F===N)return r.createElement(i,Z,r.createElement(S.a,{title:Object(b.__)("Your Reader Revenue Manager account was successfully set up!","google-site-kit"),description:Object(b.__)("Your publication is still awaiting review, you can check its status in Reader Revenue Manager.","google-site-kit"),dismissCTA:r.createElement(x.a,a()({id:n,primary:!1,dismissLabel:Object(b.__)("Got it","google-site-kit"),onDismiss:Y},Z)),additionalCTA:r.createElement(w.a,a()({id:n,ctaLabel:Object(b.__)("Check publication status","google-site-kit"),ctaLink:M,onCTAClick:$,isCTALinkExternal:!0},Z))}));if(F===D)return r.createElement(i,Z,r.createElement(S.a,{title:Object(b.__)("Your Reader Revenue Manager account was successfully set up, but your publication still requires further setup in Reader Revenue Manager.","google-site-kit"),dismissCTA:r.createElement(x.a,a()({id:n,primary:!1,dismissLabel:Object(b.__)("Got it","google-site-kit"),onDismiss:Y},Z)),additionalCTA:r.createElement(w.a,a()({id:n,ctaLabel:Object(b.__)("Complete publication setup","google-site-kit"),ctaLink:M,onCTAClick:$,isCTALinkExternal:!0},Z)),type:"warning"}));if(F===C){if(""===U)return null;var ee={title:Object(b.__)("Success! Your Reader Revenue Manager account is set up","google-site-kit"),description:"",primaryButton:{text:Object(b.__)("Manage CTAs","google-site-kit"),ctaLink:"".concat(V,"#connected-services/reader-revenue-manager/edit"),isCTALinkExternal:!1},secondaryButton:{text:Object(b.__)("Got it","google-site-kit"),onClick:Y}};switch(U){case"subscriptions":ee.description="openaccess"===z?Object(b.__)("You can edit your settings to manage product IDs and select which of your site’s pages will include a subscription CTA.","google-site-kit"):Object(b.__)("You can edit your settings and select which of your site’s pages will include a subscription CTA.","google-site-kit");break;case"contributions":q.length>0&&"openaccess"===z?ee.description=Object(b.__)("You can edit your settings to manage product IDs and select which of your site’s pages will include a contribution CTA.","google-site-kit"):ee.description=Object(b.__)("You can edit your settings and select which of your site’s pages will include a contribution CTA.","google-site-kit");break;case"noPayment":ee.description=Object(m.a)(Object(b.__)("Explore Reader Revenue Manager’s additional features, such as paywalls, subscriptions and contributions. <a>Learn more</a>","google-site-kit"),{a:r.createElement(j.a,a()({id:n,ariaLabel:Object(b.__)("Learn more about Reader Revenue Manager features","google-site-kit"),label:Object(b.__)("Learn more","google-site-kit"),url:"https://support.google.com/news/publisher-center/answer/12813936",hideExternalIndicator:!0},Z))}),ee.primaryButton={text:Object(b.__)("Get started","google-site-kit"),ctaLink:M,isCTALinkExternal:!0}}return r.createElement(i,Z,r.createElement(S.a,{title:ee.title,description:ee.description,dismissCTA:r.createElement(x.a,a()({id:n,primary:!1,dismissLabel:ee.secondaryButton.text,onDismiss:ee.secondaryButton.onClick},Z)),additionalCTA:r.createElement(w.a,a()({id:n,ctaLabel:ee.primaryButton.text,ctaLink:ee.primaryButton.ctaLink,isCTALinkExternal:ee.primaryButton.isCTALinkExternal},Z))}))}return null}}).call(this,n(28),n(4))},560:function(e,t,n){"use strict";(function(e){var r=n(15),i=n.n(r),a=n(38),o=n(2),c=n(10),s=n(70),l=n(204),u=n(574),d=n(575),f=n(199),g=n(52),p=n(3),m=n(147),b=n(18),h=n(34),v=n(50),O=n(9),E=n(23),_=n(7),y=n(42),k=n(22),j=n(13),S=y.d.ONBOARDING_COMPLETE;t.a=Object(v.a)({moduleName:y.e})((function RRMIntroductoryOverlayNotification(){var t=Object(h.a)(),n=Object(g.c)(),r=Object(b.a)(),v=Object(m.a)("notification"),w=i()(v,1)[0],x=Object(m.a)("slug"),C=i()(x,1)[0],N=Object(p.useSelect)((function(e){return e(y.c).getSettings()||{}})),D=N.publicationID,A=N.publicationOnboardingState,P=N.paymentOption,T=Object(p.useSelect)((function(e){return e(_.a).isItemDismissed("rrmIntroductoryOverlayNotification")})),R=Object(p.useSelect)((function(e){return e(_.a).isDismissingItem("rrmIntroductoryOverlayNotification")})),I=Object(p.useSelect)((function(e){return e(y.c).getServiceURL({path:"reader-revenue-manager",query:{publication:D}})})),F=Object(p.useSelect)((function(e){return e(j.c).getGoogleSupportURL({path:"/news/publisher-center/answer/********"})})),L=Object(p.useDispatch)(E.b).dismissOverlayNotification,M="authentication_success"===w&&C===y.e,B=!1===T&&!t&&n===k.n&&!M&&A===S&&["noPayment",""].includes(P),G=function(){L("rrmIntroductoryOverlayNotification")},U="".concat(r,"_rrm-introductory-notification"),z="".concat(A,":").concat(P||"");return e.createElement(l.a,{className:"googlesitekit-reader-revenue-manager-overlay-notification googlesitekit-reader-revenue-manager-introductory-notification",GraphicDesktop:u.a,GraphicMobile:d.a,shouldShowNotification:B,notificationID:"rrmIntroductoryOverlayNotification",onShow:function(){Object(O.I)(U,"view_notification",z)}},e.createElement("div",{className:"googlesitekit-overlay-notification__body"},e.createElement("h3",null,"noPayment"===P?Object(o.__)("New! Monetize your content with Reader Revenue Manager","google-site-kit"):Object(o.__)("Complete account setup with Reader Revenue Manager","google-site-kit")),e.createElement("p",null,"noPayment"===P?Object(a.a)(Object(o.__)("Now you can offer your users subscription options to access content behind a paywall, or make voluntary contributions. <a>Learn more</a>","google-site-kit"),{a:e.createElement(f.a,{path:"/news/publisher-center/answer/********",external:!0,hideExternalIndicator:!0,onClick:function(){Object(O.I)(U,"click_learn_more_link",z)}})}):Object(o.__)("Easily monetize your content by offering users subscription options to access content behind a paywall, or make voluntary contributions.","google-site-kit"))),e.createElement("div",{className:"googlesitekit-overlay-notification__actions"},e.createElement(c.Button,{tertiary:!0,disabled:R,onClick:function(){Object(O.I)(U,"dismiss_notification",z).finally((function(){G()}))}},Object(o.__)("Maybe later","google-site-kit")),e.createElement(c.Button,{disabled:R,href:"noPayment"===P?I:F,onClick:function(){Object(O.I)(U,"confirm_notification",z).finally((function(){G()}))},trailingIcon:e.createElement(s.a,{width:13,height:13}),target:"_blank"},"noPayment"===P?Object(o.__)("Explore features","google-site-kit"):Object(o.__)("Learn more","google-site-kit"))))}))}).call(this,n(4))},569:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#reader-revenue-manager-introductory-graphic-desktop_svg__clip0_192_4045)"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h316c8.837 0 16 7.163 16 16v147H0V16z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#reader-revenue-manager-introductory-graphic-desktop_svg__filter0_d_192_4045)"},r.createElement("rect",{x:101.5,y:79,width:195,height:172,rx:13.764,fill:"#fff"})),r.createElement("rect",{x:218,y:108,width:64,height:38,rx:5.161,fill:"#EBEEF0"}),r.createElement("g",{filter:"url(#reader-revenue-manager-introductory-graphic-desktop_svg__filter1_d_192_4045)"},r.createElement("rect",{x:81.5,y:56,width:195,height:172,rx:13.764,fill:"#fff"})),r.createElement("rect",{x:97,y:85,width:165,height:96,rx:5.161,fill:"#EBEEF0"}),r.createElement("rect",{x:96,y:148,width:59,height:55,rx:5.457,fill:"#EBEEF0"}),r.createElement("rect",{x:167,y:148,width:54,height:10,rx:5,fill:"#EBEEF0"}),r.createElement("g",{filter:"url(#reader-revenue-manager-introductory-graphic-desktop_svg__filter2_d_192_4045)"},r.createElement("rect",{x:61.5,y:30,width:195,height:172,rx:13.764,fill:"#fff"})),r.createElement("rect",{x:76.5,y:50,width:165,height:51,rx:5.161,fill:"#6FD3D3"}),r.createElement("rect",{x:147.5,y:113,width:54,height:14,rx:7,fill:"#EBEEF0"}),r.createElement("rect",{x:147.5,y:135,width:94,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("rect",{x:76.5,y:135,width:59,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("rect",{x:76.5,y:124,width:59,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("rect",{x:76.5,y:146,width:59,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("rect",{x:76.5,y:113,width:59,height:5,rx:2.5,fill:"#EBEEF0"}),r.createElement("rect",{x:147.5,y:146,width:94,height:5,rx:2.5,fill:"#EBEEF0"})),o=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-introductory-graphic-desktop_svg__filter0_d_192_4045",x:91.5,y:73,width:215,height:192,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:5}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_192_4045"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_192_4045",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-introductory-graphic-desktop_svg__filter1_d_192_4045",x:65.5,y:44,width:227,height:204,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_192_4045"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_192_4045",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-introductory-graphic-desktop_svg__filter2_d_192_4045",x:45.5,y:18,width:227,height:204,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_192_4045"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_192_4045",result:"shape"})),r.createElement("clipPath",{id:"reader-revenue-manager-introductory-graphic-desktop_svg__clip0_192_4045"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h316c8.837 0 16 7.163 16 16v147H0V16z",fill:"#fff"})));t.a=function SvgReaderRevenueManagerIntroductoryGraphicDesktop(e){return r.createElement("svg",i({viewBox:"0 0 348 163",fill:"none"},e),a,o)}},57:function(e,t,n){"use strict";(function(e){var r,i;n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=new Set((null===(r=e)||void 0===r||null===(i=r._googlesitekitBaseData)||void 0===i?void 0:i.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,n(28))},570:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#reader-revenue-manager-introductory-graphic-mobile_svg__clip0_584_3892)"},r.createElement("path",{d:"M29.447 64.365c-2.4 22.73 4.803 32.78 23.025 59.949 18.222 27.17-7.404 59.277 20.78 89.869 33.527 36.394 150.685 39.364 201.231 24.212 50.546-15.153 63.581-46.473 59.948-75.155C329.5 124.314 302.482 112.077 289.5 93c-19.276-28.325 2.813-54.786-34.5-77.5s-80.086 6.697-120.326 4.388c-23.216-1.332-46.017-5.627-66.626.968-20.832 6.667-36.72 25.428-38.6 43.509z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#reader-revenue-manager-introductory-graphic-mobile_svg__filter0_d_584_3892)"},r.createElement("rect",{x:108.926,y:53.721,width:158.074,height:139.43,rx:11.158,fill:"#fff"})),r.createElement("rect",{x:203.365,y:77.23,width:51.881,height:30.804,rx:4.184,fill:"#EBEEF0"}),r.createElement("g",{filter:"url(#reader-revenue-manager-introductory-graphic-mobile_svg__filter1_d_584_3892)"},r.createElement("rect",{x:92.713,y:35.077,width:158.074,height:139.43,rx:11.158,fill:"#fff"})),r.createElement("rect",{x:105.277,y:58.585,width:133.755,height:77.821,rx:4.184,fill:"#EBEEF0"}),r.createElement("rect",{x:104.467,y:109.655,width:47.828,height:44.585,rx:4.424,fill:"#EBEEF0"}),r.createElement("rect",{x:162.021,y:109.655,width:43.775,height:8.106,rx:4.053,fill:"#EBEEF0"}),r.createElement("g",{filter:"url(#reader-revenue-manager-introductory-graphic-mobile_svg__filter2_d_584_3892)"},r.createElement("rect",{x:76.5,y:14,width:158.074,height:139.43,rx:11.158,fill:"#fff"})),r.createElement("rect",{x:88.66,y:30.213,width:133.755,height:41.343,rx:4.184,fill:"#6FD3D3"}),r.createElement("rect",{x:146.215,y:81.283,width:43.775,height:11.349,rx:5.674,fill:"#EBEEF0"}),r.createElement("rect",{x:146.215,y:99.117,width:76.2,height:4.053,rx:2.027,fill:"#EBEEF0"}),r.createElement("rect",{x:88.66,y:99.117,width:47.828,height:4.053,rx:2.027,fill:"#EBEEF0"}),r.createElement("rect",{x:88.66,y:90.2,width:47.828,height:4.053,rx:2.027,fill:"#EBEEF0"}),r.createElement("rect",{x:88.66,y:108.034,width:47.828,height:4.053,rx:2.027,fill:"#EBEEF0"}),r.createElement("rect",{x:88.66,y:81.283,width:47.828,height:4.053,rx:2.027,fill:"#EBEEF0"}),r.createElement("rect",{x:146.215,y:108.034,width:76.2,height:4.053,rx:2.027,fill:"#EBEEF0"})),o=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-introductory-graphic-mobile_svg__filter0_d_584_3892",x:95.926,y:43.721,width:184.074,height:165.43,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3}),r.createElement("feGaussianBlur",{stdDeviation:6.5}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_584_3892"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_584_3892",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-introductory-graphic-mobile_svg__filter1_d_584_3892",x:79.743,y:25.349,width:184.015,height:165.37,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.243}),r.createElement("feGaussianBlur",{stdDeviation:6.485}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_584_3892"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_584_3892",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-introductory-graphic-mobile_svg__filter2_d_584_3892",x:63.53,y:4.272,width:184.015,height:165.37,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.243}),r.createElement("feGaussianBlur",{stdDeviation:6.485}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_584_3892"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_584_3892",result:"shape"})),r.createElement("clipPath",{id:"reader-revenue-manager-introductory-graphic-mobile_svg__clip0_584_3892"},r.createElement("path",{fill:"#fff",d:"M0 0h343v118H0z"})));t.a=function SvgReaderRevenueManagerIntroductoryGraphicMobile(e){return r.createElement("svg",i({viewBox:"0 0 343 118",fill:"none"},e),a,o)}},571:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M74.032-2.916C70.583 36.886 92.52 48.59 92.52 103.233c0 54.644-91.918 88.181-52.084 179.603 31.917 73.252 194.23 87.748 287.396 67.074 93.167-20.673 127.301-72.44 122.081-122.662-7.085-68.16-44.978-103.341-59.664-164.902-14.686-61.56 10.917-93.72-27.996-124.067-39.469-30.78-75.267-11.025-116.573-11.025-33.439 0-66.088-25.668-114.278-6.874-29.933 11.674-54.665 45.044-57.369 76.704z",fill:"#B8E6CA"}),o=r.createElement("g",{filter:"url(#reader-revenue-manager-setup_svg__filter0_d_30_1196)"},r.createElement("rect",{x:168.849,y:39.561,width:136,height:270,rx:20,fill:"#fff"}),r.createElement("circle",{cx:54.898,cy:177.171,r:25.926,fill:"#2F9F9F"}),r.createElement("path",{d:"M60.112 170.211c-.678-.753-2.71-2.258-5.42-2.258-3.389 0-5.422 2.258-5.422 4.517 0 6.212 10.842 2.675 10.842 9.035 0 2.259-2.033 4.517-5.42 4.517-2.711 0-4.744-1.506-5.422-2.258M54.69 163.435v27.105",stroke:"#fff",strokeWidth:2,strokeLinecap:"round"}),r.createElement("circle",{cx:348.514,cy:36.337,r:17.485,fill:"#2F9F9F"}),r.createElement("path",{d:"M352.031 31.643c-.457-.508-1.828-1.523-3.656-1.523-2.285 0-3.656 1.523-3.656 3.046 0 4.19 7.312 1.805 7.312 6.094 0 1.523-1.371 3.047-3.656 3.047-1.828 0-3.199-1.016-3.656-1.524M348.375 27.073v18.28",stroke:"#fff",strokeWidth:1.5,strokeLinecap:"round"}),r.createElement("circle",{cx:418.514,cy:149.337,r:21.515,fill:"#2F9F9F"}),r.createElement("path",{d:"M422.841 143.562c-.562-.625-2.249-1.875-4.499-1.875-2.811 0-4.498 1.875-4.498 3.749 0 5.155 8.997 2.22 8.997 7.497 0 1.875-1.687 3.749-4.499 3.749-2.249 0-3.936-1.249-4.498-1.874M418.343 137.939v22.492",stroke:"#fff",strokeWidth:2,strokeLinecap:"round"}),r.createElement("rect",{x:184.94,y:54.47,width:104.361,height:18.417,rx:4,fill:"#F3F5F7"}),r.createElement("rect",{x:184.94,y:104.458,width:104.361,height:114.008,rx:4,fill:"#F3F5F7"}),r.createElement("rect",{x:184.94,y:228.482,width:104.361,height:7.016,rx:3.508,fill:"#F3F5F7"}),r.createElement("rect",{x:184.94,y:241.482,width:104.361,height:7.016,rx:3.508,fill:"#F3F5F7"}),r.createElement("rect",{x:204.234,y:81.656,width:65.774,height:10.524,rx:5.262,fill:"#F3F5F7"}),r.createElement("rect",{x:184.94,y:82.533,width:13.155,height:1.754,rx:.877,fill:"#DEE3E6"}),r.createElement("rect",{x:184.94,y:86.041,width:13.155,height:1.754,rx:.877,fill:"#DEE3E6"}),r.createElement("rect",{x:184.94,y:89.549,width:13.155,height:1.754,rx:.877,fill:"#DEE3E6"})),c=r.createElement("g",{filter:"url(#reader-revenue-manager-setup_svg__filter1_d_30_1196)"},r.createElement("rect",{x:131.451,y:131.057,width:210,height:105,rx:8,fill:"#fff"}),r.createElement("rect",{x:180.451,y:166.057,width:112,height:5,rx:2.5,fill:"#F3F5F7"}),r.createElement("rect",{x:199.451,y:175.057,width:75,height:5,rx:2.5,fill:"#F3F5F7"}),r.createElement("rect",{x:181.849,y:190.057,width:108,height:20,rx:10,fill:"#6FD3D3"})),s=r.createElement("path",{d:"M236.5 158.266c14.762 0 26.638-12.259 26.638-27.266 0-15.007-11.876-27.266-26.638-27.266-14.762 0-26.638 12.259-26.638 27.266 0 15.007 11.876 27.266 26.638 27.266z",fill:"#6FD3D3",stroke:"#fff",strokeWidth:4.276}),l=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M236.38 123.237l-.038-.009v-.686c0-.408-.157-.751-.472-1.029a1.508 1.508 0 00-1.103-.441c-.437 0-.814.147-1.129.441a1.354 1.354 0 00-.446 1.029v.686c-1.4.327-2.537 1.021-3.412 2.082-.875 1.046-1.313 2.246-1.313 3.602v6.86h-2.1v1.96h16.8v-1.96h-2.1v-6.86c0-.363-.031-.715-.094-1.055a5.002 5.002 0 01-4.593-4.62zm-1.613 17.435a2.169 2.169 0 01-1.496-.564c-.403-.392-.604-.857-.604-1.396h4.2c0 .539-.21 1.004-.63 1.396-.402.376-.892.564-1.47.564zM246.5 121.872h-7v-2h7v2z",fill:"#fff"}),u=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M241.867 124.372v-7h2v7h-2z",fill:"#fff"}),d=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-setup_svg__filter0_d_30_1196",x:12.972,y:6.852,width:443.057,height:322.709,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_30_1196"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_30_1196",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-setup_svg__filter1_d_30_1196",x:115.451,y:123.057,width:242,height:137,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:8}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_30_1196"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_30_1196",result:"shape"})));t.a=function SvgReaderRevenueManagerSetup(e){return r.createElement("svg",i({viewBox:"0 0 479 272",fill:"none"},e),a,o,c,s,l,u,d)}},572:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#reader-revenue-manager-setup-tablet_svg__clip0_545_5344)"},r.createElement("path",{d:"M134.999 170.5c0-40.522-17.5-50-18-88s26-61.5 47-66 23.5-1.302 51.5 0 39.5-17.5 79.5-14.5 45.5 19 69 25.5c23.874 6.603 33.464 1.321 48.5 21.5 19.001 25.5-2.875 51.848 8 97.5 14.77 62-276.999 64-285.5 24z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#reader-revenue-manager-setup-tablet_svg__filter0_d_545_5344)"},r.createElement("rect",{x:234.539,y:17.537,width:86.677,height:172.078,rx:12.746,fill:"#fff"}),r.createElement("circle",{cx:169.523,cy:104.523,r:16.523,fill:"#2F9F9F"}),r.createElement("path",{d:"M172.845 100.088c-.432-.48-1.727-1.44-3.455-1.44-2.159 0-3.454 1.44-3.454 2.879 0 3.959 6.909 1.705 6.909 5.758 0 1.44-1.295 2.879-3.455 2.879-1.727 0-3.023-.959-3.454-1.439M169.391 95.769v17.274",stroke:"#fff",strokeWidth:1.275,strokeLinecap:"round"}),r.createElement("circle",{cx:105.5,cy:70.5,r:22.5,fill:"#2F9F9F"}),r.createElement("path",{d:"M110.022 64.46c-.588-.653-2.352-1.96-4.704-1.96-2.941 0-4.705 1.96-4.705 3.92 0 5.392 9.409 2.322 9.409 7.841 0 1.96-1.764 3.92-4.704 3.92-2.352 0-4.117-1.306-4.705-1.96M105.318 58.58v23.522",stroke:"#fff",strokeWidth:1.275,strokeLinecap:"round"}),r.createElement("circle",{cx:359.048,cy:37.482,r:11.144,fill:"#2F9F9F"}),r.createElement("path",{d:"M361.289 34.49c-.291-.323-1.165-.97-2.33-.97-1.456 0-2.33.97-2.33 1.941 0 2.67 4.66 1.15 4.66 3.884 0 .97-.874 1.942-2.33 1.942-1.165 0-2.039-.648-2.33-.971M358.959 31.578v11.65",stroke:"#fff",strokeWidth:.956,strokeLinecap:"round"}),r.createElement("circle",{cx:411.659,cy:109.5,r:13.712,fill:"#2F9F9F"}),r.createElement("path",{d:"M414.416 105.819c-.359-.398-1.434-1.195-2.867-1.195-1.792 0-2.867 1.195-2.867 2.39 0 3.285 5.734 1.414 5.734 4.778 0 1.195-1.075 2.389-2.867 2.389-1.434 0-2.509-.796-2.867-1.194M411.547 102.235v14.335",stroke:"#fff",strokeWidth:1.275,strokeLinecap:"round"}),r.createElement("circle",{cx:457.288,cy:50.288,r:19.288,fill:"#2F9F9F"}),r.createElement("path",{d:"M461.166 45.11c-.504-.56-2.017-1.68-4.033-1.68-2.521 0-4.033 1.68-4.033 3.36 0 4.622 8.066 1.99 8.066 6.722 0 1.68-1.513 3.36-4.033 3.36-2.017 0-3.529-1.12-4.033-1.68M457.131 40.069v20.165",stroke:"#fff",strokeWidth:1.275,strokeLinecap:"round"}),r.createElement("rect",{x:244.795,y:29.038,width:66.512,height:11.737,rx:2.549,fill:"#F3F5F7"}),r.createElement("rect",{x:244.795,y:60.897,width:66.512,height:72.66,rx:2.549,fill:"#F3F5F7"}),r.createElement("rect",{x:244.795,y:137.941,width:66.512,height:4.471,rx:2.236,fill:"#F3F5F7"}),r.createElement("rect",{x:257.094,y:44.365,width:41.919,height:6.707,rx:3.354,fill:"#F3F5F7"}),r.createElement("rect",{x:244.795,y:44.924,width:8.384,height:1.118,rx:.559,fill:"#DEE3E6"}),r.createElement("rect",{x:244.795,y:47.16,width:8.384,height:1.118,rx:.559,fill:"#DEE3E6"}),r.createElement("rect",{x:244.795,y:49.396,width:8.384,height:1.118,rx:.559,fill:"#DEE3E6"})),r.createElement("g",{filter:"url(#reader-revenue-manager-setup-tablet_svg__filter1_d_545_5344)"},r.createElement("rect",{x:211,y:64,width:134,height:63,rx:5.099,fill:"#fff"}),r.createElement("rect",{x:241.936,y:86.156,width:71.381,height:3.187,rx:1.593,fill:"#F3F5F7"}),r.createElement("rect",{x:254.043,y:91.892,width:47.8,height:3.187,rx:1.593,fill:"#F3F5F7"}),r.createElement("rect",{x:242.826,y:101.452,width:68.831,height:12.746,rx:6.373,fill:"#6FD3D3"})),r.createElement("path",{d:"M277.661 81.19c9.409 0 16.977-7.812 16.977-17.377 0-9.564-7.568-17.377-16.977-17.377-9.408 0-16.977 7.813-16.977 17.377 0 9.565 7.569 17.378 16.977 17.378z",fill:"#6FD3D3",stroke:"#fff",strokeWidth:2.725}),r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M277.728 57.666a4.675 4.675 0 00-.166-.04v-.429c0-.255-.1-.469-.301-.642a.97.97 0 00-.702-.275c-.279 0-.519.092-.72.275a.837.837 0 00-.284.642v.428c-.892.204-1.617.637-2.175 1.3a3.357 3.357 0 00-.837 2.247v4.28h-1.338v1.223h10.707v-1.223h-1.338v-3.638a3.187 3.187 0 01-2.846-4.148zm-1.169 10.844c-.368 0-.686-.118-.954-.352a1.166 1.166 0 01-.385-.871h2.677c0 .336-.134.626-.402.871a1.336 1.336 0 01-.936.352zM284.032 57.996h-4.462V56.72h4.462v1.275z",fill:"#fff"}),r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M281.077 59.59v-4.462h1.275v4.461h-1.275z",fill:"#fff"})),o=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-setup-tablet_svg__filter0_d_545_5344",x:72.803,y:9.889,width:413.971,height:192.473,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:2.549}),r.createElement("feGaussianBlur",{stdDeviation:5.099}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_545_5344"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_545_5344",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-setup-tablet_svg__filter1_d_545_5344",x:200.803,y:58.901,width:154.394,height:83.394,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:5.099}),r.createElement("feGaussianBlur",{stdDeviation:5.099}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_545_5344"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_545_5344",result:"shape"})),r.createElement("clipPath",{id:"reader-revenue-manager-setup-tablet_svg__clip0_545_5344"},r.createElement("path",{fill:"#fff",d:"M0 0h553v140H0z"})));t.a=function SvgReaderRevenueManagerSetupTablet(e){return r.createElement("svg",i({viewBox:"0 0 553 140",fill:"none"},e),a,o)}},573:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#reader-revenue-manager-setup-mobile_svg__clip0_545_3220)"},r.createElement("path",{d:"M48.29 62.984C45.736 92.5 61.979 101.179 61.979 141.7c0 40.523-68.064 65.392-38.567 133.189 23.634 54.322 143.822 65.071 212.81 49.74 68.988-15.331 94.263-53.72 90.398-90.963-5.246-50.546-33.305-76.635-44.18-122.287s8.084-69.5-20.73-92.004c-29.226-22.826-55.734-8.177-86.32-8.177-24.76 0-48.937-19.034-84.62-5.097-22.165 8.657-40.478 33.403-42.48 56.882z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#reader-revenue-manager-setup-mobile_svg__filter0_d_545_3220)"},r.createElement("rect",{x:129.539,y:17.537,width:86.677,height:172.078,rx:12.746,fill:"#fff"}),r.createElement("circle",{cx:49.916,cy:77.24,r:16.523,fill:"#2F9F9F"}),r.createElement("path",{d:"M53.238 72.804c-.432-.48-1.728-1.44-3.455-1.44-2.16 0-3.455 1.44-3.455 2.88 0 3.959 6.91 1.704 6.91 5.757 0 1.44-1.296 2.88-3.455 2.88-1.727 0-3.023-.96-3.455-1.44M49.783 68.485V85.76",stroke:"#fff",strokeWidth:1.275,strokeLinecap:"round"}),r.createElement("circle",{cx:254.048,cy:37.482,r:11.144,fill:"#2F9F9F"}),r.createElement("path",{d:"M256.289 34.49c-.291-.323-1.165-.97-2.33-.97-1.456 0-2.33.97-2.33 1.941 0 2.67 4.66 1.15 4.66 3.884 0 .97-.874 1.942-2.33 1.942-1.165 0-2.039-.648-2.33-.971M253.959 31.578v11.65",stroke:"#fff",strokeWidth:.956,strokeLinecap:"round"}),r.createElement("circle",{cx:306.659,cy:109.5,r:13.712,fill:"#2F9F9F"}),r.createElement("path",{d:"M309.416 105.819c-.359-.398-1.434-1.195-2.867-1.195-1.792 0-2.867 1.195-2.867 2.39 0 3.285 5.734 1.414 5.734 4.778 0 1.195-1.075 2.389-2.867 2.389-1.434 0-2.509-.796-2.867-1.194M306.547 102.235v14.335",stroke:"#fff",strokeWidth:1.275,strokeLinecap:"round"}),r.createElement("rect",{x:139.795,y:29.038,width:66.512,height:11.737,rx:2.549,fill:"#F3F5F7"}),r.createElement("rect",{x:139.795,y:60.897,width:66.512,height:72.66,rx:2.549,fill:"#F3F5F7"}),r.createElement("rect",{x:139.795,y:137.941,width:66.512,height:4.471,rx:2.236,fill:"#F3F5F7"}),r.createElement("rect",{x:152.094,y:44.365,width:41.919,height:6.707,rx:3.354,fill:"#F3F5F7"}),r.createElement("rect",{x:139.795,y:44.924,width:8.384,height:1.118,rx:.559,fill:"#DEE3E6"}),r.createElement("rect",{x:139.795,y:47.16,width:8.384,height:1.118,rx:.559,fill:"#DEE3E6"}),r.createElement("rect",{x:139.795,y:49.396,width:8.384,height:1.118,rx:.559,fill:"#DEE3E6"})),r.createElement("g",{filter:"url(#reader-revenue-manager-setup-mobile_svg__filter1_d_545_3220)"},r.createElement("rect",{x:106,y:64,width:134,height:63,rx:5.099,fill:"#fff"}),r.createElement("rect",{x:136.936,y:86.156,width:71.381,height:3.187,rx:1.593,fill:"#F3F5F7"}),r.createElement("rect",{x:149.043,y:91.892,width:47.8,height:3.187,rx:1.593,fill:"#F3F5F7"}),r.createElement("rect",{x:137.826,y:101.452,width:68.831,height:12.746,rx:6.373,fill:"#6FD3D3"})),r.createElement("path",{d:"M172.661 81.19c9.409 0 16.977-7.812 16.977-17.377 0-9.564-7.568-17.377-16.977-17.377-9.408 0-16.977 7.813-16.977 17.377 0 9.565 7.569 17.378 16.977 17.378z",fill:"#6FD3D3",stroke:"#fff",strokeWidth:2.725}),r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M172.728 57.666a4.675 4.675 0 00-.166-.04v-.429c0-.255-.1-.469-.301-.642a.97.97 0 00-.702-.275c-.279 0-.519.092-.72.275a.837.837 0 00-.284.642v.428c-.892.204-1.617.637-2.175 1.3a3.357 3.357 0 00-.837 2.247v4.28h-1.338v1.223h10.707v-1.223h-1.338v-3.638a3.187 3.187 0 01-2.846-4.148zm-1.169 10.844c-.368 0-.686-.118-.954-.352a1.166 1.166 0 01-.385-.871h2.677c0 .336-.134.626-.402.871a1.336 1.336 0 01-.936.352zM179.032 57.996h-4.462V56.72h4.462v1.275z",fill:"#fff"}),r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M176.077 59.59v-4.462h1.275v4.461h-1.275z",fill:"#fff"})),o=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-setup-mobile_svg__filter0_d_545_3220",x:23.195,y:9.889,width:307.373,height:192.473,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:2.549}),r.createElement("feGaussianBlur",{stdDeviation:5.099}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_545_3220"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_545_3220",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-setup-mobile_svg__filter1_d_545_3220",x:95.803,y:58.901,width:154.394,height:83.394,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:5.099}),r.createElement("feGaussianBlur",{stdDeviation:5.099}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_545_3220"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_545_3220",result:"shape"})),r.createElement("clipPath",{id:"reader-revenue-manager-setup-mobile_svg__clip0_545_3220"},r.createElement("path",{fill:"#fff",d:"M0 0h343v140H0z"})));t.a=function SvgReaderRevenueManagerSetupMobile(e){return r.createElement("svg",i({viewBox:"0 0 343 140",fill:"none"},e),a,o)}},574:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#reader-revenue-manager-monetize-graphic-desktop_svg__clip0_2428_20430)"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h316c8.837 0 16 7.163 16 16v147H0V16z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#reader-revenue-manager-monetize-graphic-desktop_svg__filter0_d_2428_20430)"},r.createElement("rect",{x:48,y:65,width:252,height:200,rx:20,fill:"#fff"})),r.createElement("rect",{x:69,y:92,width:133,height:11,rx:5.5,fill:"#F3F5F7"}),r.createElement("rect",{x:219,y:95,width:61,height:87,rx:4,fill:"#F3F5F7"}),r.createElement("rect",{x:69,y:133,width:133,height:11,rx:5.5,fill:"#F3F5F7"}),r.createElement("rect",{x:69,y:114,width:133,height:11,rx:5.5,fill:"#F3F5F7"}),r.createElement("rect",{x:69,y:152,width:133,height:11,rx:5.5,fill:"#F3F5F7"}),r.createElement("g",{filter:"url(#reader-revenue-manager-monetize-graphic-desktop_svg__filter1_d_2428_20430)"},r.createElement("rect",{x:98,y:25,width:152,height:174,rx:20,fill:"#fff"})),r.createElement("rect",{x:146,y:116,width:56,height:20,rx:10,fill:"#6FD3D3"}),r.createElement("rect",{x:132,y:74,width:84,height:11,rx:5.5,fill:"#F3F5F7"}),r.createElement("rect",{x:132,y:91,width:84,height:11,rx:5.5,fill:"#F3F5F7"}),r.createElement("mask",{id:"reader-revenue-manager-monetize-graphic-desktop_svg__a",fill:"#fff"},r.createElement("rect",{x:165.333,y:46.929,width:16.667,height:14.286,rx:1.786})),r.createElement("rect",{x:165.333,y:46.929,width:16.667,height:14.286,rx:1.786,stroke:"#6FD3D3",strokeWidth:4.444,mask:"url(#reader-revenue-manager-monetize-graphic-desktop_svg__a)"}),r.createElement("path",{d:"M169.5 47.524v-2.38a4.166 4.166 0 014.167-4.167v0a4.166 4.166 0 014.166 4.166v2.381",stroke:"#6FD3D3",strokeWidth:2.222}),r.createElement("circle",{cx:173.667,cy:54.072,r:1.786,fill:"#6FD3D3"})),o=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-monetize-graphic-desktop_svg__filter0_d_2428_20430",x:32,y:53,width:284,height:232,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2428_20430"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2428_20430",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-monetize-graphic-desktop_svg__filter1_d_2428_20430",x:82,y:17,width:184,height:206,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:8}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2428_20430"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2428_20430",result:"shape"})),r.createElement("clipPath",{id:"reader-revenue-manager-monetize-graphic-desktop_svg__clip0_2428_20430"},r.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h316c8.837 0 16 7.163 16 16v147H0V16z",fill:"#fff"})));t.a=function SvgReaderRevenueManagerMonetizeGraphicDesktop(e){return r.createElement("svg",i({viewBox:"0 0 348 163",fill:"none"},e),a,o)}},575:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#reader-revenue-manager-monetize-graphic-mobile_svg__clip0_2084_4987)"},r.createElement("path",{d:"M29.447 64.365c-2.4 22.73 4.803 32.78 23.025 59.949 18.222 27.17-7.404 59.276 20.78 89.869 33.527 36.394 150.685 39.364 201.231 24.211 50.546-15.152 63.581-46.473 59.948-75.154C329.5 124.314 302.482 112.076 289.5 93c-19.276-28.325 2.813-54.786-34.5-77.5s-80.086 6.696-120.326 4.388c-23.216-1.332-46.017-5.627-66.626.968-20.832 6.667-36.72 25.427-38.6 43.509z",fill:"#B8E6CA"}),r.createElement("g",{filter:"url(#reader-revenue-manager-monetize-graphic-mobile_svg__filter0_d_2084_4987)"},r.createElement("rect",{x:76,y:48.778,width:196,height:155.556,rx:15.556,fill:"#fff"})),r.createElement("rect",{x:92.334,y:69.778,width:103.444,height:8.556,rx:4.278,fill:"#F3F5F7"}),r.createElement("rect",{x:209,y:72.111,width:47.444,height:67.667,rx:3.111,fill:"#F3F5F7"}),r.createElement("rect",{x:92.334,y:101.667,width:103.444,height:8.556,rx:4.278,fill:"#F3F5F7"}),r.createElement("rect",{x:92.334,y:86.889,width:103.444,height:8.556,rx:4.278,fill:"#F3F5F7"}),r.createElement("rect",{x:92.334,y:116.444,width:103.444,height:8.556,rx:4.278,fill:"#F3F5F7"}),r.createElement("g",{filter:"url(#reader-revenue-manager-monetize-graphic-mobile_svg__filter1_d_2084_4987)"},r.createElement("rect",{x:114.889,y:17.666,width:118.222,height:135.333,rx:15.556,fill:"#fff"})),r.createElement("rect",{x:152.223,y:88.444,width:43.556,height:15.556,rx:7.778,fill:"#6FD3D3"}),r.createElement("rect",{x:141.334,y:55.778,width:65.333,height:8.556,rx:4.278,fill:"#F3F5F7"}),r.createElement("rect",{x:141.334,y:69,width:65.333,height:8.556,rx:4.278,fill:"#F3F5F7"}),r.createElement("mask",{id:"reader-revenue-manager-monetize-graphic-mobile_svg__a",fill:"#fff"},r.createElement("rect",{x:167.26,y:34.722,width:12.963,height:11.111,rx:1.389})),r.createElement("rect",{x:167.26,y:34.722,width:12.963,height:11.111,rx:1.389,stroke:"#6FD3D3",strokeWidth:3.457,mask:"url(#reader-revenue-manager-monetize-graphic-mobile_svg__a)"}),r.createElement("path",{d:"M170.5 35.185v-1.852a3.24 3.24 0 013.241-3.24v0a3.24 3.24 0 013.24 3.24v1.852",stroke:"#6FD3D3",strokeWidth:1.728}),r.createElement("circle",{cx:173.74,cy:40.278,r:1.389,fill:"#6FD3D3"})),o=r.createElement("defs",null,r.createElement("filter",{id:"reader-revenue-manager-monetize-graphic-mobile_svg__filter0_d_2084_4987",x:63.556,y:39.444,width:220.889,height:180.445,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.111}),r.createElement("feGaussianBlur",{stdDeviation:6.222}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2084_4987"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2084_4987",result:"shape"})),r.createElement("filter",{id:"reader-revenue-manager-monetize-graphic-mobile_svg__filter1_d_2084_4987",x:102.444,y:11.444,width:143.112,height:160.222,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:6.222}),r.createElement("feGaussianBlur",{stdDeviation:6.222}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2084_4987"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2084_4987",result:"shape"})),r.createElement("clipPath",{id:"reader-revenue-manager-monetize-graphic-mobile_svg__clip0_2084_4987"},r.createElement("path",{fill:"#fff",d:"M0 0h343v118H0z"})));t.a=function SvgReaderRevenueManagerMonetizeGraphicMobile(e){return r.createElement("svg",i({viewBox:"0 0 343 118",fill:"none"},e),a,o)}},58:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return r.createElement("svg",i({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),a,o)}},583:function(e,t,n){"use strict";(function(e){function r(e){return"string"==typeof e&&/^[a-zA-Z0-9_-]+$/.test(e)}function i(e){if("string"!=typeof e)return!1;return["ONBOARDING_STATE_UNSPECIFIED","ONBOARDING_ACTION_REQUIRED","PENDING_VERIFICATION","ONBOARDING_COMPLETE"].includes(e)}n.d(t,"c",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"d",(function(){return o}));var a=function(t){try{if("string"!=typeof t||!t)throw new TypeError("Invalid URL: ".concat(t));return"https:"===new URL(t).protocol}catch(t){return e.console.warn("Invalid URL:",t),!1}};function o(e){return["post_types","per_post","sitewide"].includes(e)}}).call(this,n(28))},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(39);function i(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(0),i=Object(r.createContext)(""),a=(i.Consumer,i.Provider);t.b=i},62:function(e,t,n){"use strict";n.d(t,"a",(function(){return w})),n.d(t,"b",(function(){return x})),n.d(t,"c",(function(){return C})),n.d(t,"d",(function(){return D})),n.d(t,"e",(function(){return A})),n.d(t,"g",(function(){return T})),n.d(t,"f",(function(){return R}));var r,i=n(5),a=n.n(i),o=n(27),c=n.n(o),s=n(6),l=n.n(s),u=n(12),d=n.n(u),f=n(63),g=n.n(f),p=n(14),m=n(116);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.reduce((function(e,t){return h(h({},e),t)}),{}),i=t.reduce((function(e,t){return[].concat(c()(e),c()(Object.keys(t)))}),[]),a=N(i);return d()(0===a.length,"collect() cannot accept collections with duplicate keys. Your call to collect() contains the following duplicated functions: ".concat(a.join(", "),". Check your data stores for duplicates.")),r},O=v,E=v,_=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i=[].concat(t);return"function"!=typeof i[0]&&(r=i.shift()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i.reduce((function(e,n){return n(e,t)}),e)}},y=v,k=v,j=v,S=function(e){return e},w=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=j.apply(void 0,c()(t.map((function(e){return e.initialState||{}}))));return{initialState:r,controls:E.apply(void 0,c()(t.map((function(e){return e.controls||{}})))),actions:O.apply(void 0,c()(t.map((function(e){return e.actions||{}})))),reducer:_.apply(void 0,[r].concat(c()(t.map((function(e){return e.reducer||S}))))),resolvers:y.apply(void 0,c()(t.map((function(e){return e.resolvers||{}})))),selectors:k.apply(void 0,c()(t.map((function(e){return e.selectors||{}}))))}},x={getRegistry:function(){return{payload:{},type:"GET_REGISTRY"}},await:a.a.mark((function e(t){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{payload:{value:t},type:"AWAIT"});case 1:case"end":return e.stop()}}),e)}))},C=(r={},l()(r,"GET_REGISTRY",Object(m.a)((function(e){return function(){return e}}))),l()(r,"AWAIT",(function(e){return e.payload.value})),r),N=function(e){for(var t=[],n={},r=0;r<e.length;r++){var i=e[r];n[i]=n[i]>=1?n[i]+1:1,n[i]>1&&t.push(i)}return t},D={actions:x,controls:C,reducer:S},A=function(e){return function(t){return P(e(t))}},P=g()((function(e){return Object(p.mapValues)(e,(function(e,t){return function(){var n=e.apply(void 0,arguments);return d()(void 0!==n,"".concat(t,"(...) is not resolved")),n}}))}));function T(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.negate,r=void 0!==n&&n,i=Object(m.b)((function(t){return function(n){var i=!r,a=!!r;try{for(var o=arguments.length,c=new Array(o>1?o-1:0),s=1;s<o;s++)c[s-1]=arguments[s];return e.apply(void 0,[t,n].concat(c)),i}catch(e){return a}}})),a=Object(m.b)((function(t){return function(n){for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];e.apply(void 0,[t,n].concat(i))}}));return{safeSelector:i,dangerousSelector:a}}function R(e,t){return d()("function"==typeof e,"a validator function is required."),d()("function"==typeof t,"an action creator function is required."),d()("Generator"!==e[Symbol.toStringTag]&&"GeneratorFunction"!==e[Symbol.toStringTag],"an action’s validator function must not be a generator."),function(){return e.apply(void 0,arguments),t.apply(void 0,arguments)}}},64:function(e,t,n){"use strict";n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return h}));var r=n(6),i=n.n(r),a=n(33),o=n.n(a),c=n(116),s=n(12),l=n.n(s),u=n(96),d=n.n(u),f=n(9);function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e,t){if(t&&Array.isArray(t)){var n=t.map((function(e){return"object"===o()(e)?Object(f.H)(e):e}));return"".concat(e,"::").concat(d()(JSON.stringify(n)))}return e}var b={receiveError:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(e,"error is required."),l()(t,"baseName is required."),l()(n&&Array.isArray(n),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:n}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return l()(e,"baseName is required."),l()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function h(e){l()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(n,"selectorName is required."),t.getError(e,n,r)},getErrorForAction:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(n,"actionName is required."),t.getError(e,n,r)},getError:function(e,t,n){var r=e.errors;return l()(t,"baseName is required."),r[m(t,n)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var n=Object.keys(e.errors).find((function(n){return e.errors[n]===t}));return n?{baseName:n.substring(0,n.indexOf("::")),args:e.errorArgs[n]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(n,r){var i=t(e).getMetaDataForError(r);if(i){var a=i.baseName,o=i.args;if(!!t(e)[a])return{storeName:e,name:a,args:o}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:b,controls:{},reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case"RECEIVE_ERROR":var a=r.baseName,o=r.args,c=r.error,s=m(a,o);return p(p({},e),{},{errors:p(p({},e.errors||{}),{},i()({},s,c)),errorArgs:p(p({},e.errorArgs||{}),{},i()({},s,o))});case"CLEAR_ERROR":var l=r.baseName,u=r.args,d=p({},e),f=m(l,u);return d.errors=p({},e.errors||{}),d.errorArgs=p({},e.errorArgs||{}),delete d.errors[f],delete d.errorArgs[f],d;case"CLEAR_ERRORS":var g=r.baseName,b=p({},e);if(g)for(var h in b.errors=p({},e.errors||{}),b.errorArgs=p({},e.errorArgs||{}),b.errors)(h===g||h.startsWith("".concat(g,"::")))&&(delete b.errors[h],delete b.errorArgs[h]);else b.errors={},b.errorArgs={};return b;default:return e}},resolvers:{},selectors:t}}},65:function(e,t,n){"use strict";n.d(t,"c",(function(){return m})),n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return h})),n.d(t,"d",(function(){return O}));var r=n(6),i=n.n(r),a=n(0);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var c=a.createElement("path",{d:"M10 15.27L16.18 19l-1.64-7.03L20 7.24l-7.19-.61L10 0 7.19 6.63 0 7.24l5.46 4.73L3.82 19 10 15.27z"});var s=function SvgInfoIcon(e){return a.createElement("svg",o({viewBox:"0 0 20 20",fill:"currentColor"},e),c)};function l(){return(l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var u=a.createElement("path",{d:"M0 4h2v7H0zm0-4h2v2H0z",fill:"currentColor",fillRule:"evenodd"});var d,f=function SvgSuggestionIcon(e){return a.createElement("svg",l({viewBox:"0 0 2 11"},e),u)},g=n(186),p=n(74),m="warning",b="info",h="suggestion",v=(d={},i()(d,b,s),i()(d,m,g.a),i()(d,h,f),d),O=function(e){return v[e]||p.a}},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return l})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return f})),n.d(t,"J",(function(){return g})),n.d(t,"I",(function(){return p})),n.d(t,"N",(function(){return m})),n.d(t,"f",(function(){return b})),n.d(t,"g",(function(){return h})),n.d(t,"h",(function(){return v})),n.d(t,"j",(function(){return O})),n.d(t,"l",(function(){return E})),n.d(t,"m",(function(){return _})),n.d(t,"n",(function(){return y})),n.d(t,"o",(function(){return k})),n.d(t,"q",(function(){return j})),n.d(t,"s",(function(){return S})),n.d(t,"r",(function(){return w})),n.d(t,"t",(function(){return x})),n.d(t,"w",(function(){return C})),n.d(t,"u",(function(){return N})),n.d(t,"v",(function(){return D})),n.d(t,"x",(function(){return A})),n.d(t,"y",(function(){return P})),n.d(t,"A",(function(){return T})),n.d(t,"B",(function(){return R})),n.d(t,"C",(function(){return I})),n.d(t,"D",(function(){return F})),n.d(t,"k",(function(){return L})),n.d(t,"F",(function(){return M})),n.d(t,"z",(function(){return B})),n.d(t,"G",(function(){return G})),n.d(t,"E",(function(){return U})),n.d(t,"i",(function(){return z})),n.d(t,"p",(function(){return q})),n.d(t,"Q",(function(){return V})),n.d(t,"P",(function(){return H}));var r="core/user",i="connected_url_mismatch",a="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",l="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",f="googlesitekit_read_shared_module_data",g="googlesitekit_manage_module_sharing_options",p="googlesitekit_delegate_module_sharing_management",m="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",h="kmAnalyticsEngagedTrafficSource",v="kmAnalyticsLeastEngagingPages",O="kmAnalyticsNewVisitors",E="kmAnalyticsPopularAuthors",_="kmAnalyticsPopularContent",y="kmAnalyticsPopularProducts",k="kmAnalyticsReturningVisitors",j="kmAnalyticsTopCities",S="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",x="kmAnalyticsTopCitiesDrivingPurchases",C="kmAnalyticsTopDeviceDrivingPurchases",N="kmAnalyticsTopConvertingTrafficSource",D="kmAnalyticsTopCountries",A="kmAnalyticsTopPagesDrivingLeads",P="kmAnalyticsTopRecentTrendingPages",T="kmAnalyticsTopTrafficSource",R="kmAnalyticsTopTrafficSourceDrivingAddToCart",I="kmAnalyticsTopTrafficSourceDrivingLeads",F="kmAnalyticsTopTrafficSourceDrivingPurchases",L="kmAnalyticsPagesPerVisit",M="kmAnalyticsVisitLength",B="kmAnalyticsTopReturningVisitorPages",G="kmSearchConsolePopularKeywords",U="kmAnalyticsVisitsPerVisitor",z="kmAnalyticsMostEngagingPages",q="kmAnalyticsTopCategories",V=[b,h,v,O,E,_,y,k,q,j,S,w,x,C,N,D,P,T,R,L,M,B,U,z,q],H=[].concat(V,[G])},70:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},72:function(e,t,n){"use strict";var r=n(15),i=n.n(r),a=n(265),o=n(1),c=n.n(o),s=n(0),l=n(144);function Portal(e){var t=e.children,n=e.slug,r=Object(s.useState)(document.createElement("div")),o=i()(r,1)[0];return Object(a.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(l.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},73:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),i=n(18),a=n(9);function o(e,t){var n=Object(i.a)(),o=null!=t?t:"".concat(n,"_").concat(e);return{view:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"view_notification"].concat(t))}),[o]),confirm:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"confirm_notification"].concat(t))}),[o]),dismiss:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"dismiss_notification"].concat(t))}),[o]),clickLearnMore:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"click_learn_more_link"].concat(t))}),[o])}}},74:function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(33),i=n.n(r),a=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,n="object"===i()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},756:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupForm}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(6),s=n.n(c),l=n(1),u=n.n(l),d=n(0),f=n(2),g=n(10),p=n(3),m=n(20),b=n(141),h=n(29),v=n(42),O=n(389);function SetupForm(t){var n=t.onCompleteSetup,r=Object(p.useSelect)((function(e){return e(v.c).canSubmitChanges()})),a=Object(p.useSelect)((function(e){return e(v.c).isDoingSubmitChanges()})),c=Object(p.useSelect)((function(e){return e(v.c).getPublications()})),l=Object(p.useSelect)((function(e){return e(v.c).getPublicationID()})),u=Object(p.useSelect)((function(e){return e(v.c).getCurrentProductIDs()})),E=Object(p.useSelect)((function(e){return e(v.c).getServiceURL()})),_=Object(p.useDispatch)(h.a).setValues,y=Object(p.useDispatch)(v.c),k=y.findMatchedPublication,j=y.selectPublication,S=y.setProductID,w=Object(d.useCallback)((function(){_(v.g,s()({},v.h,!0))}),[_]),x=Object(d.useCallback)((function(e){e.preventDefault(),n()}),[n]),C=Object(d.useCallback)((function(e){var t=e.products;(null==t?void 0:t.length)>0&&t[0].name&&S(t[0].name)}),[S]);return Object(d.useEffect)((function(){l||function(){var e=o()(i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,k();case 2:(t=e.sent)&&(j(t),C(t));case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()()}),[C,k,l,j]),c?e.createElement("form",{onSubmit:x},e.createElement(b.a,{moduleSlug:v.e,storeName:v.c}),e.createElement("p",{className:"googlesitekit-margin-bottom-0"},1===c.length?Object(f.__)("Site Kit will connect your existing publication","google-site-kit"):Object(f.__)("Select your preferred publication to connect with Site Kit","google-site-kit")),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(O.e,{onChange:function(e){return C(e)}}),(null==u?void 0:u.length)>0&&e.createElement(O.b,{showHelperText:!1})),e.createElement(O.d,null),e.createElement(m.a,{external:!0,href:E,onClick:w},Object(f.__)("Manage publications in Publisher Center","google-site-kit")),e.createElement("div",{className:"googlesitekit-setup-module__action"},e.createElement(g.SpinnerButton,{disabled:!r||a,isSaving:a},Object(f.__)("Complete setup","google-site-kit")))):null}SetupForm.propTypes={onCompleteSetup:u.a.func.isRequired}}).call(this,n(4))},757:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ProductIDSettings}));var r=n(38),i=n(2),a=n(3),o=n(13),c=n(7),s=n(42),l=n(354),u=n(389),d=n(20),f=n(258);function ProductIDSettings(t){var n=t.hasModuleAccess,g=Object(a.useSelect)((function(e){return e(s.c).getProductID()})),p=Object(a.useSelect)((function(e){return e(s.c).getCurrentProductIDs()})),m=Object(a.useSelect)((function(e){return e(s.c).getPaymentOption()})),b=Object(a.useSelect)((function(e){return e(o.c).getDocumentationLinkURL("rrm-content-settings")})),h=Object(a.useSelect)((function(e){return e(c.a).isItemDismissed(l.c)})),v=Object(a.useSelect)((function(e){return e(c.a).isItemDismissed(l.b)})),O="subscriptions"===m,E=Object(a.useDispatch)(c.a).dismissItem,_=Object(a.useDispatch)(s.c).setProductIDs;return e.createElement("div",{className:"googlesitekit-rrm-settings-edit__product-id-container"},e.createElement("div",{className:"googlesitekit-rrm-settings-edit__product-id"},e.createElement(u.b,{onChange:function(){_(p)},hasModuleAccess:n})),O&&"openaccess"===g&&!h&&e.createElement("div",{className:"googlesitekit-rrm-settings-edit__product-id-warning-notice"},e.createElement(f.b,{title:Object(i.__)("Selecting “open access” will allow your reader to access your content without a subscription","google-site-kit"),hideIcon:!0,variant:"warning",dismissLabel:Object(i.__)("Got it","google-site-kit"),onDismiss:function(){return E(l.c)}})),!v&&e.createElement("div",{className:"googlesitekit-rrm-settings-edit__product-id-info-notice"},e.createElement(f.b,{title:Object(r.a)(Object(i.__)("Use the new settings in the block editor to select different product IDs for individual pages or control where CTAs appear on an individual post. <learnMore>Learn more</learnMore>","google-site-kit"),{learnMore:e.createElement(d.a,{"aria-label":Object(i.__)("Learn more about setting product IDs at a content-level","google-site-kit"),href:b,external:!0,hideExternalIndicator:!0})}),variant:"info",dismissLabel:Object(i.__)("Got it","google-site-kit"),onDismiss:function(){return E(l.b)}})))}}).call(this,n(4))},758:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsForm}));var r=n(0),i=n(38),a=n(2),o=n(3),c=n(19),s=n(42),l=n(54),u=n(389),d=n(757),f=n(142),g=n(141),p=n(186),m=n(515);function SettingsForm(t){var n=t.hasModuleAccess,b=Object(o.useSelect)((function(e){return e(s.c).getPublicationID()})),h=Object(o.useSelect)((function(e){return e(s.c).getCurrentProductIDs()})),v=Object(o.useSelect)((function(e){return e(s.c).getSnippetMode()})),O=Object(o.useSelect)((function(e){var t=e(s.c).getProductID();if(void 0!==t){if("openaccess"===t)return null;if(void 0!==h)return h.includes(t)?null:t}})),E=Object(o.useSelect)((function(e){if(void 0!==n){if(!1===n)return!1;var t=e(s.c).getPublications();if(Array.isArray(t))return t.some((function(e){return e.publicationId===b}))}})),_=Object(o.useSelect)((function(e){var t,n=e(c.a).getModule(s.e);return(null==n||null===(t=n.owner)||void 0===t?void 0:t.login)?"<strong>".concat(n.owner.login,"</strong>"):Object(a.__)("Another admin","google-site-kit")})),y=Object(o.useSelect)((function(e){return!1===n||e(s.c).hasFinishedResolution("getPublications")}));return e.createElement(r.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-module__fields-group"},e.createElement(g.a,{moduleSlug:s.e,storeName:s.c}),n&&!1===E&&e.createElement(l.a,{message:Object(a.sprintf)(
/* translators: 1: Publication ID. */
Object(a.__)("The previously selected publication with ID %s was not found. Please select a new publication.","google-site-kit"),b)}),n&&E&&O&&e.createElement(l.a,{message:Object(a.sprintf)(
/* translators: 1: Product ID. */
Object(a.__)("The previously selected product ID %s was not found. Please select a new product ID.","google-site-kit"),Object(m.b)(O))}),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(u.e,{hasModuleAccess:n})),n&&E&&e.createElement(u.d,null),!n&&e.createElement(f.c,{type:f.a,Icon:p.a,notice:Object(i.a)(Object(a.sprintf)(
/* translators: %s: module owner's name */
Object(a.__)("%s configured Reader Revenue Manager and you don’t have access to its configured publication. Contact them to share access or change the configured publication.","google-site-kit"),_),{strong:e.createElement("strong",null)})}),y&&(null==h?void 0:h.length)>0&&e.createElement(d.a,{hasModuleAccess:n})),e.createElement("div",{className:"googlesitekit-settings-module__fields-group"},e.createElement("h4",{className:"googlesitekit-settings-module__fields-group-title"},Object(a.__)("CTA Placement","google-site-kit")),e.createElement("div",{className:"googlesitekit-rrm-settings-edit__snippet-mode"},e.createElement(u.f,{hasModuleAccess:n})),"post_types"===v&&e.createElement("div",{className:"googlesitekit-rrm-settings-edit__post-types"},e.createElement("h5",null,Object(a.__)("Select the content types where you want your CTAs to appear:","google-site-kit")),e.createElement(u.a,{hasModuleAccess:n}))))}}).call(this,n(4))},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(1),i=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,i=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:i}},n)}IconWrapper.propTypes={children:i.a.node.isRequired,marginLeft:i.a.number,marginRight:i.a.number}}).call(this,n(4))},785:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(0),i=Object(r.createContext)({}),a=(i.Consumer,i.Provider);t.b=i},786:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M26.666 0H13.333v13.333h13.333V0z",fill:"url(#reader-revenue-manager_svg__paint0_linear_63_914)"}),o=r.createElement("path",{d:"M40 13.335V40L26.667 26.667V0L40 13.335z",fill:"#FBBC04"}),c=r.createElement("path",{d:"M13.333 26.665V40L0 26.665h13.333z",fill:"#0D652D"}),s=r.createElement("path",{d:"M13.333 13.333v13.332H0V0l13.333 13.333z",fill:"#34A853"}),l=r.createElement("path",{d:"M26.666 26.665H13.333V40h13.333V26.665z",fill:"url(#reader-revenue-manager_svg__paint1_linear_63_914)"}),u=r.createElement("defs",null,r.createElement("linearGradient",{id:"reader-revenue-manager_svg__paint0_linear_63_914",x1:36.564,y1:6.667,x2:-14.942,y2:6.667,gradientUnits:"userSpaceOnUse"},r.createElement("stop",{offset:.11,stopColor:"#EA8600"}),r.createElement("stop",{offset:.14,stopColor:"#EF9601"}),r.createElement("stop",{offset:.19,stopColor:"#F4A702"}),r.createElement("stop",{offset:.26,stopColor:"#F8B303"}),r.createElement("stop",{offset:.34,stopColor:"#FABA03"}),r.createElement("stop",{offset:.59,stopColor:"#FBBC04"})),r.createElement("linearGradient",{id:"reader-revenue-manager_svg__paint1_linear_63_914",x1:-10.143,y1:33.332,x2:89.334,y2:33.332,gradientUnits:"userSpaceOnUse"},r.createElement("stop",{offset:.04,stopColor:"#174EA6"}),r.createElement("stop",{offset:.12,stopColor:"#1F59B5"}),r.createElement("stop",{offset:.27,stopColor:"#3675DE"}),r.createElement("stop",{offset:.34,stopColor:"#4285F4"})));t.a=function SvgReaderRevenueManager(e){return r.createElement("svg",i({viewBox:"0 0 40 40",fill:"none"},e),a,o,c,s,l,u)}},79:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(15),i=n.n(r),a=n(188),o=n(133),c={},s=void 0===e?null:e,l=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,u=void 0===r?0:r,d=e.initialHeight,f=void 0===d?0:d,g=Object(a.a)("undefined"==typeof document?[u,f]:l,t,n),p=i()(g,2),m=p[0],b=p[1],h=function(){return b(l)};return Object(o.a)(s,"resize",h),Object(o.a)(s,"orientationchange",h),m},d=function(e){return u(e)[0]}}).call(this,n(28))},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(106);function i(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),i=e.replace(n.origin,"");if(i.length<t)return i;var a=i.length-Math.floor(t)+1;return"…"+i.substr(a)}},82:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return a}));var r=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},i=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function a(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return k})),n.d(t,"d",(function(){return j})),n.d(t,"e",(function(){return w})),n.d(t,"c",(function(){return x})),n.d(t,"b",(function(){return C}));var r=n(15),i=n.n(r),a=n(33),o=n.n(a),c=n(6),s=n.n(c),l=n(25),u=n.n(l),d=n(14),f=n(63),g=n.n(f),p=n(2);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=E(e,t),r=n.formatUnit,i=n.formatDecimal;try{return r()}catch(e){return i()}},v=function(e){var t=O(e),n=t.hours,r=t.minutes,i=t.seconds;return i=("0"+i).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(i):"".concat(n,":").concat(r,":").concat(i)},O=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},E=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=O(e),r=n.hours,i=n.minutes,a=n.seconds;return{hours:r,minutes:i,seconds:a,formatUnit:function(){var n=t.unitDisplay,o=b(b({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(a,b(b({},o),{},{unit:"second"})):Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?w(a,b(b({},o),{},{unit:"second"})):"",i?w(i,b(b({},o),{},{unit:"minute"})):"",r?w(r,b(b({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(p.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(p.__)("%ds","google-site-kit"),a);if(0===e)return t;var n=Object(p.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(p.__)("%dm","google-site-kit"),i),o=Object(p.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(p.__)("%dh","google-site-kit"),r);return Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",i?n:"",r?o:"").trim()}}},_=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},y=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in millions.
Object(p.__)("%sM","google-site-kit"),w(_(e),e%10==0?{}:t)):1e4<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(_(e))):1e3<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(_(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function k(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=b({},e)),t}function j(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=k(t),r=n.style,i=void 0===r?"metric":r;return"metric"===i?y(e):"duration"===i?h(e,n):"durationISO"===i?v(e):w(e,n)}var S=g()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?C():n,a=u()(t,["locale"]);try{return new Intl.NumberFormat(r,a).format(e)}catch(t){S("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],l={},d=0,f=Object.entries(a);d<f.length;d++){var g=i()(f[d],2),p=g[0],m=g[1];c[p]&&m===c[p]||(s.includes(p)||(l[p]=m))}try{return new Intl.NumberFormat(r,l).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},x=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?C():n,i=t.style,a=void 0===i?"long":i,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(r,{style:a,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var l=Object(p.__)(", ","google-site-kit");return e.join(l)},C=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},832:function(e,t,n){"use strict";var r=n(916);n.d(t,"a",(function(){return r.a}));var i=n(917);n.d(t,"b",(function(){return i.a}))},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i}));var r=n(149),i=n.n(r)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a);function ChangeArrow(t){var n=t.direction,r=t.invertColor,i=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:i,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:i.a.string,invertColor:i.a.bool,width:i.a.number,height:i.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(12),i=n.n(r),a=function(e,t){var n=t.dateRangeLength;i()(Array.isArray(e),"report must be an array to partition."),i()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return i.b})),n.d(t,"J",(function(){return i.c})),n.d(t,"F",(function(){return a.a})),n.d(t,"K",(function(){return a.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return m})),n.d(t,"j",(function(){return b})),n.d(t,"i",(function(){return h})),n.d(t,"d",(function(){return k})),n.d(t,"c",(function(){return j})),n.d(t,"e",(function(){return S})),n.d(t,"b",(function(){return w})),n.d(t,"a",(function(){return x})),n.d(t,"f",(function(){return C})),n.d(t,"n",(function(){return N})),n.d(t,"w",(function(){return D})),n.d(t,"p",(function(){return A})),n.d(t,"G",(function(){return P})),n.d(t,"s",(function(){return T})),n.d(t,"v",(function(){return R})),n.d(t,"k",(function(){return I})),n.d(t,"o",(function(){return F.b})),n.d(t,"h",(function(){return F.a})),n.d(t,"t",(function(){return L.b})),n.d(t,"q",(function(){return L.a})),n.d(t,"A",(function(){return L.c})),n.d(t,"x",(function(){return M})),n.d(t,"u",(function(){return B})),n.d(t,"E",(function(){return z})),n.d(t,"D",(function(){return q.a})),n.d(t,"g",(function(){return V})),n.d(t,"L",(function(){return H})),n.d(t,"l",(function(){return W}));var r=n(14),i=n(36),a=n(75),o=n(33),c=n.n(o),s=n(96),l=n.n(s),u=function(e){return l()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var i=t[r];i&&"object"===c()(i)&&!Array.isArray(i)&&(i=e(i)),n[r]=i})),n}(e)))};n(97);var d=n(83);function f(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function g(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function p(e){return e.replace(/\n/gi,"<br>")}function m(e){for(var t=e,n=0,r=[f,g,p];n<r.length;n++){t=(0,r[n])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},h=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},v=n(15),O=n.n(v),E=n(12),_=n.n(E),y=n(2),k="Invalid dateString parameter, it must be a string.",j='Invalid date range, it must be a string with the format "last-x-days".',S=60,w=60*S,x=24*w,C=7*x;function N(){var e=function(e){return Object(y.sprintf)(
/* translators: %s: number of days */
Object(y._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function D(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function A(e){_()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function P(e){_()(D(e),k);var t=e.split("-"),n=O()(t,3),r=n[0],i=n[1],a=n[2];return new Date(r,i-1,a)}function T(e,t){return A(I(e,t*x))}function R(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function I(e,t){_()(D(e)||Object(r.isDate)(e)&&!isNaN(e),k);var n=D(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var F=n(98),L=n(80);function M(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function B(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var G=n(27),U=n.n(G),z=function(e){return Array.isArray(e)?U()(e).sort():e},q=n(89);function V(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var H=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},W=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},90:function(e,t,n){"use strict";(function(e,r){n.d(t,"c",(function(){return v})),n.d(t,"b",(function(){return E})),n.d(t,"a",(function(){return TourTooltips}));var i=n(6),a=n.n(i),o=n(81),c=n(30),s=n(1),l=n.n(s),u=n(2),d=n(3),f=n(23),g=n(7),p=n(36),m=n(107),b=n(18);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var v={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},O={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},E={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},_="feature_tooltip_view",y="feature_tooltip_advance",k="feature_tooltip_return",j="feature_tooltip_dismiss",S="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,i=t.tourID,s=t.gaEventCategory,l=t.callback,u="".concat(i,"-step"),w="".concat(i,"-run"),x=Object(d.useDispatch)(f.b).setValue,C=Object(d.useDispatch)(g.a).dismissTour,N=Object(d.useRegistry)(),D=Object(b.a)(),A=Object(d.useSelect)((function(e){return e(f.b).getValue(u)})),P=Object(d.useSelect)((function(e){return e(f.b).getValue(w)&&!1===e(g.a).isTourDismissed(i)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),x(w,!0)}));var T=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return r.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,r=e.lifecycle,i=e.size,a=e.status,o=e.type,l=t+1,u="function"==typeof s?s(D):s;o===c.b.TOOLTIP&&r===c.c.TOOLTIP?Object(p.b)(u,_,l):n===c.a.CLOSE&&r===c.c.COMPLETE?Object(p.b)(u,j,l):n===c.a.NEXT&&a===c.d.FINISHED&&o===c.b.TOUR_END&&i===l&&Object(p.b)(u,S,l),r===c.c.COMPLETE&&a!==c.d.FINISHED&&(n===c.a.PREV&&Object(p.b)(u,k,l),n===c.a.NEXT&&Object(p.b)(u,y,l))}(t);var n=t.action,r=t.index,a=t.status,o=t.step,d=t.type,f=n===c.a.CLOSE,g=!f&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),m=[c.d.FINISHED,c.d.SKIPPED].includes(a),b=f&&d===c.b.STEP_AFTER,h=m||b;if(c.b.STEP_BEFORE===d){var v,O,E=o.target;"string"==typeof o.target&&(E=e.document.querySelector(o.target)),null===(v=E)||void 0===v||null===(O=v.scrollIntoView)||void 0===O||O.call(v,{block:"center"})}g?function(e,t){x(u,e+(t===c.a.PREV?-1:1))}(r,n):h&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),C(i)),l&&l(t,N)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:E,locale:O,run:P,showProgress:!0,stepIndex:A,steps:T,styles:v,tooltipComponent:m.a})}TourTooltips.propTypes={steps:l.a.arrayOf(l.a.object).isRequired,tourID:l.a.string.isRequired,gaEventCategory:l.a.oneOfType([l.a.string,l.a.func]).isRequired,callback:l.a.func}}).call(this,n(28),n(4))},912:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PublicationOnboardingStateNotice}));var r=n(6),i=n.n(r),a=n(0),o=n(2),c=n(3),s=n(29),l=n(42),u=n(258),d=n(9),f=n(18),g=n(353),p=l.d.PENDING_VERIFICATION,m=l.d.ONBOARDING_ACTION_REQUIRED;function PublicationOnboardingStateNotice(){var t=Object(f.a)(),n=Object(c.useSelect)((function(e){return e(l.c).getPublicationOnboardingState()})),r=[p,m],b=Object(c.useSelect)((function(e){return e(l.c).getPublicationID()})),h=Object(c.useSelect)((function(e){return e(l.c).getServiceURL({path:"reader-revenue-manager",query:{publication:b}})})),v=Object(c.useSelect)((function(e){return e(s.a).getValue(l.f,l.j)&&r.includes(n)})),O=Object(c.useDispatch)(s.a).setValues,E=Object(c.useDispatch)(l.c).syncPublicationOnboardingState,_=n&&r.includes(n),y=Object(a.useCallback)((function(){O(l.f,i()({},l.j,!0)),Object(d.I)("".concat(t,"_rrm-onboarding-state-notification"),"confirm_notification",n)}),[n,O,t]),k=Object(a.useCallback)((function(){v&&E()}),[v,E]);if(Object(a.useEffect)((function(){_&&Object(d.I)("".concat(t,"_rrm-onboarding-state-notification"),"view_notification",n)}),[n,_,t]),Object(g.a)(k,15e3),!_)return null;var j=p===n?Object(o.__)("Your publication is still awaiting review. You can check its status in Reader Revenue Manager.","google-site-kit"):Object(o.__)("Your publication requires further setup in Reader Revenue Manager","google-site-kit"),S=p===n?Object(o.__)("Check publication status","google-site-kit"):Object(o.__)("Complete publication setup","google-site-kit");return e.createElement(u.b,{className:"googlesitekit-publication-onboarding-state-notice",title:j,ctaLabel:S,ctaLink:h,isCTALinkExternal:!0,variant:"warning",onCTAClick:y})}}).call(this,n(4))},913:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PublicationCreate}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(6),s=n.n(c),l=n(1),u=n.n(l),d=n(0),f=n(38),g=n(2),p=n(3),m=n(10),b=n(29),h=n(42),v=n(70),O=n(199);function PublicationCreate(t){var n=t.onCompleteSetup,r=Object(p.useSelect)((function(e){return e(h.c).getPublications()})),a=Object(p.useSelect)((function(e){return e(h.c).getCreatePublicationLinkURL()})),c=Object(p.useDispatch)(b.a).setValues,l=Object(p.useDispatch)(h.c).selectPublication,u=r&&r.length>0,E=Object(d.useCallback)((function(){c(h.g,s()({},h.h,!0))}),[c]),_=Object(d.useCallback)(o()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(u){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,l(r[0]);case 4:n();case 5:case"end":return e.stop()}}),e)}))),[u,n,r,l]);return void 0===r?null:e.createElement("div",{className:"googlesitekit-setup-module__publication-create-screen"},!u&&e.createElement(d.Fragment,null,e.createElement("h3",{className:"googlesitekit-heading-3 googlesitekit-setup-module__title"},Object(g.__)("To complete your Reader Revenue Manager account setup you will need to create a publication and set up Reader Revenue Manager in Publisher Center.","google-site-kit")),e.createElement("p",{className:"googlesitekit-setup-module__description"},Object(f.a)(Object(g.__)("Once you have created your publication, it is submitted for review. <a>Learn more</a>","google-site-kit"),{a:e.createElement(O.a,{path:"/news/publisher-center/answer/********",external:!0,"aria-label":Object(g.__)("Learn more about setting up Reader Revenue Manager","google-site-kit")})})),e.createElement("div",{className:"googlesitekit-setup-module__action"},e.createElement(m.Button,{href:a,target:"_blank",trailingIcon:e.createElement(v.a,{width:14,height:14}),onClick:E},Object(g.__)("Create publication","google-site-kit")))),u&&e.createElement(d.Fragment,null,e.createElement("h3",{className:"googlesitekit-heading-3 googlesitekit-setup-module__title"},Object(g.__)("You have successfully created your publication and it is now awaiting review. This might take up to 2 weeks.","google-site-kit")),e.createElement("div",{className:"googlesitekit-setup-module__action"},e.createElement(m.SpinnerButton,{onClick:_},Object(g.__)("Complete setup","google-site-kit")))))}PublicationCreate.propTypes={onCompleteSetup:u.a.func.isRequired}}).call(this,n(4))},914:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PublicationSelect}));var r=n(11),i=n.n(r),a=n(1),o=n.n(a),c=n(2),s=n(0),l=n(42),u=n(10),d=n(583),f=n(3);function PublicationSelect(t){var n=t.isDisabled,r=t.hasModuleAccess,a=t.className,o=t.onChange,g=void 0===o?function(){}:o,p=Object(f.useSelect)((function(e){return e(l.c).getPublicationID()})),m=Object(f.useSelect)((function(e){return!1===r||n?null:e(l.c).getPublications()||[]})),b=Object(f.useSelect)((function(e){return!1===r||n||e(l.c).hasFinishedResolution("getPublications")})),h=Object(f.useDispatch)(l.c).selectPublication,v=Object(s.useCallback)((function(e,t){var n=t.dataset.value,r=m.find((function(e){return e.publicationId===n}));h(r),g(r)}),[m,h,g]);if(!b)return e.createElement(u.ProgressBar,{smallHeight:80,desktopHeight:88,small:!0});var O=void 0===p||""===p||Object(d.c)(p);return!1===r?e.createElement(u.Select,{className:i()(a),label:Object(c.__)("Publication","google-site-kit"),value:p,enhanced:!0,outlined:!0,disabled:!0},e.createElement(u.Option,{value:p},p)):e.createElement(u.Select,{className:i()(a,{"mdc-select--invalid":!O}),label:Object(c.__)("Publication","google-site-kit"),value:p,onEnhancedChange:v,disabled:n,enhanced:!0,outlined:!0},(m||[]).map((function(t){var n=t.publicationId,r=t.displayName;return e.createElement(u.Option,{key:n,value:n},Object(c.sprintf)(
/* translators: 1: Publication display name, 2: Publication ID */
Object(c.__)("%1$s (%2$s)","google-site-kit"),r,n))})))}PublicationSelect.propTypes={isDisabled:o.a.bool,hasModuleAccess:o.a.bool,className:o.a.string,onChange:o.a.func}}).call(this,n(4))},915:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PostTypesSelect}));var r=n(27),i=n.n(r),a=n(1),o=n.n(a),c=n(0),s=n(3),l=n(13),u=n(42),d=n(832);function PostTypesSelect(t){var n=t.isDisabled,r=t.hasModuleAccess,a=t.onChange,o=void 0===a?function(){}:a,f=Object(s.useSelect)((function(e){return e(l.c).getPostTypes()})),g=Object(s.useSelect)((function(e){return e(u.c).getPostTypes()})),p=Object(s.useDispatch)(u.c).setPostTypes,m=Object(c.useCallback)((function(e){var t=g.includes(e)?g.filter((function(t){return t!==e})):[].concat(i()(g),[e]);p(t),o(t)}),[o,g,p]);return e.createElement(d.a,{onToggleChip:m},f.map((function(t){var i=t.slug,a=t.label;return e.createElement(d.b,{key:i,id:i,selected:g.includes(i),disabled:n||!r},a)})))}PostTypesSelect.propTypes={isDisabled:o.a.bool,hasModuleAccess:o.a.bool,onChange:o.a.func}}).call(this,n(4))},916:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChipMultiSelect}));var r=n(1),i=n.n(r),a=n(785);function ChipMultiSelect(t){var n=t.children,r=t.onToggleChip,i=void 0===r?function(){}:r;return e.createElement(a.a,{value:{onToggleChip:i}},e.createElement("div",{className:"googlesitekit-chip-multi-select"},n))}ChipMultiSelect.propTypes={children:i.a.node.isRequired,onToggleChip:i.a.func}}).call(this,n(4))},917:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChipMultiSelectItem}));var r=n(6),i=n.n(r),a=n(21),o=n.n(a),c=n(25),s=n.n(c),l=n(14),u=n(1),d=n.n(u),f=n(0),g=n(10),p=n(785),m=n(1080);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ChipMultiSelectItem(t){var n=t.children,r=t.id,i=t.selected,a=s()(t,["children","id","selected"]),c=Object(f.useContext)(p.b).onToggleChip;return e.createElement(g.Chip,o()({className:"googlesitekit-chip-multi-select__item",CheckMark:m.a,onClick:function(){c(r,!i)},id:r,label:n,selected:i},a))}ChipMultiSelectItem.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({children:d.a.string.isRequired,id:d.a.string.isRequired},Object(l.omit)(g.Chip.propTypes,"label"))}).call(this,n(4))},918:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ProductIDSelect}));var r=n(1),i=n.n(r),a=n(2),o=n(0),c=n(38),s=n(20),l=n(42),u=n(10),d=n(3),f=n(515);function ProductIDSelect(t){var n=t.isDisabled,r=t.hasModuleAccess,i=t.showHelperText,g=void 0===i||i,p=t.className,m=t.onChange,b=void 0===m?function(){}:m,h=Object(d.useSelect)((function(e){return e(l.c).getProductID()})),v=Object(d.useSelect)((function(e){return e(l.c).getCurrentProductIDs()})),O=Object(d.useDispatch)(l.c).setProductID,E=Object(o.useCallback)((function(e,t){var n=t.dataset.value;O(n),b(n)}),[O,b]);return!1===r?e.createElement(u.Select,{className:p,label:Object(a.__)("Default Product ID","google-site-kit"),value:h,enhanced:!0,outlined:!0,disabled:!0},e.createElement(u.Option,{value:h},h)):e.createElement(u.Select,{className:p,label:Object(a.__)("Default Product ID","google-site-kit"),value:h,onEnhancedChange:E,disabled:n,enhanced:!0,outlined:!0,helperText:g?Object(c.a)(Object(a.__)("Product IDs offer a way to link content to payment plans. <a>Learn more</a>","google-site-kit"),{a:e.createElement(s.a,{"aria-label":Object(a.__)("Learn more about product IDs","google-site-kit"),href:"https://support.google.com/news/publisher-center/answer/12345540",external:!0,hideExternalIndicator:!0})}):void 0},e.createElement(u.Option,{key:"openaccess",value:"openaccess"},Object(a.__)("Open access","google-site-kit")),(v||[]).map((function(t){return e.createElement(u.Option,{key:t,value:t},Object(f.b)(t))})))}ProductIDSelect.propTypes={isDisabled:i.a.bool,hasModuleAccess:i.a.bool,showHelperText:i.a.bool,className:i.a.string,onChange:i.a.func}}).call(this,n(4))},919:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SnippetModeSelect}));var r=n(1),i=n.n(r),a=n(2),o=n(0),c=n(38),s=n(20),l=n(10),u=n(3),d=n(13),f=n(42),g=n(354);function SnippetModeSelect(t){var n=t.isDisabled,r=t.hasModuleAccess,i=t.className,p=t.onChange,m=void 0===p?function(){}:p,b=Object(u.useSelect)((function(e){return e(f.c).getSnippetMode()})),h=Object(u.useSelect)((function(e){return e(d.c).getDocumentationLinkURL("rrm-content-settings")})),v=Object(u.useDispatch)(f.c).setSnippetMode,O=Object(o.useCallback)((function(e,t){var n=t.dataset.value;v(n),m(n)}),[v,m]);return!1===r?e.createElement(l.Select,{className:i,label:Object(a.__)("Display CTAs","google-site-kit"),value:b,enhanced:!0,outlined:!0,disabled:!0},e.createElement(l.Option,{value:b},g.g[b])):e.createElement(l.Select,{className:i,label:Object(a.__)("Display CTAs","google-site-kit"),value:b,onEnhancedChange:O,disabled:n,enhanced:!0,outlined:!0,helperText:Object(c.a)(Object(a.__)("Use the new settings in the block editor to customize where your CTAs appear. <a>Learn more</a>","google-site-kit"),{a:e.createElement(s.a,{"aria-label":Object(a.__)("Learn more about Reader Revenue Manager settings in the block editor","google-site-kit"),href:h,external:!0,hideExternalIndicator:!0})})},Object.keys(g.g).map((function(t){return e.createElement(l.Option,{key:t,value:t},g.g[t])})))}SnippetModeSelect.propTypes={isDisabled:i.a.bool,hasModuleAccess:i.a.bool,className:i.a.string,onChange:i.a.func}}).call(this,n(4))},92:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Dismiss}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(1),u=n.n(l),d=n(2),f=n(3),g=n(73),p=n(41),m=n(10);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Dismiss(t){var n=t.id,r=t.primary,a=void 0===r||r,o=t.dismissLabel,c=void 0===o?Object(d.__)("OK, Got it!","google-site-kit"):o,l=t.dismissExpires,u=void 0===l?0:l,b=t.disabled,v=t.onDismiss,O=void 0===v?function(){}:v,E=t.gaTrackingEventArgs,_=t.dismissOptions,y=Object(g.a)(n,null==E?void 0:E.category),k=Object(f.useDispatch)(p.a).dismissNotification,j=function(){var e=s()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==O?void 0:O(t);case 2:y.dismiss(null==E?void 0:E.label,null==E?void 0:E.value),k(n,h(h({},_),{},{expiresInSeconds:u}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(m.Button,{tertiary:!a,onClick:j,disabled:b},c)}Dismiss.propTypes={id:u.a.string,primary:u.a.bool,dismissLabel:u.a.string,dismissExpires:u.a.number,disabled:u.a.bool,onDismiss:u.a.func,gaTrackingEventArgs:u.a.shape({label:u.a.string,value:u.a.string})}}).call(this,n(4))},920:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupMain}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(6),s=n.n(c),l=n(1),u=n.n(l),d=n(0),f=n(422),g=n(2),p=n(29),m=n(42),b=n(3),h=n(353),v=n(10),O=n(389),E=n(786),_=n(756);function SetupMain(t){var n=t.finishSetup,r=void 0===n?function(){}:n,a=Object(b.useSelect)((function(e){return e(m.c).getPublications()})),c=Object(b.useSelect)((function(e){return e(m.c).hasFinishedResolution("getPublications")})),l=Object(b.useSelect)((function(e){return e(p.a).getValue(m.g,m.i)})),u=Object(b.useSelect)((function(e){return e(p.a).getValue(m.g,m.h)})),y=Object(b.useSelect)((function(e){return e(m.c).getPublicationID()})),k=Object(b.useDispatch)(p.a).setValues,j=Object(b.useDispatch)(m.c),S=j.resetPublications,w=j.submitChanges,x=Object(d.useCallback)((function(){u&&S()}),[S,u]);Object(h.a)(x,15e3),Object(d.useEffect)((function(){l||!c||void 0===a||a.length||k(m.g,s()({},m.i,!0))}),[c,l,a,k]);var C=Object(f.a)(y);Object(d.useEffect)((function(){C!==y&&u&&k(m.g,s()({},m.h,!1))}),[C,y,k,u]);var N,D=Object(d.useCallback)(o()(i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,w();case 2:t=e.sent,t.error||r();case 5:case"end":return e.stop()}}),e)}))),[r,w]);return N=c?l?e.createElement(O.c,{onCompleteSetup:D}):e.createElement(_.a,{onCompleteSetup:D}):e.createElement(v.ProgressBar,null),e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--reader-revenue-manager"},e.createElement("div",{className:"googlesitekit-setup-module__step"},e.createElement("div",{className:"googlesitekit-setup-module__logo"},e.createElement(E.a,{width:"40",height:"40"})),e.createElement("h2",{className:"googlesitekit-heading-3 googlesitekit-setup-module__title"},Object(g._x)("Reader Revenue Manager","Service name","google-site-kit"))),e.createElement("div",{className:"googlesitekit-setup-module__step"},N))}SetupMain.propTypes={finishSetup:u.a.func}}).call(this,n(4))},921:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsEdit}));var r=n(15),i=n.n(r),a=n(420),o=n(0),c=n(10),s=n(3),l=n(18),u=n(515),d=n(9),f=n(19),g=n(13),p=n(42),m=n(354),b=n(758);function SettingsEdit(){var t=Object(l.a)(),n=Object(s.useSelect)((function(e){return e(p.c).isDoingSubmitChanges()})),r=Object(s.useSelect)((function(e){var t=e(f.a),n=t.hasModuleOwnershipOrAccess,r=t.getErrorForAction,i=n(p.e);if(i)return!0;var a=r("checkModuleAccess",[p.e]);return void 0!==i||a?!1!==i&&"module_not_connected"===(null==a?void 0:a.code):void 0})),h=Object(s.useSelect)((function(e){return e(p.c).haveSettingsChanged()})),v=Object(s.useSelect)((function(e){return e(g.c).getPostTypes()})),O=Object(s.useSelect)((function(e){return e(p.c).getSettings()})),E=O||{},_=E.snippetMode,y=E.postTypes,k=Object(o.useState)(O),j=i()(k,2),S=j[0],w=j[1];return Object(o.useEffect)((function(){!S&&O&&w(O)}),[S,O]),Object(a.a)((function(){h||(_!==S.snippetMode&&Object(d.I)("".concat(t,"_rrm-settings"),"change_snippet_mode",m.g[_]),Object(u.a)(y)!==Object(u.a)(S.postTypes)&&Object(d.I)("".concat(t,"_rrm-settings"),"change_post_types",Object(u.a)(y,v)))})),n||void 0===r?e.createElement(c.ProgressBar,null):e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--reader-revenue-manager googlesitekit-rrm-settings-edit"},e.createElement(b.a,{hasModuleAccess:r}))}}).call(this,n(4))},922:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsView}));var r=n(2),i=n(3),a=n(515),o=n(19),c=n(13),s=n(42),l=n(354),u=n(182),d=n(389);function SettingsView(){var t=Object(i.useSelect)((function(e){return e(s.c).getPublicationID()})),n=Object(i.useSelect)((function(e){var t=e(s.c).getProductID();return"openaccess"===t?Object(r.__)("Open access","google-site-kit"):Object(a.b)(t)})),f=Object(i.useSelect)((function(e){return e(s.c).getSnippetMode()})),g=Object(i.useSelect)((function(e){var t=e(c.c).getPostTypes(),n=e(s.c).getPostTypes();return Object(a.a)(n,t)})),p=Object(i.useSelect)((function(e){var t=e(o.a),n=t.hasModuleOwnershipOrAccess,r=t.getErrorForAction,i=n(s.e);if(i)return!0;var a=r("checkModuleAccess",[s.e]);return void 0!==i||a?!1!==i&&"module_not_connected"===(null==a?void 0:a.code):void 0}));return e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--reader-revenue-manager"},e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Publication","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:t}))),e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Default Product ID","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:n})))),p&&e.createElement(d.d,null),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Display CTAs","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:l.g[f]||f}))),"post_types"===f&&e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Content type to display CTAs","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(u.b,{value:g})))))}}).call(this,n(4))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o}));var r=n(239),i=n(85),a=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=n.invertColor,o=void 0!==a&&a;return Object(r.a)(e.createElement(i.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(6),i=n.n(r),a=n(14),o=n(100),c=n(101);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=l(l({},u),t);i.referenceSiteURL&&(i.referenceSiteURL=i.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(i,n),d=Object(c.a)(i,n,s,r),f={},g=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);f[r]||(f[r]=Object(a.once)(d)),f[r].apply(f,t)};return{enableTracking:function(){i.trackingEnabled=!0},disableTracking:function(){i.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!i.trackingEnabled},trackEvent:d,trackEventOnce:g}}}).call(this,n(28))}},[[1295,1,0]]]);