(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[30],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(59),i=n(39),a=n(57);function o(t,n){var o,c=Object(r.a)(n),s=t.activeModules,l=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,g=void 0===d?[]:d,f=t.isAuthenticated,m=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(i.b,"]"))),!o){o=!0;var r=(null==g?void 0:g.length)?g.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:l,plugin_version:m||"",enabled_features:Array.from(a.a).join(","),active_modules:s.join(","),authenticated:f?"1":"0",user_properties:{user_roles:r,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(i.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a)}}}}}).call(this,n(28))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n,r){var a=Object(l.a)(t);return function(){var t=s()(i.a.mark((function t(o,c,s,l){var u;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:s,value:l},t.abrupt("return",new Promise((function(e){var t,n,i=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(i),e()};a("event",c,d(d({},u),{},{event_callback:s})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,i){return t.apply(this,arguments)}}()}},102:function(e,t,n){"use strict";var r=n(123);n.d(t,"a",(function(){return r.a}));var i=n(124);n.d(t,"c",(function(){return i.a}));var a=n(125);n.d(t,"b",(function(){return a.a}))},104:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return r.createElement("svg",i({viewBox:"0 0 14 14",fill:"none"},e),a)}},105:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l);function VisuallyHidden(t){var n=t.className,r=t.children,a=o()(t,["className","children"]);return r?e.createElement("span",i()({},a,{className:u()("screen-reader-text",n)}),r):null}VisuallyHidden.propTypes={className:s.a.string,children:s.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(4))},107:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var r=n(21),i=n.n(r),a=n(152),o=n.n(a),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(2),g=n(10),f=n(154),m=n(104);function TourTooltip(t){var n=t.backProps,r=t.closeProps,c=t.index,l=t.primaryProps,u=t.size,p=t.step,b=t.tooltipProps,v=u>1?Object(f.a)(u):[],h=function(e){return s()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",i()({className:s()("googlesitekit-tour-tooltip",p.className)},b),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},p.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},p.content)),e.createElement(a.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},v.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:h(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(g.Button,i()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),p.cta,l.title&&e.createElement(g.Button,i()({className:"googlesitekit-tooltip-button",text:!0},l),l.title))),e.createElement(g.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(m.a,{width:"14",height:"14"}),onClick:r.onClick,"aria-label":Object(d.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(4))},109:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(208),s=n(38),l=n(2),u=n(10),d=n(58);function ModalDialog(t){var n=t.className,r=void 0===n?"":n,i=t.dialogActive,a=void 0!==i&&i,g=t.handleDialog,f=void 0===g?null:g,m=t.onOpen,p=void 0===m?null:m,b=t.onClose,v=void 0===b?null:b,h=t.title,_=void 0===h?null:h,O=t.provides,E=t.handleConfirm,k=t.subtitle,y=t.confirmButton,j=void 0===y?null:y,S=t.dependentModules,N=t.danger,w=void 0!==N&&N,T=t.inProgress,C=void 0!==T&&T,A=t.small,x=void 0!==A&&A,I=t.medium,R=void 0!==I&&I,D=t.buttonLink,L=void 0===D?null:D,P=Object(c.a)(ModalDialog),M="googlesitekit-dialog-description-".concat(P),z=!(!O||!O.length);return e.createElement(u.Dialog,{open:a,onOpen:p,onClose:v,"aria-describedby":z?M:void 0,tabIndex:"-1",className:o()(r,{"googlesitekit-dialog-sm":x,"googlesitekit-dialog-md":R})},e.createElement(u.DialogTitle,null,w&&e.createElement(d.a,{width:28,height:28}),_),k?e.createElement("p",{className:"mdc-dialog__lead"},k):[],e.createElement(u.DialogContent,null,z&&e.createElement("section",{id:M,className:"mdc-dialog__provides"},e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},O.map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t},e.createElement("span",{className:"mdc-list-item__text"},t))})))),S&&e.createElement("p",{className:"mdc-dialog__dependencies"},Object(s.a)(Object(l.sprintf)(
/* translators: %s is replaced with the dependent modules. */
Object(l.__)("<strong>Note:</strong> %s","google-site-kit"),S),{strong:e.createElement("strong",null)}))),e.createElement(u.DialogFooter,null,e.createElement(u.Button,{className:"mdc-dialog__cancel-button",tertiary:!0,onClick:f,disabled:C},Object(l.__)("Cancel","google-site-kit")),L?e.createElement(u.Button,{href:L,onClick:E,target:"_blank",danger:w},j):e.createElement(u.SpinnerButton,{onClick:E,danger:w,disabled:C,isSaving:C},j||Object(l.__)("Disconnect","google-site-kit"))))}ModalDialog.displayName="Dialog",ModalDialog.propTypes={className:i.a.string,dialogActive:i.a.bool,handleDialog:i.a.func,handleConfirm:i.a.func.isRequired,onOpen:i.a.func,onClose:i.a.func,title:i.a.string,confirmButton:i.a.string,danger:i.a.bool,small:i.a.bool,medium:i.a.bool,buttonLink:i.a.string},t.a=ModalDialog}).call(this,n(4))},1100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UserInputApp}));var r=n(0),i=n(2),a=n(3),o=n(10),c=n(7),s=n(17),l=n(234),u=n(235),d=n(365),g=n(1101),f=n(192),m=n(87),p=n(29);function UserInputApp(){var t,n=Object(a.useSelect)((function(e){return e(p.a).getValue(m.a,"questionNumber")}))||1,b=(null===(t=Object(m.m)()[n-1])||void 0===t?void 0:t.title)||"",v=Object(a.useSelect)((function(e){return e(c.a).getUserInputSettings(),e(c.a).hasFinishedResolution("getUserInputSettings")}));return e.createElement(r.Fragment,null,e.createElement(l.a,null,e.createElement(u.a,null)),e.createElement("div",{className:"googlesitekit-user-input"},e.createElement("div",{className:"googlesitekit-module-page"},!v&&e.createElement(s.e,null,e.createElement(s.k,null,e.createElement(s.a,{lgSize:12,mdSize:8,smSize:4},e.createElement(o.ProgressBar,null)))),v&&e.createElement(s.e,null,e.createElement(f.a,{rounded:!0},e.createElement(s.e,{className:"googlesitekit-user-input__header"},e.createElement(s.k,null,e.createElement(s.a,{size:12,className:"googlesitekit-user-input__question-number"},Object(i.sprintf)(
/*  translators: %d is replaced with the current page number (1, 2, or 3 etc.). */
Object(i.__)("%d / 3","google-site-kit"),n))),e.createElement(s.k,null,e.createElement(s.a,{lgSize:12},e.createElement(d.a,{className:"googlesitekit-heading-3 googlesitekit-user-input__heading",title:b,fullWidth:!0})))),e.createElement(s.e,{className:"googlesitekit-user-input__content"},e.createElement(s.k,null,e.createElement(s.a,{lgSize:12,mdSize:8,smSize:4},e.createElement(g.a,null)))))))))}}).call(this,n(4))},1101:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return UserInputQuestionnaire}));var i=n(5),a=n.n(i),o=n(16),c=n.n(o),s=n(15),l=n.n(s),u=n(0),d=n(3),g=n(804),f=n(604),m=n(87),p=n(147),b=n(7),v=n(13),h=n(32),_=n(9),O=n(18),E=n(29),k=n(1103),y=n(8),j=n(19);function UserInputQuestionnaire(){var t=Object(O.a)(),n=Object(p.a)("question",m.h[0]),i=l()(n,2),o=i[0],s=i[1],S=m.h.indexOf(o);-1===S&&s(m.h[0]);var N=Object(d.useDispatch)(E.a).setValues,w=Object(d.useSelect)((function(e){return e(E.a).getValue(m.a,"questionNumber")}))||1,T=Object(d.useDispatch)(b.a).saveUserInputSettings,C=Object(d.useDispatch)(h.a).navigateTo,A=Object(d.useSelect)((function(e){return e(v.c).getAdminURL("googlesitekit-dashboard")})),x=Object(d.useSelect)((function(e){return e(b.a).getErrorForAction("saveUserInputSettings",[])})),I="".concat(t,"_kmw");Object(u.useEffect)((function(){var e;o===m.i&&(e="site_purpose_question_view"),o===m.j&&(e="content_frequency_question_view"),o===m.g&&(e="site_goals_question_view"),e&&Object(_.I)(I,e)}),[o,I,t]);var R=Object(m.k)(),D=R.USER_INPUT_ANSWERS_PURPOSE,L=R.USER_INPUT_ANSWERS_GOALS,P=R.USER_INPUT_ANSWERS_POST_FREQUENCY,M=Object(m.l)().USER_INPUT_ANSWERS_PURPOSE,z=function(){e.scrollTo({top:0,left:0,behavior:"smooth"})},B=Object(u.useCallback)((function(){Object(_.I)(I,"question_advance",m.h[S]),s(m.h[S+1]),N(m.a,{questionNumber:w+1}),z()}),[S,I,s,N,w]),H=Object(u.useCallback)((function(){Object(_.I)(I,"question_return",m.h[S]),s(m.h[S-1]),N(m.a,{questionNumber:w-1}),z()}),[S,I,s,N,w]),U=Object(d.useSelect)((function(e){return e(j.a).isModuleConnected("analytics-4")?e(y.r).getUserInputPurposeConversionEvents():[]})),F=Object(d.useDispatch)(b.a).setUserInputSetting,V=Object(u.useCallback)(c()(a.a.mark((function e(){var t;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return Object(_.I)(I,"summary_submit"),F("includeConversionEvents",U),e.next=4,T();case 4:e.sent.error||(t=new URL(A),C(t.toString()));case 6:case"end":return e.stop()}}),e)}))),[I,T,U,A,F,C]),W=Object(d.useSelect)((function(e){return e(b.a).getUserInputSettings()})),q=Object(d.useSelect)((function(e){return e(b.a).isSavingUserInputSettings(W)})),G=Object(d.useSelect)((function(e){return e(h.a).isNavigating()})),K=q||G,X=Object(u.useCallback)((function(){K||V()}),[K,V]),Y=r.createElement(k.a,{currentSegment:S+1,totalSegments:m.h.length,className:"googlesitekit-user-input__question--progress"});return r.createElement("div",null,r.createElement("div",{className:"googlesitekit-user-input__question-progress"},Y),S===m.h.indexOf(m.i)&&r.createElement(g.a,{slug:m.i,questionNumber:1,next:B,error:x},r.createElement(f.a,{slug:m.i,max:m.e[m.i],options:D,descriptions:M,next:B,showInstructions:!0})),S===m.h.indexOf(m.j)&&r.createElement(g.a,{slug:m.j,questionNumber:2,next:B,back:H,error:x},r.createElement(f.a,{slug:m.j,max:m.e[m.j],options:P,next:B,showInstructions:!0})),S===m.h.indexOf(m.g)&&r.createElement(g.a,{slug:m.g,questionNumber:3,complete:X,back:H,error:x},r.createElement(f.a,{slug:m.g,max:m.e[m.g],options:L,next:X,showInstructions:!0})))}}).call(this,n(28),n(4))},1102:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UserInputQuestionInfo}));var r=n(1),i=n.n(r),a=n(0),o=n(2),c=n(3),s=n(13),l=n(7),u=n(17),d=n(624),g=n(682),f=n(87);function UserInputQuestionInfo(t){var n,r=t.slug,i=t.questionNumber,m=Object(c.useSelect)((function(e){return e(s.c).hasMultipleAdmins()})),p=Object(c.useSelect)((function(e){return e(l.a).getUserInputSettingScope(r)})),b=Object(c.useSelect)((function(e){return e(l.a).getUserInputSettingAuthor(r)})),v=(null===(n=Object(f.m)()[i-1])||void 0===n?void 0:n.description)||"";return e.createElement(a.Fragment,null,e.createElement(u.a,{className:"googlesitekit-user-input__question-instructions",lgSize:6,mdSize:8,smSize:4},v&&e.createElement("p",{className:"googlesitekit-user-input__question-instructions--description"},v),e.createElement(d.a,{className:"googlesitekit-non-desktop-display-none"})),e.createElement(u.a,{className:"googlesitekit-user-input__question-info",lgSize:5,mdSize:8,smSize:4,smOrder:3},e.createElement(d.a,{className:"googlesitekit-desktop-display-none "}),"site"===p&&m&&e.createElement("p",null,b?Object(o.__)("This answer can be edited by all Site Kit admins","google-site-kit"):Object(o.__)("Your answer to this question will apply to all Site Kit users. Any other admins with access to Site Kit can see and edit this response.","google-site-kit")),e.createElement(g.a,{slug:r})))}UserInputQuestionInfo.propTypes={slug:i.a.string.isRequired,questionNumber:i.a.number}}).call(this,n(4))},1103:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ProgressSegments}));var r=n(11),i=n.n(r);function ProgressSegments(t){var n=t.currentSegment,r=t.totalSegments,a=t.className;return e.createElement("div",{className:i()("googlesitekit-progress-segments",a)},Array.from(Array(r).keys()).map((function(t){return e.createElement("div",{key:t,className:i()("googlesitekit-progress-segments__segment",{"googlesitekit-progress-segments__segment--active":t+1<=n})})})))}}).call(this,n(4))},111:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(1),i=n.n(r),a=n(0),o=n(9),c=n(54);function Description(t){var n=t.className,r=void 0===n?"googlesitekit-publisher-win__desc":n,i=t.text,s=t.learnMoreLink,l=t.errorText,u=t.children;return e.createElement(a.Fragment,null,e.createElement("div",{className:r},e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.F)(i,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",s)),l&&e.createElement(c.a,{message:l}),u)}Description.propTypes={className:i.a.string,text:i.a.string,learnMoreLink:i.a.node,errorText:i.a.string,children:i.a.node}}).call(this,n(4))},112:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(392),i=function(e,t,n){Object(r.a)((function(n){return e.includes(n.keyCode)&&t.current.contains(n.target)}),n)}},120:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var r=n(1),i=n.n(r),a=n(0),o=n(2),c=n(3),s=n(10),l=n(35),u=n(54);function ErrorNotice(t){var n,r=t.error,i=t.hasButton,d=void 0!==i&&i,g=t.storeName,f=t.message,m=void 0===f?r.message:f,p=t.noPrefix,b=void 0!==p&&p,v=t.skipRetryMessage,h=t.Icon,_=Object(c.useDispatch)(),O=Object(c.useSelect)((function(e){return g?e(g).getSelectorDataForError(r):null})),E=Object(a.useCallback)((function(){_(O.storeName).invalidateResolution(O.name,O.args)}),[_,O]);if(!r||Object(l.f)(r))return null;var k=d&&Object(l.d)(r,O);return d||v||(m=Object(o.sprintf)(
/* translators: %1$s: Error message from Google API. */
Object(o.__)("%1$s%2$s Please try again.","google-site-kit"),m,m.endsWith(".")?"":".")),e.createElement(a.Fragment,null,h&&e.createElement("div",{className:"googlesitekit-error-notice__icon"},e.createElement(h,{width:"24",height:"24"})),e.createElement(u.a,{message:m,reconnectURL:null===(n=r.data)||void 0===n?void 0:n.reconnectURL,noPrefix:b}),k&&e.createElement(s.Button,{className:"googlesitekit-error-notice__retry-button",onClick:E},Object(o.__)("Retry","google-site-kit")))}ErrorNotice.propTypes={error:i.a.shape({message:i.a.string}),hasButton:i.a.bool,storeName:i.a.string,message:i.a.string,noPrefix:i.a.bool,skipRetryMessage:i.a.bool,Icon:i.a.elementType}}).call(this,n(4))},123:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(21),i=n.n(r),a=n(6),o=n.n(a),c=n(25),s=n.n(c),l=n(1),u=n.n(l),d=n(11),g=n.n(d);function Cell(t){var n,r=t.className,a=t.alignTop,c=t.alignMiddle,l=t.alignBottom,u=t.alignRight,d=t.alignLeft,f=t.smAlignRight,m=t.mdAlignRight,p=t.lgAlignRight,b=t.smSize,v=t.smStart,h=t.smOrder,_=t.mdSize,O=t.mdStart,E=t.mdOrder,k=t.lgSize,y=t.lgStart,j=t.lgOrder,S=t.size,N=t.children,w=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",i()({},w,{className:g()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":l,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":f,"mdc-layout-grid__cell--align-right-tablet":m,"mdc-layout-grid__cell--align-right-desktop":p},o()(n,"mdc-layout-grid__cell--span-".concat(S),12>=S&&S>0),o()(n,"mdc-layout-grid__cell--span-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--start-".concat(y,"-desktop"),12>=y&&y>0),o()(n,"mdc-layout-grid__cell--order-".concat(j,"-desktop"),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(_,"-tablet"),8>=_&&_>0),o()(n,"mdc-layout-grid__cell--start-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--order-".concat(E,"-tablet"),8>=E&&E>0),o()(n,"mdc-layout-grid__cell--span-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--start-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--order-".concat(h,"-phone"),4>=h&&h>0),n))}),N)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},124:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),g=Object(d.forwardRef)((function(t,n){var r=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",i()({ref:n,className:u()("mdc-layout-grid__inner",r)},c),a)}));g.displayName="Row",g.propTypes={className:s.a.string,children:s.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(4))},125:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),g=Object(d.forwardRef)((function(t,n){var r=t.alignLeft,a=t.fill,c=t.className,s=t.children,l=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",i()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":l,"mdc-layout-grid--fill":a})},d,{ref:n}),s)}));g.displayName="Grid",g.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(4))},126:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},127:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},1271:function(e,t,n){"use strict";n.r(t),function(e){var t=n(332),r=n(144),i=n(224),a=n(1100),o=n(22);Object(t.a)((function(){var t=document.getElementById("js-googlesitekit-user-input");t&&Object(r.render)(e.createElement(i.a,{viewContext:o.t},e.createElement(a.a,null)),t)}))}.call(this,n(4))},128:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},129:function(e,t,n){"use strict";n.d(t,"a",(function(){return p})),n.d(t,"c",(function(){return v})),n.d(t,"b",(function(){return h}));var r=n(25),i=n.n(r),a=n(6),o=n.n(a),c=n(5),s=n.n(c),l=n(12),u=n.n(l),d=n(3),g=n.n(d),f=n(37),m=n(9),p=function(e){var t;u()(e,"storeName is required to create a snapshot store.");var n={},r={deleteSnapshot:s.a.mark((function e(){var t;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"DELETE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})),restoreSnapshot:s.a.mark((function e(){var t,n,r,i,a,o,c=arguments;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},n=t.clearAfterRestore,r=void 0===n||n,e.next=4,{payload:{},type:"RESTORE_SNAPSHOT"};case 4:if(i=e.sent,a=i.cacheHit,o=i.value,!a){e.next=13;break}return e.next=10,{payload:{snapshot:o},type:"SET_STATE_FROM_SNAPSHOT"};case 10:if(!r){e.next=13;break}return e.next=13,{payload:{},type:"DELETE_SNAPSHOT"};case 13:return e.abrupt("return",a);case 14:case"end":return e.stop()}}),e)})),createSnapshot:s.a.mark((function e(){var t;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"CREATE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))},a=(t={},o()(t,"DELETE_SNAPSHOT",(function(){return Object(f.c)("datastore::cache::".concat(e))})),o()(t,"CREATE_SNAPSHOT",Object(d.createRegistryControl)((function(t){return function(){return Object(f.f)("datastore::cache::".concat(e),t.stores[e].store.getState())}}))),o()(t,"RESTORE_SNAPSHOT",(function(){return Object(f.d)("datastore::cache::".concat(e),m.b)})),t);return{initialState:n,actions:r,controls:a,reducer:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1?arguments[1]:void 0,r=t.type,a=t.payload;switch(r){case"SET_STATE_FROM_SNAPSHOT":var o=a.snapshot,c=(o.error,i()(o,["error"]));return c;default:return e}}}},b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Object.values(e.stores).filter((function(e){return Object.keys(e.getActions()).includes("restoreSnapshot")}))},v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Promise.all(b(e).map((function(e){return e.getActions().createSnapshot()})))},h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Promise.all(b(e).map((function(e){return e.getActions().restoreSnapshot()})))}},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var r="core/site",i="primary",a="secondary"},130:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(14),i=function(e){return Object(r.isFinite)(e)?e:0}},134:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(38),s=n(2),l=n(20),u=n(34);function SourceLink(t){var n=t.name,r=t.href,i=t.className,a=t.external;return Object(u.a)()?null:e.createElement("div",{className:o()("googlesitekit-source-link",i)},Object(c.a)(Object(s.sprintf)(
/* translators: %s: source link */
Object(s.__)("Source: %s","google-site-kit"),"<a>".concat(n,"</a>")),{a:e.createElement(l.a,{key:"link",href:r,external:a})}))}SourceLink.propTypes={name:i.a.string,href:i.a.string,className:i.a.string,external:i.a.bool},SourceLink.defaultProps={name:"",href:"",className:"",external:!1},t.a=SourceLink}).call(this,n(4))},136:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"d",(function(){return a})),n.d(t,"c",(function(){return o}));function r(e){var t=e.format,n=void 0===t?"small":t,r=e.hasErrorOrWarning,i=e.hasSmallImageSVG,o=e.hasWinImageSVG,c={smSize:4,mdSize:8,lgSize:12},s=a(n);return Object.keys(c).forEach((function(e){var t=c[e];r&&(t-=1),i&&(t-=1),o&&0<t-s[e]&&(t-=s[e]),c[e]=t})),c}var i=function(e){switch(e){case"small":return{};case"larger":return{smOrder:2,mdOrder:2,lgOrder:1};default:return{smOrder:2,mdOrder:1}}},a=function(e){switch(e){case"smaller":return{smSize:4,mdSize:2,lgSize:2};case"larger":return{smSize:4,mdSize:8,lgSize:7};default:return{smSize:4,mdSize:2,lgSize:4}}},o=function(e){switch(e){case"larger":return{smOrder:1,mdOrder:1,lgOrder:2};default:return{smOrder:1,mdOrder:2}}}},139:function(e,t,n){"use strict";var r=n(0),i=Object(r.createContext)(!1);t.a=i},147:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(15),o=n.n(a),c=n(0),s=n(409),l=n(157);t.a=function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=Object(c.useState)(Object(s.a)(r.location.href,t)||n),u=o()(a,2),d=u[0],g=u[1],f=function(e){g(e);var n=Object(l.a)(r.location.href,i()({},t,e));r.history.replaceState(null,"",n)};return[d,f]}}).call(this,n(28))},148:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M3.332 24.75h21.335c2.182 0 3.542-2.366 2.451-4.25L16.451 2.07C15.36.184 12.64.184 11.549 2.07L.882 20.5c-1.091 1.884.269 4.25 2.45 4.25zM14 14.833a1.42 1.42 0 01-1.417-1.416v-2.834c0-.779.638-1.416 1.417-1.416.78 0 1.417.637 1.417 1.416v2.834A1.42 1.42 0 0114 14.833zm1.417 5.667h-2.834v-2.833h2.834V20.5z",fill:"currentColor",fillRule:"nonzero"});t.a=function SvgError(e){return r.createElement("svg",i({viewBox:"0 0 28 25"},e),a)}},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},155:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("path",{d:"M2.253 12.252l7.399 5.658A13.055 13.055 0 009 22c0 1.43.229 2.805.652 4.09l-7.4 5.658A22.02 22.02 0 010 22c0-3.506.81-6.814 2.253-9.748z",fill:"#FBBC05"}),r.createElement("path",{d:"M9.652 17.91l-7.4-5.658A21.935 21.935 0 0122 0c5.6 0 10.6 2.1 14.5 5.5l-6.4 6.4C27.9 10.1 25.1 9 22 9c-5.77 0-10.64 3.725-12.348 8.91z",fill:"#EA4335"}),r.createElement("path",{d:"M2.25 31.742l7.396-5.67A12.975 12.975 0 0022 35c6.1 0 10.7-3.1 11.8-8.5H22V18h20.5c.3 1.3.5 2.7.5 4 0 14-10 22-21 22A21.935 21.935 0 012.25 31.742z",fill:"#34A853"}),r.createElement("path",{d:"M36.34 38.52l-7.025-5.437c2.297-1.45 3.895-3.685 4.485-6.583H22V18h20.5c.3 1.3.5 2.7.5 4 0 7.17-2.623 12.767-6.66 16.52z",fill:"#4285F4"}));t.a=function SvgLogoG(e){return r.createElement("svg",i({viewBox:"0 0 43 44"},e),a)}},158:function(e,t,n){"use strict";var r=n(0),i=n(57),a=Object(r.createContext)(i.a);t.a=a},160:function(e,t,n){"use strict";var r=n(139),i=(r.a.Consumer,r.a.Provider);t.a=i},161:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(3),i=n(23),a=function(e){return"notification/".concat(e,"/viewed")};function o(e){return Object(r.useSelect)((function(t){return!!t(i.b).getValue(a(e))}),[e])}o.getKey=a},164:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(1),s=n.n(c),l=n(0),u=n(20),d=n(9),g=n(18);function HelpMenuLink(t){var n=t.children,r=t.href,a=t.gaEventLabel,c=Object(g.a)(),s=Object(l.useCallback)(o()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!a){e.next=3;break}return e.next=3,Object(d.I)("".concat(c,"_headerbar_helpmenu"),"click_outgoing_link",a);case 3:case"end":return e.stop()}}),e)}))),[a,c]);return e.createElement("li",{className:"googlesitekit-help-menu-link mdc-list-item",role:"none"},e.createElement(u.a,{className:"mdc-list-item__text",href:r,external:!0,hideExternalIndicator:!0,role:"menuitem",onClick:s},n))}HelpMenuLink.propTypes={children:s.a.node.isRequired,href:s.a.string.isRequired,gaEventLabel:s.a.string},t.a=HelpMenuLink}).call(this,n(4))},167:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notifications}));var r=n(6),i=n.n(r),a=n(1),o=n.n(a),c=n(3),s=n(18),l=n(41),u=n(283);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Notifications(t){var n,r=t.areaSlug,a=t.groupID,o=void 0===a?l.c.DEFAULT:a,g=Object(s.a)(),f=Object(c.useSelect)((function(e){return e(l.a).getQueuedNotifications(g,o)}));if(void 0===(null==f?void 0:f[0])||(null==f||null===(n=f[0])||void 0===n?void 0:n.areaSlug)!==r)return null;var m=f[0],p=m.id,b=m.Component,v=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},Object(u.a)(p));return e.createElement(b,v)}Notifications.propTypes={viewContext:o.a.string,areaSlug:o.a.string}}).call(this,n(4))},17:function(e,t,n){"use strict";var r=n(254);n.d(t,"i",(function(){return r.a}));var i=n(319);n.d(t,"f",(function(){return i.a}));var a=n(320);n.d(t,"h",(function(){return a.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var s=n(91),l=n.n(s);n.d(t,"b",(function(){return l.a})),n.d(t,"c",(function(){return s.DialogContent})),n.d(t,"d",(function(){return s.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},172:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GenericErrorHandlerActions}));var r=n(1),i=n.n(r),a=n(2),o=n(20),c=n(194);function GenericErrorHandlerActions(t){var n=t.message,r=t.componentStack;return e.createElement("div",{className:"googlesitekit-generic-error-handler-actions"},e.createElement(c.a,{message:n,componentStack:r}),e.createElement(o.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0},Object(a.__)("Report this problem","google-site-kit")))}GenericErrorHandlerActions.propTypes={message:i.a.string,componentStack:i.a.string}}).call(this,n(4))},173:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(22),i=function(e){return r.f.includes(e)}},18:function(e,t,n){"use strict";var r=n(0),i=n(61);t.a=function(){return Object(r.useContext)(i.b)}},183:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LoadingWrapper}));var r=n(6),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(44);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function LoadingWrapper(t){var n=t.loading,r=t.children,i=o()(t,["loading","children"]);return n?e.createElement(l.a,i):r}LoadingWrapper.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({loading:s.a.bool,children:s.a.node},l.a.propTypes)}).call(this,n(4))},189:function(e,t,n){"use strict";(function(e){function Title(t){var n=t.title;return e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n)}n.d(t,"a",(function(){return Title}))}).call(this,n(4))},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="core/modules",i="insufficient_module_dependencies"},191:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notification}));var r=n(15),i=n.n(r),a=n(1),o=n.n(a),c=n(0),s=n(282),l=n(161),u=n(73);function Notification(t){var n=t.id,r=t.className,a=t.gaTrackingEventArgs,o=t.children,d=t.onView,g=Object(c.useRef)(),f=Object(l.a)(n),m=Object(u.a)(n,null==a?void 0:a.category),p=Object(c.useState)(!1),b=i()(p,2),v=b[0],h=b[1];return Object(c.useEffect)((function(){!v&&f&&(m.view(null==a?void 0:a.label,null==a?void 0:a.value),null==d||d(),h(!0))}),[f,m,v,a,d]),e.createElement("section",{id:n,ref:g,className:r},o,!f&&e.createElement(s.a,{id:n,observeRef:g,threshold:.5}))}Notification.propTypes={id:o.a.string,className:o.a.string,gaTrackingEventArgs:o.a.shape({category:o.a.string,label:o.a.string,value:o.a.string}),children:o.a.node,onView:o.a.func}}).call(this,n(4))},192:function(e,t,n){"use strict";(function(e){var r=n(51),i=n.n(r),a=n(53),o=n.n(a),c=n(68),s=n.n(c),l=n(69),u=n.n(l),d=n(49),g=n.n(d),f=n(1),m=n.n(f),p=n(11),b=n.n(p),v=n(0),h=n(329),_=n(330);function O(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=g()(e);if(t){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return u()(this,n)}}var E=function(t){s()(Layout,t);var n=O(Layout);function Layout(){return i()(this,Layout),n.apply(this,arguments)}return o()(Layout,[{key:"render",value:function(){var t=this.props,n=t.header,r=t.footer,i=t.children,a=t.title,o=t.badge,c=t.headerCTALabel,s=t.headerCTALink,l=t.footerCTALabel,u=t.footerCTALink,d=t.footerContent,g=t.className,f=t.fill,m=t.relative,p=t.rounded,v=void 0!==p&&p,O=t.transparent,E=void 0!==O&&O;return e.createElement("div",{className:b()("googlesitekit-layout",g,{"googlesitekit-layout--fill":f,"googlesitekit-layout--relative":m,"googlesitekit-layout--rounded":v,"googlesitekit-layout--transparent":E})},n&&e.createElement(h.a,{title:a,badge:o,ctaLabel:c,ctaLink:s}),i,r&&e.createElement(_.a,{ctaLabel:l,ctaLink:u,footerContent:d}))}}]),Layout}(v.Component);E.propTypes={header:m.a.bool,footer:m.a.bool,children:m.a.node.isRequired,title:m.a.string,badge:m.a.node,headerCTALabel:m.a.string,headerCTALink:m.a.string,footerCTALabel:m.a.string,footerCTALink:m.a.string,footerContent:m.a.node,className:m.a.string,fill:m.a.bool,relative:m.a.bool,rounded:m.a.bool,transparent:m.a.bool},E.defaultProps={header:!1,footer:!1,title:"",badge:null,headerCTALabel:"",headerCTALink:"",footerCTALabel:"",footerCTALink:"",footerContent:null,className:"",fill:!1,relative:!1},t.a=E}).call(this,n(4))},194:function(e,t,n){"use strict";(function(e){var r=n(15),i=n.n(r),a=n(195),o=n.n(a),c=n(1),s=n.n(c),l=n(0),u=n(2),d=n(266),g=n(423),f=n(424),m=n(10);function ReportErrorButton(t){var n=t.message,r=t.componentStack,a=Object(l.useState)(!1),c=i()(a,2),s=c[0],p=c[1];return e.createElement(m.Button,{"aria-label":s?Object(u.__)("Error message copied to clipboard. Click to copy the error message again.","google-site-kit"):void 0,onClick:function(){o()("`".concat(n,"\n").concat(r,"`")),p(!0)},trailingIcon:e.createElement(d.a,{className:"mdc-button__icon",icon:s?g.a:f.a})},s?Object(u.__)("Copied to clipboard","google-site-kit"):Object(u.__)("Copy error contents","google-site-kit"))}ReportErrorButton.propTypes={message:s.a.string,componentStack:s.a.string},t.a=ReportErrorButton}).call(this,n(4))},196:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationError}));var r=n(17),i=n(222),a=n(189);function NotificationError(t){var n=t.title,o=t.description,c=t.actions;return e.createElement(r.e,null,e.createElement(r.k,null,e.createElement(r.a,{smSize:3,mdSize:7,lgSize:11,className:"googlesitekit-publisher-win__content"},e.createElement(a.a,{title:n}),o,c),e.createElement(i.a,{type:"win-error"})))}}).call(this,n(4))},197:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerNotification}));var r=n(21),i=n.n(r),a=n(6),o=n.n(a),c=n(5),s=n.n(c),l=n(16),u=n.n(l),d=n(15),g=n.n(d),f=n(1),m=n.n(f),p=n(11),b=n.n(p),v=n(206),h=n(240),_=n(81),O=n(0),E=n(106),k=n(3),y=n(17),j=n(93),S=n(37),N=n(24),w=n(211),T=n(213),C=n(212),A=n(226),x=n(227),I=n(86),R=n(136),D=n(130),L=n(32),P=n(228),M=n(79);function BannerNotification(t){var n,r=t.badgeLabel,a=t.children,c=t.className,l=void 0===c?"":c,d=t.ctaLabel,f=t.ctaLink,m=t.ctaTarget,p=t.description,z=t.dismiss,B=t.dismissExpires,H=void 0===B?0:B,U=t.format,F=void 0===U?"":U,V=t.id,W=t.isDismissible,q=void 0===W||W,G=t.learnMoreDescription,K=t.learnMoreLabel,X=t.learnMoreURL,Y=t.learnMoreTarget,Q=void 0===Y?I.a.EXTERNAL:Y,$=t.logo,J=t.module,Z=t.moduleName,ee=t.onCTAClick,te=t.onView,ne=t.onDismiss,re=t.onLearnMoreClick,ie=t.showOnce,ae=void 0!==ie&&ie,oe=t.SmallImageSVG,ce=t.title,se=t.type,le=t.WinImageSVG,ue=t.showSmallWinImage,de=void 0===ue||ue,ge=t.smallWinImageSVGWidth,fe=void 0===ge?75:ge,me=t.smallWinImageSVGHeight,pe=void 0===me?75:me,be=t.mediumWinImageSVGWidth,ve=void 0===be?105:be,he=t.mediumWinImageSVGHeight,_e=void 0===he?105:he,Oe=t.rounded,Ee=void 0!==Oe&&Oe,ke=t.footer,ye=t.secondaryPane,je=t.ctaComponent,Se=Object(O.useState)(!1),Ne=g()(Se,2),we=Ne[0],Te=Ne[1],Ce=Object(O.useState)(!1),Ae=g()(Ce,2),xe=Ae[0],Ie=Ae[1],Re="notification::dismissed::".concat(V),De=function(){return Object(S.f)(Re,new Date,{ttl:null})},Le=Object(M.a)(),Pe=Object(N.e)(),Me=Object(v.a)(),ze=Object(O.useState)(!1),Be=g()(ze,2),He=Be[0],Ue=Be[1],Fe=Object(O.useRef)(),Ve=Object(h.a)(Fe,{rootMargin:"".concat(-Object(D.a)(Object(j.c)(Pe)),"px 0px 0px 0px"),threshold:0});Object(O.useEffect)((function(){!He&&(null==Ve?void 0:Ve.isIntersecting)&&("function"==typeof te&&te(),Ue(!0))}),[V,te,He,Ve]);var We=Le>=600;Object(_.a)(u()(s.a.mark((function e(){var t,n;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(H>0)){e.next=3;break}return e.next=3,$e();case 3:if(!q){e.next=9;break}return e.next=6,Object(S.d)(Re);case 6:t=e.sent,n=t.cacheHit,Ie(n);case 9:if(!ae){e.next=12;break}return e.next=12,De();case 12:case"end":return e.stop()}}),e)}))));var qe=function(){var e=u()(s.a.mark((function e(t){return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),t.preventDefault(),!ne){e.next=5;break}return e.next=5,ne(t);case 5:Ke();case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Ge=Object(E.a)(f)&&"_blank"!==m,Ke=function(){return Ge||Te(!0),new Promise((function(e){setTimeout(u()(s.a.mark((function t(){var n;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,De();case 2:Me()&&Ie(!0),n=new Event("notificationDismissed"),document.dispatchEvent(n),e();case 6:case"end":return t.stop()}}),t)}))),350)}))},Xe=Object(k.useSelect)((function(e){return!!f&&e(L.a).isNavigatingTo(f)})),Ye=Object(k.useDispatch)(L.a).navigateTo,Qe=function(){var e=u()(s.a.mark((function e(t){var n,r,i;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),Ge&&!t.defaultPrevented&&t.preventDefault(),n=!0,!ee){e.next=12;break}return e.next=6,ee(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:r=e.t0,i=r.dismissOnCTAClick,n=void 0===i||i;case 12:if(!q||!n){e.next=15;break}return e.next=15,Ke();case 15:Ge&&Ye(f);case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),$e=function(){var e=u()(s.a.mark((function e(){var t,n,r;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(S.d)(Re);case 2:if(t=e.sent,!(n=t.value)){e.next=10;break}if((r=new Date(n)).setSeconds(r.getSeconds()+parseInt(H,10)),!(r<new Date)){e.next=10;break}return e.next=10,Object(S.c)(Re);case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();if(!Xe&&q&&(void 0===xe||xe))return null;var Je=!Xe&&we?"is-closed":"is-open",Ze=Object(R.d)(F),et=Object(R.c)(F),tt=Object(R.a)(F),nt=Object(R.b)({format:F,hasErrorOrWarning:"win-error"===se||"win-warning"===se,hasSmallImageSVG:!!oe,hasWinImageSVG:!!le});return e.createElement(w.a,{id:V,className:b()(l,(n={},o()(n,"googlesitekit-publisher-win--".concat(F),F),o()(n,"googlesitekit-publisher-win--".concat(se),se),o()(n,"googlesitekit-publisher-win--".concat(Je),Je),o()(n,"googlesitekit-publisher-win--rounded",Ee),n)),secondaryPane:ye,ref:Fe},$&&e.createElement(x.a,{module:J,moduleName:Z}),oe&&e.createElement(y.a,{size:1,className:"googlesitekit-publisher-win__small-media"},e.createElement(oe,null)),e.createElement(y.a,i()({},nt,tt,{className:"googlesitekit-publisher-win__content"}),e.createElement(T.a,{title:ce,badgeLabel:r,smallWinImageSVGHeight:pe,smallWinImageSVGWidth:fe,winImageFormat:F,WinImageSVG:!We&&de?le:void 0}),e.createElement(P.a,{description:p,learnMoreURL:X,learnMoreLabel:K,learnMoreTarget:Q,learnMoreDescription:G,onLearnMoreClick:re}),a,e.createElement(C.a,{ctaLink:f,ctaLabel:d,ctaComponent:je,ctaTarget:m,ctaCallback:Qe,dismissLabel:q?z:void 0,dismissCallback:qe}),ke&&e.createElement("div",{className:"googlesitekit-publisher-win__footer"},ke)),le&&(We||!de)&&e.createElement(y.a,i()({},Ze,et,{alignBottom:"larger"===F,className:"googlesitekit-publisher-win__image"}),e.createElement("div",{className:"googlesitekit-publisher-win__image-".concat(F)},e.createElement(le,{style:{maxWidth:ve,maxHeight:_e}}))),e.createElement(A.a,{type:se}))}BannerNotification.propTypes={id:m.a.string.isRequired,className:m.a.string,title:m.a.string.isRequired,description:m.a.node,learnMoreURL:m.a.string,learnMoreDescription:m.a.string,learnMoreLabel:m.a.string,learnMoreTarget:m.a.oneOf(Object.values(I.a)),WinImageSVG:m.a.elementType,SmallImageSVG:m.a.elementType,format:m.a.string,ctaLink:m.a.string,ctaLabel:m.a.string,type:m.a.string,dismiss:m.a.string,isDismissible:m.a.bool,logo:m.a.bool,module:m.a.string,moduleName:m.a.string,dismissExpires:m.a.number,showOnce:m.a.bool,onCTAClick:m.a.func,onView:m.a.func,onDismiss:m.a.func,onLearnMoreClick:m.a.func,badgeLabel:m.a.string,rounded:m.a.bool,footer:m.a.node,secondaryPane:m.a.node,showSmallWinImage:m.a.bool,smallWinImageSVGWidth:m.a.number,smallWinImageSVGHeight:m.a.number,mediumWinImageSVGWidth:m.a.number,mediumWinImageSVGHeight:m.a.number}}).call(this,n(4))},198:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleIcon}));var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(3),u=n(19);function ModuleIcon(t){var n=t.slug,r=t.size,a=o()(t,["slug","size"]),c=Object(l.useSelect)((function(e){return e(u.a).getModuleIcon(n)}));return c?e.createElement(c,i()({width:r,height:r},a)):null}ModuleIcon.propTypes={slug:s.a.string.isRequired,size:s.a.number},ModuleIcon.defaultProps={size:33}}).call(this,n(4))},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(146),g=n(0),f=n(2),m=n(126),p=n(127),b=n(128),v=n(70),h=n(76),_=Object(g.forwardRef)((function(t,n){var r,a=t["aria-label"],c=t.secondary,l=void 0!==c&&c,u=t.arrow,g=void 0!==u&&u,_=t.back,O=void 0!==_&&_,E=t.caps,k=void 0!==E&&E,y=t.children,j=t.className,S=void 0===j?"":j,N=t.danger,w=void 0!==N&&N,T=t.disabled,C=void 0!==T&&T,A=t.external,x=void 0!==A&&A,I=t.hideExternalIndicator,R=void 0!==I&&I,D=t.href,L=void 0===D?"":D,P=t.inverse,M=void 0!==P&&P,z=t.noFlex,B=void 0!==z&&z,H=t.onClick,U=t.small,F=void 0!==U&&U,V=t.standalone,W=void 0!==V&&V,q=t.linkButton,G=void 0!==q&&q,K=t.to,X=t.leadingIcon,Y=t.trailingIcon,Q=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),$=L||K||!H?K?"ROUTER_LINK":x?"EXTERNAL_LINK":"LINK":C?"BUTTON_DISABLED":"BUTTON",J="BUTTON"===$||"BUTTON_DISABLED"===$?"button":"ROUTER_LINK"===$?d.b:"a",Z=("EXTERNAL_LINK"===$&&(r=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===$&&(r=Object(f._x)("(disabled)","screen reader text","google-site-kit")),r?a?"".concat(a," ").concat(r):"string"==typeof y?"".concat(y," ").concat(r):void 0:a),ee=X,te=Y;return O&&(ee=e.createElement(b.a,{width:14,height:14})),x&&!R&&(te=e.createElement(v.a,{width:14,height:14})),g&&!M&&(te=e.createElement(m.a,{width:14,height:14})),g&&M&&(te=e.createElement(p.a,{width:14,height:14})),e.createElement(J,i()({"aria-label":Z,className:s()("googlesitekit-cta-link",S,{"googlesitekit-cta-link--secondary":l,"googlesitekit-cta-link--inverse":M,"googlesitekit-cta-link--small":F,"googlesitekit-cta-link--caps":k,"googlesitekit-cta-link--danger":w,"googlesitekit-cta-link--disabled":C,"googlesitekit-cta-link--standalone":W,"googlesitekit-cta-link--link-button":G,"googlesitekit-cta-link--no-flex":!!B}),disabled:C,href:"LINK"!==$&&"EXTERNAL_LINK"!==$||C?void 0:L,onClick:H,rel:"EXTERNAL_LINK"===$?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===$?"_blank":void 0,to:K},Q),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},y),!!te&&e.createElement(h.a,{marginLeft:5},te))}));_.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=_}).call(this,n(4))},211:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(0),s=n(17),l=Object(c.forwardRef)((function(t,n){var r=t.id,i=t.className,a=t.children,l=t.secondaryPane;return e.createElement("section",{id:r,className:o()(i,"googlesitekit-publisher-win"),ref:n},e.createElement(s.e,null,e.createElement(s.k,null,a)),l&&e.createElement(c.Fragment,null,e.createElement("div",{className:"googlesitekit-publisher-win__secondary-pane-divider"}),e.createElement(s.e,{className:"googlesitekit-publisher-win__secondary-pane"},e.createElement(s.k,null,e.createElement(s.a,{className:"googlesitekit-publisher-win__secondary-pane",size:12},l)))))}));l.displayName="Banner",l.propTypes={id:i.a.string,className:i.a.string,secondaryPane:i.a.node},t.a=l}).call(this,n(4))},212:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerActions}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(15),s=n.n(c),l=n(1),u=n.n(l),d=n(206),g=n(0),f=n(3),m=n(10),p=n(32);function BannerActions(t){var n=t.ctaLink,r=t.ctaLabel,a=t.ctaComponent,c=t.ctaTarget,l=t.ctaCallback,u=t.dismissLabel,b=t.dismissCallback,v=Object(g.useState)(!1),h=s()(v,2),_=h[0],O=h[1],E=Object(d.a)(),k=Object(f.useSelect)((function(e){return!!n&&e(p.a).isNavigatingTo(n)})),y=function(){var e=o()(i.a.mark((function e(){var t,n,r,a=arguments;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(O(!0),t=a.length,n=new Array(t),r=0;r<t;r++)n[r]=a[r];return e.next=4,null==l?void 0:l.apply(void 0,n);case 4:E()&&O(!1);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return n||u||a?e.createElement("div",{className:"googlesitekit-publisher-win__actions"},a,r&&e.createElement(m.SpinnerButton,{className:"googlesitekit-notification__cta",href:n,target:c,onClick:y,disabled:_||k,isSaving:_||k},r),u&&e.createElement(m.Button,{tertiary:n||a,onClick:b,disabled:_||k},u)):null}BannerActions.propTypes={ctaLink:u.a.string,ctaLabel:u.a.string,ctaComponent:u.a.element,ctaTarget:u.a.string,ctaCallback:u.a.func,dismissLabel:u.a.string,dismissCallback:u.a.func}}).call(this,n(4))},213:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerTitle}));var r=n(6),i=n.n(r),a=n(1),o=n.n(a),c=n(11),s=n.n(c),l=n(77);function BannerTitle(t){var n=t.title,r=t.badgeLabel,a=t.WinImageSVG,o=t.winImageFormat,c=void 0===o?"":o,u=t.smallWinImageSVGWidth,d=void 0===u?75:u,g=t.smallWinImageSVGHeight,f=void 0===g?75:g;return n?e.createElement("div",{className:"googlesitekit-publisher-win__title-image-wrapper"},e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n,r&&e.createElement(l.a,{label:r})),a&&e.createElement("div",{className:s()(i()({},"googlesitekit-publisher-win__image-".concat(c),c))},e.createElement(a,{width:d,height:f}))):null}BannerTitle.propTypes={title:o.a.string,badgeLabel:o.a.string,WinImageSVG:o.a.elementType,winImageFormat:o.a.string,smallWinImageSVGWidth:o.a.number,smallWinImageSVGHeight:o.a.number}}).call(this,n(4))},218:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OptIn}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),g=n(38),f=n(2),m=n(3),p=n(10),b=n(7),v=n(36),h=n(20),_=n(18);function OptIn(t){var n=t.id,r=void 0===n?"googlesitekit-opt-in":n,a=t.name,c=void 0===a?"optIn":a,s=t.className,l=t.trackEventCategory,O=t.alignLeftCheckbox,E=void 0!==O&&O,k=Object(m.useSelect)((function(e){return e(b.a).isTrackingEnabled()})),y=Object(m.useSelect)((function(e){return e(b.a).isSavingTrackingEnabled()})),j=Object(m.useSelect)((function(e){return e(b.a).getErrorForAction("setTrackingEnabled",[!k])})),S=Object(m.useDispatch)(b.a).setTrackingEnabled,N=Object(_.a)(),w=Object(d.useCallback)(function(){var e=o()(i.a.mark((function e(t){var n,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S(!!t.target.checked);case 2:n=e.sent,r=n.response,n.error||(Object(v.a)(r.enabled),r.enabled&&Object(v.b)(l||N,"tracking_optin"));case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[S,l,N]);return e.createElement("div",{className:u()("googlesitekit-opt-in",s)},e.createElement(p.Checkbox,{id:r,name:c,value:"1",checked:k,disabled:y,onChange:w,loading:void 0===k,alignLeft:E},Object(g.a)(Object(f.__)("<span>Help us improve Site Kit by sharing anonymous usage data.</span> <span>All collected data is treated in accordance with the <a>Google Privacy Policy.</a></span>","google-site-kit"),{a:e.createElement(h.a,{key:"link",href:"https://policies.google.com/privacy",external:!0}),span:e.createElement("span",null)})),(null==j?void 0:j.message)&&e.createElement("div",{className:"googlesitekit-error-text"},null==j?void 0:j.message))}OptIn.propTypes={id:s.a.string,name:s.a.string,className:s.a.string,trackEventCategory:s.a.string,alignLeftCheckbox:s.a.bool}}).call(this,n(4))},22:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return i})),n.d(t,"o",(function(){return a})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return s})),n.d(t,"s",(function(){return l})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"u",(function(){return m})),n.d(t,"v",(function(){return p})),n.d(t,"q",(function(){return b})),n.d(t,"p",(function(){return v})),n.d(t,"b",(function(){return h})),n.d(t,"e",(function(){return _})),n.d(t,"a",(function(){return O})),n.d(t,"d",(function(){return E})),n.d(t,"c",(function(){return k})),n.d(t,"f",(function(){return y})),n.d(t,"g",(function(){return j}));var r="mainDashboard",i="entityDashboard",a="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",s="activation",l="splash",u="adminBar",d="adminBarViewOnly",g="settings",f="adBlockingRecovery",m="wpDashboard",p="wpDashboardViewOnly",b="moduleSetup",v="metricSelection",h="key-metrics",_="traffic",O="content",E="speed",k="monetization",y=[r,i,a,o,c,l,g,b,v],j=[a,o,d,p]},220:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Item}));var r=n(1),i=n.n(r);function Item(t){var n=t.icon,r=t.label;return e.createElement("div",{className:"googlesitekit-user-menu__item"},e.createElement("div",{className:"googlesitekit-user-menu__item-icon"},n),e.createElement("span",{className:"googlesitekit-user-menu__item-label"},r))}Item.propTypes={icon:i.a.node,label:i.a.string}}).call(this,n(4))},222:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var r=n(1),i=n.n(r),a=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var r="win-warning"===n?e.createElement(a.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},r))}BannerIcon.propTypes={type:i.a.string}}).call(this,n(4))},224:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Root}));var r=n(15),i=n.n(r),a=n(1),o=n.n(a),c=n(432),s=n(534),l=n(0),u=n(3),d=n.n(u),g=n(225),f=n(229),m=n(57),p=n(230),b=n(232),v=n(233),h=n(61),_=n(160),O=n(173);function Root(t){var n=t.children,r=t.registry,a=t.viewContext,o=void 0===a?null:a,d=c.a,E=Object(l.useState)({key:"Root",value:!0}),k=i()(E,1)[0];return e.createElement(l.StrictMode,null,e.createElement(_.a,{value:k},e.createElement(u.RegistryProvider,{value:r},e.createElement(f.a,{value:m.a},e.createElement(h.a,{value:o},e.createElement(s.a,{theme:d()},e.createElement(g.a,null,e.createElement(b.a,null,n,o&&e.createElement(v.a,null)),Object(O.a)(o)&&e.createElement(p.a,null))))))))}Root.propTypes={children:o.a.node,registry:o.a.object,viewContext:o.a.string.isRequired},Root.defaultProps={registry:d.a}}).call(this,n(4))},225:function(e,t,n){"use strict";(function(e,r){var i=n(51),a=n.n(i),o=n(53),c=n.n(o),s=n(68),l=n.n(s),u=n(69),d=n.n(u),g=n(49),f=n.n(g),m=n(1),p=n.n(m),b=n(0),v=n(2),h=n(172),_=n(61),O=n(197),E=n(9);function k(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var i=f()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d()(this,n)}}var y=function(t){l()(ErrorHandler,t);var n=k(ErrorHandler);function ErrorHandler(e){var t;return a()(this,ErrorHandler),(t=n.call(this,e)).state={error:null,info:null,copied:!1},t}return c()(ErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t,info:n}),Object(E.I)("react_error","handle_".concat(this.context||"unknown","_error"),"".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,i=t.info;return n?r.createElement(O.a,{id:"googlesitekit-error",className:"googlesitekit-error-handler",title:Object(v.__)("Site Kit encountered an error","google-site-kit"),description:r.createElement(h.a,{message:n.message,componentStack:i.componentStack}),isDismissible:!1,format:"small",type:"win-error"},r.createElement("pre",{className:"googlesitekit-overflow-auto"},n.message,i.componentStack)):e}}]),ErrorHandler}(b.Component);y.contextType=_.b,y.propTypes={children:p.a.node.isRequired},t.a=y}).call(this,n(28),n(4))},226:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var r=n(1),i=n.n(r),a=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var r="win-warning"===n?e.createElement(a.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},r))}BannerIcon.propTypes={type:i.a.string}}).call(this,n(4))},227:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerLogo}));var r=n(1),i=n.n(r),a=n(17),o=n(155),c=n(198);function BannerLogo(t){var n=t.module,r=t.moduleName;return e.createElement(a.a,{size:12},e.createElement("div",{className:"googlesitekit-publisher-win__logo"},n&&e.createElement(c.a,{slug:n,size:19}),!n&&e.createElement(o.a,{height:"34",width:"32"})),r&&e.createElement("div",{className:"googlesitekit-publisher-win__module-name"},r))}BannerLogo.propTypes={module:i.a.string,moduleName:i.a.string}}).call(this,n(4))},228:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerDescription}));var r=n(1),i=n.n(r),a=n(0),o=n(75),c=n(20),s=n(86);function BannerDescription(t){var n=t.description,r=t.learnMoreLabel,i=t.learnMoreURL,l=t.learnMoreTarget,u=t.learnMoreDescription,d=t.onLearnMoreClick;if(!n)return null;var g;return r&&(g=e.createElement(a.Fragment,null,e.createElement(c.a,{onClick:function(e){e.persist(),null==d||d()},href:i,external:l===s.a.EXTERNAL},r),u)),e.createElement("div",{className:"googlesitekit-publisher-win__desc"},Object(a.isValidElement)(n)?e.createElement(a.Fragment,null,n,g&&e.createElement("p",null,g)):e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.a)(n,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",g))}BannerDescription.propTypes={description:i.a.node,learnMoreURL:i.a.string,learnMoreDescription:i.a.string,learnMoreLabel:i.a.string,learnMoreTarget:i.a.oneOf(Object.values(s.a)),onLearnMoreClick:i.a.func}}).call(this,n(4))},229:function(e,t,n){"use strict";var r=n(158),i=(r.a.Consumer,r.a.Provider);t.a=i},23:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r="core/ui",i="activeContextID"},230:function(e,t,n){"use strict";(function(e){var r=n(3),i=n(231),a=n(7);t.a=function PermissionsModal(){return Object(r.useSelect)((function(e){return e(a.a).isAuthenticated()}))?e.createElement(i.a,null):null}}).call(this,n(4))},231:function(e,t,n){"use strict";(function(e,r){var i=n(5),a=n.n(i),o=n(16),c=n.n(o),s=n(2),l=n(0),u=n(3),d=n(109),g=n(29),f=n(32),m=n(7),p=n(129),b=n(72);t.a=function AuthenticatedPermissionsModal(){var t,n,i,o,v=Object(u.useRegistry)(),h=Object(u.useSelect)((function(e){return e(m.a).getPermissionScopeError()})),_=Object(u.useSelect)((function(e){return e(m.a).getUnsatisfiedScopes()})),O=Object(u.useSelect)((function(t){var n,r,i;return t(m.a).getConnectURL({additionalScopes:null==h||null===(n=h.data)||void 0===n?void 0:n.scopes,redirectURL:(null==h||null===(r=h.data)||void 0===r?void 0:r.redirectURL)||e.location.href,errorRedirectURL:null==h||null===(i=h.data)||void 0===i?void 0:i.errorRedirectURL})})),E=Object(u.useDispatch)(m.a).clearPermissionScopeError,k=Object(u.useDispatch)(f.a).navigateTo,y=Object(u.useDispatch)(g.a).setValues,j=Object(l.useCallback)((function(){E()}),[E]),S=Object(l.useCallback)(c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return y(m.d,{permissionsError:h}),e.next=3,Object(p.c)(v);case 3:k(O);case 4:case"end":return e.stop()}}),e)}))),[v,O,k,h,y]);return Object(l.useEffect)((function(){(function(){var e=c()(a.a.mark((function e(){var t,n,r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(null==h||null===(t=h.data)||void 0===t?void 0:t.skipModal)||!(null==h||null===(n=h.data)||void 0===n||null===(r=n.scopes)||void 0===r?void 0:r.length)){e.next=3;break}return e.next=3,S();case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[S,h]),h?(null==h||null===(t=h.data)||void 0===t||null===(n=t.scopes)||void 0===n?void 0:n.length)?(null==h||null===(i=h.data)||void 0===i?void 0:i.skipModal)||_&&(null==h||null===(o=h.data)||void 0===o?void 0:o.scopes.every((function(e){return _.includes(e)})))?null:r.createElement(b.a,null,r.createElement(d.a,{title:Object(s.__)("Additional Permissions Required","google-site-kit"),subtitle:h.message,confirmButton:Object(s.__)("Proceed","google-site-kit"),dialogActive:!0,handleConfirm:S,handleDialog:j,medium:!0})):(e.console.warn("permissionsError lacks scopes array to use for redirect, so not showing the PermissionsModal. permissionsError was:",h),null):null}}).call(this,n(28),n(4))},232:function(e,t,n){"use strict";var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(15),s=n.n(c),l=n(0),u=n(3),d=n(129);t.a=function RestoreSnapshots(e){var t=e.children,n=Object(u.useRegistry)(),r=Object(l.useState)(!1),a=s()(r,2),c=a[0],g=a[1];return Object(l.useEffect)((function(){c||o()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(d.b)(n);case 2:g(!0);case 3:case"end":return e.stop()}}),e)})))()}),[n,c]),c?t:null}},233:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return FeatureTours}));var i=n(81),a=n(0),o=n(3),c=n(7),s=n(18),l=n(90);function FeatureTours(){var t=Object(s.a)(),n=Object(o.useDispatch)(c.a).triggerTourForView;Object(i.a)((function(){n(t)}));var u=Object(o.useSelect)((function(e){return e(c.a).getCurrentTour()}));return Object(a.useEffect)((function(){if(u){var t=document.getElementById("js-googlesitekit-main-dashboard");if(t){var n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}}),[u]),u?r.createElement(l.a,{tourID:u.slug,steps:u.steps,gaEventCategory:u.gaEventCategory,callback:u.callback}):null}}).call(this,n(28),n(4))},234:function(e,t,n){"use strict";(function(e){var r=n(15),i=n.n(r),a=n(1),o=n.n(a),c=n(11),s=n.n(c),l=n(590),u=n(2),d=n(0),g=n(3),f=n(256),m=n(276),p=n(280),b=n(7),v=n(17),h=n(284),_=n(291),O=n(293),E=n(34),k=n(52),y=n(20),j=n(299),S=n(13),N=n(300);function Header(t){var n,r=t.children,a=t.subHeader,o=t.showNavigation,c=!!Object(k.c)(),w=Object(E.a)();Object(N.a)();var T=Object(g.useSelect)((function(e){return e(S.c).getAdminURL("googlesitekit-dashboard")})),C=Object(g.useSelect)((function(e){return e(b.a).isAuthenticated()})),A=Object(l.a)({childList:!0}),x=i()(A,2),I=x[0],R=!!(null===(n=x[1].target)||void 0===n?void 0:n.childElementCount);return e.createElement(d.Fragment,null,e.createElement("header",{className:s()("googlesitekit-header",{"googlesitekit-header--has-subheader":R,"googlesitekit-header--has-navigation":o})},e.createElement(v.e,null,e.createElement(v.k,null,e.createElement(v.a,{smSize:1,mdSize:2,lgSize:4,className:"googlesitekit-header__logo",alignMiddle:!0},e.createElement(y.a,{"aria-label":Object(u.__)("Go to dashboard","google-site-kit"),className:"googlesitekit-header__logo-link",href:T},e.createElement(f.a,null))),e.createElement(v.a,{smSize:3,mdSize:6,lgSize:8,className:"googlesitekit-header__children",alignMiddle:!0},r,!C&&c&&w&&e.createElement(O.a,null),C&&!w&&e.createElement(m.a,null))))),e.createElement("div",{className:"googlesitekit-subheader",ref:I},e.createElement(p.a,null),a),o&&e.createElement(h.a,null),c&&e.createElement(j.a,null),e.createElement(_.a,null))}Header.displayName="Header",Header.propTypes={children:o.a.node,subHeader:o.a.element,showNavigation:o.a.bool},Header.defaultProps={children:null,subHeader:null},t.a=Header}).call(this,n(4))},235:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return HelpMenu}));var r=n(15),i=n.n(r),a=n(1),o=n.n(a),c=n(209),s=n(0),l=n(56),u=n(2),d=n(3),g=n(10),f=n(301),m=n(112),p=n(9),b=n(164),v=n(19),h=n(18),_=n(13);function HelpMenu(t){var n=t.children,r=Object(s.useState)(!1),a=i()(r,2),o=a[0],O=a[1],E=Object(s.useRef)(),k=Object(h.a)();Object(c.a)(E,(function(){return O(!1)})),Object(m.a)([l.c,l.f],E,(function(){return O(!1)}));var y=Object(d.useSelect)((function(e){return e(v.a).isModuleActive("adsense")})),j=Object(s.useCallback)((function(){o||Object(p.I)("".concat(k,"_headerbar"),"open_helpmenu"),O(!o)}),[o,k]),S=Object(s.useCallback)((function(){O(!1)}),[]),N=Object(d.useSelect)((function(e){return e(_.c).getDocumentationLinkURL("fix-common-issues")}));return e.createElement("div",{ref:E,className:"googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},e.createElement(g.Button,{"aria-controls":"googlesitekit-help-menu","aria-expanded":o,"aria-label":Object(u.__)("Help","google-site-kit"),"aria-haspopup":"menu",className:"googlesitekit-header__dropdown googlesitekit-border-radius-round googlesitekit-button-icon googlesitekit-help-menu__button mdc-button--dropdown",icon:e.createElement(f.a,{width:"20",height:"20"}),onClick:j,text:!0,tooltipEnterDelayInMS:500}),e.createElement(g.Menu,{className:"googlesitekit-width-auto",menuOpen:o,id:"googlesitekit-help-menu",onSelected:S},n,e.createElement(b.a,{gaEventLabel:"fix_common_issues",href:N},Object(u.__)("Fix common issues","google-site-kit")),e.createElement(b.a,{gaEventLabel:"documentation",href:"https://sitekit.withgoogle.com/documentation/"},Object(u.__)("Read help docs","google-site-kit")),e.createElement(b.a,{gaEventLabel:"support_forum",href:"https://wordpress.org/support/plugin/google-site-kit/"},Object(u.__)("Get support","google-site-kit")),y&&e.createElement(b.a,{gaEventLabel:"adsense_help",href:"https://support.google.com/adsense/"},Object(u.__)("Get help with AdSense","google-site-kit"))))}HelpMenu.propTypes={children:o.a.node}}).call(this,n(4))},24:function(e,t,n){"use strict";n.d(t,"d",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return s}));var r=n(79),i="xlarge",a="desktop",o="tablet",c="small";function s(){var e=Object(r.a)();return e>1280?i:e>960?a:e>600?o:c}},249:function(e,t,n){"use strict";(function(e){var r=n(15),i=n.n(r),a=n(0);t.a=function(t,n){var r=Object(a.useState)(null),o=i()(r,2),c=o[0],s=o[1];return Object(a.useEffect)((function(){if(t.current&&"function"==typeof e.IntersectionObserver){var r=new e.IntersectionObserver((function(e){s(e[e.length-1])}),n);return r.observe(t.current),function(){s(null),r.disconnect()}}return function(){}}),[t.current,n.threshold,n.root,n.rootMargin]),c}}).call(this,n(28))},256:function(e,t,n){"use strict";(function(e){var r=n(2),i=n(155),a=n(257),o=n(105);t.a=function Logo(){return e.createElement("div",{className:"googlesitekit-logo","aria-hidden":"true"},e.createElement(i.a,{className:"googlesitekit-logo__logo-g",height:"34",width:"32"}),e.createElement(a.a,{className:"googlesitekit-logo__logo-sitekit",height:"26",width:"99"}),e.createElement(o.a,null,Object(r.__)("Site Kit by Google Logo","google-site-kit")))}}).call(this,n(4))},257:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M62.09 1.664h3.038v.1L58.34 9.593l7.241 10.224v.1H62.7L56.755 11.4 53.95 14.64v5.278h-2.351V1.664h2.35v9.415h.1l8.04-9.415zM69.984 3.117c0 .454-.166.853-.487 1.175-.322.322-.71.488-1.176.488-.455 0-.854-.166-1.175-.488a1.599 1.599 0 01-.488-1.175c0-.466.166-.854.488-1.176.321-.322.71-.488 1.175-.488.455 0 .854.166 1.176.488.332.333.487.72.487 1.176zm-.476 4.313v12.498h-2.351V7.43h2.35zM77.016 20.128c-1.02 0-1.864-.31-2.54-.943-.676-.632-1.02-1.508-1.031-2.628V9.57h-2.196V7.43h2.196V3.603h2.35V7.43h3.061v2.14h-3.06v6.222c0 .831.166 1.397.488 1.696.321.3.687.444 1.097.444.189 0 .366-.022.555-.067.188-.044.344-.1.499-.166l.743 2.096c-.632.222-1.342.333-2.162.333zM2.673 18.952C1.375 18.009.488 16.678 0 14.97l2.883-1.176c.289 1.076.799 1.94 1.542 2.628.732.677 1.619 1.02 2.65 1.02.965 0 1.774-.244 2.45-.742.677-.5 1.01-1.187 1.01-2.052 0-.798-.3-1.453-.887-1.974-.588-.521-1.62-1.042-3.094-1.564l-1.22-.432C4.025 10.224 2.928 9.57 2.04 8.716 1.153 7.862.71 6.742.71 5.346c0-.966.266-1.853.787-2.673C2.018 1.852 2.75 1.209 3.693.72 4.624.244 5.678 0 6.864 0c1.708 0 3.072.41 4.081 1.242 1.02.832 1.697 1.752 2.04 2.795L10.236 5.2c-.2-.621-.576-1.164-1.142-1.63-.565-.477-1.286-.71-2.173-.71s-1.641.222-2.251.676c-.61.455-.91 1.032-.91 1.742 0 .676.278 1.22.82 1.663.544.432 1.398.854 2.563 1.253l1.22.41c1.674.577 2.96 1.342 3.88 2.274.921.931 1.376 2.184 1.376 3.748 0 1.275-.322 2.34-.976 3.193a6.01 6.01 0 01-2.495 1.919 8.014 8.014 0 01-3.116.621c-1.62 0-3.072-.466-4.358-1.408zM15.969 3.449a1.95 1.95 0 01-.588-1.43c0-.566.2-1.043.588-1.431A1.95 1.95 0 0117.399 0c.566 0 1.043.2 1.43.588.389.388.588.865.588 1.43 0 .566-.2 1.043-.587 1.43a1.95 1.95 0 01-1.43.589c-.566-.012-1.043-.2-1.431-.588zm-.067 2.595h2.994v13.883h-2.994V6.044zM25.405 19.85c-.543-.2-.986-.466-1.33-.788-.776-.776-1.176-1.84-1.176-3.182V8.683h-2.428v-2.64h2.428V2.13h2.994v3.926h3.372v2.639h-3.372v6.531c0 .743.145 1.276.433 1.575.277.366.743.543 1.42.543.31 0 .576-.044.82-.122.233-.077.488-.21.765-.399v2.917c-.599.277-1.32.41-2.173.41a5.01 5.01 0 01-1.753-.3zM33.623 19.407a6.63 6.63 0 01-2.529-2.628c-.61-1.12-.909-2.373-.909-3.77 0-1.332.3-2.551.887-3.693.588-1.132 1.409-2.04 2.462-2.706 1.053-.666 2.251-1.01 3.593-1.01 1.397 0 2.606.311 3.637.921a6.123 6.123 0 012.34 2.528c.532 1.076.799 2.274.799 3.627 0 .255-.023.576-.078.953H33.179c.111 1.287.566 2.285 1.375 2.983a4.162 4.162 0 002.817 1.043c.854 0 1.597-.189 2.218-.588a4.266 4.266 0 001.508-1.597l2.528 1.198c-.654 1.142-1.508 2.04-2.561 2.694-1.054.655-2.318.976-3.782.976-1.364.022-2.584-.288-3.66-.931zm7.23-8.051a3.332 3.332 0 00-.466-1.453c-.277-.477-.687-.887-1.242-1.208-.554-.322-1.23-.488-2.03-.488-.964 0-1.773.288-2.439.853-.665.566-1.12 1.342-1.375 2.296h7.552z",fill:"#5F6368"});t.a=function SvgLogoSitekit(e){return r.createElement("svg",i({viewBox:"0 0 80 21",fill:"none"},e),a)}},260:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return f})),n.d(t,"b",(function(){return SpinnerButton}));var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(344),g=n(261),f={BEFORE:"before",AFTER:"after"};function SpinnerButton(t){var n=t.className,r=t.onClick,a=void 0===r?function(){}:r,c=t.isSaving,s=void 0!==c&&c,l=t.spinnerPosition,m=void 0===l?f.AFTER:l,p=o()(t,["className","onClick","isSaving","spinnerPosition"]);return e.createElement(d.a,i()({className:u()(n,"googlesitekit-button-icon--spinner",{"googlesitekit-button-icon--spinner__running":s,"googlesitekit-button-icon--spinner__before":m===f.BEFORE,"googlesitekit-button-icon--spinner__after":m===f.AFTER}),icon:s&&m===f.BEFORE?e.createElement(g.a,{size:14}):void 0,trailingIcon:s&&m===f.AFTER?e.createElement(g.a,{size:14}):void 0,onClick:a},p))}SpinnerButton.propTypes={className:s.a.string,onClick:s.a.func,isSaving:s.a.bool,spinnerPosition:s.a.oneOf(Object.values(f))}}).call(this,n(4))},261:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CircularProgress}));var r=n(640);function CircularProgress(t){return e.createElement(r.a,t)}}).call(this,n(4))},276:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return UserMenu}));var i=n(5),a=n.n(i),o=n(16),c=n.n(o),s=n(15),l=n.n(s),u=n(209),d=n(0),g=n(2),f=n(56),m=n(3),p=n(10),b=n(109),v=n(9),h=n(37),_=n(72),O=n(277),E=n(220),k=n(278),y=n(279),j=n(29),S=n(13),N=n(7),w=n(32),T=n(8),C=n(112),A=n(18);function UserMenu(){var t=Object(m.useSelect)((function(e){return e(S.c).getProxyPermissionsURL()})),n=Object(m.useSelect)((function(e){return e(N.a).getEmail()})),i=Object(m.useSelect)((function(e){return e(N.a).getPicture()})),o=Object(m.useSelect)((function(e){return e(N.a).getFullName()})),s=Object(m.useSelect)((function(e){return e(S.c).getAdminURL("googlesitekit-splash",{googlesitekit_context:"revoked"})})),x=Object(m.useSelect)((function(e){return e(j.a).getValue(T.d,"isAutoCreatingCustomDimensionsForAudience")})),I=Object(d.useState)(!1),R=l()(I,2),D=R[0],L=R[1],P=Object(d.useState)(!1),M=l()(P,2),z=M[0],B=M[1],H=Object(d.useRef)(),U=Object(d.useRef)(),F=Object(A.a)(),V=Object(m.useDispatch)(w.a).navigateTo;Object(u.a)(H,(function(){return B(!1)})),Object(C.a)([f.c,f.f],H,(function(){var e;B(!1),null===(e=U.current)||void 0===e||e.focus()})),Object(d.useEffect)((function(){var t=function(e){f.c===e.keyCode&&(L(!1),B(!1))};return e.addEventListener("keyup",t),function(){e.removeEventListener("keyup",t)}}),[]);var W,q=Object(d.useCallback)((function(){z||Object(v.I)("".concat(F,"_headerbar"),"open_usermenu"),B(!z)}),[z,F]),G=Object(d.useCallback)((function(){L(!D),B(!1)}),[D]),K=Object(d.useCallback)(function(){var e=c()(a.a.mark((function e(n,r){var i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:i=r.detail.item,e.t0=null==i?void 0:i.id,e.next="manage-sites"===e.t0?4:"disconnect"===e.t0?9:11;break;case 4:if(!t){e.next=8;break}return e.next=7,Object(v.I)("".concat(F,"_headerbar_usermenu"),"manage_sites");case 7:V(t);case 8:return e.abrupt("break",12);case 9:return G(),e.abrupt("break",12);case 11:q();case 12:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),[t,q,G,V,F]),X=Object(d.useCallback)(c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return L(!1),e.next=3,Object(h.b)();case 3:return e.next=5,Object(v.I)("".concat(F,"_headerbar_usermenu"),"disconnect_user");case 5:V(s);case 6:case"end":return e.stop()}}),e)}))),[s,V,F]);return n?(o&&n&&(W=Object(g.sprintf)(
/* translators: Account info text. 1: User's (full) name 2: User's email address. */
Object(g.__)("Google Account for %1$s (Email: %2$s)","google-site-kit"),o,n)),o&&!n&&(W=Object(g.sprintf)(
/* translators: Account info text. 1: User's (full) name. */
Object(g.__)("Google Account for %1$s","google-site-kit"),o)),!o&&n&&(W=Object(g.sprintf)(
/* translators: Account info text. 1: User's email address. */
Object(g.__)("Google Account (Email: %1$s)","google-site-kit"),n)),r.createElement(d.Fragment,null,r.createElement("div",{ref:H,className:"googlesitekit-user-selector googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},r.createElement(p.Button,{disabled:x,ref:U,className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--tablet googlesitekit-border-radius-round--phone googlesitekit-border-radius-round googlesitekit-button-icon",text:!0,onClick:q,icon:!!i&&r.createElement("i",{className:"mdc-button__icon mdc-button__account","aria-hidden":"true"},r.createElement("img",{className:"mdc-button__icon--image",src:i,alt:Object(g.__)("User Avatar","google-site-kit")})),"aria-haspopup":"menu","aria-expanded":z,"aria-controls":"user-menu","aria-label":x?void 0:Object(g.__)("Account","google-site-kit"),tooltip:!0,tooltipEnterDelayInMS:500,customizedTooltip:x?null:r.createElement("span",{"aria-label":W},r.createElement("strong",null,Object(g.__)("Google Account","google-site-kit")),r.createElement("br",null),r.createElement("br",null),o,o&&r.createElement("br",null),n)}),r.createElement(p.Menu,{className:"googlesitekit-user-menu",menuOpen:z,onSelected:K,id:"user-menu"},r.createElement("li",null,r.createElement(O.a,null)),!!t&&r.createElement("li",{id:"manage-sites",className:"mdc-list-item",role:"menuitem"},r.createElement(E.a,{icon:r.createElement(y.a,{width:"22"}),label:Object(g.__)("Manage Sites","google-site-kit")})),r.createElement("li",{id:"disconnect",className:"mdc-list-item",role:"menuitem"},r.createElement(E.a,{icon:r.createElement(k.a,{width:"22"}),label:Object(g.__)("Disconnect","google-site-kit")})))),r.createElement(_.a,null,r.createElement(b.a,{dialogActive:D,handleConfirm:X,handleDialog:G,title:Object(g.__)("Disconnect","google-site-kit"),subtitle:Object(g.__)("Disconnecting Site Kit by Google will remove your access to all services. After disconnecting, you will need to re-authorize to restore service.","google-site-kit"),confirmButton:Object(g.__)("Disconnect","google-site-kit"),danger:!0,small:!0})))):null}}).call(this,n(28),n(4))},277:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Details}));var r=n(2),i=n(3),a=n(7);function Details(){var t=Object(i.useSelect)((function(e){return e(a.a).getPicture()})),n=Object(i.useSelect)((function(e){return e(a.a).getFullName()})),o=Object(i.useSelect)((function(e){return e(a.a).getEmail()}));return e.createElement("div",{className:"googlesitekit-user-menu__details","aria-label":Object(r.__)("Google account","google-site-kit")},!!t&&e.createElement("img",{className:"googlesitekit-user-menu__details-avatar",src:t,alt:""}),e.createElement("div",{className:"googlesitekit-user-menu__details-info"},e.createElement("p",{className:"googlesitekit-user-menu__details-info__name"},n),e.createElement("p",{className:"googlesitekit-user-menu__details-info__email","aria-label":Object(r.__)("Email","google-site-kit")},o)))}}).call(this,n(4))},278:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M6.83 2H20a2 2 0 012 2v12c0 .34-.09.66-.23.94L20 15.17V6h-9.17l-4-4zm13.66 19.31L17.17 18H4a2 2 0 01-2-2V4c0-.34.08-.66.23-.94L.69 1.51 2.1.1l19.8 19.8-1.41 1.41zM15.17 16l-10-10H4v10h11.17z",fill:"currentColor"});t.a=function SvgDisconnect(e){return r.createElement("svg",i({viewBox:"0 0 22 22",fill:"none"},e),a)}},279:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M20 0H2C.9 0 0 .9 0 2v12c0 1.1.9 2 2 2h5v2h8v-2h5c1.1 0 2-.9 2-2V2c0-1.1-.9-2-2-2zm0 14H2V2h18v12zm-2-9H7v2h11V5zm0 4H7v2h11V9zM6 5H4v2h2V5zm0 4H4v2h2V9z",fill:"currentColor"});t.a=function SvgManageSites(e){return r.createElement("svg",i({viewBox:"0 0 22 18",fill:"none"},e),a)}},280:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotifications}));var r=n(0),i=n(281),a=n(167),o=n(41);function ErrorNotifications(){return e.createElement(r.Fragment,null,e.createElement(i.a,null),e.createElement(a.a,{areaSlug:o.b.ERRORS}))}}).call(this,n(4))},281:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InternalServerError}));var r=n(3),i=n(13),a=n(196),o=n(191),c=n(111);function InternalServerError(){var t=Object(r.useSelect)((function(e){return e(i.c).getInternalServerError()}));return t?e.createElement(o.a,{className:"googlesitekit-publisher-win googlesitekit-publisher-win--win-error"},e.createElement(a.a,{title:t.title,description:e.createElement(c.a,{text:t.description})})):null}}).call(this,n(4))},282:function(e,t,n){"use strict";n.d(t,"a",(function(){return ViewedStateObserver}));var r=n(1),i=n.n(r),a=n(0),o=n(3),c=n(23),s=n(249),l=n(161);function ViewedStateObserver(e){var t=e.id,n=e.observeRef,r=e.threshold,i=Object(s.a)(n,{threshold:r}),u=Object(o.useDispatch)(c.b).setValue,d=!!(null==i?void 0:i.isIntersecting),g=Object(l.a)(t);return Object(a.useEffect)((function(){!g&&d&&u(l.a.getKey(t),!0)}),[g,d,u,t]),null}ViewedStateObserver.propTypes={id:i.a.string,observeRef:i.a.object,threshold:i.a.number}},283:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return s}));var r=n(21),i=n.n(r),a=n(63),o=n.n(a),c=n(191),s=o()((function(e){return{id:e,Notification:l(e)(c.a)}}));function l(t){return function(n){function WithNotificationID(r){return e.createElement(n,i()({},r,{id:t}))}return WithNotificationID.displayName="WithNotificationID",(n.displayName||n.name)&&(WithNotificationID.displayName+="(".concat(n.displayName||n.name,")")),WithNotificationID}}}).call(this,n(4))},284:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardNavigation}));var r=n(3),i=n(7),a=n(34),o=n(183),c=n(285);function DashboardNavigation(){var t=Object(a.a)(),n=Object(r.useSelect)((function(e){return t?e(i.a).getViewableModules():null})),s=Object(r.useSelect)((function(e){return e(i.a).getKeyMetrics()}));return e.createElement(o.a,{loading:void 0===n||void 0===s,width:"100%",smallHeight:"59px",height:"71px"},e.createElement(c.a,null))}}).call(this,n(4))},285:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return Navigation}));var i=n(27),a=n.n(i),o=n(15),c=n.n(o),s=n(11),l=n.n(s),u=n(14),d=n(81),g=n(153),f=n(0),m=n(2),p=n(3),b=n(286),v=n(287),h=n(288),_=n(289),O=n(290),E=n(22),k=n(7),y=n(47),j=n(23),S=n(71),N=n(52),w=n(24),T=n(93),C=n(9),A=n(18),x=n(34);function I(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return R(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return R(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Navigation(){var t,n=Object(N.c)(),i=Object(f.useRef)(),o=Object(w.e)(),s=null===(t=e.location.hash)||void 0===t?void 0:t.substring(1),R=Object(f.useState)(s),D=c()(R,2),L=D[0],P=D[1],M=Object(f.useState)(s||void 0),z=c()(M,2),B=z[0],H=z[1],U=Object(f.useState)(!1),F=c()(U,2),V=F[0],W=F[1],q=Object(A.a)(),G=Object(x.a)(),K=Object(p.useDispatch)(j.b).setValue,X=Object(p.useSelect)((function(e){return G?e(k.a).getViewableModules():null})),Y=Object(p.useSelect)((function(e){return e(k.a).isKeyMetricsWidgetHidden()})),Q={modules:X||void 0},$=Object(p.useSelect)((function(e){return n===N.b&&!0!==Y&&e(y.a).isWidgetContextActive(S.CONTEXT_MAIN_DASHBOARD_KEY_METRICS,Q)})),J=Object(p.useSelect)((function(e){return e(y.a).isWidgetContextActive(n===N.b?S.CONTEXT_MAIN_DASHBOARD_TRAFFIC:S.CONTEXT_ENTITY_DASHBOARD_TRAFFIC,Q)})),Z=Object(p.useSelect)((function(e){return e(y.a).isWidgetContextActive(n===N.b?S.CONTEXT_MAIN_DASHBOARD_CONTENT:S.CONTEXT_ENTITY_DASHBOARD_CONTENT,Q)})),ee=Object(p.useSelect)((function(e){return e(y.a).isWidgetContextActive(n===N.b?S.CONTEXT_MAIN_DASHBOARD_SPEED:S.CONTEXT_ENTITY_DASHBOARD_SPEED,Q)})),te=Object(p.useSelect)((function(e){return e(y.a).isWidgetContextActive(n===N.b?S.CONTEXT_MAIN_DASHBOARD_MONETIZATION:S.CONTEXT_ENTITY_DASHBOARD_MONETIZATION,Q)})),ne=Object(f.useCallback)((function(){return $?E.b:G?J?E.e:Z?E.a:ee?E.d:te?E.c:"":E.e}),[$,J,Z,ee,te,G]),re=Object(f.useCallback)((function(t){var n,r=t.target.closest(".mdc-chip"),i=null==r||null===(n=r.dataset)||void 0===n?void 0:n.contextId;e.history.replaceState({},"","#".concat(i)),H(i),Object(C.I)("".concat(q,"_navigation"),"tab_select",i),e.scrollTo({top:i!==ne()?Object(T.a)("#".concat(i),o):0,behavior:"smooth"}),setTimeout((function(){K(j.a,i)}),50)}),[o,q,K,ne]);return Object(d.a)((function(){var t=ne();if(!s)return P(t),void setTimeout((function(){return e.history.replaceState({},"","#".concat(t))}));var n=s;(function(e){return!(!$||e!==E.b)||(!(!J||e!==E.e)||(!(!Z||e!==E.a)||(!(!ee||e!==E.d)||!(!te||e!==E.c))))})(n)||(n=t),K(j.a,n),P(n),setTimeout((function(){var r=n!==t?Object(T.a)("#".concat(n),o):0;e.scrollY!==r?e.scrollTo({top:r,behavior:"smooth"}):K(j.a,void 0)}),50)})),Object(f.useEffect)((function(){var t=function(e){K(j.a,void 0),P(e),H(void 0)},n=Object(u.throttle)((function(n){var r,o,c,s,l=e.scrollY,u=null===(r=document.querySelector(".googlesitekit-entity-header"))||void 0===r||null===(o=r.getBoundingClientRect())||void 0===o?void 0:o.bottom,d=null==i||null===(c=i.current)||void 0===c?void 0:c.getBoundingClientRect(),g=d.bottom,f=d.top,m=[].concat(a()($?[E.b]:[]),a()(J?[E.e]:[]),a()(Z?[E.a]:[]),a()(ee?[E.d]:[]),a()(te?[E.c]:[])),p=ne();if(0===l)W(!1);else{var b,v=null===(b=document.querySelector(".googlesitekit-header"))||void 0===b?void 0:b.getBoundingClientRect().bottom;W(f===v)}var h,_=I(m);try{for(_.s();!(h=_.n()).done;){var O=h.value,k=document.getElementById(O);if(k){var y=k.getBoundingClientRect().top-20-(u||g||0);y<0&&(void 0===s||s<y)&&(s=y,p=O)}}}catch(e){_.e(e)}finally{_.f()}if(B)B===p&&t(p);else{var j=e.location.hash;p!==(null==j?void 0:j.substring(1))&&(n&&Object(C.I)("".concat(q,"_navigation"),"tab_scroll",p),e.history.replaceState({},"","#".concat(p)),t(p))}}),150);return e.addEventListener("scroll",n),function(){e.removeEventListener("scroll",n)}}),[B,$,J,Z,ee,te,q,K,ne]),r.createElement("nav",{className:l()("mdc-chip-set","googlesitekit-navigation","googlesitekit-navigation--".concat(n),{"googlesitekit-navigation--is-sticky":V}),ref:i},$&&r.createElement(g.Chip,{id:E.b,label:Object(m.__)("Key metrics","google-site-kit"),leadingIcon:r.createElement(b.a,{width:"18",height:"16"}),onClick:re,selected:L===E.b,"data-context-id":E.b}),J&&r.createElement(g.Chip,{id:E.e,label:Object(m.__)("Traffic","google-site-kit"),leadingIcon:r.createElement(v.a,{width:"18",height:"16"}),onClick:re,selected:L===E.e,"data-context-id":E.e}),Z&&r.createElement(g.Chip,{id:E.a,label:Object(m.__)("Content","google-site-kit"),leadingIcon:r.createElement(h.a,{width:"18",height:"18"}),onClick:re,selected:L===E.a,"data-context-id":E.a}),ee&&r.createElement(g.Chip,{id:E.d,label:Object(m.__)("Speed","google-site-kit"),leadingIcon:r.createElement(_.a,{width:"20",height:"16"}),onClick:re,selected:L===E.d,"data-context-id":E.d}),te&&r.createElement(g.Chip,{id:E.c,label:Object(m.__)("Monetization","google-site-kit"),leadingIcon:r.createElement(O.a,{width:"18",height:"16"}),onClick:re,selected:L===E.c,"data-context-id":E.c}))}}).call(this,n(28),n(4))},286:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("rect",{x:.5,width:5,height:5,rx:1,fill:"currentColor"}),o=r.createElement("rect",{x:7.5,width:5,height:5,rx:1,fill:"currentColor"}),c=r.createElement("rect",{x:.5,y:7,width:5,height:5,rx:1,fill:"currentColor"}),s=r.createElement("rect",{x:7.5,y:7,width:5,height:5,rx:1,fill:"currentColor"});t.a=function SvgNavKeyMetricsIcon(e){return r.createElement("svg",i({viewBox:"0 0 13 12",fill:"none"},e),a,o,c,s)}},287:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 0h3.971v16H7V0zM0 8h4v8H0V8zm18-3h-4v11h4V5z",fill:"currentColor"});t.a=function SvgNavTrafficIcon(e){return r.createElement("svg",i({viewBox:"0 0 18 16",fill:"none"},e),a)}},288:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 16V2c0-1.1-1-2-2.222-2H2.222C1 0 0 .9 0 2v14c0 1.1 1 2 2.222 2h13.556C17 18 18 17.1 18 16zM9 7h5V5H9v2zm7-5H2v14h14V2zM4 4h4v4H4V4zm10 7H9v2h5v-2zM4 10h4v4H4v-4z",fill:"currentColor"});t.a=function SvgNavContentIcon(e){return r.createElement("svg",i({viewBox:"0 0 18 18",fill:"none"},e),a)}},289:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M18.378 4.543l-1.232 1.854a8.024 8.024 0 01-.22 7.598H3.043A8.024 8.024 0 014.154 4.49 8.011 8.011 0 0113.57 2.82l1.853-1.233A10.01 10.01 0 003.117 2.758a10.026 10.026 0 00-1.797 12.24A2.004 2.004 0 003.043 16h13.873a2.003 2.003 0 001.742-1.002 10.03 10.03 0 00-.27-10.465l-.01.01z",fill:"currentColor"}),o=r.createElement("path",{d:"M8.572 11.399a2.003 2.003 0 002.835 0l5.669-8.51-8.504 5.673a2.005 2.005 0 000 2.837z",fill:"currentColor"});t.a=function SvgNavSpeedIcon(e){return r.createElement("svg",i({viewBox:"0 0 20 16",fill:"none"},e),a,o)}},29:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/forms"},290:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M16.1 0v2h2.967l-5.946 5.17-4.6-4L0 10.59 1.621 12l6.9-6 4.6 4L20.7 3.42V6H23V0h-6.9z",fill:"currentColor"});t.a=function SvgNavMonetizationIcon(e){return r.createElement("svg",i({viewBox:"0 0 23 12",fill:"none"},e),a)}},291:function(e,t,n){"use strict";(function(e,r){var i=n(15),a=n.n(i),o=n(14),c=n(2),s=n(0),l=n(3),u=n(10),d=n(13),g=n(292),f=n(32),m=n(20),p=n(80),b=n(9),v=n(52),h=n(18);t.a=function EntityHeader(){var t=Object(h.a)(),n=Object(v.c)(),i=Object(l.useSelect)((function(e){return e(d.c).getCurrentEntityTitle()})),_=Object(l.useSelect)((function(e){return e(d.c).getCurrentEntityURL()})),O=Object(s.useRef)(),E=Object(s.useState)(_),k=a()(E,2),y=k[0],j=k[1];Object(s.useEffect)((function(){var t=function(){if(O.current){var t=O.current.clientWidth-40,n=e.getComputedStyle(O.current.lastChild,null).getPropertyValue("font-size"),r=2*t/parseFloat(n);j(Object(p.d)(_,r))}},n=Object(o.throttle)(t,100);return t(),e.addEventListener("resize",n),function(){e.removeEventListener("resize",n)}}),[_,O,j]);var S=Object(l.useDispatch)(f.a).navigateTo,N=Object(l.useSelect)((function(e){return e(d.c).getAdminURL("googlesitekit-dashboard")})),w=Object(s.useCallback)((function(){Object(b.I)("".concat(t,"_navigation"),"return_to_dashboard"),S(N)}),[N,S,t]);return v.a!==n||null===_||null===i?null:r.createElement("div",{className:"googlesitekit-entity-header"},r.createElement("div",{className:"googlesitekit-entity-header__back"},r.createElement(u.Button,{icon:r.createElement(g.a,{width:24,height:24}),"aria-label":Object(c.__)("Back to dashboard","google-site-kit"),onClick:w,text:!0,tertiary:!0},Object(c.__)("Back to dashboard","google-site-kit"))),r.createElement("div",{ref:O,className:"googlesitekit-entity-header__details"},r.createElement("p",null,i),r.createElement(m.a,{secondary:!0,href:_,"aria-label":_,external:!0},y)))}}).call(this,n(28),n(4))},292:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M21 11H6.83l3.58-3.59L9 6l-6 6 6 6 1.41-1.41L6.83 13H21z",fill:"currentColor"});t.a=function SvgKeyboardBackspace(e){return r.createElement("svg",i({viewBox:"0 0 24 24"},e),a,o)}},293:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ViewOnlyMenu}));var r=n(15),i=n.n(r),a=n(209),o=n(11),c=n.n(o),s=n(0),l=n(2),u=n(56),d=n(10),g=n(18),f=n(112),m=n(9),p=n(294),b=n(295),v=n(296),h=n(298),_=n(3),O=n(7);function ViewOnlyMenu(){var t=Object(s.useState)(!1),n=i()(t,2),r=n[0],o=n[1],E=Object(s.useRef)(),k=Object(g.a)();Object(a.a)(E,(function(){return o(!1)})),Object(f.a)([u.c,u.f],E,(function(){return o(!1)}));var y=Object(s.useCallback)((function(){r||Object(m.I)("".concat(k,"_headerbar"),"open_viewonly"),o(!r)}),[r,k]),j=Object(_.useSelect)((function(e){return e(O.a).hasCapability(O.H)}));return e.createElement("div",{ref:E,className:c()("googlesitekit-view-only-menu","googlesitekit-dropdown-menu","googlesitekit-dropdown-menu__icon-menu","mdc-menu-surface--anchor",{"googlesitekit-view-only-menu--user-can-authenticate":j})},e.createElement(d.Button,{className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--phone googlesitekit-button-icon",text:!0,onClick:y,icon:e.createElement("span",{className:"mdc-button__icon","aria-hidden":"true"},e.createElement(p.a,{className:"mdc-button__icon--image"})),"aria-haspopup":"menu","aria-expanded":r,"aria-controls":"view-only-menu","aria-label":Object(l.__)("View only","google-site-kit"),tooltip:!0,tooltipEnterDelayInMS:500},Object(l.__)("View only","google-site-kit")),e.createElement(d.Menu,{menuOpen:r,nonInteractive:!0,onSelected:y,id:"view-only-menu"},e.createElement(b.a,null),e.createElement(v.a,null),e.createElement("li",{className:"mdc-list-divider",role:"separator"}),e.createElement(h.a,null)))}}).call(this,n(4))},294:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M8 1.333c2.756 0 5.214 1.42 6.415 3.667-1.2 2.247-3.659 3.667-6.415 3.667-2.756 0-5.215-1.42-6.415-3.667C2.785 2.753 5.244 1.333 8 1.333zM8 0C4.364 0 1.258 2.073 0 5c1.258 2.927 4.364 5 8 5s6.742-2.073 8-5c-1.258-2.927-4.364-5-8-5zm0 3.333c1.004 0 1.818.747 1.818 1.667S9.004 6.667 8 6.667 6.182 5.92 6.182 5 6.996 3.333 8 3.333zM8 2C6.196 2 4.727 3.347 4.727 5S6.197 8 8 8c1.804 0 3.273-1.347 3.273-3S9.803 2 8 2z",fill:"currentColor"});t.a=function SvgView(e){return r.createElement("svg",i({viewBox:"0 0 16 10",fill:"none"},e),a)}},295:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(0),s=n(38),l=n(2),u=n(3),d=n(10),g=n(32),f=n(13),m=n(7),p=n(9),b=n(20),v=n(18),h=n(37);function Description(){var t=Object(v.a)(),n=Object(u.useSelect)((function(e){return e(m.a).hasCapability(m.H)})),r=Object(u.useSelect)((function(e){return e(f.c).getProxySetupURL()})),a=Object(u.useSelect)((function(e){return e(f.c).getDocumentationLinkURL("dashboard-sharing")})),_=Object(u.useDispatch)(g.a).navigateTo,O=Object(c.useCallback)(function(){var e=o()(i.a.mark((function e(n){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.preventDefault(),e.next=3,Promise.all([Object(h.f)("start_user_setup",!0),Object(p.I)("".concat(t,"_headerbar_viewonly"),"start_user_setup",r?"proxy":"custom-oauth")]);case 3:_(r);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[r,_,t]),E=Object(c.useCallback)((function(){Object(p.I)("".concat(t,"_headerbar_viewonly"),"click_learn_more_link")}),[t]),k=n?Object(s.a)(Object(l.__)("You can see stats from all shared Google services, but you can't make any changes. <strong>Sign in to connect more services and control sharing access.</strong>","google-site-kit"),{strong:e.createElement("strong",null)}):Object(s.a)(Object(l.__)("You can see stats from all shared Google services, but you can't make any changes. <a>Learn more</a>","google-site-kit"),{a:e.createElement(b.a,{href:a,external:!0,onClick:E,"aria-label":Object(l.__)("Learn more about dashboard sharing","google-site-kit")})});return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item googlesitekit-view-only-menu__description"},e.createElement("p",null,k),n&&e.createElement(d.Button,{onClick:O},Object(l._x)("Sign in with Google","Service name","google-site-kit")))}}).call(this,n(4))},296:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SharedServices}));var r=n(2),i=n(3),a=n(7),o=n(297);function SharedServices(){var t=Object(i.useSelect)((function(e){return e(a.a).getViewableModules()}));return void 0===t?null:e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("h4",null,Object(r.__)("Shared services","google-site-kit")),e.createElement("ul",null,t.map((function(t){return e.createElement(o.a,{key:t,module:t})}))))}}).call(this,n(4))},297:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Service}));var r=n(1),i=n.n(r),a=n(38),o=n(2),c=n(3),s=n(19),l=n(7);function Service(t){var n=t.module,r=Object(c.useSelect)((function(e){return e(l.a).hasCapability(l.H)})),i=Object(c.useSelect)((function(e){return e(s.a).getModule(n)||{}})),u=i.name,d=i.owner,g=Object(c.useSelect)((function(e){return e(s.a).getModuleIcon(n)}));return e.createElement("li",{className:"googlesitekit-view-only-menu__service"},e.createElement("span",{className:"googlesitekit-view-only-menu__service--icon"},e.createElement(g,{height:26})),e.createElement("span",{className:"googlesitekit-view-only-menu__service--name"},u),r&&(null==d?void 0:d.login)&&e.createElement("span",{className:"googlesitekit-view-only-menu__service--owner"},Object(a.a)(Object(o.sprintf)(
/* translators: %s: module owner Google Account email address */
Object(o.__)("Shared by <strong>%s</strong>","google-site-kit"),d.login),{strong:e.createElement("strong",{title:d.login})})))}Service.propTypes={module:i.a.string.isRequired}}).call(this,n(4))},298:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Tracking}));var r=n(38),i=n(2),a=n(218),o=n(18);function Tracking(){var t=Object(o.a)();return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("p",null,Object(r.a)(Object(i.__)("Thanks for using Site Kit!<br />Help us make it even better","google-site-kit"),{br:e.createElement("br",null)})),e.createElement(a.a,{trackEventCategory:"".concat(t,"_headerbar_viewonly"),alignCheckboxLeft:!0}))}}).call(this,n(4))},299:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SubtleNotifications}));var r=n(167),i=n(41);function SubtleNotifications(){return e.createElement(r.a,{areaSlug:i.b.BANNERS_BELOW_NAV})}}).call(this,n(4))},3:function(e,t){e.exports=googlesitekit.data},300:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(0),s=n(3),l=n(13),u=n(18),d=n(37),g=n(9),f=function(){var e=Object(u.a)(),t=Object(s.useSelect)((function(e){return e(l.c).isUsingProxy()})),n=Object(s.useSelect)((function(e){return e(l.c).getSetupErrorMessage()}));Object(c.useEffect)((function(){n||void 0===t||function(){var n=o()(i.a.mark((function n(){var r,a;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,Object(d.d)("start_user_setup");case 2:return r=n.sent,n.next=5,Object(d.d)("start_site_setup");case 5:if(a=n.sent,!r.cacheHit){n.next=10;break}return n.next=9,Object(d.c)("start_user_setup");case 9:Object(g.I)("".concat(e,"_setup"),"complete_user_setup",t?"proxy":"custom-oauth");case 10:if(!a.cacheHit){n.next=14;break}return n.next=13,Object(d.c)("start_site_setup");case 13:Object(g.I)("".concat(e,"_setup"),"complete_site_setup",t?"proxy":"custom-oauth");case 14:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}()()}),[e,t,n])}},301:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M9 16h2v-2H9v2zm1-16C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14C7.79 4 6 5.79 6 8h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z",fill:"currentColor"});t.a=function SvgHelp(e){return r.createElement("svg",i({viewBox:"0 0 20 20",fill:"none"},e),a)}},311:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"currentColor",fillRule:"evenodd"},r.createElement("path",{d:"M0 6.414L1.415 5l5.292 5.292-1.414 1.415z"}),r.createElement("path",{d:"M14.146.146l1.415 1.414L5.414 11.707 4 10.292z"}));t.a=function SvgConnected(e){return r.createElement("svg",i({viewBox:"0 0 16 12"},e),a)}},312:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"currentColor",fillRule:"evenodd"},r.createElement("path",{d:"M0 0h2v7H0zM0 10h2v2H0z"}));t.a=function SvgExclamation(e){return r.createElement("svg",i({viewBox:"0 0 2 12"},e),a)}},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},329:function(e,t,n){"use strict";(function(e){var r=n(51),i=n.n(r),a=n(53),o=n.n(a),c=n(68),s=n.n(c),l=n(69),u=n.n(l),d=n(49),g=n.n(d),f=n(1),m=n.n(f),p=n(0),b=n(17),v=n(20);function h(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=g()(e);if(t){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return u()(this,n)}}var _=function(t){s()(LayoutHeader,t);var n=h(LayoutHeader);function LayoutHeader(){return i()(this,LayoutHeader),n.apply(this,arguments)}return o()(LayoutHeader,[{key:"render",value:function(){var t=this.props,n=t.title,r=t.badge,i=t.ctaLabel,a=t.ctaLink,o=a?{alignMiddle:!0,smSize:4,lgSize:6}:{alignMiddle:!0,smSize:4,mdSize:8,lgSize:12};return e.createElement("header",{className:"googlesitekit-layout__header"},e.createElement(b.e,null,e.createElement(b.k,null,n&&e.createElement(b.a,o,e.createElement("h3",{className:"googlesitekit-subheading-1 googlesitekit-layout__header-title"},n,r)),a&&e.createElement(b.a,{alignMiddle:!0,mdAlignRight:!0,smSize:4,lgSize:6},e.createElement(v.a,{href:a,external:!0},i)))))}}]),LayoutHeader}(p.Component);_.propTypes={title:m.a.string,badge:m.a.node,ctaLabel:m.a.string,ctaLink:m.a.string},_.defaultProps={title:"",badge:null,ctaLabel:"",ctaLink:""},t.a=_}).call(this,n(4))},330:function(e,t,n){"use strict";(function(e){var r=n(51),i=n.n(r),a=n(53),o=n.n(a),c=n(68),s=n.n(c),l=n(69),u=n.n(l),d=n(49),g=n.n(d),f=n(1),m=n.n(f),p=n(0),b=n(17),v=n(134);function h(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=g()(e);if(t){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return u()(this,n)}}var _=function(t){s()(LayoutFooter,t);var n=h(LayoutFooter);function LayoutFooter(){return i()(this,LayoutFooter),n.apply(this,arguments)}return o()(LayoutFooter,[{key:"render",value:function(){var t=this.props,n=t.ctaLabel,r=t.ctaLink,i=t.footerContent;return e.createElement("footer",{className:"googlesitekit-layout__footer"},e.createElement(b.e,null,e.createElement(b.k,null,e.createElement(b.a,{size:12},r&&n&&e.createElement(v.a,{className:"googlesitekit-data-block__source",name:n,href:r,external:!0}),i))))}}]),LayoutFooter}(p.Component);_.propTypes={ctaLabel:m.a.string,ctaLink:m.a.string},t.a=_}).call(this,n(4))},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(22),i=n(18);function a(){var e=Object(i.a)();return r.g.includes(e)}},344:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(406),g=n(0),f=n(2),m=n(17),p=n(345),b=Object(g.forwardRef)((function(t,n){var r=t.children,a=t.href,c=t.text,l=t.className,u=t.danger,b=t.disabled,v=t.target,h=t.icon,_=t.trailingIcon,O=t["aria-label"],E=t.title,k=t.customizedTooltip,y=t.tooltip,j=t.inverse,S=t.hideTooltipTitle,N=void 0!==S&&S,w=t.tooltipEnterDelayInMS,T=void 0===w?100:w,C=t.tertiary,A=void 0!==C&&C,x=t.callout,I=t.calloutStyle,R=o()(t,["children","href","text","className","danger","disabled","target","icon","trailingIcon","aria-label","title","customizedTooltip","tooltip","inverse","hideTooltipTitle","tooltipEnterDelayInMS","tertiary","callout","calloutStyle"]),D=Object(g.useCallback)((function(e){null!==e&&m.i.attachTo(e)}),[]),L=Object(d.a)(n,D),P=a&&!b?"a":"button",M=e.createElement(P,i()({className:s()("mdc-button",l,{"mdc-button--raised":!c&&!A&&!x,"mdc-button--danger":u,"mdc-button--inverse":j,"mdc-button--tertiary":A,"mdc-button--callout":x,"mdc-button--callout-primary":x||"primary"===I,"mdc-button--callout-warning":"warning"===I,"mdc-button--callout-error":"error"===I}),href:b?void 0:a,ref:L,disabled:!!b,"aria-label":function(){var e=O;if("_blank"!==v)return e;var t=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit");return"string"==typeof r&&(e=e||r),e?"".concat(e," ").concat(t):t}(),target:v||"_self",role:"a"===P?"button":void 0},R),h,r&&e.createElement("span",{className:"mdc-button__label"},r),_),z=N?null:E||k||O;return!b&&(y&&z||h&&z&&void 0===r)?e.createElement(p.a,{title:z,enterDelay:T},M):M}));b.displayName="Button",b.propTypes={onClick:u.a.func,children:u.a.node,href:u.a.string,text:u.a.bool,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,icon:u.a.element,trailingIcon:u.a.element,title:u.a.string,customizedTooltip:u.a.element,tooltip:u.a.bool,inverse:u.a.bool,hideTooltipTitle:u.a.bool,callout:u.a.bool,calloutStyle:u.a.oneOf(["primary","warning","error"])},b.defaultProps={onClick:null,href:null,text:!1,className:"",danger:!1,disabled:!1,icon:null,trailingIcon:null,title:null,customizedTooltip:null,tooltip:!1,inverse:!1,calloutStyle:null,callout:null},t.a=b}).call(this,n(4))},345:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Tooltip}));var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(641),g=n(0);function Tooltip(t){var n=t.children,r=t.popperClassName,a=t.tooltipClassName,c=t.onOpen,s=t.onClose,l=o()(t,["children","popperClassName","tooltipClassName","onOpen","onClose"]),f=Object(g.useRef)(!1),m=c?function(){f.current||(f.current=!0,null==c||c())}:void 0,p=c?function(){f.current=!1,null==s||s()}:s;return e.createElement(d.a,i()({classes:{popper:u()("googlesitekit-tooltip-popper",r),tooltip:u()("googlesitekit-tooltip",a)},arrow:!0,onOpen:m,onClose:p},l),n)}Tooltip.propTypes={children:s.a.node,popperClassName:s.a.string,tooltipClassName:s.a.string,onOpen:s.a.func,onClose:s.a.func}}).call(this,n(4))},35:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return d}));n(14);var r=n(2),i="missing_required_scopes",a="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===i}function s(e){var t;return[a,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function l(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||s(e)||c(e)||l(e))}function d(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(r.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(r.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return O})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return _}));var r=n(99),i=e._googlesitekitTrackingData||{},a=i.activeModules,o=void 0===a?[]:a,c=i.isSiteKitScreen,s=i.trackingEnabled,l=i.trackingID,u=i.referenceSiteURL,d=i.userIDHash,g=i.isAuthenticated,f={activeModules:o,trackingEnabled:s,trackingID:l,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:i.userRoles,isAuthenticated:g,pluginVersion:"1.151.0"},m=Object(r.a)(f),p=m.enableTracking,b=m.disableTracking,v=(m.isTrackingEnabled,m.initializeSnippet),h=m.trackEvent,_=m.trackEventOnce;function O(e){e?p():b()}c&&s&&v()}).call(this,n(28))},365:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PageHeader}));var r=n(11),i=n.n(r),a=n(1),o=n.n(a),c=n(17),s=n(311),l=n(312),u=n(76);function PageHeader(t){var n=t.title,r=t.icon,a=t.className,o=t.status,d=t.statusText,g=t.fullWidth,f=t.children,m=g?{size:12}:{smSize:4,mdSize:4,lgSize:6},p=""!==o||Boolean(f);return e.createElement("header",{className:"googlesitekit-page-header"},e.createElement(c.k,null,n&&e.createElement(c.a,m,r,e.createElement("h1",{className:i()("googlesitekit-page-header__title",a)},n)),p&&e.createElement(c.a,{alignBottom:!0,mdAlignRight:!0,smSize:4,mdSize:4,lgSize:6},e.createElement("div",{className:"googlesitekit-page-header__details"},o&&e.createElement("span",{className:i()("googlesitekit-page-header__status","googlesitekit-page-header__status--".concat(o))},d,e.createElement(u.a,null,"connected"===o?e.createElement(s.a,{width:10,height:8}):e.createElement(l.a,{width:2,height:12}))),f))))}PageHeader.propTypes={title:o.a.string,icon:o.a.node,className:o.a.string,status:o.a.string,statusText:o.a.string,fullWidth:o.a.bool},PageHeader.defaultProps={title:"",icon:null,className:"googlesitekit-heading-3",status:"",statusText:"",fullWidth:!1}}).call(this,n(4))},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return _})),n.d(t,"c",(function(){return O})),n.d(t,"e",(function(){return E})),n.d(t,"b",(function(){return k}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u,d="googlesitekit_",g="".concat(d).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),f=["sessionStorage","localStorage"],m=[].concat(f),p=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,a="__storage_test__",r.setItem(a,a),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function b(){return v.apply(this,arguments)}function v(){return(v=o()(i.a.mark((function t(){var n,r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=s(m),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(a=r.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,p(a);case 11:if(!t.sent){t.next=13;break}u=e[a];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=o()(i.a.mark((function e(t){var n,r,a,o,c,s,l;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(g).concat(t)))){e.next=10;break}if(a=JSON.parse(r),o=a.timestamp,c=a.ttl,s=a.value,l=a.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:l});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),_=function(){var t=o()(i.a.mark((function t(n,r){var a,o,s,l,u,d,f,m,p=arguments;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=p.length>2&&void 0!==p[2]?p[2]:{},o=a.ttl,s=void 0===o?c.b:o,l=a.timestamp,u=void 0===l?Math.round(Date.now()/1e3):l,d=a.isError,f=void 0!==d&&d,t.next=3,b();case 3:if(!(m=t.sent)){t.next=14;break}return t.prev=5,m.setItem("".concat(g).concat(n),JSON.stringify({timestamp:u,ttl:s,value:r,isError:f})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),O=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,a=n.startsWith(d)?n:"".concat(g).concat(n),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),E=function(){var t=o()(i.a.mark((function t(){var n,r,a,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],a=0;a<n.length;a++)0===(o=n.key(a)).indexOf(d)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),k=function(){var e=o()(i.a.mark((function e(){var t,n,r,a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!e.sent){e.next=25;break}return e.next=6,E();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return a=r.value,e.next=14,O(a);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="_googlesitekitDataLayer",i="data-googlesitekit-gtag"},41:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(22),i="core/notifications",a={ERRORS:"notification-area-errors",BANNERS_ABOVE_NAV:"notification-area-banners-above-nav",BANNERS_BELOW_NAV:"notification-area-banners-below-nav"},o={DEFAULT:"default",SETUP_CTAS:"setup-ctas"},c=[r.s,r.n,r.l,r.o,r.m]},44:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(1),o=n.n(a),c=n(11),s=n.n(c),l=n(24);function PreviewBlock(t){var n,r,a=t.className,o=t.width,c=t.height,u=t.shape,d=t.padding,g=t.smallWidth,f=t.smallHeight,m=t.tabletWidth,p=t.tabletHeight,b=t.desktopWidth,v=t.desktopHeight,h=Object(l.e)(),_={width:(n={},i()(n,l.b,g),i()(n,l.c,m),i()(n,l.a,b),i()(n,l.d,b),n),height:(r={},i()(r,l.b,f),i()(r,l.c,p),i()(r,l.a,v),i()(r,l.d,b),r)};return e.createElement("div",{className:s()("googlesitekit-preview-block",a,{"googlesitekit-preview-block--padding":d}),style:{width:_.width[h]||o,height:_.height[h]||c}},e.createElement("div",{className:s()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(4))},47:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var r={BOXES:"boxes",COMPOSITE:"composite"},i={QUARTER:"quarter",HALF:"half",FULL:"full"},a="core/widgets"},52:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(22),i=n(18),a=r.n,o=r.l;function c(){var e=Object(i.a)();return e===r.n||e===r.o?a:e===r.l||e===r.m?o:null}},54:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,r=t.reconnectURL,i=t.noPrefix;if(!n)return null;var s=n;void 0!==i&&i||(s=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),r&&Object(a.a)(r)&&(s=s+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),r));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(s,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:i.a.string.isRequired,reconnectURL:i.a.string,noPrefix:i.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},549:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a}));var r=n(2);function i(e){return 0===e.length}function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return 0===e.length?1===t?Object(r.__)("Please select an answer","google-site-kit"):Object(r.__)("Please select at least 1 answer","google-site-kit"):null}},57:function(e,t,n){"use strict";(function(e){var r,i;n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=new Set((null===(r=e)||void 0===r||null===(i=r._googlesitekitBaseData)||void 0===i?void 0:i.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,n(28))},58:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return r.createElement("svg",i({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),a,o)}},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(39);function i(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},604:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UserInputSelectOptions}));var r=n(6),i=n.n(r),a=n(27),o=n.n(a),c=n(1),s=n.n(c),l=n(0),u=n(56),d=n(2),g=n(3),f=n(10),m=n(7),p=n(29),b=n(32),v=n(17),h=n(87),_=n(9),O=n(18);function E(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function UserInputSelectOptions(t){var n=t.slug,r=t.descriptions,a=t.options,c=t.max,s=t.next,k=t.showInstructions,y=t.alignLeftOptions,j=Object(O.a)(),S=Object(g.useSelect)((function(e){return e(m.a).getUserInputSetting(n)||[]})),N=Object(g.useSelect)((function(e){return e(m.a).isSavingUserInputSettings(S)})),w=Object(g.useSelect)((function(e){return e(b.a).isNavigating()})),T=Object(g.useDispatch)(m.a).setUserInputSetting,C=Object(l.useRef)();Object(l.useEffect)((function(){if(null==C?void 0:C.current){var e=function(e){e&&setTimeout((function(){e.focus()}),50)},t=1===c?"radio":"checkbox",n=C.current.querySelector('input[type="'.concat(t,'"]:checked'));if(n)e(n);else e(C.current.querySelector('input[type="'.concat(t,'"]')))}}),[c]);var A=Object(g.useDispatch)(p.a).setValues,x=Object(l.useCallback)((function(e){var t=e.target,r=t.value,a=t.checked,s=new Set([r].concat(o()(S)));a||s.delete(r);var l=n===h.j?"content_frequency_question_answer":"site_".concat(n,"_question_answer"),u=Array.from(s).slice(0,c);Object(_.I)("".concat(j,"_kmw"),l,u.join()),n===h.i&&A(h.b,i()({},n,S)),T(n,u)}),[c,T,n,S,j,A]),I=Object(l.useCallback)((function(e){e.keyCode===u.b&&S.length>0&&S.length<=c&&!S.includes("")&&s&&"function"==typeof s&&s()}),[S,s,c]),R=i()({},c>1?"onChange":"onClick",x),D=1===c?f.Radio:f.Checkbox,L=Object.keys(a).map((function(t){if("sell_products_or_service"===t)return!1;var o=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?E(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):E(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({id:"".concat(n,"-").concat(t),value:t,description:null==r?void 0:r[t],checked:S.includes(t),onKeyDown:I,alignLeft:y},R);return c>1?(o.disabled=S.length>=c&&!S.includes(t),o.name="".concat(n,"-").concat(t)):o.name=n,(N||w)&&(o.disabled=!0),e.createElement("div",{key:t,className:"googlesitekit-user-input__select-option"},e.createElement(D,o,a[t]))}));return e.createElement(v.a,{className:"googlesitekit-user-input__select-options-wrapper",lgStart:6,lgSize:6,mdSize:8,smSize:4},k&&e.createElement("p",{className:"googlesitekit-user-input__select-instruction"},e.createElement("span",null,Object(d.sprintf)(
/* translators: %s: number of answers allowed. */
Object(d._n)("Select only %d answer","Select up to %d answers",c,"google-site-kit"),c))),e.createElement("div",{className:"googlesitekit-user-input__select-options",ref:C},L))}UserInputSelectOptions.propTypes={slug:s.a.string.isRequired,descriptions:s.a.shape({}),options:s.a.shape({}).isRequired,max:s.a.number,next:s.a.func,showInstructions:s.a.bool,alignLeftOptions:s.a.bool},UserInputSelectOptions.defaultProps={max:1,showInstructions:!1,alignLeftOptions:!1}}).call(this,n(4))},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(0),i=Object(r.createContext)(""),a=(i.Consumer,i.Provider);t.b=i},624:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UserInputQuestionNotice}));var r=n(11),i=n.n(r),a=n(2);function UserInputQuestionNotice(t){var n=t.className;return e.createElement("p",{className:i()(n,"googlesitekit-user-input__question-notice")},Object(a.__)("You can always edit your answers later in Settings","google-site-kit"))}}).call(this,n(4))},682:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UserInputQuestionAuthor}));var r=n(1),i=n.n(r),a=n(2),o=n(3),c=n(7);function UserInputQuestionAuthor(t){var n=t.slug,r=Object(o.useSelect)((function(e){return e(c.a).getUserInputSettingAuthor(n)}));return r&&r.photo&&r.login?e.createElement("div",{className:"googlesitekit-user-input__author"},e.createElement("p",null,Object(a.__)("This question has been answered by:","google-site-kit")),e.createElement("div",{className:"googlesitekit-user-input__author-info"},e.createElement("img",{alt:r.login,src:r.photo}),r.login)):null}UserInputQuestionAuthor.propTypes={slug:i.a.string.isRequired}}).call(this,n(4))},683:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CancelUserInputButton}));var r=n(1),i=n.n(r),a=n(2),o=n(3),c=n(10),s=n(13),l=n(32);function CancelUserInputButton(t){var n=t.disabled,r=Object(o.useSelect)((function(e){return e(s.c).getAdminURL("googlesitekit-dashboard")})),i=Object(o.useDispatch)(l.a).navigateTo;return e.createElement(c.Button,{tertiary:!0,className:"googlesitekit-user-input__buttons--cancel",onClick:function(){return i(r)},disabled:n},Object(a.__)("Cancel","google-site-kit"))}CancelUserInputButton.propTypes={disabled:i.a.bool}}).call(this,n(4))},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return l})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return g})),n.d(t,"J",(function(){return f})),n.d(t,"I",(function(){return m})),n.d(t,"N",(function(){return p})),n.d(t,"f",(function(){return b})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return _})),n.d(t,"l",(function(){return O})),n.d(t,"m",(function(){return E})),n.d(t,"n",(function(){return k})),n.d(t,"o",(function(){return y})),n.d(t,"q",(function(){return j})),n.d(t,"s",(function(){return S})),n.d(t,"r",(function(){return N})),n.d(t,"t",(function(){return w})),n.d(t,"w",(function(){return T})),n.d(t,"u",(function(){return C})),n.d(t,"v",(function(){return A})),n.d(t,"x",(function(){return x})),n.d(t,"y",(function(){return I})),n.d(t,"A",(function(){return R})),n.d(t,"B",(function(){return D})),n.d(t,"C",(function(){return L})),n.d(t,"D",(function(){return P})),n.d(t,"k",(function(){return M})),n.d(t,"F",(function(){return z})),n.d(t,"z",(function(){return B})),n.d(t,"G",(function(){return H})),n.d(t,"E",(function(){return U})),n.d(t,"i",(function(){return F})),n.d(t,"p",(function(){return V})),n.d(t,"Q",(function(){return W})),n.d(t,"P",(function(){return q}));var r="core/user",i="connected_url_mismatch",a="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",l="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",g="googlesitekit_read_shared_module_data",f="googlesitekit_manage_module_sharing_options",m="googlesitekit_delegate_module_sharing_management",p="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",_="kmAnalyticsNewVisitors",O="kmAnalyticsPopularAuthors",E="kmAnalyticsPopularContent",k="kmAnalyticsPopularProducts",y="kmAnalyticsReturningVisitors",j="kmAnalyticsTopCities",S="kmAnalyticsTopCitiesDrivingLeads",N="kmAnalyticsTopCitiesDrivingAddToCart",w="kmAnalyticsTopCitiesDrivingPurchases",T="kmAnalyticsTopDeviceDrivingPurchases",C="kmAnalyticsTopConvertingTrafficSource",A="kmAnalyticsTopCountries",x="kmAnalyticsTopPagesDrivingLeads",I="kmAnalyticsTopRecentTrendingPages",R="kmAnalyticsTopTrafficSource",D="kmAnalyticsTopTrafficSourceDrivingAddToCart",L="kmAnalyticsTopTrafficSourceDrivingLeads",P="kmAnalyticsTopTrafficSourceDrivingPurchases",M="kmAnalyticsPagesPerVisit",z="kmAnalyticsVisitLength",B="kmAnalyticsTopReturningVisitorPages",H="kmSearchConsolePopularKeywords",U="kmAnalyticsVisitsPerVisitor",F="kmAnalyticsMostEngagingPages",V="kmAnalyticsTopCategories",W=[b,v,h,_,O,E,k,y,V,j,S,N,w,T,C,A,I,R,D,M,z,B,U,F,V],q=[].concat(W,[H])},70:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},71:function(e,t,n){"use strict";n.r(t),n.d(t,"CONTEXT_MAIN_DASHBOARD_KEY_METRICS",(function(){return r})),n.d(t,"CONTEXT_MAIN_DASHBOARD_TRAFFIC",(function(){return i})),n.d(t,"CONTEXT_MAIN_DASHBOARD_CONTENT",(function(){return a})),n.d(t,"CONTEXT_MAIN_DASHBOARD_SPEED",(function(){return o})),n.d(t,"CONTEXT_MAIN_DASHBOARD_MONETIZATION",(function(){return c})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_TRAFFIC",(function(){return s})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_CONTENT",(function(){return l})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_SPEED",(function(){return u})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_MONETIZATION",(function(){return d}));var r="mainDashboardKeyMetrics",i="mainDashboardTraffic",a="mainDashboardContent",o="mainDashboardSpeed",c="mainDashboardMonetization",s="entityDashboardTraffic",l="entityDashboardContent",u="entityDashboardSpeed",d="entityDashboardMonetization";t.default={CONTEXT_MAIN_DASHBOARD_KEY_METRICS:r,CONTEXT_MAIN_DASHBOARD_TRAFFIC:i,CONTEXT_MAIN_DASHBOARD_CONTENT:a,CONTEXT_MAIN_DASHBOARD_SPEED:o,CONTEXT_MAIN_DASHBOARD_MONETIZATION:c,CONTEXT_ENTITY_DASHBOARD_TRAFFIC:s,CONTEXT_ENTITY_DASHBOARD_CONTENT:l,CONTEXT_ENTITY_DASHBOARD_SPEED:u,CONTEXT_ENTITY_DASHBOARD_MONETIZATION:d}},72:function(e,t,n){"use strict";var r=n(15),i=n.n(r),a=n(265),o=n(1),c=n.n(o),s=n(0),l=n(144);function Portal(e){var t=e.children,n=e.slug,r=Object(s.useState)(document.createElement("div")),o=i()(r,1)[0];return Object(a.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(l.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},73:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),i=n(18),a=n(9);function o(e,t){var n=Object(i.a)(),o=null!=t?t:"".concat(n,"_").concat(e);return{view:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"view_notification"].concat(t))}),[o]),confirm:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"confirm_notification"].concat(t))}),[o]),dismiss:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"dismiss_notification"].concat(t))}),[o]),clickLearnMore:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"click_learn_more_link"].concat(t))}),[o])}}},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(33),i=n.n(r),a=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,n="object"===i()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(1),i=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,i=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:i}},n)}IconWrapper.propTypes={children:i.a.node.isRequired,marginLeft:i.a.number,marginRight:i.a.number}}).call(this,n(4))},77:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(0),g=Object(d.forwardRef)((function(t,n){var r=t.label,a=t.className,c=t.hasLeftSpacing,l=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",i()({ref:n},u,{className:s()("googlesitekit-badge",a,{"googlesitekit-badge--has-left-spacing":l})}),r)}));g.displayName="Badge",g.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=g}).call(this,n(4))},79:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(15),i=n.n(r),a=n(188),o=n(133),c={},s=void 0===e?null:e,l=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,u=void 0===r?0:r,d=e.initialHeight,g=void 0===d?0:d,f=Object(a.a)("undefined"==typeof document?[u,g]:l,t,n),m=i()(f,2),p=m[0],b=m[1],v=function(){return b(l)};return Object(o.a)(s,"resize",v),Object(o.a)(s,"orientationchange",v),p},d=function(e){return u(e)[0]}}).call(this,n(28))},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"s",(function(){return a})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return s})),n.d(t,"g",(function(){return l})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"m",(function(){return m})),n.d(t,"n",(function(){return p})),n.d(t,"h",(function(){return b})),n.d(t,"x",(function(){return v})),n.d(t,"w",(function(){return h})),n.d(t,"y",(function(){return _})),n.d(t,"u",(function(){return O})),n.d(t,"v",(function(){return E})),n.d(t,"f",(function(){return k})),n.d(t,"l",(function(){return y})),n.d(t,"e",(function(){return j})),n.d(t,"t",(function(){return S})),n.d(t,"c",(function(){return N})),n.d(t,"d",(function(){return w})),n.d(t,"b",(function(){return T}));var r="modules/analytics-4",i="account_create",a="property_create",o="webdatastream_create",c="analyticsSetup",s=10,l=1,u="https://www.googleapis.com/auth/tagmanager.readonly",d="enhanced-measurement-form",g="enhanced-measurement-enabled",f="enhanced-measurement-should-dismiss-activation-banner",m="analyticsAccountCreate",p="analyticsCustomDimensionsCreate",b="https://www.googleapis.com/auth/analytics.edit",v="dashboardAllTrafficWidgetDimensionName",h="dashboardAllTrafficWidgetDimensionColor",_="dashboardAllTrafficWidgetDimensionValue",O="dashboardAllTrafficWidgetActiveRowIndex",E="dashboardAllTrafficWidgetLoaded",k={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},y={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},j=[y.CONTACT,y.GENERATE_LEAD,y.SUBMIT_LEAD_FORM],S={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},N="audiencePermissionsSetup",w="audienceTileCustomDimensionCreate",T="audience-selection-panel-expirable-new-badge-"},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(106);function i(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),i=e.replace(n.origin,"");if(i.length<t)return i;var a=i.length-Math.floor(t)+1;return"…"+i.substr(a)}},804:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UserInputQuestionWrapper}));var r=n(1),i=n.n(r),a=n(2),o=n(38),c=n(3),s=n(10),l=n(7),u=n(17),d=n(1102),g=n(120),f=n(683),m=n(549),p=n(260),b=n(32),v=n(58);function UserInputQuestionWrapper(t){var n=t.children,r=t.slug,i=t.questionNumber,h=t.next,_=t.back,O=t.complete,E=t.error,k=Object(c.useSelect)((function(e){return e(l.a).getUserInputSetting(r)||[]})),y=Object(c.useSelect)((function(e){return e(l.a).getUserInputSettings()})),j=Object(c.useSelect)((function(e){return e(l.a).isSavingUserInputSettings(y)})),S=Object(c.useSelect)((function(e){return e(b.a).isNavigating()})),N=j||S;return e.createElement("div",{className:"googlesitekit-user-input__question"},e.createElement("div",{className:"googlesitekit-user-input__question-contents"},e.createElement(u.k,null,e.createElement(u.a,{lgSize:12,mdSize:8,smSize:4},e.createElement(u.k,null,e.createElement(d.a,{slug:r,questionNumber:i}),n)))),E&&e.createElement("div",{className:"googlesitekit-user-input__error"},e.createElement(g.a,{error:E,Icon:v.a})),e.createElement("div",{className:"googlesitekit-user-input__footer googlesitekit-user-input__buttons"},e.createElement("div",{className:"googlesitekit-user-input__footer-nav"},_&&e.createElement(s.Button,{tertiary:!0,className:"googlesitekit-user-input__buttons--back",onClick:_},Object(a.__)("Back","google-site-kit")),h&&e.createElement(s.Button,{className:"googlesitekit-user-input__buttons--next",onClick:h,disabled:Object(m.b)(k)},Object(a.__)("Next","google-site-kit")),O&&e.createElement(p.b,{className:"googlesitekit-user-input__buttons--complete",onClick:O,isSaving:N,disabled:Object(m.b)(k)},Object(o.a)(Object(a.__)("Complete<span> setup</span>","google-site-kit"),{span:e.createElement("span",{className:"googlesitekit-user-input__responsive-text"})}))),e.createElement("div",{className:"googlesitekit-user-input__footer-cancel"},e.createElement(f.a,null))))}UserInputQuestionWrapper.propTypes={slug:i.a.string.isRequired,questionNumber:i.a.number.isRequired,children:i.a.node,description:i.a.string,next:i.a.func,back:i.a.func,complete:i.a.func,error:i.a.object}}).call(this,n(4))},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return y})),n.d(t,"d",(function(){return j})),n.d(t,"e",(function(){return N})),n.d(t,"c",(function(){return w})),n.d(t,"b",(function(){return T}));var r=n(15),i=n.n(r),a=n(33),o=n.n(a),c=n(6),s=n.n(c),l=n(25),u=n.n(l),d=n(14),g=n(63),f=n.n(g),m=n(2);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=O(e,t),r=n.formatUnit,i=n.formatDecimal;try{return r()}catch(e){return i()}},h=function(e){var t=_(e),n=t.hours,r=t.minutes,i=t.seconds;return i=("0"+i).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(i):"".concat(n,":").concat(r,":").concat(i)},_=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},O=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=_(e),r=n.hours,i=n.minutes,a=n.seconds;return{hours:r,minutes:i,seconds:a,formatUnit:function(){var n=t.unitDisplay,o=b(b({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?N(a,b(b({},o),{},{unit:"second"})):Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?N(a,b(b({},o),{},{unit:"second"})):"",i?N(i,b(b({},o),{},{unit:"minute"})):"",r?N(r,b(b({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(m.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(m.__)("%ds","google-site-kit"),a);if(0===e)return t;var n=Object(m.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(m.__)("%dm","google-site-kit"),i),o=Object(m.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(m.__)("%dh","google-site-kit"),r);return Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",i?n:"",r?o:"").trim()}}},E=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},k=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in millions.
Object(m.__)("%sM","google-site-kit"),N(E(e),e%10==0?{}:t)):1e4<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),N(E(e))):1e3<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),N(E(e),e%10==0?{}:t)):N(e,{signDisplay:"never",maximumFractionDigits:1})};function y(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=b({},e)),t}function j(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=y(t),r=n.style,i=void 0===r?"metric":r;return"metric"===i?k(e):"duration"===i?v(e,n):"durationISO"===i?h(e):N(e,n)}var S=f()(console.warn),N=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?T():n,a=u()(t,["locale"]);try{return new Intl.NumberFormat(r,a).format(e)}catch(t){S("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],l={},d=0,g=Object.entries(a);d<g.length;d++){var f=i()(g[d],2),m=f[0],p=f[1];c[m]&&p===c[m]||(s.includes(m)||(l[m]=p))}try{return new Intl.NumberFormat(r,l).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?T():n,i=t.style,a=void 0===i?"long":i,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(r,{style:a,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var l=Object(m.__)(", ","google-site-kit");return e.join(l)},T=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i}));var r=n(149),i=n.n(r)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a);function ChangeArrow(t){var n=t.direction,r=t.invertColor,i=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:i,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:i.a.string,invertColor:i.a.bool,width:i.a.number,height:i.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},86:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={EXTERNAL:"external",INTERNAL:"internal"}},87:function(e,t,n){"use strict";n.d(t,"i",(function(){return s})),n.d(t,"j",(function(){return l})),n.d(t,"g",(function(){return u})),n.d(t,"h",(function(){return d})),n.d(t,"e",(function(){return g})),n.d(t,"c",(function(){return f})),n.d(t,"a",(function(){return m})),n.d(t,"b",(function(){return p})),n.d(t,"d",(function(){return b})),n.d(t,"f",(function(){return v})),n.d(t,"m",(function(){return h})),n.d(t,"k",(function(){return _})),n.d(t,"l",(function(){return O}));var r,i=n(6),a=n.n(i),o=n(2),c=n(8),s="purpose",l="postFrequency",u="goals",d=[s,l,u],g=(r={},a()(r,s,1),a()(r,l,1),a()(r,u,3),r),f="googlesitekit-user-input-currently-editing",m="user_input_question_number",p="user_input_question_snapshot",b="user-input-legacy-site-purpose-dismissed-item",v={publish_blog:[c.l.CONTACT,c.l.GENERATE_LEAD,c.l.SUBMIT_LEAD_FORM],publish_news:[c.l.CONTACT,c.l.GENERATE_LEAD,c.l.SUBMIT_LEAD_FORM],monetize_content:[],sell_products_or_service:[c.l.PURCHASE,c.l.ADD_TO_CART],sell_products:[c.l.PURCHASE,c.l.ADD_TO_CART],provide_services:[c.l.CONTACT,c.l.GENERATE_LEAD,c.l.SUBMIT_LEAD_FORM],share_portfolio:[c.l.CONTACT,c.l.GENERATE_LEAD,c.l.SUBMIT_LEAD_FORM],other:[]};function h(){var e=Object(o.__)("Based on your answer, Site Kit will suggest the metrics you see on your dashboard to help you track how close you’re getting to your specific goals","google-site-kit");return[{title:Object(o.__)("What is the main purpose of this site?","google-site-kit"),description:e},{title:Object(o.__)("How often do you create new content for this site?","google-site-kit"),description:e},{title:Object(o.__)("What are your top 3 goals for this site?","google-site-kit"),description:e}]}function _(){return{USER_INPUT_ANSWERS_PURPOSE:{sell_products_or_service:Object(o.__)("Sell products or services","google-site-kit"),sell_products:Object(o.__)("Sell products","google-site-kit"),provide_services:Object(o.__)("Provide services","google-site-kit"),monetize_content:Object(o.__)("Monetize content","google-site-kit"),publish_blog:Object(o.__)("Publish a blog","google-site-kit"),publish_news:Object(o.__)("Publish news content","google-site-kit"),share_portfolio:Object(o.__)("Portfolio or business card","google-site-kit"),other:Object(o.__)("Other","google-site-kit")},USER_INPUT_ANSWERS_POST_FREQUENCY:{never:Object(o.__)("Never","google-site-kit"),daily:Object(o.__)("Daily","google-site-kit"),weekly:Object(o.__)("Weekly","google-site-kit"),monthly:Object(o.__)("Monthly","google-site-kit"),other:Object(o.__)("Other","google-site-kit")},USER_INPUT_ANSWERS_GOALS:{retaining_visitors:Object(o.__)("Retain visitors, turn them into loyal readers or customers","google-site-kit"),improving_performance:Object(o.__)("Improve speed and performance","google-site-kit"),finding_new_topics:Object(o.__)("Find new topics to write about that connect with my audience","google-site-kit"),growing_audience:Object(o.__)("Grow my audience","google-site-kit"),expanding_business:Object(o.__)("Expand my business into new cities, states or markets","google-site-kit"),generating_revenue:Object(o.__)("Generate more revenue","google-site-kit"),generating_leads:Object(o.__)("Generate leads","google-site-kit"),help_better_rank:Object(o.__)("Help my content rank in a better position in Google search results","google-site-kit"),understanding_content_performance:Object(o.__)("Understand which content is performing best","google-site-kit"),encourage_to_post:Object(o.__)("Encouragement to post more frequently","google-site-kit"),other:Object(o.__)("Other","google-site-kit")}}}function O(){return{USER_INPUT_ANSWERS_PURPOSE:{sell_products_or_service:Object(o.__)("E.g. selling products like devices, apparel, equipment, etc. or offering services like courses, consulting, tutoring, etc.","google-site-kit"),sell_products:Object(o.__)("E.g. selling devices, apparel, equipment, etc.","google-site-kit"),provide_services:Object(o.__)("E.g. offering courses, consulting, tutoring, etc.","google-site-kit"),monetize_content:Object(o.__)("Using display ads, affiliate links, sponsored content, etc.","google-site-kit"),publish_blog:Object(o.__)("Writing on a topic you’re passionate about, no focus on monetizing content","google-site-kit"),publish_news:Object(o.__)("E.g. local news, investigative pieces, interviews, etc.","google-site-kit"),share_portfolio:Object(o.__)("My website represents me or my company","google-site-kit"),other:void 0}}}},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(12),i=n.n(r),a=function(e,t){var n=t.dateRangeLength;i()(Array.isArray(e),"report must be an array to partition."),i()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return i.b})),n.d(t,"J",(function(){return i.c})),n.d(t,"F",(function(){return a.a})),n.d(t,"K",(function(){return a.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return p})),n.d(t,"j",(function(){return b})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return y})),n.d(t,"c",(function(){return j})),n.d(t,"e",(function(){return S})),n.d(t,"b",(function(){return N})),n.d(t,"a",(function(){return w})),n.d(t,"f",(function(){return T})),n.d(t,"n",(function(){return C})),n.d(t,"w",(function(){return A})),n.d(t,"p",(function(){return x})),n.d(t,"G",(function(){return I})),n.d(t,"s",(function(){return R})),n.d(t,"v",(function(){return D})),n.d(t,"k",(function(){return L})),n.d(t,"o",(function(){return P.b})),n.d(t,"h",(function(){return P.a})),n.d(t,"t",(function(){return M.b})),n.d(t,"q",(function(){return M.a})),n.d(t,"A",(function(){return M.c})),n.d(t,"x",(function(){return z})),n.d(t,"u",(function(){return B})),n.d(t,"E",(function(){return F})),n.d(t,"D",(function(){return V.a})),n.d(t,"g",(function(){return W})),n.d(t,"L",(function(){return q})),n.d(t,"l",(function(){return G}));var r=n(14),i=n(36),a=n(75),o=n(33),c=n.n(o),s=n(96),l=n.n(s),u=function(e){return l()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var i=t[r];i&&"object"===c()(i)&&!Array.isArray(i)&&(i=e(i)),n[r]=i})),n}(e)))};n(97);var d=n(83);function g(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function f(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function m(e){return e.replace(/\n/gi,"<br>")}function p(e){for(var t=e,n=0,r=[g,f,m];n<r.length;n++){t=(0,r[n])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(15),_=n.n(h),O=n(12),E=n.n(O),k=n(2),y="Invalid dateString parameter, it must be a string.",j='Invalid date range, it must be a string with the format "last-x-days".',S=60,N=60*S,w=24*N,T=7*w;function C(){var e=function(e){return Object(k.sprintf)(
/* translators: %s: number of days */
Object(k._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function A(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function x(e){E()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function I(e){E()(A(e),y);var t=e.split("-"),n=_()(t,3),r=n[0],i=n[1],a=n[2];return new Date(r,i-1,a)}function R(e,t){return x(L(e,t*w))}function D(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function L(e,t){E()(A(e)||Object(r.isDate)(e)&&!isNaN(e),y);var n=A(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var P=n(98),M=n(80);function z(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function B(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var H=n(27),U=n.n(H),F=function(e){return Array.isArray(e)?U()(e).sort():e},V=n(89);function W(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var q=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},G=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},90:function(e,t,n){"use strict";(function(e,r){n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return O})),n.d(t,"a",(function(){return TourTooltips}));var i=n(6),a=n.n(i),o=n(81),c=n(30),s=n(1),l=n.n(s),u=n(2),d=n(3),g=n(23),f=n(7),m=n(36),p=n(107),b=n(18);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var h={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},_={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},O={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},E="feature_tooltip_view",k="feature_tooltip_advance",y="feature_tooltip_return",j="feature_tooltip_dismiss",S="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,i=t.tourID,s=t.gaEventCategory,l=t.callback,u="".concat(i,"-step"),N="".concat(i,"-run"),w=Object(d.useDispatch)(g.b).setValue,T=Object(d.useDispatch)(f.a).dismissTour,C=Object(d.useRegistry)(),A=Object(b.a)(),x=Object(d.useSelect)((function(e){return e(g.b).getValue(u)})),I=Object(d.useSelect)((function(e){return e(g.b).getValue(N)&&!1===e(f.a).isTourDismissed(i)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),w(N,!0)}));var R=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return r.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,r=e.lifecycle,i=e.size,a=e.status,o=e.type,l=t+1,u="function"==typeof s?s(A):s;o===c.b.TOOLTIP&&r===c.c.TOOLTIP?Object(m.b)(u,E,l):n===c.a.CLOSE&&r===c.c.COMPLETE?Object(m.b)(u,j,l):n===c.a.NEXT&&a===c.d.FINISHED&&o===c.b.TOUR_END&&i===l&&Object(m.b)(u,S,l),r===c.c.COMPLETE&&a!==c.d.FINISHED&&(n===c.a.PREV&&Object(m.b)(u,y,l),n===c.a.NEXT&&Object(m.b)(u,k,l))}(t);var n=t.action,r=t.index,a=t.status,o=t.step,d=t.type,g=n===c.a.CLOSE,f=!g&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),p=[c.d.FINISHED,c.d.SKIPPED].includes(a),b=g&&d===c.b.STEP_AFTER,v=p||b;if(c.b.STEP_BEFORE===d){var h,_,O=o.target;"string"==typeof o.target&&(O=e.document.querySelector(o.target)),null===(h=O)||void 0===h||null===(_=h.scrollIntoView)||void 0===_||_.call(h,{block:"center"})}f?function(e,t){w(u,e+(t===c.a.PREV?-1:1))}(r,n):v&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),T(i)),l&&l(t,C)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:O,locale:_,run:I,showProgress:!0,stepIndex:x,steps:R,styles:h,tooltipComponent:p.a})}TourTooltips.propTypes={steps:l.a.arrayOf(l.a.object).isRequired,tourID:l.a.string.isRequired,gaEventCategory:l.a.oneOfType([l.a.string,l.a.func]).isRequired,callback:l.a.func}}).call(this,n(28),n(4))},93:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(24),i=n(130);function a(t,n){var r=document.querySelector(t);if(!r)return 0;var i=r.getBoundingClientRect().top,a=o(n);return i+e.scrollY-a}function o(e){var t=c(e),n=document.querySelectorAll(".googlesitekit-navigation, .googlesitekit-entity-header");return t+=Array.from(n).reduce((function(e,t){return e+t.offsetHeight}),0)}function c(t){var n=0,a=document.querySelector(".googlesitekit-header");return n=!!a&&"sticky"===e.getComputedStyle(a).position?function(e){var t=document.querySelector(".googlesitekit-header");if(t){if(e===r.b)return t.offsetHeight;var n=t.getBoundingClientRect().bottom;return n<0?0:n}return 0}(t):function(e){var t=document.querySelector("#wpadminbar");return t&&e!==r.b?t.offsetHeight:0}(t),(n=Object(i.a)(n))<0?0:n}}).call(this,n(28))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o}));var r=n(239),i=n(85),a=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=n.invertColor,o=void 0!==a&&a;return Object(r.a)(e.createElement(i.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(6),i=n.n(r),a=n(14),o=n(100),c=n(101);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=l(l({},u),t);i.referenceSiteURL&&(i.referenceSiteURL=i.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(i,n),d=Object(c.a)(i,n,s,r),g={},f=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);g[r]||(g[r]=Object(a.once)(d)),g[r].apply(g,t)};return{enableTracking:function(){i.trackingEnabled=!0},disableTracking:function(){i.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!i.trackingEnabled},trackEvent:d,trackEventOnce:f}}}).call(this,n(28))}},[[1271,1,0]]]);