(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[2],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(59),a=n(39),i=n(57);function o(t,n){var o,c=Object(r.a)(n),s=t.activeModules,u=t.referenceSiteURL,l=t.userIDHash,d=t.userRoles,f=void 0===d?[]:d,g=t.isAuthenticated,m=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(a.b,"]"))),!o){o=!0;var r=(null==f?void 0:f.length)?f.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:u,plugin_version:m||"",enabled_features:Array.from(i.a).join(","),active_modules:s.join(","),authenticated:g?"1":"0",user_properties:{user_roles:r,user_identifier:l}});var d=n.createElement("script");return d.setAttribute(a.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(a.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(a.a)}}}}}).call(this,n(28))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var r=n(5),a=n.n(r),i=n(6),o=n.n(i),c=n(16),s=n.n(c),u=n(59);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function f(e,t,n,r){var i=Object(u.a)(t);return function(){var t=s()(a.a.mark((function t(o,c,s,u){var l;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),l={send_to:"site_kit",event_category:o,event_label:s,value:u},t.abrupt("return",new Promise((function(e){var t,n,a=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(a),e()};i("event",c,d(d({},l),{},{event_callback:s})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,a){return t.apply(this,arguments)}}()}},102:function(e,t,n){"use strict";var r=n(123);n.d(t,"a",(function(){return r.a}));var a=n(124);n.d(t,"c",(function(){return a.a}));var i=n(125);n.d(t,"b",(function(){return i.a}))},104:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return r.createElement("svg",a({viewBox:"0 0 14 14",fill:"none"},e),i)}},105:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),u=n(11),l=n.n(u);function VisuallyHidden(t){var n=t.className,r=t.children,i=o()(t,["className","children"]);return r?e.createElement("span",a()({},i,{className:l()("screen-reader-text",n)}),r):null}VisuallyHidden.propTypes={className:s.a.string,children:s.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(4))},107:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var r=n(21),a=n.n(r),i=n(152),o=n.n(i),c=n(11),s=n.n(c),u=n(1),l=n.n(u),d=n(2),f=n(10),g=n(154),m=n(104);function TourTooltip(t){var n=t.backProps,r=t.closeProps,c=t.index,u=t.primaryProps,l=t.size,p=t.step,v=t.tooltipProps,b=l>1?Object(g.a)(l):[],h=function(e){return s()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",a()({className:s()("googlesitekit-tour-tooltip",p.className)},v),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},p.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},p.content)),e.createElement(i.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},b.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:h(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(f.Button,a()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),p.cta,u.title&&e.createElement(f.Button,a()({className:"googlesitekit-tooltip-button",text:!0},u),u.title))),e.createElement(f.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(m.a,{width:"14",height:"14"}),onClick:r.onClick,"aria-label":Object(d.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:l.a.object.isRequired,closeProps:l.a.object.isRequired,index:l.a.number.isRequired,isLastStep:l.a.bool.isRequired,primaryProps:l.a.object.isRequired,size:l.a.number.isRequired,step:l.a.shape({content:l.a.node,title:l.a.node,cta:l.a.oneOfType([l.a.element,l.a.bool]),className:l.a.string}).isRequired,tooltipProps:l.a.object.isRequired}}).call(this,n(4))},109:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(208),s=n(38),u=n(2),l=n(10),d=n(58);function ModalDialog(t){var n=t.className,r=void 0===n?"":n,a=t.dialogActive,i=void 0!==a&&a,f=t.handleDialog,g=void 0===f?null:f,m=t.onOpen,p=void 0===m?null:m,v=t.onClose,b=void 0===v?null:v,h=t.title,y=void 0===h?null:h,O=t.provides,k=t.handleConfirm,E=t.subtitle,_=t.confirmButton,w=void 0===_?null:_,j=t.dependentModules,S=t.danger,N=void 0!==S&&S,T=t.inProgress,x=void 0!==T&&T,L=t.small,D=void 0!==L&&L,A=t.medium,C=void 0!==A&&A,R=t.buttonLink,P=void 0===R?null:R,I=Object(c.a)(ModalDialog),M="googlesitekit-dialog-description-".concat(I),B=!(!O||!O.length);return e.createElement(l.Dialog,{open:i,onOpen:p,onClose:b,"aria-describedby":B?M:void 0,tabIndex:"-1",className:o()(r,{"googlesitekit-dialog-sm":D,"googlesitekit-dialog-md":C})},e.createElement(l.DialogTitle,null,N&&e.createElement(d.a,{width:28,height:28}),y),E?e.createElement("p",{className:"mdc-dialog__lead"},E):[],e.createElement(l.DialogContent,null,B&&e.createElement("section",{id:M,className:"mdc-dialog__provides"},e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},O.map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t},e.createElement("span",{className:"mdc-list-item__text"},t))})))),j&&e.createElement("p",{className:"mdc-dialog__dependencies"},Object(s.a)(Object(u.sprintf)(
/* translators: %s is replaced with the dependent modules. */
Object(u.__)("<strong>Note:</strong> %s","google-site-kit"),j),{strong:e.createElement("strong",null)}))),e.createElement(l.DialogFooter,null,e.createElement(l.Button,{className:"mdc-dialog__cancel-button",tertiary:!0,onClick:g,disabled:x},Object(u.__)("Cancel","google-site-kit")),P?e.createElement(l.Button,{href:P,onClick:k,target:"_blank",danger:N},w):e.createElement(l.SpinnerButton,{onClick:k,danger:N,disabled:x,isSaving:x},w||Object(u.__)("Disconnect","google-site-kit"))))}ModalDialog.displayName="Dialog",ModalDialog.propTypes={className:a.a.string,dialogActive:a.a.bool,handleDialog:a.a.func,handleConfirm:a.a.func.isRequired,onOpen:a.a.func,onClose:a.a.func,title:a.a.string,confirmButton:a.a.string,danger:a.a.bool,small:a.a.bool,medium:a.a.bool,buttonLink:a.a.string},t.a=ModalDialog}).call(this,n(4))},1114:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActivationApp}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(15),s=n.n(c),u=n(0),l=n(2),d=n(3),f=n(10),g=n(256),m=n(17),p=n(9),v=n(13),b=n(7),h=n(32),y=n(18);function ActivationApp(){var t=Object(d.useDispatch)(h.a).navigateTo,n=Object(y.a)(),r=Object(d.useSelect)((function(e){return e(v.c).getAdminURL("googlesitekit-dashboard")})),i=Object(d.useSelect)((function(e){return e(v.c).getAdminURL("googlesitekit-splash")})),c=Object(d.useSelect)((function(e){return e(b.a).hasCapability(b.O)})),O=Object(u.useState)(!1),k=s()(O,2),E=k[0],_=k[1],w=c?r:i,j=c?Object(l.__)("Go to Dashboard","google-site-kit"):Object(l.__)("Start setup","google-site-kit");Object(u.useEffect)((function(){!E&&w&&(Object(p.I)(n,"view_notification"),_(!0))}),[n,w,E]);var S=Object(u.useCallback)(function(){var e=o()(a.a.mark((function e(r){var i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r.preventDefault(),i=c?"dashboard":"splash",e.next=4,Object(p.I)(n,"confirm_notification",i);case 4:t(w);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[w,c,t,n]);return w?e.createElement(m.e,null,e.createElement(m.k,null,e.createElement(m.a,{size:12},e.createElement(g.a,null),e.createElement("h3",{className:"googlesitekit-heading-3 googlesitekit-activation__title"},Object(l.__)("Congratulations, the Site Kit plugin is now activated","google-site-kit")),e.createElement(f.Button,{id:"start-setup-link",className:"googlesitekit-start-setup",onClick:S},j)))):null}}).call(this,n(4))},123:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(21),a=n.n(r),i=n(6),o=n.n(i),c=n(25),s=n.n(c),u=n(1),l=n.n(u),d=n(11),f=n.n(d);function Cell(t){var n,r=t.className,i=t.alignTop,c=t.alignMiddle,u=t.alignBottom,l=t.alignRight,d=t.alignLeft,g=t.smAlignRight,m=t.mdAlignRight,p=t.lgAlignRight,v=t.smSize,b=t.smStart,h=t.smOrder,y=t.mdSize,O=t.mdStart,k=t.mdOrder,E=t.lgSize,_=t.lgStart,w=t.lgOrder,j=t.size,S=t.children,N=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",a()({},N,{className:f()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":i,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":u,"mdc-layout-grid__cell--align-right":l,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":g,"mdc-layout-grid__cell--align-right-tablet":m,"mdc-layout-grid__cell--align-right-desktop":p},o()(n,"mdc-layout-grid__cell--span-".concat(j),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(E,"-desktop"),12>=E&&E>0),o()(n,"mdc-layout-grid__cell--start-".concat(_,"-desktop"),12>=_&&_>0),o()(n,"mdc-layout-grid__cell--order-".concat(w,"-desktop"),12>=w&&w>0),o()(n,"mdc-layout-grid__cell--span-".concat(y,"-tablet"),8>=y&&y>0),o()(n,"mdc-layout-grid__cell--start-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--order-".concat(k,"-tablet"),8>=k&&k>0),o()(n,"mdc-layout-grid__cell--span-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--start-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--order-".concat(h,"-phone"),4>=h&&h>0),n))}),S)}Cell.propTypes={smSize:l.a.number,smStart:l.a.number,smOrder:l.a.number,mdSize:l.a.number,mdStart:l.a.number,mdOrder:l.a.number,lgSize:l.a.number,lgStart:l.a.number,lgOrder:l.a.number,size:l.a.number,alignTop:l.a.bool,alignMiddle:l.a.bool,alignBottom:l.a.bool,alignRight:l.a.bool,alignLeft:l.a.bool,smAlignRight:l.a.bool,mdAlignRight:l.a.bool,lgAlignRight:l.a.bool,className:l.a.string,children:l.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},124:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),u=n(11),l=n.n(u),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.className,i=t.children,c=o()(t,["className","children"]);return e.createElement("div",a()({ref:n,className:l()("mdc-layout-grid__inner",r)},c),i)}));f.displayName="Row",f.propTypes={className:s.a.string,children:s.a.node},f.defaultProps={className:""},t.a=f}).call(this,n(4))},125:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),u=n(11),l=n.n(u),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.alignLeft,i=t.fill,c=t.className,s=t.children,u=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",a()({className:l()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":u,"mdc-layout-grid--fill":i})},d,{ref:n}),s)}));f.displayName="Grid",f.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},f.defaultProps={className:""},t.a=f}).call(this,n(4))},126:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",a({viewBox:"0 0 13 13"},e),i)}},127:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",a({viewBox:"0 0 13 13"},e),i)}},128:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},1283:function(e,t,n){"use strict";n.r(t),function(e){var t=n(332),r=n(144),a=n(1114),i=n(22),o=n(224);Object(t.a)((function(){var t=document.getElementById("js-googlesitekit-activation");t&&(Object(r.render)(e.createElement(o.a,{viewContext:i.h},e.createElement(a.a,null)),t),t.classList.remove("googlesitekit-activation--loading"))}))}.call(this,n(4))},129:function(e,t,n){"use strict";n.d(t,"a",(function(){return p})),n.d(t,"c",(function(){return b})),n.d(t,"b",(function(){return h}));var r=n(25),a=n.n(r),i=n(6),o=n.n(i),c=n(5),s=n.n(c),u=n(12),l=n.n(u),d=n(3),f=n.n(d),g=n(37),m=n(9),p=function(e){var t;l()(e,"storeName is required to create a snapshot store.");var n={},r={deleteSnapshot:s.a.mark((function e(){var t;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"DELETE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})),restoreSnapshot:s.a.mark((function e(){var t,n,r,a,i,o,c=arguments;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},n=t.clearAfterRestore,r=void 0===n||n,e.next=4,{payload:{},type:"RESTORE_SNAPSHOT"};case 4:if(a=e.sent,i=a.cacheHit,o=a.value,!i){e.next=13;break}return e.next=10,{payload:{snapshot:o},type:"SET_STATE_FROM_SNAPSHOT"};case 10:if(!r){e.next=13;break}return e.next=13,{payload:{},type:"DELETE_SNAPSHOT"};case 13:return e.abrupt("return",i);case 14:case"end":return e.stop()}}),e)})),createSnapshot:s.a.mark((function e(){var t;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"CREATE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))},i=(t={},o()(t,"DELETE_SNAPSHOT",(function(){return Object(g.c)("datastore::cache::".concat(e))})),o()(t,"CREATE_SNAPSHOT",Object(d.createRegistryControl)((function(t){return function(){return Object(g.f)("datastore::cache::".concat(e),t.stores[e].store.getState())}}))),o()(t,"RESTORE_SNAPSHOT",(function(){return Object(g.d)("datastore::cache::".concat(e),m.b)})),t);return{initialState:n,actions:r,controls:i,reducer:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1?arguments[1]:void 0,r=t.type,i=t.payload;switch(r){case"SET_STATE_FROM_SNAPSHOT":var o=i.snapshot,c=(o.error,a()(o,["error"]));return c;default:return e}}}},v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.a;return Object.values(e.stores).filter((function(e){return Object.keys(e.getActions()).includes("restoreSnapshot")}))},b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.a;return Promise.all(v(e).map((function(e){return e.getActions().createSnapshot()})))},h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.a;return Promise.all(v(e).map((function(e){return e.getActions().restoreSnapshot()})))}},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var r="core/site",a="primary",i="secondary"},130:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(14),a=function(e){return Object(r.isFinite)(e)?e:0}},136:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"d",(function(){return i})),n.d(t,"c",(function(){return o}));function r(e){var t=e.format,n=void 0===t?"small":t,r=e.hasErrorOrWarning,a=e.hasSmallImageSVG,o=e.hasWinImageSVG,c={smSize:4,mdSize:8,lgSize:12},s=i(n);return Object.keys(c).forEach((function(e){var t=c[e];r&&(t-=1),a&&(t-=1),o&&0<t-s[e]&&(t-=s[e]),c[e]=t})),c}var a=function(e){switch(e){case"small":return{};case"larger":return{smOrder:2,mdOrder:2,lgOrder:1};default:return{smOrder:2,mdOrder:1}}},i=function(e){switch(e){case"smaller":return{smSize:4,mdSize:2,lgSize:2};case"larger":return{smSize:4,mdSize:8,lgSize:7};default:return{smSize:4,mdSize:2,lgSize:4}}},o=function(e){switch(e){case"larger":return{smOrder:1,mdOrder:1,lgOrder:2};default:return{smOrder:1,mdOrder:2}}}},139:function(e,t,n){"use strict";var r=n(0),a=Object(r.createContext)(!1);t.a=a},148:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M3.332 24.75h21.335c2.182 0 3.542-2.366 2.451-4.25L16.451 2.07C15.36.184 12.64.184 11.549 2.07L.882 20.5c-1.091 1.884.269 4.25 2.45 4.25zM14 14.833a1.42 1.42 0 01-1.417-1.416v-2.834c0-.779.638-1.416 1.417-1.416.78 0 1.417.637 1.417 1.416v2.834A1.42 1.42 0 0114 14.833zm1.417 5.667h-2.834v-2.833h2.834V20.5z",fill:"currentColor",fillRule:"nonzero"});t.a=function SvgError(e){return r.createElement("svg",a({viewBox:"0 0 28 25"},e),i)}},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},155:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("path",{d:"M2.253 12.252l7.399 5.658A13.055 13.055 0 009 22c0 1.43.229 2.805.652 4.09l-7.4 5.658A22.02 22.02 0 010 22c0-3.506.81-6.814 2.253-9.748z",fill:"#FBBC05"}),r.createElement("path",{d:"M9.652 17.91l-7.4-5.658A21.935 21.935 0 0122 0c5.6 0 10.6 2.1 14.5 5.5l-6.4 6.4C27.9 10.1 25.1 9 22 9c-5.77 0-10.64 3.725-12.348 8.91z",fill:"#EA4335"}),r.createElement("path",{d:"M2.25 31.742l7.396-5.67A12.975 12.975 0 0022 35c6.1 0 10.7-3.1 11.8-8.5H22V18h20.5c.3 1.3.5 2.7.5 4 0 14-10 22-21 22A21.935 21.935 0 012.25 31.742z",fill:"#34A853"}),r.createElement("path",{d:"M36.34 38.52l-7.025-5.437c2.297-1.45 3.895-3.685 4.485-6.583H22V18h20.5c.3 1.3.5 2.7.5 4 0 7.17-2.623 12.767-6.66 16.52z",fill:"#4285F4"}));t.a=function SvgLogoG(e){return r.createElement("svg",a({viewBox:"0 0 43 44"},e),i)}},158:function(e,t,n){"use strict";var r=n(0),a=n(57),i=Object(r.createContext)(a.a);t.a=i},160:function(e,t,n){"use strict";var r=n(139),a=(r.a.Consumer,r.a.Provider);t.a=a},17:function(e,t,n){"use strict";var r=n(254);n.d(t,"i",(function(){return r.a}));var a=n(319);n.d(t,"f",(function(){return a.a}));var i=n(320);n.d(t,"h",(function(){return i.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var s=n(91),u=n.n(s);n.d(t,"b",(function(){return u.a})),n.d(t,"c",(function(){return s.DialogContent})),n.d(t,"d",(function(){return s.DialogFooter}));var l=n(102);n.d(t,"a",(function(){return l.a})),n.d(t,"e",(function(){return l.b})),n.d(t,"k",(function(){return l.c}))},172:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GenericErrorHandlerActions}));var r=n(1),a=n.n(r),i=n(2),o=n(20),c=n(194);function GenericErrorHandlerActions(t){var n=t.message,r=t.componentStack;return e.createElement("div",{className:"googlesitekit-generic-error-handler-actions"},e.createElement(c.a,{message:n,componentStack:r}),e.createElement(o.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0},Object(i.__)("Report this problem","google-site-kit")))}GenericErrorHandlerActions.propTypes={message:a.a.string,componentStack:a.a.string}}).call(this,n(4))},173:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(22),a=function(e){return r.f.includes(e)}},18:function(e,t,n){"use strict";var r=n(0),a=n(61);t.a=function(){return Object(r.useContext)(a.b)}},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r="core/modules",a="insufficient_module_dependencies"},194:function(e,t,n){"use strict";(function(e){var r=n(15),a=n.n(r),i=n(195),o=n.n(i),c=n(1),s=n.n(c),u=n(0),l=n(2),d=n(266),f=n(423),g=n(424),m=n(10);function ReportErrorButton(t){var n=t.message,r=t.componentStack,i=Object(u.useState)(!1),c=a()(i,2),s=c[0],p=c[1];return e.createElement(m.Button,{"aria-label":s?Object(l.__)("Error message copied to clipboard. Click to copy the error message again.","google-site-kit"):void 0,onClick:function(){o()("`".concat(n,"\n").concat(r,"`")),p(!0)},trailingIcon:e.createElement(d.a,{className:"mdc-button__icon",icon:s?f.a:g.a})},s?Object(l.__)("Copied to clipboard","google-site-kit"):Object(l.__)("Copy error contents","google-site-kit"))}ReportErrorButton.propTypes={message:s.a.string,componentStack:s.a.string},t.a=ReportErrorButton}).call(this,n(4))},197:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerNotification}));var r=n(21),a=n.n(r),i=n(6),o=n.n(i),c=n(5),s=n.n(c),u=n(16),l=n.n(u),d=n(15),f=n.n(d),g=n(1),m=n.n(g),p=n(11),v=n.n(p),b=n(206),h=n(240),y=n(81),O=n(0),k=n(106),E=n(3),_=n(17),w=n(93),j=n(37),S=n(24),N=n(211),T=n(213),x=n(212),L=n(226),D=n(227),A=n(86),C=n(136),R=n(130),P=n(32),I=n(228),M=n(79);function BannerNotification(t){var n,r=t.badgeLabel,i=t.children,c=t.className,u=void 0===c?"":c,d=t.ctaLabel,g=t.ctaLink,m=t.ctaTarget,p=t.description,B=t.dismiss,z=t.dismissExpires,V=void 0===z?0:z,H=t.format,F=void 0===H?"":H,W=t.id,G=t.isDismissible,U=void 0===G||G,q=t.learnMoreDescription,K=t.learnMoreLabel,$=t.learnMoreURL,J=t.learnMoreTarget,X=void 0===J?A.a.EXTERNAL:J,Q=t.logo,Y=t.module,Z=t.moduleName,ee=t.onCTAClick,te=t.onView,ne=t.onDismiss,re=t.onLearnMoreClick,ae=t.showOnce,ie=void 0!==ae&&ae,oe=t.SmallImageSVG,ce=t.title,se=t.type,ue=t.WinImageSVG,le=t.showSmallWinImage,de=void 0===le||le,fe=t.smallWinImageSVGWidth,ge=void 0===fe?75:fe,me=t.smallWinImageSVGHeight,pe=void 0===me?75:me,ve=t.mediumWinImageSVGWidth,be=void 0===ve?105:ve,he=t.mediumWinImageSVGHeight,ye=void 0===he?105:he,Oe=t.rounded,ke=void 0!==Oe&&Oe,Ee=t.footer,_e=t.secondaryPane,we=t.ctaComponent,je=Object(O.useState)(!1),Se=f()(je,2),Ne=Se[0],Te=Se[1],xe=Object(O.useState)(!1),Le=f()(xe,2),De=Le[0],Ae=Le[1],Ce="notification::dismissed::".concat(W),Re=function(){return Object(j.f)(Ce,new Date,{ttl:null})},Pe=Object(M.a)(),Ie=Object(S.e)(),Me=Object(b.a)(),Be=Object(O.useState)(!1),ze=f()(Be,2),Ve=ze[0],He=ze[1],Fe=Object(O.useRef)(),We=Object(h.a)(Fe,{rootMargin:"".concat(-Object(R.a)(Object(w.c)(Ie)),"px 0px 0px 0px"),threshold:0});Object(O.useEffect)((function(){!Ve&&(null==We?void 0:We.isIntersecting)&&("function"==typeof te&&te(),He(!0))}),[W,te,Ve,We]);var Ge=Pe>=600;Object(y.a)(l()(s.a.mark((function e(){var t,n;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(V>0)){e.next=3;break}return e.next=3,Qe();case 3:if(!U){e.next=9;break}return e.next=6,Object(j.d)(Ce);case 6:t=e.sent,n=t.cacheHit,Ae(n);case 9:if(!ie){e.next=12;break}return e.next=12,Re();case 12:case"end":return e.stop()}}),e)}))));var Ue=function(){var e=l()(s.a.mark((function e(t){return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),t.preventDefault(),!ne){e.next=5;break}return e.next=5,ne(t);case 5:Ke();case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),qe=Object(k.a)(g)&&"_blank"!==m,Ke=function(){return qe||Te(!0),new Promise((function(e){setTimeout(l()(s.a.mark((function t(){var n;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Re();case 2:Me()&&Ae(!0),n=new Event("notificationDismissed"),document.dispatchEvent(n),e();case 6:case"end":return t.stop()}}),t)}))),350)}))},$e=Object(E.useSelect)((function(e){return!!g&&e(P.a).isNavigatingTo(g)})),Je=Object(E.useDispatch)(P.a).navigateTo,Xe=function(){var e=l()(s.a.mark((function e(t){var n,r,a;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),qe&&!t.defaultPrevented&&t.preventDefault(),n=!0,!ee){e.next=12;break}return e.next=6,ee(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:r=e.t0,a=r.dismissOnCTAClick,n=void 0===a||a;case 12:if(!U||!n){e.next=15;break}return e.next=15,Ke();case 15:qe&&Je(g);case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Qe=function(){var e=l()(s.a.mark((function e(){var t,n,r;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(j.d)(Ce);case 2:if(t=e.sent,!(n=t.value)){e.next=10;break}if((r=new Date(n)).setSeconds(r.getSeconds()+parseInt(V,10)),!(r<new Date)){e.next=10;break}return e.next=10,Object(j.c)(Ce);case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();if(!$e&&U&&(void 0===De||De))return null;var Ye=!$e&&Ne?"is-closed":"is-open",Ze=Object(C.d)(F),et=Object(C.c)(F),tt=Object(C.a)(F),nt=Object(C.b)({format:F,hasErrorOrWarning:"win-error"===se||"win-warning"===se,hasSmallImageSVG:!!oe,hasWinImageSVG:!!ue});return e.createElement(N.a,{id:W,className:v()(u,(n={},o()(n,"googlesitekit-publisher-win--".concat(F),F),o()(n,"googlesitekit-publisher-win--".concat(se),se),o()(n,"googlesitekit-publisher-win--".concat(Ye),Ye),o()(n,"googlesitekit-publisher-win--rounded",ke),n)),secondaryPane:_e,ref:Fe},Q&&e.createElement(D.a,{module:Y,moduleName:Z}),oe&&e.createElement(_.a,{size:1,className:"googlesitekit-publisher-win__small-media"},e.createElement(oe,null)),e.createElement(_.a,a()({},nt,tt,{className:"googlesitekit-publisher-win__content"}),e.createElement(T.a,{title:ce,badgeLabel:r,smallWinImageSVGHeight:pe,smallWinImageSVGWidth:ge,winImageFormat:F,WinImageSVG:!Ge&&de?ue:void 0}),e.createElement(I.a,{description:p,learnMoreURL:$,learnMoreLabel:K,learnMoreTarget:X,learnMoreDescription:q,onLearnMoreClick:re}),i,e.createElement(x.a,{ctaLink:g,ctaLabel:d,ctaComponent:we,ctaTarget:m,ctaCallback:Xe,dismissLabel:U?B:void 0,dismissCallback:Ue}),Ee&&e.createElement("div",{className:"googlesitekit-publisher-win__footer"},Ee)),ue&&(Ge||!de)&&e.createElement(_.a,a()({},Ze,et,{alignBottom:"larger"===F,className:"googlesitekit-publisher-win__image"}),e.createElement("div",{className:"googlesitekit-publisher-win__image-".concat(F)},e.createElement(ue,{style:{maxWidth:be,maxHeight:ye}}))),e.createElement(L.a,{type:se}))}BannerNotification.propTypes={id:m.a.string.isRequired,className:m.a.string,title:m.a.string.isRequired,description:m.a.node,learnMoreURL:m.a.string,learnMoreDescription:m.a.string,learnMoreLabel:m.a.string,learnMoreTarget:m.a.oneOf(Object.values(A.a)),WinImageSVG:m.a.elementType,SmallImageSVG:m.a.elementType,format:m.a.string,ctaLink:m.a.string,ctaLabel:m.a.string,type:m.a.string,dismiss:m.a.string,isDismissible:m.a.bool,logo:m.a.bool,module:m.a.string,moduleName:m.a.string,dismissExpires:m.a.number,showOnce:m.a.bool,onCTAClick:m.a.func,onView:m.a.func,onDismiss:m.a.func,onLearnMoreClick:m.a.func,badgeLabel:m.a.string,rounded:m.a.bool,footer:m.a.node,secondaryPane:m.a.node,showSmallWinImage:m.a.bool,smallWinImageSVGWidth:m.a.number,smallWinImageSVGHeight:m.a.number,mediumWinImageSVGWidth:m.a.number,mediumWinImageSVGHeight:m.a.number}}).call(this,n(4))},198:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleIcon}));var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),u=n(3),l=n(19);function ModuleIcon(t){var n=t.slug,r=t.size,i=o()(t,["slug","size"]),c=Object(u.useSelect)((function(e){return e(l.a).getModuleIcon(n)}));return c?e.createElement(c,a()({width:r,height:r},i)):null}ModuleIcon.propTypes={slug:s.a.string.isRequired,size:s.a.number},ModuleIcon.defaultProps={size:33}}).call(this,n(4))},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(11),s=n.n(c),u=n(1),l=n.n(u),d=n(146),f=n(0),g=n(2),m=n(126),p=n(127),v=n(128),b=n(70),h=n(76),y=Object(f.forwardRef)((function(t,n){var r,i=t["aria-label"],c=t.secondary,u=void 0!==c&&c,l=t.arrow,f=void 0!==l&&l,y=t.back,O=void 0!==y&&y,k=t.caps,E=void 0!==k&&k,_=t.children,w=t.className,j=void 0===w?"":w,S=t.danger,N=void 0!==S&&S,T=t.disabled,x=void 0!==T&&T,L=t.external,D=void 0!==L&&L,A=t.hideExternalIndicator,C=void 0!==A&&A,R=t.href,P=void 0===R?"":R,I=t.inverse,M=void 0!==I&&I,B=t.noFlex,z=void 0!==B&&B,V=t.onClick,H=t.small,F=void 0!==H&&H,W=t.standalone,G=void 0!==W&&W,U=t.linkButton,q=void 0!==U&&U,K=t.to,$=t.leadingIcon,J=t.trailingIcon,X=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),Q=P||K||!V?K?"ROUTER_LINK":D?"EXTERNAL_LINK":"LINK":x?"BUTTON_DISABLED":"BUTTON",Y="BUTTON"===Q||"BUTTON_DISABLED"===Q?"button":"ROUTER_LINK"===Q?d.b:"a",Z=("EXTERNAL_LINK"===Q&&(r=Object(g._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===Q&&(r=Object(g._x)("(disabled)","screen reader text","google-site-kit")),r?i?"".concat(i," ").concat(r):"string"==typeof _?"".concat(_," ").concat(r):void 0:i),ee=$,te=J;return O&&(ee=e.createElement(v.a,{width:14,height:14})),D&&!C&&(te=e.createElement(b.a,{width:14,height:14})),f&&!M&&(te=e.createElement(m.a,{width:14,height:14})),f&&M&&(te=e.createElement(p.a,{width:14,height:14})),e.createElement(Y,a()({"aria-label":Z,className:s()("googlesitekit-cta-link",j,{"googlesitekit-cta-link--secondary":u,"googlesitekit-cta-link--inverse":M,"googlesitekit-cta-link--small":F,"googlesitekit-cta-link--caps":E,"googlesitekit-cta-link--danger":N,"googlesitekit-cta-link--disabled":x,"googlesitekit-cta-link--standalone":G,"googlesitekit-cta-link--link-button":q,"googlesitekit-cta-link--no-flex":!!z}),disabled:x,href:"LINK"!==Q&&"EXTERNAL_LINK"!==Q||x?void 0:P,onClick:V,rel:"EXTERNAL_LINK"===Q?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===Q?"_blank":void 0,to:K},X),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},_),!!te&&e.createElement(h.a,{marginLeft:5},te))}));y.propTypes={arrow:l.a.bool,back:l.a.bool,caps:l.a.bool,children:l.a.node,className:l.a.string,danger:l.a.bool,disabled:l.a.bool,external:l.a.bool,hideExternalIndicator:l.a.bool,href:l.a.string,inverse:l.a.bool,leadingIcon:l.a.node,linkButton:l.a.bool,noFlex:l.a.bool,onClick:l.a.func,small:l.a.bool,standalone:l.a.bool,to:l.a.string,trailingIcon:l.a.node},t.a=y}).call(this,n(4))},211:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(0),s=n(17),u=Object(c.forwardRef)((function(t,n){var r=t.id,a=t.className,i=t.children,u=t.secondaryPane;return e.createElement("section",{id:r,className:o()(a,"googlesitekit-publisher-win"),ref:n},e.createElement(s.e,null,e.createElement(s.k,null,i)),u&&e.createElement(c.Fragment,null,e.createElement("div",{className:"googlesitekit-publisher-win__secondary-pane-divider"}),e.createElement(s.e,{className:"googlesitekit-publisher-win__secondary-pane"},e.createElement(s.k,null,e.createElement(s.a,{className:"googlesitekit-publisher-win__secondary-pane",size:12},u)))))}));u.displayName="Banner",u.propTypes={id:a.a.string,className:a.a.string,secondaryPane:a.a.node},t.a=u}).call(this,n(4))},212:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerActions}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(15),s=n.n(c),u=n(1),l=n.n(u),d=n(206),f=n(0),g=n(3),m=n(10),p=n(32);function BannerActions(t){var n=t.ctaLink,r=t.ctaLabel,i=t.ctaComponent,c=t.ctaTarget,u=t.ctaCallback,l=t.dismissLabel,v=t.dismissCallback,b=Object(f.useState)(!1),h=s()(b,2),y=h[0],O=h[1],k=Object(d.a)(),E=Object(g.useSelect)((function(e){return!!n&&e(p.a).isNavigatingTo(n)})),_=function(){var e=o()(a.a.mark((function e(){var t,n,r,i=arguments;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(O(!0),t=i.length,n=new Array(t),r=0;r<t;r++)n[r]=i[r];return e.next=4,null==u?void 0:u.apply(void 0,n);case 4:k()&&O(!1);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return n||l||i?e.createElement("div",{className:"googlesitekit-publisher-win__actions"},i,r&&e.createElement(m.SpinnerButton,{className:"googlesitekit-notification__cta",href:n,target:c,onClick:_,disabled:y||E,isSaving:y||E},r),l&&e.createElement(m.Button,{tertiary:n||i,onClick:v,disabled:y||E},l)):null}BannerActions.propTypes={ctaLink:l.a.string,ctaLabel:l.a.string,ctaComponent:l.a.element,ctaTarget:l.a.string,ctaCallback:l.a.func,dismissLabel:l.a.string,dismissCallback:l.a.func}}).call(this,n(4))},213:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerTitle}));var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(11),s=n.n(c),u=n(77);function BannerTitle(t){var n=t.title,r=t.badgeLabel,i=t.WinImageSVG,o=t.winImageFormat,c=void 0===o?"":o,l=t.smallWinImageSVGWidth,d=void 0===l?75:l,f=t.smallWinImageSVGHeight,g=void 0===f?75:f;return n?e.createElement("div",{className:"googlesitekit-publisher-win__title-image-wrapper"},e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n,r&&e.createElement(u.a,{label:r})),i&&e.createElement("div",{className:s()(a()({},"googlesitekit-publisher-win__image-".concat(c),c))},e.createElement(i,{width:d,height:g}))):null}BannerTitle.propTypes={title:o.a.string,badgeLabel:o.a.string,WinImageSVG:o.a.elementType,winImageFormat:o.a.string,smallWinImageSVGWidth:o.a.number,smallWinImageSVGHeight:o.a.number}}).call(this,n(4))},22:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return a})),n.d(t,"o",(function(){return i})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return s})),n.d(t,"s",(function(){return u})),n.d(t,"i",(function(){return l})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return f})),n.d(t,"k",(function(){return g})),n.d(t,"u",(function(){return m})),n.d(t,"v",(function(){return p})),n.d(t,"q",(function(){return v})),n.d(t,"p",(function(){return b})),n.d(t,"b",(function(){return h})),n.d(t,"e",(function(){return y})),n.d(t,"a",(function(){return O})),n.d(t,"d",(function(){return k})),n.d(t,"c",(function(){return E})),n.d(t,"f",(function(){return _})),n.d(t,"g",(function(){return w}));var r="mainDashboard",a="entityDashboard",i="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",s="activation",u="splash",l="adminBar",d="adminBarViewOnly",f="settings",g="adBlockingRecovery",m="wpDashboard",p="wpDashboardViewOnly",v="moduleSetup",b="metricSelection",h="key-metrics",y="traffic",O="content",k="speed",E="monetization",_=[r,a,i,o,c,u,f,v,b],w=[i,o,d,p]},224:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Root}));var r=n(15),a=n.n(r),i=n(1),o=n.n(i),c=n(432),s=n(534),u=n(0),l=n(3),d=n.n(l),f=n(225),g=n(229),m=n(57),p=n(230),v=n(232),b=n(233),h=n(61),y=n(160),O=n(173);function Root(t){var n=t.children,r=t.registry,i=t.viewContext,o=void 0===i?null:i,d=c.a,k=Object(u.useState)({key:"Root",value:!0}),E=a()(k,1)[0];return e.createElement(u.StrictMode,null,e.createElement(y.a,{value:E},e.createElement(l.RegistryProvider,{value:r},e.createElement(g.a,{value:m.a},e.createElement(h.a,{value:o},e.createElement(s.a,{theme:d()},e.createElement(f.a,null,e.createElement(v.a,null,n,o&&e.createElement(b.a,null)),Object(O.a)(o)&&e.createElement(p.a,null))))))))}Root.propTypes={children:o.a.node,registry:o.a.object,viewContext:o.a.string.isRequired},Root.defaultProps={registry:d.a}}).call(this,n(4))},225:function(e,t,n){"use strict";(function(e,r){var a=n(51),i=n.n(a),o=n(53),c=n.n(o),s=n(68),u=n.n(s),l=n(69),d=n.n(l),f=n(49),g=n.n(f),m=n(1),p=n.n(m),v=n(0),b=n(2),h=n(172),y=n(61),O=n(197),k=n(9);function E(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=g()(e);if(t){var a=g()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return d()(this,n)}}var _=function(t){u()(ErrorHandler,t);var n=E(ErrorHandler);function ErrorHandler(e){var t;return i()(this,ErrorHandler),(t=n.call(this,e)).state={error:null,info:null,copied:!1},t}return c()(ErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t,info:n}),Object(k.I)("react_error","handle_".concat(this.context||"unknown","_error"),"".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,a=t.info;return n?r.createElement(O.a,{id:"googlesitekit-error",className:"googlesitekit-error-handler",title:Object(b.__)("Site Kit encountered an error","google-site-kit"),description:r.createElement(h.a,{message:n.message,componentStack:a.componentStack}),isDismissible:!1,format:"small",type:"win-error"},r.createElement("pre",{className:"googlesitekit-overflow-auto"},n.message,a.componentStack)):e}}]),ErrorHandler}(v.Component);_.contextType=y.b,_.propTypes={children:p.a.node.isRequired},t.a=_}).call(this,n(28),n(4))},226:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var r=n(1),a=n.n(r),i=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var r="win-warning"===n?e.createElement(i.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},r))}BannerIcon.propTypes={type:a.a.string}}).call(this,n(4))},227:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerLogo}));var r=n(1),a=n.n(r),i=n(17),o=n(155),c=n(198);function BannerLogo(t){var n=t.module,r=t.moduleName;return e.createElement(i.a,{size:12},e.createElement("div",{className:"googlesitekit-publisher-win__logo"},n&&e.createElement(c.a,{slug:n,size:19}),!n&&e.createElement(o.a,{height:"34",width:"32"})),r&&e.createElement("div",{className:"googlesitekit-publisher-win__module-name"},r))}BannerLogo.propTypes={module:a.a.string,moduleName:a.a.string}}).call(this,n(4))},228:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerDescription}));var r=n(1),a=n.n(r),i=n(0),o=n(75),c=n(20),s=n(86);function BannerDescription(t){var n=t.description,r=t.learnMoreLabel,a=t.learnMoreURL,u=t.learnMoreTarget,l=t.learnMoreDescription,d=t.onLearnMoreClick;if(!n)return null;var f;return r&&(f=e.createElement(i.Fragment,null,e.createElement(c.a,{onClick:function(e){e.persist(),null==d||d()},href:a,external:u===s.a.EXTERNAL},r),l)),e.createElement("div",{className:"googlesitekit-publisher-win__desc"},Object(i.isValidElement)(n)?e.createElement(i.Fragment,null,n,f&&e.createElement("p",null,f)):e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.a)(n,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",f))}BannerDescription.propTypes={description:a.a.node,learnMoreURL:a.a.string,learnMoreDescription:a.a.string,learnMoreLabel:a.a.string,learnMoreTarget:a.a.oneOf(Object.values(s.a)),onLearnMoreClick:a.a.func}}).call(this,n(4))},229:function(e,t,n){"use strict";var r=n(158),a=(r.a.Consumer,r.a.Provider);t.a=a},23:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a}));var r="core/ui",a="activeContextID"},230:function(e,t,n){"use strict";(function(e){var r=n(3),a=n(231),i=n(7);t.a=function PermissionsModal(){return Object(r.useSelect)((function(e){return e(i.a).isAuthenticated()}))?e.createElement(a.a,null):null}}).call(this,n(4))},231:function(e,t,n){"use strict";(function(e,r){var a=n(5),i=n.n(a),o=n(16),c=n.n(o),s=n(2),u=n(0),l=n(3),d=n(109),f=n(29),g=n(32),m=n(7),p=n(129),v=n(72);t.a=function AuthenticatedPermissionsModal(){var t,n,a,o,b=Object(l.useRegistry)(),h=Object(l.useSelect)((function(e){return e(m.a).getPermissionScopeError()})),y=Object(l.useSelect)((function(e){return e(m.a).getUnsatisfiedScopes()})),O=Object(l.useSelect)((function(t){var n,r,a;return t(m.a).getConnectURL({additionalScopes:null==h||null===(n=h.data)||void 0===n?void 0:n.scopes,redirectURL:(null==h||null===(r=h.data)||void 0===r?void 0:r.redirectURL)||e.location.href,errorRedirectURL:null==h||null===(a=h.data)||void 0===a?void 0:a.errorRedirectURL})})),k=Object(l.useDispatch)(m.a).clearPermissionScopeError,E=Object(l.useDispatch)(g.a).navigateTo,_=Object(l.useDispatch)(f.a).setValues,w=Object(u.useCallback)((function(){k()}),[k]),j=Object(u.useCallback)(c()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return _(m.d,{permissionsError:h}),e.next=3,Object(p.c)(b);case 3:E(O);case 4:case"end":return e.stop()}}),e)}))),[b,O,E,h,_]);return Object(u.useEffect)((function(){(function(){var e=c()(i.a.mark((function e(){var t,n,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(null==h||null===(t=h.data)||void 0===t?void 0:t.skipModal)||!(null==h||null===(n=h.data)||void 0===n||null===(r=n.scopes)||void 0===r?void 0:r.length)){e.next=3;break}return e.next=3,j();case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[j,h]),h?(null==h||null===(t=h.data)||void 0===t||null===(n=t.scopes)||void 0===n?void 0:n.length)?(null==h||null===(a=h.data)||void 0===a?void 0:a.skipModal)||y&&(null==h||null===(o=h.data)||void 0===o?void 0:o.scopes.every((function(e){return y.includes(e)})))?null:r.createElement(v.a,null,r.createElement(d.a,{title:Object(s.__)("Additional Permissions Required","google-site-kit"),subtitle:h.message,confirmButton:Object(s.__)("Proceed","google-site-kit"),dialogActive:!0,handleConfirm:j,handleDialog:w,medium:!0})):(e.console.warn("permissionsError lacks scopes array to use for redirect, so not showing the PermissionsModal. permissionsError was:",h),null):null}}).call(this,n(28),n(4))},232:function(e,t,n){"use strict";var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(15),s=n.n(c),u=n(0),l=n(3),d=n(129);t.a=function RestoreSnapshots(e){var t=e.children,n=Object(l.useRegistry)(),r=Object(u.useState)(!1),i=s()(r,2),c=i[0],f=i[1];return Object(u.useEffect)((function(){c||o()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(d.b)(n);case 2:f(!0);case 3:case"end":return e.stop()}}),e)})))()}),[n,c]),c?t:null}},233:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return FeatureTours}));var a=n(81),i=n(0),o=n(3),c=n(7),s=n(18),u=n(90);function FeatureTours(){var t=Object(s.a)(),n=Object(o.useDispatch)(c.a).triggerTourForView;Object(a.a)((function(){n(t)}));var l=Object(o.useSelect)((function(e){return e(c.a).getCurrentTour()}));return Object(i.useEffect)((function(){if(l){var t=document.getElementById("js-googlesitekit-main-dashboard");if(t){var n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}}),[l]),l?r.createElement(u.a,{tourID:l.slug,steps:l.steps,gaEventCategory:l.gaEventCategory,callback:l.callback}):null}}).call(this,n(28),n(4))},24:function(e,t,n){"use strict";n.d(t,"d",(function(){return a})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return s}));var r=n(79),a="xlarge",i="desktop",o="tablet",c="small";function s(){var e=Object(r.a)();return e>1280?a:e>960?i:e>600?o:c}},256:function(e,t,n){"use strict";(function(e){var r=n(2),a=n(155),i=n(257),o=n(105);t.a=function Logo(){return e.createElement("div",{className:"googlesitekit-logo","aria-hidden":"true"},e.createElement(a.a,{className:"googlesitekit-logo__logo-g",height:"34",width:"32"}),e.createElement(i.a,{className:"googlesitekit-logo__logo-sitekit",height:"26",width:"99"}),e.createElement(o.a,null,Object(r.__)("Site Kit by Google Logo","google-site-kit")))}}).call(this,n(4))},257:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M62.09 1.664h3.038v.1L58.34 9.593l7.241 10.224v.1H62.7L56.755 11.4 53.95 14.64v5.278h-2.351V1.664h2.35v9.415h.1l8.04-9.415zM69.984 3.117c0 .454-.166.853-.487 1.175-.322.322-.71.488-1.176.488-.455 0-.854-.166-1.175-.488a1.599 1.599 0 01-.488-1.175c0-.466.166-.854.488-1.176.321-.322.71-.488 1.175-.488.455 0 .854.166 1.176.488.332.333.487.72.487 1.176zm-.476 4.313v12.498h-2.351V7.43h2.35zM77.016 20.128c-1.02 0-1.864-.31-2.54-.943-.676-.632-1.02-1.508-1.031-2.628V9.57h-2.196V7.43h2.196V3.603h2.35V7.43h3.061v2.14h-3.06v6.222c0 .831.166 1.397.488 1.696.321.3.687.444 1.097.444.189 0 .366-.022.555-.067.188-.044.344-.1.499-.166l.743 2.096c-.632.222-1.342.333-2.162.333zM2.673 18.952C1.375 18.009.488 16.678 0 14.97l2.883-1.176c.289 1.076.799 1.94 1.542 2.628.732.677 1.619 1.02 2.65 1.02.965 0 1.774-.244 2.45-.742.677-.5 1.01-1.187 1.01-2.052 0-.798-.3-1.453-.887-1.974-.588-.521-1.62-1.042-3.094-1.564l-1.22-.432C4.025 10.224 2.928 9.57 2.04 8.716 1.153 7.862.71 6.742.71 5.346c0-.966.266-1.853.787-2.673C2.018 1.852 2.75 1.209 3.693.72 4.624.244 5.678 0 6.864 0c1.708 0 3.072.41 4.081 1.242 1.02.832 1.697 1.752 2.04 2.795L10.236 5.2c-.2-.621-.576-1.164-1.142-1.63-.565-.477-1.286-.71-2.173-.71s-1.641.222-2.251.676c-.61.455-.91 1.032-.91 1.742 0 .676.278 1.22.82 1.663.544.432 1.398.854 2.563 1.253l1.22.41c1.674.577 2.96 1.342 3.88 2.274.921.931 1.376 2.184 1.376 3.748 0 1.275-.322 2.34-.976 3.193a6.01 6.01 0 01-2.495 1.919 8.014 8.014 0 01-3.116.621c-1.62 0-3.072-.466-4.358-1.408zM15.969 3.449a1.95 1.95 0 01-.588-1.43c0-.566.2-1.043.588-1.431A1.95 1.95 0 0117.399 0c.566 0 1.043.2 1.43.588.389.388.588.865.588 1.43 0 .566-.2 1.043-.587 1.43a1.95 1.95 0 01-1.43.589c-.566-.012-1.043-.2-1.431-.588zm-.067 2.595h2.994v13.883h-2.994V6.044zM25.405 19.85c-.543-.2-.986-.466-1.33-.788-.776-.776-1.176-1.84-1.176-3.182V8.683h-2.428v-2.64h2.428V2.13h2.994v3.926h3.372v2.639h-3.372v6.531c0 .743.145 1.276.433 1.575.277.366.743.543 1.42.543.31 0 .576-.044.82-.122.233-.077.488-.21.765-.399v2.917c-.599.277-1.32.41-2.173.41a5.01 5.01 0 01-1.753-.3zM33.623 19.407a6.63 6.63 0 01-2.529-2.628c-.61-1.12-.909-2.373-.909-3.77 0-1.332.3-2.551.887-3.693.588-1.132 1.409-2.04 2.462-2.706 1.053-.666 2.251-1.01 3.593-1.01 1.397 0 2.606.311 3.637.921a6.123 6.123 0 012.34 2.528c.532 1.076.799 2.274.799 3.627 0 .255-.023.576-.078.953H33.179c.111 1.287.566 2.285 1.375 2.983a4.162 4.162 0 002.817 1.043c.854 0 1.597-.189 2.218-.588a4.266 4.266 0 001.508-1.597l2.528 1.198c-.654 1.142-1.508 2.04-2.561 2.694-1.054.655-2.318.976-3.782.976-1.364.022-2.584-.288-3.66-.931zm7.23-8.051a3.332 3.332 0 00-.466-1.453c-.277-.477-.687-.887-1.242-1.208-.554-.322-1.23-.488-2.03-.488-.964 0-1.773.288-2.439.853-.665.566-1.12 1.342-1.375 2.296h7.552z",fill:"#5F6368"});t.a=function SvgLogoSitekit(e){return r.createElement("svg",a({viewBox:"0 0 80 21",fill:"none"},e),i)}},29:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/forms"},3:function(e,t){e.exports=googlesitekit.data},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return O})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return y}));var r=n(99),a=e._googlesitekitTrackingData||{},i=a.activeModules,o=void 0===i?[]:i,c=a.isSiteKitScreen,s=a.trackingEnabled,u=a.trackingID,l=a.referenceSiteURL,d=a.userIDHash,f=a.isAuthenticated,g={activeModules:o,trackingEnabled:s,trackingID:u,referenceSiteURL:l,userIDHash:d,isSiteKitScreen:c,userRoles:a.userRoles,isAuthenticated:f,pluginVersion:"1.151.0"},m=Object(r.a)(g),p=m.enableTracking,v=m.disableTracking,b=(m.isTrackingEnabled,m.initializeSnippet),h=m.trackEvent,y=m.trackEventOnce;function O(e){e?p():v()}c&&s&&b()}).call(this,n(28))},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return y})),n.d(t,"c",(function(){return O})),n.d(t,"e",(function(){return k})),n.d(t,"b",(function(){return E}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return u(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return u(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var l,d="googlesitekit_",f="".concat(d).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),g=["sessionStorage","localStorage"],m=[].concat(g),p=function(){var t=o()(a.a.mark((function t(n){var r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,i="__storage_test__",r.setItem(i,i),r.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function v(){return b.apply(this,arguments)}function b(){return(b=o()(a.a.mark((function t(){var n,r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===l){t.next=2;break}return t.abrupt("return",l);case 2:n=s(m),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(i=r.value,!l){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,p(i);case 11:if(!t.sent){t.next=13;break}l=e[i];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===l&&(l=null),t.abrupt("return",l);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=o()(a.a.mark((function e(t){var n,r,i,o,c,s,u;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(f).concat(t)))){e.next=10;break}if(i=JSON.parse(r),o=i.timestamp,c=i.ttl,s=i.value,u=i.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:u});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),y=function(){var t=o()(a.a.mark((function t(n,r){var i,o,s,u,l,d,g,m,p=arguments;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=p.length>2&&void 0!==p[2]?p[2]:{},o=i.ttl,s=void 0===o?c.b:o,u=i.timestamp,l=void 0===u?Math.round(Date.now()/1e3):u,d=i.isError,g=void 0!==d&&d,t.next=3,v();case 3:if(!(m=t.sent)){t.next=14;break}return t.prev=5,m.setItem("".concat(f).concat(n),JSON.stringify({timestamp:l,ttl:s,value:r,isError:g})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),O=function(){var t=o()(a.a.mark((function t(n){var r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,v();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,i=n.startsWith(d)?n:"".concat(f).concat(n),r.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),k=function(){var t=o()(a.a.mark((function t(){var n,r,i,o;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,v();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],i=0;i<n.length;i++)0===(o=n.key(i)).indexOf(d)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),E=function(){var e=o()(a.a.mark((function e(){var t,n,r,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v();case 2:if(!e.sent){e.next=25;break}return e.next=6,k();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return i=r.value,e.next=14,O(i);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r="_googlesitekitDataLayer",a="data-googlesitekit-gtag"},57:function(e,t,n){"use strict";(function(e){var r,a;n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var i=new Set((null===(r=e)||void 0===r||null===(a=r._googlesitekitBaseData)||void 0===a?void 0:a.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;return t instanceof Set&&t.has(e)}}).call(this,n(28))},58:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return r.createElement("svg",a({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),i,o)}},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(39);function a(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(0),a=Object(r.createContext)(""),i=(a.Consumer,a.Provider);t.b=a},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a})),n.d(t,"e",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return u})),n.d(t,"O",(function(){return l})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return f})),n.d(t,"J",(function(){return g})),n.d(t,"I",(function(){return m})),n.d(t,"N",(function(){return p})),n.d(t,"f",(function(){return v})),n.d(t,"g",(function(){return b})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return y})),n.d(t,"l",(function(){return O})),n.d(t,"m",(function(){return k})),n.d(t,"n",(function(){return E})),n.d(t,"o",(function(){return _})),n.d(t,"q",(function(){return w})),n.d(t,"s",(function(){return j})),n.d(t,"r",(function(){return S})),n.d(t,"t",(function(){return N})),n.d(t,"w",(function(){return T})),n.d(t,"u",(function(){return x})),n.d(t,"v",(function(){return L})),n.d(t,"x",(function(){return D})),n.d(t,"y",(function(){return A})),n.d(t,"A",(function(){return C})),n.d(t,"B",(function(){return R})),n.d(t,"C",(function(){return P})),n.d(t,"D",(function(){return I})),n.d(t,"k",(function(){return M})),n.d(t,"F",(function(){return B})),n.d(t,"z",(function(){return z})),n.d(t,"G",(function(){return V})),n.d(t,"E",(function(){return H})),n.d(t,"i",(function(){return F})),n.d(t,"p",(function(){return W})),n.d(t,"Q",(function(){return G})),n.d(t,"P",(function(){return U}));var r="core/user",a="connected_url_mismatch",i="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",u="googlesitekit_setup",l="googlesitekit_view_dashboard",d="googlesitekit_manage_options",f="googlesitekit_read_shared_module_data",g="googlesitekit_manage_module_sharing_options",m="googlesitekit_delegate_module_sharing_management",p="googlesitekit_update_plugins",v="kmAnalyticsAdSenseTopEarningContent",b="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",y="kmAnalyticsNewVisitors",O="kmAnalyticsPopularAuthors",k="kmAnalyticsPopularContent",E="kmAnalyticsPopularProducts",_="kmAnalyticsReturningVisitors",w="kmAnalyticsTopCities",j="kmAnalyticsTopCitiesDrivingLeads",S="kmAnalyticsTopCitiesDrivingAddToCart",N="kmAnalyticsTopCitiesDrivingPurchases",T="kmAnalyticsTopDeviceDrivingPurchases",x="kmAnalyticsTopConvertingTrafficSource",L="kmAnalyticsTopCountries",D="kmAnalyticsTopPagesDrivingLeads",A="kmAnalyticsTopRecentTrendingPages",C="kmAnalyticsTopTrafficSource",R="kmAnalyticsTopTrafficSourceDrivingAddToCart",P="kmAnalyticsTopTrafficSourceDrivingLeads",I="kmAnalyticsTopTrafficSourceDrivingPurchases",M="kmAnalyticsPagesPerVisit",B="kmAnalyticsVisitLength",z="kmAnalyticsTopReturningVisitorPages",V="kmSearchConsolePopularKeywords",H="kmAnalyticsVisitsPerVisitor",F="kmAnalyticsMostEngagingPages",W="kmAnalyticsTopCategories",G=[v,b,h,y,O,k,E,_,W,w,j,S,N,T,x,L,A,C,R,M,B,z,H,F,W],U=[].concat(G,[V])},70:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},72:function(e,t,n){"use strict";var r=n(15),a=n.n(r),i=n(265),o=n(1),c=n.n(o),s=n(0),u=n(144);function Portal(e){var t=e.children,n=e.slug,r=Object(s.useState)(document.createElement("div")),o=a()(r,1)[0];return Object(i.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(u.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(33),a=n.n(r),i=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:i.a.sanitize(e,t)}};function c(e){var t,n="object"===a()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(1),a=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,a=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:a}},n)}IconWrapper.propTypes={children:a.a.node.isRequired,marginLeft:a.a.number,marginRight:a.a.number}}).call(this,n(4))},77:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(11),s=n.n(c),u=n(1),l=n.n(u),d=n(0),f=Object(d.forwardRef)((function(t,n){var r=t.label,i=t.className,c=t.hasLeftSpacing,u=void 0!==c&&c,l=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",a()({ref:n},l,{className:s()("googlesitekit-badge",i,{"googlesitekit-badge--has-left-spacing":u})}),r)}));f.displayName="Badge",f.propTypes={label:l.a.string.isRequired,hasLeftSpacing:l.a.bool},t.a=f}).call(this,n(4))},79:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(15),a=n.n(r),i=n(188),o=n(133),c={},s=void 0===e?null:e,u=function(){return[e.innerWidth,e.innerHeight]},l=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,l=void 0===r?0:r,d=e.initialHeight,f=void 0===d?0:d,g=Object(i.a)("undefined"==typeof document?[l,f]:u,t,n),m=a()(g,2),p=m[0],v=m[1],b=function(){return v(u)};return Object(o.a)(s,"resize",b),Object(o.a)(s,"orientationchange",b),p},d=function(e){return l(e)[0]}}).call(this,n(28))},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(106);function a(e){try{return new URL(e).pathname}catch(e){}return null}function i(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),a=e.replace(n.origin,"");if(a.length<t)return a;var i=a.length-Math.floor(t)+1;return"…"+a.substr(i)}},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return _})),n.d(t,"d",(function(){return w})),n.d(t,"e",(function(){return S})),n.d(t,"c",(function(){return N})),n.d(t,"b",(function(){return T}));var r=n(15),a=n.n(r),i=n(33),o=n.n(i),c=n(6),s=n.n(c),u=n(25),l=n.n(u),d=n(14),f=n(63),g=n.n(f),m=n(2);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var b=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=O(e,t),r=n.formatUnit,a=n.formatDecimal;try{return r()}catch(e){return a()}},h=function(e){var t=y(e),n=t.hours,r=t.minutes,a=t.seconds;return a=("0"+a).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(a):"".concat(n,":").concat(r,":").concat(a)},y=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},O=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=y(e),r=n.hours,a=n.minutes,i=n.seconds;return{hours:r,minutes:a,seconds:i,formatUnit:function(){var n=t.unitDisplay,o=v(v({unitDisplay:void 0===n?"short":n},l()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?S(i,v(v({},o),{},{unit:"second"})):Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?S(i,v(v({},o),{},{unit:"second"})):"",a?S(a,v(v({},o),{},{unit:"minute"})):"",r?S(r,v(v({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(m.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(m.__)("%ds","google-site-kit"),i);if(0===e)return t;var n=Object(m.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(m.__)("%dm","google-site-kit"),a),o=Object(m.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(m.__)("%dh","google-site-kit"),r);return Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?t:"",a?n:"",r?o:"").trim()}}},k=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},E=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in millions.
Object(m.__)("%sM","google-site-kit"),S(k(e),e%10==0?{}:t)):1e4<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),S(k(e))):1e3<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),S(k(e),e%10==0?{}:t)):S(e,{signDisplay:"never",maximumFractionDigits:1})};function _(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=v({},e)),t}function w(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=_(t),r=n.style,a=void 0===r?"metric":r;return"metric"===a?E(e):"duration"===a?b(e,n):"durationISO"===a?h(e):S(e,n)}var j=g()(console.warn),S=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?T():n,i=l()(t,["locale"]);try{return new Intl.NumberFormat(r,i).format(e)}catch(t){j("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(i)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],u={},d=0,f=Object.entries(i);d<f.length;d++){var g=a()(f[d],2),m=g[0],p=g[1];c[m]&&p===c[m]||(s.includes(m)||(u[m]=p))}try{return new Intl.NumberFormat(r,u).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},N=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?T():n,a=t.style,i=void 0===a?"long":a,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(r,{style:i,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var u=Object(m.__)(", ","google-site-kit");return e.join(u)},T=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a}));var r=n(149),a=n.n(r)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i);function ChangeArrow(t){var n=t.direction,r=t.invertColor,a=t.width,i=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:a,height:i,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:a.a.string,invertColor:a.a.bool,width:a.a.number,height:a.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},86:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={EXTERNAL:"external",INTERNAL:"internal"}},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(12),a=n.n(r),i=function(e,t){var n=t.dateRangeLength;a()(Array.isArray(e),"report must be an array to partition."),a()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return a.b})),n.d(t,"J",(function(){return a.c})),n.d(t,"F",(function(){return i.a})),n.d(t,"K",(function(){return i.b})),n.d(t,"H",(function(){return l})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return p})),n.d(t,"j",(function(){return v})),n.d(t,"i",(function(){return b})),n.d(t,"d",(function(){return _})),n.d(t,"c",(function(){return w})),n.d(t,"e",(function(){return j})),n.d(t,"b",(function(){return S})),n.d(t,"a",(function(){return N})),n.d(t,"f",(function(){return T})),n.d(t,"n",(function(){return x})),n.d(t,"w",(function(){return L})),n.d(t,"p",(function(){return D})),n.d(t,"G",(function(){return A})),n.d(t,"s",(function(){return C})),n.d(t,"v",(function(){return R})),n.d(t,"k",(function(){return P})),n.d(t,"o",(function(){return I.b})),n.d(t,"h",(function(){return I.a})),n.d(t,"t",(function(){return M.b})),n.d(t,"q",(function(){return M.a})),n.d(t,"A",(function(){return M.c})),n.d(t,"x",(function(){return B})),n.d(t,"u",(function(){return z})),n.d(t,"E",(function(){return F})),n.d(t,"D",(function(){return W.a})),n.d(t,"g",(function(){return G})),n.d(t,"L",(function(){return U})),n.d(t,"l",(function(){return q}));var r=n(14),a=n(36),i=n(75),o=n(33),c=n.n(o),s=n(96),u=n.n(s),l=function(e){return u()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var a=t[r];a&&"object"===c()(a)&&!Array.isArray(a)&&(a=e(a)),n[r]=a})),n}(e)))};n(97);var d=n(83);function f(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function g(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function m(e){return e.replace(/\n/gi,"<br>")}function p(e){for(var t=e,n=0,r=[f,g,m];n<r.length;n++){t=(0,r[n])(t)}return t}var v=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},b=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(15),y=n.n(h),O=n(12),k=n.n(O),E=n(2),_="Invalid dateString parameter, it must be a string.",w='Invalid date range, it must be a string with the format "last-x-days".',j=60,S=60*j,N=24*S,T=7*N;function x(){var e=function(e){return Object(E.sprintf)(
/* translators: %s: number of days */
Object(E._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function L(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function D(e){k()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function A(e){k()(L(e),_);var t=e.split("-"),n=y()(t,3),r=n[0],a=n[1],i=n[2];return new Date(r,a-1,i)}function C(e,t){return D(P(e,t*N))}function R(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function P(e,t){k()(L(e)||Object(r.isDate)(e)&&!isNaN(e),_);var n=L(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var I=n(98),M=n(80);function B(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function z(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var V=n(27),H=n.n(V),F=function(e){return Array.isArray(e)?H()(e).sort():e},W=n(89);function G(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var U=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},q=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},90:function(e,t,n){"use strict";(function(e,r){n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return O})),n.d(t,"a",(function(){return TourTooltips}));var a=n(6),i=n.n(a),o=n(81),c=n(30),s=n(1),u=n.n(s),l=n(2),d=n(3),f=n(23),g=n(7),m=n(36),p=n(107),v=n(18);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var h={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},y={back:Object(l.__)("Back","google-site-kit"),close:Object(l.__)("Close","google-site-kit"),last:Object(l.__)("Got it","google-site-kit"),next:Object(l.__)("Next","google-site-kit")},O={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},k="feature_tooltip_view",E="feature_tooltip_advance",_="feature_tooltip_return",w="feature_tooltip_dismiss",j="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,a=t.tourID,s=t.gaEventCategory,u=t.callback,l="".concat(a,"-step"),S="".concat(a,"-run"),N=Object(d.useDispatch)(f.b).setValue,T=Object(d.useDispatch)(g.a).dismissTour,x=Object(d.useRegistry)(),L=Object(v.a)(),D=Object(d.useSelect)((function(e){return e(f.b).getValue(l)})),A=Object(d.useSelect)((function(e){return e(f.b).getValue(S)&&!1===e(g.a).isTourDismissed(a)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(a)),N(S,!0)}));var C=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return r.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,r=e.lifecycle,a=e.size,i=e.status,o=e.type,u=t+1,l="function"==typeof s?s(L):s;o===c.b.TOOLTIP&&r===c.c.TOOLTIP?Object(m.b)(l,k,u):n===c.a.CLOSE&&r===c.c.COMPLETE?Object(m.b)(l,w,u):n===c.a.NEXT&&i===c.d.FINISHED&&o===c.b.TOUR_END&&a===u&&Object(m.b)(l,j,u),r===c.c.COMPLETE&&i!==c.d.FINISHED&&(n===c.a.PREV&&Object(m.b)(l,_,u),n===c.a.NEXT&&Object(m.b)(l,E,u))}(t);var n=t.action,r=t.index,i=t.status,o=t.step,d=t.type,f=n===c.a.CLOSE,g=!f&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),p=[c.d.FINISHED,c.d.SKIPPED].includes(i),v=f&&d===c.b.STEP_AFTER,b=p||v;if(c.b.STEP_BEFORE===d){var h,y,O=o.target;"string"==typeof o.target&&(O=e.document.querySelector(o.target)),null===(h=O)||void 0===h||null===(y=h.scrollIntoView)||void 0===y||y.call(h,{block:"center"})}g?function(e,t){N(l,e+(t===c.a.PREV?-1:1))}(r,n):b&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(a)),T(a)),u&&u(t,x)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:O,locale:y,run:A,showProgress:!0,stepIndex:D,steps:C,styles:h,tooltipComponent:p.a})}TourTooltips.propTypes={steps:u.a.arrayOf(u.a.object).isRequired,tourID:u.a.string.isRequired,gaEventCategory:u.a.oneOfType([u.a.string,u.a.func]).isRequired,callback:u.a.func}}).call(this,n(28),n(4))},93:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(24),a=n(130);function i(t,n){var r=document.querySelector(t);if(!r)return 0;var a=r.getBoundingClientRect().top,i=o(n);return a+e.scrollY-i}function o(e){var t=c(e),n=document.querySelectorAll(".googlesitekit-navigation, .googlesitekit-entity-header");return t+=Array.from(n).reduce((function(e,t){return e+t.offsetHeight}),0)}function c(t){var n=0,i=document.querySelector(".googlesitekit-header");return n=!!i&&"sticky"===e.getComputedStyle(i).position?function(e){var t=document.querySelector(".googlesitekit-header");if(t){if(e===r.b)return t.offsetHeight;var n=t.getBoundingClientRect().bottom;return n<0?0:n}return 0}(t):function(e){var t=document.querySelector("#wpadminbar");return t&&e!==r.b?t.offsetHeight:0}(t),(n=Object(a.a)(n))<0?0:n}}).call(this,n(28))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o}));var r=n(239),a=n(85),i=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var i=n.invertColor,o=void 0!==i&&i;return Object(r.a)(e.createElement(a.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(6),a=n.n(r),i=n(14),o=n(100),c=n(101);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=u(u({},l),t);a.referenceSiteURL&&(a.referenceSiteURL=a.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(a,n),d=Object(c.a)(a,n,s,r),f={},g=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);f[r]||(f[r]=Object(i.once)(d)),f[r].apply(f,t)};return{enableTracking:function(){a.trackingEnabled=!0},disableTracking:function(){a.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!a.trackingEnabled},trackEvent:d,trackEventOnce:g}}}).call(this,n(28))}},[[1283,1,0]]]);