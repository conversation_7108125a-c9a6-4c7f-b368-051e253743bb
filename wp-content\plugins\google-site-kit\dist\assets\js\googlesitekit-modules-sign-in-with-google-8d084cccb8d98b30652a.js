(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[24],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(59),i=n(39),a=n(57);function o(t,n){var o,c=Object(r.a)(n),s=t.activeModules,l=t.referenceSiteURL,u=t.userIDHash,g=t.userRoles,d=void 0===g?[]:g,f=t.isAuthenticated,m=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(i.b,"]"))),!o){o=!0;var r=(null==d?void 0:d.length)?d.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:l,plugin_version:m||"",enabled_features:Array.from(a.a).join(","),active_modules:s.join(","),authenticated:f?"1":"0",user_properties:{user_roles:r,user_identifier:u}});var g=n.createElement("script");return g.setAttribute(i.b,""),g.async=!0,g.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a),n.head.appendChild(g),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a)}}}}}).call(this,n(28))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function d(e,t,n,r){var a=Object(l.a)(t);return function(){var t=s()(i.a.mark((function t(o,c,s,l){var u;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:s,value:l},t.abrupt("return",new Promise((function(e){var t,n,i=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(i),e()};a("event",c,g(g({},u),{},{event_callback:s})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,i){return t.apply(this,arguments)}}()}},102:function(e,t,n){"use strict";var r=n(123);n.d(t,"a",(function(){return r.a}));var i=n(124);n.d(t,"c",(function(){return i.a}));var a=n(125);n.d(t,"b",(function(){return a.a}))},1090:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupMain}));var r=n(2),i=n(3),a=n(13),o=n(788),c=n(1091),s=n(77),l=n(1092),u=n(603);function SetupMain(){var t=Object(i.useSelect)((function(e){return e(a.c).getHomeURL()}));return e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--with-panels googlesitekit-setup-module--sign-in-with-google"},e.createElement("div",{className:"googlesitekit-setup-module__step"},e.createElement("div",{className:"googlesitekit-setup-module__logo"},e.createElement(o.a,{width:"40",height:"40"})),e.createElement("h2",{className:"googlesitekit-heading-3 googlesitekit-setup-module__title"},Object(r._x)("Sign in with Google","Service name","google-site-kit"),e.createElement(s.a,{className:"googlesitekit-badge--beta",label:Object(r.__)("Beta","google-site-kit")}))),e.createElement("div",{className:"googlesitekit-setup-module__step"},e.createElement(l.a,{moduleSlug:"sign-in-with-google"}),void 0!==t&&Object(u.a)(t)&&e.createElement(c.a,null)))}}).call(this,n(4))},1091:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return SetupForm}));var i=n(5),a=n.n(i),o=n(16),c=n.n(o),s=n(15),l=n.n(s),u=n(81),g=n(0),d=n(38),f=n(2),m=n(3),p=n(141),v=n(13),b=n(110),h=n(760),O=n(10),y=n(20),_=n(70),k=n(44),E=n(151),j=Object(g.lazy)((function(){return n.e(42).then(n.bind(null,1305))}));function SetupForm(){var t=Object(m.useRegistry)(),n=Object(g.useState)(),i=l()(n,2),o=i[0],s=i[1],S=Object(m.useSelect)((function(e){return e(v.c).getDocumentationLinkURL("sign-in-with-google")})),w=Object(m.useSelect)((function(e){return e(b.b).getServiceClientIDProvisioningURL()}));return Object(u.a)(c()(a.a.mark((function n(){var r,i;return a.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,t.resolveSelect(b.b).getSettings();case 2:""===t.select(b.b).getClientID()&&(null===(r=e._googlesitekitModulesData)||void 0===r||null===(i=r["sign-in-with-google"])||void 0===i?void 0:i.existingClientID)&&s(e._googlesitekitModulesData["sign-in-with-google"].existingClientID);case 4:case"end":return n.stop()}}),n)})))),r.createElement("div",{className:"googlesitekit-sign-in-with-google-setup__form"},r.createElement("div",{className:"googlesitekit-setup-module__panel-item"},r.createElement(p.a,{moduleSlug:b.b,storeName:b.b}),r.createElement("p",{className:"googlesitekit-setup-module__step-description"},Object(d.a)(Object(f.sprintf)(
/* translators: %1$s: Sign in with Google service name */
Object(f.__)("To set up %1$s, Site Kit will help you create an “OAuth Client ID“ that will be used to enable %1$s on your website. You will be directed to a page that will allow you to generate an “OAuth Client ID“. <a>Learn more</a>","google-site-kit"),Object(f._x)("Sign in with Google","Service name","google-site-kit")),{a:r.createElement(y.a,{href:S,external:!0})})),r.createElement("p",{className:"googlesitekit-margin-bottom-0"},Object(f.__)("Add your client ID here to complete setup:","google-site-kit")),r.createElement("div",{className:"googlesitekit-setup-module__inputs"},r.createElement(h.a,{existingClientID:o})),r.createElement(O.Button,{className:"googlesitekit-sign-in-with-google-client-id-cta",href:w,target:"_blank",trailingIcon:r.createElement(_.a,{width:"15",height:"15"}),inverse:!0},Object(f.__)("Get your client ID","google-site-kit"))),r.createElement("div",{className:"googlesitekit-setup-module__panel-item googlesitekit-setup-module__panel-item--with-svg"},r.createElement(g.Suspense,{fallback:r.createElement(k.a,{width:"100%",height:"235px"})},r.createElement(E.a,{errorMessage:Object(f.__)("Failed to load graphic","google-site-kit")},r.createElement(j,null)))))}}).call(this,n(28),n(4))},1092:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return HTTPSWarning}));var r=n(1),i=n.n(r),a=n(2),o=n(3),c=n(19),s=n(13),l=n(217),u=n(603);function HTTPSWarning(t){var n=t.moduleSlug,r=t.className,i=Object(o.useSelect)((function(e){return e(c.a).getModule(n)})),g=Object(o.useSelect)((function(e){return e(s.c).getHomeURL()}));if(!(null==i?void 0:i.name)||void 0!==g&&Object(u.a)(g))return null;var d=i.name;return e.createElement(l.a,{className:r},Object(a.sprintf)(
/* translators: %s: Module name. */
Object(a.__)("The site should use HTTPS to set up %s","google-site-kit"),d,Object(a.__)("Get help","google-site-kit")))}HTTPSWarning.propTypes={className:i.a.string,moduleSlug:i.a.string.isRequired}}).call(this,n(4))},1093:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsEdit}));var r=n(3),i=n(10),a=n(110),o=n(1094);function SettingsEdit(){var t=Object(r.useSelect)((function(e){return e(a.b).isDoingSubmitChanges()}));return e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--sign-in-with-google"},t?e.createElement(i.ProgressBar,null):e.createElement(o.a,null))}}).call(this,n(4))},1094:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsForm}));var r=n(518),i=n(110),a=n(141),o=n(17);function SettingsForm(){return e.createElement("div",{className:"googlesitekit-sign-in-with-google-settings-fields"},e.createElement(a.a,{moduleSlug:"sign-in-with-google",storeName:i.b}),e.createElement(o.e,null,e.createElement(o.k,null,e.createElement(o.a,{size:8},e.createElement(o.e,{className:"googlesitekit-sign-in-with-google-settings-fields__stretch-form"},e.createElement(o.k,null,e.createElement(o.a,{size:12},e.createElement(r.e,null))),e.createElement(o.k,null,e.createElement(o.a,{size:4},e.createElement(r.c,null)),e.createElement(o.a,{size:4},e.createElement(r.d,null)),e.createElement(o.a,{size:4},e.createElement(r.b,null))))),e.createElement(o.a,{size:4,className:"googlesitekit-sign-in-with-google-settings-fields__button-preview"},e.createElement(o.e,null,e.createElement(o.k,null,e.createElement(o.a,{size:12},e.createElement(r.g,null)))))),e.createElement(o.k,null,e.createElement(o.a,{size:12},e.createElement(r.f,null)),e.createElement(o.a,{size:12},e.createElement(r.a,null),e.createElement(r.h,null)))))}}).call(this,n(4))},1095:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsView}));var r=n(2),i=n(3),a=n(13),o=n(110),c=n(518),s=n(141),l=n(182);function SettingsView(){var t=Object(i.useSelect)((function(e){return e(o.b).getClientID()})),n=Object(i.useSelect)((function(e){return e(a.c).getAnyoneCanRegister()})),u=Object(i.useSelect)((function(e){var t,n=e(o.b).getShape();return null===(t=o.c.find((function(e){return e.value===n})))||void 0===t?void 0:t.label})),g=Object(i.useSelect)((function(e){var t,n=e(o.b).getText();return null===(t=o.d.find((function(e){return e.value===n})))||void 0===t?void 0:t.label})),d=Object(i.useSelect)((function(e){var t,n=e(o.b).getTheme();return null===(t=o.e.find((function(e){return e.value===n})))||void 0===t?void 0:t.label})),f=Object(i.useSelect)((function(e){return e(o.b).getOneTapEnabled()})),m=Object(i.useSelect)((function(e){return e(o.b).getOneTapOnAllPages()}));return t?e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--sign-in-with-google"},e.createElement(s.a,{moduleSlug:"sign-in-with-google",storeName:o.b}),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Client ID","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(l.b,{value:t})))),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Button text","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(l.b,{value:g}))),e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Button theme","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(l.b,{value:d}))),e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Button shape","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(l.b,{value:u})))),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("One Tap sign in","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},!f&&e.createElement(l.b,{value:Object(r.__)("Disabled","google-site-kit")}),!!f&&e.createElement(l.b,{value:m?Object(r.__)("Enabled (on all pages)","google-site-kit"):Object(r.__)("Enabled (login pages only)","google-site-kit")})))),e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("User registration","google-site-kit")),void 0!==n&&e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(l.b,{value:n?Object(r.__)("Enabled","google-site-kit"):Object(r.__)("Disabled","google-site-kit")})))),e.createElement(c.h,{className:"googlesitekit-margin-top-0"})):null}}).call(this,n(4))},1096:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SignInWithGoogleSetupCTABanner}));var r=n(1),i=n.n(r),a=n(81),o=n(2),c=n(3),s=n(13),l=n(7),u=n(159),g=n(242),d=n(111),f=n(180),m=n(179),p=n(1097);function SignInWithGoogleSetupCTABanner(t){var n=t.id,r=t.Notification,i=Object(c.useSelect)((function(e){return e(s.c).getDocumentationLinkURL("sign-in-with-google")})),v=Object(c.useDispatch)(l.a).triggerSurvey;Object(a.a)((function(){v("view_siwg_setup_cta")}));var b=Object(u.a)("sign-in-with-google");return e.createElement(r,null,e.createElement(g.a,{id:n,title:Object(o.sprintf)(
/* translators: %s: Sign in with Google service name */
Object(o.__)("Boost onboarding, security, and trust on your site using %s","google-site-kit"),Object(o._x)("Sign in with Google","Service name","google-site-kit")),description:e.createElement(d.a,{className:"googlesitekit-setup-cta-banner__description",text:Object(o.sprintf)(
/* translators: %s: Sign in with Google service name */
Object(o.__)("Provide your site visitors with a simple, secure, and personalised experience by adding a %s button to your login page.","google-site-kit"),Object(o._x)("Sign in with Google","Service name","google-site-kit")),learnMoreLink:e.createElement(f.a,{id:n,label:Object(o.__)("Learn more","google-site-kit"),url:i})}),actions:e.createElement(m.a,{id:n,className:"googlesitekit-setup-cta-banner__actions-wrapper",ctaLabel:Object(o.sprintf)(
/* translators: %s: Sign in with Google service name */
Object(o.__)("Set up %s","google-site-kit"),Object(o._x)("Sign in with Google","Service name","google-site-kit")),onCTAClick:b,dismissLabel:Object(o.__)("Maybe later","google-site-kit")}),SVG:p.a}))}SignInWithGoogleSetupCTABanner.propTypes={id:i.a.string,Notification:i.a.elementType}}).call(this,n(4))},1097:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M448.71 220.432c-39.44 65.437-108.589 70.346-142.701 62.23-44.503-10.589-50.116-42.082-74.692-50.93-24.577-8.847-35.882-5.29-70.79 0-34.907 5.29-93.676-1.62-119.279-54.354-25.602-52.734-5.92-107.518 42.22-134.123 48.14-26.605 76.356 1.837 123.278 2.74 46.921.902 59.733-22.909 103.458-25.334 97.588-5.414 198.687 99.921 138.506 199.771z",fill:"#B8E6CA"}),o=r.createElement("g",{filter:"url(#sign-in-with-google-setup-cta_svg__filter0_d_9_1090)"},r.createElement("path",{d:"M148.363 83.825c0-7.602 6.162-13.764 13.764-13.764h167.472c7.602 0 13.764 6.162 13.764 13.764v144.472c0 7.602-6.162 13.764-13.764 13.764H162.127c-7.602 0-13.764-6.162-13.764-13.764V83.825z",fill:"#fff"})),c=r.createElement("path",{d:"M163.363 101.061a4 4 0 014-4h157a4 4 0 014 4v38a4 4 0 01-4 4h-157a4 4 0 01-4-4v-38zM163.363 206.061a4 4 0 014-4h157a4 4 0 014 4v5a4 4 0 01-4 4h-157a4 4 0 01-4-4v-5z",fill:"#BED4FF"}),s=r.createElement("rect",{x:234.363,y:153.061,width:54,height:14,rx:7,fill:"#BED4FF"}),l=r.createElement("rect",{x:234.363,y:175.061,width:94,height:5,rx:2.5,fill:"#BED4FF"}),u=r.createElement("rect",{x:163.363,y:175.061,width:59,height:5,rx:2.5,fill:"#BED4FF"}),g=r.createElement("rect",{x:163.363,y:164.061,width:59,height:5,rx:2.5,fill:"#BED4FF"}),d=r.createElement("rect",{x:163.363,y:186.061,width:59,height:5,rx:2.5,fill:"#BED4FF"}),f=r.createElement("rect",{x:163.363,y:153.061,width:59,height:5,rx:2.5,fill:"#BED4FF"}),m=r.createElement("rect",{x:234.363,y:186.061,width:94,height:5,rx:2.5,fill:"#BED4FF"}),p=r.createElement("path",{d:"M339.892 43.46c-5.813 2.537-18.468 7.787-30.492 10.97-3.489.923-6.059 4.031-5.912 7.637 2.109 51.839 30.311 73.97 39.674 73.97 9.354 0 37.92-26.219 38.536-74.3.044-3.458-2.429-6.392-5.764-7.312-12-3.309-23.943-8.408-29.518-10.92a8.04 8.04 0 00-6.524-.044z",fill:"#3C7251"}),v=r.createElement("path",{d:"M340.009 43.46c-5.814 2.537-18.468 7.787-30.492 10.97-3.489.923-6.059 4.031-5.912 7.637 2.109 51.839 30.311 73.97 39.674 73.97 9.353 0 37.919-26.219 38.536-74.3.044-3.458-2.429-6.392-5.764-7.312-12-3.309-23.943-8.408-29.519-10.92a8.037 8.037 0 00-6.523-.044z",fill:"#5C9271"}),b=r.createElement("g",{mask:"url(#sign-in-with-google-setup-cta_svg__a)"},r.createElement("path",{d:"M343.148 42s-17.392 8.11-33.636 12.426c-3.487.926-6.054 4.033-5.908 7.637 2.101 51.842 30.212 73.974 39.544 73.974V42z",fill:"#265C3B"})),h=r.createElement("circle",{cx:342.998,cy:81.805,fill:"#D0FBE1",r:15.503}),O=r.createElement("circle",{opacity:.5,cx:342.697,cy:81.95,r:19.735,stroke:"#B8E6CA",strokeWidth:3.138}),y=r.createElement("circle",{cx:342.998,cy:81.805,fill:"#D0FBE1",r:15.503}),_=r.createElement("g",{mask:"url(#sign-in-with-google-setup-cta_svg__b)",fill:"#77AD8C"},r.createElement("path",{d:"M349.114 76.709c0 3.346-2.788 6.057-6.228 6.057s-6.228-2.712-6.228-6.057 2.788-6.057 6.228-6.057 6.228 2.712 6.228 6.057z"}),r.createElement("ellipse",{cx:342.886,cy:94.608,rx:13.235,ry:10.588})),k=r.createElement("defs",null,r.createElement("filter",{id:"sign-in-with-google-setup-cta_svg__filter0_d_9_1090",x:132.363,y:58.061,width:227,height:204,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:4}),r.createElement("feGaussianBlur",{stdDeviation:8}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_9_1090"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_9_1090",result:"shape"})),r.createElement("filter",{id:"sign-in-with-google-setup-cta_svg__filter1_d_9_1090",x:291.348,y:36.724,width:102.484,height:117.514,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:6.067}),r.createElement("feGaussianBlur",{stdDeviation:6.067}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0.0460389 0 0 0 0 0.348203 0 0 0 0 0.163547 0 0 0 0.25 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_9_1090"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_9_1090",result:"shape"})));t.a=function SvgSignInWithGoogleSetupCta(e){return r.createElement("svg",i({viewBox:"0 0 499 272",fill:"none"},e),a,o,c,s,l,u,g,d,f,m,r.createElement("g",{filter:"url(#sign-in-with-google-setup-cta_svg__filter1_d_9_1090)"},p,r.createElement("mask",{id:"sign-in-with-google-setup-cta_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:303,y:42,width:79,height:95},v),b,h,O,r.createElement("mask",{id:"sign-in-with-google-setup-cta_svg__b",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:327,y:66,width:32,height:32},y),_),k)}},1098:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupSuccessSubtleNotification}));var r=n(15),i=n.n(r),a=n(2),o=n(115),c=n(92),s=n(168),l=n(147),u=n(3),g=n(13);function SetupSuccessSubtleNotification(t){var n=t.id,r=t.Notification,d=Object(l.a)("notification"),f=i()(d,2)[1],m=Object(l.a)("slug"),p=i()(m,2)[1],v=Object(u.useSelect)((function(e){return e(g.c).getAdminURL("googlesitekit-settings")}));return e.createElement(r,null,e.createElement(o.a,{title:Object(a.sprintf)(
/* translators: %s: Sign in with Google service name */
Object(a.__)("You successfully set up %s!","google-site-kit"),Object(a._x)("Sign in with Google","Service name","google-site-kit")),description:Object(a.sprintf)(
/* translators: %s: Sign in with Google service name */
Object(a.__)("%s button was added to your site login page. You can customize the button appearance in settings.","google-site-kit"),Object(a._x)("Sign in with Google","Service name","google-site-kit")),dismissCTA:e.createElement(c.a,{id:n,primary:!1,dismissLabel:Object(a.__)("Maybe later","google-site-kit"),onDismiss:function(){f(void 0),p(void 0)}}),additionalCTA:e.createElement(s.a,{id:n,ctaLabel:Object(a.__)("Customize settings","google-site-kit"),ctaLink:"".concat(v,"#connected-services/sign-in-with-google")})}))}}).call(this,n(4))},1099:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(12),s=n.n(c),l=n(3),u=n(110);function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function f(e){return Object(l.createRegistrySelector)((function(t){return function(){return(t(u.b).getModuleData()||[])[e]}}))}var m={moduleData:{isWooCommerceActive:void 0,isWooCommerceRegistrationEnabled:void 0}},p={receiveModuleData:function(e){return s()(e,"moduleData is required."),{payload:e,type:"RECEIVE_MODULE_DATA"}}},v={getModuleData:i.a.mark((function t(){var n,r;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=null===(n=e._googlesitekitModulesData)||void 0===n?void 0:n["sign-in-with-google"]){t.next=3;break}return t.abrupt("return");case 3:return t.next=5,p.receiveModuleData(r);case 5:case"end":return t.stop()}}),t)}))},b={getModuleData:function(e){return e.moduleData},getIsWooCommerceActive:f("isWooCommerceActive"),getIsWooCommerceRegistrationEnabled:f("isWooCommerceRegistrationEnabled")};t.a={initialState:m,actions:p,controls:{},reducer:function(e,t){var n=t.payload;switch(t.type){case"RECEIVE_MODULE_DATA":var r={isWooCommerceActive:n.isWooCommerceActive,isWooCommerceRegistrationEnabled:n.isWooCommerceRegistrationEnabled};return d(d({},e),{},{moduleData:r});default:return e}},resolvers:v,selectors:b}}).call(this,n(28))},110:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c}));var r="modules/sign-in-with-google",i="non_https_site",a=[{value:"outline",label:"Light"},{value:"filled_blue",label:"Neutral"},{value:"filled_black",label:"Dark"}],o=[{value:"continue_with",label:"Continue with Google"},{value:"signin",label:"Sign in"},{value:"signin_with",label:"Sign in with Google"},{value:"signup_with",label:"Sign up with Google"}],c=[{value:"pill",label:"Pill"},{value:"rectangular",label:"Rectangular"}]},111:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var r=n(1),i=n.n(r),a=n(0),o=n(9),c=n(54);function Description(t){var n=t.className,r=void 0===n?"googlesitekit-publisher-win__desc":n,i=t.text,s=t.learnMoreLink,l=t.errorText,u=t.children;return e.createElement(a.Fragment,null,e.createElement("div",{className:r},e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.F)(i,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",s)),l&&e.createElement(c.a,{message:l}),u)}Description.propTypes={className:i.a.string,text:i.a.string,learnMoreLink:i.a.node,errorText:i.a.string,children:i.a.node}}).call(this,n(4))},115:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a),c=n(0),s=n(137),l=n(58),u=n(131),g=n(17),d=Object(c.forwardRef)((function(t,n){var r=t.className,i=t.title,a=t.description,c=t.dismissCTA,d=t.additionalCTA,f=t.reverseCTAs,m=void 0!==f&&f,p=t.type,v=void 0===p?"success":p,b=t.icon;return e.createElement(g.e,{ref:n},e.createElement(g.k,null,e.createElement(g.a,{alignMiddle:!0,size:12,className:o()("googlesitekit-subtle-notification",r,{"googlesitekit-subtle-notification--success":"success"===v,"googlesitekit-subtle-notification--warning":"warning"===v,"googlesitekit-subtle-notification--new-feature":"new-feature"===v})},e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},b,"success"===v&&!b&&e.createElement(s.a,{width:24,height:24}),"warning"===v&&!b&&e.createElement(l.a,{width:24,height:24}),"new-feature"===v&&!b&&e.createElement(u.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,i),e.createElement("p",{className:"googlesitekit-subtle-notification__secondary_description"},a)),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},!m&&c,m&&d,!m&&d,m&&c))))}));d.propTypes={className:i.a.string,title:i.a.node,description:i.a.node,dismissCTA:i.a.node,additionalCTA:i.a.node,reverseCTAs:i.a.bool,type:i.a.oneOf(["success","warning","new-feature"]),icon:i.a.object},t.a=d}).call(this,n(4))},120:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var r=n(1),i=n.n(r),a=n(0),o=n(2),c=n(3),s=n(10),l=n(35),u=n(54);function ErrorNotice(t){var n,r=t.error,i=t.hasButton,g=void 0!==i&&i,d=t.storeName,f=t.message,m=void 0===f?r.message:f,p=t.noPrefix,v=void 0!==p&&p,b=t.skipRetryMessage,h=t.Icon,O=Object(c.useDispatch)(),y=Object(c.useSelect)((function(e){return d?e(d).getSelectorDataForError(r):null})),_=Object(a.useCallback)((function(){O(y.storeName).invalidateResolution(y.name,y.args)}),[O,y]);if(!r||Object(l.f)(r))return null;var k=g&&Object(l.d)(r,y);return g||b||(m=Object(o.sprintf)(
/* translators: %1$s: Error message from Google API. */
Object(o.__)("%1$s%2$s Please try again.","google-site-kit"),m,m.endsWith(".")?"":".")),e.createElement(a.Fragment,null,h&&e.createElement("div",{className:"googlesitekit-error-notice__icon"},e.createElement(h,{width:"24",height:"24"})),e.createElement(u.a,{message:m,reconnectURL:null===(n=r.data)||void 0===n?void 0:n.reconnectURL,noPrefix:v}),k&&e.createElement(s.Button,{className:"googlesitekit-error-notice__retry-button",onClick:_},Object(o.__)("Retry","google-site-kit")))}ErrorNotice.propTypes={error:i.a.shape({message:i.a.string}),hasButton:i.a.bool,storeName:i.a.string,message:i.a.string,noPrefix:i.a.bool,skipRetryMessage:i.a.bool,Icon:i.a.elementType}}).call(this,n(4))},121:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(219),i=n(14),a=n(0);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=Object(r.b)((function(){return i.debounce.apply(void 0,t)}),t);return Object(a.useEffect)((function(){return function(){return o.cancel()}}),[o]),o}},123:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(21),i=n.n(r),a=n(6),o=n.n(a),c=n(25),s=n.n(c),l=n(1),u=n.n(l),g=n(11),d=n.n(g);function Cell(t){var n,r=t.className,a=t.alignTop,c=t.alignMiddle,l=t.alignBottom,u=t.alignRight,g=t.alignLeft,f=t.smAlignRight,m=t.mdAlignRight,p=t.lgAlignRight,v=t.smSize,b=t.smStart,h=t.smOrder,O=t.mdSize,y=t.mdStart,_=t.mdOrder,k=t.lgSize,E=t.lgStart,j=t.lgOrder,S=t.size,w=t.children,N=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",i()({},N,{className:d()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":l,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":g,"mdc-layout-grid__cell--align-right-phone":f,"mdc-layout-grid__cell--align-right-tablet":m,"mdc-layout-grid__cell--align-right-desktop":p},o()(n,"mdc-layout-grid__cell--span-".concat(S),12>=S&&S>0),o()(n,"mdc-layout-grid__cell--span-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--start-".concat(E,"-desktop"),12>=E&&E>0),o()(n,"mdc-layout-grid__cell--order-".concat(j,"-desktop"),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--start-".concat(y,"-tablet"),8>=y&&y>0),o()(n,"mdc-layout-grid__cell--order-".concat(_,"-tablet"),8>=_&&_>0),o()(n,"mdc-layout-grid__cell--span-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--start-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--order-".concat(h,"-phone"),4>=h&&h>0),n))}),w)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},124:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),g=n(0),d=Object(g.forwardRef)((function(t,n){var r=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",i()({ref:n,className:u()("mdc-layout-grid__inner",r)},c),a)}));d.displayName="Row",d.propTypes={className:s.a.string,children:s.a.node},d.defaultProps={className:""},t.a=d}).call(this,n(4))},125:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),g=n(0),d=Object(g.forwardRef)((function(t,n){var r=t.alignLeft,a=t.fill,c=t.className,s=t.children,l=t.collapsed,g=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",i()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":l,"mdc-layout-grid--fill":a})},g,{ref:n}),s)}));d.displayName="Grid",d.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},d.defaultProps={className:""},t.a=d}).call(this,n(4))},126:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},127:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},128:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},1298:function(e,t,n){"use strict";n.r(t);var r=n(3),i=n.n(r),a=n(190),o=n.n(a),c=n(363),s=n.n(c),l=n(5),u=n.n(l),g=n(16),d=n.n(g),f=n(2),m=n(409),p=n(13),v=n(19),b=n(110),h=n(788),O=n(1090),y=n(1093),_=n(1095),k=n(1096),E=n(41),j=n(22),S=n(1098),w=n(603),N=n(205),T=n(12),C=n.n(T),A=n(200),x=n(62),D=n(727);var R=o.a.createModuleStore("sign-in-with-google",{storeName:b.b,validateCanSubmitChanges:function(e){var t=Object(x.e)(e)(b.b),n=t.getClientID,r=t.getShape,i=t.getText,a=t.getTheme,o=t.haveSettingsChanged,c=t.isDoingSubmitChanges;C()(!c(),A.a),C()(o(),A.b);var s=n(),l=r(),u=i(),g=a();C()(null==s?void 0:s.length,"clientID is required"),C()(Object(D.a)(s),"a valid clientID is required to submit changes"),C()(!!b.c.find((function(e){return e.value===l})),"shape must be one of: ".concat(b.c.map((function(e){return e.value})).join(", "))),C()(!!b.d.find((function(e){return e.value===u})),"text must be one of: ".concat(b.d.map((function(e){return e.value})).join(", "))),C()(!!b.e.find((function(e){return e.value===g})),"theme must be one of: ".concat(b.e.map((function(e){return e.value})).join(", ")))},ownedSettingsSlugs:[],settingSlugs:["clientID","shape","text","theme","oneTapEnabled","oneTapOnAllPages"]}),L=n(157),P=n(7),I={selectors:{getServiceURL:Object(r.createRegistrySelector)((function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.path,i=n.query,a="https://developers.google.com/identity/site-kit";if(i&&(a=Object(L.a)(a,i)),r){var o="/".concat(r.replace(/^\//,""));a="".concat(a,"#").concat(o)}var c=e(P.a).getAccountChooserURL(a);if(void 0!==c)return c}})),getServiceClientIDProvisioningURL:Object(r.createRegistrySelector)((function(e){return function(){var t=e(p.c).getSiteName(),n=e(p.c).getHomeURL(),r={appname:t,sitename:t,siteorigin:n?new URL(n).origin:n};return e(b.b).getServiceURL({query:r})}}))}},M=n(1099),B=Object(r.combineStores)(R,I,M.a);B.initialState,B.actions,B.controls,B.reducer,B.resolvers,B.selectors;var F,z,U,H;o.a.registerModule("sign-in-with-google",{storeName:b.b,SettingsEditComponent:y.a,SettingsViewComponent:_.a,SetupComponent:O.a,onCompleteSetup:(z=d()(u.a.mark((function e(t,n){var r,i;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.dispatch(b.b),i=r.submitChanges,e.next=3,i();case 3:e.sent.error||n();case 5:case"end":return e.stop()}}),e)}))),function(e,t){return z.apply(this,arguments)}),Icon:h.a,features:[Object(f.__)("Users will no longer be able to sign in to your WordPress site using their Google Accounts","google-site-kit"),Object(f.__)("Users will not be able to create an account on your site using their Google Account (if account creation is enabled)","google-site-kit"),Object(f.__)("Existing users who have only used Sign in with Google to sign in to your site will need to use WordPress’ “Reset my password” to set a password for their account","google-site-kit")],overrideSetupSuccessNotification:!0,checkRequirements:(F=d()(u.a.mark((function e(t){var n;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.resolveSelect(p.c).getSiteInfo();case 2:if(n=t.select(p.c).getHomeURL(),!Object(w.a)(n)){e.next=5;break}return e.abrupt("return");case 5:throw{code:b.a,message:Object(f.__)("The site should use HTTPS to set up Sign in with Google","google-site-kit"),data:null};case 6:case"end":return e.stop()}}),e)}))),function(e){return F.apply(this,arguments)})}),i.a.registerStore(b.b,B),(U=s.a).registerNotification("sign-in-with-google-setup-cta",{Component:k.a,priority:N.c.SETUP_CTA_LOW,areaSlug:E.b.BANNERS_BELOW_NAV,groupID:E.c.SETUP_CTAS,viewContexts:[j.n],checkRequirements:(H=d()(u.a.mark((function e(t){var n,r,i;return u.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.select,r=t.resolveSelect,e.next=3,Promise.all([r(v.a).getModules(),r(p.c).getSiteInfo()]);case 3:if(!n(v.a).isModuleConnected("sign-in-with-google")){e.next=6;break}return e.abrupt("return",!1);case 6:if(i=n(p.c).getHomeURL(),Object(w.a)(i)){e.next=9;break}return e.abrupt("return",!1);case 9:return e.abrupt("return",!0);case 10:case"end":return e.stop()}}),e)}))),function(e){return H.apply(this,arguments)}),isDismissible:!0}),U.registerNotification("setup-success-notification-siwg",{Component:S.a,areaSlug:E.b.BANNERS_BELOW_NAV,viewContexts:[j.n],checkRequirements:function(){var e=Object(m.a)(location.href,"notification"),t=Object(m.a)(location.href,"slug");return"authentication_success"===e&&"sign-in-with-google"===t}})},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var r="core/site",i="primary",a="secondary"},131:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},137:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},141:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return StoreErrorNotices}));var r=n(1),i=n.n(r),a=n(3),o=n(120),c=n(19),s=n(35),l=n(169);function StoreErrorNotices(t){var n=t.hasButton,r=void 0!==n&&n,i=t.moduleSlug,u=t.storeName,g=Object(a.useSelect)((function(e){return e(u).getErrors()})),d=Object(a.useSelect)((function(e){return e(c.a).getModule(i)})),f=[];return g.filter((function(e){return!(!(null==e?void 0:e.message)||f.includes(e.message))&&(f.push(e.message),!0)})).map((function(t,n){var i=t.message;return Object(s.e)(t)&&(i=Object(l.a)(i,d)),e.createElement(o.a,{key:n,error:t,hasButton:r,storeName:u,message:i})}))}StoreErrorNotices.propTypes={hasButton:i.a.bool,storeName:i.a.string.isRequired,moduleSlug:i.a.string}}).call(this,n(4))},142:function(e,t,n){"use strict";var r=n(166);n.d(t,"c",(function(){return r.a}));var i=n(65);n.d(t,"b",(function(){return i.c})),n.d(t,"a",(function(){return i.a}))},147:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(15),o=n.n(a),c=n(0),s=n(409),l=n(157);t.a=function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=Object(c.useState)(Object(s.a)(r.location.href,t)||n),u=o()(a,2),g=u[0],d=u[1],f=function(e){d(e);var n=Object(l.a)(r.location.href,i()({},t,e));r.history.replaceState(null,"",n)};return[g,f]}}).call(this,n(28))},151:function(e,t,n){"use strict";(function(e,r){var i=n(51),a=n.n(i),o=n(53),c=n.n(o),s=n(68),l=n.n(s),u=n(69),g=n.n(u),d=n(49),f=n.n(d),m=n(1),p=n.n(m),v=n(0),b=n(2),h=n(54);function O(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var i=f()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return g()(this,n)}}var y=function(t){l()(MediaErrorHandler,t);var n=O(MediaErrorHandler);function MediaErrorHandler(e){var t;return a()(this,MediaErrorHandler),(t=n.call(this,e)).state={error:null},t}return c()(MediaErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t})}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.errorMessage;return this.state.error?r.createElement(h.a,{message:n}):t}}]),MediaErrorHandler}(v.Component);y.defaultProps={errorMessage:Object(b.__)("Failed to load media","google-site-kit")},y.propTypes={children:p.a.node.isRequired,errorMessage:p.a.string.isRequired},t.a=y}).call(this,n(28),n(4))},159:function(e,t,n){"use strict";n.d(t,"a",(function(){return v}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(0),s=n(3),l=n(13),u=n(7),g=n(19),d=n(32),f=n(37),m=n(36),p=n(18);function v(e){var t=Object(p.a)(),n=Object(s.useSelect)((function(t){return t(g.a).getModule(e)})),r=Object(s.useSelect)((function(e){return e(u.a).hasCapability(u.K)})),a=Object(s.useDispatch)(g.a).activateModule,v=Object(s.useDispatch)(d.a).navigateTo,b=Object(s.useDispatch)(l.c).setInternalServerError,h=Object(c.useCallback)(o()(i.a.mark((function n(){var r,o,c;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,a(e);case 2:if(r=n.sent,o=r.error,c=r.response,o){n.next=13;break}return n.next=8,Object(m.b)("".concat(t,"_widget-activation-cta"),"activate_module",e);case 8:return n.next=10,Object(f.f)("module_setup",e,{ttl:300});case 10:v(c.moduleReauthURL),n.next=14;break;case 13:b({id:"".concat(e,"-setup-error"),description:o.message});case 14:case"end":return n.stop()}}),n)}))),[a,e,v,b,t]);return(null==n?void 0:n.name)&&r?h:null}},166:function(e,t,n){"use strict";(function(e){var r=n(11),i=n.n(r),a=n(1),o=n.n(a),c=n(2),s=n(3),l=n(201),u=n(210),g=n(65),d=n(7),f=n(10),m=n(0),p=Object(m.forwardRef)((function(t,n){var r=t.className,a=t.children,o=t.type,m=t.dismiss,p=void 0===m?"":m,v=t.dismissCallback,b=t.dismissLabel,h=void 0===b?Object(c.__)("OK, Got it!","google-site-kit"):b,O=t.Icon,y=void 0===O?Object(g.d)(o):O,_=t.OuterCTA,k=Object(s.useDispatch)(d.a).dismissItem,E=Object(s.useSelect)((function(e){return p?e(d.a).isItemDismissed(p):void 0}));if(p&&E)return null;var j=a?u.a:l.a;return e.createElement("div",{ref:n,className:i()(r,"googlesitekit-settings-notice","googlesitekit-settings-notice--".concat(o),{"googlesitekit-settings-notice--single-row":!a,"googlesitekit-settings-notice--multi-row":a})},e.createElement("div",{className:"googlesitekit-settings-notice__icon"},e.createElement(y,{width:"20",height:"20"})),e.createElement("div",{className:"googlesitekit-settings-notice__body"},e.createElement(j,t)),p&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(f.Button,{tertiary:!0,onClick:function(){"string"==typeof p&&k(p),null==v||v()}},h)),_&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(_,null)))}));p.propTypes={className:o.a.string,children:o.a.node,notice:o.a.node.isRequired,type:o.a.oneOf([g.a,g.c,g.b]),Icon:o.a.elementType,LearnMore:o.a.elementType,CTA:o.a.elementType,OuterCTA:o.a.elementType,dismiss:o.a.string,dismissLabel:o.a.string,dismissCallback:o.a.func},p.defaultProps={type:g.a},t.a=p}).call(this,n(4))},168:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALinkSubtle}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=n(1),s=n.n(c),l=n(11),u=n.n(l),g=n(73),d=n(10),f=n(70);function CTALinkSubtle(t){var n=t.id,r=t.ctaLink,a=t.ctaLabel,c=t.onCTAClick,s=t.isCTALinkExternal,l=void 0!==s&&s,m=t.gaTrackingEventArgs,p=t.tertiary,v=void 0!==p&&p,b=t.isSaving,h=void 0!==b&&b,O=Object(g.a)(n,null==m?void 0:m.category),y=function(){var e=o()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==c?void 0:c(t);case 2:O.confirm(null==m?void 0:m.label,null==m?void 0:m.value);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(d.Button,{className:u()("googlesitekit-subtle-notification__cta",{"googlesitekit-subtle-notification__cta--spinner__running":h}),href:r,onClick:y,target:l?"_blank":"_self",trailingIcon:l?e.createElement(f.a,{width:14,height:14}):void 0,icon:h?e.createElement(d.CircularProgress,{size:14}):void 0,tertiary:v},a)}CTALinkSubtle.propTypes={id:s.a.string,ctaLink:s.a.string,ctaLabel:s.a.string,onCTAClick:s.a.func,isCTALinkExternal:s.a.bool,gaTrackingEventArgs:s.a.shape({label:s.a.string,value:s.a.string}),tertiary:s.a.bool,isSaving:s.a.bool}}).call(this,n(4))},169:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(2);function i(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},i=n.slug,a=void 0===i?"":i,o=n.name,c=void 0===o?"":o,s=n.owner,l=void 0===s?{}:s;if(!a||!c)return e;var u="",g="";return"analytics-4"===a?e.match(/account/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):"search-console"===a&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),u||(u=Object(r.sprintf)(
/* translators: %s: module name */
Object(r.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),c)),l&&l.login&&(g=Object(r.sprintf)(
/* translators: %s: owner name */
Object(r.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),l.login)),g||(g=Object(r.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(u," ").concat(g)}},17:function(e,t,n){"use strict";var r=n(254);n.d(t,"i",(function(){return r.a}));var i=n(319);n.d(t,"f",(function(){return i.a}));var a=n(320);n.d(t,"h",(function(){return a.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var s=n(91),l=n.n(s);n.d(t,"b",(function(){return l.a})),n.d(t,"c",(function(){return s.DialogContent})),n.d(t,"d",(function(){return s.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},179:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActionsCTALinkDismiss}));var r=n(1),i=n.n(r),a=n(2),o=n(0),c=n(3),s=n(32),l=n(92),u=n(187);function ActionsCTALinkDismiss(t){var n=t.id,r=t.className,i=void 0===r?"googlesitekit-publisher-win__actions":r,g=t.ctaLink,d=t.ctaLabel,f=t.ctaDisabled,m=void 0!==f&&f,p=t.onCTAClick,v=t.ctaDismissOptions,b=t.isSaving,h=void 0!==b&&b,O=t.onDismiss,y=void 0===O?function(){}:O,_=t.dismissLabel,k=void 0===_?Object(a.__)("OK, Got it!","google-site-kit"):_,E=t.dismissOnCTAClick,j=void 0===E||E,S=t.dismissExpires,w=void 0===S?0:S,N=t.dismissOptions,T=void 0===N?{}:N,C=t.gaTrackingEventArgs,A=void 0===C?{}:C,x=Object(c.useSelect)((function(e){return!!g&&e(s.a).isNavigatingTo(g)}));return e.createElement(o.Fragment,null,e.createElement("div",{className:i},e.createElement(u.a,{id:n,ctaLink:g,ctaLabel:d,onCTAClick:p,dismissOnCTAClick:j,dismissExpires:w,dismissOptions:v,gaTrackingEventArgs:A,isSaving:h,isDisabled:m}),e.createElement(l.a,{id:n,primary:!1,dismissLabel:k,dismissExpires:w,disabled:x,onDismiss:y,dismissOptions:T,gaTrackingEventArgs:A})))}ActionsCTALinkDismiss.propTypes={id:i.a.string,className:i.a.string,ctaDisabled:i.a.bool,ctaLink:i.a.string,ctaLabel:i.a.string,onCTAClick:i.a.func,isSaving:i.a.bool,onDismiss:i.a.func,ctaDismissOptions:i.a.object,dismissLabel:i.a.string,dismissOnCTAClick:i.a.bool,dismissExpires:i.a.number,dismissOptions:i.a.object,gaTrackingEventArgs:i.a.object}}).call(this,n(4))},18:function(e,t,n){"use strict";var r=n(0),i=n(61);t.a=function(){return Object(r.useContext)(i.b)}},180:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LearnMoreLink}));var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(1),s=n.n(c),l=n(20),u=n(73);function LearnMoreLink(t){var n=t.id,r=t.label,a=t.url,c=t.ariaLabel,s=t.gaTrackingEventArgs,g=t.external,d=void 0===g||g,f=o()(t,["id","label","url","ariaLabel","gaTrackingEventArgs","external"]),m=Object(u.a)(n);return e.createElement(l.a,i()({onClick:function(e){e.persist(),m.clickLearnMore(null==s?void 0:s.label,null==s?void 0:s.value)},href:a,"aria-label":c,external:d},f),r)}LearnMoreLink.propTypes={id:s.a.string,label:s.a.string,url:s.a.string,ariaLabel:s.a.string,gaTrackingEventArgs:s.a.shape({label:s.a.string,value:s.a.string}),external:s.a.bool}}).call(this,n(4))},182:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(1),i=n.n(r),a=" ";function DisplaySetting(e){return e.value||a}DisplaySetting.propTypes={value:i.a.oneOfType([i.a.string,i.a.bool,i.a.number])},t.b=DisplaySetting},186:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 0h2v7H0zm0 10h2v2H0z",fill:"currentColor",fillRule:"evenodd"});t.a=function SvgWarningIcon(e){return r.createElement("svg",i({viewBox:"0 0 2 12"},e),a)}},187:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALink}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(15),u=n.n(l),g=n(1),d=n.n(g),f=n(206),m=n(0),p=n(3),v=n(41),b=n(32),h=n(13),O=n(73),y=n(10);function _(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function CTALink(t){var n=t.id,r=t.ctaLink,a=t.ctaLabel,o=t.onCTAClick,c=t.isSaving,l=t.dismissOnCTAClick,g=void 0!==l&&l,d=t.dismissExpires,_=void 0===d?0:d,E=t.dismissOptions,j=void 0===E?{}:E,S=t.gaTrackingEventArgs,w=t.isDisabled,N=void 0!==w&&w,T=Object(m.useState)(!1),C=u()(T,2),A=C[0],x=C[1],D=Object(f.a)(),R=Object(O.a)(n,null==S?void 0:S.category),L=Object(p.useSelect)((function(e){return!!r&&e(b.a).isNavigatingTo(r)})),P=Object(p.useDispatch)(h.c),I=P.clearError,M=P.receiveError,B=Object(p.useDispatch)(v.a).dismissNotification,F=Object(p.useDispatch)(b.a).navigateTo,z=function(){var e=s()(i.a.mark((function e(t){var a,c,s;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return I("notificationAction",[n]),t.persist(),!t.defaultPrevented&&r&&t.preventDefault(),x(!0),e.next=6,null==o?void 0:o(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:if(a=e.t0,c=a.error,D()&&x(!1),!c){e.next=15;break}return M(c,"notificationAction",[n]),e.abrupt("return");case 15:return s=[R.confirm()],g&&s.push(B(n,k(k({},j),{},{expiresInSeconds:_}))),e.next=19,Promise.all(s);case 19:r&&F(r);case 20:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(y.SpinnerButton,{className:"googlesitekit-notification__cta",href:r,onClick:z,disabled:A||L||N,isSaving:A||L||c},a)}CTALink.propTypes={id:d.a.string,ctaLink:d.a.string,ctaLabel:d.a.string,onCTAClick:d.a.func,dismissOnCTAClick:d.a.bool,dismissExpires:d.a.number,dismissOptions:d.a.object,isDisabled:d.a.bool}}).call(this,n(4))},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="core/modules",i="insufficient_module_dependencies"},190:function(e,t){e.exports=googlesitekit.modules},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(11),s=n.n(c),l=n(1),u=n.n(l),g=n(146),d=n(0),f=n(2),m=n(126),p=n(127),v=n(128),b=n(70),h=n(76),O=Object(d.forwardRef)((function(t,n){var r,a=t["aria-label"],c=t.secondary,l=void 0!==c&&c,u=t.arrow,d=void 0!==u&&u,O=t.back,y=void 0!==O&&O,_=t.caps,k=void 0!==_&&_,E=t.children,j=t.className,S=void 0===j?"":j,w=t.danger,N=void 0!==w&&w,T=t.disabled,C=void 0!==T&&T,A=t.external,x=void 0!==A&&A,D=t.hideExternalIndicator,R=void 0!==D&&D,L=t.href,P=void 0===L?"":L,I=t.inverse,M=void 0!==I&&I,B=t.noFlex,F=void 0!==B&&B,z=t.onClick,U=t.small,H=void 0!==U&&U,G=t.standalone,W=void 0!==G&&G,q=t.linkButton,V=void 0!==q&&q,K=t.to,$=t.leadingIcon,J=t.trailingIcon,Y=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),X=P||K||!z?K?"ROUTER_LINK":x?"EXTERNAL_LINK":"LINK":C?"BUTTON_DISABLED":"BUTTON",Q="BUTTON"===X||"BUTTON_DISABLED"===X?"button":"ROUTER_LINK"===X?g.b:"a",Z=("EXTERNAL_LINK"===X&&(r=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===X&&(r=Object(f._x)("(disabled)","screen reader text","google-site-kit")),r?a?"".concat(a," ").concat(r):"string"==typeof E?"".concat(E," ").concat(r):void 0:a),ee=$,te=J;return y&&(ee=e.createElement(v.a,{width:14,height:14})),x&&!R&&(te=e.createElement(b.a,{width:14,height:14})),d&&!M&&(te=e.createElement(m.a,{width:14,height:14})),d&&M&&(te=e.createElement(p.a,{width:14,height:14})),e.createElement(Q,i()({"aria-label":Z,className:s()("googlesitekit-cta-link",S,{"googlesitekit-cta-link--secondary":l,"googlesitekit-cta-link--inverse":M,"googlesitekit-cta-link--small":H,"googlesitekit-cta-link--caps":k,"googlesitekit-cta-link--danger":N,"googlesitekit-cta-link--disabled":C,"googlesitekit-cta-link--standalone":W,"googlesitekit-cta-link--link-button":V,"googlesitekit-cta-link--no-flex":!!F}),disabled:C,href:"LINK"!==X&&"EXTERNAL_LINK"!==X||C?void 0:P,onClick:z,rel:"EXTERNAL_LINK"===X?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===X?"_blank":void 0,to:K},Y),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},E),!!te&&e.createElement(h.a,{marginLeft:5},te))}));O.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=O}).call(this,n(4))},200:function(e,t,n){"use strict";n.d(t,"a",(function(){return E})),n.d(t,"b",(function(){return j})),n.d(t,"c",(function(){return S})),n.d(t,"g",(function(){return w})),n.d(t,"f",(function(){return N})),n.d(t,"d",(function(){return T})),n.d(t,"e",(function(){return C}));var r=n(16),i=n.n(r),a=n(5),o=n.n(a),c=n(6),s=n.n(c),l=n(12),u=n.n(l),g=n(14),d=n(45),f=n.n(d),m=n(3),p=n(62),v=n(82),b=n(48),h=n(64);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var _=h.a.clearError,k=h.a.receiveError,E="cannot submit changes while submitting changes",j="cannot submit changes if settings have not changed",S=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=r.ownedSettingsSlugs,a=void 0===i?void 0:i,c=r.storeName,l=void 0===c?void 0:c,d=r.settingSlugs,h=void 0===d?[]:d,O=r.initialSettings,E=void 0===O?void 0:O,j=r.validateHaveSettingsChanged,S=void 0===j?C():j;u()(e,"type is required."),u()(t,"identifier is required."),u()(n,"datapoint is required.");var w=l||"".concat(e,"/").concat(t),N={ownedSettingsSlugs:a,settings:E,savedSettings:void 0},T=Object(b.a)({baseName:"getSettings",controlCallback:function(){return f.a.get(e,t,n,{},{useCache:!1})},reducerCallback:function(e,t){return y(y({},e),{},{savedSettings:y({},t),settings:y(y({},t),e.settings||{})})}}),A=Object(b.a)({baseName:"saveSettings",controlCallback:function(r){var i=r.values;return f.a.set(e,t,n,i)},reducerCallback:function(e,t){return y(y({},e),{},{savedSettings:y({},t),settings:y({},t)})},argsToParams:function(e){return{values:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.values;u()(Object(g.isPlainObject)(t),"values is required.")}}),x={},D={setSettings:function(e){return u()(Object(g.isPlainObject)(e),"values is required."),{payload:{values:e},type:"SET_SETTINGS"}},rollbackSettings:function(){return{payload:{},type:"ROLLBACK_SETTINGS"}},rollbackSetting:function(e){return u()(e,"setting is required."),{payload:{setting:e},type:"ROLLBACK_SETTING"}},saveSettings:o.a.mark((function e(){var t,n,r,i,a;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,_("saveSettings",[]);case 5:return n=t.select(w).getSettings(),e.next=8,A.actions.fetchSaveSettings(n);case 8:if(r=e.sent,i=r.response,!(a=r.error)){e.next=14;break}return e.next=14,k(a,"saveSettings",[]);case 14:return e.abrupt("return",{response:i,error:a});case 15:case"end":return e.stop()}}),e)}))},R={},L=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:N,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.payload;switch(n){case"SET_SETTINGS":var i=r.values;return y(y({},e),{},{settings:y(y({},e.settings||{}),i)});case"ROLLBACK_SETTINGS":return y(y({},e),{},{settings:e.savedSettings});case"ROLLBACK_SETTING":var a=r.setting;return e.savedSettings[a]?y(y({},e),{},{settings:y(y({},e.settings||{}),{},s()({},a,e.savedSettings[a]))}):y({},e);default:return void 0!==x[n]?x[n](e,{type:n,payload:r}):e}},P={getSettings:o.a.mark((function e(){var t;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m.commonActions.getRegistry();case 2:if(t=e.sent,t.select(w).getSettings()){e.next=7;break}return e.next=7,T.actions.fetchGetSettings();case 7:case"end":return e.stop()}}),e)}))},I=Object(p.g)(S),M=I.safeSelector,B=I.dangerousSelector,F={haveSettingsChanged:M,__dangerousHaveSettingsChanged:B,getSettings:function(e){return e.settings},hasSettingChanged:function(e,t){u()(t,"setting is required.");var n=e.settings,r=e.savedSettings;return!(!n||!r)&&!Object(g.isEqual)(n[t],r[t])},isDoingSaveSettings:function(e){return Object.values(e.isFetchingSaveSettings).some(Boolean)},getOwnedSettingsSlugs:function(e){return e.ownedSettingsSlugs},haveOwnedSettingsChanged:Object(m.createRegistrySelector)((function(e){return function(){var t=e(w).getOwnedSettingsSlugs();return e(w).haveSettingsChanged(t)}}))};h.forEach((function(e){var t=Object(v.b)(e),n=Object(v.a)(e);D["set".concat(t)]=function(e){return u()(void 0!==e,"value is required for calls to set".concat(t,"().")),{payload:{value:e},type:"SET_".concat(n)}},x["SET_".concat(n)]=function(t,n){var r=n.payload.value;return y(y({},t),{},{settings:y(y({},t.settings||{}),{},s()({},e,r))})},F["get".concat(t)]=Object(m.createRegistrySelector)((function(t){return function(){return(t(w).getSettings()||{})[e]}}))}));var z=Object(m.combineStores)(m.commonStore,T,A,{initialState:N,actions:D,controls:R,reducer:L,resolvers:P,selectors:F});return y(y({},z),{},{STORE_NAME:w})};function w(e,t){return function(){var n=i()(o.a.mark((function n(r){var i,a,c,s;return o.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(i=r.select,a=r.dispatch,!i(t).haveSettingsChanged()){n.next=8;break}return n.next=4,a(t).saveSettings();case 4:if(c=n.sent,!(s=c.error)){n.next=8;break}return n.abrupt("return",{error:s});case 8:return n.next=10,f.a.invalidateCache("modules",e);case 10:return n.abrupt("return",{});case 11:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}function N(e){return function(t){var n=t.select,r=t.dispatch;return n(e).haveSettingsChanged()?r(e).rollbackSettings():{}}}function T(e){return function(t){var n=Object(p.e)(t)(e),r=n.haveSettingsChanged,i=n.isDoingSubmitChanges;u()(!i(),E),u()(r(),j)}}function C(){return function(e,t,n){var r=t.settings,i=t.savedSettings;n&&u()(!Object(g.isEqual)(Object(g.pick)(r,n),Object(g.pick)(i,n)),j),u()(!Object(g.isEqual)(r,i),j)}}},201:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeSingleRow}));var r=n(1),i=n.n(r),a=n(0);function SettingsNoticeSingleRow(t){var n=t.notice,r=t.LearnMore,i=t.CTA;return e.createElement(a.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),i&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(i,null)))}SettingsNoticeSingleRow.propTypes={notice:i.a.node.isRequired,LearnMore:i.a.elementType,CTA:i.a.elementType}}).call(this,n(4))},205:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a}));var r="warning-notification-fpm",i="fpm-setup-cta",a={ERROR_HIGH:30,ERROR_LOW:60,WARNING:100,INFO:150,SETUP_CTA_HIGH:150,SETUP_CTA_LOW:200}},207:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5 22c-1.522 0-2.952-.284-4.29-.852a11.303 11.303 0 01-3.493-2.366 11.303 11.303 0 01-2.365-3.492A10.86 10.86 0 01.5 11c0-1.522.284-2.952.853-4.29a11.302 11.302 0 012.364-3.493A10.92 10.92 0 017.21.88 10.567 10.567 0 0111.5 0c1.522 0 2.952.293 4.29.88a10.92 10.92 0 013.492 2.337c.99.99 1.77 2.155 2.338 3.493.587 1.338.88 2.768.88 4.29 0 1.522-.293 2.952-.88 4.29a10.92 10.92 0 01-2.338 3.492c-.99.99-2.154 1.779-3.492 2.366A10.86 10.86 0 0111.5 22zm0-14.3c.312 0 .569-.1.77-.303.22-.22.33-.485.33-.797a.999.999 0 00-.33-.77.999.999 0 00-.77-.33c-.311 0-.577.11-.797.33a1.043 1.043 0 00-.303.77c0 .312.101.578.303.798.22.201.486.302.797.302zm-1.1 8.8V9.9h2.2v6.6h-2.2z",fill:"currentColor"});t.a=function SvgInfoCircle(e){return r.createElement("svg",i({viewBox:"0 0 23 22",fill:"none"},e),a)}},210:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeMultiRow}));var r=n(1),i=n.n(r),a=n(0);function SettingsNoticeMultiRow(t){var n=t.notice,r=t.LearnMore,i=t.CTA,o=t.children;return e.createElement(a.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),e.createElement("div",{className:"googlesitekit-settings-notice__inner-row"},e.createElement("div",{className:"googlesitekit-settings-notice__children-container"},o),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),i&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(i,null))))}SettingsNoticeMultiRow.propTypes={children:i.a.node.isRequired,notice:i.a.node.isRequired,LearnMore:i.a.elementType,CTA:i.a.elementType}}).call(this,n(4))},217:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WarningNotice}));var r=n(11),i=n.n(r),a=n(1),o=n.n(a);function WarningNotice(t){var n=t.children,r=t.className;return e.createElement("div",{className:i()("googlesitekit-warning-notice",r)},n)}WarningNotice.propTypes={children:o.a.node.isRequired,className:o.a.string}}).call(this,n(4))},22:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return i})),n.d(t,"o",(function(){return a})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return s})),n.d(t,"s",(function(){return l})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return g})),n.d(t,"r",(function(){return d})),n.d(t,"k",(function(){return f})),n.d(t,"u",(function(){return m})),n.d(t,"v",(function(){return p})),n.d(t,"q",(function(){return v})),n.d(t,"p",(function(){return b})),n.d(t,"b",(function(){return h})),n.d(t,"e",(function(){return O})),n.d(t,"a",(function(){return y})),n.d(t,"d",(function(){return _})),n.d(t,"c",(function(){return k})),n.d(t,"f",(function(){return E})),n.d(t,"g",(function(){return j}));var r="mainDashboard",i="entityDashboard",a="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",s="activation",l="splash",u="adminBar",g="adminBarViewOnly",d="settings",f="adBlockingRecovery",m="wpDashboard",p="wpDashboardViewOnly",v="moduleSetup",b="metricSelection",h="key-metrics",O="traffic",y="content",_="speed",k="monetization",E=[r,i,a,o,c,l,d,v,b],j=[a,o,g,p]},24:function(e,t,n){"use strict";n.d(t,"d",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return s}));var r=n(79),i="xlarge",a="desktop",o="tablet",c="small";function s(){var e=Object(r.a)();return e>1280?i:e>960?a:e>600?o:c}},242:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationWithSVG}));var r=n(21),i=n.n(r),a=n(11),o=n.n(a),c=n(24),s=n(17),l=n(255);function NotificationWithSVG(t){var n=t.id,r=t.title,a=t.description,u=t.actions,g=t.SVG,d=t.primaryCellSizes,f=t.SVGCellSizes,m=Object(c.e)(),p={mdSize:(null==f?void 0:f.md)||8,lgSize:(null==f?void 0:f.lg)||6};return m===c.c&&(p={mdSize:(null==f?void 0:f.md)||8}),m===c.b&&(p={smSize:(null==f?void 0:f.sm)||12}),e.createElement("div",{className:"googlesitekit-widget-context"},e.createElement(s.e,{className:"googlesitekit-widget-area"},e.createElement(s.k,null,e.createElement(s.a,{size:12},e.createElement("div",{className:o()("googlesitekit-widget","googlesitekit-widget--no-padding","googlesitekit-setup-cta-banner","googlesitekit-setup-cta-banner--".concat(n))},e.createElement("div",{className:"googlesitekit-widget__body"},e.createElement(s.e,{collapsed:!0},e.createElement(s.k,null,e.createElement(s.a,{smSize:(null==d?void 0:d.sm)||12,mdSize:(null==d?void 0:d.md)||8,lgSize:(null==d?void 0:d.lg)||6,className:"googlesitekit-setup-cta-banner__primary-cell"},e.createElement("h3",{className:"googlesitekit-setup-cta-banner__title"},r),a,e.createElement(l.a,{id:n}),u),e.createElement(s.a,i()({alignBottom:!0,className:"googlesitekit-setup-cta-banner__svg-wrapper--".concat(n)},p),e.createElement(g,null))))))))))}}).call(this,n(4))},255:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Error}));var r=n(1),i=n.n(r),a=n(0),o=n(3),c=n(13),s=n(54);function Error(t){var n=t.id,r=Object(o.useSelect)((function(e){return e(c.c).getError("notificationAction",[n])})),i=Object(o.useDispatch)(c.c).clearError;return Object(a.useEffect)((function(){return function(){i("notificationAction",[n])}}),[i,n]),r?e.createElement(s.a,{message:r.message}):null}Error.propTypes={id:i.a.string}}).call(this,n(4))},3:function(e,t){e.exports=googlesitekit.data},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},35:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return g}));n(14);var r=n(2),i="missing_required_scopes",a="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===i}function s(e){var t;return[a,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function l(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||s(e)||c(e)||l(e))}function g(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(r.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(r.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return y})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return O}));var r=n(99),i=e._googlesitekitTrackingData||{},a=i.activeModules,o=void 0===a?[]:a,c=i.isSiteKitScreen,s=i.trackingEnabled,l=i.trackingID,u=i.referenceSiteURL,g=i.userIDHash,d=i.isAuthenticated,f={activeModules:o,trackingEnabled:s,trackingID:l,referenceSiteURL:u,userIDHash:g,isSiteKitScreen:c,userRoles:i.userRoles,isAuthenticated:d,pluginVersion:"1.151.0"},m=Object(r.a)(f),p=m.enableTracking,v=m.disableTracking,b=(m.isTrackingEnabled,m.initializeSnippet),h=m.trackEvent,O=m.trackEventOnce;function y(e){e?p():v()}c&&s&&b()}).call(this,n(28))},363:function(e,t){e.exports=googlesitekit.notifications},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return O})),n.d(t,"c",(function(){return y})),n.d(t,"e",(function(){return _})),n.d(t,"b",(function(){return k}));var r=n(5),i=n.n(r),a=n(16),o=n.n(a),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u,g="googlesitekit_",d="".concat(g).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),f=["sessionStorage","localStorage"],m=[].concat(f),p=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,a="__storage_test__",r.setItem(a,a),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function v(){return b.apply(this,arguments)}function b(){return(b=o()(i.a.mark((function t(){var n,r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=s(m),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(a=r.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,p(a);case 11:if(!t.sent){t.next=13;break}u=e[a];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=o()(i.a.mark((function e(t){var n,r,a,o,c,s,l;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(d).concat(t)))){e.next=10;break}if(a=JSON.parse(r),o=a.timestamp,c=a.ttl,s=a.value,l=a.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:l});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),O=function(){var t=o()(i.a.mark((function t(n,r){var a,o,s,l,u,g,f,m,p=arguments;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=p.length>2&&void 0!==p[2]?p[2]:{},o=a.ttl,s=void 0===o?c.b:o,l=a.timestamp,u=void 0===l?Math.round(Date.now()/1e3):l,g=a.isError,f=void 0!==g&&g,t.next=3,v();case 3:if(!(m=t.sent)){t.next=14;break}return t.prev=5,m.setItem("".concat(d).concat(n),JSON.stringify({timestamp:u,ttl:s,value:r,isError:f})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),y=function(){var t=o()(i.a.mark((function t(n){var r,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,v();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,a=n.startsWith(g)?n:"".concat(d).concat(n),r.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),_=function(){var t=o()(i.a.mark((function t(){var n,r,a,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,v();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],a=0;a<n.length;a++)0===(o=n.key(a)).indexOf(g)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),k=function(){var e=o()(i.a.mark((function e(){var t,n,r,a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v();case 2:if(!e.sent){e.next=25;break}return e.next=6,_();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return a=r.value,e.next=14,y(a);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="_googlesitekitDataLayer",i="data-googlesitekit-gtag"},41:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(22),i="core/notifications",a={ERRORS:"notification-area-errors",BANNERS_ABOVE_NAV:"notification-area-banners-above-nav",BANNERS_BELOW_NAV:"notification-area-banners-below-nav"},o={DEFAULT:"default",SETUP_CTAS:"setup-ctas"},c=[r.s,r.n,r.l,r.o,r.m]},44:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),a=n(1),o=n.n(a),c=n(11),s=n.n(c),l=n(24);function PreviewBlock(t){var n,r,a=t.className,o=t.width,c=t.height,u=t.shape,g=t.padding,d=t.smallWidth,f=t.smallHeight,m=t.tabletWidth,p=t.tabletHeight,v=t.desktopWidth,b=t.desktopHeight,h=Object(l.e)(),O={width:(n={},i()(n,l.b,d),i()(n,l.c,m),i()(n,l.a,v),i()(n,l.d,v),n),height:(r={},i()(r,l.b,f),i()(r,l.c,p),i()(r,l.a,b),i()(r,l.d,v),r)};return e.createElement("div",{className:s()("googlesitekit-preview-block",a,{"googlesitekit-preview-block--padding":g}),style:{width:O.width[h]||o,height:O.height[h]||c}},e.createElement("div",{className:s()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(4))},45:function(e,t){e.exports=googlesitekit.api},48:function(e,t,n){"use strict";n.d(t,"a",(function(){return y}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(12),s=n.n(c),l=n(14),u=n(64),g=n(82),d=n(9);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var p=function(e){return e},v=function(){return{}},b=function(){},h=u.a.clearError,O=u.a.receiveError,y=function(e){var t,n,r=i.a.mark(P),a=e.baseName,c=e.controlCallback,u=e.reducerCallback,f=void 0===u?p:u,y=e.argsToParams,_=void 0===y?v:y,k=e.validateParams,E=void 0===k?b:k;s()(a,"baseName is required."),s()("function"==typeof c,"controlCallback is required and must be a function."),s()("function"==typeof f,"reducerCallback must be a function."),s()("function"==typeof _,"argsToParams must be a function."),s()("function"==typeof E,"validateParams must be a function.");try{E(_()),n=!1}catch(e){n=!0}var j=Object(g.b)(a),S=Object(g.a)(a),w="FETCH_".concat(S),N="START_".concat(w),T="FINISH_".concat(w),C="CATCH_".concat(w),A="RECEIVE_".concat(S),x="fetch".concat(j),D="receive".concat(j),R="isFetching".concat(j),L=o()({},R,{});function P(e,t){var n,o;return i.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,{payload:{params:e},type:N};case 2:return r.next=4,h(a,t);case 4:return r.prev=4,r.next=7,{payload:{params:e},type:w};case 7:return n=r.sent,r.next=10,I[D](n,e);case 10:return r.next=12,{payload:{params:e},type:T};case 12:r.next=21;break;case 14:return r.prev=14,r.t0=r.catch(4),o=r.t0,r.next=19,O(o,a,t);case 19:return r.next=21,{payload:{params:e},type:C};case 21:return r.abrupt("return",{response:n,error:o});case 22:case"end":return r.stop()}}),r,null,[[4,14]])}var I=(t={},o()(t,x,(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=_.apply(void 0,t);return E(r),P(r,t)})),o()(t,D,(function(e,t){return s()(void 0!==e,"response is required."),n?(s()(Object(l.isPlainObject)(t),"params is required."),E(t)):t={},{payload:{response:e,params:t},type:A}})),t),M=o()({},w,(function(e){var t=e.payload;return c(t.params)})),B=o()({},R,(function(e){if(void 0===e[R])return!1;var t;try{for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];t=_.apply(void 0,r),E(t)}catch(e){return!1}return!!e[R][Object(d.H)(t)]}));return{initialState:L,actions:I,controls:M,reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case N:var i=r.params;return m(m({},e),{},o()({},R,m(m({},e[R]),{},o()({},Object(d.H)(i),!0))));case A:var a=r.response,c=r.params;return f(e,a,c);case T:var s=r.params;return m(m({},e),{},o()({},R,m(m({},e[R]),{},o()({},Object(d.H)(s),!1))));case C:var l=r.params;return m(m({},e),{},o()({},R,m(m({},e[R]),{},o()({},Object(d.H)(l),!1))));default:return e}},resolvers:{},selectors:B}}},518:function(e,t,n){"use strict";var r=n(928);n.d(t,"c",(function(){return r.a}));var i=n(929);n.d(t,"d",(function(){return i.a}));var a=n(930);n.d(t,"b",(function(){return a.a}));var o=n(760);n.d(t,"e",(function(){return o.a}));var c=n(931);n.d(t,"a",(function(){return c.a}));n(761),n(762);var s=n(932);n.d(t,"h",(function(){return s.a}));var l=n(933);n.d(t,"f",(function(){return l.a}));var u=n(934);n.d(t,"g",(function(){return u.a}))},54:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,r=t.reconnectURL,i=t.noPrefix;if(!n)return null;var s=n;void 0!==i&&i||(s=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),r&&Object(a.a)(r)&&(s=s+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),r));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(s,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:i.a.string.isRequired,reconnectURL:i.a.string,noPrefix:i.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},57:function(e,t,n){"use strict";(function(e){var r,i;n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=new Set((null===(r=e)||void 0===r||null===(i=r._googlesitekitBaseData)||void 0===i?void 0:i.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,n(28))},58:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return r.createElement("svg",i({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),a,o)}},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(39);function i(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},603:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return r}));var r=function(t){try{if("string"!=typeof t||!t)throw new TypeError("Invalid URL: ".concat(t));return"https:"===new URL(t).protocol}catch(t){return e.console.warn("Invalid URL:",t),!1}}}).call(this,n(28))},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(0),i=Object(r.createContext)(""),a=(i.Consumer,i.Provider);t.b=i},62:function(e,t,n){"use strict";n.d(t,"a",(function(){return w})),n.d(t,"b",(function(){return N})),n.d(t,"c",(function(){return T})),n.d(t,"d",(function(){return A})),n.d(t,"e",(function(){return x})),n.d(t,"g",(function(){return R})),n.d(t,"f",(function(){return L}));var r,i=n(5),a=n.n(i),o=n(27),c=n.n(o),s=n(6),l=n.n(s),u=n(12),g=n.n(u),d=n(63),f=n.n(d),m=n(14),p=n(116);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.reduce((function(e,t){return b(b({},e),t)}),{}),i=t.reduce((function(e,t){return[].concat(c()(e),c()(Object.keys(t)))}),[]),a=C(i);return g()(0===a.length,"collect() cannot accept collections with duplicate keys. Your call to collect() contains the following duplicated functions: ".concat(a.join(", "),". Check your data stores for duplicates.")),r},O=h,y=h,_=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i=[].concat(t);return"function"!=typeof i[0]&&(r=i.shift()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i.reduce((function(e,n){return n(e,t)}),e)}},k=h,E=h,j=h,S=function(e){return e},w=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=j.apply(void 0,c()(t.map((function(e){return e.initialState||{}}))));return{initialState:r,controls:y.apply(void 0,c()(t.map((function(e){return e.controls||{}})))),actions:O.apply(void 0,c()(t.map((function(e){return e.actions||{}})))),reducer:_.apply(void 0,[r].concat(c()(t.map((function(e){return e.reducer||S}))))),resolvers:k.apply(void 0,c()(t.map((function(e){return e.resolvers||{}})))),selectors:E.apply(void 0,c()(t.map((function(e){return e.selectors||{}}))))}},N={getRegistry:function(){return{payload:{},type:"GET_REGISTRY"}},await:a.a.mark((function e(t){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{payload:{value:t},type:"AWAIT"});case 1:case"end":return e.stop()}}),e)}))},T=(r={},l()(r,"GET_REGISTRY",Object(p.a)((function(e){return function(){return e}}))),l()(r,"AWAIT",(function(e){return e.payload.value})),r),C=function(e){for(var t=[],n={},r=0;r<e.length;r++){var i=e[r];n[i]=n[i]>=1?n[i]+1:1,n[i]>1&&t.push(i)}return t},A={actions:N,controls:T,reducer:S},x=function(e){return function(t){return D(e(t))}},D=f()((function(e){return Object(m.mapValues)(e,(function(e,t){return function(){var n=e.apply(void 0,arguments);return g()(void 0!==n,"".concat(t,"(...) is not resolved")),n}}))}));function R(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.negate,r=void 0!==n&&n,i=Object(p.b)((function(t){return function(n){var i=!r,a=!!r;try{for(var o=arguments.length,c=new Array(o>1?o-1:0),s=1;s<o;s++)c[s-1]=arguments[s];return e.apply(void 0,[t,n].concat(c)),i}catch(e){return a}}})),a=Object(p.b)((function(t){return function(n){for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];e.apply(void 0,[t,n].concat(i))}}));return{safeSelector:i,dangerousSelector:a}}function L(e,t){return g()("function"==typeof e,"a validator function is required."),g()("function"==typeof t,"an action creator function is required."),g()("Generator"!==e[Symbol.toStringTag]&&"GeneratorFunction"!==e[Symbol.toStringTag],"an action’s validator function must not be a generator."),function(){return e.apply(void 0,arguments),t.apply(void 0,arguments)}}},64:function(e,t,n){"use strict";n.d(t,"a",(function(){return v})),n.d(t,"b",(function(){return b}));var r=n(6),i=n.n(r),a=n(33),o=n.n(a),c=n(116),s=n(12),l=n.n(s),u=n(96),g=n.n(u),d=n(9);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t){if(t&&Array.isArray(t)){var n=t.map((function(e){return"object"===o()(e)?Object(d.H)(e):e}));return"".concat(e,"::").concat(g()(JSON.stringify(n)))}return e}var v={receiveError:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(e,"error is required."),l()(t,"baseName is required."),l()(n&&Array.isArray(n),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:n}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return l()(e,"baseName is required."),l()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function b(e){l()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(n,"selectorName is required."),t.getError(e,n,r)},getErrorForAction:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(n,"actionName is required."),t.getError(e,n,r)},getError:function(e,t,n){var r=e.errors;return l()(t,"baseName is required."),r[p(t,n)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var n=Object.keys(e.errors).find((function(n){return e.errors[n]===t}));return n?{baseName:n.substring(0,n.indexOf("::")),args:e.errorArgs[n]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(n,r){var i=t(e).getMetaDataForError(r);if(i){var a=i.baseName,o=i.args;if(!!t(e)[a])return{storeName:e,name:a,args:o}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:v,controls:{},reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case"RECEIVE_ERROR":var a=r.baseName,o=r.args,c=r.error,s=p(a,o);return m(m({},e),{},{errors:m(m({},e.errors||{}),{},i()({},s,c)),errorArgs:m(m({},e.errorArgs||{}),{},i()({},s,o))});case"CLEAR_ERROR":var l=r.baseName,u=r.args,g=m({},e),d=p(l,u);return g.errors=m({},e.errors||{}),g.errorArgs=m({},e.errorArgs||{}),delete g.errors[d],delete g.errorArgs[d],g;case"CLEAR_ERRORS":var f=r.baseName,v=m({},e);if(f)for(var b in v.errors=m({},e.errors||{}),v.errorArgs=m({},e.errorArgs||{}),v.errors)(b===f||b.startsWith("".concat(f,"::")))&&(delete v.errors[b],delete v.errorArgs[b]);else v.errors={},v.errorArgs={};return v;default:return e}},resolvers:{},selectors:t}}},65:function(e,t,n){"use strict";n.d(t,"c",(function(){return p})),n.d(t,"a",(function(){return v})),n.d(t,"b",(function(){return b})),n.d(t,"d",(function(){return O}));var r=n(6),i=n.n(r),a=n(0);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var c=a.createElement("path",{d:"M10 15.27L16.18 19l-1.64-7.03L20 7.24l-7.19-.61L10 0 7.19 6.63 0 7.24l5.46 4.73L3.82 19 10 15.27z"});var s=function SvgInfoIcon(e){return a.createElement("svg",o({viewBox:"0 0 20 20",fill:"currentColor"},e),c)};function l(){return(l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var u=a.createElement("path",{d:"M0 4h2v7H0zm0-4h2v2H0z",fill:"currentColor",fillRule:"evenodd"});var g,d=function SvgSuggestionIcon(e){return a.createElement("svg",l({viewBox:"0 0 2 11"},e),u)},f=n(186),m=n(74),p="warning",v="info",b="suggestion",h=(g={},i()(g,v,s),i()(g,p,f.a),i()(g,b,d),g),O=function(e){return h[e]||m.a}},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return l})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return g})),n.d(t,"L",(function(){return d})),n.d(t,"J",(function(){return f})),n.d(t,"I",(function(){return m})),n.d(t,"N",(function(){return p})),n.d(t,"f",(function(){return v})),n.d(t,"g",(function(){return b})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return O})),n.d(t,"l",(function(){return y})),n.d(t,"m",(function(){return _})),n.d(t,"n",(function(){return k})),n.d(t,"o",(function(){return E})),n.d(t,"q",(function(){return j})),n.d(t,"s",(function(){return S})),n.d(t,"r",(function(){return w})),n.d(t,"t",(function(){return N})),n.d(t,"w",(function(){return T})),n.d(t,"u",(function(){return C})),n.d(t,"v",(function(){return A})),n.d(t,"x",(function(){return x})),n.d(t,"y",(function(){return D})),n.d(t,"A",(function(){return R})),n.d(t,"B",(function(){return L})),n.d(t,"C",(function(){return P})),n.d(t,"D",(function(){return I})),n.d(t,"k",(function(){return M})),n.d(t,"F",(function(){return B})),n.d(t,"z",(function(){return F})),n.d(t,"G",(function(){return z})),n.d(t,"E",(function(){return U})),n.d(t,"i",(function(){return H})),n.d(t,"p",(function(){return G})),n.d(t,"Q",(function(){return W})),n.d(t,"P",(function(){return q}));var r="core/user",i="connected_url_mismatch",a="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",l="googlesitekit_setup",u="googlesitekit_view_dashboard",g="googlesitekit_manage_options",d="googlesitekit_read_shared_module_data",f="googlesitekit_manage_module_sharing_options",m="googlesitekit_delegate_module_sharing_management",p="googlesitekit_update_plugins",v="kmAnalyticsAdSenseTopEarningContent",b="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",O="kmAnalyticsNewVisitors",y="kmAnalyticsPopularAuthors",_="kmAnalyticsPopularContent",k="kmAnalyticsPopularProducts",E="kmAnalyticsReturningVisitors",j="kmAnalyticsTopCities",S="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",N="kmAnalyticsTopCitiesDrivingPurchases",T="kmAnalyticsTopDeviceDrivingPurchases",C="kmAnalyticsTopConvertingTrafficSource",A="kmAnalyticsTopCountries",x="kmAnalyticsTopPagesDrivingLeads",D="kmAnalyticsTopRecentTrendingPages",R="kmAnalyticsTopTrafficSource",L="kmAnalyticsTopTrafficSourceDrivingAddToCart",P="kmAnalyticsTopTrafficSourceDrivingLeads",I="kmAnalyticsTopTrafficSourceDrivingPurchases",M="kmAnalyticsPagesPerVisit",B="kmAnalyticsVisitLength",F="kmAnalyticsTopReturningVisitorPages",z="kmSearchConsolePopularKeywords",U="kmAnalyticsVisitsPerVisitor",H="kmAnalyticsMostEngagingPages",G="kmAnalyticsTopCategories",W=[v,b,h,O,y,_,k,E,G,j,S,w,N,T,C,A,D,R,L,M,B,F,U,H,G],q=[].concat(W,[z])},70:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},727:function(e,t,n){"use strict";function r(e){return"string"==typeof e&&""!==e&&/^[A-Za-z0-9-_.]+$/.test(e)}n.d(t,"a",(function(){return r}))},73:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),i=n(18),a=n(9);function o(e,t){var n=Object(i.a)(),o=null!=t?t:"".concat(n,"_").concat(e);return{view:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"view_notification"].concat(t))}),[o]),confirm:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"confirm_notification"].concat(t))}),[o]),dismiss:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"dismiss_notification"].concat(t))}),[o]),clickLearnMore:Object(r.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"click_learn_more_link"].concat(t))}),[o])}}},74:function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(33),i=n.n(r),a=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,n="object"===i()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(1),i=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,i=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:i}},n)}IconWrapper.propTypes={children:i.a.node.isRequired,marginLeft:i.a.number,marginRight:i.a.number}}).call(this,n(4))},760:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ClientIDTextField}));var r=n(15),i=n.n(r),a=n(11),o=n.n(a),c=n(1),s=n.n(c),l=n(0),u=n(2),g=n(3),d=n(10),f=n(110),m=n(727),p=n(121);function ClientIDTextField(t){var n=t.existingClientID,r=void 0===n?"":n,a=Object(g.useSelect)((function(e){return e(f.b).getClientID()})),c=Object(l.useState)(!1),s=i()(c,2),v=s[0],b=s[1],h=Object(l.useState)(!a||Object(m.a)(a)),O=i()(h,2),y=O[0],_=O[1],k=Object(p.a)(_,500),E=Object(g.useDispatch)(f.b).setClientID;Object(l.useEffect)((function(){a||!r||v||(E(r),b(!0))}),[a,E,r,v]);var j,S=Object(l.useCallback)((function(e){var t=e.currentTarget.value;t!==a&&E(t),k(Object(m.a)(t))}),[a,E,k]);return y||(j=Object(u.__)("A valid Client ID is required to use Sign in with Google","google-site-kit")),r&&a===r&&(j=Object(u.__)("Sign in with Google was already set up on this site. We recommend using your existing Client ID.","google-site-kit")),e.createElement("div",{className:"googlesitekit-settings-module__fields-group"},e.createElement(d.TextField,{label:Object(u.__)("Client ID","google-site-kit"),className:o()("googlesitekit-text-field-client-id",{"mdc-text-field--error":!y}),helperText:j,outlined:!0,value:a,onChange:S,maxLength:120}))}ClientIDTextField.propTypes={existingClientID:s.a.string}}).call(this,n(4))},761:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AnyoneCanRegisterDisabledNotice}));var r=n(11),i=n.n(r),a=n(38),o=n(0),c=n(2),s=n(3),l=n(7),u=n(13),g=n(24),d=n(142),f=n(20),m=n(207);function AnyoneCanRegisterDisabledNotice(t){var n=t.className,r=Object(g.e)(),p=Object(s.useSelect)((function(e){return e(l.a).hasCapability(l.K)})),v=Object(s.useSelect)((function(e){return e(u.c).isMultisite()})),b=Object(s.useSelect)((function(e){return e(u.c).getAdminSettingsURL()}));return!0===Object(s.useSelect)((function(e){return e(l.a).isItemDismissed("sign-in-with-google-anyone-can-register-notice")}))?null:e.createElement(d.c,{className:i()("googlesitekit-registration-disabled-notice","googlesitekit-anyone-can-register-disabled-notice",n),type:d.a,Icon:m.a,dismiss:"sign-in-with-google-anyone-can-register-notice",dismissLabel:Object(c.__)("Got it","google-site-kit"),notice:Object(a.a)(Object(c.sprintf)(
/* translators: %1$s: Setting name, %2$s: Sign in with Google service name */
Object(c.__)("Enable the %1$s setting to allow your visitors to create an account using the %2$s button. <br/>Visit <a>WordPress settings</a> to manage this setting.","google-site-kit"),v?Object(c.__)("“Allow new registrations”","google-site-kit"):Object(c.__)("“Anyone can register”","google-site-kit"),Object(c._x)("Sign in with Google","Service name","google-site-kit")),{a:!p&&v?e.createElement("span",null):e.createElement(f.a,{key:"link",href:b}),br:r===g.d||r===g.a?e.createElement("br",null):e.createElement(o.Fragment,null)})})}}).call(this,n(4))},762:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RegistrationDisabledNotice}));var r=n(11),i=n.n(r),a=n(1),o=n.n(a),c=n(2),s=n(3),l=n(13),u=n(10),g=n(142),d=n(70),f=n(207);function RegistrationDisabledNotice(t){var n=t.className,r=Object(s.useSelect)((function(e){return e(l.c).isMultisite()})),a=Object(s.useSelect)((function(e){return e(l.c).getAdminSettingsURL()}));return e.createElement(g.c,{className:i()("googlesitekit-registration-disabled-notice","googlesitekit-registration-disabled-notice--with-outer-cta",n),Icon:f.a,type:g.b,notice:Object(c.sprintf)(
/* translators: %1$s: Setting name, %2$s: Sign in with Google service name */
Object(c.__)("Using “One Tap sign in on all pages” will cause errors for users without an account. Enable “%1$s” in WordPress settings to allow anyone to use %2$s.","google-site-kit"),r?Object(c.__)("Allow new registrations","google-site-kit"):Object(c.__)("Anyone can register","google-site-kit"),Object(c._x)("Sign in with Google","Service name","google-site-kit")),OuterCTA:function OuterCTA(){return e.createElement(u.Button,{href:a,target:"_blank",trailingIcon:e.createElement(d.a,{width:13,height:13})},Object(c.__)("Manage settings","google-site-kit"))}})}RegistrationDisabledNotice.propTypes={className:o.a.string}}).call(this,n(4))},77:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),a=n(25),o=n.n(a),c=n(11),s=n.n(c),l=n(1),u=n.n(l),g=n(0),d=Object(g.forwardRef)((function(t,n){var r=t.label,a=t.className,c=t.hasLeftSpacing,l=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",i()({ref:n},u,{className:s()("googlesitekit-badge",a,{"googlesitekit-badge--has-left-spacing":l})}),r)}));d.displayName="Badge",d.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=d}).call(this,n(4))},788:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=r.createElement("g",{clipPath:"url(#sign-in-with-google_svg__clip0_27_1152)"},r.createElement("path",{d:"M8.865 24.172L7.472 29.37l-5.088.108A19.91 19.91 0 010 20c0-3.317.807-6.444 2.236-9.198h.001l4.531.83 1.985 4.504A11.888 11.888 0 008.11 20c0 1.468.266 2.875.754 4.172z",fill:"#FBBB00"}),r.createElement("path",{d:"M39.65 16.264c.23 1.21.35 2.459.35 3.736 0 1.432-.15 2.828-.438 4.176a19.996 19.996 0 01-7.041 11.42h-.001l-5.707-.292-.808-5.041a11.92 11.92 0 005.129-6.087H20.439v-7.912H39.65z",fill:"#518EF8"}),r.createElement("path",{d:"M32.52 35.596h.001A19.916 19.916 0 0120.001 40c-7.617 0-14.24-4.257-17.617-10.522l6.481-5.305c1.69 4.507 6.037 7.716 11.135 7.716 2.191 0 4.244-.592 6.006-1.626l6.514 5.333z",fill:"#28B446"}),r.createElement("path",{d:"M32.766 4.604l-6.48 5.305A11.822 11.822 0 0020 8.11c-5.213 0-9.643 3.356-11.247 8.025l-6.516-5.334C5.564 4.385 12.27 0 20 0c4.853 0 9.302 1.729 12.766 4.604z",fill:"#F14336"})),o=r.createElement("defs",null,r.createElement("clipPath",{id:"sign-in-with-google_svg__clip0_27_1152"},r.createElement("path",{fill:"#fff",d:"M0 0h40v40H0z"})));t.a=function SvgSignInWithGoogle(e){return r.createElement("svg",i({viewBox:"0 0 40 40",fill:"none"},e),a,o)}},79:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g}));var r=n(15),i=n.n(r),a=n(188),o=n(133),c={},s=void 0===e?null:e,l=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,u=void 0===r?0:r,g=e.initialHeight,d=void 0===g?0:g,f=Object(a.a)("undefined"==typeof document?[u,d]:l,t,n),m=i()(f,2),p=m[0],v=m[1],b=function(){return v(l)};return Object(o.a)(s,"resize",b),Object(o.a)(s,"orientationchange",b),p},g=function(e){return u(e)[0]}}).call(this,n(28))},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(106);function i(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),i=e.replace(n.origin,"");if(i.length<t)return i;var a=i.length-Math.floor(t)+1;return"…"+i.substr(a)}},82:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return a}));var r=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},i=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function a(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return E})),n.d(t,"d",(function(){return j})),n.d(t,"e",(function(){return w})),n.d(t,"c",(function(){return N})),n.d(t,"b",(function(){return T}));var r=n(15),i=n.n(r),a=n(33),o=n.n(a),c=n(6),s=n.n(c),l=n(25),u=n.n(l),g=n(14),d=n(63),f=n.n(d),m=n(2);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var b=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=y(e,t),r=n.formatUnit,i=n.formatDecimal;try{return r()}catch(e){return i()}},h=function(e){var t=O(e),n=t.hours,r=t.minutes,i=t.seconds;return i=("0"+i).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(i):"".concat(n,":").concat(r,":").concat(i)},O=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},y=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=O(e),r=n.hours,i=n.minutes,a=n.seconds;return{hours:r,minutes:i,seconds:a,formatUnit:function(){var n=t.unitDisplay,o=v(v({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(a,v(v({},o),{},{unit:"second"})):Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?w(a,v(v({},o),{},{unit:"second"})):"",i?w(i,v(v({},o),{},{unit:"minute"})):"",r?w(r,v(v({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(m.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(m.__)("%ds","google-site-kit"),a);if(0===e)return t;var n=Object(m.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(m.__)("%dm","google-site-kit"),i),o=Object(m.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(m.__)("%dh","google-site-kit"),r);return Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",i?n:"",r?o:"").trim()}}},_=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},k=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in millions.
Object(m.__)("%sM","google-site-kit"),w(_(e),e%10==0?{}:t)):1e4<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),w(_(e))):1e3<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),w(_(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function E(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(g.isPlainObject)(e)&&(t=v({},e)),t}function j(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(g.isFinite)(e)?e:Number(e),Object(g.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=E(t),r=n.style,i=void 0===r?"metric":r;return"metric"===i?k(e):"duration"===i?b(e,n):"durationISO"===i?h(e):w(e,n)}var S=f()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?T():n,a=u()(t,["locale"]);try{return new Intl.NumberFormat(r,a).format(e)}catch(t){S("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],l={},g=0,d=Object.entries(a);g<d.length;g++){var f=i()(d[g],2),m=f[0],p=f[1];c[m]&&p===c[m]||(s.includes(m)||(l[m]=p))}try{return new Intl.NumberFormat(r,l).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},N=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?T():n,i=t.style,a=void 0===i?"long":i,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(r,{style:a,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var l=Object(m.__)(", ","google-site-kit");return e.join(l)},T=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(g.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i}));var r=n(149),i=n.n(r)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),a=n(11),o=n.n(a);function ChangeArrow(t){var n=t.direction,r=t.invertColor,i=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:i,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:i.a.string,invertColor:i.a.bool,width:i.a.number,height:i.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(12),i=n.n(r),a=function(e,t){var n=t.dateRangeLength;i()(Array.isArray(e),"report must be an array to partition."),i()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return i.b})),n.d(t,"J",(function(){return i.c})),n.d(t,"F",(function(){return a.a})),n.d(t,"K",(function(){return a.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return g.a})),n.d(t,"B",(function(){return g.d})),n.d(t,"C",(function(){return g.e})),n.d(t,"y",(function(){return g.c})),n.d(t,"r",(function(){return g.b})),n.d(t,"z",(function(){return p})),n.d(t,"j",(function(){return v})),n.d(t,"i",(function(){return b})),n.d(t,"d",(function(){return E})),n.d(t,"c",(function(){return j})),n.d(t,"e",(function(){return S})),n.d(t,"b",(function(){return w})),n.d(t,"a",(function(){return N})),n.d(t,"f",(function(){return T})),n.d(t,"n",(function(){return C})),n.d(t,"w",(function(){return A})),n.d(t,"p",(function(){return x})),n.d(t,"G",(function(){return D})),n.d(t,"s",(function(){return R})),n.d(t,"v",(function(){return L})),n.d(t,"k",(function(){return P})),n.d(t,"o",(function(){return I.b})),n.d(t,"h",(function(){return I.a})),n.d(t,"t",(function(){return M.b})),n.d(t,"q",(function(){return M.a})),n.d(t,"A",(function(){return M.c})),n.d(t,"x",(function(){return B})),n.d(t,"u",(function(){return F})),n.d(t,"E",(function(){return H})),n.d(t,"D",(function(){return G.a})),n.d(t,"g",(function(){return W})),n.d(t,"L",(function(){return q})),n.d(t,"l",(function(){return V}));var r=n(14),i=n(36),a=n(75),o=n(33),c=n.n(o),s=n(96),l=n.n(s),u=function(e){return l()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var i=t[r];i&&"object"===c()(i)&&!Array.isArray(i)&&(i=e(i)),n[r]=i})),n}(e)))};n(97);var g=n(83);function d(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function f(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function m(e){return e.replace(/\n/gi,"<br>")}function p(e){for(var t=e,n=0,r=[d,f,m];n<r.length;n++){t=(0,r[n])(t)}return t}var v=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},b=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(15),O=n.n(h),y=n(12),_=n.n(y),k=n(2),E="Invalid dateString parameter, it must be a string.",j='Invalid date range, it must be a string with the format "last-x-days".',S=60,w=60*S,N=24*w,T=7*N;function C(){var e=function(e){return Object(k.sprintf)(
/* translators: %s: number of days */
Object(k._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function A(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function x(e){_()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function D(e){_()(A(e),E);var t=e.split("-"),n=O()(t,3),r=n[0],i=n[1],a=n[2];return new Date(r,i-1,a)}function R(e,t){return x(P(e,t*N))}function L(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function P(e,t){_()(A(e)||Object(r.isDate)(e)&&!isNaN(e),E);var n=A(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var I=n(98),M=n(80);function B(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function F(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var z=n(27),U=n.n(z),H=function(e){return Array.isArray(e)?U()(e).sort():e},G=n(89);function W(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var q=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},V=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},92:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Dismiss}));var r=n(5),i=n.n(r),a=n(6),o=n.n(a),c=n(16),s=n.n(c),l=n(1),u=n.n(l),g=n(2),d=n(3),f=n(73),m=n(41),p=n(10);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Dismiss(t){var n=t.id,r=t.primary,a=void 0===r||r,o=t.dismissLabel,c=void 0===o?Object(g.__)("OK, Got it!","google-site-kit"):o,l=t.dismissExpires,u=void 0===l?0:l,v=t.disabled,h=t.onDismiss,O=void 0===h?function(){}:h,y=t.gaTrackingEventArgs,_=t.dismissOptions,k=Object(f.a)(n,null==y?void 0:y.category),E=Object(d.useDispatch)(m.a).dismissNotification,j=function(){var e=s()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==O?void 0:O(t);case 2:k.dismiss(null==y?void 0:y.label,null==y?void 0:y.value),E(n,b(b({},_),{},{expiresInSeconds:u}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(p.Button,{tertiary:!a,onClick:j,disabled:v},c)}Dismiss.propTypes={id:u.a.string,primary:u.a.bool,dismissLabel:u.a.string,dismissExpires:u.a.number,disabled:u.a.bool,onDismiss:u.a.func,gaTrackingEventArgs:u.a.shape({label:u.a.string,value:u.a.string})}}).call(this,n(4))},928:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ButtonTextSelect}));var r=n(0),i=n(2),a=n(3),o=n(110),c=n(10),s=n(18),l=n(9),u=n(121);function ButtonTextSelect(){var t=Object(s.a)(),n=Object(a.useSelect)((function(e){return e(o.b).getText()})),g=Object(a.useDispatch)(o.b).setText,d=Object(r.useCallback)((function(){Object(l.I)("".concat(t,"_sign-in-with-google-settings"),"change_button_text")}),[t]),f=Object(u.a)(d,500),m=Object(r.useCallback)((function(e,t){var r=t.dataset.value;r!==n&&(g(r),f())}),[n,g,f]);return e.createElement("div",{className:"googlesitekit-settings-module__fields-group"},e.createElement(c.Select,{className:"googlesitekit-sign-in-with-google__select-button-text",label:Object(i.__)("Button text","google-site-kit"),value:n,onEnhancedChange:m,enhanced:!0,outlined:!0},o.d.map((function(t){return e.createElement(c.Option,{key:t.value,value:t.value},t.label)}))))}}).call(this,n(4))},929:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ButtonThemeSelect}));var r=n(0),i=n(2),a=n(3),o=n(110),c=n(10),s=n(18),l=n(9),u=n(121);function ButtonThemeSelect(){var t=Object(s.a)(),n=Object(a.useSelect)((function(e){return e(o.b).getTheme()})),g=Object(a.useDispatch)(o.b).setTheme,d=Object(r.useCallback)((function(){Object(l.I)("".concat(t,"_sign-in-with-google-settings"),"change_button_theme")}),[t]),f=Object(u.a)(d,500),m=Object(r.useCallback)((function(e,t){var r=t.dataset.value;r!==n&&(g(r),f())}),[n,g,f]);return e.createElement("div",{className:"googlesitekit-settings-module__fields-group"},e.createElement(c.Select,{className:"googlesitekit-sign-in-with-google__select-button-theme",label:Object(i.__)("Button theme","google-site-kit"),value:n,onEnhancedChange:m,enhanced:!0,outlined:!0},o.e.map((function(t){return e.createElement(c.Option,{key:t.value,value:t.value},t.label)}))))}}).call(this,n(4))},930:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ButtonShapeSelect}));var r=n(0),i=n(2),a=n(3),o=n(110),c=n(10),s=n(18),l=n(9),u=n(121);function ButtonShapeSelect(){var t=Object(s.a)(),n=Object(a.useSelect)((function(e){return e(o.b).getShape()})),g=Object(a.useDispatch)(o.b).setShape,d=Object(r.useCallback)((function(){Object(l.I)("".concat(t,"_sign-in-with-google-settings"),"change_button_shape")}),[t]),f=Object(u.a)(d,500),m=Object(r.useCallback)((function(e,t){var r=t.dataset.value;r!==n&&(g(r),f())}),[n,g,f]);return e.createElement("div",{className:"googlesitekit-settings-module__fields-group"},e.createElement(c.Select,{className:"googlesitekit-sign-in-with-google__select-button-shape",label:Object(i.__)("Button shape","google-site-kit"),value:n,onEnhancedChange:m,enhanced:!0,outlined:!0},o.c.map((function(t){return e.createElement(c.Option,{key:t.value,value:t.value},t.label)}))))}}).call(this,n(4))},931:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AnyoneCanRegisterReadOnly}));var r=n(38),i=n(0),a=n(2),o=n(3),c=n(7),s=n(13),l=n(24),u=n(10),g=n(20);function AnyoneCanRegisterReadOnly(){var t=Object(l.e)(),n=Object(o.useSelect)((function(e){return e(s.c).getAnyoneCanRegister()})),d=Object(o.useSelect)((function(e){return e(c.a).hasCapability(c.K)})),f=Object(o.useSelect)((function(e){return e(s.c).isMultisite()})),m=Object(o.useSelect)((function(e){return e(s.c).getAdminSettingsURL()}));return e.createElement("div",{className:"googlesitekit-settings-module__fields-group googlesitekit-settings-module__fields-group--read-only"},e.createElement("span",null,Object(a.__)("User registration","google-site-kit")),n&&e.createElement(u.HelperText,{persistent:!0},Object(r.a)(Object(a.sprintf)(
/* translators: %s: Sign in with Google service name */
Object(a.__)("Users can create new accounts on this site using %s. <br/>Visit <a>WordPress settings</a> to manage this membership setting.","google-site-kit"),Object(a._x)("Sign in with Google","Service name","google-site-kit")),{a:!d&&f?e.createElement("span",null):e.createElement(g.a,{key:"link",href:m}),br:t!==l.b?e.createElement("br",null):e.createElement(i.Fragment,null)})),!1===n&&e.createElement(u.HelperText,{persistent:!0},Object(a.sprintf)(
/* translators: %s: Sign in with Google service name */
Object(a.__)("Only existing users can use %s to access their accounts.","google-site-kit"),Object(a._x)("Sign in with Google","Service name","google-site-kit"))))}}).call(this,n(4))},932:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNotice}));var r=n(0),i=n(3),a=n(13),o=n(762),c=n(761),s=n(110);function SettingsNotice(t){var n=t.className,l=Object(i.useSelect)((function(e){return e(a.c).getAnyoneCanRegister()})),u=Object(i.useSelect)((function(e){return e(s.b).getOneTapEnabled()})),g=Object(i.useSelect)((function(e){return e(s.b).getOneTapOnAllPages()})),d=Object(i.useSelect)((function(e){return e(s.b).getIsWooCommerceActive()})),f=Object(i.useSelect)((function(e){return e(s.b).getIsWooCommerceRegistrationEnabled()})),m=u&&g&&!1===l&&!1===d;return d&&(m=u&&g&&!1===l&&!1===f),e.createElement(r.Fragment,null,!1===l&&!m&&e.createElement(c.a,{className:n}),m&&e.createElement(o.a,{className:n}))}}).call(this,n(4))},933:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OneTapToggles}));var r=n(0),i=n(38),a=n(2),o=n(10),c=n(3),s=n(110),l=n(20),u=n(18),g=n(9),d=n(13);function OneTapToggles(){var t=Object(u.a)(),n=Object(c.useSelect)((function(e){return e(s.b).getOneTapEnabled()})),f=Object(c.useSelect)((function(e){return e(s.b).getOneTapOnAllPages()})),m=Object(c.useDispatch)(s.b),p=m.setOneTapEnabled,v=m.setOneTapOnAllPages,b=Object(r.useCallback)((function(){Object(g.I)("".concat(t,"_sign-in-with-google-settings"),n?"disable_one_tap":"enable_one_tap")}),[t,n]),h=Object(r.useCallback)((function(){Object(g.I)("".concat(t,"_sign-in-with-google-settings"),f?"disable_one_tap_on_all_pages":"enable_one_tap_on_all_pages")}),[t,f]),O=Object(r.useCallback)((function(){p(!n),b()}),[n,p,b]),y=Object(r.useCallback)((function(){v(!f),h()}),[f,v,h]),_=Object(c.useSelect)((function(e){return e(d.c).getDocumentationLinkURL("sign-in-with-google-one-tap")}));return e.createElement(r.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-module__fields-group googlesitekit-settings-module__one-tap"},e.createElement(o.Switch,{label:Object(a.__)("Enable One Tap sign in","google-site-kit"),checked:n,onClick:O,hideLabel:!1}),e.createElement(o.HelperText,{persistent:!0},Object(i.a)(Object(a.__)("One Tap allows users to sign in or sign up with one click offering smooth user entry points without redirecting to a dedicated sign in and sign up page. <a>Learn more</a>","google-site-kit"),{a:e.createElement(l.a,{key:"link",href:_,external:!0})}))),e.createElement("div",{className:"googlesitekit-settings-module__fields-group googlesitekit-settings-module__one-tap-on-all-pages"},e.createElement(o.Switch,{label:Object(a.__)("One Tap enabled on all pages","google-site-kit"),checked:!!f,onClick:y,disabled:!n,hideLabel:!1}),e.createElement(o.HelperText,{persistent:!0},f?Object(a.__)("One Tap will be available on every page on this website.","google-site-kit"):Object(a.__)("One Tap will only appear on existing login pages.","google-site-kit"))))}}).call(this,n(4))},934:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return Preview}));var i=n(15),a=n.n(i),o=n(0),c=n(2),s=n(3),l=n(110);function Preview(){var t=Object(o.useState)(!1),n=a()(t,2),i=n[0],u=n[1],g=Object(o.useRef)(),d=Object(s.useSelect)((function(e){return e(l.b).getShape()})),f=Object(s.useSelect)((function(e){return e(l.b).getText()})),m=Object(s.useSelect)((function(e){return e(l.b).getTheme()}));return Object(o.useEffect)((function(){var t=document.createElement("script"),n=function(){u(!0),e.google.accounts.id.initialize({client_id:"notrealclientid"})};return t.src="https://accounts.google.com/gsi/client",t.addEventListener("load",n),document.body.appendChild(t),function(){u(!1),t.removeEventListener("load",n),document.body.removeChild(t)}}),[u]),Object(o.useEffect)((function(){i&&e.google.accounts.id.renderButton(g.current,{text:f,theme:m,shape:d})}),[i,g,f,m,d]),r.createElement("div",{className:"googlesitekit-sign-in-with-google__preview"},r.createElement("p",{className:"googlesitekit-sign-in-with-google__preview--label"},Object(c.__)("Preview","google-site-kit")),r.createElement("div",{ref:g}),r.createElement("div",{className:"googlesitekit-sign-in-with-google__preview--protector"}))}}).call(this,n(28),n(4))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o}));var r=n(239),i=n(85),a=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=n.invertColor,o=void 0!==a&&a;return Object(r.a)(e.createElement(i.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g}));var r=n(6),i=n.n(r),a=n(14),o=n(100),c=n(101);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function g(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=l(l({},u),t);i.referenceSiteURL&&(i.referenceSiteURL=i.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(i,n),g=Object(c.a)(i,n,s,r),d={},f=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);d[r]||(d[r]=Object(a.once)(g)),d[r].apply(d,t)};return{enableTracking:function(){i.trackingEnabled=!0},disableTracking:function(){i.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!i.trackingEnabled},trackEvent:g,trackEventOnce:f}}}).call(this,n(28))}},[[1298,1,0]]]);