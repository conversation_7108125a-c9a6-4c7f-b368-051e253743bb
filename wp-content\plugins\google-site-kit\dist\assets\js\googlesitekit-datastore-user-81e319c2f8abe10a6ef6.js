(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[13],{100:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return s}));var n=r(59),i=r(39),o=r(57);function s(t,r){var s,a=Object(n.a)(r),c=t.activeModules,u=t.referenceSiteURL,l=t.userIDHash,g=t.userRoles,f=void 0===g?[]:g,d=t.isAuthenticated,p=t.pluginVersion;return function(){var r=e.document;if(void 0===s&&(s=!!r.querySelector("script[".concat(i.b,"]"))),!s){s=!0;var n=(null==f?void 0:f.length)?f.join(","):"";a("js",new Date),a("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:u,plugin_version:p||"",enabled_features:Array.from(o.a).join(","),active_modules:c.join(","),authenticated:d?"1":"0",user_properties:{user_roles:n,user_identifier:l}});var g=r.createElement("script");return g.setAttribute(i.b,""),g.async=!0,g.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a),r.head.appendChild(g),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a)}}}}}).call(this,r(28))},101:function(e,t,r){"use strict";r.d(t,"a",(function(){return f}));var n=r(5),i=r.n(n),o=r(6),s=r.n(o),a=r(16),c=r.n(a),u=r(59);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function f(e,t,r,n){var o=Object(u.a)(t);return function(){var t=c()(i.a.mark((function t(s,a,c,u){var l;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return r(),l={send_to:"site_kit",event_category:s,event_label:c,value:u},t.abrupt("return",new Promise((function(e){var t,r,i=setTimeout((function(){n.console.warn('Tracking event "'.concat(a,'" (category "').concat(s,'") took too long to fire.')),e()}),1e3),c=function(){clearTimeout(i),e()};o("event",a,g(g({},l),{},{event_callback:c})),(null===(t=n._gaUserPrefs)||void 0===t||null===(r=t.ioo)||void 0===r?void 0:r.call(t))&&c()})));case 6:case"end":return t.stop()}}),t)})));return function(e,r,n,i){return t.apply(this,arguments)}}()}},1210:function(e,t,r){"use strict";var n,i=r(16),o=r.n(i),s=r(5),a=r.n(s),c=r(6),u=r.n(c),l=r(966),g=r.n(l),f=r(12),d=r.n(f),p=r(14),v=r(45),b=r.n(v),m=r(3),y=r(48),O=r(13),h=r(7),S=r(37),_=r(62);function j(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return E(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return E(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){a=!0,o=e},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function k(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?k(Object(r),!0).forEach((function(t){u()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var A=m.commonActions.getRegistry,T=Object(y.a)({baseName:"getDismissedTours",controlCallback:function(){return b.a.get("core","user","dismissed-tours",{},{useCache:!1})},reducerCallback:function(e,t){return w(w({},e),{},{dismissedTourSlugs:t})}}),I=Object(y.a)({baseName:"dismissTour",controlCallback:function(e){var t=e.slug;return b.a.set("core","user","dismiss-tour",{slug:t})},reducerCallback:function(e,t){return w(w({},e),{},{dismissedTourSlugs:t})},argsToParams:function(e){return{slug:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.slug;d()(t,"slug is required.")}}),D={lastDismissedAt:void 0,dismissedTourSlugs:void 0,tours:[],currentTour:void 0,shownTour:void 0},C={dismissTour:Object(_.f)((function(e){d()(e,"A tour slug is required to dismiss a tour.")}),a.a.mark((function e(t){var r,n,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,A();case 2:if(r=e.sent,!(n=r.select)(h.a).isFetchingDismissTour(t)){e.next=7;break}return i=n(h.a).getDismissedFeatureTourSlugs(),e.abrupt("return",{response:i,error:void 0});case 7:return e.next=9,{type:"DISMISS_TOUR",payload:{slug:t}};case 9:return e.next=11,U.setLastDismissedAt(Date.now());case 11:return e.next=13,I.actions.fetchDismissTour(t);case 13:return e.abrupt("return",e.sent);case 14:case"end":return e.stop()}}),e)}))),receiveCurrentTour:function(e){return d()(Object(p.isPlainObject)(e)||Object(p.isNull)(e),"tour must be a plain object or null."),{payload:{tour:e},type:"RECEIVE_CURRENT_TOUR"}},receiveFeatureToursForView:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.viewContext;return d()(Array.isArray(e),"viewTours must be an array."),d()(r,"viewContext is required."),{payload:{viewTours:e,viewContext:r},type:"RECEIVE_READY_TOURS"}},receiveAllFeatureTours:function(e){return d()(Array.isArray(e),"tours must be an array."),{payload:{tours:e},type:"RECEIVE_TOURS"}},receiveLastDismissedAt:function(e){return d()(void 0!==e,"A timestamp is required."),{type:"RECEIVE_LAST_DISMISSED_AT",payload:{timestamp:e}}},setLastDismissedAt:Object(_.f)((function(e){d()(e,"A timestamp is required.")}),a.a.mark((function e(t){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,A();case 2:return e.sent.dispatch(O.c).setCacheItem("feature_tour_last_dismissed_at",t,{ttl:7200}),e.next=6,{type:"RECEIVE_LAST_DISMISSED_AT",payload:{timestamp:t}};case 6:case"end":return e.stop()}}),e)}))),triggerTour:a.a.mark((function e(t){var r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,A();case 2:if(r=e.sent,(0,r.select)(h.a).getCurrentTour()){e.next=7;break}return e.next=7,C.receiveCurrentTour(t);case 7:case"end":return e.stop()}}),e)})),triggerOnDemandTour:a.a.mark((function e(t){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{tour:t},type:"CHECK_ON_DEMAND_TOUR_REQUIREMENTS"};case 2:if(!e.sent){e.next=6;break}return e.next=6,C.triggerTour(t);case 6:case"end":return e.stop()}}),e)})),triggerTourForView:a.a.mark((function e(t){var r,n,i,o,s,c,u;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,A();case 2:return r=e.sent,n=r.select,i=r.resolveSelect,e.next=7,m.commonActions.await(i(h.a).getLastDismissedAt());case 7:if(!n(h.a).areFeatureToursOnCooldown()){e.next=9;break}return e.abrupt("return",{});case 9:o=n(h.a).getAllFeatureTours(),s=j(o),e.prev=11,s.s();case 13:if((c=s.n()).done){e.next=24;break}return u=c.value,e.next=17,{payload:{tour:u,viewContext:t},type:"CHECK_TOUR_REQUIREMENTS"};case 17:if(!e.sent){e.next=22;break}return e.next=21,C.triggerTour(u);case 21:return e.abrupt("return",u);case 22:e.next=13;break;case 24:e.next=29;break;case 26:e.prev=26,e.t0=e.catch(11),s.e(e.t0);case 29:return e.prev=29,s.f(),e.finish(29);case 32:return e.next=34,C.triggerTour(null);case 34:return e.abrupt("return",null);case 35:case"end":return e.stop()}}),e,null,[[11,26,29,32]])}))},R=(n={},u()(n,"CHECK_TOUR_REQUIREMENTS",Object(m.createRegistryControl)((function(e){return function(){var t=o()(a.a.mark((function t(r){var n,i,o,s;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=r.payload,i=n.tour,o=n.viewContext,i.contexts.includes(o)){t.next=4;break}return t.abrupt("return",!1);case 4:return t.next=6,e.resolveSelect(h.a).getInitialSiteKitVersion();case 6:if(s=t.sent){t.next=11;break}return t.abrupt("return",!1);case 11:if(!g.a.compare(s,i.version,">=")){t.next=13;break}return t.abrupt("return",!1);case 13:return t.next=15,e.resolveSelect(h.a).getDismissedFeatureTourSlugs();case 15:if(!e.select(h.a).isTourDismissed(i.slug)){t.next=17;break}return t.abrupt("return",!1);case 17:if(!i.checkRequirements){t.next=21;break}return t.next=20,i.checkRequirements(e);case 20:return t.abrupt("return",!!t.sent);case 21:return t.abrupt("return",!0);case 22:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}))),u()(n,"CHECK_ON_DEMAND_TOUR_REQUIREMENTS",Object(m.createRegistryControl)((function(e){return function(){var t=o()(a.a.mark((function t(r){var n,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=r.payload,i=n.tour,t.next=4,e.resolveSelect(h.a).getDismissedFeatureTourSlugs();case 4:if(!e.select(h.a).isTourDismissed(i.slug)){t.next=6;break}return t.abrupt("return",!1);case 6:if(!i.checkRequirements){t.next=10;break}return t.next=9,i.checkRequirements(e);case 9:return t.abrupt("return",!!t.sent);case 10:return t.abrupt("return",!0);case 11:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}))),n),P={getDismissedFeatureTourSlugs:a.a.mark((function e(){var t,r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,A();case 2:if(t=e.sent,r=t.select,void 0!==r(h.a).getDismissedFeatureTourSlugs()){e.next=8;break}return e.next=8,T.actions.fetchGetDismissedTours();case 8:case"end":return e.stop()}}),e)})),getLastDismissedAt:a.a.mark((function e(){var t,r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m.commonActions.await(Object(S.d)("feature_tour_last_dismissed_at"));case 2:return t=e.sent,r=t.value,e.next=6,U.receiveLastDismissedAt(r||null);case 6:case"end":return e.stop()}}),e)}))},x={getCurrentTour:function(e){return e.currentTour},getShownTour:function(e){return e.shownTour},getDismissedFeatureTourSlugs:function(e){return e.dismissedTourSlugs},getAllFeatureTours:function(e){return e.tours},isTourDismissed:Object(m.createRegistrySelector)((function(e){return function(t,r){var n=e(h.a).getDismissedFeatureTourSlugs();if(void 0!==n)return n.includes(r)}})),getLastDismissedAt:function(e){return e.lastDismissedAt},areFeatureToursOnCooldown:Object(m.createRegistrySelector)((function(e){return function(){var t=e(h.a).getLastDismissedAt();if(void 0!==t){if(null===t)return!1;var r=t+72e5;return Date.now()<r}}}))},N=Object(m.combineStores)({initialState:D,actions:C,controls:R,reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"DISMISS_TOUR":var i,o=n.slug,s=e.dismissedTourSlugs,a=void 0===s?[]:s;return a.includes(o)?e:w(w({},e),{},{currentTour:(null===(i=e.currentTour)||void 0===i?void 0:i.slug)===o?null:e.currentTour,dismissedTourSlugs:a.concat(o)});case"RECEIVE_CURRENT_TOUR":return w(w({},e),{},{currentTour:n.tour,shownTour:n.tour});case"RECEIVE_READY_TOURS":var c=n.viewContext,l=n.viewTours;return w(w({},e),{},{viewTours:w(w({},e.viewTours),{},u()({},c,l))});case"RECEIVE_TOURS":return w(w({},e),{},{tours:n.tours});case"RECEIVE_LAST_DISMISSED_AT":return w(w({},e),{},{lastDismissedAt:n.timestamp});default:return e}},resolvers:P,selectors:x},I,T),U=N.actions,L=N.controls,M=N.initialState,G=N.reducer,F=N.resolvers,V=N.selectors;t.a={actions:U,controls:L,initialState:M,reducer:G,resolvers:F,selectors:V}},1256:function(e,t,r){"use strict";r.r(t);var n=r(3),i=r.n(n),o=r(957);Object(o.a)(i.a)},13:function(e,t,r){"use strict";r.d(t,"c",(function(){return n})),r.d(t,"a",(function(){return i})),r.d(t,"b",(function(){return o}));var n="core/site",i="primary",o="secondary"},145:function(e,t,r){"use strict";r.d(t,"a",(function(){return p}));var n=r(6),i=r.n(n),o=r(2),s=r(7),a=r(13),c=r(8);function u(e,t,r){return e(c.r).hasConversionReportingEvents(this.requiredConversionEventName)||e(s.a).isKeyMetricActive(r)}var l,g=r(26);function f(e,t){return!t||!(!t||!e(c.r).getAdSenseLinked())}function d(e,t){return!t||e(c.r).hasCustomDimensions(this.requiredCustomDimensions)}var p=(l={},i()(l,s.f,{title:Object(o.__)("Top earning pages","google-site-kit"),description:Object(o.__)("Pages that generated the most AdSense revenue","google-site-kit"),infoTooltip:Object(o.__)("Pages that generated the most AdSense revenue","google-site-kit"),displayInSelectionPanel:f,displayInList:f,metadata:{group:g.b.SLUG}}),i()(l,s.y,{title:Object(o.__)("Top recent trending pages","google-site-kit"),description:Object(o.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),infoTooltip:Object(o.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_date"],displayInSelectionPanel:d,displayInWidgetArea:d,displayInList:d,metadata:{group:g.b.SLUG}}),i()(l,s.l,{title:Object(o.__)("Most popular authors by pageviews","google-site-kit"),description:Object(o.__)("Authors whose posts got the most visits","google-site-kit"),infoTooltip:Object(o.__)("Authors whose posts got the most visits","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_author"],displayInSelectionPanel:d,displayInWidgetArea:d,displayInList:d,metadata:{group:g.b.SLUG}}),i()(l,s.p,{title:Object(o.__)("Top categories by pageviews","google-site-kit"),description:Object(o.__)("Categories that your site visitors viewed the most","google-site-kit"),infoTooltip:Object(o.__)("Categories that your site visitors viewed the most","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_categories"],displayInSelectionPanel:d,displayInWidgetArea:d,displayInList:d,metadata:{group:g.b.SLUG}}),i()(l,s.m,{title:Object(o.__)("Most popular content by pageviews","google-site-kit"),description:Object(o.__)("Pages that brought in the most visitors","google-site-kit"),infoTooltip:Object(o.__)("Pages your visitors read the most","google-site-kit"),metadata:{group:g.b.SLUG}}),i()(l,s.n,{title:Object(o.__)("Most popular products by pageviews","google-site-kit"),description:Object(o.__)("Products that brought in the most visitors","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_type"],displayInSelectionPanel:function(e){return e(s.a).isKeyMetricActive(s.n)||e(a.c).getProductPostType()},displayInWidgetArea:d,metadata:{group:g.f.SLUG}}),i()(l,s.k,{title:Object(o.__)("Pages per visit","google-site-kit"),description:Object(o.__)("Number of pages visitors viewed per session on average","google-site-kit"),infoTooltip:Object(o.__)("Number of pages visitors viewed per session on average","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(l,s.F,{title:Object(o.__)("Visit length","google-site-kit"),description:Object(o.__)("Average duration of engaged visits","google-site-kit"),infoTooltip:Object(o.__)("Average duration of engaged visits","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(l,s.E,{title:Object(o.__)("Visits per visitor","google-site-kit"),description:Object(o.__)("Average number of sessions per site visitor","google-site-kit"),infoTooltip:Object(o.__)("Average number of sessions per site visitor","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(l,s.i,{title:Object(o.__)("Most engaging pages","google-site-kit"),description:Object(o.__)("Pages with the highest engagement rate","google-site-kit"),infoTooltip:Object(o.__)("Pages with the highest engagement rate","google-site-kit"),metadata:{group:g.b.SLUG}}),i()(l,s.h,{title:Object(o.__)("Least engaging pages","google-site-kit"),description:Object(o.__)("Pages with the highest percentage of visitors that left without engagement with your site","google-site-kit"),infoTooltip:Object(o.__)("Percentage of visitors that left without engagement with your site","google-site-kit"),metadata:{group:g.b.SLUG}}),i()(l,s.z,{title:Object(o.__)("Top pages by returning visitors","google-site-kit"),description:Object(o.__)("Pages that attracted the most returning visitors","google-site-kit"),infoTooltip:Object(o.__)("Pages that attracted the most returning visitors","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(l,s.j,{title:Object(o.__)("New visitors","google-site-kit"),description:Object(o.__)("How many new visitors you got and how the overall audience changed","google-site-kit"),infoTooltip:Object(o.__)("Portion of visitors who visited your site for the first time in this timeframe","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(l,s.o,{title:Object(o.__)("Returning visitors","google-site-kit"),description:Object(o.__)("Portion of people who visited your site more than once","google-site-kit"),infoTooltip:Object(o.__)("Portion of your site’s visitors that returned at least once in this timeframe","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(l,s.A,{title:Object(o.__)("Top traffic source","google-site-kit"),description:Object(o.__)("Channel which brought in the most visitors to your site","google-site-kit"),infoTooltip:Object(o.__)("Channel (e.g. social, paid, search) that brought in the most visitors to your site","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(l,s.B,{title:Object(o.__)("Top traffic source driving add to cart","google-site-kit"),description:Object(o.__)("Channel which brought in the most add to cart events to your site","google-site-kit"),infoTooltip:Object(o.__)("Channel (e.g. social, paid, search) that brought in the most add to cart events to your site","google-site-kit"),requiredConversionEventName:[c.l.ADD_TO_CART],displayInSelectionPanel:u,displayInList:u,metadata:{group:g.f.SLUG}}),i()(l,s.C,{title:Object(o.__)("Top traffic source driving leads","google-site-kit"),description:Object(o.__)("Channel which brought in the most leads to your site","google-site-kit"),infoTooltip:Object(o.__)("Channel (e.g. social, paid, search) that brought in the most leads to your site","google-site-kit"),requiredConversionEventName:[c.l.SUBMIT_LEAD_FORM,c.l.CONTACT,c.l.GENERATE_LEAD],displayInSelectionPanel:u,displayInList:u,metadata:{group:g.e.SLUG}}),i()(l,s.D,{title:Object(o.__)("Top traffic source driving purchases","google-site-kit"),description:Object(o.__)("Channel which brought in the most purchases to your site","google-site-kit"),infoTooltip:Object(o.__)("Channel (e.g. social, paid, search) that brought in the most purchases to your site","google-site-kit"),requiredConversionEventName:[c.l.PURCHASE],displayInSelectionPanel:u,displayInList:u,metadata:{group:g.f.SLUG}}),i()(l,s.g,{title:Object(o.__)("Most engaged traffic source","google-site-kit"),description:Object(o.__)("Visitors coming via this channel spent the most time on your site","google-site-kit"),infoTooltip:Object(o.__)("Channel (e.g. social, paid, search) that brought in the most visitors who had a meaningful engagement with your site","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(l,s.u,{title:Object(o.__)("Top converting traffic source","google-site-kit"),description:Object(o.__)("Channel which brought in the most visits that resulted in conversions","google-site-kit"),infoTooltip:Object(o.__)("Channel (e.g. social, paid, search) that brought in visitors who generated the most conversions","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(l,s.q,{title:Object(o.__)("Top cities driving traffic","google-site-kit"),description:Object(o.__)("Which cities you get the most visitors from","google-site-kit"),infoTooltip:Object(o.__)("The cities where most of your visitors came from","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(l,s.s,{title:Object(o.__)("Top cities driving leads","google-site-kit"),description:Object(o.__)("Cities driving the most contact form submissions","google-site-kit"),infoTooltip:Object(o.__)("Cities driving the most contact form submissions","google-site-kit"),requiredConversionEventName:[c.l.SUBMIT_LEAD_FORM,c.l.CONTACT,c.l.GENERATE_LEAD],displayInSelectionPanel:u,displayInList:u,metadata:{group:g.e.SLUG}}),i()(l,s.r,{title:Object(o.__)("Top cities driving add to cart","google-site-kit"),description:Object(o.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),infoTooltip:Object(o.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),requiredConversionEventName:[c.l.ADD_TO_CART],displayInSelectionPanel:u,displayInList:u,metadata:{group:g.f.SLUG}}),i()(l,s.t,{title:Object(o.__)("Top cities driving purchases","google-site-kit"),description:Object(o.__)("Cities driving the most purchases","google-site-kit"),infoTooltip:Object(o.__)("Cities driving the most purchases","google-site-kit"),requiredConversionEventName:[c.l.PURCHASE],displayInSelectionPanel:u,displayInList:u,metadata:{group:g.f.SLUG}}),i()(l,s.w,{title:Object(o.__)("Top device driving purchases","google-site-kit"),description:Object(o.__)("Top device driving the most purchases","google-site-kit"),infoTooltip:Object(o.__)("Top device driving the most purchases","google-site-kit"),requiredConversionEventName:[c.l.PURCHASE],displayInSelectionPanel:u,displayInList:u,metadata:{group:g.f.SLUG}}),i()(l,s.v,{title:Object(o.__)("Top countries driving traffic","google-site-kit"),description:Object(o.__)("Which countries you get the most visitors from","google-site-kit"),infoTooltip:Object(o.__)("The countries where most of your visitors came from","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(l,s.G,{title:Object(o.__)("Top performing keywords","google-site-kit"),description:Object(o.__)("What people searched for before they came to your site","google-site-kit"),infoTooltip:Object(o.__)("The top search queries for your site by highest clickthrough rate","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(l,s.x,{title:Object(o.__)("Top pages driving leads","google-site-kit"),description:Object(o.__)("Pages on which forms are most frequently submitted","google-site-kit"),requiredConversionEventName:[c.l.SUBMIT_LEAD_FORM,c.l.CONTACT,c.l.GENERATE_LEAD],displayInSelectionPanel:u,displayInList:u,metadata:{group:g.e.SLUG}}),l)},176:function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));var n=r(374);function i(e){return Object(n.a)(e)}},19:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return i}));var n="core/modules",i="insufficient_module_dependencies"},2:function(e,t){e.exports=googlesitekit.i18n},26:function(e,t,r){"use strict";r.d(t,"l",(function(){return i})),r.d(t,"k",(function(){return o})),r.d(t,"j",(function(){return s})),r.d(t,"i",(function(){return a})),r.d(t,"a",(function(){return c})),r.d(t,"o",(function(){return u})),r.d(t,"n",(function(){return l})),r.d(t,"m",(function(){return g})),r.d(t,"c",(function(){return f})),r.d(t,"g",(function(){return d})),r.d(t,"h",(function(){return p})),r.d(t,"d",(function(){return v})),r.d(t,"e",(function(){return b})),r.d(t,"f",(function(){return m})),r.d(t,"b",(function(){return y}));var n=r(2),i="key-metrics-setup-cta-widget",o="googlesitekit-key-metrics-selection-panel-opened",s="key-metrics-selection-form",a="key-metrics-selected",c="key-metrics-effective-selection",u="key-metrics-unstaged-selection",l=2,g=8,f={SLUG:"current-selection",LABEL:Object(n.__)("Current selection","google-site-kit")},d={SLUG:"suggested",LABEL:Object(n.__)("Suggested","google-site-kit")},p={SLUG:"visitors",LABEL:Object(n.__)("Visitors","google-site-kit")},v={SLUG:"driving-traffic",LABEL:Object(n.__)("Driving traffic","google-site-kit")},b={SLUG:"generating-leads",LABEL:Object(n.__)("Generating leads","google-site-kit")},m={SLUG:"selling-products",LABEL:Object(n.__)("Selling products","google-site-kit")},y={SLUG:"content-performance",LABEL:Object(n.__)("Content performance","google-site-kit")}},3:function(e,t){e.exports=googlesitekit.data},36:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return h})),r.d(t,"b",(function(){return y})),r.d(t,"c",(function(){return O}));var n=r(99),i=e._googlesitekitTrackingData||{},o=i.activeModules,s=void 0===o?[]:o,a=i.isSiteKitScreen,c=i.trackingEnabled,u=i.trackingID,l=i.referenceSiteURL,g=i.userIDHash,f=i.isAuthenticated,d={activeModules:s,trackingEnabled:c,trackingID:u,referenceSiteURL:l,userIDHash:g,isSiteKitScreen:a,userRoles:i.userRoles,isAuthenticated:f,pluginVersion:"1.151.0"},p=Object(n.a)(d),v=p.enableTracking,b=p.disableTracking,m=(p.isTrackingEnabled,p.initializeSnippet),y=p.trackEvent,O=p.trackEventOnce;function h(e){e?v():b()}a&&c&&m()}).call(this,r(28))},37:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return g})),r.d(t,"d",(function(){return y})),r.d(t,"f",(function(){return O})),r.d(t,"c",(function(){return h})),r.d(t,"e",(function(){return S})),r.d(t,"b",(function(){return _}));var n=r(5),i=r.n(n),o=r(16),s=r.n(o),a=(r(27),r(9));function c(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){a=!0,o=e},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var l,g="googlesitekit_",f="".concat(g).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),d=["sessionStorage","localStorage"],p=[].concat(d),v=function(){var t=s()(i.a.mark((function t(r){var n,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e[r]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,o="__storage_test__",n.setItem(o,o),n.removeItem(o),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==n.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function b(){return m.apply(this,arguments)}function m(){return(m=s()(i.a.mark((function t(){var r,n,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===l){t.next=2;break}return t.abrupt("return",l);case 2:r=c(p),t.prev=3,r.s();case 5:if((n=r.n()).done){t.next=15;break}if(o=n.value,!l){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,v(o);case 11:if(!t.sent){t.next=13;break}l=e[o];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),r.e(t.t0);case 20:return t.prev=20,r.f(),t.finish(20);case 23:return void 0===l&&(l=null),t.abrupt("return",l);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var y=function(){var e=s()(i.a.mark((function e(t){var r,n,o,s,a,c,u;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!(r=e.sent)){e.next=10;break}if(!(n=r.getItem("".concat(f).concat(t)))){e.next=10;break}if(o=JSON.parse(n),s=o.timestamp,a=o.ttl,c=o.value,u=o.isError,!s||a&&!(Math.round(Date.now()/1e3)-s<a)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:c,isError:u});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),O=function(){var t=s()(i.a.mark((function t(r,n){var o,s,c,u,l,g,d,p,v=arguments;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o=v.length>2&&void 0!==v[2]?v[2]:{},s=o.ttl,c=void 0===s?a.b:s,u=o.timestamp,l=void 0===u?Math.round(Date.now()/1e3):u,g=o.isError,d=void 0!==g&&g,t.next=3,b();case 3:if(!(p=t.sent)){t.next=14;break}return t.prev=5,p.setItem("".concat(f).concat(r),JSON.stringify({timestamp:l,ttl:c,value:n,isError:d})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,r){return t.apply(this,arguments)}}(),h=function(){var t=s()(i.a.mark((function t(r){var n,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(n=t.sent)){t.next=14;break}return t.prev=4,o=r.startsWith(g)?r:"".concat(f).concat(r),n.removeItem(o),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),S=function(){var t=s()(i.a.mark((function t(){var r,n,o,s;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(r=t.sent)){t.next=14;break}for(t.prev=4,n=[],o=0;o<r.length;o++)0===(s=r.key(o)).indexOf(g)&&n.push(s);return t.abrupt("return",n);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),_=function(){var e=s()(i.a.mark((function e(){var t,r,n,o;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!e.sent){e.next=25;break}return e.next=6,S();case 6:t=e.sent,r=c(t),e.prev=8,r.s();case 10:if((n=r.n()).done){e.next=16;break}return o=n.value,e.next=14,h(o);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),r.e(e.t0);case 21:return e.prev=21,r.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,r(28))},39:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return i}));var n="_googlesitekitDataLayer",i="data-googlesitekit-gtag"},426:function(e,t,r){"use strict";function n(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e.reduce((function(e,t,n){return e+t+encodeURIComponent(r[n]||"")}),"")}r.d(t,"a",(function(){return n}))},45:function(e,t){e.exports=googlesitekit.api},47:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){return o}));var n={BOXES:"boxes",COMPOSITE:"composite"},i={QUARTER:"quarter",HALF:"half",FULL:"full"},o="core/widgets"},48:function(e,t,r){"use strict";r.d(t,"a",(function(){return h}));var n=r(5),i=r.n(n),o=r(6),s=r.n(o),a=r(12),c=r.n(a),u=r(14),l=r(64),g=r(82),f=r(9);function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var v=function(e){return e},b=function(){return{}},m=function(){},y=l.a.clearError,O=l.a.receiveError,h=function(e){var t,r,n=i.a.mark(N),o=e.baseName,a=e.controlCallback,l=e.reducerCallback,d=void 0===l?v:l,h=e.argsToParams,S=void 0===h?b:h,_=e.validateParams,j=void 0===_?m:_;c()(o,"baseName is required."),c()("function"==typeof a,"controlCallback is required and must be a function."),c()("function"==typeof d,"reducerCallback must be a function."),c()("function"==typeof S,"argsToParams must be a function."),c()("function"==typeof j,"validateParams must be a function.");try{j(S()),r=!1}catch(e){r=!0}var E=Object(g.b)(o),k=Object(g.a)(o),w="FETCH_".concat(k),A="START_".concat(w),T="FINISH_".concat(w),I="CATCH_".concat(w),D="RECEIVE_".concat(k),C="fetch".concat(E),R="receive".concat(E),P="isFetching".concat(E),x=s()({},P,{});function N(e,t){var r,s;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,{payload:{params:e},type:A};case 2:return n.next=4,y(o,t);case 4:return n.prev=4,n.next=7,{payload:{params:e},type:w};case 7:return r=n.sent,n.next=10,U[R](r,e);case 10:return n.next=12,{payload:{params:e},type:T};case 12:n.next=21;break;case 14:return n.prev=14,n.t0=n.catch(4),s=n.t0,n.next=19,O(s,o,t);case 19:return n.next=21,{payload:{params:e},type:I};case 21:return n.abrupt("return",{response:r,error:s});case 22:case"end":return n.stop()}}),n,null,[[4,14]])}var U=(t={},s()(t,C,(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=S.apply(void 0,t);return j(n),N(n,t)})),s()(t,R,(function(e,t){return c()(void 0!==e,"response is required."),r?(c()(Object(u.isPlainObject)(t),"params is required."),j(t)):t={},{payload:{response:e,params:t},type:D}})),t),L=s()({},w,(function(e){var t=e.payload;return a(t.params)})),M=s()({},P,(function(e){if(void 0===e[P])return!1;var t;try{for(var r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];t=S.apply(void 0,n),j(t)}catch(e){return!1}return!!e[P][Object(f.H)(t)]}));return{initialState:x,actions:U,controls:L,reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case A:var i=n.params;return p(p({},e),{},s()({},P,p(p({},e[P]),{},s()({},Object(f.H)(i),!0))));case D:var o=n.response,a=n.params;return d(e,o,a);case T:var c=n.params;return p(p({},e),{},s()({},P,p(p({},e[P]),{},s()({},Object(f.H)(c),!1))));case I:var u=n.params;return p(p({},e),{},s()({},P,p(p({},e[P]),{},s()({},Object(f.H)(u),!1))));default:return e}},resolvers:{},selectors:M}}},561:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return v}));var n=r(5),i=r.n(n),o=r(6),s=r.n(o),a=r(12),c=r.n(a),u=r(45),l=r.n(u),g=r(3),f=r(48);function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var v=function(t,r,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=o.client,u=void 0===a||a,d=o.server,v=void 0===d||d,b=o.storeName,m=void 0===b?void 0:b;c()(t,"type is required."),c()(r,"identifier is required."),c()(n,"datapoint is required.");var y=m||"".concat(t,"/").concat(r),O={serverNotifications:v?void 0:{},clientNotifications:u?void 0:{}},h=Object(f.a)({baseName:"getNotifications",controlCallback:function(){return l.a.get(t,r,n)},reducerCallback:function(e,t){return p(p({},e),{},{serverNotifications:t.reduce((function(e,t){return p(p({},e),{},s()({},t.id,t))}),{})})}}),S={addNotification:function(e){return c()(e,"notification is required."),{payload:{notification:e},type:"ADD_NOTIFICATION"}},removeNotification:function(e){return c()(e,"id is required."),{payload:{id:e},type:"REMOVE_NOTIFICATION"}}},_={},j=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:O,r=arguments.length>1?arguments[1]:void 0,n=r.type,i=r.payload;switch(n){case"ADD_NOTIFICATION":var o=i.notification;return p(p({},t),{},{clientNotifications:p(p({},t.clientNotifications||{}),{},s()({},o.id,o))});case"REMOVE_NOTIFICATION":var a=i.id;if(void 0===t.clientNotifications||void 0===t.clientNotifications[a])return void 0!==t.serverNotifications&&void 0!==t.serverNotifications[a]&&e.console.warn('Cannot remove server-side notification with ID "'.concat(a,'"; this may be changed in a future release.')),t;var c=p({},t.clientNotifications);return delete c[a],p(p({},t),{},{clientNotifications:c});default:return t}},E={getNotifications:i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,g.commonActions.getRegistry();case 2:if(t=e.sent,t.select(y).getNotifications()){e.next=7;break}return e.next=7,h.actions.fetchGetNotifications();case 7:case"end":return e.stop()}}),e)}))};v||delete E.getNotifications;var k={getNotifications:function(e){var t=e.serverNotifications,r=e.clientNotifications;return void 0===t&&void 0===r?t:Object.values(p(p({},t||{}),r||{}))}},w=Object(g.combineStores)(h,{initialState:O,actions:S,controls:_,reducer:j,resolvers:E,selectors:k});return p(p({},w),{},{STORE_NAME:y})}}).call(this,r(28))},57:function(e,t,r){"use strict";(function(e){var n,i;r.d(t,"a",(function(){return o})),r.d(t,"b",(function(){return s}));var o=new Set((null===(n=e)||void 0===n||null===(i=n._googlesitekitBaseData)||void 0===i?void 0:i.enabledFeatures)||[]),s=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o;return t instanceof Set&&t.has(e)}}).call(this,r(28))},59:function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));var n=r(39);function i(e){return function(){e[n.a]=e[n.a]||[],e[n.a].push(arguments)}}},62:function(e,t,r){"use strict";r.d(t,"a",(function(){return w})),r.d(t,"b",(function(){return A})),r.d(t,"c",(function(){return T})),r.d(t,"d",(function(){return D})),r.d(t,"e",(function(){return C})),r.d(t,"g",(function(){return P})),r.d(t,"f",(function(){return x}));var n,i=r(5),o=r.n(i),s=r(27),a=r.n(s),c=r(6),u=r.n(c),l=r(12),g=r.n(l),f=r(63),d=r.n(f),p=r(14),v=r(116);function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){u()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var y=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.reduce((function(e,t){return m(m({},e),t)}),{}),i=t.reduce((function(e,t){return[].concat(a()(e),a()(Object.keys(t)))}),[]),o=I(i);return g()(0===o.length,"collect() cannot accept collections with duplicate keys. Your call to collect() contains the following duplicated functions: ".concat(o.join(", "),". Check your data stores for duplicates.")),n},O=y,h=y,S=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n,i=[].concat(t);return"function"!=typeof i[0]&&(n=i.shift()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i.reduce((function(e,r){return r(e,t)}),e)}},_=y,j=y,E=y,k=function(e){return e},w=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=E.apply(void 0,a()(t.map((function(e){return e.initialState||{}}))));return{initialState:n,controls:h.apply(void 0,a()(t.map((function(e){return e.controls||{}})))),actions:O.apply(void 0,a()(t.map((function(e){return e.actions||{}})))),reducer:S.apply(void 0,[n].concat(a()(t.map((function(e){return e.reducer||k}))))),resolvers:_.apply(void 0,a()(t.map((function(e){return e.resolvers||{}})))),selectors:j.apply(void 0,a()(t.map((function(e){return e.selectors||{}}))))}},A={getRegistry:function(){return{payload:{},type:"GET_REGISTRY"}},await:o.a.mark((function e(t){return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{payload:{value:t},type:"AWAIT"});case 1:case"end":return e.stop()}}),e)}))},T=(n={},u()(n,"GET_REGISTRY",Object(v.a)((function(e){return function(){return e}}))),u()(n,"AWAIT",(function(e){return e.payload.value})),n),I=function(e){for(var t=[],r={},n=0;n<e.length;n++){var i=e[n];r[i]=r[i]>=1?r[i]+1:1,r[i]>1&&t.push(i)}return t},D={actions:A,controls:T,reducer:k},C=function(e){return function(t){return R(e(t))}},R=d()((function(e){return Object(p.mapValues)(e,(function(e,t){return function(){var r=e.apply(void 0,arguments);return g()(void 0!==r,"".concat(t,"(...) is not resolved")),r}}))}));function P(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.negate,n=void 0!==r&&r,i=Object(v.b)((function(t){return function(r){var i=!n,o=!!n;try{for(var s=arguments.length,a=new Array(s>1?s-1:0),c=1;c<s;c++)a[c-1]=arguments[c];return e.apply(void 0,[t,r].concat(a)),i}catch(e){return o}}})),o=Object(v.b)((function(t){return function(r){for(var n=arguments.length,i=new Array(n>1?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];e.apply(void 0,[t,r].concat(i))}}));return{safeSelector:i,dangerousSelector:o}}function x(e,t){return g()("function"==typeof e,"a validator function is required."),g()("function"==typeof t,"an action creator function is required."),g()("Generator"!==e[Symbol.toStringTag]&&"GeneratorFunction"!==e[Symbol.toStringTag],"an action’s validator function must not be a generator."),function(){return e.apply(void 0,arguments),t.apply(void 0,arguments)}}},64:function(e,t,r){"use strict";r.d(t,"a",(function(){return b})),r.d(t,"b",(function(){return m}));var n=r(6),i=r.n(n),o=r(33),s=r.n(o),a=r(116),c=r(12),u=r.n(c),l=r(96),g=r.n(l),f=r(9);function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){i()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function v(e,t){if(t&&Array.isArray(t)){var r=t.map((function(e){return"object"===s()(e)?Object(f.H)(e):e}));return"".concat(e,"::").concat(g()(JSON.stringify(r)))}return e}var b={receiveError:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(e,"error is required."),u()(t,"baseName is required."),u()(r&&Array.isArray(r),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:r}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return u()(e,"baseName is required."),u()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function m(e){u()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(r,"selectorName is required."),t.getError(e,r,n)},getErrorForAction:function(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return u()(r,"actionName is required."),t.getError(e,r,n)},getError:function(e,t,r){var n=e.errors;return u()(t,"baseName is required."),n[v(t,r)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var r=Object.keys(e.errors).find((function(r){return e.errors[r]===t}));return r?{baseName:r.substring(0,r.indexOf("::")),args:e.errorArgs[r]}:null},getSelectorDataForError:Object(a.b)((function(t){return function(r,n){var i=t(e).getMetaDataForError(n);if(i){var o=i.baseName,s=i.args;if(!!t(e)[o])return{storeName:e,name:o,args:s}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:b,controls:{},reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"RECEIVE_ERROR":var o=n.baseName,s=n.args,a=n.error,c=v(o,s);return p(p({},e),{},{errors:p(p({},e.errors||{}),{},i()({},c,a)),errorArgs:p(p({},e.errorArgs||{}),{},i()({},c,s))});case"CLEAR_ERROR":var u=n.baseName,l=n.args,g=p({},e),f=v(u,l);return g.errors=p({},e.errors||{}),g.errorArgs=p({},e.errorArgs||{}),delete g.errors[f],delete g.errorArgs[f],g;case"CLEAR_ERRORS":var d=n.baseName,b=p({},e);if(d)for(var m in b.errors=p({},e.errors||{}),b.errorArgs=p({},e.errorArgs||{}),b.errors)(m===d||m.startsWith("".concat(d,"::")))&&(delete b.errors[m],delete b.errorArgs[m]);else b.errors={},b.errorArgs={};return b;default:return e}},resolvers:{},selectors:t}}},7:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return i})),r.d(t,"e",(function(){return o})),r.d(t,"d",(function(){return s})),r.d(t,"c",(function(){return a})),r.d(t,"H",(function(){return c})),r.d(t,"M",(function(){return u})),r.d(t,"O",(function(){return l})),r.d(t,"K",(function(){return g})),r.d(t,"L",(function(){return f})),r.d(t,"J",(function(){return d})),r.d(t,"I",(function(){return p})),r.d(t,"N",(function(){return v})),r.d(t,"f",(function(){return b})),r.d(t,"g",(function(){return m})),r.d(t,"h",(function(){return y})),r.d(t,"j",(function(){return O})),r.d(t,"l",(function(){return h})),r.d(t,"m",(function(){return S})),r.d(t,"n",(function(){return _})),r.d(t,"o",(function(){return j})),r.d(t,"q",(function(){return E})),r.d(t,"s",(function(){return k})),r.d(t,"r",(function(){return w})),r.d(t,"t",(function(){return A})),r.d(t,"w",(function(){return T})),r.d(t,"u",(function(){return I})),r.d(t,"v",(function(){return D})),r.d(t,"x",(function(){return C})),r.d(t,"y",(function(){return R})),r.d(t,"A",(function(){return P})),r.d(t,"B",(function(){return x})),r.d(t,"C",(function(){return N})),r.d(t,"D",(function(){return U})),r.d(t,"k",(function(){return L})),r.d(t,"F",(function(){return M})),r.d(t,"z",(function(){return G})),r.d(t,"G",(function(){return F})),r.d(t,"E",(function(){return V})),r.d(t,"i",(function(){return q})),r.d(t,"p",(function(){return K})),r.d(t,"Q",(function(){return B})),r.d(t,"P",(function(){return H}));var n="core/user",i="connected_url_mismatch",o="__global",s="temporary_persist_permission_error",a="adblocker_active",c="googlesitekit_authenticate",u="googlesitekit_setup",l="googlesitekit_view_dashboard",g="googlesitekit_manage_options",f="googlesitekit_read_shared_module_data",d="googlesitekit_manage_module_sharing_options",p="googlesitekit_delegate_module_sharing_management",v="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",m="kmAnalyticsEngagedTrafficSource",y="kmAnalyticsLeastEngagingPages",O="kmAnalyticsNewVisitors",h="kmAnalyticsPopularAuthors",S="kmAnalyticsPopularContent",_="kmAnalyticsPopularProducts",j="kmAnalyticsReturningVisitors",E="kmAnalyticsTopCities",k="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",A="kmAnalyticsTopCitiesDrivingPurchases",T="kmAnalyticsTopDeviceDrivingPurchases",I="kmAnalyticsTopConvertingTrafficSource",D="kmAnalyticsTopCountries",C="kmAnalyticsTopPagesDrivingLeads",R="kmAnalyticsTopRecentTrendingPages",P="kmAnalyticsTopTrafficSource",x="kmAnalyticsTopTrafficSourceDrivingAddToCart",N="kmAnalyticsTopTrafficSourceDrivingLeads",U="kmAnalyticsTopTrafficSourceDrivingPurchases",L="kmAnalyticsPagesPerVisit",M="kmAnalyticsVisitLength",G="kmAnalyticsTopReturningVisitorPages",F="kmSearchConsolePopularKeywords",V="kmAnalyticsVisitsPerVisitor",q="kmAnalyticsMostEngagingPages",K="kmAnalyticsTopCategories",B=[b,m,y,O,h,S,_,j,K,E,k,w,A,T,I,D,R,P,x,L,M,G,V,q,K],H=[].concat(B,[F])},75:function(e,t,r){"use strict";r.d(t,"a",(function(){return s})),r.d(t,"b",(function(){return a}));var n=r(33),i=r.n(n),o=r(84),s=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:o.a.sanitize(e,t)}};function a(e){var t,r="object"===i()(e)?e.toString():e;return null==r||null===(t=r.replace)||void 0===t?void 0:t.call(r,/\/+$/,"")}},8:function(e,t,r){"use strict";r.d(t,"r",(function(){return n})),r.d(t,"a",(function(){return i})),r.d(t,"s",(function(){return o})),r.d(t,"z",(function(){return s})),r.d(t,"o",(function(){return a})),r.d(t,"q",(function(){return c})),r.d(t,"g",(function(){return u})),r.d(t,"p",(function(){return l})),r.d(t,"j",(function(){return g})),r.d(t,"i",(function(){return f})),r.d(t,"k",(function(){return d})),r.d(t,"m",(function(){return p})),r.d(t,"n",(function(){return v})),r.d(t,"h",(function(){return b})),r.d(t,"x",(function(){return m})),r.d(t,"w",(function(){return y})),r.d(t,"y",(function(){return O})),r.d(t,"u",(function(){return h})),r.d(t,"v",(function(){return S})),r.d(t,"f",(function(){return _})),r.d(t,"l",(function(){return j})),r.d(t,"e",(function(){return E})),r.d(t,"t",(function(){return k})),r.d(t,"c",(function(){return w})),r.d(t,"d",(function(){return A})),r.d(t,"b",(function(){return T}));var n="modules/analytics-4",i="account_create",o="property_create",s="webdatastream_create",a="analyticsSetup",c=10,u=1,l="https://www.googleapis.com/auth/tagmanager.readonly",g="enhanced-measurement-form",f="enhanced-measurement-enabled",d="enhanced-measurement-should-dismiss-activation-banner",p="analyticsAccountCreate",v="analyticsCustomDimensionsCreate",b="https://www.googleapis.com/auth/analytics.edit",m="dashboardAllTrafficWidgetDimensionName",y="dashboardAllTrafficWidgetDimensionColor",O="dashboardAllTrafficWidgetDimensionValue",h="dashboardAllTrafficWidgetActiveRowIndex",S="dashboardAllTrafficWidgetLoaded",_={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},j={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},E=[j.CONTACT,j.GENERATE_LEAD,j.SUBMIT_LEAD_FORM],k={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},w="audiencePermissionsSetup",A="audienceTileCustomDimensionCreate",T="audience-selection-panel-expirable-new-badge-"},80:function(e,t,r){"use strict";r.d(t,"b",(function(){return i})),r.d(t,"a",(function(){return o})),r.d(t,"c",(function(){return s})),r.d(t,"d",(function(){return a}));var n=r(106);function i(e){try{return new URL(e).pathname}catch(e){}return null}function o(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function s(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function a(e,t){if(!Object(n.a)(e))return e;if(e.length<=t)return e;var r=new URL(e),i=e.replace(r.origin,"");if(i.length<t)return i;var o=i.length-Math.floor(t)+1;return"…"+i.substr(o)}},82:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"a",(function(){return i})),r.d(t,"c",(function(){return o}));var n=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},i=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function o(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},83:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return j})),r.d(t,"d",(function(){return E})),r.d(t,"e",(function(){return w})),r.d(t,"c",(function(){return A})),r.d(t,"b",(function(){return T}));var n=r(15),i=r.n(n),o=r(33),s=r.n(o),a=r(6),c=r.n(a),u=r(25),l=r.n(u),g=r(14),f=r(63),d=r.n(f),p=r(2);function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){c()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var m=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=h(e,t),n=r.formatUnit,i=r.formatDecimal;try{return n()}catch(e){return i()}},y=function(e){var t=O(e),r=t.hours,n=t.minutes,i=t.seconds;return i=("0"+i).slice(-2),n=("0"+n).slice(-2),"00"===(r=("0"+r).slice(-2))?"".concat(n,":").concat(i):"".concat(r,":").concat(n,":").concat(i)},O=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},h=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=O(e),n=r.hours,i=r.minutes,o=r.seconds;return{hours:n,minutes:i,seconds:o,formatUnit:function(){var r=t.unitDisplay,s=b(b({unitDisplay:void 0===r?"short":r},l()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(o,b(b({},s),{},{unit:"second"})):Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),o?w(o,b(b({},s),{},{unit:"second"})):"",i?w(i,b(b({},s),{},{unit:"minute"})):"",n?w(n,b(b({},s),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(p.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(p.__)("%ds","google-site-kit"),o);if(0===e)return t;var r=Object(p.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(p.__)("%dm","google-site-kit"),i),s=Object(p.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(p.__)("%dh","google-site-kit"),n);return Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),o?t:"",i?r:"",n?s:"").trim()}}},S=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},_=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in millions.
Object(p.__)("%sM","google-site-kit"),w(S(e),e%10==0?{}:t)):1e4<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(S(e))):1e3<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(S(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function j(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(g.isPlainObject)(e)&&(t=b({},e)),t}function E(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(g.isFinite)(e)?e:Number(e),Object(g.isFinite)(e)||(console.warn("Invalid number",e,s()(e)),e=0);var r=j(t),n=r.style,i=void 0===n?"metric":n;return"metric"===i?_(e):"duration"===i?m(e,r):"durationISO"===i?y(e):w(e,r)}var k=d()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.locale,n=void 0===r?T():r,o=l()(t,["locale"]);try{return new Intl.NumberFormat(n,o).format(e)}catch(t){k("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(n),", ").concat(JSON.stringify(o)," ).format( ").concat(s()(e)," )"),t.message)}for(var a={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},c=["signDisplay","compactDisplay"],u={},g=0,f=Object.entries(o);g<f.length;g++){var d=i()(f[g],2),p=d[0],v=d[1];a[p]&&v===a[p]||(c.includes(p)||(u[p]=v))}try{return new Intl.NumberFormat(n,u).format(e)}catch(t){return new Intl.NumberFormat(n).format(e)}},A=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.locale,n=void 0===r?T():r,i=t.style,o=void 0===i?"long":i,s=t.type,a=void 0===s?"conjunction":s;if(Intl.ListFormat){var c=new Intl.ListFormat(n,{style:o,type:a});return c.format(e)}
/* translators: used between list items, there is a space after the comma. */var u=Object(p.__)(", ","google-site-kit");return e.join(u)},T=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,r=Object(g.get)(t,["_googlesitekitLegacyData","locale"]);if(r){var n=r.match(/^(\w{2})?(_)?(\w{2})/);if(n&&n[0])return n[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,r(28))},84:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return i}));var n=r(149),i=r.n(n)()(e)}).call(this,r(28))},85:function(e,t,r){"use strict";(function(e){var n=r(1),i=r.n(n),o=r(11),s=r.n(o);function ChangeArrow(t){var r=t.direction,n=t.invertColor,i=t.width,o=t.height;return e.createElement("svg",{className:s()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(r),{"googlesitekit-change-arrow--inverted-color":n}),width:i,height:o,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:i.a.string,invertColor:i.a.bool,width:i.a.number,height:i.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,r(4))},89:function(e,t,r){"use strict";r.d(t,"a",(function(){return o}));var n=r(12),i=r.n(n),o=function(e,t){var r=t.dateRangeLength;i()(Array.isArray(e),"report must be an array to partition."),i()(Number.isInteger(r)&&r>0,"dateRangeLength must be a positive integer.");var n=-1*r;return{currentRange:e.slice(n),compareRange:e.slice(2*n,n)}}},9:function(e,t,r){"use strict";r.d(t,"I",(function(){return i.b})),r.d(t,"J",(function(){return i.c})),r.d(t,"F",(function(){return o.a})),r.d(t,"K",(function(){return o.b})),r.d(t,"H",(function(){return l})),r.d(t,"m",(function(){return g.a})),r.d(t,"B",(function(){return g.d})),r.d(t,"C",(function(){return g.e})),r.d(t,"y",(function(){return g.c})),r.d(t,"r",(function(){return g.b})),r.d(t,"z",(function(){return v})),r.d(t,"j",(function(){return b})),r.d(t,"i",(function(){return m})),r.d(t,"d",(function(){return j})),r.d(t,"c",(function(){return E})),r.d(t,"e",(function(){return k})),r.d(t,"b",(function(){return w})),r.d(t,"a",(function(){return A})),r.d(t,"f",(function(){return T})),r.d(t,"n",(function(){return I})),r.d(t,"w",(function(){return D})),r.d(t,"p",(function(){return C})),r.d(t,"G",(function(){return R})),r.d(t,"s",(function(){return P})),r.d(t,"v",(function(){return x})),r.d(t,"k",(function(){return N})),r.d(t,"o",(function(){return U.b})),r.d(t,"h",(function(){return U.a})),r.d(t,"t",(function(){return L.b})),r.d(t,"q",(function(){return L.a})),r.d(t,"A",(function(){return L.c})),r.d(t,"x",(function(){return M})),r.d(t,"u",(function(){return G})),r.d(t,"E",(function(){return q})),r.d(t,"D",(function(){return K.a})),r.d(t,"g",(function(){return B})),r.d(t,"L",(function(){return H})),r.d(t,"l",(function(){return W}));var n=r(14),i=r(36),o=r(75),s=r(33),a=r.n(s),c=r(96),u=r.n(c),l=function(e){return u()(JSON.stringify(function e(t){var r={};return Object.keys(t).sort().forEach((function(n){var i=t[n];i&&"object"===a()(i)&&!Array.isArray(i)&&(i=e(i)),r[n]=i})),r}(e)))};r(97);var g=r(83);function f(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function d(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function p(e){return e.replace(/\n/gi,"<br>")}function v(e){for(var t=e,r=0,n=[f,d,p];r<n.length;r++){t=(0,n[r])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},m=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},y=r(15),O=r.n(y),h=r(12),S=r.n(h),_=r(2),j="Invalid dateString parameter, it must be a string.",E='Invalid date range, it must be a string with the format "last-x-days".',k=60,w=60*k,A=24*w,T=7*A;function I(){var e=function(e){return Object(_.sprintf)(
/* translators: %s: number of days */
Object(_._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function D(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(n.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var r=new Date(e);return Object(n.isDate)(r)&&!isNaN(r)}function C(e){S()(Object(n.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),r="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,r.length<2?"0".concat(r):r].join("-")}function R(e){S()(D(e),j);var t=e.split("-"),r=O()(t,3),n=r[0],i=r[1],o=r[2];return new Date(n,i-1,o)}function P(e,t){return C(N(e,t*A))}function x(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function N(e,t){S()(D(e)||Object(n.isDate)(e)&&!isNaN(e),j);var r=D(e)?Date.parse(e):e.getTime();return new Date(r-1e3*t)}var U=r(98),L=r(80);function M(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function G(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var F=r(27),V=r.n(F),q=function(e){return Array.isArray(e)?V()(e).sort():e},K=r(89);function B(e,t){var r=function(e){return"0"===e||0===e};if(r(e)&&r(t))return 0;if(r(e)||Number.isNaN(e))return null;var n=(t-e)/e;return Number.isNaN(n)||!Number.isFinite(n)?null:n}var H=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},W=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(n.unescape)(t)}},957:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return k}));var n=r(3),i=r(64),o=r(958),s=r(960),a=r(961),c=r(7),u=r(962),l=r(963),g=r(964),f=r(965),d=r(1210),p=r(967),v=r(968),b=r(969),m=r(970),y=r(972),O=r(973),h=r(974),S=r(975),_=r(976),j=r(977),E=Object(n.combineStores)(n.commonStore,Object(i.b)(c.a),o.a,s.a,a.a,u.a,l.a,g.a,f.a,d.a,p.a,v.a,m.a,y.a,b.a,O.a,h.a,S.a,_.a,j.a),k=(E.initialState,E.actions,E.controls,E.reducer,E.resolvers,E.selectors,function(t){var r;t.registerStore(c.a,E),(null===(r=e._googlesitekitBaseData)||void 0===r?void 0:r.referenceDate)&&t.dispatch(c.a).setReferenceDate(e._googlesitekitBaseData.referenceDate)})}).call(this,r(28))},958:function(e,t,r){"use strict";var n=r(6),i=r.n(n),o=r(16),s=r.n(o),a=r(5),c=r.n(a),u=r(12),l=r.n(u),g=r(959),f=r(3),d=r(7);function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach((function(t){i()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var b={isAdBlockerActive:void 0},m={checkAdBlocker:c.a.mark((function e(){return c.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"CHECK_ADBLOCKER"};case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})),receiveIsAdBlockerActive:function(e){return l()("boolean"==typeof e,"isAdBlockerActive must be boolean."),{payload:{isAdBlockerActive:e},type:"RECEIVE_IS_ADBLOCKER_ACTIVE"}}},y=i()({},"CHECK_ADBLOCKER",s()(c.a.mark((function e(){var t;return c.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(g.detectAnyAdblocker)();case 2:if(!e.sent){e.next=4;break}return e.abrupt("return",!0);case 4:return e.prev=4,t=["google-site-kit=/adsense/pagead2.googlesyndication.com/pagead/js/adsbygoogle.js","timestamp=".concat(Date.now())],e.next=8,fetch("/favicon.ico?".concat(t.join("&")),{credentials:"omit",redirect:"manual"});case 8:e.next=13;break;case 10:return e.prev=10,e.t0=e.catch(4),e.abrupt("return",!0);case 13:return e.abrupt("return",!1);case 14:case"end":return e.stop()}}),e,null,[[4,10]])})))),O={isAdBlockerActive:c.a.mark((function(){var e,t,r;return c.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,f.commonActions.getRegistry();case 2:if(e=n.sent,t=e.select(d.a).isAdBlockerActive(),void 0===t){n.next=6;break}return n.abrupt("return");case 6:return n.next=8,m.checkAdBlocker();case 8:return r=n.sent,n.next=11,m.receiveIsAdBlockerActive(r);case 11:case"end":return n.stop()}}),t)}))};t.a={initialState:b,actions:m,controls:y,reducer:function(e,t){var r=t.payload;switch(t.type){case"RECEIVE_IS_ADBLOCKER_ACTIVE":var n=r.isAdBlockerActive;return v(v({},e),{},{isAdBlockerActive:n});default:return e}},resolvers:O,selectors:{isAdBlockerActive:function(e){var t=e.isAdBlockerActive;return t}}}},960:function(e,t,r){"use strict";var n=r(5),i=r.n(n),o=r(27),s=r.n(o),a=r(6),c=r.n(a),u=r(12),l=r.n(u),g=r(14),f=r(45),d=r.n(f),p=r(3),v=r(8),b=r(48),m=r(62),y=r(176),O=r(64),h=r(7);function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach((function(t){c()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var j=O.a.receiveError,E=O.a.clearError,k=Object(y.a)((function(e,t){e.audienceSettings||(e.audienceSettings={}),e.audienceSettings.settings=t,e.audienceSettings.savedSettings=t})),w=Object(b.a)({baseName:"getUserAudienceSettings",controlCallback:function(){return d.a.get("core","user","audience-settings",{},{useCache:!1})},reducerCallback:k}),A=Object(b.a)({baseName:"saveUserAudienceSettings",controlCallback:function(e){return d.a.set("core","user","audience-settings",{settings:e})},reducerCallback:k,argsToParams:function(e){return e},validateParams:function(e){l()(Object(g.isPlainObject)(e),"Audience settings should be an object."),l()(Array.isArray(e.configuredAudiences),"Configured audiences should be an array."),l()("boolean"==typeof e.isAudienceSegmentationWidgetHidden,"Audience segmentation widget visibility should be a boolean.")}}),T={audienceSettings:void 0},I={saveUserAudienceSettings:Object(m.f)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};l()(Object(g.isPlainObject)(e),"audience settings should be an object to save.")}),i.a.mark((function e(){var t,r,n,o,a,c,u,l,g,f=arguments;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=f.length>0&&void 0!==f[0]?f[0]:{},e.next=3,E("saveUserAudienceSettings",[]);case 3:return e.next=5,p.commonActions.getRegistry();case 5:return r=e.sent,e.next=8,p.commonActions.await(r.resolveSelect(h.a).getUserAudienceSettings());case 8:return n=e.sent,o=_(_({},n),t),e.next=12,p.commonActions.await(r.resolveSelect(v.r).getAvailableAudiences());case 12:return a=e.sent,c=s()(o.configuredAudiences).sort((function(e,t){var r=a.findIndex((function(t){return t.name===e})),n=a.findIndex((function(e){return e.name===t}));return-1===r||-1===n?0:r-n})),o.configuredAudiences=c,e.next=17,A.actions.fetchSaveUserAudienceSettings(o);case 17:if(u=e.sent,l=u.response,!(g=u.error)){e.next=23;break}return e.next=23,j(g,"saveUserAudienceSettings",[]);case 23:return e.abrupt("return",{response:l,error:g});case 24:case"end":return e.stop()}}),e)}))),resetUserAudienceSettings:i.a.mark((function e(){var t,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.commonActions.getRegistry();case 2:return t=e.sent,r=t.dispatch,e.next=6,{payload:{},type:"RESET_AUDIENCE_SETTINGS"};case 6:return e.next=8,O.a.clearErrors("getUserAudienceSettings");case 8:return e.abrupt("return",r(h.a).invalidateResolutionForStoreSelector("getUserAudienceSettings"));case 9:case"end":return e.stop()}}),e)})),setConfiguredAudiences:function(e){return l()(Array.isArray(e),"Configured audiences should be an array."),{type:"SET_CONFIGURED_AUDIENCES",payload:{audienceResourceNames:e}}},setAudienceSegmentationWidgetHidden:function(e){return l()("boolean"==typeof e,"Audience segmentation widget visibility should be a boolean."),{type:"SET_AUDIENCE_SEGMENTATION_WIDGET_HIDDEN",payload:{isWidgetHidden:e}}}},D=Object(y.a)((function(e,t){var r=t.type,n=t.payload;switch(r){case"RESET_AUDIENCE_SETTINGS":e.audienceSettings=T.audienceSettings;break;case"SET_CONFIGURED_AUDIENCES":var i=n.audienceResourceNames;e.audienceSettings||(e.audienceSettings={}),e.audienceSettings.settings=_(_({},e.audienceSettings.settings),{},{configuredAudiences:i});break;case"SET_AUDIENCE_SEGMENTATION_WIDGET_HIDDEN":var o=n.isWidgetHidden;e.audienceSettings||(e.audienceSettings={}),e.audienceSettings.settings=_(_({},e.audienceSettings.settings),{},{isAudienceSegmentationWidgetHidden:o})}})),C={getUserAudienceSettings:i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.commonActions.getRegistry();case 2:if(t=e.sent,void 0!==t.select(h.a).getUserAudienceSettings()){e.next=7;break}return e.next=7,w.actions.fetchGetUserAudienceSettings();case 7:case"end":return e.stop()}}),e)}))},R={getUserAudienceSettings:function(e){var t;return null===(t=e.audienceSettings)||void 0===t?void 0:t.settings},getConfiguredAudiences:Object(p.createRegistrySelector)((function(e){return function(){var t=e(h.a).getUserAudienceSettings();return null==t?void 0:t.configuredAudiences}})),isAudienceSegmentationWidgetHidden:Object(p.createRegistrySelector)((function(e){return function(){var t=e(h.a).getUserAudienceSettings();return null==t?void 0:t.isAudienceSegmentationWidgetHidden}})),didSetAudiences:Object(p.createRegistrySelector)((function(e){return function(){var t=e(h.a).getUserAudienceSettings();return null==t?void 0:t.didSetAudiences}})),haveConfiguredAudiencesChanged:function(e){var t=e.audienceSettings||{},r=t.settings,n=t.savedSettings;return!Object(g.isEqual)(null==r?void 0:r.configuredAudiences,null==n?void 0:n.configuredAudiences)},isSavingUserAudienceSettings:function(e){return Object.values(e.isFetchingSaveUserAudienceSettings).some(Boolean)}},P=Object(p.combineStores)(w,A,{initialState:T,actions:I,controls:{},reducer:D,resolvers:C,selectors:R});P.initialState,P.actions,P.controls,P.reducer,P.resolvers,P.selectors;t.a=P},961:function(e,t,r){"use strict";var n=r(5),i=r.n(n),o=r(6),s=r.n(o),a=r(45),c=r.n(a),u=r(3),l=r(7),g=r(48);function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function p(e){return Object(u.createRegistrySelector)((function(t){return function(){return(t(l.a).getAuthentication()||{})[e]}}))}var v=Object(g.a)({baseName:"getAuthentication",controlCallback:function(){return c.a.get("core","user","authentication",void 0,{useCache:!1})},reducerCallback:function(e,t){return d(d({},e),{},{authentication:t})}}),b={authentication:void 0,authError:null},m={setAuthError:function(e){return{payload:{error:e},type:"SET_AUTH_ERROR"}},clearAuthError:function(){return{payload:{},type:"CLEAR_AUTH_ERROR"}}},y={getAuthentication:i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,u.commonActions.getRegistry();case 2:if(t=e.sent,(0,t.select)(l.a).getAuthentication()){e.next=7;break}return e.next=7,v.actions.fetchGetAuthentication();case 7:case"end":return e.stop()}}),e)}))},O={getAuthentication:function(e){return e.authentication},hasScope:Object(u.createRegistrySelector)((function(e){return function(t,r){var n=e(l.a).getGrantedScopes(t);if(void 0!==n)return n.includes(r)}})),isAuthenticated:p("authenticated"),getGrantedScopes:p("grantedScopes"),getRequiredScopes:p("requiredScopes"),getUnsatisfiedScopes:p("unsatisfiedScopes"),needsReauthentication:p("needsReauthentication"),getDisconnectedReason:p("disconnectedReason"),getConnectedProxyURL:p("connectedProxyURL"),getPreviousConnectedProxyURL:p("previousConnectedProxyURL"),getAuthError:function(e){return e.authError}},h=Object(u.combineStores)(v,{initialState:b,actions:m,reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"SET_AUTH_ERROR":return d(d({},e),{},{authError:n.error});case"CLEAR_AUTH_ERROR":return d(d({},e),{},{authError:null});default:return e}},resolvers:y,selectors:O});h.initialState,h.actions,h.controls,h.reducer,h.resolvers,h.selectors;t.a=h},962:function(e,t,r){"use strict";(function(e){var n=r(6),i=r.n(n),o=r(12),s=r.n(o),a=r(9);function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach((function(t){i()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var l={dateRange:"last-28-days",referenceDate:Object(a.p)(new Date)},g={setDateRange:function(e){return s()(e,"Date range slug is required."),s()(Object(a.v)(e),a.c),{type:"SET_DATE_RANGE",payload:{slug:e}}},setReferenceDate:function(e){return s()(e,"Date string is required."),s()(Object(a.w)(e),a.d),{type:"SET_REFERENCE_DATE",payload:{dateString:e}}}};var f={getDateRange:function(e){return e.dateRange},getDateRangeDates:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.compare,i=void 0!==n&&n,o=r.offsetDays,s=r.referenceDate,c=void 0===s?t.referenceDate:s;void 0===o&&(e.console.warn("getDateRangeDates was called without offsetDays"),o=0);var u=f.getDateRange(t),l=Object(a.s)(c,o),g=u.match("-(.*)-"),d=Number(g?g[1]:28),p=Object(a.s)(l,d-1),v={startDate:p,endDate:l};if(i){var b=Object(a.s)(p,1),m=Object(a.s)(b,d-1);v.compareStartDate=m,v.compareEndDate=b}return v},getDateRangeNumberOfDays:function(e){var t=f.getDateRange(e).match(/-(\d+)-/);return parseInt(t?t[1]:28,10)},getReferenceDate:function(e){return e.referenceDate}};t.a={initialState:l,actions:g,controls:{},reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"SET_DATE_RANGE":return u(u({},e),{},{dateRange:n.slug});case"SET_REFERENCE_DATE":return u(u({},e),{},{referenceDate:n.dateString});default:return e}},resolvers:{},selectors:f}}).call(this,r(28))},963:function(e,t,r){"use strict";var n=r(5),i=r.n(n),o=r(6),s=r.n(o),a=r(45),c=r.n(a),u=r(3),l=r(7),g=r(48);function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var p=Object(g.a)({baseName:"disconnect",controlCallback:function(){return c.a.set("core","user","disconnect")},reducerCallback:function(e,t){return d(d({},e),{},{disconnected:t})}}),v={disconnected:void 0},b={disconnect:i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.actions.fetchDisconnect();case 2:case"end":return e.stop()}}),e)}))},m={isDoingDisconnect:Object(u.createRegistrySelector)((function(e){return function(){return e(l.a).isFetchingDisconnect()}}))},y=Object(u.combineStores)(p,{initialState:v,actions:b,selectors:m});y.initialState,y.actions,y.controls,y.reducer,y.resolvers,y.selectors;t.a=y},964:function(e,t,r){"use strict";var n=r(5),i=r.n(n),o=r(6),s=r.n(o),a=r(12),c=r.n(a),u=r(45),l=r.n(u),g=r(3),f=r(7),d=r(48),p=r(62);function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var m=g.commonActions.getRegistry;function y(e,t){return b(b({},e),{},{dismissedItems:Array.isArray(t)?t:[]})}var O=Object(d.a)({baseName:"getDismissedItems",controlCallback:function(){return l.a.get("core","user","dismissed-items",{},{useCache:!1})},reducerCallback:y}),h=Object(d.a)({baseName:"removeDismissedItems",controlCallback:function(e){var t=e.slugs;return l.a.set("core","user","dismissed-items",{slugs:t},{method:"DELETE"})},reducerCallback:y,argsToParams:function(e){return{slugs:e}},validateParams:function(e){var t=e.slugs;c()(Array.isArray(t),"slugs must be an array."),c()(t.every((function(e){return"string"==typeof e})),"All slugs must be strings.")}}),S=Object(d.a)({baseName:"dismissItem",controlCallback:function(e){var t=e.slug,r=e.expiresInSeconds;return l.a.set("core","user","dismiss-item",{slug:t,expiration:r})},reducerCallback:y,argsToParams:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return{slug:e,expiresInSeconds:t}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.slug,r=e.expiresInSeconds;c()(t,"slug is required."),c()(Number.isInteger(r),"expiresInSeconds must be an integer.")}}),_={dismissedItems:void 0,isDismissingItems:{}},j={dismissItem:Object(p.f)((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.expiresInSeconds,n=void 0===r?0:r;c()(e,"A slug is required to dismiss an item."),c()("string"==typeof e,"A slug must be a string."),c()(Number.isInteger(n),"expiresInSeconds must be an integer.")}),i.a.mark((function e(t){var r,n,o,s,a,c,u,l=arguments;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=l.length>1&&void 0!==l[1]?l[1]:{},n=r.expiresInSeconds,o=void 0===n?0:n,e.next=4,g.commonActions.getRegistry();case 4:return(s=e.sent).dispatch(f.a).setIsItemDimissing(t,!0),e.next=8,S.actions.fetchDismissItem(t,o);case 8:return a=e.sent,c=a.response,u=a.error,s.dispatch(f.a).setIsItemDimissing(t,!1),e.abrupt("return",{response:c,error:u});case 13:case"end":return e.stop()}}),e)}))),removeDismissedItems:Object(p.f)((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];c()(t.length>0,"At least one slug must be provided."),c()(t.every((function(e){return"string"==typeof e})),"All slugs must be strings.")}),(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return h.actions.fetchRemoveDismissedItems(t)})),setIsItemDimissing:function(e,t){return{payload:{slug:e,isDismissing:t},type:"SET_IS_ITEM_DISMISSING"}}},E={getDismissedItems:i.a.mark((function e(){var t,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m();case 2:if(t=e.sent,r=t.select,void 0!==r(f.a).getDismissedItems()){e.next=8;break}return e.next=8,O.actions.fetchGetDismissedItems();case 8:case"end":return e.stop()}}),e)}))},k={getDismissedItems:function(e){return e.dismissedItems},isItemDismissed:Object(g.createRegistrySelector)((function(e){return function(t,r){var n;return null===(n=e(f.a).getDismissedItems())||void 0===n?void 0:n.includes(r)}})),isDismissingItem:function(e,t){return!!e.isDismissingItems[t]}},w=Object(g.combineStores)({initialState:_,actions:j,resolvers:E,reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"SET_IS_ITEM_DISMISSING":var i=n.slug,o=n.isDismissing;return b(b({},e),{},{isDismissingItems:s()({},i,o)});default:return e}},selectors:k},S,O,h),A=w.actions,T=w.controls,I=w.initialState,D=w.reducer,C=w.resolvers,R=w.selectors;t.a={actions:A,controls:T,initialState:I,reducer:D,resolvers:C,selectors:R}},965:function(e,t,r){"use strict";var n=r(5),i=r.n(n),o=r(6),s=r.n(o),a=r(12),c=r.n(a),u=r(45),l=r.n(u),g=r(3),f=r(7),d=r(48),p=r(62);function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var m=g.commonActions.getRegistry;function y(e,t){return b(b({},e),{},{expirableItems:t})}var O=Object(d.a)({baseName:"getExpirableItems",controlCallback:function(){return l.a.get("core","user","expirable-items",{},{useCache:!1})},reducerCallback:y}),h=Object(d.a)({baseName:"setExpirableItemTimers",controlCallback:function(e){return l.a.set("core","user","set-expirable-item-timers",e)},reducerCallback:y,argsToParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.map((function(e){return{slug:e.slug,expiration:e.expiresInSeconds}}))},validateParams:function(e){c()(Array.isArray(e),"items are required."),e.forEach((function(e){var t=e.slug,r=e.expiresInSeconds,n=void 0===r?0:r;c()(t,"slug is required."),c()(Number.isInteger(n),"expiresInSeconds must be an integer.")}))}}),S={expirableItems:void 0},_={setExpirableItemTimers:Object(p.f)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];e.forEach((function(e){var t=e.slug,r=e.expiresInSeconds;c()(t,"An item slug is required."),c()(Number.isInteger(r),"expiresInSeconds must be an integer.")}))}),(function(e){return h.actions.fetchSetExpirableItemTimers(e)}))},j={getExpirableItems:i.a.mark((function e(){var t,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m();case 2:if(t=e.sent,r=t.select,void 0!==r(f.a).getExpirableItems()){e.next=8;break}return e.next=8,O.actions.fetchGetExpirableItems();case 8:case"end":return e.stop()}}),e)}))},E={getExpirableItems:function(e){return e.expirableItems},hasExpirableItem:Object(g.createRegistrySelector)((function(e){return function(t,r){var n=e(f.a).getExpirableItems();if(void 0!==n)return n.hasOwnProperty(r)}})),isExpirableItemActive:Object(g.createRegistrySelector)((function(e){return function(t,r){var n=e(f.a).getExpirableItems();if(void 0!==n){var i=n[r];return void 0!==i&&i>Math.floor(Date.now()/1e3)}}}))},k=Object(g.combineStores)({initialState:S,actions:_,resolvers:j,selectors:E},O,h),w=k.actions,A=k.controls,T=k.initialState,I=k.reducer,D=k.resolvers,C=k.selectors;t.a={actions:w,controls:A,initialState:T,reducer:I,resolvers:D,selectors:C}},967:function(e,t,r){"use strict";var n=r(27),i=r.n(n),o=r(5),s=r.n(o),a=r(6),c=r.n(a),u=r(12),l=r.n(u),g=r(14),f=r(45),d=r.n(f),p=r(3),v=r(7),b=r(13),m=r(19),y=r(47),O=r(8),h=r(48),S=r(64),_=r(145);function j(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?j(Object(r),!0).forEach((function(t){c()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var k=S.a.receiveError,w=S.a.clearError,A={keyMetricsSettings:void 0},T=Object(h.a)({baseName:"getKeyMetricsSettings",controlCallback:function(){return d.a.get("core","user","key-metrics",void 0,{useCache:!1})},reducerCallback:function(e,t){return E(E({},e),{},{keyMetricsSettings:t})}}),I=Object(h.a)({baseName:"saveKeyMetricsSettings",controlCallback:function(e){return d.a.set("core","user","key-metrics",{settings:e})},reducerCallback:function(e,t){return E(E({},e),{},{keyMetricsSettings:t})},argsToParams:function(e){return e},validateParams:function(e){l()(Object(g.isPlainObject)(e),"Settings should be an object.")}}),D={setKeyMetricsSetting:function(e,t){return{type:"SET_KEY_METRICS_SETTING",payload:{settingID:e,value:t}}},saveKeyMetricsSettings:s.a.mark((function e(){var t,r,n,i,o,a,c=arguments;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},l()(Object(g.isPlainObject)(t),"key metric settings should be an object to save."),e.next=4,w("saveKeyMetricsSettings",[]);case 4:return e.next=6,p.commonActions.getRegistry();case 6:return r=e.sent,n=r.select(v.a).getKeyMetricsSettings(),e.next=10,I.actions.fetchSaveKeyMetricsSettings(E(E({},n),t));case 10:if(i=e.sent,o=i.response,!(a=i.error)){e.next=18;break}return e.next=16,k(a,"saveKeyMetricsSettings",[]);case 16:e.next=19;break;case 18:(Object(g.isEmpty)(t)||t.widgetSlugs)&&r.dispatch(b.c).setKeyMetricsSetupCompletedBy(r.select(v.a).getID());case 19:return e.abrupt("return",{response:o,error:a});case 20:case"end":return e.stop()}}),e)}))},C={getKeyMetricsSettings:s.a.mark((function e(){var t;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.commonActions.getRegistry();case 2:if(t=e.sent,!t.select(v.a).getKeyMetricsSettings()){e.next=6;break}return e.abrupt("return");case 6:return e.next=8,T.actions.fetchGetKeyMetricsSettings();case 8:case"end":return e.stop()}}),e)}))},R={getKeyMetrics:Object(p.createRegistrySelector)((function(e){return function(){var t=e(v.a),r=t.getAnswerBasedMetrics,n=(0,t.getUserPickedMetrics)();if(void 0!==n){if(n.length)return n;var i=r();if(void 0!==i)return i.length?i:e(b.c).isKeyMetricsSetupCompleted()?[v.j,v.A,v.g,v.G]:[]}}})),getRegularKeyMetricsWidgetIDs:Object(p.createRegistrySelector)((function(e){return function(){var t=(e(b.c).getPostTypes()||[]).some((function(e){return"product"===e.slug}));return{publish_blog:[v.o,v.j,v.A,v.g],publish_news:[v.k,v.F,v.E,v.i],monetize_content:[v.m,v.g,v.j,v.A],sell_products_or_service:[t?v.n:v.m,v.g,v.G,v.A],sell_products:[t?v.n:v.m,v.f,v.G,v.u],provide_services:[v.A,v.g,v.G,v.m],share_portfolio:[v.j,v.A,v.g,v.G]}}})),getConversionTailoredKeyMetricsWidgetIDs:Object(p.createRegistrySelector)((function(e){return function(t,r){var n,o=(null!==(n=e(b.c).getPostTypes())&&void 0!==n?n:[]).some((function(e){return"product"===e.slug})),s=e(v.a).getUserInputSettings(),a=function(e){return e.some((function(e){var t,n;return(null==s||null===(t=s.includeConversionEvents)||void 0===t||null===(n=t.values)||void 0===n?void 0:n.includes(e))||Array.isArray(r)&&(null==r?void 0:r.includes(e))}))};return{publish_blog:[v.p,v.u,v.z,v.G,v.y,v.A].concat(i()(a([O.l.CONTACT,O.l.GENERATE_LEAD,O.l.SUBMIT_LEAD_FORM])?[v.x,v.C]:[])),publish_news:[v.g,v.l,v.q,v.G,v.y,v.A].concat(i()(a([O.l.CONTACT,O.l.GENERATE_LEAD,O.l.SUBMIT_LEAD_FORM])?[v.x,v.C]:[])),monetize_content:[v.i,v.m,v.j,v.f,v.F,v.E,v.g,v.G],sell_products_or_service:[o?v.n:v.m].concat(i()(a([O.l.PURCHASE])?[v.t,v.w,v.D]:[]),i()(a([O.l.ADD_TO_CART])?[v.B]:[]),[v.f,v.u,v.G]),sell_products:[o?v.n:v.m].concat(i()(a([O.l.PURCHASE])?[v.t,v.w,v.D]:[]),i()(a([O.l.ADD_TO_CART])?[v.B]:[]),[v.f,v.u,v.G]),provide_services:[].concat(i()(a([O.l.CONTACT,O.l.GENERATE_LEAD,O.l.SUBMIT_LEAD_FORM])?[v.s,v.x,v.C]:[]),[v.A,v.g,v.G,v.m,v.z]),share_portfolio:[v.u,v.z,v.l].concat(i()(a([O.l.CONTACT,O.l.GENERATE_LEAD,O.l.SUBMIT_LEAD_FORM])?[v.s,v.x,v.C]:[]),[v.m,v.G])}}})),getAnswerBasedMetrics:Object(p.createRegistrySelector)((function(e){return function(t,r,n){var i,o,s=e(v.a).getUserInputSettings();if(void 0!==s){var a=null!=r?r:null==s||null===(i=s.purpose)||void 0===i||null===(o=i.values)||void 0===o?void 0:o[0];return e(v.a).getConversionTailoredKeyMetricsWidgetIDs(n)[a]||[]}}})),getUserPickedMetrics:Object(p.createRegistrySelector)((function(e){return function(){var t=e(v.a).getKeyMetricsSettings();if(void 0!==t){if(!Array.isArray(t.widgetSlugs))return[];var r=!e(v.a).isAuthenticated(),n=t.widgetSlugs.filter((function(t){var n=_.a[t];return!!n&&(!n.displayInWidgetArea||"function"!=typeof n.displayInWidgetArea||n.displayInWidgetArea(e,r,t))}));return 1===n.length?[]:n}}})),isKeyMetricActive:Object(p.createRegistrySelector)((function(e){return function(t,r){var n=e(v.a).getKeyMetrics();if(void 0!==n)return n.includes(r)}})),isKeyMetricsWidgetHidden:Object(p.createRegistrySelector)((function(e){return function(){var t=e(v.a).getKeyMetricsSettings();if(void 0!==t)return t.isWidgetHidden}})),getKeyMetricsSettings:function(e){var t=e.keyMetricsSettings;if(t)return t},isSavingKeyMetricsSettings:function(e){return Object.values(e.isFetchingSaveKeyMetricsSettings).some(Boolean)},isKeyMetricAvailable:Object(p.createRegistrySelector)((function(e){return function(t,r){l()(r,"Key metric widget slug required.");var n=e(v.a).isAuthenticated();if(void 0!==n){var i=e(y.a).getWidget(r);if(!i)return!1;var o=e(m.a).getModule,s=e(v.a).canViewSharedModule;return i.modules.every((function(e){var t=o(e);return!!t&&!(!n&&(null==t?void 0:t.shareable)&&!s(e))}))}}}))},P=Object(p.combineStores)(T,I,{initialState:A,actions:D,controls:{},reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"SET_KEY_METRICS_SETTING":return E(E({},e),{},{keyMetricsSettings:E(E({},e.keyMetricsSettings),{},c()({},n.settingID,n.value))});default:return e}},resolvers:C,selectors:R});P.initialState,P.actions,P.controls,P.reducer,P.resolvers,P.selectors;t.a=P},968:function(e,t,r){"use strict";var n=r(561),i=Object(n.a)("core","user","notifications",{server:!1});t.a=i},969:function(e,t,r){"use strict";var n=r(5),i=r.n(n),o=r(6),s=r.n(o),a=r(45),c=r.n(a),u=r(3),l=r(7),g=r(48);function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var p=Object(g.a)({baseName:"getNonces",controlCallback:function(){return c.a.get("core","user","nonces",void 0,{useCache:!1})},reducerCallback:function(e,t){return d(d({},e),{},{nonces:t})}}),v={nonces:void 0},b={receiveNonces:function(e){return{type:"RECEIVE_NONCES",payload:{nonces:e}}}},m={getNonces:i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,u.commonActions.getRegistry();case 2:if(!e.sent.select(l.a).getNonces()){e.next=5;break}return e.abrupt("return");case 5:return e.next=7,p.actions.fetchGetNonces();case 7:case"end":return e.stop()}}),e)}))},y={getNonces:function(e){return e.nonces},getNonce:Object(u.createRegistrySelector)((function(e){return function(t,r){var n=e(l.a).getNonces();if(n)return n[r]}}))},O=Object(u.combineStores)(p,{initialState:v,actions:b,controls:{},reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"RECEIVE_NONCES":var i=n.nonces;return d(d({},e),{},{nonces:i});default:return e}},resolvers:m,selectors:y});O.initialState,O.actions,O.controls,O.reducer,O.resolvers,O.selectors;t.a=O},97:function(e,t,r){"use strict";(function(e){r(51),r(53)}).call(this,r(28))},970:function(e,t,r){"use strict";(function(e){var n=r(27),i=r.n(n),o=r(5),s=r.n(o),a=r(6),c=r.n(a),u=r(12),l=r.n(u),g=r(45),f=r.n(g),d=r(3),p=r(7),v=r(19),b=r(971),m=r(48);function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function O(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach((function(t){c()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var h=Object(m.a)({baseName:"getCapabilities",controlCallback:function(){return f.a.get("core","user","permissions",void 0,{useCache:!1})},reducerCallback:function(e,t){return O(O({},e),{},{capabilities:t})}}),S={permissionError:null,capabilities:void 0},_={clearPermissionScopeError:function(){return{payload:{},type:"CLEAR_PERMISSION_SCOPE_ERROR"}},setPermissionScopeError:function(e){return l()(e,"permissionError is required."),{payload:{permissionError:e},type:"SET_PERMISSION_SCOPE_ERROR"}},receiveCapabilities:function(e){return{type:"RECEIVE_CAPABILITIES",payload:{capabilities:e}}},refreshCapabilities:s.a.mark((function e(){var t,r,n,i,o;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,d.commonActions.getRegistry();case 2:return t=e.sent,r=t.dispatch,e.next=6,h.actions.fetchGetCapabilities();case 6:return n=e.sent,i=n.response,(o=n.error)&&r(p.a).setPermissionScopeError(o),e.abrupt("return",{response:i,error:o});case 11:case"end":return e.stop()}}),e)}))},j={getCapabilities:s.a.mark((function t(){var r,n,i,o;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,d.commonActions.getRegistry();case 2:if(!t.sent.select(p.a).getCapabilities()){t.next=5;break}return t.abrupt("return");case 5:if(!(o=null===(r=e._googlesitekitAPIFetchData)||void 0===r||null===(n=r.preloadedData)||void 0===n||null===(i=n["/google-site-kit/v1/core/user/data/permissions"])||void 0===i?void 0:i.body)){t.next=9;break}return t.next=9,h.actions.receiveGetCapabilities(O({},o));case 9:return t.next=11,h.actions.fetchGetCapabilities();case 11:case"end":return t.stop()}}),t)}))},E={getPermissionScopeError:function(e){return e.permissionError},getCapabilities:function(e){return e.capabilities},getViewableModules:Object(d.createRegistrySelector)((function(e){return function(){var t=e(v.a).getModules();if(void 0!==t)return Object.values(t).reduce((function(t,r){var n=e(p.a).hasCapability(p.L,r.slug);return r.shareable&&n?[].concat(i()(t),[r.slug]):t}),[])}})),hasCapability:Object(d.createRegistrySelector)((function(e){return function(t,r){for(var n=e(p.a).getCapabilities(),i=arguments.length,o=new Array(i>2?i-2:0),s=2;s<i;s++)o[s-2]=arguments[s];if(o.length>0&&(r=b.a.apply(void 0,[r].concat(o))),n)return!!n[r]}})),canViewSharedModule:Object(d.createRegistrySelector)((function(e){return function(t,r){var n=e(v.a).getModule(r);if(void 0!==n)return!(null===n||!n.shareable)&&e(p.a).hasCapability(p.L,n.slug)}})),hasAccessToShareableModule:Object(d.createRegistrySelector)((function(e){return function(t,r){var n=e(v.a).isModuleAvailable(r);if(void 0!==n)return!1!==n&&(!!e(p.a).isAuthenticated()||e(p.a).canViewSharedModule(r))}}))},k=Object(d.combineStores)(h,{initialState:S,actions:_,controls:{},reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"CLEAR_PERMISSION_SCOPE_ERROR":return O(O({},e),{},{permissionError:null});case"SET_PERMISSION_SCOPE_ERROR":var i=n.permissionError;return O(O({},e),{},{permissionError:i});case"RECEIVE_CAPABILITIES":var o=n.capabilities;return O(O({},e),{},{capabilities:o});default:return e}},resolvers:j,selectors:E});k.initialState,k.actions,k.controls,k.reducer,k.resolvers,k.selectors;t.a=k}).call(this,r(28))},971:function(e,t,r){"use strict";function n(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return"".concat(e,"::").concat(JSON.stringify(r))}r.d(t,"a",(function(){return n}))},972:function(e,t,r){"use strict";var n=r(15),i=r.n(n),o=r(5),s=r.n(o),a=r(33),c=r.n(a),u=r(6),l=r.n(u),g=r(12),f=r.n(g),d=r(45),p=r.n(d),v=r(3),b=r(7),m=r(48),y=r(62);function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach((function(t){l()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var S=v.commonActions.getRegistry;function _(e,t){return h(h({},e),{},{dismissedPrompts:"object"===c()(t)?t:{}})}var j=Object(m.a)({baseName:"getDismissedPrompts",controlCallback:function(){return p.a.get("core","user","dismissed-prompts",{},{useCache:!1})},reducerCallback:_}),E=Object(m.a)({baseName:"dismissPrompt",controlCallback:function(e){var t=e.slug,r=e.expiresInSeconds;return p.a.set("core","user","dismiss-prompt",{slug:t,expiration:r})},reducerCallback:_,argsToParams:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return{slug:e,expiresInSeconds:t}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.slug,r=e.expiresInSeconds;f()(t,"slug is required."),f()(Number.isInteger(r),"expiresInSeconds must be an integer.")}}),k={dismissedPrompts:void 0,isDismissingPrompts:{}},w={dismissPrompt:Object(y.f)((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.expiresInSeconds,n=void 0===r?0:r;f()(e,"A tour slug is required to dismiss a tour."),f()(Number.isInteger(n),"expiresInSeconds must be an integer.")}),s.a.mark((function e(t){var r,n,i,o,a,c,u,l=arguments;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=l.length>1&&void 0!==l[1]?l[1]:{},n=r.expiresInSeconds,i=void 0===n?0:n,e.next=4,v.commonActions.getRegistry();case 4:return(o=e.sent).dispatch(b.a).setIsPromptDimissing(t,!0),e.next=8,E.actions.fetchDismissPrompt(t,i);case 8:return a=e.sent,c=a.response,u=a.error,o.dispatch(b.a).setIsPromptDimissing(t,!1),e.abrupt("return",{response:c,error:u});case 13:case"end":return e.stop()}}),e)}))),setIsPromptDimissing:function(e,t){return{payload:{slug:e,isDismissing:t},type:"SET_IS_PROMPT_DISMISSING"}}},A={getDismissedPrompts:s.a.mark((function e(){var t,r;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S();case 2:if(t=e.sent,r=t.select,void 0!==r(b.a).getDismissedPrompts()){e.next=8;break}return e.next=8,j.actions.fetchGetDismissedPrompts();case 8:case"end":return e.stop()}}),e)}))},T={getDismissedPrompts:function(e){if(void 0!==e.dismissedPrompts){var t=Math.floor(Date.now()/1e3);return Object.entries(e.dismissedPrompts).reduce((function(e,r){var n=i()(r,2),o=n[0],s=n[1].expires;return(0===s||s>t)&&e.push(o),e}),[])}},getPromptDismissCount:Object(v.createRegistrySelector)((function(){return function(e,t){var r;if(e.dismissedPrompts)return(null===(r=e.dismissedPrompts[t])||void 0===r?void 0:r.count)||0}})),isPromptDismissed:Object(v.createRegistrySelector)((function(e){return function(t,r){var n;return null===(n=e(b.a).getDismissedPrompts())||void 0===n?void 0:n.includes(r)}})),isDismissingPrompt:function(e,t){return!!e.isDismissingPrompts[t]}},I=Object(v.combineStores)({initialState:k,actions:w,resolvers:A,selectors:T,reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"SET_IS_PROMPT_DISMISSING":var i=n.slug,o=n.isDismissing;return h(h({},e),{},{isDismissingPrompts:l()({},i,o)});default:return e}}},E,j),D=I.actions,C=I.controls,R=I.initialState,P=I.reducer,x=I.resolvers,N=I.selectors;t.a={actions:D,controls:C,initialState:R,reducer:P,resolvers:x,selectors:N}},973:function(e,t,r){"use strict";var n=r(5),i=r.n(n),o=r(27),s=r.n(o),a=r(6),c=r.n(a),u=r(12),l=r.n(u),g=r(14),f=r(45),d=r.n(f),p=r(3),v=r(7),b=r(48),m=r(62);function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function O(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach((function(t){c()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var h=Object(b.a)({baseName:"triggerSurvey",controlCallback:function(e){var t=e.triggerID,r=e.ttl,n=r?{ttl:r}:{};return d.a.set("core","user","survey-trigger",O({triggerID:t},n))},argsToParams:function(e,t){return{triggerID:e,ttl:t}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.triggerID,r=e.ttl,n=void 0===r?0:r;l()("string"==typeof t&&t.length,"triggerID is required and must be a string"),l()("number"==typeof n,"ttl must be a number")}}),S=Object(b.a)({baseName:"sendSurveyEvent",controlCallback:function(e){var t=e.event,r=e.session;return d.a.set("core","user","survey-event",{event:t,session:r})},argsToParams:function(e,t){return{event:e,session:t}}}),_=Object(b.a)({baseName:"getSurveyTimeouts",controlCallback:function(){return d.a.get("core","user","survey-timeouts",{},{useCache:!1})},reducerCallback:function(e,t){return O(O({},e),{},{surveyTimeouts:Array.isArray(t)?t:[]})}}),j=Object(b.a)({baseName:"getSurvey",controlCallback:function(){return d.a.get("core","user","survey",{})},reducerCallback:function(e,t){var r=t.survey,n=r||{},i=n.survey_payload,o=void 0===i?null:i,s=n.session,a=void 0===s?null:s;return O(O({},e),{},{currentSurvey:o,currentSurveySession:a})}}),E={currentSurvey:void 0,currentSurveySession:void 0,lockedSurveyTriggers:{}};function k(e){return{type:"LOCK_SURVEY_TRIGGER",payload:{triggerID:e}}}function w(e){return{type:"UNLOCK_SURVEY_TRIGGER",payload:{triggerID:e}}}var A={triggerSurvey:Object(m.f)((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.ttl,n=void 0===r?0:r;l()("string"==typeof e&&e.length,"triggerID is required and must be a string"),l()(Object(g.isPlainObject)(t),"options must be an object"),l()("number"==typeof n,"options.ttl must be a number")}),i.a.mark((function e(t){var r,n,o,a,c,u,l,g,f,d,b,m,y,O,S,j=arguments;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=j.length>1&&void 0!==j[1]?j[1]:{},n=r.ttl,o=void 0===n?0:n,e.next=4,p.commonActions.getRegistry();case 4:if(a=e.sent,c=a.select,u=a.resolveSelect,l=c(v.a),g=l.isAuthenticated,f=l.isSurveyTimedOut,d=l.isSurveyTriggerLocked,b=l.getSurveyTimeouts,!d(t)){e.next=10;break}return e.abrupt("return",{});case 10:return e.next=12,k(t);case 12:return e.prev=12,e.next=15,p.commonActions.await(u(v.a).getAuthentication());case 15:if(g()){e.next=17;break}return e.abrupt("return",{});case 17:return e.next=19,p.commonActions.await(u(v.a).getSurveyTimeouts());case 19:if(!f(t)){e.next=21;break}return e.abrupt("return",{response:{},error:!1});case 21:return e.next=23,h.actions.fetchTriggerSurvey(t,o);case 23:if(m=e.sent,y=m.response,!(O=m.error)){e.next=28;break}return e.abrupt("return",{response:y,error:O});case 28:if(!(o>0)){e.next=32;break}return S=b()||[],e.next=32,_.actions.receiveGetSurveyTimeouts([].concat(s()(S),[t]));case 32:return e.abrupt("return",{response:{},error:!1});case 33:return e.prev=33,e.next=36,w(t);case 36:return e.finish(33);case 37:case"end":return e.stop()}}),e,null,[[12,,33,37]])}))),sendSurveyEvent:Object(m.f)((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};l()("string"==typeof e&&e.length,"eventID is required and must be a string"),l()(Object(g.isPlainObject)(t),"eventData must be an object")}),i.a.mark((function e(t){var r,n,o,s,a,u,l,g,f=arguments;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=f.length>1&&void 0!==f[1]?f[1]:{},n=c()({},t,r),e.next=4,p.commonActions.getRegistry();case 4:if(o=e.sent,s=o.select,!(a=s(v.a).getCurrentSurveySession())){e.next=14;break}return e.next=10,S.actions.fetchSendSurveyEvent(n,a);case 10:return u=e.sent,l=u.response,g=u.error,e.abrupt("return",{response:l,error:g});case 14:case"end":return e.stop()}}),e)})))},T={getCurrentSurvey:i.a.mark((function e(){var t,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.commonActions.getRegistry();case 2:if(t=e.sent,r=t.select,void 0!==r(v.a).getCurrentSurvey()){e.next=8;break}return e.next=8,j.actions.fetchGetSurvey();case 8:case"end":return e.stop()}}),e)})),getSurveyTimeouts:i.a.mark((function e(){var t,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.commonActions.getRegistry();case 2:if(t=e.sent,r=t.select,void 0!==r(v.a).getSurveyTimeouts()){e.next=8;break}return e.next=8,_.actions.fetchGetSurveyTimeouts();case 8:case"end":return e.stop()}}),e)}))},I={getCurrentSurvey:function(e){return e.currentSurvey},getCurrentSurveySession:function(e){return e.currentSurveySession},getCurrentSurveyCompletions:Object(p.createRegistrySelector)((function(e){return function(){var t=e(v.a).getCurrentSurvey();return(null==t?void 0:t.completion)||null}})),getCurrentSurveyQuestions:Object(p.createRegistrySelector)((function(e){return function(){var t=e(v.a).getCurrentSurvey();return(null==t?void 0:t.question)||null}})),getSurveyTimeouts:function(e){return e.surveyTimeouts},isSurveyTimedOut:Object(p.createRegistrySelector)((function(e){return function(t,r){var n=e(v.a).getSurveyTimeouts();return void 0===n?void 0:n.includes(r)}})),isSurveyTriggerLocked:function(e,t){return!!e.lockedSurveyTriggers[t]},areSurveysOnCooldown:Object(p.createRegistrySelector)((function(e){return function(){return e(v.a).isSurveyTimedOut(v.e)}}))},D=Object(p.createReducer)((function(e,t){switch(t.type){case"LOCK_SURVEY_TRIGGER":var r=t.payload.triggerID;e.lockedSurveyTriggers[r]=!0;break;case"UNLOCK_SURVEY_TRIGGER":var n=t.payload.triggerID;e.lockedSurveyTriggers[n]=!1}})),C=Object(p.combineStores)(h,S,_,j,{initialState:E,actions:A,reducer:D,resolvers:T,selectors:I});C.initialState,C.actions,C.resolvers,C.selectors;t.a=C},974:function(e,t,r){"use strict";var n=r(5),i=r.n(n),o=r(6),s=r.n(o),a=r(45),c=r.n(a),u=r(3),l=r(7),g=r(48),f=r(64);function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var v=f.a.receiveError,b=f.a.clearError,m=function(e,t){return p(p({},e),{},{tracking:t})},y=Object(g.a)({baseName:"getTracking",controlCallback:function(){return c.a.get("core","user","tracking")},reducerCallback:m}),O=Object(g.a)({baseName:"setTracking",controlCallback:function(e){return c.a.set("core","user","tracking",{enabled:!!e})},reducerCallback:m,argsToParams:function(e){return e}}),h={tracking:void 0,isSavingTrackingEnabled:!1},S={setTrackingEnabled:i.a.mark((function e(t){var r,n,o;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b("setTrackingEnabled",[t]);case 2:return e.next=4,{type:"SET_TRACKING_ENABLED_SAVING_ACTION",payload:{isSaving:!0}};case 4:return e.next=6,O.actions.fetchSetTracking(t);case 6:if(r=e.sent,n=r.response,!(o=r.error)){e.next=12;break}return e.next=12,v(o,"setTrackingEnabled",[t]);case 12:return e.next=14,{type:"SET_TRACKING_ENABLED_SAVING_ACTION",payload:{isSaving:!1}};case 14:return e.abrupt("return",{response:n,error:o});case 15:case"end":return e.stop()}}),e)}))},_={isTrackingEnabled:i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,u.commonActions.getRegistry();case 2:if(t=e.sent,void 0!==(0,t.select)(l.a).isTrackingEnabled()){e.next=7;break}return e.next=7,y.actions.fetchGetTracking();case 7:case"end":return e.stop()}}),e)}))},j=Object(u.combineStores)(y,O,{initialState:h,actions:S,reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"SET_TRACKING_ENABLED_SAVING_ACTION":return p(p({},e),{},{isSavingTrackingEnabled:n.isSaving});default:return e}},resolvers:_,selectors:{isSavingTrackingEnabled:function(e){return!!(null==e?void 0:e.isSavingTrackingEnabled)},isTrackingEnabled:function(e){var t=e.tracking;return null==t?void 0:t.enabled}}});j.initialState,j.actions,j.controls,j.reducer,j.resolvers,j.selectors;t.a=j},975:function(e,t,r){"use strict";(function(e){var n=r(304),i=r.n(n),o=r(5),s=r.n(o),a=r(6),c=r.n(a),u=r(12),l=r.n(u),g=r(157),f=r(3),d=r(7),p=r(426);function v(){var e=i()(["https://accounts.google.com/accountchooser?continue=","&Email=",""]);return v=function(){return e},e}function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){c()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var y={connectURL:void 0,initialVersion:void 0,user:void 0,verified:void 0,isUserInputCompleted:void 0},O={receiveConnectURL:function(e){return l()(e,"connectURL is required."),{payload:{connectURL:e},type:"RECEIVE_CONNECT_URL"}},receiveUserInfo:function(e){return l()(e,"userInfo is required."),{payload:{user:e},type:"RECEIVE_USER_INFO"}},receiveInitialSiteKitVersion:function(e){return l()(e,"initialVersion is required."),{payload:{initialVersion:e},type:"RECEIVE_USER_INITIAL_SITE_KIT_VERSION"}},receiveUserIsVerified:function(e){return l()(void 0!==e,"userIsVerified is required."),{payload:{verified:e},type:"RECEIVE_USER_IS_VERIFIED"}},receiveIsUserInputCompleted:function(e){return l()(void 0!==e,"The isUserInputCompleted param is required."),{payload:{isUserInputCompleted:e},type:"RECEIVE_IS_USER_INPUT_COMPLETED"}}},h={getConnectURL:s.a.mark((function t(){var r,n;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,f.commonActions.getRegistry();case 2:if(r=t.sent,!(0,r.select)(d.a).getConnectURL()){t.next=6;break}return t.abrupt("return");case 6:if(e._googlesitekitUserData){t.next=9;break}return e.console.error("Could not load core/user info."),t.abrupt("return");case 9:return n=e._googlesitekitUserData.connectURL,t.next=12,O.receiveConnectURL(n);case 12:case"end":return t.stop()}}),t)})),getUser:s.a.mark((function t(){var r,n;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,f.commonActions.getRegistry();case 2:if(r=t.sent,void 0===(0,r.select)(d.a).getUser()){t.next=6;break}return t.abrupt("return");case 6:if(e._googlesitekitUserData){t.next=9;break}return e.console.error("Could not load core/user info."),t.abrupt("return");case 9:return n=e._googlesitekitUserData.user,t.next=12,O.receiveUserInfo(n);case 12:case"end":return t.stop()}}),t)})),getInitialSiteKitVersion:s.a.mark((function t(){var r,n;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,f.commonActions.getRegistry();case 2:if(r=t.sent,void 0===(0,r.select)(d.a).getInitialSiteKitVersion()){t.next=6;break}return t.abrupt("return");case 6:if(e._googlesitekitUserData){t.next=9;break}return e.console.error("Could not load core/user info."),t.abrupt("return");case 9:if(!(n=e._googlesitekitUserData.initialVersion)){t.next=13;break}return t.next=13,O.receiveInitialSiteKitVersion(n);case 13:case"end":return t.stop()}}),t)})),isVerified:s.a.mark((function t(){var r,n;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,f.commonActions.getRegistry();case 2:if(r=t.sent,void 0===(0,r.select)(d.a).isVerified()){t.next=6;break}return t.abrupt("return");case 6:if(e._googlesitekitUserData){t.next=9;break}return e.console.error("Could not load core/user info."),t.abrupt("return");case 9:return n=e._googlesitekitUserData.verified,t.next=12,O.receiveUserIsVerified(n);case 12:case"end":return t.stop()}}),t)})),isUserInputCompleted:s.a.mark((function(){var t,r;return s.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,f.commonActions.getRegistry();case 2:if(t=n.sent,void 0===(0,t.select)(d.a).isUserInputCompleted()){n.next=6;break}return n.abrupt("return");case 6:if(e._googlesitekitUserData){n.next=9;break}return e.console.error("Could not load core/user info."),n.abrupt("return");case 9:return r=e._googlesitekitUserData.isUserInputCompleted,n.next=12,O.receiveIsUserInputCompleted(r);case 12:case"end":return n.stop()}}),r)}))},S={getUser:function(e){return e.user},getConnectURL:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.additionalScopes,n=void 0===r?[]:r,i=t.redirectURL,o=void 0===i?void 0:i,s=t.errorRedirectURL,a=void 0===s?void 0:s,c=e.connectURL,u={redirect:o,errorRedirect:a};if(void 0!==c){if(null==n?void 0:n.length){var l=n.map((function(e){return e.replace(/^http(s)?:/,"gttp$1:")}));return Object(g.a)(c,m(m({},u),{},{additional_scopes:l}))}return Object(g.a)(c,u)}},getID:Object(f.createRegistrySelector)((function(e){return function(){var t=e(d.a).getUser();return void 0!==t?t.id:t}})),getName:Object(f.createRegistrySelector)((function(e){return function(){var t=e(d.a).getUser();return void 0!==t?t.name:t}})),getEmail:Object(f.createRegistrySelector)((function(e){return function(){var t=e(d.a).getUser();return void 0!==t?t.email:t}})),getPicture:Object(f.createRegistrySelector)((function(e){return function(){var t=e(d.a).getUser();return void 0!==t?t.picture:t}})),getFullName:Object(f.createRegistrySelector)((function(e){return function(){var t=e(d.a).getUser();if(void 0!==t)return t.full_name}})),getAccountChooserURL:Object(f.createRegistrySelector)((function(e){return function(t,r){l()(r,"destinationURL is required");var n=e(d.a).getEmail();if(void 0!==n)return Object(p.a)(v(),r,n)}})),getInitialSiteKitVersion:function(e){return e.initialVersion},isVerified:function(e){return e.verified},isUserInputCompleted:function(e){var t=e.isUserInputCompleted;return t}};t.a={initialState:y,actions:O,controls:{},reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"RECEIVE_CONNECT_URL":var i=n.connectURL;return m(m({},e),{},{connectURL:i});case"RECEIVE_USER_INFO":var o=n.user;return m(m({},e),{},{user:o});case"RECEIVE_USER_INITIAL_SITE_KIT_VERSION":var s=n.initialVersion;return m(m({},e),{},{initialVersion:s});case"RECEIVE_USER_IS_VERIFIED":var a=n.verified;return m(m({},e),{},{verified:a});case"RECEIVE_IS_USER_INPUT_COMPLETED":var c=n.isUserInputCompleted;return m(m({},e),{},{isUserInputCompleted:c});default:return e}},resolvers:h,selectors:S}}).call(this,r(28))},976:function(e,t,r){"use strict";var n=r(5),i=r.n(n),o=r(6),s=r.n(o),a=r(12),c=r.n(a),u=r(14),l=r(45),g=r.n(l),f=r(3),d=r(7),p=r(48),v=r(64);function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var y=v.a.receiveError,O=v.a.clearError;function h(e,t){return m(m({},e),{},{inputSettings:t,savedInputSettings:t})}var S=Object(p.a)({baseName:"getUserInputSettings",controlCallback:function(){return g.a.get("core","user","user-input-settings",void 0,{useCache:!1})},reducerCallback:h}),_=Object(p.a)({baseName:"saveUserInputSettings",controlCallback:function(e){return g.a.set("core","user","user-input-settings",{settings:e})},reducerCallback:h,argsToParams:function(e){return e},validateParams:function(e){c()(Object(u.isPlainObject)(e),"valid settings are required.")}}),j={inputSettings:void 0,isSavingInputSettings:!1,savedInputSettings:void 0},E={setUserInputSetting:function(e,t){return{type:"SET_USER_INPUT_SETTING",payload:{settingID:e,values:t.map((function(e){return e.trim()}))}}},saveUserInputSettings:i.a.mark((function e(){var t,r,n,o,a,c,u,l;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,O("saveUserInputSettings",[]);case 5:return r=function(e){return e.trim()},n=function(e){return e.length>0},o=t.select(d.a).getUserInputSettings(),a=Object.keys(o).reduce((function(e,t){var i;return m(m({},e),{},s()({},t,((null===(i=o[t])||void 0===i?void 0:i.values)||[]).map(r).filter(n)))}),{}),e.next=11,{type:"SET_USER_INPUT_SETTINGS_SAVING_FLAG",payload:{isSaving:!0}};case 11:return e.next=13,_.actions.fetchSaveUserInputSettings(a);case 13:if(c=e.sent,u=c.response,!(l=c.error)){e.next=19;break}return e.next=19,y(l,"saveUserInputSettings",[]);case 19:if(l){e.next=22;break}return e.next=22,E.maybeTriggerUserInputSurvey();case 22:return e.next=24,{type:"SET_USER_INPUT_SETTINGS_SAVING_FLAG",payload:{isSaving:!1}};case 24:return e.abrupt("return",{response:u,error:l});case 25:case"end":return e.stop()}}),e)})),resetUserInputSettings:i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{type:"RESET_USER_INPUT_SETTINGS",payload:{}});case 1:case"end":return e.stop()}}),e)})),maybeTriggerUserInputSurvey:i.a.mark((function e(){var t,r,n,o,s,a,c,u,l;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.commonActions.getRegistry();case 2:return t=e.sent,r=t.resolveSelect,n=t.dispatch,e.next=7,f.commonActions.await(r(d.a).getUserInputSettings());case 7:if(o=e.sent,!(!(s=Object.keys(o).filter((function(e){return o[e].values.includes("other")}))).length>0)){e.next=11;break}return e.abrupt("return");case 11:return a="userInput_answered_other__".concat(s.join("_")),e.next=14,f.commonActions.await(n(d.a).triggerSurvey(a));case 14:return c=e.sent,u=c.response,l=c.error,e.abrupt("return",{response:u,error:l});case 18:case"end":return e.stop()}}),e)}))},k={getUserInputSettings:i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.commonActions.getRegistry();case 2:if(t=e.sent,(0,t.select)(d.a).getUserInputSettings()){e.next=7;break}return e.next=7,S.actions.fetchGetUserInputSettings();case 7:case"end":return e.stop()}}),e)}))},w={isSavingUserInputSettings:function(e){return!!(null==e?void 0:e.isSavingInputSettings)},getUserInputSettings:function(e){return e.inputSettings},getSavedUserInputSettings:function(e){return e.savedInputSettings},getUserInputSetting:Object(f.createRegistrySelector)((function(e){return function(t,r){var n,i=null===(n=(e(d.a).getUserInputSettings()||{})[r])||void 0===n?void 0:n.values;return Array.isArray(i)?i:[]}})),getUserInputSettingScope:Object(f.createRegistrySelector)((function(e){return function(t,r){var n;return null===(n=(e(d.a).getUserInputSettings()||{})[r])||void 0===n?void 0:n.scope}})),getUserInputSettingAuthor:Object(f.createRegistrySelector)((function(e){return function(t,r){var n;return null===(n=(e(d.a).getUserInputSettings()||{})[r])||void 0===n?void 0:n.author}})),haveUserInputSettingsChanged:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=e.inputSettings,n=e.savedInputSettings;return t?!Object(u.isEqual)(Object(u.pick)(r,t),Object(u.pick)(n,t)):!Object(u.isEqual)(r,n)},hasUserInputSettingChanged:function(e,t){return c()(t,"setting is required."),w.haveUserInputSettingsChanged(e,[t])}},A=Object(f.combineStores)(S,_,{initialState:j,actions:E,reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"SET_USER_INPUT_SETTING":return m(m({},e),{},{inputSettings:m(m({},e.inputSettings),{},s()({},n.settingID,m(m({},(e.inputSettings||{})[n.settingID]||{}),{},{values:n.values})))});case"SET_USER_INPUT_SETTINGS_SAVING_FLAG":return m(m({},e),{},{isSavingInputSettings:n.isSaving});case"RESET_USER_INPUT_SETTINGS":return m(m({},e),{},{inputSettings:e.savedInputSettings});default:return e}},resolvers:k,selectors:w});A.initialState,A.actions,A.controls,A.reducer,A.resolvers,A.selectors;t.a=A},977:function(e,t,r){"use strict";var n=r(5),i=r.n(n),o=r(6),s=r.n(o),a=r(12),c=r.n(a),u=r(14),l=r(45),g=r.n(l),f=r(3),d=r(7),p=r(48),v=r(62);function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var y={conversionReportingSettings:void 0},O=Object(p.a)({baseName:"getConversionReportingSettings",controlCallback:function(){return g.a.get("core","user","conversion-reporting-settings",void 0,{useCache:!1})},reducerCallback:function(e,t){return m(m({},e),{},{conversionReportingSettings:t})}}),h=Object(p.a)({baseName:"saveConversionReportingSettings",controlCallback:function(e){return g.a.set("core","user","conversion-reporting-settings",{settings:e})},reducerCallback:function(e,t){return m(m({},e),{},{conversionReportingSettings:t})},argsToParams:function(e){return e},validateParams:function(e){c()(Object(u.isPlainObject)(e),"Conversion reporting settings should be an object."),e.newEventsCalloutDismissedAt&&c()(Number.isInteger(e.newEventsCalloutDismissedAt),"newEventsCalloutDismissedAt should be a timestamp."),e.lostEventsCalloutDismissedAt&&c()(Number.isInteger(e.lostEventsCalloutDismissedAt),"lostEventsCalloutDismissedAt should be an integer.")}}),S={saveConversionReportingSettings:Object(v.f)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};c()(Object(u.isPlainObject)(e),"Conversion reporting settings should be an object to save.")}),i.a.mark((function e(){var t,r=arguments;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.length>0&&void 0!==r[0]?r[0]:{},e.next=3,h.actions.fetchSaveConversionReportingSettings(t);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e)})))},_={getConversionReportingSettings:i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,f.commonActions.getRegistry();case 2:if(t=e.sent,void 0!==t.select(d.a).getConversionReportingSettings()){e.next=7;break}return e.next=7,O.actions.fetchGetConversionReportingSettings();case 7:case"end":return e.stop()}}),e)}))},j={getConversionReportingSettings:function(e){return e.conversionReportingSettings},isSavingConversionReportingSettings:function(e){return Object.values(e.isFetchingSaveConversionReportingSettings).some(Boolean)},haveNewConversionEventsAfterDismiss:Object(f.createRegistrySelector)((function(e){return function(t,r){var n=(0,e(d.a).getConversionReportingSettings)();return!!n&&r>n.newEventsCalloutDismissedAt}})),haveLostConversionEventsAfterDismiss:Object(f.createRegistrySelector)((function(e){return function(t,r){var n=(0,e(d.a).getConversionReportingSettings)();return!!n&&r>n.lostEventsCalloutDismissedAt}}))},E=Object(f.combineStores)(O,h,{initialState:y,actions:S,resolvers:_,selectors:j});E.initialState,E.actions,E.controls,E.reducer,E.resolvers,E.selectors;t.a=E},98:function(e,t,r){"use strict";(function(e){r.d(t,"b",(function(){return o})),r.d(t,"a",(function(){return s}));var n=r(239),i=r(85),o=function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var o=r.invertColor,s=void 0!==o&&o;return Object(n.a)(e.createElement(i.a,{direction:t>0?"up":"down",invertColor:s}))},s=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,r(4))},99:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return g}));var n=r(6),i=r.n(n),o=r(14),s=r(100),a=r(101);function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach((function(t){i()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var l={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function g(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=u(u({},l),t);i.referenceSiteURL&&(i.referenceSiteURL=i.referenceSiteURL.toString().replace(/\/+$/,""));var c=Object(s.a)(i,r),g=Object(a.a)(i,r,c,n),f={},d=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=JSON.stringify(t);f[n]||(f[n]=Object(o.once)(g)),f[n].apply(f,t)};return{enableTracking:function(){i.trackingEnabled=!0},disableTracking:function(){i.trackingEnabled=!1},initializeSnippet:c,isTrackingEnabled:function(){return!!i.trackingEnabled},trackEvent:g,trackEventOnce:d}}}).call(this,r(28))}},[[1256,1,0]]]);