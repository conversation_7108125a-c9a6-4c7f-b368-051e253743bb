(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[23],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a}));var r=n(59),i=n(39),o=n(57);function a(t,n){var a,c=Object(r.a)(n),s=t.activeModules,l=t.referenceSiteURL,u=t.userIDHash,g=t.userRoles,d=void 0===g?[]:g,f=t.isAuthenticated,p=t.pluginVersion;return function(){var n=e.document;if(void 0===a&&(a=!!n.querySelector("script[".concat(i.b,"]"))),!a){a=!0;var r=(null==d?void 0:d.length)?d.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:l,plugin_version:p||"",enabled_features:Array.from(o.a).join(","),active_modules:s.join(","),authenticated:f?"1":"0",user_properties:{user_roles:r,user_identifier:u}});var g=n.createElement("script");return g.setAttribute(i.b,""),g.async=!0,g.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a),n.head.appendChild(g),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a)}}}}}).call(this,n(28))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var r=n(5),i=n.n(r),o=n(6),a=n.n(o),c=n(16),s=n.n(c),l=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function d(e,t,n,r){var o=Object(l.a)(t);return function(){var t=s()(i.a.mark((function t(a,c,s,l){var u;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:a,event_label:s,value:l},t.abrupt("return",new Promise((function(e){var t,n,i=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(a,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(i),e()};o("event",c,g(g({},u),{},{event_callback:s})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,i){return t.apply(this,arguments)}}()}},102:function(e,t,n){"use strict";var r=n(123);n.d(t,"a",(function(){return r.a}));var i=n(124);n.d(t,"c",(function(){return i.a}));var o=n(125);n.d(t,"b",(function(){return o.a}))},104:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return r.createElement("svg",i({viewBox:"0 0 14 14",fill:"none"},e),o)}},105:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),o=n(25),a=n.n(o),c=n(1),s=n.n(c),l=n(11),u=n.n(l);function VisuallyHidden(t){var n=t.className,r=t.children,o=a()(t,["className","children"]);return r?e.createElement("span",i()({},o,{className:u()("screen-reader-text",n)}),r):null}VisuallyHidden.propTypes={className:s.a.string,children:s.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(4))},107:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var r=n(21),i=n.n(r),o=n(152),a=n.n(o),c=n(11),s=n.n(c),l=n(1),u=n.n(l),g=n(2),d=n(10),f=n(154),p=n(104);function TourTooltip(t){var n=t.backProps,r=t.closeProps,c=t.index,l=t.primaryProps,u=t.size,m=t.step,b=t.tooltipProps,v=u>1?Object(f.a)(u):[],h=function(e){return s()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",i()({className:s()("googlesitekit-tour-tooltip",m.className)},b),e.createElement(a.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},m.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},m.content)),e.createElement(o.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},v.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:h(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(d.Button,i()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),m.cta,l.title&&e.createElement(d.Button,i()({className:"googlesitekit-tooltip-button",text:!0},l),l.title))),e.createElement(d.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(p.a,{width:"14",height:"14"}),onClick:r.onClick,"aria-label":Object(g.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(4))},108:function(e,t,n){"use strict";var r=n(434);n.d(t,"a",(function(){return r.a}));var i=n(435);n.d(t,"b",(function(){return i.a}));var o=n(436);n.d(t,"c",(function(){return o.a}));var a=n(437);n.d(t,"d",(function(){return a.a}));var c=n(438);n.d(t,"e",(function(){return c.a}));var s=n(439);n.d(t,"f",(function(){return s.a}));var l=n(272);n.d(t,"g",(function(){return l.a}));var u=n(202);n.d(t,"h",(function(){return u.a}));n(359)},1081:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardPopularKeywordsWidget}));var r=n(6),i=n.n(r),o=n(2),a=n(3),c=n(66),s=n(13),l=n(7),u=n(503),g=n(134),d=n(387),f=n(505),p=n(504),m=n(20),b=n(9),v=n(427),h=n(34);function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function DashboardPopularKeywordsWidget(t){var n=t.Widget,r=t.WidgetReportError,i=Object(h.a)(),y=Object(a.useInViewSelect)((function(e){return e(c.b).isGatheringData()})),_=Object(a.useSelect)((function(e){return e(l.a).getDateRangeDates({offsetDays:c.a})})),k=O(O({},_),{},{dimensions:"query",limit:10}),j=Object(a.useSelect)((function(e){return e(s.c).getCurrentEntityURL()}));j&&(k.url=j);var E=Object(a.useInViewSelect)((function(e){return e(c.b).getReport(k)}),[k]),S=Object(a.useSelect)((function(e){return e(c.b).getErrorForSelector("getReport",[k])})),w=Object(a.useSelect)((function(e){return!e(c.b).hasFinishedResolution("getReport",[k])})),A=Object(a.useSelect)((function(e){return i?null:e(c.b).getServiceReportURL(O(O({},Object(d.b)(_)),{},{page:j?"!".concat(j):void 0}))}));function Footer(){return e.createElement(g.a,{className:"googlesitekit-data-block__source",name:Object(o._x)("Search Console","Service name","google-site-kit"),href:A,external:!0})}if(S)return e.createElement(n,{Footer:Footer},e.createElement(r,{moduleSlug:"search-console",error:S}));if(w||void 0===y)return e.createElement(n,{noPadding:!0,Footer:Footer},e.createElement(u.a,{padding:!0}));var T=[{title:j?Object(o.__)("Top search queries for your page","google-site-kit"):Object(o.__)("Top search queries for your site","google-site-kit"),description:Object(o.__)("Most searched for keywords related to your content","google-site-kit"),primary:!0,field:"keys.0",Component:function Component(t){var n=t.fieldValue,r=Object(a.useSelect)((function(e){if(i)return null;var t=e(l.a).getDateRangeDates({offsetDays:c.a}),r=e(s.c).getCurrentEntityURL();return e(c.b).getServiceReportURL(O(O({},Object(d.b)(t)),{},{query:"!".concat(n),page:r?"!".concat(r):void 0}))}));return i?e.createElement("span",null,n):e.createElement(m.a,{href:r,external:!0,hideExternalIndicator:!0},n)}},{title:Object(o.__)("Clicks","google-site-kit"),description:Object(o.__)("Number of times users clicked on your content in search results","google-site-kit"),Component:function Component(t){var n=t.row;return e.createElement("span",null,Object(b.B)(n.clicks,{style:"decimal"}))}},{title:Object(o.__)("Impressions","google-site-kit"),description:Object(o.__)("Counted each time your content appears in search results","google-site-kit"),Component:function Component(t){var n=t.row;return e.createElement("span",null,Object(b.B)(n.impressions,{style:"decimal"}))}}];return e.createElement(n,{noPadding:!0,Footer:Footer},e.createElement(f.a,null,e.createElement(p.a,{rows:E,columns:T,zeroState:v.d,gatheringData:y})))}}).call(this,n(4))},1082:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),o=n(15),a=n.n(o),c=n(1),s=n.n(c),l=n(14),u=n(0),g=n(2),d=n(106),f=n(3),p=n(66),m=n(13),b=n(7),v=n(9),h=n(44),y=n(803),O=n(1083),_=n(1084),k=n(1087),j=n(427),E=n(19),S=n(17),w=n(24),A=n(34),T=n(8);function C(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function D(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?C(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):C(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function SearchFunnelWidgetGA4(t){var n=t.Widget,r=t.WidgetReportError,i=Object(u.useState)(0),o=a()(i,2),c=o[0],s=o[1],C=Object(w.e)(),R=Object(A.a)(),N=Object(f.useSelect)((function(e){return e(E.a).isModuleAvailable("analytics-4")})),P=Object(f.useSelect)((function(e){return!!N&&(!R||e(b.a).canViewSharedModule("analytics-4"))})),x=Object(f.useSelect)((function(e){return e(E.a).isModuleConnected("analytics-4")})),L=Object(f.useSelect)((function(e){return e(E.a).isModuleActive("analytics-4")})),I=Object(f.useSelect)((function(e){return e(b.a).getDateRangeNumberOfDays()})),M=Object(f.useSelect)((function(e){return e(m.c).getCurrentEntityURL()})),G=Object(f.useSelect)((function(e){return e(b.a).getDateRangeDates({compare:!0,offsetDays:p.a})})),F=G.endDate,B=G.compareStartDate,z=Object(f.useSelect)((function(e){return e(b.a).getDateRangeDates({compare:!0,offsetDays:T.g})})),V=Object(f.useSelect)((function(e){if(!R)return!1;var t=e(E.a).getRecoverableModules();return void 0!==t?Object.keys(t).includes("analytics-4"):void 0})),U=Object(f.useInViewSelect)((function(e){return x&&P&&!V?e(T.r).getConversionEvents():[]}),[x,P,V]),q={startDate:B,endDate:F,dimensions:"date"},W=D(D({},z),{},{metrics:[{name:"conversions"},{name:"engagementRate"}],dimensionFilters:{sessionDefaultChannelGrouping:["Organic Search"]}}),H=D(D(D({},z),W),{},{dimensions:[{name:"date"}],orderby:[{dimension:{dimensionName:"date"}}]}),K=D(D({},z),{},{metrics:[{name:"totalUsers"}],dimensions:[{name:"date"}],dimensionFilters:{sessionDefaultChannelGrouping:["Organic Search"]},orderby:[{dimension:{dimensionName:"date"}}]});Object(d.a)(M)&&(q.url=M,W.url=M,H.url=M,K.url=M);var Y=Object(f.useInViewSelect)((function(e){return e(p.b).getReport(q)}),[q]),$=Object(f.useSelect)((function(e){return e(p.b).getErrorForSelector("getReport",[q])})),Z=Object(f.useSelect)((function(e){return!e(p.b).hasFinishedResolution("getReport",[q])})),J=Object(f.useInViewSelect)((function(e){return x&&P&&!V?e(T.r).getReport(W):null}),[x,P,V,W]),X=Object(f.useInViewSelect)((function(e){return x&&P&&!V?e(T.r).getReport(H):null}),[x,P,V,H]),Q=Object(f.useInViewSelect)((function(e){return x&&P&&!V?e(T.r).getReport(K):null}),[x,P,V,K]),ee=Object(f.useSelect)((function(e){if(!x||!P||V)return!1;var t=e(T.r).hasFinishedResolution;return!(t("getReport",[W])&&t("getReport",[H])&&t("getReport",[K])&&t("getConversionEvents",[]))})),te=Object(f.useSelect)((function(e){if(!x||V)return null;var t=e(T.r).getErrorForSelector;return t("getReport",[W])||t("getReport",[H])||t("getReport",[K])||t("getConversionEvents",[])})),ne=Object(f.useInViewSelect)((function(e){return!(!x||!P||V)&&e(T.r).isGatheringData()}),[x,P,V]),re=Object(f.useInViewSelect)((function(e){return e(p.b).isGatheringData()}));function WidgetFooter(){return e.createElement(O.a,{metrics:SearchFunnelWidgetGA4.metrics,selectedStats:c})}return $?e.createElement(n,{Header:y.a,Footer:WidgetFooter},e.createElement(r,{moduleSlug:"search-console",error:$})):Z||ee||void 0===ne||void 0===re?e.createElement(n,{Header:y.a,Footer:WidgetFooter,noPadding:!0},e.createElement(h.a,{width:"100%",height:"190px",padding:!0}),e.createElement(h.a,{width:"100%",height:"270px",padding:!0})):e.createElement(n,{noPadding:!0,Header:y.a,Footer:WidgetFooter},e.createElement(_.a,{ga4Data:J,ga4ConversionsData:U,ga4VisitorsData:Q,searchConsoleData:Y,handleStatsSelection:s,selectedStats:c,dateRangeLength:I,error:te,WidgetReportError:r,showRecoverableAnalytics:V}),(0===c||1===c)&&e.createElement(k.a,{data:Y,dateRangeLength:I,selectedStats:c,metrics:SearchFunnelWidgetGA4.metrics,gatheringData:re}),P&&(!L||!x)&&w.b===C&&e.createElement(S.e,null,e.createElement(S.k,null,e.createElement(S.a,null,e.createElement(j.a,{title:Object(g.__)("Conversions completed","google-site-kit")})))),2===c&&e.createElement(j.b,{data:Q,dateRangeLength:I,selectedStats:0,metrics:SearchFunnelWidgetGA4.metrics,dataLabels:[Object(g.__)("Unique Visitors","google-site-kit")],tooltipDataFormats:[function(e){return parseFloat(e).toLocaleString()}],statsColor:SearchFunnelWidgetGA4.metrics[c].color,gatheringData:ne,moduleSlug:"analytics-4"}),P&&(3===c||4===c)&&e.createElement(j.b,{data:X,dateRangeLength:I,selectedStats:c-3,metrics:SearchFunnelWidgetGA4.metrics,dataLabels:[Object(g.__)("Conversions","google-site-kit"),Object(g.__)("Engagement Rate %","google-site-kit")],tooltipDataFormats:[function(e){return parseFloat(e).toLocaleString()},function(e){return Object(v.B)(e/100,{style:"percent",signDisplay:"never",maximumFractionDigits:2})}],chartDataFormats:[l.identity,function(e){return 100*e}],statsColor:SearchFunnelWidgetGA4.metrics[c].color,gatheringData:ne,moduleSlug:"analytics-4"}))}SearchFunnelWidgetGA4.metrics=[{id:"impressions",color:"#6380b8",label:Object(g.__)("Impressions","google-site-kit"),metric:"impressions",service:"search-console"},{id:"clicks",color:"#4bbbbb",label:Object(g.__)("Clicks","google-site-kit"),metric:"clicks",service:"search-console"},{id:"users",color:"#3c7251",label:Object(g.__)("Users","google-site-kit"),service:"analytics-4"},{id:"conversions",color:"#8e68cb",label:Object(g.__)("Conversions","google-site-kit"),service:"analytics-4"},{id:"engagement-rate",color:"#8e68cb",label:Object(g.__)("Engagement Rate","google-site-kit"),service:"analytics-4"}],SearchFunnelWidgetGA4.propTypes={Widget:s.a.elementType.isRequired,WidgetReportZero:s.a.elementType.isRequired,WidgetReportError:s.a.elementType.isRequired},t.a=SearchFunnelWidgetGA4}).call(this,n(4))},1083:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),o=n(1),a=n.n(o),c=n(106),s=n(2),l=n(66),u=n(7),g=n(13),d=n(387),f=n(9),p=n(8),m=n(134),b=n(3),v=n(34);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function SourceLinkAnalytics4(){var t=Object(v.a)(),n=Object(b.useSelect)((function(e){if(t)return null;var n=e(p.r).getServiceReportURL,r=e(g.c).getCurrentEntityURL(),i={dates:e(u.a).getDateRangeDates({compare:!0,offsetDays:p.g}),filters:{sessionSource:"google"},otherArgs:{collectionId:"life-cycle"}};return Object(c.a)(r)&&(i.filters.unifiedPagePathScreen=Object(f.t)(r)),n("lifecycle-traffic-acquisition-v2",i)}));return e.createElement(m.a,{href:n,name:Object(s._x)("Analytics","Service name","google-site-kit"),external:!0})}function SourceLinkSearch(t){var n=t.metric,r=Object(v.a)(),o=Object(b.useSelect)((function(e){if(r)return null;var t=e(l.b),o=t.getServiceReportURL,a=t.getPropertyID,c=t.isDomainProperty,s=Object(f.K)(e(g.c).getReferenceSiteURL()),p=e(g.c).getCurrentEntityURL(),m=e(u.a).getDateRangeDates({offsetDays:l.a}),b=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({resource_id:a(),metrics:n},Object(d.b)(m));return p?b.page="!".concat(p):c()&&s&&(b.page="*".concat(s)),o(b)}),[n]);return e.createElement(m.a,{href:o,name:Object(s._x)("Search Console","Service name","google-site-kit"),external:!0})}function Footer(t){var n=t.metrics,r=t.selectedStats;if(!(null==n?void 0:n[r]))return null;var i=n[r],o=i.service,a=i.metric;return"search-console"===o?e.createElement(SourceLinkSearch,{metric:a}):e.createElement(SourceLinkAnalytics4,null)}Footer.propTypes={metrics:a.a.arrayOf(a.a.object).isRequired,selectedStats:a.a.number.isRequired},t.a=Footer}).call(this,n(4))},1084:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Overview}));var r=n(21),i=n.n(r),o=n(6),a=n.n(o),c=n(27),s=n.n(c),l=n(1),u=n.n(l),g=n(14),d=n(0),f=n(2),p=n(3),m=n(17),b=n(387),v=n(9),h=n(19),y=n(7),O=n(13),_=n(66),k=n(8),j=n(52),E=n(236),S=n(34),w=n(18),A=n(1085),T=n(302),C=n(507);function D(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function R(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?D(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):D(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function N(e,t){var n,r,i,o,a,c,s,l,u,g,d,f,p=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return{datapoint:(null==e||null===(n=e.totals)||void 0===n||null===(r=n[0])||void 0===r||null===(i=r.metricValues)||void 0===i||null===(o=i[t])||void 0===o?void 0:o.value)/p,change:Object(v.g)(null==e||null===(a=e.totals)||void 0===a||null===(c=a[1])||void 0===c||null===(s=c.metricValues)||void 0===s||null===(l=s[t])||void 0===l?void 0:l.value,null==e||null===(u=e.totals)||void 0===u||null===(g=u[0])||void 0===g||null===(d=g.metricValues)||void 0===d||null===(f=d[t])||void 0===f?void 0:f.value)}}function Overview(t){var n=t.ga4Data,r=t.ga4ConversionsData,o=t.ga4VisitorsData,a=t.searchConsoleData,c=t.selectedStats,l=t.handleStatsSelection,u=t.dateRangeLength,D=t.error,P=t.WidgetReportError,x=t.showRecoverableAnalytics,L=Object(j.c)(),I=Object(S.a)(),M=Object(w.a)(),G=Object(p.useSelect)((function(e){return e(h.a).isModuleAvailable("analytics-4")})),F=Object(p.useSelect)((function(e){return!!G&&(!I||e(y.a).canViewSharedModule("analytics-4"))})),B=Object(p.useSelect)((function(e){return e(h.a).isModuleConnected("analytics-4")})),z=Object(p.useSelect)((function(e){return e(h.a).isModuleActive("analytics-4")})),V=Object(p.useInViewSelect)((function(e){return!!B&&e(k.r).isGatheringData()}),[B]),U=Object(p.useInViewSelect)((function(e){return e(_.b).isGatheringData()})),q=Object(p.useSelect)((function(e){return e(y.a).isAuthenticated()})),W=Object(p.useSelect)((function(e){return e(O.c).getGoogleSupportURL({path:"/analytics/answer/12195621"})})),H=Object(b.a)(a,u),K=H.totalClicks,Y=H.totalImpressions,$=H.totalClicksChange,Z=H.totalImpressionsChange,J=null,X=null,Q=null,ee=null,te=null,ne=null;if(z&&Object(g.isPlainObject)(n)&&Object(g.isPlainObject)(o)){var re,ie,oe,ae,ce,se,le,ue;J=N(n,0,100).change,X=null==n||null===(re=n.totals)||void 0===re||null===(ie=re[0])||void 0===ie||null===(oe=ie.metricValues)||void 0===oe||null===(ae=oe[0])||void 0===ae?void 0:ae.value;var ge=N(n,1);Q=ge.datapoint,ee=ge.change,te=null==o||null===(ce=o.totals)||void 0===ce||null===(se=ce[0])||void 0===se||null===(le=se.metricValues)||void 0===le||null===(ue=le[0])||void 0===ue?void 0:ue.value,ne=N(o,0,100).change}var de=F&&B&&!D&&!x,fe=Object(d.useCallback)((function(){Object(v.I)("".concat(M,"_ga4-new-badge"),"click_learn_more_link")}),[M]),pe=q&&de&&L===j.b&&(!(null==r?void 0:r.length)||1===(null==r?void 0:r.length)&&"purchase"===r[0].eventName&&"0"===X),me={smSize:2,mdSize:pe?4:2,lgSize:3},be={smSize:4,mdSize:4,lgSize:6},ve=[{id:"impressions",stat:0,title:Object(f.__)("Total Impressions","google-site-kit"),datapoint:Y,change:Z,isGatheringData:U},{id:"clicks",stat:1,title:Object(f.__)("Total Clicks","google-site-kit"),datapoint:K,change:$,isGatheringData:U}].concat(s()(de?[{id:"visitors",stat:2,title:Object(f.__)("Unique Visitors from Search","google-site-kit"),datapoint:te,change:ne,isGatheringData:V}]:[]),s()(de&&L===j.b&&!pe?[{id:"conversions",stat:3,title:Object(f.__)("Conversions","google-site-kit"),datapoint:X,change:J,isGatheringData:V}]:[]),s()(de&&L===j.a?[{id:"engagement-rate",stat:4,title:Object(f.__)("Engagement Rate","google-site-kit"),datapoint:Q,datapointUnit:"%",change:ee,isGatheringData:V,badge:e.createElement(T.a,{tooltipTitle:Object(f.__)("Sessions which lasted 10 seconds or longer, had 1 or more conversion events, or 2 or more page views.","google-site-kit"),learnMoreLink:W,onLearnMoreClick:fe})}]:[])),he={2:be,3:{smSize:4,mdSize:4,lgSize:9},4:{smSize:4,mdSize:8,lgSize:12}},ye={2:R(R({},be),{},{smSize:2}),3:{smSize:2,mdSize:4,lgSize:4},4:me};return e.createElement(m.e,null,e.createElement(m.k,null,e.createElement(m.a,he[ve.length],e.createElement(C.a,{className:"mdc-layout-grid__inner"},ve.map((function(t,n){return e.createElement(m.a,i()({key:t.id},ye[ve.length]),e.createElement(E.a,{stat:t.stat,className:"googlesitekit-data-block--".concat(t.id," googlesitekit-data-block--button-").concat(n+1),title:t.title,datapoint:t.datapoint,datapointUnit:t.datapointUnit?t.datapointUnit:void 0,change:t.change,changeDataUnit:"%",context:"button",selected:c===t.stat,handleStatSelection:l,gatheringData:t.isGatheringData}))})))),e.createElement(A.a,{canViewSharedAnalytics4:F,error:D,halfCellProps:be,quarterCellProps:me,showGA4:de,showConversionsCTA:pe,showRecoverableAnalytics:x,WidgetReportError:P})))}Overview.propTypes={ga4Data:u.a.object,ga4ConversionsData:u.a.arrayOf(u.a.object),ga4VisitorsData:u.a.object,searchConsoleData:u.a.arrayOf(u.a.object),selectedStats:u.a.number.isRequired,handleStatsSelection:u.a.func.isRequired,error:u.a.object,WidgetReportError:u.a.elementType.isRequired}}).call(this,n(4))},1085:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OptionalCells}));var r=n(21),i=n.n(r),o=n(1),a=n.n(o),c=n(0),s=n(2),l=n(3),u=n(17),g=n(19),d=n(427),f=n(1086),p=n(163),m=n(24);function OptionalCells(t){var n=t.canViewSharedAnalytics4,r=t.error,o=t.halfCellProps,a=t.quarterCellProps,b=t.showGA4,v=t.showConversionsCTA,h=t.showRecoverableGA4,y=t.WidgetReportError,O=Object(m.e)(),_=Object(l.useSelect)((function(e){return e(g.a).isModuleConnected("analytics-4")})),k=Object(l.useSelect)((function(e){return e(g.a).isModuleActive("analytics-4")})),j=k&&_;return e.createElement(c.Fragment,null,n&&(!_||!k)&&e.createElement(u.a,o,m.b!==O&&e.createElement(d.a,{title:Object(s.__)("Conversions completed","google-site-kit")})),!h&&n&&j&&r&&e.createElement(u.a,o,e.createElement(y,{moduleSlug:"analytics-4",error:r})),b&&e.createElement(u.a,i()({},a,{smSize:4}),v&&e.createElement(f.a,null)),n&&j&&h&&e.createElement(u.a,o,e.createElement(p.a,{moduleSlugs:["analytics-4"]})))}OptionalCells.propTypes={canViewSharedAnalytics4:a.a.bool.isRequired,error:a.a.object,halfCellProps:a.a.object.isRequired,quarterCellProps:a.a.object.isRequired,showGA4:a.a.bool.isRequired,showConversionsCTA:a.a.bool.isRequired,showRecoverableGA4:a.a.bool,WidgetReportError:a.a.elementType.isRequired}}).call(this,n(4))},1086:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CreateConversionCTA}));var r=n(81),i=n(2),o=n(0),a=n(3),c=n(10),s=n(416),l=n(787),u=n(9),g=n(18),d=n(13);function CreateConversionCTA(){var t=Object(g.a)(),n="".concat(t,"_search-traffic-widget"),f=Object(a.useSelect)((function(e){return e(d.c).getGoogleSupportURL({path:"/analytics/answer/12844695"})})),p=Object(o.useCallback)((function(){Object(u.I)(n,"click_ga4_conversions_cta")}),[n]);return Object(r.a)((function(){Object(u.I)(n,"view_ga4_conversions_cta")})),e.createElement("div",{className:"googlesitekit-analytics-cta googlesitekit-analytics-cta--setup-conversions"},e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graphs"},e.createElement(s.a,{title:Object(i.__)("Conversions completed","google-site-kit"),GraphSVG:l.a})),e.createElement("div",{className:"googlesitekit-analytics-cta__details"},e.createElement("p",{className:"googlesitekit-analytics-cta--description"},Object(i.__)("Set up conversion events to track how well your site fulfills your business objectives","google-site-kit")),e.createElement(c.Button,{href:f,target:"_blank",onClick:p},Object(i.__)("Set up conversions","google-site-kit"))))}}).call(this,n(4))},1087:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SearchConsoleStats}));var r=n(6),i=n.n(r),o=n(15),a=n.n(o),c=n(526),s=n.n(c),l=n(1),u=n.n(l),g=n(2),d=n(387),f=n(17),p=n(89),m=n(366);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function SearchConsoleStats(t){var n=t.data,r=t.metrics,i=t.selectedStats,o=t.dateRangeLength,c=t.gatheringData,l=Object(p.a)(n,{dateRangeLength:o}),u=l.compareRange,g=l.currentRange,b=Object(d.c)(g,u,r[i].label,r[i].metric,o),h=b.slice(1).map((function(e){return a()(e,1)[0]})),y=s()(h).slice(1),O=v(v({},SearchConsoleStats.chartOptions),{},{hAxis:{format:"MMM d",gridlines:{color:"#fff"},textStyle:{color:"#616161",fontSize:12},ticks:y},series:{0:{color:r[i].color,targetAxisIndex:0},1:{color:r[i].color,targetAxisIndex:0,lineDashStyle:[3,3],lineWidth:1}}}),_=!b.slice(1).some((function(e){return e[2]>0||e[3]>0}));return O.vAxis.viewWindow.max=_?1:void 0,e.createElement(f.e,{className:"googlesitekit-search-console-site-stats"},e.createElement(f.k,null,e.createElement(f.a,{size:12},e.createElement(m.a,{chartType:"LineChart",data:b,loadingHeight:"270px",loadingWidth:"100%",options:O,gatheringData:c}))))}SearchConsoleStats.propTypes={data:u.a.arrayOf(u.a.object).isRequired,dateRangeLength:u.a.number.isRequired,metrics:u.a.arrayOf(u.a.object).isRequired,selectedStats:u.a.number.isRequired},SearchConsoleStats.chartOptions={chart:{title:Object(g.__)("Search Traffic Summary","google-site-kit")},curveType:"function",height:270,width:"100%",chartArea:{height:"80%",left:60,right:25},legend:{position:"top",textStyle:{color:"#616161",fontSize:12}},vAxis:{direction:1,gridlines:{color:"#eee"},minorGridlines:{color:"#eee"},textStyle:{color:"#616161",fontSize:12},titleTextStyle:{color:"#616161",fontSize:12,italic:!1},viewWindow:{min:0}},tooltip:{isHtml:!0,trigger:"both"},focusTarget:"category",crosshair:{color:"gray",opacity:.1,orientation:"vertical",trigger:"both"}}}).call(this,n(4))},1088:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PopularKeywordsWidget}));var r=n(6),i=n.n(r),o=n(1),a=n.n(o),c=n(2),s=n(3),l=n(7),u=n(66),g=n(387),d=n(9),f=n(20),p=n(34),m=n(108),b=n(427);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function PopularKeywordsWidget(t){var n=t.Widget,r=Object(p.a)(),i=Object(s.useSelect)((function(e){return e(l.a).getDateRangeDates({offsetDays:u.a})})),o=h(h({},i),{},{dimensions:"query",limit:100}),a=Object(s.useInViewSelect)((function(e){return e(u.b).getReport(o)}),[o]),v=Object(s.useSelect)((function(e){return e(u.b).getErrorForSelector("getReport",[o])}),[o]),y=Object(s.useSelect)((function(e){return!e(u.b).hasFinishedResolution("getReport",[o])})),O=Object(g.b)(i),_=[{field:"keys.0",Component:function Component(t){var n=t.fieldValue,i=Object(s.useSelect)((function(e){return r?null:e(u.b).getServiceReportURL(h(h({},O),{},{query:"!".concat(n)}))}));return r?e.createElement(m.f,{content:n}):e.createElement(f.a,{href:i,external:!0,hideExternalIndicator:!0},n)}},{field:"ctr",Component:function Component(t){var n=t.fieldValue;return e.createElement("strong",null,Object(c.sprintf)(
/* translators: %s: clickthrough rate value */
Object(c.__)("%s CTR","google-site-kit"),Object(d.B)(n,"%")))}}],k=(a||[]).sort((function(e,t){var n=e.ctr,r=void 0===n?0:n,i=t.ctr;return(void 0===i?0:i)-r}));return e.createElement(m.e,{Widget:n,widgetSlug:l.G,loading:y,rows:k,columns:_,ZeroState:b.d,limit:3,error:v,moduleSlug:"search-console"})}PopularKeywordsWidget.propTypes={Widget:a.a.elementType.isRequired}}).call(this,n(4))},1089:function(e,t,n){"use strict";(function(e){var r,i=n(5),o=n.n(i),a=n(6),c=n.n(a),s=n(12),l=n.n(s),u=n(14),g=n(45),d=n.n(g),f=n(3),p=n(7),m=n(13),b=n(66),v=n(9),h=n(48),y=n(178),O=n(387),_=n(679);function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function j(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){c()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var E=Object(h.a)({baseName:"getReport",storeName:b.b,controlCallback:function(e){var t=e.options;return d.a.get("modules","search-console","searchanalytics",t)},reducerCallback:function(e,t,n){var r=n.options;return j(j({},e),{},{reports:j(j({},e.reports),{},c()({},Object(v.H)(r),t))})},argsToParams:function(e){return{options:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.options;l()(Object(u.isPlainObject)(t),"Options for Search Console report must be an object."),l()(Object(y.a)(t),"Either date range or start/end dates must be provided for Search Console report.");var n=t.dimensions;n&&l()(Object(y.d)(n),"Dimensions for Search Console report must be either a string or an array of strings")}}),S=Object(_.a)("search-console",{storeName:b.b,dataAvailable:null===(r=e._googlesitekitModulesData)||void 0===r?void 0:r["data_available_search-console"],selectDataAvailability:Object(f.createRegistrySelector)((function(e){return function(){var t=e(b.b).getSampleReportArgs(),n=e(b.b).getReport(t);if(e(b.b).hasFinishedResolution("getReport",[t]))return e(b.b).getErrorForSelector("getReport",[t])||!Array.isArray(n)?null:!!n.length}}))}),w={getReport:o.a.mark((function e(){var t,n,r=arguments;return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.length>0&&void 0!==r[0]?r[0]:{},e.next=3,f.commonActions.getRegistry();case 3:if(n=e.sent,!n.select(b.b).getReport(t)){e.next=7;break}return e.abrupt("return");case 7:return e.next=9,E.actions.fetchGetReport(t);case 9:case"end":return e.stop()}}),e)}))},A={getReport:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.reports;return n[Object(v.H)(t)]},hasZeroData:Object(f.createRegistrySelector)((function(e){return function(){var t=e(b.b).isGatheringData();if(void 0!==t){if(!0===t)return!0;var n=e(b.b).getSampleReportArgs(),r=e(b.b).getReport(n);if(e(b.b).hasFinishedResolution("getReport",[n]))return!!Array.isArray(r)&&Object(O.e)(r)}}})),getSampleReportArgs:Object(f.createRegistrySelector)((function(e){return function(){var t=e(m.c).getCurrentEntityURL(),n=e(p.a).getDateRangeDates({compare:!0,offsetDays:b.a}),r={startDate:n.compareStartDate,endDate:n.endDate,dimensions:"date"};return t&&(r.url=t),r}}))},T=Object(f.combineStores)(E,S,{initialState:{reports:{}},resolvers:w,selectors:A});T.initialState,T.actions,T.controls,T.reducer,T.resolvers,T.selectors;t.a=T}).call(this,n(28))},114:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return u}));var r=n(6),i=n.n(r),o=n(1),a=n.n(o),c=n(11),s=n.n(c),l=n(2),u={DEFAULT:"default",OVERLAY:"overlay",SMALL:"small",SMALL_OVERLAY:"small-overlay",LARGE:"large"};function GatheringDataNotice(t){var n=t.style;return e.createElement("div",{className:s()("googlesitekit-gathering-data-notice",i()({},"googlesitekit-gathering-data-notice--has-style-".concat(n),!!n))},e.createElement("span",null,Object(l.__)("Gathering data…","google-site-kit")))}GatheringDataNotice.propTypes={style:a.a.oneOf(Object.values(u))},t.b=GatheringDataNotice}).call(this,n(4))},117:function(e,t,n){"use strict";var r=n(326),i=n(314);n.d(t,"b",(function(){return i.a}));var o=n(315);n.d(t,"c",(function(){return o.a}));var a=n(316);n.d(t,"d",(function(){return a.a}));var c=n(317);n.d(t,"a",(function(){return c.a})),t.e=r.a},120:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var r=n(1),i=n.n(r),o=n(0),a=n(2),c=n(3),s=n(10),l=n(35),u=n(54);function ErrorNotice(t){var n,r=t.error,i=t.hasButton,g=void 0!==i&&i,d=t.storeName,f=t.message,p=void 0===f?r.message:f,m=t.noPrefix,b=void 0!==m&&m,v=t.skipRetryMessage,h=t.Icon,y=Object(c.useDispatch)(),O=Object(c.useSelect)((function(e){return d?e(d).getSelectorDataForError(r):null})),_=Object(o.useCallback)((function(){y(O.storeName).invalidateResolution(O.name,O.args)}),[y,O]);if(!r||Object(l.f)(r))return null;var k=g&&Object(l.d)(r,O);return g||v||(p=Object(a.sprintf)(
/* translators: %1$s: Error message from Google API. */
Object(a.__)("%1$s%2$s Please try again.","google-site-kit"),p,p.endsWith(".")?"":".")),e.createElement(o.Fragment,null,h&&e.createElement("div",{className:"googlesitekit-error-notice__icon"},e.createElement(h,{width:"24",height:"24"})),e.createElement(u.a,{message:p,reconnectURL:null===(n=r.data)||void 0===n?void 0:n.reconnectURL,noPrefix:b}),k&&e.createElement(s.Button,{className:"googlesitekit-error-notice__retry-button",onClick:_},Object(a.__)("Retry","google-site-kit")))}ErrorNotice.propTypes={error:i.a.shape({message:i.a.string}),hasButton:i.a.bool,storeName:i.a.string,message:i.a.string,noPrefix:i.a.bool,skipRetryMessage:i.a.bool,Icon:i.a.elementType}}).call(this,n(4))},121:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(219),i=n(14),o=n(0);function a(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=Object(r.b)((function(){return i.debounce.apply(void 0,t)}),t);return Object(o.useEffect)((function(){return function(){return a.cancel()}}),[a]),a}},122:function(e,t,n){"use strict";n.d(t,"d",(function(){return c})),n.d(t,"b",(function(){return s})),n.d(t,"a",(function(){return l})),n.d(t,"c",(function(){return u})),n.d(t,"e",(function(){return g}));var r=n(33),i=n.n(r),o=n(14),a=n(178);function c(e){var t=function(e){return"string"==typeof e&&/^[a-zA-Z0-9_]+$/.test(e)};return"string"==typeof e?e.split(",").every(t):Object(a.c)(e,(function(e){var n=e.hasOwnProperty("name")&&t(e.name);if(!e.hasOwnProperty("expression"))return n;var r="string"==typeof e.expression;return n&&r}),t)}function s(e){return Object(a.c)(e,(function(e){return e.hasOwnProperty("name")&&"string"==typeof e.name}))}function l(e){var t=["string"];return Object.keys(e).every((function(n){if(t.includes(i()(e[n])))return!0;if(Array.isArray(e[n]))return e[n].every((function(e){return t.includes(i()(e))}));if(Object(o.isPlainObject)(e[n])){var r=Object.keys(e[n]);return!!r.includes("filterType")&&!("emptyFilter"!==e[n].filterType&&!r.includes("value"))}return!1}))}function u(e){var t=["string"],n=["numericFilter","betweenFilter"];return Object.values(e).every((function(e){if(t.includes(i()(e)))return!0;if(Array.isArray(e))return e.every((function(e){return t.includes(i()(e))}));if(!Object(o.isPlainObject)(e))return!1;var r=e.filterType,a=e.value,c=e.fromValue,s=e.toValue;if(r&&!n.includes(r))return!1;var l=Object.keys(e);return r&&"numericFilter"!==r?"betweenFilter"===r&&(l.includes("fromValue")&&l.includes("toValue")&&[c,s].every((function(e){return!Object(o.isPlainObject)(e)||"int64Value"in e}))):l.includes("operation")&&l.includes("value")&&(!Object(o.isPlainObject)(a)||"int64Value"in a)}))}function g(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(o.isPlainObject)(e)&&((!e.hasOwnProperty("desc")||"boolean"==typeof e.desc)&&(e.metric?!e.dimension&&"string"==typeof(null===(t=e.metric)||void 0===t?void 0:t.metricName):!!e.dimension&&"string"==typeof(null===(n=e.dimension)||void 0===n?void 0:n.dimensionName)));var t,n}))}},123:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(21),i=n.n(r),o=n(6),a=n.n(o),c=n(25),s=n.n(c),l=n(1),u=n.n(l),g=n(11),d=n.n(g);function Cell(t){var n,r=t.className,o=t.alignTop,c=t.alignMiddle,l=t.alignBottom,u=t.alignRight,g=t.alignLeft,f=t.smAlignRight,p=t.mdAlignRight,m=t.lgAlignRight,b=t.smSize,v=t.smStart,h=t.smOrder,y=t.mdSize,O=t.mdStart,_=t.mdOrder,k=t.lgSize,j=t.lgStart,E=t.lgOrder,S=t.size,w=t.children,A=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",i()({},A,{className:d()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":o,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":l,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":g,"mdc-layout-grid__cell--align-right-phone":f,"mdc-layout-grid__cell--align-right-tablet":p,"mdc-layout-grid__cell--align-right-desktop":m},a()(n,"mdc-layout-grid__cell--span-".concat(S),12>=S&&S>0),a()(n,"mdc-layout-grid__cell--span-".concat(k,"-desktop"),12>=k&&k>0),a()(n,"mdc-layout-grid__cell--start-".concat(j,"-desktop"),12>=j&&j>0),a()(n,"mdc-layout-grid__cell--order-".concat(E,"-desktop"),12>=E&&E>0),a()(n,"mdc-layout-grid__cell--span-".concat(y,"-tablet"),8>=y&&y>0),a()(n,"mdc-layout-grid__cell--start-".concat(O,"-tablet"),8>=O&&O>0),a()(n,"mdc-layout-grid__cell--order-".concat(_,"-tablet"),8>=_&&_>0),a()(n,"mdc-layout-grid__cell--span-".concat(b,"-phone"),4>=b&&b>0),a()(n,"mdc-layout-grid__cell--start-".concat(v,"-phone"),4>=v&&v>0),a()(n,"mdc-layout-grid__cell--order-".concat(h,"-phone"),4>=h&&h>0),n))}),w)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},124:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),o=n(25),a=n.n(o),c=n(1),s=n.n(c),l=n(11),u=n.n(l),g=n(0),d=Object(g.forwardRef)((function(t,n){var r=t.className,o=t.children,c=a()(t,["className","children"]);return e.createElement("div",i()({ref:n,className:u()("mdc-layout-grid__inner",r)},c),o)}));d.displayName="Row",d.propTypes={className:s.a.string,children:s.a.node},d.defaultProps={className:""},t.a=d}).call(this,n(4))},125:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),o=n(25),a=n.n(o),c=n(1),s=n.n(c),l=n(11),u=n.n(l),g=n(0),d=Object(g.forwardRef)((function(t,n){var r=t.alignLeft,o=t.fill,c=t.className,s=t.children,l=t.collapsed,g=a()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",i()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":l,"mdc-layout-grid--fill":o})},g,{ref:n}),s)}));d.displayName="Grid",d.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},d.defaultProps={className:""},t.a=d}).call(this,n(4))},126:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),o)}},127:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",i({viewBox:"0 0 13 13"},e),o)}},128:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),o)}},1296:function(e,t,n){"use strict";n.r(t);var r=n(3),i=n.n(r),o=n(190),a=n.n(o),c=n(401),s=n.n(c),l=n(923),u=(n(759),n(927)),g=n(1081),d=n(1082),f=n(88),p=n(0);function m(){return(m=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var b=p.createElement("path",{fill:"#525252",d:"M97.15 2.73c.46-.55 1.37-1.64 1.82-2.2 27-.1 53.98-.04 80.97-.02.44.5 1.32 1.5 1.75 2 .1.15.4.43.5.57.18.16.5.48.68.65.25.23.77.67 1.03.9.33.3.98.92 1.3 1.24.26.25.76.75 1 1 .25.23.7.7.96.94.25.25.74.75.98 1 .25.25.76.75 1 1 .27.26.78.76 1.03 1.02.25.25.76.75 1.02 1 .23.25.72.73.96.97l1 1c.24.24.73.73.98 1 .3.3.93.94 1.24 1.26.23.26.67.78.9 1.04.17.2.5.55.67.73.18.2.56.6.75.78.43.45 1.3 1.3 1.72 1.76.14.15.43.46.57.6.07.13.25.37.33.48.27.56.8 1.67 1.08 2.22.2 5.67-.07 11.34.06 17-7.7-.1-15.4-.1-23.08.02.26-.16.8-.47 1.07-.63-.7-6.6-.7-13.24-.58-19.87-26.28-.02-52.56-.02-78.83 0 .17 6.83-.08 13.64-.44 20.46l1.27.04c-7.8-.1-15.63-.12-23.44-.02.12-5.66-.14-11.32.03-16.98.3-.57.86-1.72 1.15-2.3.1-.1.26-.34.35-.46.15-.16.45-.47.6-.63.44-.45 1.33-1.3 1.77-1.75.2-.18.6-.56.78-.75.22-.22.65-.64.86-.85.24-.23.7-.7.94-.94.25-.24.74-.73.98-1 .25-.22.74-.72 1-.96.23-.26.72-.75.96-1 .24-.23.7-.7.94-.95.2-.23.63-.67.84-.9.32-.32.96-1 1.28-1.33l1.28-1.2c.2-.2.62-.6.83-.8.23-.22.7-.68.9-.9.26-.24.75-.73 1-.97s.72-.73.97-.97c.23-.24.7-.7.94-.94l.8-.8c.1-.14.35-.4.46-.54zm37.7 166.57c2.8-14.95 12.87-28.3 26.16-35.56.2 12.76.08 25.53-.04 38.3.03 2.54-.08 5.12.42 7.63 6.28 4 13.18 6.96 19.6 10.74 6.25-3.25 12.47-6.6 18.7-9.9 2.18-.66 1.4-3.2 1.62-4.93-.06-13.9-.1-27.82 0-41.73 10.12 4.16 17.33 12.82 23 21.85 1.95 6.62 4.35 13.27 4.08 20.3.38 16.85-9.32 33.53-24.3 41.32-1.08.74-2.8 1.17-2.66 2.8-.96 9.1-.42 18.3.17 27.4h-41c.3-9.72.8-19.43-.05-29.14-7.56-3.6-14.02-9.35-18.68-16.3-6.28-9.55-8.7-21.48-7.03-32.76z"}),v=p.createElement("path",{fill:"#4d4d4d",d:"M96.68 3.26c.12-.13.36-.4.47-.53-.1.13-.35.4-.47.53zm85.02-.76c.12.14.4.42.52.56-.13-.14-.4-.42-.53-.56zM95.9 4.06c.2-.2.6-.6.78-.8-.2.2-.6.6-.78.8zm86.32-1c.17.16.5.48.67.65-.2-.15-.5-.47-.7-.63zM94.96 5c.23-.24.7-.7.94-.94-.24.24-.7.7-.94.93zm87.94-1.3c.25.23.77.67 1.03.9-.26-.23-.78-.67-1.04-.9zM93.98 5.96c.24-.24.73-.73.98-.97-.25.22-.74.7-.98.95zm89.95-1.36c.32.3.97.92 1.3 1.24-.33-.32-.98-.94-1.3-1.25zM93 6.93c.25-.24.74-.73.98-.97l-.97.97zm92.22-1.1l1 1-1-1zm-93.12 2c.22-.22.68-.7.9-.9-.22.2-.68.68-.9.9zm94.13-1l.95.95c-.24-.24-.7-.7-.95-.94zm-94.97 1.8c.2-.2.62-.6.83-.8-.23.2-.64.6-.85.8zm95.92-.85zm-97.2 2.05l1.28-1.2-1.28 1.2zm98.18-1.06c.25.26.76.76 1 1.02-.24-.28-.75-.78-1-1.04zm-99.46 2.4c.32-.33.96-1 1.28-1.34-.32.34-.96 1-1.28 1.34zM189.17 9.8c.26.24.77.74 1.02 1-.27-.26-.78-.76-1.04-1zm-101.3 2.25c.2-.22.62-.66.83-.88-.2.22-.63.66-.84.88zM190.2 10.8c.25.25.76.75 1.02 1-.26-.25-.77-.75-1.03-1zM86.92 13c.24-.23.7-.7.94-.95-.23.24-.7.72-.94.96zm104.3-1.2c.24.25.73.73.97.97-.26-.24-.75-.72-1-.96zM85.95 14c.24-.26.73-.75.97-1-.24.25-.73.74-.97 1zm106.25-1.23zm-107.24 2.2c.25-.24.74-.74 1-.98-.26.22-.75.72-1 .96zm108.24-1.2c.24.25.73.74.98 1-.25-.26-.74-.75-1-1zm-109.22 2.2c.25-.26.74-.75.98-1-.24.25-.73.74-.98 1zm110.2-1.2c.3.3.93.95 1.24 1.27-.3-.32-.93-.96-1.24-1.28zM83.04 16.9c.24-.23.7-.7.94-.94l-.94.94zm112.38-.86c.23.26.67.78.9 1.04-.23-.26-.67-.78-.9-1.04zm-113.24 1.7c.22-.2.65-.63.86-.84-.2.2-.64.63-.86.85zm114.14-.66c.17.2.5.55.67.73-.2-.16-.5-.52-.7-.7zM81.4 18.5c.2-.18.6-.56.78-.75-.2.2-.6.57-.8.76zm115.6-.7c.18.2.56.6.75.78-.2-.2-.57-.58-.76-.77zM79.02 20.88c.15-.16.45-.47.6-.63-.15.16-.45.47-.6.63zm120.45-.55zm-120.8 1c.1-.1.26-.33.35-.45-.1.12-.26.35-.34.46zm121.37-.4c.08.13.26.37.34.48-.08-.1-.26-.33-.34-.45z",opacity:.47}),h=p.createElement("path",{fill:"#d8d9da",d:"M40.3 40.66c12.4-.04 24.8.04 37.2-.04 7.8-.1 15.62-.1 23.43.02 25.83.02 51.66.02 77.5 0 7.68-.1 15.38-.12 23.08-.02 13.27.05 26.5.06 39.76 0C253.9 53.07 266.42 65.67 279 78.2v165.98c-1.32 1.53-2.82 3.45-5.07 3.3-8.86.12-17.72 0-26.57.04-.42-18.84-.05-37.68-.18-56.52-.1-4.3.55-8.73-.8-12.9 1.9-4.46.78-9.4 1.04-14.1-.2-15.05.4-30.14-.3-45.2-.66-11.58-.07-23.2-.3-34.8-.15-1.27.04-2.84-1-3.78-2.52-2.72-6.54-1.6-9.8-1.8-64.34.05-128.67.04-193 0-3.28.2-7.3-.97-9.8 1.8-1.05.92-.9 2.46-1.05 3.7-.24 11.63.4 23.27-.3 34.88-.26 3.07-.36 6.15-.32 9.23.15 39.83-.14 79.66.14 119.5-8.9-.26-17.78.14-26.65-.05-2.24.14-3.72-1.78-5.06-3.27V78.3c13.35-12.6 26.9-25.07 40.3-37.64zM52.6 86.8c6.7-1.8 13.87 5.82 11.3 12.38-1.67 5.15-8.2 8.87-13.24 6.06-3.68-1.78-6.6-5.98-5.97-10.17.94-3.9 3.8-7.48 7.9-8.26zm19.16 9.23c-.2-6.04 6.67-11.2 12.37-8.95 4.2 1 6.32 5.1 7.3 8.96-.83 3.42-2 7.26-5.5 8.8-5.96 4.1-14.94-1.8-14.17-8.8zm-26.43 38.3c23.17-.1 46.34.07 69.5-.05.3 26.85.12 53.72.08 80.58-23.2 0-46.4.14-69.6-.05.13-26.8.05-53.63.04-80.45zm.8 96.92c31.16-.04 62.33-.07 93.5.02 0 5.4-.08 10.82.12 16.23-2.8-.12-5.63-.16-8.44-.1-3.52.13-7.07.16-10.6-.02-1.46-.03-2.9-.03-4.37 0-23.74.25-47.48-.03-71.23.13 1.76-5.24.62-10.83 1.03-16.24z"}),y=p.createElement("path",{fill:"#4189f8",d:"M33.22 80.23c2.5-2.78 6.52-1.6 9.8-1.8 64.33.03 128.66.04 193-.02 3.26.24 7.28-.9 9.8 1.83 1.04.94.85 2.5 1 3.78.23 11.6-.36 23.23.3 34.8-71.75.1-143.5.1-215.26 0 .7-11.6.07-23.24.3-34.87.16-1.24 0-2.78 1.06-3.7m19.38 6.6c-4.1.77-6.95 4.35-7.9 8.25-.65 4.2 2.28 8.4 5.96 10.17 5.03 2.8 11.57-.9 13.24-6.06 2.57-6.58-4.6-14.2-11.3-12.4m19.16 9.23c-.77 7 8.2 12.9 14.16 8.82 3.5-1.55 4.68-5.4 5.5-8.8-.97-3.86-3.1-7.97-7.3-8.97-5.7-2.25-12.55 2.9-12.36 8.95z"}),O=p.createElement("path",{fill:"#fff",d:"M31.86 118.8c71.75.1 143.5.1 215.27 0 .68 15.06.08 30.15.3 45.2-.27 4.7.86 9.64-1.05 14.1-7.74-7.1-13.8-15.87-22.07-22.42-5.66-9.03-12.87-17.7-23-21.85-.1 13.9-.05 27.82 0 41.73-2.63-1.17-4.42-3.44-6.27-5.52-4.96-5.4-10.38-10.37-15.13-15.95-6.08-7-13.2-13.03-18.9-20.37-13.28 7.26-23.35 20.6-26.15 35.56-1.67 11.27.75 23.2 7.03 32.76 4.66 6.94 11.12 12.7 18.68 16.3.85 9.7.35 19.4.04 29.13h-20.85c-.2-5.4-.13-10.83-.12-16.24-31.17-.1-62.34-.06-93.5-.02-.4 5.4.73 11-1.04 16.26-4.48 0-8.95-.02-13.4.02-.3-39.83 0-79.66-.16-119.5-.04-3.07.06-6.15.3-9.22m13.48 15.54c0 26.82.1 53.65-.05 80.47 23.2.2 46.42.08 69.63.07.05-26.86.22-53.73-.07-80.58-23.16.1-46.33-.04-69.5.05z"}),_=p.createElement("path",{fill:"#bdbdbd",d:"M161 133.74c5.7 7.34 12.82 13.36 18.9 20.35 4.74 5.56 10.16 10.53 15.12 15.93 1.85 2.08 3.64 4.35 6.27 5.52-.24 1.72.54 4.27-1.64 4.93-6.23 3.3-12.45 6.64-18.7 9.9-6.42-3.77-13.32-6.74-19.6-10.73-.5-2.5-.4-5.1-.42-7.64.12-12.75.24-25.52.06-38.3zm63.3 21.94c8.27 6.55 14.34 15.32 22.08 22.42 1.35 4.17.7 8.6.8 12.9.13 18.84-.24 37.68.18 56.52-15.26-.04-30.5-.02-45.77 0-.6-9.13-1.15-18.32-.2-27.43-.13-1.64 1.6-2.07 2.66-2.8 15-7.8 24.7-24.47 24.3-41.33.28-7.02-2.12-13.67-4.06-20.3z"});var k=function SvgSearchConsole(e){return p.createElement("svg",m({viewBox:"0 0 279 248"},e),b,v,h,y,O,_)},j=n(66),E=n(1088),S=n(7),w=n(5),A=n.n(w),T=n(16),C=n.n(T),D=n(12),R=n.n(D),N=n(45),P=n.n(N),x=n(62),L=n(387),I=n(200),M={selectors:{areSettingsEditDependenciesLoaded:Object(r.createRegistrySelector)((function(e){return function(){return e(j.b).hasFinishedResolution("getMatchedProperties")}}))}};function G(){return(G=C()(A.a.mark((function e(t){var n,r,i,o;return A.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.select,r=t.dispatch,!n(j.b).haveSettingsChanged()){e.next=8;break}return e.next=4,r(j.b).saveSettings();case 4:if(i=e.sent,!(o=i.error)){e.next=8;break}return e.abrupt("return",{error:o});case 8:return e.next=10,P.a.invalidateCache("modules","search-console");case 10:return e.abrupt("return",{});case 11:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var F=a.a.createModuleStore("search-console",{ownedSettingsSlugs:["propertyID"],storeName:j.b,settingSlugs:["propertyID","ownerID"],requiresSetup:!1,submitChanges:function(e){return G.apply(this,arguments)},validateCanSubmitChanges:function(e){var t=Object(x.e)(e)(j.b),n=t.getPropertyID,r=t.haveSettingsChanged;R()(Object(L.d)(n()),"a valid propertyID is required to submit changes"),R()(r(),I.b)}}),B=n(1089),z=n(6),V=n.n(z),U=n(25),q=n.n(U),W=n(157),H=n(13),K=n(9);function Y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function $(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(n),!0).forEach((function(t){V()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Z={getServiceURL:Object(r.createRegistrySelector)((function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.path,i=n.query,o="https://search.google.com/search-console";if(r){var a="/".concat(r.replace(/^\//,""));o="".concat(o).concat(a)}i&&(o=Object(W.a)(o,i));var c=e(S.a).getAccountChooserURL(o);if(void 0!==c)return c}})),getServiceReportURL:Object(r.createRegistrySelector)((function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e(j.b).getPropertyID(),i=Z.isDomainProperty(t),o=e(H.c).getReferenceSiteURL(),a=n.page,c=void 0===a?i?"*".concat(Object(K.K)(o)):void 0:a,s=q()(n,["page"]),l="/performance/search-analytics",u=$($({page:c},s),{},{resource_id:r});return Z.getServiceURL(t,{path:l,query:u})}})),getServiceEntityAccessURL:Object(r.createRegistrySelector)((function(e){return function(t){var n={resource_id:e(j.b).getPropertyID()};return Z.getServiceURL(t,{query:n})}})),isDomainProperty:Object(r.createRegistrySelector)((function(e){return function(){var t=e(j.b).getPropertyID();return t&&t.startsWith("sc-domain:")}}))},J={selectors:Z},X=n(48);function Q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ee(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Q(Object(n),!0).forEach((function(t){V()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Q(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var te,ne=Object(X.a)({baseName:"getMatchedProperties",controlCallback:function(){return P.a.get("modules","search-console","matched-sites",{},{useCache:!0})},reducerCallback:function(e,t){return ee(ee({},e),{},{properties:t})}}),re={properties:void 0},ie={getMatchedProperties:A.a.mark((function e(){var t;return A.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.commonActions.getRegistry();case 2:if(t=e.sent,void 0!==t.select(j.b).getMatchedProperties()){e.next=7;break}return e.next=7,ne.actions.fetchGetMatchedProperties();case 7:case"end":return e.stop()}}),e)}))},oe=Object(r.combineStores)(ne,{initialState:re,actions:{},controls:{},reducer:function(e,t){t.type;return e},resolvers:ie,selectors:{getMatchedProperties:function(e){return e.properties}}}),ae=(oe.initialState,oe.actions,oe.controls,oe.reducer,oe.resolvers,oe.selectors,oe),ce=Object(r.combineStores)(F,B.a,J,M,ae);ce.initialState,ce.actions,ce.controls,ce.reducer,ce.resolvers,ce.selectors;i.a.registerStore(j.b,ce),a.a.registerModule("search-console",{storeName:j.b,SettingsEditComponent:u.a,SettingsViewComponent:l.a,Icon:k}),(te=s.a).registerWidget("searchConsolePopularKeywords",{Component:g.a,width:[te.WIDGET_WIDTHS.HALF,te.WIDGET_WIDTHS.FULL],priority:1,wrapWidget:!1,modules:["search-console"]},[f.AREA_MAIN_DASHBOARD_CONTENT_PRIMARY,f.AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY]),te.registerWidget("searchFunnelGA4",{Component:d.a,width:[te.WIDGET_WIDTHS.FULL],priority:3,wrapWidget:!1,modules:["search-console"]},[f.AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY,f.AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY]),te.registerWidget(S.G,{Component:E.a,width:te.WIDGET_WIDTHS.QUARTER,priority:2,wrapWidget:!1,modules:["search-console"],isActive:function(e){return e(S.a).isKeyMetricActive(S.G)}},[f.AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY])},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var r="core/site",i="primary",o="secondary"},131:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),o)}},132:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InfoTooltip}));var r=n(11),i=n.n(r),o=n(1),a=n.n(o),c=n(10),s=n(325);function InfoTooltip(t){var n=t.onOpen,r=t.title,o=t.tooltipClassName;return r?e.createElement(c.Tooltip,{className:"googlesitekit-info-tooltip",tooltipClassName:i()("googlesitekit-info-tooltip__content",o),title:r,placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,interactive:!0,onOpen:n},e.createElement("span",null,e.createElement(s.a,{width:"16",height:"16"}))):null}InfoTooltip.propTypes={onOpen:a.a.func,title:a.a.oneOfType([a.a.string,a.a.element]),tooltipClassName:a.a.string}}).call(this,n(4))},134:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),o=n(11),a=n.n(o),c=n(38),s=n(2),l=n(20),u=n(34);function SourceLink(t){var n=t.name,r=t.href,i=t.className,o=t.external;return Object(u.a)()?null:e.createElement("div",{className:a()("googlesitekit-source-link",i)},Object(c.a)(Object(s.sprintf)(
/* translators: %s: source link */
Object(s.__)("Source: %s","google-site-kit"),"<a>".concat(n,"</a>")),{a:e.createElement(l.a,{key:"link",href:r,external:o})}))}SourceLink.propTypes={name:i.a.string,href:i.a.string,className:i.a.string,external:i.a.bool},SourceLink.defaultProps={name:"",href:"",className:"",external:!1},t.a=SourceLink}).call(this,n(4))},135:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportErrorActions}));var r=n(6),i=n.n(r),o=n(1),a=n.n(o),c=n(0),s=n(38),l=n(2),u=n(3),g=n(10),d=n(13),f=n(19),p=n(35),m=n(34),b=n(20);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportErrorActions(t){var n=t.moduleSlug,r=t.error,i=t.GetHelpLink,o=t.hideGetHelpLink,a=t.buttonVariant,v=t.onRetry,y=t.onRequestAccess,O=t.getHelpClassName,_=t.RequestAccessButton,k=t.RetryButton,j=Object(m.a)(),E=Object(u.useSelect)((function(e){return e(f.a).getModuleStoreName(n)})),S=Object(u.useSelect)((function(e){var t;return"function"==typeof(null===(t=e(E))||void 0===t?void 0:t.getServiceEntityAccessURL)?e(E).getServiceEntityAccessURL():null})),w=Array.isArray(r)?r:[r],A=Object(u.useSelect)((function(e){return w.map((function(t){var n,r=null===(n=e(E))||void 0===n?void 0:n.getSelectorDataForError(t);return h(h({},t),{},{selectorData:r})}))})),T=null==A?void 0:A.filter((function(e){return Object(p.d)(e,e.selectorData)&&"getReport"===e.selectorData.name})),C=!!T.length,D=Object(u.useSelect)((function(e){var t=h({},C?T[0]:w[0]);return Object(p.e)(t)&&(t.code="".concat(n,"_insufficient_permissions")),e(d.c).getErrorTroubleshootingLinkURL(t)})),R=Object(u.useDispatch)(),N=w.some((function(e){return Object(p.e)(e)})),P=Object(c.useCallback)((function(){T.forEach((function(e){var t=e.selectorData;R(t.storeName).invalidateResolution(t.name,t.args)})),null==v||v()}),[R,T,v]),x=S&&N&&!j;return e.createElement("div",{className:"googlesitekit-report-error-actions"},x&&("function"==typeof _?e.createElement(_,{requestAccessURL:S}):e.createElement(g.Button,{onClick:y,href:S,target:"_blank",danger:"danger"===a,tertiary:"tertiary"===a},Object(l.__)("Request access","google-site-kit"))),C&&e.createElement(c.Fragment,null,"function"==typeof k?e.createElement(k,{handleRetry:P}):e.createElement(g.Button,{onClick:P,danger:"danger"===a,tertiary:"tertiary"===a},Object(l.__)("Retry","google-site-kit")),!o&&e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(s.a)(Object(l.__)("Retry didn’t work? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(b.a,{href:D,external:!0,hideExternalIndicator:!0},Object(l.__)("Get help","google-site-kit"))}))),!C&&!o&&e.createElement("div",{className:O},"function"==typeof i?e.createElement(i,{linkURL:D}):e.createElement(b.a,{href:D,external:!0,hideExternalIndicator:!0},Object(l.__)("Get help","google-site-kit"))))}ReportErrorActions.propTypes={moduleSlug:a.a.string.isRequired,error:a.a.oneOfType([a.a.arrayOf(a.a.object),a.a.object]).isRequired,GetHelpLink:a.a.elementType,hideGetHelpLink:a.a.bool,buttonVariant:a.a.string,onRetry:a.a.func,onRequestAccess:a.a.func,getHelpClassName:a.a.string,RequestAccessButton:a.a.elementType,RetryButton:a.a.elementType}}).call(this,n(4))},141:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return StoreErrorNotices}));var r=n(1),i=n.n(r),o=n(3),a=n(120),c=n(19),s=n(35),l=n(169);function StoreErrorNotices(t){var n=t.hasButton,r=void 0!==n&&n,i=t.moduleSlug,u=t.storeName,g=Object(o.useSelect)((function(e){return e(u).getErrors()})),d=Object(o.useSelect)((function(e){return e(c.a).getModule(i)})),f=[];return g.filter((function(e){return!(!(null==e?void 0:e.message)||f.includes(e.message))&&(f.push(e.message),!0)})).map((function(t,n){var i=t.message;return Object(s.e)(t)&&(i=Object(l.a)(i,d)),e.createElement(a.a,{key:n,error:t,hasButton:r,storeName:u,message:i})}))}StoreErrorNotices.propTypes={hasButton:i.a.bool,storeName:i.a.string.isRequired,moduleSlug:i.a.string}}).call(this,n(4))},142:function(e,t,n){"use strict";var r=n(166);n.d(t,"c",(function(){return r.a}));var i=n(65);n.d(t,"b",(function(){return i.c})),n.d(t,"a",(function(){return i.a}))},145:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var r=n(6),i=n.n(r),o=n(2),a=n(7),c=n(13),s=n(8);function l(e,t,n){return e(s.r).hasConversionReportingEvents(this.requiredConversionEventName)||e(a.a).isKeyMetricActive(n)}var u,g=n(26);function d(e,t){return!t||!(!t||!e(s.r).getAdSenseLinked())}function f(e,t){return!t||e(s.r).hasCustomDimensions(this.requiredCustomDimensions)}var p=(u={},i()(u,a.f,{title:Object(o.__)("Top earning pages","google-site-kit"),description:Object(o.__)("Pages that generated the most AdSense revenue","google-site-kit"),infoTooltip:Object(o.__)("Pages that generated the most AdSense revenue","google-site-kit"),displayInSelectionPanel:d,displayInList:d,metadata:{group:g.b.SLUG}}),i()(u,a.y,{title:Object(o.__)("Top recent trending pages","google-site-kit"),description:Object(o.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),infoTooltip:Object(o.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_date"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:g.b.SLUG}}),i()(u,a.l,{title:Object(o.__)("Most popular authors by pageviews","google-site-kit"),description:Object(o.__)("Authors whose posts got the most visits","google-site-kit"),infoTooltip:Object(o.__)("Authors whose posts got the most visits","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_author"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:g.b.SLUG}}),i()(u,a.p,{title:Object(o.__)("Top categories by pageviews","google-site-kit"),description:Object(o.__)("Categories that your site visitors viewed the most","google-site-kit"),infoTooltip:Object(o.__)("Categories that your site visitors viewed the most","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_categories"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:g.b.SLUG}}),i()(u,a.m,{title:Object(o.__)("Most popular content by pageviews","google-site-kit"),description:Object(o.__)("Pages that brought in the most visitors","google-site-kit"),infoTooltip:Object(o.__)("Pages your visitors read the most","google-site-kit"),metadata:{group:g.b.SLUG}}),i()(u,a.n,{title:Object(o.__)("Most popular products by pageviews","google-site-kit"),description:Object(o.__)("Products that brought in the most visitors","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_type"],displayInSelectionPanel:function(e){return e(a.a).isKeyMetricActive(a.n)||e(c.c).getProductPostType()},displayInWidgetArea:f,metadata:{group:g.f.SLUG}}),i()(u,a.k,{title:Object(o.__)("Pages per visit","google-site-kit"),description:Object(o.__)("Number of pages visitors viewed per session on average","google-site-kit"),infoTooltip:Object(o.__)("Number of pages visitors viewed per session on average","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(u,a.F,{title:Object(o.__)("Visit length","google-site-kit"),description:Object(o.__)("Average duration of engaged visits","google-site-kit"),infoTooltip:Object(o.__)("Average duration of engaged visits","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(u,a.E,{title:Object(o.__)("Visits per visitor","google-site-kit"),description:Object(o.__)("Average number of sessions per site visitor","google-site-kit"),infoTooltip:Object(o.__)("Average number of sessions per site visitor","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(u,a.i,{title:Object(o.__)("Most engaging pages","google-site-kit"),description:Object(o.__)("Pages with the highest engagement rate","google-site-kit"),infoTooltip:Object(o.__)("Pages with the highest engagement rate","google-site-kit"),metadata:{group:g.b.SLUG}}),i()(u,a.h,{title:Object(o.__)("Least engaging pages","google-site-kit"),description:Object(o.__)("Pages with the highest percentage of visitors that left without engagement with your site","google-site-kit"),infoTooltip:Object(o.__)("Percentage of visitors that left without engagement with your site","google-site-kit"),metadata:{group:g.b.SLUG}}),i()(u,a.z,{title:Object(o.__)("Top pages by returning visitors","google-site-kit"),description:Object(o.__)("Pages that attracted the most returning visitors","google-site-kit"),infoTooltip:Object(o.__)("Pages that attracted the most returning visitors","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(u,a.j,{title:Object(o.__)("New visitors","google-site-kit"),description:Object(o.__)("How many new visitors you got and how the overall audience changed","google-site-kit"),infoTooltip:Object(o.__)("Portion of visitors who visited your site for the first time in this timeframe","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(u,a.o,{title:Object(o.__)("Returning visitors","google-site-kit"),description:Object(o.__)("Portion of people who visited your site more than once","google-site-kit"),infoTooltip:Object(o.__)("Portion of your site’s visitors that returned at least once in this timeframe","google-site-kit"),metadata:{group:g.h.SLUG}}),i()(u,a.A,{title:Object(o.__)("Top traffic source","google-site-kit"),description:Object(o.__)("Channel which brought in the most visitors to your site","google-site-kit"),infoTooltip:Object(o.__)("Channel (e.g. social, paid, search) that brought in the most visitors to your site","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(u,a.B,{title:Object(o.__)("Top traffic source driving add to cart","google-site-kit"),description:Object(o.__)("Channel which brought in the most add to cart events to your site","google-site-kit"),infoTooltip:Object(o.__)("Channel (e.g. social, paid, search) that brought in the most add to cart events to your site","google-site-kit"),requiredConversionEventName:[s.l.ADD_TO_CART],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.f.SLUG}}),i()(u,a.C,{title:Object(o.__)("Top traffic source driving leads","google-site-kit"),description:Object(o.__)("Channel which brought in the most leads to your site","google-site-kit"),infoTooltip:Object(o.__)("Channel (e.g. social, paid, search) that brought in the most leads to your site","google-site-kit"),requiredConversionEventName:[s.l.SUBMIT_LEAD_FORM,s.l.CONTACT,s.l.GENERATE_LEAD],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.e.SLUG}}),i()(u,a.D,{title:Object(o.__)("Top traffic source driving purchases","google-site-kit"),description:Object(o.__)("Channel which brought in the most purchases to your site","google-site-kit"),infoTooltip:Object(o.__)("Channel (e.g. social, paid, search) that brought in the most purchases to your site","google-site-kit"),requiredConversionEventName:[s.l.PURCHASE],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.f.SLUG}}),i()(u,a.g,{title:Object(o.__)("Most engaged traffic source","google-site-kit"),description:Object(o.__)("Visitors coming via this channel spent the most time on your site","google-site-kit"),infoTooltip:Object(o.__)("Channel (e.g. social, paid, search) that brought in the most visitors who had a meaningful engagement with your site","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(u,a.u,{title:Object(o.__)("Top converting traffic source","google-site-kit"),description:Object(o.__)("Channel which brought in the most visits that resulted in conversions","google-site-kit"),infoTooltip:Object(o.__)("Channel (e.g. social, paid, search) that brought in visitors who generated the most conversions","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(u,a.q,{title:Object(o.__)("Top cities driving traffic","google-site-kit"),description:Object(o.__)("Which cities you get the most visitors from","google-site-kit"),infoTooltip:Object(o.__)("The cities where most of your visitors came from","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(u,a.s,{title:Object(o.__)("Top cities driving leads","google-site-kit"),description:Object(o.__)("Cities driving the most contact form submissions","google-site-kit"),infoTooltip:Object(o.__)("Cities driving the most contact form submissions","google-site-kit"),requiredConversionEventName:[s.l.SUBMIT_LEAD_FORM,s.l.CONTACT,s.l.GENERATE_LEAD],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.e.SLUG}}),i()(u,a.r,{title:Object(o.__)("Top cities driving add to cart","google-site-kit"),description:Object(o.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),infoTooltip:Object(o.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),requiredConversionEventName:[s.l.ADD_TO_CART],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.f.SLUG}}),i()(u,a.t,{title:Object(o.__)("Top cities driving purchases","google-site-kit"),description:Object(o.__)("Cities driving the most purchases","google-site-kit"),infoTooltip:Object(o.__)("Cities driving the most purchases","google-site-kit"),requiredConversionEventName:[s.l.PURCHASE],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.f.SLUG}}),i()(u,a.w,{title:Object(o.__)("Top device driving purchases","google-site-kit"),description:Object(o.__)("Top device driving the most purchases","google-site-kit"),infoTooltip:Object(o.__)("Top device driving the most purchases","google-site-kit"),requiredConversionEventName:[s.l.PURCHASE],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.f.SLUG}}),i()(u,a.v,{title:Object(o.__)("Top countries driving traffic","google-site-kit"),description:Object(o.__)("Which countries you get the most visitors from","google-site-kit"),infoTooltip:Object(o.__)("The countries where most of your visitors came from","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(u,a.G,{title:Object(o.__)("Top performing keywords","google-site-kit"),description:Object(o.__)("What people searched for before they came to your site","google-site-kit"),infoTooltip:Object(o.__)("The top search queries for your site by highest clickthrough rate","google-site-kit"),metadata:{group:g.d.SLUG}}),i()(u,a.x,{title:Object(o.__)("Top pages driving leads","google-site-kit"),description:Object(o.__)("Pages on which forms are most frequently submitted","google-site-kit"),requiredConversionEventName:[s.l.SUBMIT_LEAD_FORM,s.l.CONTACT,s.l.GENERATE_LEAD],displayInSelectionPanel:l,displayInList:l,metadata:{group:g.e.SLUG}}),u)},151:function(e,t,n){"use strict";(function(e,r){var i=n(51),o=n.n(i),a=n(53),c=n.n(a),s=n(68),l=n.n(s),u=n(69),g=n.n(u),d=n(49),f=n.n(d),p=n(1),m=n.n(p),b=n(0),v=n(2),h=n(54);function y(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var i=f()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return g()(this,n)}}var O=function(t){l()(MediaErrorHandler,t);var n=y(MediaErrorHandler);function MediaErrorHandler(e){var t;return o()(this,MediaErrorHandler),(t=n.call(this,e)).state={error:null},t}return c()(MediaErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t})}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.errorMessage;return this.state.error?r.createElement(h.a,{message:n}):t}}]),MediaErrorHandler}(b.Component);O.defaultProps={errorMessage:Object(v.__)("Failed to load media","google-site-kit")},O.propTypes={children:m.a.node.isRequired,errorMessage:m.a.string.isRequired},t.a=O}).call(this,n(28),n(4))},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},159:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var r=n(5),i=n.n(r),o=n(16),a=n.n(o),c=n(0),s=n(3),l=n(13),u=n(7),g=n(19),d=n(32),f=n(37),p=n(36),m=n(18);function b(e){var t=Object(m.a)(),n=Object(s.useSelect)((function(t){return t(g.a).getModule(e)})),r=Object(s.useSelect)((function(e){return e(u.a).hasCapability(u.K)})),o=Object(s.useDispatch)(g.a).activateModule,b=Object(s.useDispatch)(d.a).navigateTo,v=Object(s.useDispatch)(l.c).setInternalServerError,h=Object(c.useCallback)(a()(i.a.mark((function n(){var r,a,c;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,o(e);case 2:if(r=n.sent,a=r.error,c=r.response,a){n.next=13;break}return n.next=8,Object(p.b)("".concat(t,"_widget-activation-cta"),"activate_module",e);case 8:return n.next=10,Object(f.f)("module_setup",e,{ttl:300});case 10:b(c.moduleReauthURL),n.next=14;break;case 13:v({id:"".concat(e,"-setup-error"),description:a.message});case 14:case"end":return n.stop()}}),n)}))),[o,e,b,v,t]);return(null==n?void 0:n.name)&&r?h:null}},163:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RecoverableModules}));var r=n(1),i=n.n(r),o=n(2),a=n(3),c=n(19),s=n(95);function RecoverableModules(t){var n=t.moduleSlugs,r=Object(a.useSelect)((function(e){var t=e(c.a).getModules();if(void 0!==t)return n.map((function(e){return t[e].name}))}));if(void 0===r)return null;var i=1===r.length?Object(o.sprintf)(
/* translators: %s: Module name */
Object(o.__)("%s data was previously shared by an admin who no longer has access. Please contact another admin to restore it.","google-site-kit"),r[0]):Object(o.sprintf)(
/* translators: %s: List of module names */
Object(o.__)("The data for the following modules was previously shared by an admin who no longer has access: %s. Please contact another admin to restore it.","google-site-kit"),r.join(Object(o._x)(", ","Recoverable modules","google-site-kit")));return e.createElement(s.a,{title:Object(o.__)("Data Unavailable","google-site-kit"),description:i})}RecoverableModules.propTypes={moduleSlugs:i.a.arrayOf(i.a.string).isRequired}}).call(this,n(4))},166:function(e,t,n){"use strict";(function(e){var r=n(11),i=n.n(r),o=n(1),a=n.n(o),c=n(2),s=n(3),l=n(201),u=n(210),g=n(65),d=n(7),f=n(10),p=n(0),m=Object(p.forwardRef)((function(t,n){var r=t.className,o=t.children,a=t.type,p=t.dismiss,m=void 0===p?"":p,b=t.dismissCallback,v=t.dismissLabel,h=void 0===v?Object(c.__)("OK, Got it!","google-site-kit"):v,y=t.Icon,O=void 0===y?Object(g.d)(a):y,_=t.OuterCTA,k=Object(s.useDispatch)(d.a).dismissItem,j=Object(s.useSelect)((function(e){return m?e(d.a).isItemDismissed(m):void 0}));if(m&&j)return null;var E=o?u.a:l.a;return e.createElement("div",{ref:n,className:i()(r,"googlesitekit-settings-notice","googlesitekit-settings-notice--".concat(a),{"googlesitekit-settings-notice--single-row":!o,"googlesitekit-settings-notice--multi-row":o})},e.createElement("div",{className:"googlesitekit-settings-notice__icon"},e.createElement(O,{width:"20",height:"20"})),e.createElement("div",{className:"googlesitekit-settings-notice__body"},e.createElement(E,t)),m&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(f.Button,{tertiary:!0,onClick:function(){"string"==typeof m&&k(m),null==b||b()}},h)),_&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(_,null)))}));m.propTypes={className:a.a.string,children:a.a.node,notice:a.a.node.isRequired,type:a.a.oneOf([g.a,g.c,g.b]),Icon:a.a.elementType,LearnMore:a.a.elementType,CTA:a.a.elementType,OuterCTA:a.a.elementType,dismiss:a.a.string,dismissLabel:a.a.string,dismissCallback:a.a.func},m.defaultProps={type:g.a},t.a=m}).call(this,n(4))},169:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(2);function i(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},i=n.slug,o=void 0===i?"":i,a=n.name,c=void 0===a?"":a,s=n.owner,l=void 0===s?{}:s;if(!o||!c)return e;var u="",g="";return"analytics-4"===o?e.match(/account/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):"search-console"===o&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),u||(u=Object(r.sprintf)(
/* translators: %s: module name */
Object(r.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),c)),l&&l.login&&(g=Object(r.sprintf)(
/* translators: %s: owner name */
Object(r.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),l.login)),g||(g=Object(r.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(u," ").concat(g)}},17:function(e,t,n){"use strict";var r=n(254);n.d(t,"i",(function(){return r.a}));var i=n(319);n.d(t,"f",(function(){return i.a}));var o=n(320);n.d(t,"h",(function(){return o.a}));var a=n(321);n.d(t,"j",(function(){return a.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var s=n(91),l=n.n(s);n.d(t,"b",(function(){return l.a})),n.d(t,"c",(function(){return s.DialogContent})),n.d(t,"d",(function(){return s.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},172:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GenericErrorHandlerActions}));var r=n(1),i=n.n(r),o=n(2),a=n(20),c=n(194);function GenericErrorHandlerActions(t){var n=t.message,r=t.componentStack;return e.createElement("div",{className:"googlesitekit-generic-error-handler-actions"},e.createElement(c.a,{message:n,componentStack:r}),e.createElement(a.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0},Object(o.__)("Report this problem","google-site-kit")))}GenericErrorHandlerActions.propTypes={message:i.a.string,componentStack:i.a.string}}).call(this,n(4))},173:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(22),i=function(e){return r.f.includes(e)}},175:function(e,t,n){"use strict";var r=n(216);n.d(t,"b",(function(){return r.a}));var i=n(221);n.d(t,"a",(function(){return i.a}))},178:function(e,t,n){"use strict";n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return c})),n.d(t,"d",(function(){return s}));var r=n(33),i=n.n(r);function o(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return!0};return"string"==typeof e?n(e):!("object"!==i()(e)||!t(e))||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e?n(e):"object"===i()(e)&&t(e)}))}function a(e){var t=e.startDate,n=e.endDate,r=t&&t.match(/^\d{4}-\d{2}-\d{2}$/),i=n&&n.match(/^\d{4}-\d{2}-\d{2}$/);return r&&i}function c(e){var t=function(e){var t=e.hasOwnProperty("fieldName")&&!!e.fieldName,n=e.hasOwnProperty("sortOrder")&&/(ASCENDING|DESCENDING)/i.test(e.sortOrder.toString());return t&&n};return Array.isArray(e)?e.every((function(e){return"object"===i()(e)&&t(e)})):"object"===i()(e)&&t(e)}function s(e){return"string"==typeof e||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e}))}},18:function(e,t,n){"use strict";var r=n(0),i=n(61);t.a=function(){return Object(r.useContext)(i.b)}},182:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(1),i=n.n(r),o=" ";function DisplaySetting(e){return e.value||o}DisplaySetting.propTypes={value:i.a.oneOfType([i.a.string,i.a.bool,i.a.number])},t.b=DisplaySetting},184:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeBadge}));var r=n(1),i=n.n(r),o=n(11),a=n.n(o),c=n(9);function ChangeBadge(t){var n=t.previousValue,r=t.currentValue,i=t.isAbsolute?r-n:Object(c.g)(n,r),o=i<0,s=0===i;return null===i?null:e.createElement("div",{className:a()("googlesitekit-change-badge",{"googlesitekit-change-badge--negative":o,"googlesitekit-change-badge--zero":s})},Object(c.B)(i,{style:"percent",signDisplay:"exceptZero",maximumFractionDigits:1}))}ChangeBadge.propTypes={isAbsolute:i.a.bool,previousValue:i.a.number.isRequired,currentValue:i.a.number.isRequired}}).call(this,n(4))},186:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("path",{d:"M0 0h2v7H0zm0 10h2v2H0z",fill:"currentColor",fillRule:"evenodd"});t.a=function SvgWarningIcon(e){return r.createElement("svg",i({viewBox:"0 0 2 12"},e),o)}},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="core/modules",i="insufficient_module_dependencies"},190:function(e,t){e.exports=googlesitekit.modules},194:function(e,t,n){"use strict";(function(e){var r=n(15),i=n.n(r),o=n(195),a=n.n(o),c=n(1),s=n.n(c),l=n(0),u=n(2),g=n(266),d=n(423),f=n(424),p=n(10);function ReportErrorButton(t){var n=t.message,r=t.componentStack,o=Object(l.useState)(!1),c=i()(o,2),s=c[0],m=c[1];return e.createElement(p.Button,{"aria-label":s?Object(u.__)("Error message copied to clipboard. Click to copy the error message again.","google-site-kit"):void 0,onClick:function(){a()("`".concat(n,"\n").concat(r,"`")),m(!0)},trailingIcon:e.createElement(g.a,{className:"mdc-button__icon",icon:s?d.a:f.a})},s?Object(u.__)("Copied to clipboard","google-site-kit"):Object(u.__)("Copy error contents","google-site-kit"))}ReportErrorButton.propTypes={message:s.a.string,componentStack:s.a.string},t.a=ReportErrorButton}).call(this,n(4))},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),o=n(25),a=n.n(o),c=n(11),s=n.n(c),l=n(1),u=n.n(l),g=n(146),d=n(0),f=n(2),p=n(126),m=n(127),b=n(128),v=n(70),h=n(76),y=Object(d.forwardRef)((function(t,n){var r,o=t["aria-label"],c=t.secondary,l=void 0!==c&&c,u=t.arrow,d=void 0!==u&&u,y=t.back,O=void 0!==y&&y,_=t.caps,k=void 0!==_&&_,j=t.children,E=t.className,S=void 0===E?"":E,w=t.danger,A=void 0!==w&&w,T=t.disabled,C=void 0!==T&&T,D=t.external,R=void 0!==D&&D,N=t.hideExternalIndicator,P=void 0!==N&&N,x=t.href,L=void 0===x?"":x,I=t.inverse,M=void 0!==I&&I,G=t.noFlex,F=void 0!==G&&G,B=t.onClick,z=t.small,V=void 0!==z&&z,U=t.standalone,q=void 0!==U&&U,W=t.linkButton,H=void 0!==W&&W,K=t.to,Y=t.leadingIcon,$=t.trailingIcon,Z=a()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),J=L||K||!B?K?"ROUTER_LINK":R?"EXTERNAL_LINK":"LINK":C?"BUTTON_DISABLED":"BUTTON",X="BUTTON"===J||"BUTTON_DISABLED"===J?"button":"ROUTER_LINK"===J?g.b:"a",Q=("EXTERNAL_LINK"===J&&(r=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===J&&(r=Object(f._x)("(disabled)","screen reader text","google-site-kit")),r?o?"".concat(o," ").concat(r):"string"==typeof j?"".concat(j," ").concat(r):void 0:o),ee=Y,te=$;return O&&(ee=e.createElement(b.a,{width:14,height:14})),R&&!P&&(te=e.createElement(v.a,{width:14,height:14})),d&&!M&&(te=e.createElement(p.a,{width:14,height:14})),d&&M&&(te=e.createElement(m.a,{width:14,height:14})),e.createElement(X,i()({"aria-label":Q,className:s()("googlesitekit-cta-link",S,{"googlesitekit-cta-link--secondary":l,"googlesitekit-cta-link--inverse":M,"googlesitekit-cta-link--small":V,"googlesitekit-cta-link--caps":k,"googlesitekit-cta-link--danger":A,"googlesitekit-cta-link--disabled":C,"googlesitekit-cta-link--standalone":q,"googlesitekit-cta-link--link-button":H,"googlesitekit-cta-link--no-flex":!!F}),disabled:C,href:"LINK"!==J&&"EXTERNAL_LINK"!==J||C?void 0:L,onClick:B,rel:"EXTERNAL_LINK"===J?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===J?"_blank":void 0,to:K},Z),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},j),!!te&&e.createElement(h.a,{marginLeft:5},te))}));y.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=y}).call(this,n(4))},200:function(e,t,n){"use strict";n.d(t,"a",(function(){return j})),n.d(t,"b",(function(){return E})),n.d(t,"c",(function(){return S})),n.d(t,"g",(function(){return w})),n.d(t,"f",(function(){return A})),n.d(t,"d",(function(){return T})),n.d(t,"e",(function(){return C}));var r=n(16),i=n.n(r),o=n(5),a=n.n(o),c=n(6),s=n.n(c),l=n(12),u=n.n(l),g=n(14),d=n(45),f=n.n(d),p=n(3),m=n(62),b=n(82),v=n(48),h=n(64);function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var _=h.a.clearError,k=h.a.receiveError,j="cannot submit changes while submitting changes",E="cannot submit changes if settings have not changed",S=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=r.ownedSettingsSlugs,o=void 0===i?void 0:i,c=r.storeName,l=void 0===c?void 0:c,d=r.settingSlugs,h=void 0===d?[]:d,y=r.initialSettings,j=void 0===y?void 0:y,E=r.validateHaveSettingsChanged,S=void 0===E?C():E;u()(e,"type is required."),u()(t,"identifier is required."),u()(n,"datapoint is required.");var w=l||"".concat(e,"/").concat(t),A={ownedSettingsSlugs:o,settings:j,savedSettings:void 0},T=Object(v.a)({baseName:"getSettings",controlCallback:function(){return f.a.get(e,t,n,{},{useCache:!1})},reducerCallback:function(e,t){return O(O({},e),{},{savedSettings:O({},t),settings:O(O({},t),e.settings||{})})}}),D=Object(v.a)({baseName:"saveSettings",controlCallback:function(r){var i=r.values;return f.a.set(e,t,n,i)},reducerCallback:function(e,t){return O(O({},e),{},{savedSettings:O({},t),settings:O({},t)})},argsToParams:function(e){return{values:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.values;u()(Object(g.isPlainObject)(t),"values is required.")}}),R={},N={setSettings:function(e){return u()(Object(g.isPlainObject)(e),"values is required."),{payload:{values:e},type:"SET_SETTINGS"}},rollbackSettings:function(){return{payload:{},type:"ROLLBACK_SETTINGS"}},rollbackSetting:function(e){return u()(e,"setting is required."),{payload:{setting:e},type:"ROLLBACK_SETTING"}},saveSettings:a.a.mark((function e(){var t,n,r,i,o;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.commonActions.getRegistry();case 2:return t=e.sent,e.next=5,_("saveSettings",[]);case 5:return n=t.select(w).getSettings(),e.next=8,D.actions.fetchSaveSettings(n);case 8:if(r=e.sent,i=r.response,!(o=r.error)){e.next=14;break}return e.next=14,k(o,"saveSettings",[]);case 14:return e.abrupt("return",{response:i,error:o});case 15:case"end":return e.stop()}}),e)}))},P={},x=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:A,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.payload;switch(n){case"SET_SETTINGS":var i=r.values;return O(O({},e),{},{settings:O(O({},e.settings||{}),i)});case"ROLLBACK_SETTINGS":return O(O({},e),{},{settings:e.savedSettings});case"ROLLBACK_SETTING":var o=r.setting;return e.savedSettings[o]?O(O({},e),{},{settings:O(O({},e.settings||{}),{},s()({},o,e.savedSettings[o]))}):O({},e);default:return void 0!==R[n]?R[n](e,{type:n,payload:r}):e}},L={getSettings:a.a.mark((function e(){var t;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p.commonActions.getRegistry();case 2:if(t=e.sent,t.select(w).getSettings()){e.next=7;break}return e.next=7,T.actions.fetchGetSettings();case 7:case"end":return e.stop()}}),e)}))},I=Object(m.g)(S),M=I.safeSelector,G=I.dangerousSelector,F={haveSettingsChanged:M,__dangerousHaveSettingsChanged:G,getSettings:function(e){return e.settings},hasSettingChanged:function(e,t){u()(t,"setting is required.");var n=e.settings,r=e.savedSettings;return!(!n||!r)&&!Object(g.isEqual)(n[t],r[t])},isDoingSaveSettings:function(e){return Object.values(e.isFetchingSaveSettings).some(Boolean)},getOwnedSettingsSlugs:function(e){return e.ownedSettingsSlugs},haveOwnedSettingsChanged:Object(p.createRegistrySelector)((function(e){return function(){var t=e(w).getOwnedSettingsSlugs();return e(w).haveSettingsChanged(t)}}))};h.forEach((function(e){var t=Object(b.b)(e),n=Object(b.a)(e);N["set".concat(t)]=function(e){return u()(void 0!==e,"value is required for calls to set".concat(t,"().")),{payload:{value:e},type:"SET_".concat(n)}},R["SET_".concat(n)]=function(t,n){var r=n.payload.value;return O(O({},t),{},{settings:O(O({},t.settings||{}),{},s()({},e,r))})},F["get".concat(t)]=Object(p.createRegistrySelector)((function(t){return function(){return(t(w).getSettings()||{})[e]}}))}));var B=Object(p.combineStores)(p.commonStore,T,D,{initialState:A,actions:N,controls:P,reducer:x,resolvers:L,selectors:F});return O(O({},B),{},{STORE_NAME:w})};function w(e,t){return function(){var n=i()(a.a.mark((function n(r){var i,o,c,s;return a.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(i=r.select,o=r.dispatch,!i(t).haveSettingsChanged()){n.next=8;break}return n.next=4,o(t).saveSettings();case 4:if(c=n.sent,!(s=c.error)){n.next=8;break}return n.abrupt("return",{error:s});case 8:return n.next=10,f.a.invalidateCache("modules",e);case 10:return n.abrupt("return",{});case 11:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}function A(e){return function(t){var n=t.select,r=t.dispatch;return n(e).haveSettingsChanged()?r(e).rollbackSettings():{}}}function T(e){return function(t){var n=Object(m.e)(t)(e),r=n.haveSettingsChanged,i=n.isDoingSubmitChanges;u()(!i(),j),u()(r(),E)}}function C(){return function(e,t,n){var r=t.settings,i=t.savedSettings;n&&u()(!Object(g.isEqual)(Object(g.pick)(r,n),Object(g.pick)(i,n)),E),u()(!Object(g.isEqual)(r,i),E)}}},201:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeSingleRow}));var r=n(1),i=n.n(r),o=n(0);function SettingsNoticeSingleRow(t){var n=t.notice,r=t.LearnMore,i=t.CTA;return e.createElement(o.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),i&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(i,null)))}SettingsNoticeSingleRow.propTypes={notice:i.a.node.isRequired,LearnMore:i.a.elementType,CTA:i.a.elementType}}).call(this,n(4))},202:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileWrapper}));var r=n(11),i=n.n(r),o=n(14),a=n(1),c=n.n(a),s=n(0),l=n(2),u=n(145),g=n(455),d=n(456),f=n(303),p=n(381),m=n(135),b=n(35),v=n(9),h=n(18);function MetricTileWrapper(t){var n,r,a,c=t.className,y=t.children,O=t.error,_=t.loading,k=t.moduleSlug,j=t.Widget,E=t.widgetSlug,S=t.title,w=void 0===S?null===(n=u.a[E])||void 0===n?void 0:n.title:S,A=t.infoTooltip,T=void 0===A?(null===(r=u.a[E])||void 0===r?void 0:r.infoTooltip)||(null===(a=u.a[E])||void 0===a?void 0:a.description):A,C=Object(h.a)(),D=!!O&&Object(o.castArray)(O).some(b.e),R=Object(s.useCallback)((function(){Object(v.I)("".concat(C,"_kmw"),"data_loading_error_retry")}),[C]);return Object(s.useEffect)((function(){O&&Object(v.I)("".concat(C,"_kmw"),"data_loading_error")}),[C,O]),O?e.createElement(f.a,{title:D?Object(l.__)("Insufficient permissions","google-site-kit"):Object(l.__)("Data loading failed","google-site-kit"),headerText:w,infoTooltip:T},e.createElement(m.a,{moduleSlug:k,error:O,onRetry:R,GetHelpLink:D?g.a:void 0,getHelpClassName:"googlesitekit-error-retry-text"})):e.createElement(j,{noPadding:!0},e.createElement("div",{className:i()("googlesitekit-km-widget-tile",c)},e.createElement(p.a,{title:w,infoTooltip:T,loading:_}),e.createElement("div",{className:"googlesitekit-km-widget-tile__body"},_&&e.createElement(d.a,null),!_&&y)))}MetricTileWrapper.propTypes={Widget:c.a.elementType.isRequired,loading:c.a.bool,title:c.a.string,infoTooltip:c.a.oneOfType([c.a.string,c.a.element]),moduleSlug:c.a.string.isRequired}}).call(this,n(4))},210:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeMultiRow}));var r=n(1),i=n.n(r),o=n(0);function SettingsNoticeMultiRow(t){var n=t.notice,r=t.LearnMore,i=t.CTA,a=t.children;return e.createElement(o.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),e.createElement("div",{className:"googlesitekit-settings-notice__inner-row"},e.createElement("div",{className:"googlesitekit-settings-notice__children-container"},a),r&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(r,null)),i&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(i,null))))}SettingsNoticeMultiRow.propTypes={children:i.a.node.isRequired,notice:i.a.node.isRequired,LearnMore:i.a.elementType,CTA:i.a.elementType}}).call(this,n(4))},214:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("path",{d:"M2 5.309l1.474 2.14c.69 1.001 1.946 1.001 2.636 0L10 1.8",stroke:"currentColor",strokeWidth:1.6,strokeLinecap:"square"});t.a=function SvgCheck2(e){return r.createElement("svg",i({viewBox:"0 0 12 9",fill:"none"},e),o)}},216:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var r=n(5),i=n.n(r),o=n(6),a=n.n(o),c=n(16),s=n.n(c),l=n(0),u=n(3),g=n(13),d=n(23);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e){var t=Object(u.useDispatch)(d.b).setValue,n=Object(u.useSelect)((function(e){return e(g.c).hasMinimumWordPressVersion("6.2")})),r=Object(u.useSelect)((function(e){return e(g.c).hasMinimumWordPressVersion("6.4")}));return Object(l.useCallback)(s()(i.a.mark((function o(){var a,c,s,l;return i.a.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(a=document.querySelector("#adminmenu").offsetHeight>0){i.next=7;break}if(!(c=document.getElementById("wp-admin-bar-menu-toggle"))){i.next=7;break}return c.firstChild.click(),i.next=7,new Promise((function(e){setTimeout(e,0)}));case 7:"#adminmenu [href*='page=googlesitekit-dashboard']",(s=!!document.querySelector("".concat("#adminmenu [href*='page=googlesitekit-dashboard']","[aria-haspopup=true]")))&&document.querySelector("#adminmenu [href*='page=googlesitekit-dashboard']").click(),n&&!r&&(l=document.hasFocus,document.hasFocus=function(){return document.hasFocus=l,!1}),t("admin-menu-tooltip",p({isTooltipVisible:!0,rehideAdminMenu:!a,rehideAdminSubMenu:s},e));case 12:case"end":return i.stop()}}),o)}))),[n,r,t,e])}},22:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return i})),n.d(t,"o",(function(){return o})),n.d(t,"m",(function(){return a})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return s})),n.d(t,"s",(function(){return l})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return g})),n.d(t,"r",(function(){return d})),n.d(t,"k",(function(){return f})),n.d(t,"u",(function(){return p})),n.d(t,"v",(function(){return m})),n.d(t,"q",(function(){return b})),n.d(t,"p",(function(){return v})),n.d(t,"b",(function(){return h})),n.d(t,"e",(function(){return y})),n.d(t,"a",(function(){return O})),n.d(t,"d",(function(){return _})),n.d(t,"c",(function(){return k})),n.d(t,"f",(function(){return j})),n.d(t,"g",(function(){return E}));var r="mainDashboard",i="entityDashboard",o="mainDashboardViewOnly",a="entityDashboardViewOnly",c="userInput",s="activation",l="splash",u="adminBar",g="adminBarViewOnly",d="settings",f="adBlockingRecovery",p="wpDashboard",m="wpDashboardViewOnly",b="moduleSetup",v="metricSelection",h="key-metrics",y="traffic",O="content",_="speed",k="monetization",j=[r,i,o,a,c,l,d,b,v],E=[o,a,g,m]},221:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdminMenuTooltip}));var r=n(0),i=n(3),o=n(223),a=n(23),c=n(9),s=n(18);function AdminMenuTooltip(){var t=Object(s.a)(),n=Object(i.useDispatch)(a.b).setValue,l=Object(i.useSelect)((function(e){return e(a.b).getValue("admin-menu-tooltip")||{isTooltipVisible:!1}})),u=l.isTooltipVisible,g=void 0!==u&&u,d=l.rehideAdminMenu,f=void 0!==d&&d,p=l.rehideAdminSubMenu,m=void 0!==p&&p,b=l.tooltipSlug,v=l.title,h=l.content,y=l.dismissLabel,O=Object(r.useCallback)((function(){var e;f&&(document.querySelector("#adminmenu").offsetHeight>0&&(null===(e=document.getElementById("wp-admin-bar-menu-toggle"))||void 0===e||e.click()));m&&document.querySelector("body").click(),b&&Object(c.I)("".concat(t,"_").concat(b),"tooltip_dismiss"),n("admin-menu-tooltip",void 0)}),[f,m,n,b,t]);return g?e.createElement(o.a,{target:'#adminmenu [href*="page=googlesitekit-settings"]',slug:"ga4-activation-banner-admin-menu-tooltip",title:v,content:h,dismissLabel:y,onView:function(){Object(c.I)("".concat(t,"_").concat(b),"tooltip_view")},onDismiss:O}):null}}).call(this,n(4))},223:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return JoyrideTooltip}));var i=n(6),o=n.n(i),a=n(15),c=n.n(a),s=n(1),l=n(30),u=n(421),g=n(0),d=n(107),f=n(72),p=n(90);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function JoyrideTooltip(t){var n=t.title,i=t.content,o=t.dismissLabel,a=t.target,s=t.cta,m=void 0!==s&&s,v=t.className,h=t.styles,y=void 0===h?{}:h,O=t.slug,_=void 0===O?"":O,k=t.onDismiss,j=void 0===k?function(){}:k,E=t.onView,S=void 0===E?function(){}:E,w=t.onTourStart,A=void 0===w?function(){}:w,T=t.onTourEnd,C=void 0===T?function(){}:T,D=function(){return!!e.document.querySelector(a)},R=Object(g.useState)(D),N=c()(R,2),P=N[0],x=N[1];if(Object(u.a)((function(){D()&&x(!0)}),P?null:250),Object(g.useEffect)((function(){if(P&&e.ResizeObserver){var t=e.document.querySelector(a),n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}),[a,P]),!P)return null;var L=[{title:n,target:a,content:i,disableBeacon:!0,isFixed:!0,placement:"auto",cta:m,className:v}],I={close:o,last:o};return r.createElement(f.a,{slug:_},r.createElement(l.e,{callback:function(t){switch(t.type){case l.b.TOUR_START:A(),e.document.body.classList.add("googlesitekit-showing-tooltip");break;case l.b.TOUR_END:C(),e.document.body.classList.remove("googlesitekit-showing-tooltip");break;case l.b.STEP_AFTER:j();break;case l.b.TOOLTIP:S()}},disableOverlay:!0,disableScrolling:!0,spotlightPadding:0,floaterProps:p.b,locale:I,steps:L,styles:b(b(b({},p.c),y),{},{options:b(b({},p.c.options),null==y?void 0:y.options),spotlight:b(b({},p.c.spotlight),null==y?void 0:y.spotlight)}),tooltipComponent:d.a,run:!0}))}JoyrideTooltip.propTypes={title:s.PropTypes.node,content:s.PropTypes.string,dismissLabel:s.PropTypes.string,target:s.PropTypes.string.isRequired,onDismiss:s.PropTypes.func,onShow:s.PropTypes.func,className:s.PropTypes.string,styles:s.PropTypes.object,slug:s.PropTypes.string,onView:s.PropTypes.func}}).call(this,n(28),n(4))},23:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r="core/ui",i="activeContextID"},236:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),o=n(11),a=n.n(o),c=n(0),s=n(114),l=n(9),u=n(402),g=n(77),d=n(403),f=n(134);function DataBlock(t){var n=t.stat,r=void 0===n?null:n,i=t.className,o=void 0===i?"":i,p=t.title,m=void 0===p?"":p,b=t.datapoint,v=void 0===b?null:b,h=t.datapointUnit,y=void 0===h?"":h,O=t.change,_=void 0===O?null:O,k=t.changeDataUnit,j=void 0===k?"":k,E=t.context,S=void 0===E?"default":E,w=t.period,A=void 0===w?"":w,T=t.selected,C=void 0!==T&&T,D=t.source,R=t.sparkline,N=t.handleStatSelection,P=void 0===N?null:N,x=t.invertChangeColor,L=void 0!==x&&x,I=t.gatheringData,M=void 0!==I&&I,G=t.gatheringDataNoticeStyle,F=void 0===G?s.a.DEFAULT:G,B=t.badge,z=Object(c.useCallback)((function(){!M&&P&&P(r)}),[M,P,r]),V=Object(c.useCallback)((function(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),z())}),[z]),U=void 0===v?v:Object(l.B)(v,y),q="button"===S,W=q?"button":"";return e.createElement("div",{className:a()("googlesitekit-data-block",o,"googlesitekit-data-block--".concat(S),{"googlesitekit-data-block--selected":C,"googlesitekit-data-block--is-gathering-data":M}),tabIndex:q&&!M?"0":"-1",role:P&&W,onClick:z,onKeyDown:V,"aria-disabled":M||void 0,"aria-label":P&&m,"aria-pressed":P&&C},e.createElement("div",{className:"googlesitekit-data-block__title-datapoint-wrapper"},e.createElement("h3",{className:" googlesitekit-subheading-1 googlesitekit-data-block__title "},!0===B?e.createElement(g.a,{"aria-hidden":"true",className:"googlesitekit-badge--hidden",label:"X"}):B,e.createElement("span",{className:"googlesitekit-data-block__title-inner"},m)),!M&&e.createElement("div",{className:"googlesitekit-data-block__datapoint"},U)),!M&&R&&e.createElement(u.a,{sparkline:R,invertChangeColor:L}),!M&&e.createElement("div",{className:"googlesitekit-data-block__change-source-wrapper"},e.createElement(d.a,{change:_,changeDataUnit:j,period:A,invertChangeColor:L}),D&&e.createElement(f.a,{className:"googlesitekit-data-block__source",name:D.name,href:D.link,external:null==D?void 0:D.external})),M&&e.createElement(s.b,{style:F}))}DataBlock.propTypes={stat:i.a.number,className:i.a.string,title:i.a.string,datapoint:i.a.oneOfType([i.a.string,i.a.number]),datapointUnit:i.a.string,change:i.a.oneOfType([i.a.string,i.a.number]),changeDataUnit:i.a.oneOfType([i.a.string,i.a.bool]),context:i.a.string,period:i.a.string,selected:i.a.bool,handleStatSelection:i.a.func,invertChangeColor:i.a.bool,gatheringData:i.a.bool,gatheringDataNoticeStyle:i.a.oneOf(Object.values(s.a)),badge:i.a.oneOfType([i.a.bool,i.a.node])},t.a=DataBlock}).call(this,n(4))},24:function(e,t,n){"use strict";n.d(t,"d",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return a})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return s}));var r=n(79),i="xlarge",o="desktop",a="tablet",c="small";function s(){var e=Object(r.a)();return e>1280?i:e>960?o:e>600?a:c}},251:function(e,t,n){"use strict";n.d(t,"a",(function(){return SurveyViewTrigger}));var r=n(0),i=n(1),o=n.n(i),a=n(3),c=n(13),s=n(7);function SurveyViewTrigger(e){var t=e.triggerID,n=e.ttl,i=void 0===n?0:n,o=Object(a.useSelect)((function(e){return e(c.c).isUsingProxy()})),l=Object(a.useDispatch)(s.a).triggerSurvey;return Object(r.useEffect)((function(){o&&l(t,{ttl:i})}),[o,t,i,l]),null}SurveyViewTrigger.propTypes={triggerID:o.a.string.isRequired,ttl:o.a.number}},26:function(e,t,n){"use strict";n.d(t,"l",(function(){return i})),n.d(t,"k",(function(){return o})),n.d(t,"j",(function(){return a})),n.d(t,"i",(function(){return c})),n.d(t,"a",(function(){return s})),n.d(t,"o",(function(){return l})),n.d(t,"n",(function(){return u})),n.d(t,"m",(function(){return g})),n.d(t,"c",(function(){return d})),n.d(t,"g",(function(){return f})),n.d(t,"h",(function(){return p})),n.d(t,"d",(function(){return m})),n.d(t,"e",(function(){return b})),n.d(t,"f",(function(){return v})),n.d(t,"b",(function(){return h}));var r=n(2),i="key-metrics-setup-cta-widget",o="googlesitekit-key-metrics-selection-panel-opened",a="key-metrics-selection-form",c="key-metrics-selected",s="key-metrics-effective-selection",l="key-metrics-unstaged-selection",u=2,g=8,d={SLUG:"current-selection",LABEL:Object(r.__)("Current selection","google-site-kit")},f={SLUG:"suggested",LABEL:Object(r.__)("Suggested","google-site-kit")},p={SLUG:"visitors",LABEL:Object(r.__)("Visitors","google-site-kit")},m={SLUG:"driving-traffic",LABEL:Object(r.__)("Driving traffic","google-site-kit")},b={SLUG:"generating-leads",LABEL:Object(r.__)("Generating leads","google-site-kit")},v={SLUG:"selling-products",LABEL:Object(r.__)("Selling products","google-site-kit")},h={SLUG:"content-performance",LABEL:Object(r.__)("Content performance","google-site-kit")}},263:function(e,t,n){"use strict";n.d(t,"d",(function(){return s})),n.d(t,"e",(function(){return l})),n.d(t,"b",(function(){return u})),n.d(t,"a",(function(){return g})),n.d(t,"c",(function(){return d}));var r=n(27),i=n.n(r),o=n(14),a=n(24),c=n(9),s=function(e,t){if(!(null==t?void 0:t.length))return e;var n=[];return(null==e?void 0:e.length)&&(n=e[0].reduce((function(e,t,n){return(null==t?void 0:t.role)?[].concat(i()(e),[n]):e}),[])),e.map((function(e){return e.filter((function(e,r){return 0===r||t.includes(r-1)||n.includes(r-1)}))}))},l=function(e,t,n,r){var i={height:e||t,width:n||r};return i.width&&!i.height&&(i.height="100%"),i.height&&!i.width&&(i.width="100%"),i},u=function(e,t,n){var r=i()(e||[]);return t&&r.push({eventName:"ready",callback:t}),n&&r.push({eventName:"select",callback:n}),r},g=function(e,t,n,r,i,s){var l,u,g,d,f,p,m,b,v=Object(o.cloneDeep)(e);t&&"LineChart"===n&&((null==e||null===(l=e.vAxis)||void 0===l||null===(u=l.viewWindow)||void 0===u?void 0:u.min)||Object(o.set)(v,"vAxis.viewWindow.min",0),(null==e||null===(g=e.vAxis)||void 0===g||null===(d=g.viewWindow)||void 0===d?void 0:d.max)||Object(o.set)(v,"vAxis.viewWindow.max",100),(null==e||null===(f=e.hAxis)||void 0===f||null===(p=f.viewWindow)||void 0===p?void 0:p.min)||(Object(o.set)(v,"hAxis.viewWindow.min",Object(c.G)(r)),delete v.hAxis.ticks),(null==e||null===(m=e.hAxis)||void 0===m||null===(b=m.viewWindow)||void 0===b?void 0:b.max)||(Object(o.set)(v,"hAxis.viewWindow.max",Object(c.G)(i)),delete v.hAxis.ticks));if("LineChart"===n){var h,y,O;if((null==e||null===(h=e.hAxis)||void 0===h?void 0:h.maxTextLines)||Object(o.set)(v,"hAxis.maxTextLines",1),!(null==e||null===(y=e.hAxis)||void 0===y?void 0:y.minTextSpacing)){var _=s===a.b?50:100;Object(o.set)(v,"hAxis.minTextSpacing",_)}void 0===(null==e||null===(O=e.tooltip)||void 0===O?void 0:O.isHtml)&&(Object(o.set)(v,"tooltip.isHtml",!0),Object(o.set)(v,"tooltip.trigger","both"))}return Object(o.merge)(v,{hAxis:{textStyle:{fontSize:10,color:"#5f6561"}},vAxis:{textStyle:{color:"#5f6561",fontSize:10}},legend:{textStyle:{color:"#131418",fontSize:12}}}),v},d=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object(c.r)(),n=Intl.NumberFormat(t,{style:"currency",currency:e}),r=n.formatToParts(1e6);return r.reduce((function(e,t){var n=t.value;switch(t.type){case"group":return e+",";case"decimal":return e+".";case"currency":return e+n;case"literal":return e+(/^\s*$/.test(n)?n:"");case"integer":var i=n.replace(/\d/g,"#");return e+(Object(o.findLast)(r,(function(e){return"integer"===e.type}))===t?i.replace(/#$/,"0"):i);case"fraction":return e+n.replace(/\d/g,"0");default:return e}}),"")}},272:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileText}));var r=n(21),i=n.n(r),o=n(25),a=n.n(o),c=n(1),s=n.n(c),l=n(184),u=n(9),g=n(202);function MetricTileText(t){var n=t.metricValue,r=t.metricValueFormat,o=t.subText,c=t.previousValue,s=t.currentValue,d=a()(t,["metricValue","metricValueFormat","subText","previousValue","currentValue"]),f=Object(u.m)(r);return e.createElement(g.a,i()({className:"googlesitekit-km-widget-tile--text"},d),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-container"},e.createElement("div",{className:"googlesitekit-km-widget-tile__metric"},n),e.createElement("p",{className:"googlesitekit-km-widget-tile__subtext"},o)),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-change-container"},e.createElement(l.a,{previousValue:c,currentValue:s,isAbsolute:"percent"===(null==f?void 0:f.style)})))}MetricTileText.propTypes={metricValue:s.a.oneOfType([s.a.string,s.a.number]),subtext:s.a.string,previousValue:s.a.number,currentValue:s.a.number}}).call(this,n(4))},275:function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var r=n(6),i=n.n(r),o=n(25),a=n.n(o),c=n(63),s=n.n(c),l=n(14);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var d=s()((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.metrics,n=e.dimensions,r=a()(e,["metrics","dimensions"]);return g({metrics:f(t),dimensions:p(n)},r)})),f=function(e){return Object(l.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(l.isPlainObject)(e)}))},p=function(e){return Object(l.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(l.isPlainObject)(e)}))}},29:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/forms"},3:function(e,t){e.exports=googlesitekit.data},302:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),o=n(11),a=n.n(o),c=n(0),s=n(2),l=n(10),u=n(77),g=n(20);function NewBadge(t){var n=t.tooltipTitle,r=t.learnMoreLink,i=t.forceOpen,o=t.hasLeftSpacing,d=t.hasNoSpacing,f=t.onLearnMoreClick,p=void 0===f?function(){}:f,m=e.createElement(u.a,{className:a()("googlesitekit-new-badge",{"googlesitekit-new-badge--has-no-spacing":d}),label:Object(s.__)("New","google-site-kit"),hasLeftSpacing:o});return n?e.createElement(l.Tooltip,{tooltipClassName:"googlesitekit-new-badge__tooltip",title:e.createElement(c.Fragment,null,n,e.createElement("br",null),e.createElement(g.a,{href:r,onClick:p,external:!0,hideExternalIndicator:!0},Object(s.__)("Learn more","google-site-kit"))),placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,interactive:!0,open:i},m):m}NewBadge.propTypes={tooltipTitle:i.a.string,learnMoreLink:i.a.string,forceOpen:i.a.bool,onLearnMoreClick:i.a.func,hasLeftSpacing:i.a.bool,hasNoSpacing:i.a.bool},t.a=NewBadge}).call(this,n(4))},303:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileError}));var r=n(95),i=n(132);function MetricTileError(t){var n=t.children,o=t.headerText,a=t.infoTooltip,c=t.title;return e.createElement("div",{className:"googlesitekit-km-widget-tile--error"},e.createElement(r.a,{title:c,headerText:o,headerContent:a&&e.createElement(i.a,{title:a}),description:"",error:!0},n))}}).call(this,n(4))},310:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.149 7.96l-5.166 5.166a.344.344 0 00-.094.176l-.35 1.755a.344.344 0 00.404.404l1.755-.35a.344.344 0 00.175-.095l5.166-5.165-1.89-1.89zm2.301-1.814a1.031 1.031 0 00-1.458 0L6.497 12.64a1.031 1.031 0 00-.282.527l-.35 1.755a1.031 1.031 0 001.213 1.213l1.754-.35c.2-.04.383-.139.527-.283l6.495-6.494a1.031 1.031 0 000-1.459L14.45 6.146z"}),a=r.createElement("path",{d:"M12.149 7.96l.117-.116a.165.165 0 00-.234 0l.117.117zm-5.166 5.166l-.116-.116.116.116zm-.094.176l.162.033-.162-.033zm-.35 1.755l.161.032-.162-.032zm.404.404l.032.162-.032-.162zm1.755-.35l.032.161-.032-.162zm.175-.095l.117.117-.117-.117zm5.166-5.165l.116.116a.165.165 0 000-.233l-.116.117zm-1.047-3.705l.116.116-.116-.116zm1.458 0l-.116.116.116-.116zM6.497 12.64l.117.117-.117-.117zm-.282.527l-.162-.032.162.032zm-.35 1.755l.161.032-.162-.032zm1.213 1.213l-.033-.162.033.162zm1.754-.35l.033.161-.033-.162zm.527-.283l.117.117-.117-.117zm6.495-6.494l-.117-.117.117.117zm0-1.459l.117-.116-.117.116zm-3.822.295L6.867 13.01l.233.233 5.166-5.165-.234-.234zM6.867 13.01a.509.509 0 00-.14.26l.324.065a.18.18 0 01.05-.092l-.234-.233zm-.14.26l-.35 1.754.323.065.351-1.755-.323-.064zm-.35 1.754a.509.509 0 00.598.599l-.064-.324a.179.179 0 01-.21-.21l-.324-.065zm.598.599l1.755-.35-.065-.325-1.754.351.064.324zm1.755-.35a.508.508 0 00.26-.14l-.233-.233a.18.18 0 01-.092.048l.065.324zm.26-.14l5.165-5.166-.233-.233L8.757 14.9l.233.233zm3.042-7.055l1.89 1.89.233-.234-1.89-1.89-.233.234zm1.076-1.816a.866.866 0 011.226 0l.233-.233a1.196 1.196 0 00-1.692 0l.233.233zm-6.494 6.495l6.494-6.495-.233-.233-6.494 6.495.233.233zm-.237.443a.866.866 0 01.237-.443l-.233-.233c-.167.167-.281.38-.328.61l.324.066zm-.35 1.754l.35-1.754-.324-.065-.35 1.755.323.064zm1.018 1.02a.866.866 0 01-1.019-1.02l-.323-.065a1.196 1.196 0 001.407 1.408l-.065-.324zm1.755-.351l-1.755.35.065.324 1.755-.35-.065-.324zm.443-.237a.866.866 0 01-.443.237l.065.323c.231-.046.444-.16.611-.327l-.233-.233zm6.494-6.495l-6.494 6.495.233.233 6.495-6.494-.234-.234zm0-1.225a.866.866 0 010 1.225l.234.234a1.196 1.196 0 000-1.692l-.234.233zm-1.403-1.404l1.403 1.404.234-.233-1.404-1.404-.233.233z"});t.a=function SvgPencilAlt(e){return r.createElement("svg",i({viewBox:"0 0 22 22",fill:"currentColor"},e),o,a)}},314:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelHeader}));var r=n(1),i=n.n(r),o=n(20),a=n(104);function SelectionPanelHeader(t){var n=t.children,r=t.title,i=t.onCloseClick;return e.createElement("header",{className:"googlesitekit-selection-panel-header"},e.createElement("div",{className:"googlesitekit-selection-panel-header__row"},e.createElement("h3",null,r),e.createElement(o.a,{className:"googlesitekit-selection-panel-header__close",onClick:i,linkButton:!0},e.createElement(a.a,{width:"15",height:"15"}))),n)}SelectionPanelHeader.propTypes={children:i.a.node,title:i.a.string,onCloseClick:i.a.func}}).call(this,n(4))},315:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelItem}));var r=n(1),i=n.n(r),o=n(2),a=n(328),c=n(77);function SelectionPanelItem(t){var n=t.children,r=t.id,i=t.slug,s=t.title,l=t.description,u=t.isItemSelected,g=t.isItemDisabled,d=t.onCheckboxChange,f=t.subtitle,p=t.suffix,m=t.badge,b=t.isNewlyDetected;return e.createElement("div",{className:"googlesitekit-selection-panel-item"},e.createElement(a.a,{badge:m,checked:u,disabled:g,id:r,onChange:d,title:s,value:i},f&&e.createElement("span",{className:"googlesitekit-selection-panel-item__subtitle"},f),l,n),b&&e.createElement(c.a,{label:Object(o.__)("New","google-site-kit")}),p&&e.createElement("span",{className:"googlesitekit-selection-panel-item__suffix"},p))}SelectionPanelItem.propTypes={children:i.a.node,id:i.a.string,slug:i.a.string,title:i.a.string,description:i.a.string,isItemSelected:i.a.bool,isItemDisabled:i.a.bool,onCheckboxChange:i.a.func,subtitle:i.a.string,suffix:i.a.node,badge:i.a.node,isNewlyDetected:i.a.bool}}).call(this,n(4))},316:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelItems}));var r=n(21),i=n.n(r),o=n(1),a=n.n(o),c=n(0),s=n(2);function SelectionPanelItems(t){var n=t.currentSelectionTitle,r=void 0===n?Object(s.__)("Current selection","google-site-kit"):n,o=t.availableItemsTitle,a=void 0===o?Object(s.__)("Additional items","google-site-kit"):o,l=t.savedItemSlugs,u=void 0===l?[]:l,g=t.availableSavedItems,d=void 0===g?{}:g,f=t.availableUnsavedItems,p=void 0===f?{}:f,m=t.ItemComponent,b=t.notice,v=function(t){return Object.keys(t).map((function(n){return e.createElement(m,i()({key:n,slug:n,savedItemSlugs:u},t[n]))}))},h=Object.keys(p).length;return e.createElement("div",{className:"googlesitekit-selection-panel-items"},0!==u.length&&e.createElement(c.Fragment,null,e.createElement("p",{className:"googlesitekit-selection-panel-items__subheading"},r),e.createElement("div",{className:"googlesitekit-selection-panel-items__subsection"},v(d)),h>0&&e.createElement("p",{className:"googlesitekit-selection-panel-items__subheading"},a)),h>0&&e.createElement("div",{className:"googlesitekit-selection-panel-items__subsection"},v(p)),b)}SelectionPanelItems.propTypes={currentSelectionTitle:a.a.string,availableItemsTitle:a.a.string,savedItemSlugs:a.a.array,availableSavedItems:a.a.object,availableUnsavedItems:a.a.object,ItemComponent:a.a.elementType,notice:a.a.node}}).call(this,n(4))},317:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelFooter}));var r=n(5),i=n.n(r),o=n(16),a=n.n(o),c=n(15),s=n.n(c),l=n(14),u=n(1),g=n.n(u),d=n(0),f=n(38),p=n(2),m=n(3),b=n(10),v=n(120),h=n(9),y=n(8),O=n(44),_=n(54);function SelectionPanelFooter(t){var n=t.savedItemSlugs,r=void 0===n?[]:n,o=t.selectedItemSlugs,c=void 0===o?[]:o,u=t.saveSettings,g=void 0===u?function(){}:u,k=t.saveError,j=t.itemLimitError,E=t.minSelectedItemCount,S=void 0===E?0:E,w=t.maxSelectedItemCount,A=void 0===w?0:w,T=t.isBusy,C=t.onSaveSuccess,D=void 0===C?function(){}:C,R=t.onCancel,N=void 0===R?function(){}:R,P=t.isOpen,x=t.closePanel,L=void 0===x?function(){}:x,I=Object(d.useState)(null),M=s()(I,2),G=M[0],F=M[1],B=Object(d.useState)(!1),z=s()(B,2),V=z[0],U=z[1],q=Object(m.useSelect)((function(e){return e(y.r).isFetchingSyncAvailableAudiences()})),W=Object(d.useMemo)((function(){return!Object(l.isEqual)(Object(h.E)(c),Object(h.E)(r))}),[r,c]),H=(null==r?void 0:r.length)>0&&W?Object(p.__)("Apply changes","google-site-kit"):Object(p.__)("Save selection","google-site-kit"),K=Object(d.useCallback)(a()(i.a.mark((function e(){var t;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,g(c);case 2:t=e.sent,t.error||(D(),L(),F(H),U(!0));case 5:case"end":return e.stop()}}),e)}))),[g,c,D,L,H]),Y=Object(d.useCallback)((function(){L(),N()}),[L,N]),$=Object(d.useState)(null),Z=s()($,2),J=Z[0],X=Z[1];Object(d.useEffect)((function(){null!==J&&J!==P&&P&&(F(null),U(!1)),X(P)}),[P,J]);var Q=(null==c?void 0:c.length)||0,ee=q?e.createElement(O.a,{width:"89px",height:"20px"}):e.createElement("p",{className:"googlesitekit-selection-panel-footer__item-count"},Object(f.a)(Object(p.sprintf)(
/* translators: 1: Number of selected items. 2: Maximum number of items that can be selected. */
Object(p.__)("%1$d selected <MaxCount>(up to %2$d)</MaxCount>","google-site-kit"),Q,A),{MaxCount:e.createElement("span",{className:"googlesitekit-selection-panel-footer__item-count--max-count"})}));return e.createElement("footer",{className:"googlesitekit-selection-panel-footer"},k&&e.createElement(v.a,{error:k}),e.createElement("div",{className:"googlesitekit-selection-panel-footer__content"},W&&j?e.createElement(_.a,{noPrefix:!0,message:j}):ee,e.createElement("div",{className:"googlesitekit-selection-panel-footer__actions"},e.createElement(b.Button,{tertiary:!0,onClick:Y,disabled:T},Object(p.__)("Cancel","google-site-kit")),e.createElement(b.SpinnerButton,{onClick:K,isSaving:T,disabled:Q<S||Q>A||T||!P&&V},G||H))))}SelectionPanelFooter.propTypes={savedItemSlugs:g.a.array,selectedItemSlugs:g.a.array,saveSettings:g.a.func,saveError:g.a.object,itemLimitError:g.a.string,minSelectedItemCount:g.a.number,maxSelectedItemCount:g.a.number,isBusy:g.a.bool,onSaveSuccess:g.a.func,onCancel:g.a.func,isOpen:g.a.bool,closePanel:g.a.func}}).call(this,n(4))},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},325:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("path",{d:"M7.334 11.333h1.333v-4H7.334v4zM8.001 6a.658.658 0 00.667-.667.605.605 0 00-.2-.467.605.605 0 00-.467-.2.658.658 0 00-.667.667c0 .189.061.35.183.483A.69.69 0 008.001 6zm0 8.666a6.583 6.583 0 01-2.6-.516 6.85 6.85 0 01-2.117-1.434A6.85 6.85 0 011.851 10.6 6.582 6.582 0 011.334 8c0-.923.172-1.79.517-2.6a6.85 6.85 0 011.433-2.117c.6-.6 1.306-1.072 2.117-1.417A6.404 6.404 0 018 1.333c.922 0 1.789.178 2.6.533a6.618 6.618 0 012.116 1.417c.6.6 1.072 1.306 1.417 2.117.355.81.533 1.677.533 2.6 0 .922-.178 1.789-.533 2.6a6.619 6.619 0 01-1.417 2.116 6.85 6.85 0 01-2.116 1.434 6.583 6.583 0 01-2.6.516zm0-1.333c1.489 0 2.75-.517 3.783-1.55s1.55-2.294 1.55-3.783c0-1.49-.517-2.75-1.55-3.784-1.033-1.033-2.294-1.55-3.783-1.55-1.49 0-2.75.517-3.784 1.55C3.184 5.25 2.667 6.511 2.667 8c0 1.489.517 2.75 1.55 3.783 1.034 1.033 2.295 1.55 3.784 1.55z",fill:"currentColor"});t.a=function SvgInfoGreen(e){return r.createElement("svg",i({viewBox:"0 0 16 16",fill:"none"},e),o)}},326:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanel}));var r=n(11),i=n.n(r),o=n(1),a=n.n(o),c=n(327);function SelectionPanel(t){var n=t.children,r=t.isOpen,o=t.isLoading,a=t.onOpen,s=t.closePanel,l=t.className,u=null==l?void 0:l.split(/\s+/).map((function(e){return".".concat(e)})).join(""),g=u?"".concat(u," .googlesitekit-selection-panel-item .googlesitekit-selection-box input"):".googlesitekit-selection-panel-item .googlesitekit-selection-box input";return e.createElement(c.a,{className:i()("googlesitekit-selection-panel",l),isOpen:r,isLoading:o,onOpen:a,closeSheet:s,focusTrapOptions:{initialFocus:g}},n)}SelectionPanel.propTypes={children:a.a.node,isOpen:a.a.bool,isLoading:a.a.bool,onOpen:a.a.func,closePanel:a.a.func,className:a.a.string}}).call(this,n(4))},327:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SideSheet}));var r=n(6),i=n.n(r),o=n(11),a=n.n(o),c=n(405),s=n.n(c),l=n(1),u=n.n(l),g=n(209),d=n(392),f=n(0),p=n(56),m=n(72);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function SideSheet(t){var n=t.className,r=t.children,i=t.isOpen,o=t.isLoading,c=t.onOpen,l=void 0===c?function(){}:c,u=t.closeSheet,b=void 0===u?function(){}:u,h=t.focusTrapOptions,y=void 0===h?{}:h,O=Object(f.useRef)();return Object(f.useEffect)((function(){i?(l(),document.body.classList.add("googlesitekit-side-sheet-scroll-lock")):document.body.classList.remove("googlesitekit-side-sheet-scroll-lock")}),[i,l]),Object(g.a)(O,b),Object(d.a)((function(e){return i&&p.c===e.keyCode}),b),e.createElement(m.a,null,e.createElement(s.a,{active:!!i&&!o,focusTrapOptions:v({fallbackFocus:"body"},y)},e.createElement("section",{ref:O,className:a()("googlesitekit-side-sheet",n,{"googlesitekit-side-sheet--open":i}),role:"dialog","aria-modal":"true","aria-hidden":!i,tabIndex:"0"},r)),i&&e.createElement("span",{className:"googlesitekit-side-sheet-overlay"}))}SideSheet.propTypes={className:u.a.string,children:u.a.node,isOpen:u.a.bool,isLoading:u.a.bool,onOpen:u.a.func,closeSheet:u.a.func,focusTrapOptions:u.a.object}}).call(this,n(4))},328:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionBox}));var r=n(1),i=n.n(r),o=n(11),a=n.n(o),c=n(10);function SelectionBox(t){var n=t.badge,r=t.checked,i=t.children,o=t.disabled,s=t.id,l=t.onChange,u=t.title,g=t.value;return e.createElement("div",{className:a()("googlesitekit-selection-box",{"googlesitekit-selection-box--disabled":o})},e.createElement(c.Checkbox,{checked:r,description:i,disabled:o,id:s,name:s,onChange:l,value:g,badge:n},u))}SelectionBox.propTypes={badge:i.a.node,checked:i.a.bool,children:i.a.node,disabled:i.a.bool,id:i.a.string,onChange:i.a.func,title:i.a.string,value:i.a.string}}).call(this,n(4))},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(22),i=n(18);function o(){var e=Object(i.a)();return r.g.includes(e)}},342:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(14),i=n(122);function o(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(r.isPlainObject)(e)&&(!(!e.hasOwnProperty("fieldNames")||!Array.isArray(e.fieldNames)||0===e.fieldNames.length)&&(!(!e.hasOwnProperty("limit")||"number"!=typeof e.limit)&&!(e.hasOwnProperty("orderby")&&!Object(i.e)(e.orderby))))}))}},35:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return g}));n(14);var r=n(2),i="missing_required_scopes",o="insufficientPermissions",a="forbidden";function c(e){return(null==e?void 0:e.code)===i}function s(e){var t;return[o,a].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function l(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||s(e)||c(e)||l(e))}function g(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(r.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(r.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},359:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return ChipTabGroup}));var i,o=n(21),a=n.n(o),c=n(27),s=n.n(c),l=n(15),u=n.n(l),g=n(6),d=n.n(g),f=n(81),p=n(420),m=n(0),b=n(422),v=n(2),h=n(3),y=n(10),O=n(26),_=n(29),k=n(8),j=n(23),E=n(7),S=n(19),w=n(360),A=n(361),T=n(362),C=n(24),D=n(121),R=n(214),N=n(131),P=n(74),x=(i={},d()(i,O.c.SLUG,R.a),d()(i,O.g.SLUG,N.a),i);function ChipTabGroup(t){var n=t.allMetricItems,i=t.savedItemSlugs,o=Object(m.useRef)(),c=Object(m.useState)(O.c.SLUG),l=u()(c,2),g=l[0],R=l[1],N=Object(m.useState)(0),L=u()(N,2),I=L[0],M=L[1],G=Object(C.e)()===C.b,F=Object(h.useSelect)((function(e){return e(_.a).getValue(O.j,O.i)})),B=Object(h.useSelect)((function(e){return e(_.a).getValue(O.j,O.a)||[]})),z=Object(h.useSelect)((function(e){return e(_.a).getValue(O.j,O.o)||[]})),V=Object(h.useSelect)((function(e){return e(E.a).isUserInputCompleted()})),U=Object(h.useSelect)((function(e){var t,n=e(E.a).getUserPickedMetrics();if(null==n?void 0:n.length){var r=e(k.r).getKeyMetricsConversionEventWidgets();return Object.keys(r).filter((function(e){return n.some((function(t){return r[e].includes(t)}))}))}var i=e(E.a).getUserInputSettings();return null==i||null===(t=i.includeConversionEvents)||void 0===t?void 0:t.values})),q=Object(h.useSelect)((function(e){return e(S.a).isModuleConnected("analytics-4")})),W=Object(h.useSelect)((function(e){return q?e(k.r).getDetectedEvents():[]})),H=Object(h.useSelect)((function(e){return e(E.a).getAnswerBasedMetrics(null,[].concat(s()(U||[]),s()(W||[])))})),K=[k.l.SUBMIT_LEAD_FORM,k.l.CONTACT,k.l.GENERATE_LEAD].filter((function(e){return(null==W?void 0:W.includes(e))||(null==U?void 0:U.includes(e))})),Y=[k.l.ADD_TO_CART,k.l.PURCHASE].filter((function(e){return(null==W?void 0:W.includes(e))||(null==U?void 0:U.includes(e))})),$=Object(m.useMemo)((function(){return[O.h,O.d].concat(s()((null==K?void 0:K.length)?[O.e]:[]),s()((null==Y?void 0:Y.length)?[O.f]:[]),[O.b])}),[K,Y]),Z=Object(m.useMemo)((function(){return V&&(null==H?void 0:H.length)?[O.c,O.g]:[O.c]}),[V,H]),J=Object(m.useMemo)((function(){return[].concat(s()(Z),s()($))}),[Z,$]),X=Object(h.useSelect)((function(e){if(!q)return[];var t=e(k.r).getNewBadgeEvents();if((null==W?void 0:W.length)&&(null==t?void 0:t.length)){var n=W.filter((function(e){return k.e.includes(e)})),r=t.filter((function(e){return k.e.includes(e)})),i=t.filter((function(e){return!k.e.includes(e)}));if((null==n?void 0:n.length)>1&&r.length>0)return i}return t})),Q=Object(h.useSelect)((function(e){return q?e(k.r).getKeyMetricsConversionEventWidgets():[]})),ee=Object(m.useCallback)((function(){var e,t,n,r=null===(e=o.current)||void 0===e?void 0:e.querySelector(".mdc-tab-scroller__scroll-content");if(G){var i=null===(t=o.current)||void 0===t?void 0:t.querySelectorAll(".googlesitekit-chip-tab-group__tab-items .mdc-tab");if((null==i?void 0:i.length)&&r){var a=null===(n=o.current)||void 0===n?void 0:n.getBoundingClientRect(),c=[];i.forEach((function(e,t){var n=e.getBoundingClientRect();n.left>=a.left&&n.right<=a.right&&c.push(t)}));var s=i[c.length];if(s){var l=s.getBoundingClientRect();(l.left>=a.right||l.left-a.right<0&&-(l.left-a.right)<=20)&&("2px"===r.style.columnGap?r.style.columnGap="20px":r.style.columnGap="2px",ee())}}}}),[G]),te=d()({},O.c.SLUG,0),ne={},re={},ie=function(e){var t,r=n[e].group;if((r===g||g===O.c.SLUG&&B.includes(e))&&(ne[e]=n[e]),g===O.g.SLUG&&H.includes(e)&&H.includes(e)&&(ne[e]=n[e]),!te[r]){var i=Object.keys(n).filter((function(e){return!(n[e].group!==r||!(null==F?void 0:F.includes(e)))})).length;te[r]=i}(null==X?void 0:X.length)&&(X.some((function(t){return Q[t].includes(e)}))&&(re[r]=[].concat(s()(null!==(t=re[r])&&void 0!==t?t:[]),[e])))};for(var oe in n)ie(oe);var ae=Object(h.useDispatch)(_.a).setValues,ce=Object(m.useCallback)((function(){var e;ae(O.j,(e={},d()(e,O.i,F),d()(e,O.a,[].concat(s()(B),s()(z))),d()(e,O.o,[]),e))}),[F,B,z,ae]),se=Object(m.useCallback)((function(e,t){if(e)R(e);else{var n=J[t];M(t),R(n.SLUG)}z.length&&ce()}),[J,z,R,ce]),le=Object(h.useSelect)((function(e){return e(j.b).getValue(O.k)})),ue=Object(b.a)(le),ge=Object.keys(re);Object(m.useEffect)((function(){if(!ue&&le)if(R(O.c.SLUG),M(0),ge.length&&G){var e=J.find((function(e){return e.SLUG===ge[0]}));M(J.indexOf(e)),R(e.SLUG)}else M(0),R(O.c.SLUG);ue&&!le&&ce(),!ue&&le&&ee()}),[le,ue,z,J,G,ge,ce,ee]);var de=Object(D.a)(ee,50);Object(f.a)((function(){e.addEventListener("resize",de)})),Object(p.a)((function(){return e.removeEventListener("resize",de)}));var fe=[[].concat(s()(Z),s()($.slice(0,2))),s()($.slice(2))];return r.createElement("div",{className:"googlesitekit-chip-tab-group"},r.createElement("div",{className:"googlesitekit-chip-tab-group__tab-items",ref:o},!G&&fe.map((function(e){return r.createElement("div",{key:"row-".concat(e[0].SLUG),className:"googlesitekit-chip-tab-group__tab-items-row"},e.map((function(e){return r.createElement(w.a,{key:e.SLUG,slug:e.SLUG,label:e.LABEL,hasNewBadge:!!(null==re?void 0:re[e.SLUG]),isActive:e.SLUG===g,onClick:se,selectedCount:te[e.SLUG]})})))})),G&&r.createElement(y.TabBar,{activeIndex:I,handleActiveIndexUpdate:function(e){return se(null,e)}},J.map((function(e,t){var n=x[e.SLUG]||P.a;return r.createElement(y.Tab,{key:t,"aria-label":e.LABEL},r.createElement(n,{width:12,height:12,className:"googlesitekit-chip-tab-group__chip-item-svg googlesitekit-chip-tab-group__tab-item-mobile-svg googlesitekit-chip-tab-group__chip-item-svg__".concat(e.SLUG)}),e.LABEL,te[e.SLUG]>0&&r.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-count"},"(",te[e.SLUG],")"),!!(null==re?void 0:re[e.SLUG])&&r.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-new-dot"}))})))),r.createElement("div",{className:"googlesitekit-chip-tab-group__tab-item"},Object.keys(ne).map((function(e){var t,n=ne[e].group,o=null==re||null===(t=re[n])||void 0===t?void 0:t.includes(e);return r.createElement(A.a,a()({key:e,slug:e,savedItemSlugs:i,isNewlyDetected:o},ne[e]))})),!Object.keys(ne).length&&r.createElement("div",{className:"googlesitekit-chip-tab-group__graphic"},r.createElement(T.a,{height:250}),r.createElement("p",null,Object(v.__)("No metrics were selected yet","google-site-kit")))))}}).call(this,n(28),n(4))},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return O})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return y}));var r=n(99),i=e._googlesitekitTrackingData||{},o=i.activeModules,a=void 0===o?[]:o,c=i.isSiteKitScreen,s=i.trackingEnabled,l=i.trackingID,u=i.referenceSiteURL,g=i.userIDHash,d=i.isAuthenticated,f={activeModules:a,trackingEnabled:s,trackingID:l,referenceSiteURL:u,userIDHash:g,isSiteKitScreen:c,userRoles:i.userRoles,isAuthenticated:d,pluginVersion:"1.151.0"},p=Object(r.a)(f),m=p.enableTracking,b=p.disableTracking,v=(p.isTrackingEnabled,p.initializeSnippet),h=p.trackEvent,y=p.trackEventOnce;function O(e){e?m():b()}c&&s&&v()}).call(this,n(28))},360:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Chip}));var r,i=n(6),o=n.n(i),a=n(1),c=n.n(a),s=n(11),l=n.n(s),u=n(10),g=n(26),d=n(214),f=n(131),p=n(74),m=(r={},o()(r,g.c.SLUG,d.a),o()(r,g.g.SLUG,f.a),r);function Chip(t){var n=t.slug,r=t.label,i=t.isActive,o=t.onClick,a=t.hasNewBadge,c=void 0!==a&&a,s=t.selectedCount,g=void 0===s?0:s,d=m[n]||p.a;return e.createElement(u.Button,{className:l()("googlesitekit-chip-tab-group__chip-item",{"googlesitekit-chip-tab-group__chip-item--active":i}),icon:e.createElement(d,{width:12,height:12,className:"googlesitekit-chip-tab-group__chip-item-svg googlesitekit-chip-tab-group__chip-item-svg__".concat(n)}),trailingIcon:g>0?e.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-count"},"(",g,")"):null,onClick:function(){return o(n)}},r,c&&e.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-new-dot"}))}Chip.propTypes={slug:c.a.string.isRequired,label:c.a.string.isRequired,isActive:c.a.bool,hasNewBadge:c.a.bool,selectedCount:c.a.number,onClick:c.a.func.isRequired}}).call(this,n(4))},361:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricItem}));var r=n(6),i=n.n(r),o=n(27),a=n.n(o),c=n(1),s=n.n(c),l=n(0),u=n(2),g=n(3),d=n(29),f=n(47),p=n(19),m=n(26),b=n(117);function MetricItem(t){var n=t.slug,r=t.title,o=t.description,c=t.isNewlyDetected,s=t.savedItemSlugs,v=void 0===s?[]:s,h=Object(g.useSelect)((function(e){var t=e(p.a).getModule,r=e(f.a).getWidget(n);return null==r?void 0:r.modules.reduce((function(e,n){var r=t(n);return(null==r?void 0:r.connected)||!(null==r?void 0:r.name)?e:[].concat(a()(e),[r.name])}),[])})),y=Object(g.useSelect)((function(e){return e(d.a).getValue(m.j,m.i)})),O=Object(g.useSelect)((function(e){return e(d.a)})).getValue,_=Object(g.useDispatch)(d.a).setValues,k=Object(l.useCallback)((function(e){var t,r=O(m.j,m.i),o=e.target.checked?r.concat([n]):r.filter((function(e){return e!==n}));_(m.j,(t={},i()(t,m.i,o),i()(t,m.o,o),t))}),[O,_,n]),j=null==y?void 0:y.includes(n),E=!v.includes(n)&&h.length>0,S="key-metric-selection-checkbox-".concat(n);return e.createElement(b.c,{id:S,slug:n,title:r,description:o,isNewlyDetected:c,isItemSelected:j,isItemDisabled:E,onCheckboxChange:k},h.length>0&&e.createElement("div",{className:"googlesitekit-selection-panel-item-error"},Object(u.sprintf)(
/* translators: %s: module names. */
Object(u._n)("%s is disconnected, no data to show","%s are disconnected, no data to show",h.length,"google-site-kit"),h.join(Object(u.__)(" and ","google-site-kit")))))}MetricItem.propTypes={slug:s.a.string.isRequired,title:s.a.string.isRequired,description:s.a.string.isRequired,isNewlyDetected:s.a.bool,savedItemSlugs:s.a.array}}).call(this,n(4))},362:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("path",{d:"M59.238 58.571c-2.136 20.178 4.272 29.099 20.48 53.216 16.209 24.118-29.092 62.914 5.475 101.268 33.827 37.532 69.419.009 111.314-4.555 29.443-3.208 57.819 12.98 90.86 5.9 33.04-7.08 46.385-42.599 43.153-68.059-5.59-44.041-26.24-49.107-34.893-66.461-8.654-17.354 2.902-52.997-30.287-73.16-33.19-20.163-76.71 14.42-112.503 12.37-20.651-1.182-40.932-4.995-59.264.86-18.53 5.918-32.662 22.571-34.335 38.621z",fill:"#B8E6CA"}),a=r.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter0_d_2200_11981)"},r.createElement("rect",{x:242.455,y:45.266,width:130.621,height:89.651,rx:10.957,transform:"rotate(15 242.455 45.266)",fill:"#fff"})),c=r.createElement("rect",{x:253.726,y:64.785,width:24.903,height:7.969,rx:3.985,transform:"rotate(15 253.726 64.785)",fill:"#EBEEF0"}),s=r.createElement("rect",{x:249.342,y:81.144,width:49.806,height:19.923,rx:9.961,transform:"rotate(15 249.342 81.144)",fill:"#FFDED3"}),l=r.createElement("rect",{x:240.436,y:114.357,width:99.428,height:8.773,rx:3.985,transform:"rotate(15 240.436 114.357)",fill:"#EBEEF0"}),u=r.createElement("path",{d:"M256.195 90.198l4.644 8.044m0 0l1.412-4.986m-1.412 4.986l-5.023-1.27",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),g=r.createElement("rect",{x:268.706,y:93.551,width:19.923,height:5.977,rx:1.992,transform:"rotate(15 268.706 93.55)",fill:"#fff"}),d=r.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter1_d_2200_11981)"},r.createElement("rect",{x:13.887,y:79.094,width:130.621,height:89.68,rx:10.957,transform:"rotate(-15 13.887 79.094)",fill:"#fff"})),f=r.createElement("rect",{x:32.989,y:90.122,width:62.386,height:7.798,rx:3.899,transform:"rotate(-15 32.99 90.122)",fill:"#EBEEF0"}),p=r.createElement("rect",{x:37.691,y:106.902,width:49.806,height:19.923,rx:9.961,transform:"rotate(-15 37.691 106.902)",fill:"#FFDED3"}),m=r.createElement("rect",{x:46.612,y:140.967,width:99.428,height:7.798,rx:3.899,transform:"rotate(-15 46.612 140.967)",fill:"#EBEEF0"}),b=r.createElement("path",{d:"M48.152 111.318l8.044 4.645m0 0l-1.27-5.024m1.27 5.024l-4.986 1.411",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),v=r.createElement("rect",{x:60.663,y:107.966,width:19.923,height:5.977,rx:1.992,transform:"rotate(-15 60.663 107.966)",fill:"#fff"}),h=r.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter2_d_2200_11981)"},r.createElement("rect",{x:126.251,y:37.4,width:130.621,height:89.68,rx:10.957,fill:"#fff"})),y=r.createElement("rect",{x:143.013,y:53.134,width:98.333,height:7.867,rx:3.933,fill:"#EBEEF0"}),O=r.createElement("rect",{x:142.369,y:70.423,width:49.806,height:19.923,rx:9.961,fill:"#B8E6CA"}),_=r.createElement("rect",{x:143.013,y:105.84,width:33.04,height:7.867,rx:3.933,fill:"#EBEEF0"}),k=r.createElement("path",{d:"M151.336 84.036l6.568-6.567m0 0l-5.182-.073m5.182.073l.073 5.18",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),j=r.createElement("rect",{x:164.287,y:77.395,width:19.923,height:5.977,rx:1.992,fill:"#fff"}),E=r.createElement("path",{d:"M59.237 58.571C57.1 78.75 63.509 87.67 79.717 111.787c16.209 24.118-29.091 62.914 5.475 101.268 33.827 37.532 69.419.009 111.314-4.555 29.444-3.208 57.82 12.98 90.86 5.9s46.385-42.599 43.153-68.059c-5.59-44.041-26.24-49.107-34.893-66.461-8.654-17.354 2.902-52.997-30.287-73.16-33.19-20.163-76.71 14.42-112.503 12.37-20.651-1.182-40.932-4.995-59.264.86C75.042 25.867 60.91 42.52 59.237 58.57z",fill:"#B8E6CA"}),S=r.createElement("g",{mask:"url(#key-metrics-no-selected-items_svg__a)"},r.createElement("path",{d:"M227.674 108.973l11.312-8.418M218.925 98.852l2.868-12.68M205.623 102.87l-5.375-13.037",stroke:"#CBD0D3",strokeWidth:3.147,strokeMiterlimit:10}),r.createElement("path",{d:"M63.953 190.487c16.127 12.193 38.716 10.349 55.335 5.162 16.618-5.187 31.107-14.61 45.314-23.791 6.717-4.337 13.617-8.738 21.496-11.119 7.878-2.381 17.057-2.39 22.958 1.658 3.392 2.328 5.205 5.923 5.36 9.702",stroke:"#3C7251",strokeWidth:9.44,strokeLinejoin:"round"}),r.createElement("path",{d:"M215.831 109.67l-19.169 71.73",stroke:"#CBD0D3",strokeWidth:9.44,strokeMiterlimit:10,strokeLinecap:"round"}),r.createElement("path",{d:"M213.975 116.472l-19.169 71.731",stroke:"#161B18",strokeWidth:9.44,strokeMiterlimit:10})),w=r.createElement("defs",null,r.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter0_d_2200_11981",x:205.773,y:35.772,width:176.33,height:147.36,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.985}),r.createElement("feGaussianBlur",{stdDeviation:7.969}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})),r.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter1_d_2200_11981",x:.409,y:35.793,width:176.337,height:147.388,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.985}),r.createElement("feGaussianBlur",{stdDeviation:7.969}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})),r.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter2_d_2200_11981",x:110.313,y:25.447,width:162.497,height:121.556,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),r.createElement("feOffset",{dy:3.985}),r.createElement("feGaussianBlur",{stdDeviation:7.969}),r.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})));t.a=function SvgKeyMetricsNoSelectedItems(e){return r.createElement("svg",i({viewBox:"0 0 383 238",fill:"none"},e),o,a,c,s,l,u,g,d,f,p,m,b,v,h,y,O,_,k,j,r.createElement("mask",{id:"key-metrics-no-selected-items_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:58,y:0,width:273,height:230},E),S,w)}},366:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return GoogleChart}));var i=n(6),o=n.n(i),a=n(27),c=n.n(a),s=n(21),l=n.n(s),u=n(15),g=n.n(u),d=n(25),f=n.n(d),p=(n(594),n(11)),m=n.n(p),b=n(12),v=n.n(b),h=n(1),y=n.n(h),O=n(429),_=n(81),k=n(208),j=n(0),E=n(44),S=n(7),w=n(114),A=n(3),T=n(508),C=n(509),D=n(23),R=n(18),N=n(173),P=n(263),x=n(9),L=n(24);function I(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function M(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?I(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):I(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function GoogleChart(t){var n=t.chartEvents,i=t.chartType,o=t.children,a=t.className,s=t.data,u=t.dateMarkers,d=t.getChartWrapper,p=t.height,b=t.loaded,h=t.loadingHeight,y=t.loadingWidth,I=t.onMouseOver,M=t.onMouseOut,G=t.onReady,F=t.onSelect,B=t.selectedStats,z=t.width,V=t.options,U=t.gatheringData,q=f()(t,["chartEvents","chartType","children","className","data","dateMarkers","getChartWrapper","height","loaded","loadingHeight","loadingWidth","onMouseOver","onMouseOut","onReady","onSelect","selectedStats","width","options","gatheringData"]),W=Object(k.a)(GoogleChart),H=Object(L.e)(),K=Object(A.useSelect)((function(e){return e(S.a).getDateRangeDates({offsetDays:0})})),Y=K.startDate,$=K.endDate,Z=Object(R.a)(),J=Object(A.useSelect)((function(e){return e(D.b).getValue("googleChartsCollisionError")})),X=Object(j.useState)(!1),Q=g()(X,2),ee=Q[0],te=Q[1],ne=Object(A.useDispatch)(D.b).setValue,re=Object(P.d)(s,B),ie="PieChart"===i?"circular":"square",oe=Object(P.e)(h,p,y,z),ae=e.createElement("div",{className:"googlesitekit-chart-loading"},e.createElement(E.a,l()({className:"googlesitekit-chart-loading__wrapper",shape:ie},oe))),ce=Object(j.useRef)(),se=Object(j.useRef)();Object(_.a)((function(){var e,t,n,i;void 0===J&&(Object(N.a)(Z)&&(null===(e=r)||void 0===e||null===(t=e.google)||void 0===t?void 0:t.charts)&&(r.google.charts=void 0),!Object(N.a)(Z)&&(null===(n=r)||void 0===n||null===(i=n.google)||void 0===i?void 0:i.charts)?ne("googleChartsCollisionError",!0):ne("googleChartsCollisionError",!1))})),Object(j.useEffect)((function(){return function(){if(se.current&&ce.current){var e=se.current.visualization.events;e.removeAllListeners(ce.current.getChart()),e.removeAllListeners(ce.current)}}}),[]),Object(j.useLayoutEffect)((function(){var e,t;I&&(null===(e=se.current)||void 0===e||e.visualization.events.addListener(ce.current.getChart(),"onmouseover",(function(e){I(e,{chartWrapper:ce.current,google:se.current})})));M&&(null===(t=se.current)||void 0===t||t.visualization.events.addListener(ce.current.getChart(),"onmouseout",(function(e){M(e,{chartWrapper:ce.current,google:se.current})})))}),[I,M]);var le=u.filter((function(e){return!!((t=new Date(e.date))&&Y&&$)&&!(t.getTime()<Object(x.G)(Y).getTime()||t.getTime()>Object(x.G)($).getTime());var t}));if(J)return null;if(!b)return e.createElement("div",{className:m()("googlesitekit-chart","googlesitekit-chart-loading__forced",a)},ae);var ue=Object(P.b)([].concat(c()(n||[]),[{eventName:"ready",callback:function(){var e;if(ce.current&&le.length){var t=ce.current.getChart(),n=null==t?void 0:t.getChartLayoutInterface(),r=null==n?void 0:n.getChartAreaBoundingBox(),i=ce.current.getDataTable();if(n&&r&&i){le.forEach((function(e,t){var i=new Date(e.date),o=document.getElementById("googlesitekit-chart__date-marker-line--".concat(W,"-").concat(t));v()(o,"#googlesitekit-chart__date-marker-line--".concat(W,"-").concat(t," is missing from the DOM, but required to render date markers."));var a=Math.floor(n.getXLocation(Object(x.G)(Object(x.p)(i))));if(Object.assign(o.style,{left:"".concat(a-1,"px"),top:"".concat(Math.floor(r.top),"px"),height:"".concat(Math.floor(r.height),"px"),opacity:1}),e.text){var c=document.getElementById("googlesitekit-chart__date-marker-tooltip--".concat(W,"-").concat(t));v()(c,"#googlesitekit-chart__date-marker-tooltip--".concat(W,"-").concat(t," is missing from the DOM, but required to render date marker tooltips.")),Object.assign(c.style,{left:"".concat(a-9,"px"),top:"".concat(Math.floor(r.top)-18,"px"),opacity:1})}}));var o=null===(e=document.querySelector("#googlesitekit-chart-".concat(W," svg:first-of-type > g:first-of-type > g > g > text")))||void 0===e?void 0:e.parentElement.parentElement.parentElement;!!o&&document.querySelectorAll("#googlesitekit-chart-".concat(W," svg:first-of-type > g")).length>=3&&(o.style.transform="translateY(-10px)")}}}}]),G,F),ge=Object(P.a)(V,U,i,Y,$,H);return e.createElement(T.a,null,e.createElement("div",{className:m()("googlesitekit-chart","googlesitekit-chart--".concat(i),a),id:"googlesitekit-chart-".concat(W),tabIndex:-1},e.createElement(O.a,l()({className:"googlesitekit-chart__inner",chartEvents:ue,chartLanguage:Object(x.r)(),chartType:i,chartVersion:"49",data:re,loader:ae,height:p,getChartWrapper:function(e,t){var n,r,i;(ee||te(!0),e!==ce.current)&&(null===(n=se.current)||void 0===n||n.visualization.events.removeAllListeners(null===(r=ce.current)||void 0===r?void 0:r.getChart()),null===(i=se.current)||void 0===i||i.visualization.events.removeAllListeners(ce.current));ce.current=e,se.current=t,d&&d(e,t)},width:z,options:ge},q)),U&&ee&&e.createElement(w.b,{style:w.a.OVERLAY}),!!le.length&&le.map((function(t,n){return e.createElement(C.a,{key:"googlesitekit-chart__date-marker--".concat(W,"-").concat(n),id:"".concat(W,"-").concat(n),text:t.text})})),o))}GoogleChart.propTypes={className:y.a.string,children:y.a.node,chartEvents:y.a.arrayOf(y.a.shape({eventName:y.a.string,callback:y.a.func})),chartType:y.a.oneOf(["LineChart","PieChart"]).isRequired,data:y.a.array,dateMarkers:y.a.arrayOf(y.a.shape({date:y.a.string.isRequired,text:y.a.string})),getChartWrapper:y.a.func,height:y.a.string,loaded:y.a.bool,loadingHeight:y.a.string,loadingWidth:y.a.string,onMouseOut:y.a.func,onMouseOver:y.a.func,onReady:y.a.func,onSelect:y.a.func,selectedStats:y.a.arrayOf(y.a.number),width:y.a.string,options:y.a.object,gatheringData:y.a.bool},GoogleChart.defaultProps=M(M({},O.a.defaultProps),{},{dateMarkers:[],gatheringData:!1,loaded:!0})}).call(this,n(4),n(28))},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return y})),n.d(t,"c",(function(){return O})),n.d(t,"e",(function(){return _})),n.d(t,"b",(function(){return k}));var r=n(5),i=n.n(r),o=n(16),a=n.n(o),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){c=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(c)throw o}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u,g="googlesitekit_",d="".concat(g).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),f=["sessionStorage","localStorage"],p=[].concat(f),m=function(){var t=a()(i.a.mark((function t(n){var r,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,o="__storage_test__",r.setItem(o,o),r.removeItem(o),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function b(){return v.apply(this,arguments)}function v(){return(v=a()(i.a.mark((function t(){var n,r,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=s(p),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(o=r.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,m(o);case 11:if(!t.sent){t.next=13;break}u=e[o];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=a()(i.a.mark((function e(t){var n,r,o,a,c,s,l;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(d).concat(t)))){e.next=10;break}if(o=JSON.parse(r),a=o.timestamp,c=o.ttl,s=o.value,l=o.isError,!a||c&&!(Math.round(Date.now()/1e3)-a<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:l});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),y=function(){var t=a()(i.a.mark((function t(n,r){var o,a,s,l,u,g,f,p,m=arguments;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o=m.length>2&&void 0!==m[2]?m[2]:{},a=o.ttl,s=void 0===a?c.b:a,l=o.timestamp,u=void 0===l?Math.round(Date.now()/1e3):l,g=o.isError,f=void 0!==g&&g,t.next=3,b();case 3:if(!(p=t.sent)){t.next=14;break}return t.prev=5,p.setItem("".concat(d).concat(n),JSON.stringify({timestamp:u,ttl:s,value:r,isError:f})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),O=function(){var t=a()(i.a.mark((function t(n){var r,o;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,o=n.startsWith(g)?n:"".concat(d).concat(n),r.removeItem(o),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),_=function(){var t=a()(i.a.mark((function t(){var n,r,o,a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],o=0;o<n.length;o++)0===(a=n.key(o)).indexOf(g)&&r.push(a);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),k=function(){var e=a()(i.a.mark((function e(){var t,n,r,o;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!e.sent){e.next=25;break}return e.next=6,_();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return o=r.value,e.next=14,O(o);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},373:function(e,t,n){"use strict";t.a=function(e){if("string"==typeof e&&e.match(/[0-9]{8}/)){var t=e.slice(0,4),n=Number(e.slice(4,6))-1,r=e.slice(6,8);return new Date(t,n.toString(),r)}return!1}},376:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return a.a})),n.d(t,"e",(function(){return c.a})),n.d(t,"a",(function(){return s.a}));var r=n(2);function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.keyColumnIndex,i=void 0===n?0:n,o=t.maxSlices,a=t.withOthers,c=void 0!==a&&a,s=t.tooltipCallback,l=e||{},u=l.rows,g=void 0===u?[]:u,d="function"==typeof s,f=["Source","Percent"];d&&f.push({type:"string",role:"tooltip",p:{html:!0}});var p=[f],m=g.filter((function(e){return"date_range_0"===e.dimensionValues[1].value})),b=m.reduce((function(e,t){return e+parseInt(t.metricValues[0].value,10)}),0),v=g.filter((function(e){return"date_range_1"===e.dimensionValues[1].value})),h=v.reduce((function(e,t){return e+parseInt(t.metricValues[0].value,10)}),0),y=c,O=m.length,_=b,k=h;o>0?(y=c&&m.length>o,O=Math.min(m.length,y?o-1:o)):(y=!1,O=m.length);for(var j=function(e){var t=m[e],n=t.metricValues[i].value,r=v.find((function(e){return e.dimensionValues[0].value===t.dimensionValues[0].value})),o=r?r.metricValues[i].value:0;_-=n,k-=o;var a=b>0?n/b:0,c=[t.dimensionValues[0].value,a];if(d){var l=g.find((function(e){var n=e.dimensionValues;return"date_range_1"===n[1].value&&n[0].value===t.dimensionValues[0].value}));c.push(s(t,l,c))}p.push(c)},E=0;E<O;E++)j(E);if(y&&_>0){var S=[Object(r.__)("Others","google-site-kit"),_/b];d&&S.push(s({metricValues:[{value:_}]},{metricValues:[{value:k}]},S)),p.push(S)}return p}var o=function(e){var t,n,r,i,o,a,c;if(void 0!==e){var s=((null==e?void 0:e.rows)||[]).filter((function(e){return"date_range_0"===e.dimensionValues[1].value}));return 1===(null==s?void 0:s.length)||(null==s||null===(t=s[0])||void 0===t||null===(n=t.metricValues)||void 0===n||null===(r=n[0])||void 0===r?void 0:r.value)===(null==e||null===(i=e.totals)||void 0===i||null===(o=i[0])||void 0===o||null===(a=o.metricValues)||void 0===a||null===(c=a[0])||void 0===c?void 0:c.value)}},a=n(393),c=n(275),s=(n(122),n(342),n(447))},379:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsCTAContent}));var r=n(15),i=n.n(r),o=n(11),a=n.n(o),c=n(1),s=n.n(c),l=n(240),u=n(0),g=n(3),d=n(13),f=n(7),p=n(24),m=n(9),b=n(18),v=n(17),h=n(450),y=n(451),O=n(452),_=n(453),k=n(79);function KeyMetricsCTAContent(t){var n=t.className,r=t.title,o=t.description,c=t.actions,s=t.ga4Connected,j=Object(u.useRef)(),E=Object(p.e)(),S=Object(k.a)(),w=Object(b.a)(),A=E===p.b,T=E===p.c&&S<960,C=S>=1280,D=S>=960&&S<1280;s||(T=E===p.c&&S<800,D=S>=800&&S<1280);var R=Object(l.a)(j,{threshold:.25}),N=Object(u.useState)(!1),P=i()(N,2),x=P[0],L=P[1],I=!!(null==R?void 0:R.intersectionRatio),M=Object(g.useDispatch)(f.a).triggerSurvey,G=Object(g.useSelect)((function(e){return e(d.c).isUsingProxy()}));return Object(u.useEffect)((function(){I&&!x&&(s&&Object(m.I)("".concat(w,"_kmw-cta-notification"),"view_notification"),G&&M("view_kmw_setup_cta",{ttl:m.f}),L(!0))}),[I,w,s,x,G,M]),e.createElement("section",{ref:j,className:a()("googlesitekit-setup__wrapper","googlesitekit-setup__wrapper--key-metrics-setup-cta",n)},e.createElement(v.e,null,e.createElement(v.k,null,e.createElement(v.a,{smSize:5,mdSize:6,lgSize:5,className:"googlesitekit-widget-key-metrics-content__wrapper"},e.createElement("div",{className:"googlesitekit-widget-key-metrics-text__wrapper"},e.createElement("h3",{className:"googlesitekit-publisher-win__title"},r),e.createElement("p",null,o)),e.createElement("div",{className:"googlesitekit-widget-key-metrics-actions__wrapper"},c),T&&e.createElement(v.a,{className:"googlesitekit-widget-key-metrics-svg__wrapper"},e.createElement(O.a,null)),A&&e.createElement(v.a,{className:"googlesitekit-widget-key-metrics-svg__wrapper"},e.createElement(_.a,null))),D&&e.createElement(v.a,{className:"googlesitekit-widget-key-metrics-svg__wrapper"},e.createElement(y.a,null)),C&&e.createElement(v.a,{className:"googlesitekit-widget-key-metrics-svg__wrapper",smSize:6,mdSize:3,lgSize:6},e.createElement(h.a,null)))))}KeyMetricsCTAContent.propTypes={title:s.a.string,description:s.a.string,actions:s.a.node}}).call(this,n(4))},380:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsCTAFooter}));var r=n(1),i=n.n(r),o=n(2),a=n(17),c=n(20);function KeyMetricsCTAFooter(t){var n=t.onActionClick,r=void 0===n?function(){}:n,i=t.showDismiss;return e.createElement(a.k,{className:"googlesitekit-widget-key-metrics-footer"},e.createElement(a.a,{size:12,className:"googlesitekit-widget-key-metrics-footer__cta-wrapper"},!i&&e.createElement("span",null,Object(o.__)("Interested in specific metrics?","google-site-kit")),e.createElement(c.a,{onClick:r},i?Object(o.__)("Maybe later","google-site-kit"):Object(o.__)("Select your own metrics","google-site-kit"))))}KeyMetricsCTAFooter.propTypes={onActionClick:i.a.func}}).call(this,n(4))},381:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileHeader}));var r=n(1),i=n.n(r),o=n(132),a=n(105);function MetricTileHeader(t){var n=t.title,r=t.infoTooltip,i=t.loading;return e.createElement("div",{className:"googlesitekit-km-widget-tile__title-container"},e.createElement("h3",{className:"googlesitekit-km-widget-tile__title"},n),i?e.createElement(a.a,null,e.createElement(o.a,{title:r})):e.createElement(o.a,{title:r}))}MetricTileHeader.propTypes={title:i.a.string,infoTooltip:i.a.oneOfType([i.a.string,i.a.element]),loading:i.a.bool}}).call(this,n(4))},387:function(e,t,n){"use strict";n.d(t,"e",(function(){return o})),n.d(t,"c",(function(){return l})),n.d(t,"b",(function(){return d})),n.d(t,"d",(function(){return f})),n.d(t,"a",(function(){return m}));var r=n(14),i=n(9);function o(e){if(void 0!==e)return!Array.isArray(e)||!e.length||!e.some((function(e){return e.clicks>0||e.ctr>0||e.impressions>0||e.position>0}))}var a=n(11),c=n.n(a),s=n(2),l=function(e,t,n,r,o){var a=[[{type:"date",label:Object(s.__)("Day","google-site-kit")},{type:"string",role:"tooltip",p:{html:!0}},{type:"number",label:n},{type:"number",label:Object(s.__)("Previous period","google-site-kit")}]],l=Object(i.r)(),u={weekday:"short",month:"short",day:"numeric"};return e.forEach((function(e,g){var d,f,p=e[r],m=e.keys[0],b=(null===(d=t[g])||void 0===d?void 0:d[r])||0,v=(null===(f=t[g])||void 0===f?void 0:f.keys[0])||Object(i.s)(m,o),h=Object(s.sprintf)(
/* translators: 1: date for user stats, 2: previous date for user stats comparison */
Object(s._x)("%1$s vs %2$s","Date range for chart tooltip","google-site-kit"),Object(i.G)(m).toLocaleDateString(l,u),Object(i.G)(v).toLocaleDateString(l,u)),y=Object(i.g)(b,p),O=Object(i.h)(p,b),_=Object(i.o)(O),k=Object(s.sprintf)(
/* translators: 1: selected stat label, 2: numeric value of selected stat, 3: up or down arrow , 4: different change in percentage, %%: percent symbol */
Object(s._x)("%1$s: <strong>%2$s</strong> <em>%3$s %4$s%%</em>","Stat information for chart tooltip","google-site-kit"),n,Math.abs(p).toFixed(2).replace(/(.00|0)$/,""),_,Object(i.B)(y));a.push([Object(i.G)(m),'<div class="'.concat(c()("googlesitekit-visualization-tooltip",{"googlesitekit-visualization-tooltip--up":O>0,"googlesitekit-visualization-tooltip--down":O<0}),'">\n\t\t\t\t<p>').concat(h,"</p>\n\t\t\t\t<p>").concat(k,"</p>\n\t\t\t</div>"),p,b])})),a},u=n(12),g=n.n(u),d=function(e){var t=e.startDate,n=e.endDate;return g()(Object(i.w)(t),"A valid startDate is required."),g()(Object(i.w)(n),"A valid endDate is required."),{start_date:t.replace(/-/g,""),end_date:n.replace(/-/g,"")}};function f(e){return"string"==typeof e&&e.length>0}function p(e){var t=[[{type:"string",label:"Day"},{type:"number",label:"Clicks"},{type:"number",label:"Impressions"},{type:"number",label:"CTR"},{type:"number",label:"Position"}]],n=0,o=0,a=0,c=0,s=e.length;return Object(r.each)(e,(function(e){var s=Object(i.G)(e.keys[0]);t.push([s.getMonth()+1+"/"+s.getUTCDate(),e.clicks,e.impressions,Object(r.round)(e.ctr,3),Object(r.round)(e.position,3)]),n+=e.clicks,o+=e.impressions,a+=e.ctr,c+=e.position})),{dataMap:t,totalClicks:n,totalImpressions:o,averageCTR:s>0?a/s:0,averagePosition:s>0?c/s:0}}var m=function(e,t){var n=Object(i.D)(e,{dateRangeLength:t}),r=n.compareRange,o=p(n.currentRange),a=p(r);return{dataMap:o.dataMap,totalClicks:o.totalClicks,totalImpressions:o.totalImpressions,averageCTR:o.averageCTR,averagePosition:o.averagePosition,totalClicksChange:Object(i.g)(a.totalClicks,o.totalClicks),totalImpressionsChange:Object(i.g)(a.totalImpressions,o.totalImpressions),averageCTRChange:Object(i.g)(a.averageCTR,o.averageCTR),averagePositionChange:Object(i.g)(a.averagePosition,o.averagePosition)}}},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var r="_googlesitekitDataLayer",i="data-googlesitekit-gtag"},393:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(14);function i(e){var t;if(void 0!==e)return!((null==e?void 0:e.rows)&&(null==e?void 0:e.totals)&&!(null==e||null===(t=e.totals)||void 0===t?void 0:t.every(r.isEmpty)))||!e.totals.some((function(e){return!!e.metricValues&&e.metricValues.some((function(e){return e.value>0}))}))}},397:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var r=n(3),i=n(19),o=n(7),a=n(26),c=n(8),s=n(66);function l(){return Object(r.useSelect)((function(e){var t=e(o.a).isItemDismissed(a.l),n=e(o.a).isDismissingItem(a.l),r=u(e,"search-console",s.b),i=u(e,"analytics-4",c.r);return!1===t&&!1===n&&r&&i}),[])}function u(e,t,n){if(e(i.a).isModuleConnected(t)){var r=e(n),o=r.isGatheringData,a=r.isDataAvailableOnLoad;return o(),a()}}},398:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r=n(0),i=n(3),o=n(7),a=n(19),c=n(32);function s(e){var t=Object(i.useSelect)((function(e){return e(o.a).hasCapability(o.K)})),n=Object(i.useSelect)((function(t){return t(a.a).getModuleStoreName(e)})),s=Object(i.useSelect)((function(e){var t;return null===(t=e(n))||void 0===t?void 0:t.getAdminReauthURL()})),l=Object(i.useDispatch)(c.a).navigateTo,u=Object(r.useCallback)((function(){return l(s)}),[s,l]);return s&&t?u:null}},401:function(e,t){e.exports=googlesitekit.widgets},402:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),o=n(0);function Sparkline(t){var n=t.sparkline,r=t.invertChangeColor,i=n;return i&&r&&(i=Object(o.cloneElement)(n,{invertChangeColor:r})),e.createElement("div",{className:"googlesitekit-data-block__sparkline"},i)}Sparkline.propTypes={sparkline:i.a.element,invertChangeColor:i.a.bool},t.a=Sparkline}).call(this,n(4))},403:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),o=n(11),a=n.n(o),c=n(2),s=n(9),l=n(85);function Change(t){var n=t.change,r=t.changeDataUnit,i=t.period,o=t.invertChangeColor,u=n;return r&&(u="%"===r?Object(s.B)(n,{style:"percent",signDisplay:"never",maximumFractionDigits:1}):Object(s.B)(n,r)),i&&(u=Object(c.sprintf)(i,u)),e.createElement("div",{className:a()("googlesitekit-data-block__change",{"googlesitekit-data-block__change--no-change":!n})},!!n&&e.createElement("span",{className:"googlesitekit-data-block__arrow"},e.createElement(l.a,{direction:0<parseFloat(n)?"up":"down",invertColor:o})),e.createElement("span",{className:"googlesitekit-data-block__value"},u))}Change.propTypes={change:i.a.oneOfType([i.a.string,i.a.number]),changeDataUnit:i.a.oneOfType([i.a.string,i.a.bool]),period:i.a.string,invertChangeColor:i.a.bool},t.a=Change}).call(this,n(4))},416:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PreviewGraph}));var r=n(1),i=n.n(r),o=n(577);function PreviewGraph(t){var n=t.title,r=t.GraphSVG,i=t.showIcons;return e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graph"},e.createElement("h3",{className:"googlesitekit-analytics-cta__preview-graph--title"},n),e.createElement("div",null,e.createElement(r,null)),i&&e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graph--icons"},e.createElement(o.a,{className:"googlesitekit-analytics-cta__preview-graph--up-arrow"}),e.createElement("span",{className:"googlesitekit-analytics-cta__preview-graph--bar"})))}PreviewGraph.propTypes={title:i.a.string.isRequired,GraphSVG:i.a.elementType.isRequired,showIcons:i.a.bool},PreviewGraph.defaultProps={showIcons:!0}}).call(this,n(4))},427:function(e,t,n){"use strict";n.d(t,"c",(function(){return r.a})),n.d(t,"d",(function(){return ZeroDataMessage})),n.d(t,"a",(function(){return l.a})),n.d(t,"b",(function(){return u.a}));var r=n(924),i=n(1),o=n.n(i),a=n(2),c=n(3),s=n(13);function ZeroDataMessage(e){var t=e.skipPrefix,n=Object(c.useSelect)((function(e){return e(s.c).getCurrentEntityURL()}));return t?n?Object(a.__)("Your page hasn’t appeared in Search yet","google-site-kit"):Object(a.__)("Your site hasn’t appeared in Search yet","google-site-kit"):n?Object(a.__)("No data to display: your page hasn’t appeared in Search yet","google-site-kit"):Object(a.__)("No data to display: your site hasn’t appeared in Search yet","google-site-kit")}ZeroDataMessage.propTypes={skipPrefix:o.a.bool};var l=n(925),u=n(926)},434:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeMetricsLink}));var r=n(0),i=n(2),o=n(3),a=n(23),c=n(7),s=n(26),l=n(20),u=n(310),g=n(449),d=n(9),f=n(18),p=n(511);function ChangeMetricsLink(){var t=Object(o.useSelect)((function(e){return e(c.a).getKeyMetrics()})),n=Object(f.a)(),m=Object(o.useDispatch)(a.b).setValue,b=Object(r.useCallback)((function(){m(s.k,!0),Object(d.I)("".concat(n,"_kmw"),"change_metrics")}),[m,n]),v=Array.isArray(t)&&(null==t?void 0:t.length)>0;return Object(p.a)(v),v?e.createElement(r.Fragment,null,e.createElement(l.a,{secondary:!0,linkButton:!0,className:"googlesitekit-widget-area__cta-link googlesitekit-km-change-metrics-cta",onClick:b,leadingIcon:e.createElement(u.a,{width:22,height:22})},Object(i.__)("Change metrics","google-site-kit")),e.createElement(g.a,null)):null}}).call(this,n(4))},435:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InsufficientPermissionsError}));var r=n(1),i=n.n(r),o=n(0),a=n(38),c=n(2),s=n(3),l=n(13),u=n(20),g=n(303),d=n(9),f=n(18);function InsufficientPermissionsError(t){var n=t.moduleSlug,r=t.onRetry,i=t.infoTooltip,p=t.headerText,m=Object(f.a)(),b=Object(s.useSelect)((function(e){return e(l.c).getErrorTroubleshootingLinkURL({code:"".concat(n,"_insufficient_permissions")})}));Object(o.useEffect)((function(){Object(d.J)("".concat(m,"_kmw"),"insufficient_permissions_error")}),[m]);var v=Object(o.useCallback)((function(){Object(d.I)("".concat(m,"_kmw"),"insufficient_permissions_error_retry"),null==r||r()}),[r,m]);return e.createElement(g.a,{title:Object(c.__)("Insufficient permissions","google-site-kit"),headerText:p,infoTooltip:i},e.createElement("div",{className:"googlesitekit-report-error-actions"},e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(a.a)(Object(c.__)("Permissions updated? <a>Retry</a>","google-site-kit"),{a:e.createElement(u.a,{onClick:v})})),e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(a.a)(Object(c.__)("You’ll need to contact your administrator. <a>Learn more</a>","google-site-kit"),{a:e.createElement(u.a,{href:b,external:!0,hideExternalIndicator:!0})}))))}InsufficientPermissionsError.propTypes={moduleSlug:i.a.string.isRequired,onRetry:i.a.func.isRequired,headerText:i.a.string,infoTooltip:i.a.string}}).call(this,n(4))},436:function(e,t,n){"use strict";(function(e){var r=n(5),i=n.n(r),o=n(16),a=n.n(o),c=n(1),s=n.n(c),l=n(2),u=n(0),g=n(3),d=n(10),f=n(379),p=n(380),m=n(7),b=n(13),v=n(26),h=n(50),y=n(175),O=n(9),_=n(18),k=n(397),j=n(454),E=n(32);function KeyMetricsSetupCTAWidget(t){var n=t.Widget,r=t.WidgetNull,o=Object(_.a)(),c=Object(k.a)(),s=Object(g.useSelect)((function(e){return e(b.c).getAdminURL("googlesitekit-user-input")})),h=Object(g.useSelect)((function(e){return e(b.c).getAdminURL("googlesitekit-metric-selection")})),S={tooltipSlug:v.l,title:Object(l.__)("You can always set up goals in Settings later","google-site-kit"),content:Object(l.__)("The Key Metrics section will be added back to your dashboard once you set your goals in Settings","google-site-kit"),dismissLabel:Object(l.__)("Got it","google-site-kit")},w=Object(y.b)(S),A=Object(g.useDispatch)(m.a).dismissItem,T=function(){var e=a()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(O.I)("".concat(o,"_kmw-cta-notification"),"dismiss_notification");case 2:return w(),e.next=5,A(v.l);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),C=Object(g.useDispatch)(E.a).navigateTo,D=Object(u.useCallback)(a()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(O.I)("".concat(o,"_kmw-cta-notification"),"confirm_pick_own_metrics");case 2:C(h);case 3:case"end":return e.stop()}}),e)}))),[C,h,o]),R=Object(u.useCallback)((function(){Object(O.I)("".concat(o,"_kmw-cta-notification"),"confirm_get_tailored_metrics")}),[o]);return c?e.createElement(n,{noPadding:!0,Footer:function Footer(){return e.createElement(p.a,{onActionClick:D})}},e.createElement(f.a,{title:Object(l.__)("Get personalized suggestions for user interaction metrics based on your goals","google-site-kit"),description:Object(l.__)("Answer 3 questions and we’ll suggest relevant metrics for your dashboard. These metrics will help you track how users interact with your site.","google-site-kit"),actions:e.createElement(u.Fragment,null,e.createElement(j.a,null),e.createElement(d.Button,{className:"googlesitekit-key-metrics-cta-button",href:s,onClick:R},Object(l.__)("Get tailored metrics","google-site-kit")),e.createElement(d.Button,{tertiary:!0,onClick:T},Object(l.__)("Maybe later","google-site-kit"))),ga4Connected:!0})):e.createElement(r,null)}KeyMetricsSetupCTAWidget.propTypes={Widget:s.a.elementType.isRequired,WidgetNull:s.a.elementType},t.a=Object(h.a)({moduleName:"analytics-4"})(KeyMetricsSetupCTAWidget)}).call(this,n(4))},437:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileNumeric}));var r=n(21),i=n.n(r),o=n(25),a=n.n(o),c=n(1),s=n.n(c),l=n(9),u=n(184),g=n(202);function MetricTileNumeric(t){var n=t.metricValue,r=t.metricValueFormat,o=t.subText,c=t.previousValue,s=t.currentValue,d=a()(t,["metricValue","metricValueFormat","subText","previousValue","currentValue"]),f=Object(l.m)(r);return e.createElement(g.a,i()({className:"googlesitekit-km-widget-tile--numeric"},d),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-container"},e.createElement("div",{className:"googlesitekit-km-widget-tile__metric"},Object(l.B)(n,f)),e.createElement("p",{className:"googlesitekit-km-widget-tile__subtext"},o)),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-change-container"},e.createElement(u.a,{previousValue:c,currentValue:s,isAbsolute:"percent"===(null==f?void 0:f.style)})))}MetricTileNumeric.propTypes={metricValue:s.a.oneOfType([s.a.string,s.a.number]),metricValueFormat:s.a.oneOfType([s.a.string,s.a.object]),subtext:s.a.string,previousValue:s.a.number,currentValue:s.a.number}}).call(this,n(4))},438:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileTable}));var r=n(21),i=n.n(r),o=n(25),a=n.n(o),c=n(1),s=n.n(c),l=n(14),u=n(11),g=n.n(u),d=n(202);function MetricTileTable(t){var n=t.rows,r=void 0===n?[]:n,o=t.columns,c=void 0===o?[]:o,s=t.limit,u=t.ZeroState,f=a()(t,["rows","columns","limit","ZeroState"]),p=null;return(null==r?void 0:r.length)>0?p=r.slice(0,s||r.length).map((function(t,n){return e.createElement("div",{key:n,className:"googlesitekit-table__body-row"},c.map((function(n,r){var i=n.Component,o=n.field,a=n.className,c=void 0!==o?Object(l.get)(t,o):void 0;return e.createElement("div",{key:r,className:g()("googlesitekit-table__body-item",a)},i&&e.createElement(i,{row:t,fieldValue:c}),!i&&c)})))})):u&&(p=e.createElement("div",{className:"googlesitekit-table__body-row googlesitekit-table__body-row--no-data"},e.createElement("div",{className:"googlesitekit-table__body-zero-data"},e.createElement(u,null)))),e.createElement(d.a,i()({className:"googlesitekit-km-widget-tile--table"},f),e.createElement("div",{className:"googlesitekit-km-widget-tile__table"},p))}MetricTileTable.propTypes={rows:s.a.array,columns:s.a.array,limit:s.a.number,ZeroState:s.a.elementType}}).call(this,n(4))},439:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileTablePlainText}));var r=n(1),i=n.n(r);function MetricTileTablePlainText(t){var n=t.content;return e.createElement("p",{className:"googlesitekit-km-widget-tile__table-plain-text"},n)}MetricTileTablePlainText.propTypes={content:i.a.string.isRequired}}).call(this,n(4))},44:function(e,t,n){"use strict";(function(e){var r=n(6),i=n.n(r),o=n(1),a=n.n(o),c=n(11),s=n.n(c),l=n(24);function PreviewBlock(t){var n,r,o=t.className,a=t.width,c=t.height,u=t.shape,g=t.padding,d=t.smallWidth,f=t.smallHeight,p=t.tabletWidth,m=t.tabletHeight,b=t.desktopWidth,v=t.desktopHeight,h=Object(l.e)(),y={width:(n={},i()(n,l.b,d),i()(n,l.c,p),i()(n,l.a,b),i()(n,l.d,b),n),height:(r={},i()(r,l.b,f),i()(r,l.c,m),i()(r,l.a,v),i()(r,l.d,b),r)};return e.createElement("div",{className:s()("googlesitekit-preview-block",o,{"googlesitekit-preview-block--padding":g}),style:{width:y.width[h]||a,height:y.height[h]||c}},e.createElement("div",{className:s()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:a.a.string,width:a.a.string,height:a.a.string,shape:a.a.string,padding:a.a.bool,smallWidth:a.a.string,smallHeight:a.a.string,tabletWidth:a.a.string,tabletHeight:a.a.string,desktopWidth:a.a.string,desktopHeight:a.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(4))},447:function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var r=n(27),i=n.n(r),o=n(11),a=n.n(o),c=n(14),s=n(2),l=n(83),u=n(9),g=n(15),d=n.n(g),f=n(12),p=n.n(f);function m(e,t){var n=t.dateRangeLength;p()(Array.isArray(e),"report must be an array to partition."),p()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=function(t){return e.filter((function(e){return d()(e.dimensionValues,2)[1].value===t}))},i=-1*n;return{currentRange:r("date_range_0").slice(i),compareRange:r("date_range_1").slice(2*i,i)}}var b=n(373);function v(e,t){var n=[];return e.forEach((function(e){if(e.metricValues){var r=e.metricValues[t].value,i=e.dimensionValues[0].value,o=Object(b.a)(i);n.push([o,r])}})),n}function h(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[Object(s.__)("Users","google-site-kit"),Object(s.__)("Sessions","google-site-kit"),Object(s.__)("Engagement Rate","google-site-kit"),Object(s.__)("Session Duration","google-site-kit")],g=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[function(e){return parseFloat(e).toLocaleString()},function(e){return parseFloat(e).toLocaleString()},function(e){return Object(u.B)(e/100,{style:"percent",signDisplay:"never",maximumFractionDigits:2})},function(e){return Object(u.B)(e,"s")}],d=arguments.length>6&&void 0!==arguments[6]?arguments[6]:[c.identity,c.identity,function(e){return 100*e},c.identity],f=i()((null==e?void 0:e.rows)||[]),p=f.length;if(2*n>p){for(var b=Object(u.G)(r),h=0;n>h;h++){var y=(b.getMonth()+1).toString(),O=b.getDate().toString(),_=b.getFullYear().toString()+(2>y.length?"0":"")+y+(2>O.length?"0":"")+O;if(h>p){var k=[{dimensionValues:[{value:_},{value:"date_range_0"}],metricValues:[{value:0},{value:0}]},{dimensionValues:[{value:_},{value:"date_range_1"}],metricValues:[{value:0},{value:0}]}];f.unshift.apply(f,k)}b.setDate(b.getDate()-1)}f.push({dimensionValues:[{value:"0"},{value:"date_range_0"}]},{dimensionValues:[{value:"0"},{value:"date_range_1"}]})}var j=o[t]===Object(s.__)("Session Duration","google-site-kit"),E=j?"timeofday":"number",S=[[{type:"date",label:Object(s.__)("Day","google-site-kit")},{type:"string",role:"tooltip",p:{html:!0}},{type:E,label:o[t]},{type:E,label:Object(s.__)("Previous period","google-site-kit")}]],w=m(f,{dateRangeLength:n}),A=w.compareRange,T=w.currentRange,C=v(T,t),D=v(A,t),R=Object(l.b)(),N={weekday:"short",month:"short",day:"numeric"};return C.forEach((function(e,n){if(e[0]&&e[1]&&D[n]){var r=d[t],i=r(e[1]),c=r(D[n][1]),l=parseFloat(c),f=Object(u.h)(i,l),p=Object(u.o)(f),m=Object(s.sprintf)(
/* translators: 1: date for user stats, 2: previous date for user stats comparison */
Object(s._x)("%1$s vs %2$s","Date range for chart tooltip","google-site-kit"),e[0].toLocaleDateString(R,N),D[n][0].toLocaleDateString(R,N)),b=Object(s.sprintf)(
/* translators: 1: selected stat label, 2: numeric value of selected stat, 3: up or down arrow , 4: different change in percentage */
Object(s._x)("%1$s: <strong>%2$s</strong> <em>%3$s %4$s</em>","Stat information for chart tooltip","google-site-kit"),o[t],g[t](i),p,Object(u.B)(Math.abs(f),"%"));S.push([e[0],'<div class="'.concat(a()("googlesitekit-visualization-tooltip",{"googlesitekit-visualization-tooltip--up":f>0,"googlesitekit-visualization-tooltip--down":f<0}),'">\n\t\t\t\t<p>').concat(m,"</p>\n\t\t\t\t<p>").concat(b,"</p>\n\t\t\t</div>"),j?Object(u.j)(i):i,j?Object(u.j)(c):c])}})),S}},449:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupCompletedSurveyTrigger}));var r=n(0),i=n(3),o=n(13),a=n(7),c=n(9),s=n(251);function SetupCompletedSurveyTrigger(){var t=Object(i.useSelect)((function(e){return e(o.c).isKeyMetricsSetupCompleted()})),n=Object(i.useSelect)((function(e){return e(o.c).getKeyMetricsSetupCompletedBy()})),l=Object(i.useSelect)((function(e){return e(a.a).getID()}));return t?e.createElement(r.Fragment,null,e.createElement(s.a,{triggerID:"view_kmw",ttl:c.f}),n===l&&e.createElement(s.a,{triggerID:"view_kmw_setup_completed",ttl:c.f})):null}}).call(this,n(4))},45:function(e,t){e.exports=googlesitekit.api},450:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsSetupDesktopSVG}));var r=n(0),i=n(2),o=n(44),a=n(151),c=Object(r.lazy)((function(){return n.e(38).then(n.bind(null,814))}));function KeyMetricsSetupDesktopSVG(){return e.createElement(r.Suspense,{fallback:e.createElement(o.a,{width:"100%",height:"235px"})},e.createElement(a.a,{errorMessage:Object(i.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},451:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsSetupSmallDesktopSVG}));var r=n(0),i=n(2),o=n(44),a=n(151),c=Object(r.lazy)((function(){return n.e(40).then(n.bind(null,815))}));function KeyMetricsSetupSmallDesktopSVG(){return e.createElement(r.Suspense,{fallback:e.createElement(o.a,{width:"100%",height:"235px"})},e.createElement(a.a,{errorMessage:Object(i.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},452:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsSetupTabletSVG}));var r=n(0),i=n(2),o=n(44),a=n(151),c=Object(r.lazy)((function(){return n.e(41).then(n.bind(null,816))}));function KeyMetricsSetupTabletSVG(){return e.createElement(r.Suspense,{fallback:e.createElement(o.a,{width:"100%",height:"235px"})},e.createElement(a.a,{errorMessage:Object(i.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},453:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsSetupMobileSVG}));var r=n(0),i=n(2),o=n(44),a=n(151),c=Object(r.lazy)((function(){return n.e(39).then(n.bind(null,817))}));function KeyMetricsSetupMobileSVG(){return e.createElement(r.Suspense,{fallback:e.createElement(o.a,{width:"100%",height:"235px"})},e.createElement(a.a,{errorMessage:Object(i.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},454:function(e,t,n){"use strict";n.d(t,"a",(function(){return KeyMetricsSetupCTARenderedEffect}));var r=n(265),i=n(3),o=n(23);function KeyMetricsSetupCTARenderedEffect(){var e=Object(i.useDispatch)(o.b).setValue;return Object(r.a)((function(){e("KEY_METRICS_SETUP_CTA_RENDERED",!0)})),null}},455:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GetHelpLink}));var r=n(1),i=n.n(r),o=n(38),a=n(2),c=n(20);function GetHelpLink(t){var n=t.linkURL;return Object(o.a)(
/* translators: %s: get help text. */
Object(a.__)("Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(c.a,{href:n,external:!0,hideExternalIndicator:!0},Object(a.__)("Get help","google-site-kit"))})}GetHelpLink.propTypes={linkURL:i.a.string.isRequired}}).call(this,n(4))},456:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileLoader}));var r=n(44);function MetricTileLoader(){return e.createElement("div",{className:"googlesitekit-km-widget-tile__loading"},e.createElement(r.a,{className:"googlesitekit-km-widget-tile__loading-header",width:"100%",height:"14px"}),e.createElement(r.a,{className:"googlesitekit-km-widget-tile__loading-body",width:"100%",height:"53px"}))}}).call(this,n(4))},47:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return o}));var r={BOXES:"boxes",COMPOSITE:"composite"},i={QUARTER:"quarter",HALF:"half",FULL:"full"},o="core/widgets"},48:function(e,t,n){"use strict";n.d(t,"a",(function(){return O}));var r=n(5),i=n.n(r),o=n(6),a=n.n(o),c=n(12),s=n.n(c),l=n(14),u=n(64),g=n(82),d=n(9);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var m=function(e){return e},b=function(){return{}},v=function(){},h=u.a.clearError,y=u.a.receiveError,O=function(e){var t,n,r=i.a.mark(L),o=e.baseName,c=e.controlCallback,u=e.reducerCallback,f=void 0===u?m:u,O=e.argsToParams,_=void 0===O?b:O,k=e.validateParams,j=void 0===k?v:k;s()(o,"baseName is required."),s()("function"==typeof c,"controlCallback is required and must be a function."),s()("function"==typeof f,"reducerCallback must be a function."),s()("function"==typeof _,"argsToParams must be a function."),s()("function"==typeof j,"validateParams must be a function.");try{j(_()),n=!1}catch(e){n=!0}var E=Object(g.b)(o),S=Object(g.a)(o),w="FETCH_".concat(S),A="START_".concat(w),T="FINISH_".concat(w),C="CATCH_".concat(w),D="RECEIVE_".concat(S),R="fetch".concat(E),N="receive".concat(E),P="isFetching".concat(E),x=a()({},P,{});function L(e,t){var n,a;return i.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,{payload:{params:e},type:A};case 2:return r.next=4,h(o,t);case 4:return r.prev=4,r.next=7,{payload:{params:e},type:w};case 7:return n=r.sent,r.next=10,I[N](n,e);case 10:return r.next=12,{payload:{params:e},type:T};case 12:r.next=21;break;case 14:return r.prev=14,r.t0=r.catch(4),a=r.t0,r.next=19,y(a,o,t);case 19:return r.next=21,{payload:{params:e},type:C};case 21:return r.abrupt("return",{response:n,error:a});case 22:case"end":return r.stop()}}),r,null,[[4,14]])}var I=(t={},a()(t,R,(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=_.apply(void 0,t);return j(r),L(r,t)})),a()(t,N,(function(e,t){return s()(void 0!==e,"response is required."),n?(s()(Object(l.isPlainObject)(t),"params is required."),j(t)):t={},{payload:{response:e,params:t},type:D}})),t),M=a()({},w,(function(e){var t=e.payload;return c(t.params)})),G=a()({},P,(function(e){if(void 0===e[P])return!1;var t;try{for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];t=_.apply(void 0,r),j(t)}catch(e){return!1}return!!e[P][Object(d.H)(t)]}));return{initialState:x,actions:I,controls:M,reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case A:var i=r.params;return p(p({},e),{},a()({},P,p(p({},e[P]),{},a()({},Object(d.H)(i),!0))));case D:var o=r.response,c=r.params;return f(e,o,c);case T:var s=r.params;return p(p({},e),{},a()({},P,p(p({},e[P]),{},a()({},Object(d.H)(s),!1))));case C:var l=r.params;return p(p({},e),{},a()({},P,p(p({},e[P]),{},a()({},Object(d.H)(l),!1))));default:return e}},resolvers:{},selectors:G}}},50:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a}));var r=n(3),i=n(19),o=n(82);function a(t){var n=t.moduleName,a=t.FallbackComponent,c=t.IncompleteComponent;return function(t){function WhenActiveComponent(o){var s=Object(r.useSelect)((function(e){return e(i.a).getModule(n)}),[n]);if(!s)return null;var l=a||o.WidgetNull||null;if(!1===s.active)return l&&e.createElement(l,o);if(!1===s.connected){var u=c||l;return u&&e.createElement(u,o)}return e.createElement(t,o)}return WhenActiveComponent.displayName="When".concat(Object(o.c)(n),"Active"),(t.displayName||t.name)&&(WhenActiveComponent.displayName+="(".concat(t.displayName||t.name,")")),WhenActiveComponent}}}).call(this,n(4))},503:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),o=n(11),a=n.n(o),c=n(44);function PreviewTable(t){for(var n=t.rows,r=t.rowHeight,i=t.padding,o=[],s=0;n>s;s++)o.push(e.createElement("div",{className:"googlesitekit-preview-table__row",key:"table-row-"+s},e.createElement(c.a,{width:"100%",height:r+"px"})));return e.createElement("div",{className:a()("googlesitekit-preview-table",{"googlesitekit-preview-table--padding":i})},o)}PreviewTable.propTypes={rows:i.a.number,rowHeight:i.a.number,padding:i.a.bool},PreviewTable.defaultProps={rows:11,rowHeight:35,padding:!1},t.a=PreviewTable}).call(this,n(4))},504:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportTable}));var r=n(15),i=n.n(r),o=n(11),a=n.n(o),c=n(12),s=n.n(c),l=n(1),u=n.n(l),g=n(14),d=n(0),f=n(10),p=n(114);function ReportTable(t){var n=t.rows,r=t.columns,o=t.className,c=t.limit,l=t.zeroState,u=t.gatheringData,m=void 0!==u&&u,b=t.tabbedLayout,v=void 0!==b&&b;function h(e){return!v&&e}s()(Array.isArray(n),"rows must be an array."),s()(Array.isArray(r),"columns must be an array."),r.forEach((function(e){var t=e.Component,n=e.field,r=void 0===n?null:n;s()(t||null!==r,"each column must define a Component and/or a field.")})),s()(Number.isInteger(c)||void 0===c,"limit must be an integer, if provided.");var y=r.some((function(e){return!!e.badge})),O=Object(d.useState)(0),_=i()(O,2),k=_[0],j=_[1],E=v&&r.slice(1),S=v?[r[0],E[k]]:r,w=S.filter((function(e){return!h(e.hideOnMobile)}));return e.createElement("div",{className:o},v&&e.createElement(f.TabBar,{className:"googlesitekit-tab-bar--start-aligned-high-contrast",activeIndex:k,handleActiveIndexUpdate:j},E.map((function(t){var n=t.title,r=t.badge;return e.createElement(f.Tab,{key:n,"aria-label":n},n,r)}))),e.createElement("div",{className:a()("googlesitekit-table","googlesitekit-table--with-list",{"googlesitekit-table--gathering-data":m})},e.createElement("table",{className:a()("googlesitekit-table__wrapper","googlesitekit-table__wrapper--".concat(S.length,"-col"),"googlesitekit-table__wrapper--mobile-".concat(w.length,"-col"),{"googlesitekit-table__wrapper--tabbed-layout":v})},!v&&e.createElement("thead",{className:"googlesitekit-table__head"},y&&e.createElement("tr",{className:a()("googlesitekit-table__head-badges",{"hidden-on-mobile":!r.some((function(e){var t=e.badge,n=e.hideOnMobile;return!!t&&!h(n)}))})},r.map((function(t,n){var r=t.badge,i=t.primary,o=t.hideOnMobile,c=t.className;return e.createElement("th",{className:a()("googlesitekit-table__head-item","googlesitekit-table__head-item--badge",{"googlesitekit-table__head-item--primary":i,"hidden-on-mobile":h(o)},c),key:"googlesitekit-table__head-row-badge-".concat(n)},r)}))),e.createElement("tr",{className:"googlesitekit-table__head-row"},r.map((function(t,n){var r=t.title,i=t.description,o=t.primary,c=t.hideOnMobile,s=t.className;return e.createElement("th",{className:a()("googlesitekit-table__head-item",{"googlesitekit-table__head-item--primary":o,"hidden-on-mobile":h(c)},s),"data-tooltip":i,key:"googlesitekit-table__head-row-".concat(n)},r)})))),e.createElement("tbody",{className:"googlesitekit-table__body"},m&&e.createElement("tr",{className:"googlesitekit-table__body-row googlesitekit-table__body-row--no-data"},e.createElement("td",{className:"googlesitekit-table__body-item",colSpan:S.length},e.createElement(p.b,null))),!m&&!(null==n?void 0:n.length)&&l&&e.createElement("tr",{className:"googlesitekit-table__body-row googlesitekit-table__body-row--no-data"},e.createElement("td",{className:"googlesitekit-table__body-item",colSpan:S.length},e.createElement(l,null))),!m&&n.slice(0,c).map((function(t,n){return e.createElement("tr",{className:"googlesitekit-table__body-row",key:"googlesitekit-table__body-row-".concat(n)},S.map((function(n,r){var i=n.Component,o=n.field,c=n.hideOnMobile,s=n.className,l=void 0!==o?Object(g.get)(t,o):void 0;return e.createElement("td",{key:"googlesitekit-table__body-item-".concat(r),className:a()("googlesitekit-table__body-item",{"hidden-on-mobile":h(c)},s)},e.createElement("div",{className:"googlesitekit-table__body-item-content"},i&&e.createElement(i,{row:t,fieldValue:l}),!i&&l))})))}))))))}ReportTable.propTypes={rows:u.a.arrayOf(u.a.oneOfType([u.a.array,u.a.object])).isRequired,columns:u.a.arrayOf(u.a.shape({title:u.a.string,description:u.a.string,primary:u.a.bool,className:u.a.string,field:u.a.string,hideOnMobile:u.a.bool,Component:u.a.componentType,badge:u.a.node})).isRequired,className:u.a.string,limit:u.a.number,zeroState:u.a.func,gatheringData:u.a.bool,tabbedLayout:u.a.bool}}).call(this,n(4))},505:function(e,t,n){"use strict";(function(e,r){var i=n(15),o=n.n(i),a=n(1),c=n.n(a),s=n(14),l=n(11),u=n.n(l),g=n(0);function TableOverflowContainer(t){var n=t.children,i=Object(g.useState)(!1),a=o()(i,2),c=a[0],l=a[1],d=Object(g.useRef)();Object(g.useEffect)((function(){f();var t=Object(s.debounce)(f,100);return e.addEventListener("resize",t),function(){return e.removeEventListener("resize",t)}}),[]);var f=function(){if(d.current){var e=d.current,t=e.scrollLeft,n=e.scrollWidth-e.offsetWidth;l(t<n-16&&0<n-16)}};return r.createElement("div",{onScroll:Object(s.debounce)(f,100),className:u()("googlesitekit-table-overflow",{"googlesitekit-table-overflow--gradient":c})},r.createElement("div",{ref:d,className:"googlesitekit-table-overflow__container"},n))}TableOverflowContainer.propTypes={children:c.a.element},t.a=TableOverflowContainer}).call(this,n(28),n(4))},507:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return DataBlockGroup}));var i=n(81),o=n(420),a=n(0),c=n(121);function DataBlockGroup(t){var n=t.className,s=t.children,l=Object(a.useRef)(),u=function(){var t,n,r,i,o=null==l||null===(t=l.current)||void 0===t?void 0:t.querySelectorAll(".googlesitekit-data-block");if(o){var a=null===(n=o[0])||void 0===n?void 0:n.querySelector(".googlesitekit-data-block__datapoint");if(a){g(o,"");var c=parseInt(null===(r=e)||void 0===r||null===(i=r.getComputedStyle(a))||void 0===i?void 0:i.fontSize,10),s=c;o.forEach((function(t){var n,r,i,o=t.querySelector(".googlesitekit-data-block__datapoint");if(o){var a=parseInt(null===(n=e)||void 0===n||null===(r=n.getComputedStyle(o))||void 0===r?void 0:r.fontSize,10),c=null==o||null===(i=o.parentElement)||void 0===i?void 0:i.offsetWidth;if(o.scrollWidth>c&&a>14){for(;o.scrollWidth>c&&a>14;)a-=1,o.style.fontSize="".concat(a,"px");s=a}}})),c!==s&&g(o,"".concat(s,"px"))}}},g=function(e,t){e.forEach((function(e){var n=null==e?void 0:e.querySelector(".googlesitekit-data-block__datapoint");n&&(n.style.fontSize=t)}))},d=Object(c.a)(u,50);return Object(i.a)((function(){u(),e.addEventListener("resize",d)})),Object(o.a)((function(){return e.removeEventListener("resize",d)})),r.createElement("div",{ref:l,className:n},s)}}).call(this,n(28),n(4))},508:function(e,t,n){"use strict";(function(e,r){var i=n(51),o=n.n(i),a=n(53),c=n.n(a),s=n(237),l=n.n(s),u=n(68),g=n.n(u),d=n(69),f=n.n(d),p=n(49),m=n.n(p),b=n(195),v=n.n(b),h=n(1),y=n.n(h),O=n(0),_=n(2),k=n(95),j=n(172),E=n(61),S=n(9);function w(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=m()(e);if(t){var i=m()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return f()(this,n)}}var A=function(t){g()(GoogleChartErrorHandler,t);var n=w(GoogleChartErrorHandler);function GoogleChartErrorHandler(e){var t;return o()(this,GoogleChartErrorHandler),(t=n.call(this,e)).state={error:null,info:null},t.onErrorClick=t.onErrorClick.bind(l()(t)),t}return c()(GoogleChartErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Google Charts error:",t,n),this.setState({error:t,info:n}),Object(S.I)("google_chart_error","handle_".concat(this.context||"unknown","_error"),"".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500))}},{key:"onErrorClick",value:function(){var e=this.state,t=e.error,n=e.info;v()("`".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack,"`"))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,i=t.info;return n?r.createElement("div",{className:"googlesitekit-googlechart-error-handler"},r.createElement(k.a,{description:r.createElement(O.Fragment,null,r.createElement("p",null,Object(_.__)("An error prevented this Google chart from being displayed properly. Report the exact contents of the error on the support forum to find out what caused it.","google-site-kit")),r.createElement(j.a,{message:n.message,componentStack:i.componentStack})),error:!0,onErrorClick:this.onErrorClick,onClick:this.onErrorClick,title:Object(_.__)("Error in Google Chart","google-site-kit")})):e}}]),GoogleChartErrorHandler}(O.Component);A.contextType=E.b,A.propTypes={children:y.a.node.isRequired},t.a=A}).call(this,n(28),n(4))},509:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DateMarker}));var r=n(0),i=n(266),o=n(589),a=n(10),c=n(18),s=n(121),l=n(9);function DateMarker(t){var n=t.id,u=t.text,g=Object(c.a)(),d="".concat(g,"_ga4-data-collection-line");Object(r.useEffect)((function(){Object(l.I)(d,"chart_line_view")}),[d]);var f=Object(r.useCallback)((function(){Object(l.I)(d,"chart_tooltip_view")}),[d]),p=Object(s.a)(f,5e3,{leading:!0,trailing:!1});return e.createElement(r.Fragment,null,e.createElement("div",{id:"googlesitekit-chart__date-marker-line--".concat(n),className:"googlesitekit-chart__date-marker-line"}),u&&e.createElement("div",{id:"googlesitekit-chart__date-marker-tooltip--".concat(n),className:"googlesitekit-chart__date-marker-tooltip"},e.createElement(a.Tooltip,{title:u,onOpen:p},e.createElement("span",null,e.createElement(i.a,{fill:"currentColor",icon:o.a,size:18})))))}}).call(this,n(4))},511:function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var r=n(0),i=n(3),o=n(13),a=n(7),c=n(2),s=n(22),l={slug:"sharedKeyMetrics",contexts:[s.n,s.o,s.l,s.m],gaEventCategory:function(e){return"".concat(e,"_shared_key-metrics")},steps:[{target:".googlesitekit-km-change-metrics-cta",title:Object(c.__)("Personalize your key metrics","google-site-kit"),content:Object(c.__)("Another admin has set up these tailored metrics for your site. Click here to personalize them.","google-site-kit"),placement:"bottom-start"}]},u=function(e){var t=Object(i.useSelect)((function(e){return e(o.c).getKeyMetricsSetupCompletedBy()})),n=Object(i.useSelect)((function(e){return e(a.a).getID()})),c=Object(i.useDispatch)(a.a).triggerOnDemandTour,s=Number.isInteger(t)&&Number.isInteger(n)&&t>0&&n!==t;Object(r.useEffect)((function(){e&&s&&c(l)}),[e,s,c])}},52:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return c}));var r=n(22),i=n(18),o=r.n,a=r.l;function c(){var e=Object(i.a)();return e===r.n||e===r.o?o:e===r.l||e===r.m?a:null}},523:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r);function WidgetHeaderTitle(t){var n=t.title;return e.createElement("h3",{className:"googlesitekit-subheading-1 googlesitekit-widget__header-title"},n)}WidgetHeaderTitle.propTypes={title:i.a.string.isRequired},t.a=WidgetHeaderTitle}).call(this,n(4))},54:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),o=n(106),a=n(2),c=n(9);function ErrorText(t){var n=t.message,r=t.reconnectURL,i=t.noPrefix;if(!n)return null;var s=n;void 0!==i&&i||(s=Object(a.sprintf)(
/* translators: %s: Error message */
Object(a.__)("Error: %s","google-site-kit"),n)),r&&Object(o.a)(r)&&(s=s+" "+Object(a.sprintf)(
/* translators: %s: Reconnect URL */
Object(a.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),r));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(s,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:i.a.string.isRequired,reconnectURL:i.a.string,noPrefix:i.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},57:function(e,t,n){"use strict";(function(e){var r,i;n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return a}));var o=new Set((null===(r=e)||void 0===r||null===(i=r._googlesitekitBaseData)||void 0===i?void 0:i.enabledFeatures)||[]),a=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o;return t instanceof Set&&t.has(e)}}).call(this,n(28))},576:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActivateAnalyticsCTA}));var r=n(15),i=n.n(r),o=n(1),a=n.n(o),c=n(0),s=n(2),l=n(10),u=n(3),g=n(19),d=n(8),f=n(32),p=n(159),m=n(398),b=n(121);function ActivateAnalyticsCTA(t){var n=t.children,r=Object(p.a)("analytics-4"),o=Object(m.a)("analytics-4"),a=Object(u.useSelect)((function(e){return e(g.a).isModuleActive("analytics-4")})),v=Object(u.useSelect)((function(e){return(0,e(g.a).isModuleAvailable)("analytics-4")&&!!e(d.r)})),h=Object(c.useState)(!1),y=i()(h,2),O=y[0],_=y[1],k=Object(u.useSelect)((function(e){if(!v)return!1;var t=e(d.r).getAdminReauthURL();return!!t&&e(f.a).isNavigatingTo(t)})),j=Object(u.useSelect)((function(e){return!!v&&e(g.a).isFetchingSetModuleActivation("analytics-4",!0)})),E=Object(b.a)(_,3e3);Object(c.useEffect)((function(){j||k?_(!0):E(!1)}),[j,k,E]);var S=a?o:r;return v&&S?e.createElement("div",{className:"googlesitekit-analytics-cta"},e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graphs"},n),e.createElement("div",{className:"googlesitekit-analytics-cta__details"},e.createElement("p",{className:"googlesitekit-analytics-cta--description"},Object(s.__)("See how many people visit your site from Search and track how you’re achieving your goals","google-site-kit")),e.createElement(l.SpinnerButton,{onClick:S,isSaving:O},a?Object(s.__)("Complete setup","google-site-kit"):Object(s.__)("Set up Google Analytics","google-site-kit")))):null}ActivateAnalyticsCTA.propTypes={children:a.a.node.isRequired}}).call(this,n(4))},577:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("path",{d:"M0 3.01l.443.387 1.755-1.534v3.344h.628V1.863L4.578 3.4l.446-.39L2.512.811 0 3.009z",fill:"currentColor"});t.a=function SvgArrowUp(e){return r.createElement("svg",i({viewBox:"0 0 6 6",fill:"none"},e),o)}},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(39);function i(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},594:function(e,t,n){(function(e){Object.prototype.hasOwnProperty.call(e,"google")||(e.google={})}).call(this,n(28))},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),i=Object(r.createContext)(""),o=(i.Consumer,i.Provider);t.b=i},62:function(e,t,n){"use strict";n.d(t,"a",(function(){return w})),n.d(t,"b",(function(){return A})),n.d(t,"c",(function(){return T})),n.d(t,"d",(function(){return D})),n.d(t,"e",(function(){return R})),n.d(t,"g",(function(){return P})),n.d(t,"f",(function(){return x}));var r,i=n(5),o=n.n(i),a=n(27),c=n.n(a),s=n(6),l=n.n(s),u=n(12),g=n.n(u),d=n(63),f=n.n(d),p=n(14),m=n(116);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.reduce((function(e,t){return v(v({},e),t)}),{}),i=t.reduce((function(e,t){return[].concat(c()(e),c()(Object.keys(t)))}),[]),o=C(i);return g()(0===o.length,"collect() cannot accept collections with duplicate keys. Your call to collect() contains the following duplicated functions: ".concat(o.join(", "),". Check your data stores for duplicates.")),r},y=h,O=h,_=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i=[].concat(t);return"function"!=typeof i[0]&&(r=i.shift()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i.reduce((function(e,n){return n(e,t)}),e)}},k=h,j=h,E=h,S=function(e){return e},w=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=E.apply(void 0,c()(t.map((function(e){return e.initialState||{}}))));return{initialState:r,controls:O.apply(void 0,c()(t.map((function(e){return e.controls||{}})))),actions:y.apply(void 0,c()(t.map((function(e){return e.actions||{}})))),reducer:_.apply(void 0,[r].concat(c()(t.map((function(e){return e.reducer||S}))))),resolvers:k.apply(void 0,c()(t.map((function(e){return e.resolvers||{}})))),selectors:j.apply(void 0,c()(t.map((function(e){return e.selectors||{}}))))}},A={getRegistry:function(){return{payload:{},type:"GET_REGISTRY"}},await:o.a.mark((function e(t){return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{payload:{value:t},type:"AWAIT"});case 1:case"end":return e.stop()}}),e)}))},T=(r={},l()(r,"GET_REGISTRY",Object(m.a)((function(e){return function(){return e}}))),l()(r,"AWAIT",(function(e){return e.payload.value})),r),C=function(e){for(var t=[],n={},r=0;r<e.length;r++){var i=e[r];n[i]=n[i]>=1?n[i]+1:1,n[i]>1&&t.push(i)}return t},D={actions:A,controls:T,reducer:S},R=function(e){return function(t){return N(e(t))}},N=f()((function(e){return Object(p.mapValues)(e,(function(e,t){return function(){var n=e.apply(void 0,arguments);return g()(void 0!==n,"".concat(t,"(...) is not resolved")),n}}))}));function P(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.negate,r=void 0!==n&&n,i=Object(m.b)((function(t){return function(n){var i=!r,o=!!r;try{for(var a=arguments.length,c=new Array(a>1?a-1:0),s=1;s<a;s++)c[s-1]=arguments[s];return e.apply(void 0,[t,n].concat(c)),i}catch(e){return o}}})),o=Object(m.b)((function(t){return function(n){for(var r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];e.apply(void 0,[t,n].concat(i))}}));return{safeSelector:i,dangerousSelector:o}}function x(e,t){return g()("function"==typeof e,"a validator function is required."),g()("function"==typeof t,"an action creator function is required."),g()("Generator"!==e[Symbol.toStringTag]&&"GeneratorFunction"!==e[Symbol.toStringTag],"an action’s validator function must not be a generator."),function(){return e.apply(void 0,arguments),t.apply(void 0,arguments)}}},64:function(e,t,n){"use strict";n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return v}));var r=n(6),i=n.n(r),o=n(33),a=n.n(o),c=n(116),s=n(12),l=n.n(s),u=n(96),g=n.n(u),d=n(9);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e,t){if(t&&Array.isArray(t)){var n=t.map((function(e){return"object"===a()(e)?Object(d.H)(e):e}));return"".concat(e,"::").concat(g()(JSON.stringify(n)))}return e}var b={receiveError:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(e,"error is required."),l()(t,"baseName is required."),l()(n&&Array.isArray(n),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:n}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return l()(e,"baseName is required."),l()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function v(e){l()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(n,"selectorName is required."),t.getError(e,n,r)},getErrorForAction:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(n,"actionName is required."),t.getError(e,n,r)},getError:function(e,t,n){var r=e.errors;return l()(t,"baseName is required."),r[m(t,n)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var n=Object.keys(e.errors).find((function(n){return e.errors[n]===t}));return n?{baseName:n.substring(0,n.indexOf("::")),args:e.errorArgs[n]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(n,r){var i=t(e).getMetaDataForError(r);if(i){var o=i.baseName,a=i.args;if(!!t(e)[o])return{storeName:e,name:o,args:a}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:b,controls:{},reducer:function(e,t){var n=t.type,r=t.payload;switch(n){case"RECEIVE_ERROR":var o=r.baseName,a=r.args,c=r.error,s=m(o,a);return p(p({},e),{},{errors:p(p({},e.errors||{}),{},i()({},s,c)),errorArgs:p(p({},e.errorArgs||{}),{},i()({},s,a))});case"CLEAR_ERROR":var l=r.baseName,u=r.args,g=p({},e),d=m(l,u);return g.errors=p({},e.errors||{}),g.errorArgs=p({},e.errorArgs||{}),delete g.errors[d],delete g.errorArgs[d],g;case"CLEAR_ERRORS":var f=r.baseName,b=p({},e);if(f)for(var v in b.errors=p({},e.errors||{}),b.errorArgs=p({},e.errorArgs||{}),b.errors)(v===f||v.startsWith("".concat(f,"::")))&&(delete b.errors[v],delete b.errorArgs[v]);else b.errors={},b.errorArgs={};return b;default:return e}},resolvers:{},selectors:t}}},65:function(e,t,n){"use strict";n.d(t,"c",(function(){return m})),n.d(t,"a",(function(){return b})),n.d(t,"b",(function(){return v})),n.d(t,"d",(function(){return y}));var r=n(6),i=n.n(r),o=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var c=o.createElement("path",{d:"M10 15.27L16.18 19l-1.64-7.03L20 7.24l-7.19-.61L10 0 7.19 6.63 0 7.24l5.46 4.73L3.82 19 10 15.27z"});var s=function SvgInfoIcon(e){return o.createElement("svg",a({viewBox:"0 0 20 20",fill:"currentColor"},e),c)};function l(){return(l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var u=o.createElement("path",{d:"M0 4h2v7H0zm0-4h2v2H0z",fill:"currentColor",fillRule:"evenodd"});var g,d=function SvgSuggestionIcon(e){return o.createElement("svg",l({viewBox:"0 0 2 11"},e),u)},f=n(186),p=n(74),m="warning",b="info",v="suggestion",h=(g={},i()(g,b,s),i()(g,m,f.a),i()(g,v,d),g),y=function(e){return h[e]||p.a}},66:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r="modules/search-console",i=1},677:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return EntityOwnershipChangeNotice}));var r=n(6),i=n.n(r),o=n(1),a=n.n(o),c=n(2),s=n(3),l=n(19),u=n(7),g=n(166),d=n(65),f=n(9);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function EntityOwnershipChangeNotice(t){var n=t.slug,r=Array.isArray(n)?n:[n],o=Object(s.useSelect)((function(e){var t=e(l.a),n=t.getModuleStoreName,o=t.getSharedRoles;return r.filter((function(e){var t;return!!(null===(t=o(e))||void 0===t?void 0:t.length)})).reduce((function(e,t){var r=n(t);return r?m(m({},e),{},i()({},t,r)):e}),{})})),a=Object(s.useSelect)((function(e){return Object.keys(o).reduce((function(t,n){var r,i,a=o[n],c=null===(r=e(a))||void 0===r?void 0:r.getOwnerID(),s=e(u.a).getID(),l=null===(i=e(a))||void 0===i?void 0:i.haveOwnedSettingsChanged();return l&&c!==s&&(t[n]=l),t}),{})})),p=Object.values(a).some((function(e){return e})),b=Object(s.useSelect)((function(e){return Object.keys(a).reduce((function(t,n){var r=e(l.a).getModule(n);return r&&t.push(r.name),t}),[])}));return p?e.createElement(g.a,{type:d.c,notice:Object(c.sprintf)(
/* translators: %s: module name. */
Object(c.__)("By clicking confirm changes, you’re granting other users view-only access to data from %s via your Google account. You can always manage this later in the dashboard sharing settings.","google-site-kit"),Object(f.y)(b))}):null}EntityOwnershipChangeNotice.propTypes={slug:a.a.oneOfType([a.a.string,a.a.arrayOf(a.a.string)]).isRequired}}).call(this,n(4))},679:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var r=n(5),i=n.n(r),o=n(6),a=n.n(o),c=n(12),s=n.n(c),l=n(45),u=n.n(l),g=n(3),d=n(48);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var m=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.storeName,r=t.dataAvailable,o=void 0!==r&&r,c=t.selectDataAvailability;s()("string"==typeof e&&e,"module slug is required."),s()("string"==typeof n&&n,"store name is required."),s()("boolean"==typeof o,"dataAvailable must be a boolean."),s()("function"==typeof c,"selectDataAvailability must be a function.");var l=Object(d.a)({baseName:"saveDataAvailableState",controlCallback:function(){return u.a.set("modules",e,"data-available")}}),f={dataAvailableOnLoad:o,gatheringData:void 0},m={receiveIsGatheringData:function(e){return s()("boolean"==typeof e,"gatheringData must be a boolean."),{payload:{gatheringData:e},type:"RECEIVE_GATHERING_DATA"}},receiveIsDataAvailableOnLoad:function(e){return s()("boolean"==typeof e,"dataAvailableOnLoad must be a boolean."),{payload:{dataAvailableOnLoad:e},type:"RECEIVE_DATA_AVAILABLE_ON_LOAD"}}},b=a()({},"WAIT_FOR_DATA_AVAILABILITY_STATE",Object(g.createRegistryControl)((function(e){return function(){var t=function(){return void 0!==e.select(n).selectDataAvailability()};return!!t()||new Promise((function(n){var r=e.subscribe((function(){t()&&(r(),n(!0))}))}))}}))),v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.payload;switch(n){case"RECEIVE_GATHERING_DATA":var i=r.gatheringData;return p(p({},e),{},{gatheringData:i});case"RECEIVE_DATA_AVAILABLE_ON_LOAD":var o=r.dataAvailableOnLoad;return p(p({},e),{},{dataAvailableOnLoad:o});default:return e}},h={isGatheringData:i.a.mark((function e(){var t,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,g.commonActions.getRegistry();case 2:if(void 0===(t=e.sent).select(n).isGatheringData()){e.next=5;break}return e.abrupt("return");case 5:if(!t.select(n).isDataAvailableOnLoad()){e.next=10;break}return e.next=9,m.receiveIsGatheringData(!1);case 9:return e.abrupt("return");case 10:return e.next=12,{payload:{},type:"WAIT_FOR_DATA_AVAILABILITY_STATE"};case 12:return r=t.select(n).selectDataAvailability(),e.next=15,m.receiveIsGatheringData(!r);case 15:if(!r){e.next=18;break}return e.next=18,l.actions.fetchSaveDataAvailableState();case 18:case"end":return e.stop()}}),e)}))},y={selectDataAvailability:c,isDataAvailableOnLoad:function(e){return e.dataAvailableOnLoad},isGatheringData:function(e){return e.gatheringData}};return Object(g.combineStores)(l,{actions:m,controls:b,initialState:f,reducer:v,resolvers:h,selectors:y})}},681:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("path",{d:"M1 24.053l5-4.825 4 2.193 5.333-2.193 7.334 6.579 6-1.754 3-4.825 4.666 6.579 3.334-1.754L47.333 28 55 19.228l4.333 2.193 5.334-3.509 2 1.316h6L81.333 3 84 9.579l2.333-1.754L89 13.088l12-5.263",stroke:"#CCC",strokeWidth:2});t.a=function SvgCtaGraphVisitors(e){return r.createElement("svg",i({viewBox:"0 0 102 30",fill:"none"},e),o)}},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return o})),n.d(t,"d",(function(){return a})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return l})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return g})),n.d(t,"L",(function(){return d})),n.d(t,"J",(function(){return f})),n.d(t,"I",(function(){return p})),n.d(t,"N",(function(){return m})),n.d(t,"f",(function(){return b})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return y})),n.d(t,"l",(function(){return O})),n.d(t,"m",(function(){return _})),n.d(t,"n",(function(){return k})),n.d(t,"o",(function(){return j})),n.d(t,"q",(function(){return E})),n.d(t,"s",(function(){return S})),n.d(t,"r",(function(){return w})),n.d(t,"t",(function(){return A})),n.d(t,"w",(function(){return T})),n.d(t,"u",(function(){return C})),n.d(t,"v",(function(){return D})),n.d(t,"x",(function(){return R})),n.d(t,"y",(function(){return N})),n.d(t,"A",(function(){return P})),n.d(t,"B",(function(){return x})),n.d(t,"C",(function(){return L})),n.d(t,"D",(function(){return I})),n.d(t,"k",(function(){return M})),n.d(t,"F",(function(){return G})),n.d(t,"z",(function(){return F})),n.d(t,"G",(function(){return B})),n.d(t,"E",(function(){return z})),n.d(t,"i",(function(){return V})),n.d(t,"p",(function(){return U})),n.d(t,"Q",(function(){return q})),n.d(t,"P",(function(){return W}));var r="core/user",i="connected_url_mismatch",o="__global",a="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",l="googlesitekit_setup",u="googlesitekit_view_dashboard",g="googlesitekit_manage_options",d="googlesitekit_read_shared_module_data",f="googlesitekit_manage_module_sharing_options",p="googlesitekit_delegate_module_sharing_management",m="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",y="kmAnalyticsNewVisitors",O="kmAnalyticsPopularAuthors",_="kmAnalyticsPopularContent",k="kmAnalyticsPopularProducts",j="kmAnalyticsReturningVisitors",E="kmAnalyticsTopCities",S="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",A="kmAnalyticsTopCitiesDrivingPurchases",T="kmAnalyticsTopDeviceDrivingPurchases",C="kmAnalyticsTopConvertingTrafficSource",D="kmAnalyticsTopCountries",R="kmAnalyticsTopPagesDrivingLeads",N="kmAnalyticsTopRecentTrendingPages",P="kmAnalyticsTopTrafficSource",x="kmAnalyticsTopTrafficSourceDrivingAddToCart",L="kmAnalyticsTopTrafficSourceDrivingLeads",I="kmAnalyticsTopTrafficSourceDrivingPurchases",M="kmAnalyticsPagesPerVisit",G="kmAnalyticsVisitLength",F="kmAnalyticsTopReturningVisitorPages",B="kmSearchConsolePopularKeywords",z="kmAnalyticsVisitsPerVisitor",V="kmAnalyticsMostEngagingPages",U="kmAnalyticsTopCategories",q=[b,v,h,y,O,_,k,j,U,E,S,w,A,T,C,D,N,P,x,M,G,F,z,V,U],W=[].concat(q,[B])},70:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),o)}},72:function(e,t,n){"use strict";var r=n(15),i=n.n(r),o=n(265),a=n(1),c=n.n(a),s=n(0),l=n(144);function Portal(e){var t=e.children,n=e.slug,r=Object(s.useState)(document.createElement("div")),a=i()(r,1)[0];return Object(o.a)((function(){n&&a.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(a),function(){return e.removeChild(a)}})),Object(l.createPortal)(t,a)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},74:function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return c}));var r=n(33),i=n.n(r),o=n(84),a=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:o.a.sanitize(e,t)}};function c(e){var t,n="object"===i()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},759:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsForm}));var r=n(38),i=n(2),o=n(3),a=n(66),c=n(19),s=n(427),l=n(141),u=n(677),g=n(166),d=n(142),f=n(186);function SettingsForm(t){var n,p=t.hasModuleAccess,m=Object(o.useSelect)((function(e){return e(c.a).getModule("search-console")})),b=(null==m||null===(n=m.owner)||void 0===n?void 0:n.login)?"<strong>".concat(m.owner.login,"</strong>"):Object(i.__)("Another admin","google-site-kit");return e.createElement("div",{className:"googlesitekit-search-console-settings-fields"},e.createElement(l.a,{moduleSlug:"search-console",storeName:a.b}),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(s.c,{hasModuleAccess:p})),!1===p&&e.createElement(g.a,{type:d.a,Icon:f.a,notice:Object(r.a)(Object(i.sprintf)(
/* translators: 1: module owner's name, 2: module name */
Object(i.__)("%1$s configured %2$s and you don’t have access to this Search Console property. Contact them to share access or change the Search Console property.","google-site-kit"),b,null==m?void 0:m.name),{strong:e.createElement("strong",null)})}),p&&e.createElement(u.a,{slug:"search-console"}))}}).call(this,n(4))},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(1),i=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,i=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:i}},n)}IconWrapper.propTypes={children:i.a.node.isRequired,marginLeft:i.a.number,marginRight:i.a.number}}).call(this,n(4))},77:function(e,t,n){"use strict";(function(e){var r=n(21),i=n.n(r),o=n(25),a=n.n(o),c=n(11),s=n.n(c),l=n(1),u=n.n(l),g=n(0),d=Object(g.forwardRef)((function(t,n){var r=t.label,o=t.className,c=t.hasLeftSpacing,l=void 0!==c&&c,u=a()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",i()({ref:n},u,{className:s()("googlesitekit-badge",o,{"googlesitekit-badge--has-left-spacing":l})}),r)}));d.displayName="Badge",d.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=d}).call(this,n(4))},787:function(e,t,n){"use strict";var r=n(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var o=r.createElement("path",{d:"M101 35.5H1V0",stroke:"#CCC"}),a=r.createElement("path",{d:"M2 24.685l24.5-7.404L51 25 75.5 8.774 100 2",stroke:"#CCC",strokeWidth:2});t.a=function SvgCtaGraphGoals(e){return r.createElement("svg",i({viewBox:"0 0 101 36",fill:"none"},e),o,a)}},79:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g}));var r=n(15),i=n.n(r),o=n(188),a=n(133),c={},s=void 0===e?null:e,l=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,u=void 0===r?0:r,g=e.initialHeight,d=void 0===g?0:g,f=Object(o.a)("undefined"==typeof document?[u,d]:l,t,n),p=i()(f,2),m=p[0],b=p[1],v=function(){return b(l)};return Object(a.a)(s,"resize",v),Object(a.a)(s,"orientationchange",v),m},g=function(e){return u(e)[0]}}).call(this,n(28))},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"s",(function(){return o})),n.d(t,"z",(function(){return a})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return s})),n.d(t,"g",(function(){return l})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return g})),n.d(t,"i",(function(){return d})),n.d(t,"k",(function(){return f})),n.d(t,"m",(function(){return p})),n.d(t,"n",(function(){return m})),n.d(t,"h",(function(){return b})),n.d(t,"x",(function(){return v})),n.d(t,"w",(function(){return h})),n.d(t,"y",(function(){return y})),n.d(t,"u",(function(){return O})),n.d(t,"v",(function(){return _})),n.d(t,"f",(function(){return k})),n.d(t,"l",(function(){return j})),n.d(t,"e",(function(){return E})),n.d(t,"t",(function(){return S})),n.d(t,"c",(function(){return w})),n.d(t,"d",(function(){return A})),n.d(t,"b",(function(){return T}));var r="modules/analytics-4",i="account_create",o="property_create",a="webdatastream_create",c="analyticsSetup",s=10,l=1,u="https://www.googleapis.com/auth/tagmanager.readonly",g="enhanced-measurement-form",d="enhanced-measurement-enabled",f="enhanced-measurement-should-dismiss-activation-banner",p="analyticsAccountCreate",m="analyticsCustomDimensionsCreate",b="https://www.googleapis.com/auth/analytics.edit",v="dashboardAllTrafficWidgetDimensionName",h="dashboardAllTrafficWidgetDimensionColor",y="dashboardAllTrafficWidgetDimensionValue",O="dashboardAllTrafficWidgetActiveRowIndex",_="dashboardAllTrafficWidgetLoaded",k={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},j={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},E=[j.CONTACT,j.GENERATE_LEAD,j.SUBMIT_LEAD_FORM],S={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},w="audiencePermissionsSetup",A="audienceTileCustomDimensionCreate",T="audience-selection-panel-expirable-new-badge-"},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return a})),n.d(t,"d",(function(){return c}));var r=n(106);function i(e){try{return new URL(e).pathname}catch(e){}return null}function o(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function a(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),i=e.replace(n.origin,"");if(i.length<t)return i;var o=i.length-Math.floor(t)+1;return"…"+i.substr(o)}},803:function(e,t,n){"use strict";(function(e){var r=n(2),i=n(7),o=n(523),a=n(3);t.a=function Header(){var t=Object(a.useSelect)((function(e){return e(i.a).getDateRangeNumberOfDays()}));return e.createElement(o.a,{title:Object(r.sprintf)(
/* translators: %s: number of days */
Object(r._n)("Search traffic over the last %s day","Search traffic over the last %s days",t,"google-site-kit"),t)})}}).call(this,n(4))},82:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o}));var r=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},i=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function o(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return j})),n.d(t,"d",(function(){return E})),n.d(t,"e",(function(){return w})),n.d(t,"c",(function(){return A})),n.d(t,"b",(function(){return T}));var r=n(15),i=n.n(r),o=n(33),a=n.n(o),c=n(6),s=n.n(c),l=n(25),u=n.n(l),g=n(14),d=n(63),f=n.n(d),p=n(2);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=O(e,t),r=n.formatUnit,i=n.formatDecimal;try{return r()}catch(e){return i()}},h=function(e){var t=y(e),n=t.hours,r=t.minutes,i=t.seconds;return i=("0"+i).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(i):"".concat(n,":").concat(r,":").concat(i)},y=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},O=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=y(e),r=n.hours,i=n.minutes,o=n.seconds;return{hours:r,minutes:i,seconds:o,formatUnit:function(){var n=t.unitDisplay,a=b(b({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(o,b(b({},a),{},{unit:"second"})):Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),o?w(o,b(b({},a),{},{unit:"second"})):"",i?w(i,b(b({},a),{},{unit:"minute"})):"",r?w(r,b(b({},a),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(p.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(p.__)("%ds","google-site-kit"),o);if(0===e)return t;var n=Object(p.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(p.__)("%dm","google-site-kit"),i),a=Object(p.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(p.__)("%dh","google-site-kit"),r);return Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),o?t:"",i?n:"",r?a:"").trim()}}},_=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},k=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in millions.
Object(p.__)("%sM","google-site-kit"),w(_(e),e%10==0?{}:t)):1e4<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(_(e))):1e3<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(_(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function j(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(g.isPlainObject)(e)&&(t=b({},e)),t}function E(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(g.isFinite)(e)?e:Number(e),Object(g.isFinite)(e)||(console.warn("Invalid number",e,a()(e)),e=0);var n=j(t),r=n.style,i=void 0===r?"metric":r;return"metric"===i?k(e):"duration"===i?v(e,n):"durationISO"===i?h(e):w(e,n)}var S=f()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?T():n,o=u()(t,["locale"]);try{return new Intl.NumberFormat(r,o).format(e)}catch(t){S("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(o)," ).format( ").concat(a()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],l={},g=0,d=Object.entries(o);g<d.length;g++){var f=i()(d[g],2),p=f[0],m=f[1];c[p]&&m===c[p]||(s.includes(p)||(l[p]=m))}try{return new Intl.NumberFormat(r,l).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},A=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?T():n,i=t.style,o=void 0===i?"long":i,a=t.type,c=void 0===a?"conjunction":a;if(Intl.ListFormat){var s=new Intl.ListFormat(r,{style:o,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var l=Object(p.__)(", ","google-site-kit");return e.join(l)},T=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(g.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i}));var r=n(149),i=n.n(r)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),o=n(11),a=n.n(o);function ChangeArrow(t){var n=t.direction,r=t.invertColor,i=t.width,o=t.height;return e.createElement("svg",{className:a()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:i,height:o,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:i.a.string,invertColor:i.a.bool,width:i.a.number,height:i.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},88:function(e,t,n){"use strict";n.r(t),n.d(t,"AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY",(function(){return r})),n.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY",(function(){return i})),n.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION",(function(){return o})),n.d(t,"AREA_MAIN_DASHBOARD_CONTENT_PRIMARY",(function(){return a})),n.d(t,"AREA_MAIN_DASHBOARD_SPEED_PRIMARY",(function(){return c})),n.d(t,"AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY",(function(){return s})),n.d(t,"AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY",(function(){return l})),n.d(t,"AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY",(function(){return u})),n.d(t,"AREA_ENTITY_DASHBOARD_SPEED_PRIMARY",(function(){return g})),n.d(t,"AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY",(function(){return d}));var r="mainDashboardKeyMetricsPrimary",i="mainDashboardTrafficPrimary",o="mainDashboardTrafficAudienceSegmentation",a="mainDashboardContentPrimary",c="mainDashboardSpeedPrimary",s="mainDashboardMonetizationPrimary",l="entityDashboardTrafficPrimary",u="entityDashboardContentPrimary",g="entityDashboardSpeedPrimary",d="entityDashboardMonetizationPrimary";t.default={AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY:r,AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY:i,AREA_MAIN_DASHBOARD_CONTENT_PRIMARY:a,AREA_MAIN_DASHBOARD_SPEED_PRIMARY:c,AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY:s,AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY:l,AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY:u,AREA_ENTITY_DASHBOARD_SPEED_PRIMARY:g,AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY:d}},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(12),i=n.n(r),o=function(e,t){var n=t.dateRangeLength;i()(Array.isArray(e),"report must be an array to partition."),i()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return i.b})),n.d(t,"J",(function(){return i.c})),n.d(t,"F",(function(){return o.a})),n.d(t,"K",(function(){return o.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return g.a})),n.d(t,"B",(function(){return g.d})),n.d(t,"C",(function(){return g.e})),n.d(t,"y",(function(){return g.c})),n.d(t,"r",(function(){return g.b})),n.d(t,"z",(function(){return m})),n.d(t,"j",(function(){return b})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return j})),n.d(t,"c",(function(){return E})),n.d(t,"e",(function(){return S})),n.d(t,"b",(function(){return w})),n.d(t,"a",(function(){return A})),n.d(t,"f",(function(){return T})),n.d(t,"n",(function(){return C})),n.d(t,"w",(function(){return D})),n.d(t,"p",(function(){return R})),n.d(t,"G",(function(){return N})),n.d(t,"s",(function(){return P})),n.d(t,"v",(function(){return x})),n.d(t,"k",(function(){return L})),n.d(t,"o",(function(){return I.b})),n.d(t,"h",(function(){return I.a})),n.d(t,"t",(function(){return M.b})),n.d(t,"q",(function(){return M.a})),n.d(t,"A",(function(){return M.c})),n.d(t,"x",(function(){return G})),n.d(t,"u",(function(){return F})),n.d(t,"E",(function(){return V})),n.d(t,"D",(function(){return U.a})),n.d(t,"g",(function(){return q})),n.d(t,"L",(function(){return W})),n.d(t,"l",(function(){return H}));var r=n(14),i=n(36),o=n(75),a=n(33),c=n.n(a),s=n(96),l=n.n(s),u=function(e){return l()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var i=t[r];i&&"object"===c()(i)&&!Array.isArray(i)&&(i=e(i)),n[r]=i})),n}(e)))};n(97);var g=n(83);function d(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function f(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function p(e){return e.replace(/\n/gi,"<br>")}function m(e){for(var t=e,n=0,r=[d,f,p];n<r.length;n++){t=(0,r[n])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(15),y=n.n(h),O=n(12),_=n.n(O),k=n(2),j="Invalid dateString parameter, it must be a string.",E='Invalid date range, it must be a string with the format "last-x-days".',S=60,w=60*S,A=24*w,T=7*A;function C(){var e=function(e){return Object(k.sprintf)(
/* translators: %s: number of days */
Object(k._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function D(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function R(e){_()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function N(e){_()(D(e),j);var t=e.split("-"),n=y()(t,3),r=n[0],i=n[1],o=n[2];return new Date(r,i-1,o)}function P(e,t){return R(L(e,t*A))}function x(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function L(e,t){_()(D(e)||Object(r.isDate)(e)&&!isNaN(e),j);var n=D(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var I=n(98),M=n(80);function G(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function F(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var B=n(27),z=n.n(B),V=function(e){return Array.isArray(e)?z()(e).sort():e},U=n(89);function q(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var W=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},H=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},90:function(e,t,n){"use strict";(function(e,r){n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return O})),n.d(t,"a",(function(){return TourTooltips}));var i=n(6),o=n.n(i),a=n(81),c=n(30),s=n(1),l=n.n(s),u=n(2),g=n(3),d=n(23),f=n(7),p=n(36),m=n(107),b=n(18);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var h={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},y={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},O={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},_="feature_tooltip_view",k="feature_tooltip_advance",j="feature_tooltip_return",E="feature_tooltip_dismiss",S="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,i=t.tourID,s=t.gaEventCategory,l=t.callback,u="".concat(i,"-step"),w="".concat(i,"-run"),A=Object(g.useDispatch)(d.b).setValue,T=Object(g.useDispatch)(f.a).dismissTour,C=Object(g.useRegistry)(),D=Object(b.a)(),R=Object(g.useSelect)((function(e){return e(d.b).getValue(u)})),N=Object(g.useSelect)((function(e){return e(d.b).getValue(w)&&!1===e(f.a).isTourDismissed(i)}));Object(a.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),A(w,!0)}));var P=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return r.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,r=e.lifecycle,i=e.size,o=e.status,a=e.type,l=t+1,u="function"==typeof s?s(D):s;a===c.b.TOOLTIP&&r===c.c.TOOLTIP?Object(p.b)(u,_,l):n===c.a.CLOSE&&r===c.c.COMPLETE?Object(p.b)(u,E,l):n===c.a.NEXT&&o===c.d.FINISHED&&a===c.b.TOUR_END&&i===l&&Object(p.b)(u,S,l),r===c.c.COMPLETE&&o!==c.d.FINISHED&&(n===c.a.PREV&&Object(p.b)(u,j,l),n===c.a.NEXT&&Object(p.b)(u,k,l))}(t);var n=t.action,r=t.index,o=t.status,a=t.step,g=t.type,d=n===c.a.CLOSE,f=!d&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(g),m=[c.d.FINISHED,c.d.SKIPPED].includes(o),b=d&&g===c.b.STEP_AFTER,v=m||b;if(c.b.STEP_BEFORE===g){var h,y,O=a.target;"string"==typeof a.target&&(O=e.document.querySelector(a.target)),null===(h=O)||void 0===h||null===(y=h.scrollIntoView)||void 0===y||y.call(h,{block:"center"})}f?function(e,t){A(u,e+(t===c.a.PREV?-1:1))}(r,n):v&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(i)),T(i)),l&&l(t,C)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:O,locale:y,run:N,showProgress:!0,stepIndex:R,steps:P,styles:h,tooltipComponent:m.a})}TourTooltips.propTypes={steps:l.a.arrayOf(l.a.object).isRequired,tourID:l.a.string.isRequired,gaEventCategory:l.a.oneOfType([l.a.string,l.a.func]).isRequired,callback:l.a.func}}).call(this,n(28),n(4))},923:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsView}));var r=n(2),i=n(3),o=n(66),a=n(182);function SettingsView(){var t=Object(i.useSelect)((function(e){return e(o.b).getPropertyID()}));return e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("h5",{className:"googlesitekit-settings-module__meta-item-type"},Object(r.__)("Connected Property","google-site-kit")),e.createElement("p",{className:"googlesitekit-settings-module__meta-item-data"},e.createElement(a.b,{value:t})))}}).call(this,n(4))},924:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PropertySelect}));var r=n(0),i=n(2),o=n(10),a=n(3),c=n(66),s=n(36),l=n(18);function PropertySelect(t){var n=t.hasModuleAccess,u=Object(l.a)(),g=Object(a.useSelect)((function(e){return e(c.b).getPropertyID()})),d=Object(a.useSelect)((function(e){return e(c.b).getMatchedProperties()})),f=Object(a.useSelect)((function(e){return e(c.b).hasFinishedResolution("getMatchedProperties")})),p=Object(a.useDispatch)(c.b).setPropertyID,m=Object(r.useCallback)((function(e,t){var n=t.dataset.value;g!==n&&(p(n),Object(s.b)("".concat(u,"_search-console"),"change_property"))}),[g,p,u]);return f?!1===n?e.createElement(o.Select,{className:"googlesitekit-search-console__select-property",label:Object(i.__)("Property","google-site-kit"),value:g,enhanced:!0,outlined:!0,disabled:!0},e.createElement(o.Option,{value:g},g)):e.createElement(o.Select,{className:"googlesitekit-search-console__select-property",label:Object(i.__)("Property","google-site-kit"),value:g,onEnhancedChange:m,enhanced:!0,outlined:!0},(d||[]).map((function(t){var n=t.siteURL;return e.createElement(o.Option,{key:n,value:n},n.startsWith("sc-domain:")?Object(i.sprintf)(
/* translators: %s: domain name */
Object(i.__)("%s (domain property)","google-site-kit"),n.replace(/^sc-domain:/,"")):n)}))):e.createElement(o.ProgressBar,{small:!0})}}).call(this,n(4))},925:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActivateAnalyticsCTA}));var r=n(1),i=n.n(r),o=n(2),a=n(681),c=n(787),s=n(576),l=n(416);function ActivateAnalyticsCTA(t){var n=t.title;return e.createElement(s.a,null,e.createElement(l.a,{title:Object(o.__)("Unique visitors from Search","google-site-kit"),GraphSVG:a.a}),e.createElement(l.a,{title:n,GraphSVG:c.a}))}ActivateAnalyticsCTA.propTypes={title:i.a.string.isRequired}}).call(this,n(4))},926:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AnalyticsStats}));var r=n(6),i=n.n(r),o=n(15),a=n.n(o),c=n(526),s=n.n(c),l=n(1),u=n.n(l),g=n(2),d=n(3),f=n(17),p=n(19),m=n(376),b=n(366),v=n(8),h=n(34),y=n(9),O=n(7);function _(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function AnalyticsStats(t){var n=t.data,r=t.selectedStats,i=t.dateRangeLength,o=t.dataLabels,c=t.tooltipDataFormats,l=t.chartDataFormats,u=t.statsColor,_=t.gatheringData,j=t.moduleSlug,E=Object(h.a)(),S=Object(d.useSelect)((function(e){return e(p.a).isModuleConnected(j)})),w=Object(d.useSelect)((function(e){return e(p.a).isModuleActive(j)})),A=Object(d.useSelect)((function(e){return e(O.a).getReferenceDate()})),T=Object(d.useSelect)((function(e){return E?null:e(v.r).getPropertyCreateTime()})),C=[];if(T&&(C=[{date:Object(y.p)(new Date(T)),text:Object(g.__)("Google Analytics property created","google-site-kit")}]),!w||!S)return null;var D=Object(m.a)(n,r,i,A,o,c,l),R=D.slice(1).map((function(e){return a()(e,1)[0]})),N=s()(R).slice(1),P=k(k({},AnalyticsStats.chartOptions),{},{hAxis:k(k({},AnalyticsStats.chartOptions.hAxis),{},{ticks:N}),vAxis:k({},AnalyticsStats.chartOptions.vAxis),series:{0:{color:u,targetAxisIndex:0},1:{color:u,targetAxisIndex:0,lineDashStyle:[3,3],lineWidth:1}}});if(!D.slice(1).some((function(e){return e[2]>0||e[3]>0}))){var x={0:1,1:100}[r];P.vAxis.viewWindow.max=x}else P.vAxis.viewWindow.max=void 0;return e.createElement(f.e,{className:"googlesitekit-analytics-site-stats"},e.createElement(f.k,null,e.createElement(f.a,{size:12},e.createElement(b.a,{chartType:"LineChart",data:D,dateMarkers:C,loadingHeight:"270px",loadingWidth:"100%",options:P,gatheringData:_}))))}AnalyticsStats.propTypes={data:u.a.oneOfType([u.a.arrayOf(u.a.object),u.a.object]).isRequired,dateRangeLength:u.a.number.isRequired,selectedStats:u.a.number.isRequired,dataLabels:u.a.arrayOf(u.a.string).isRequired,tooltipDataFormats:u.a.arrayOf(u.a.func).isRequired,statsColor:u.a.string.isRequired,gatheringData:u.a.bool,moduleSlug:u.a.string.isRequired},AnalyticsStats.chartOptions={chart:{title:""},curveType:"function",height:270,width:"100%",chartArea:{height:"80%",left:60,right:25},legend:{position:"top",textStyle:{color:"#616161",fontSize:12}},hAxis:{format:"MMM d",gridlines:{color:"#fff"},textStyle:{color:"#616161",fontSize:12}},vAxis:{gridlines:{color:"#eee"},minorGridlines:{color:"#eee"},textStyle:{color:"#616161",fontSize:12},titleTextStyle:{color:"#616161",fontSize:12,italic:!1},viewWindow:{min:0}},focusTarget:"category",crosshair:{color:"gray",opacity:.1,orientation:"vertical",trigger:"both"},tooltip:{isHtml:!0,trigger:"both"}}}).call(this,n(4))},927:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsEdit}));var r=n(3),i=n(10),o=n(66),a=n(19),c=n(759);function SettingsEdit(){Object(r.useSelect)((function(e){return e(o.b).getMatchedProperties()}));var t,n=Object(r.useSelect)((function(e){return e(o.b).isDoingSubmitChanges()})),s=Object(r.useSelect)((function(e){return e(o.b).hasFinishedResolution("getMatchedProperties")})),l=Object(r.useSelect)((function(e){return e(a.a).hasModuleOwnershipOrAccess("search-console")}));return t=n||!s||void 0===l?e.createElement(i.ProgressBar,null):e.createElement(c.a,{hasModuleAccess:l}),e.createElement("div",{className:"googlesitekit-setup-module googlesitekit-setup-module--search-console"},t)}}).call(this,n(4))},95:function(e,t,n){"use strict";(function(e){var r=n(1),i=n.n(r),o=n(11),a=n.n(o),c=n(10),s=n(20);function CTA(t){var n=t.title,r=t.headerText,i=t.headerContent,o=t.description,l=t.ctaLink,u=t.ctaLabel,g=t.ctaLinkExternal,d=t.ctaType,f=t.error,p=t.onClick,m=t["aria-label"],b=t.children;return e.createElement("div",{className:a()("googlesitekit-cta",{"googlesitekit-cta--error":f})},(r||i)&&e.createElement("div",{className:"googlesitekit-cta__header"},r&&e.createElement("h2",{className:"googlesitekit-cta__header_text"},r),i),e.createElement("div",{className:"googlesitekit-cta__body"},n&&e.createElement("h3",{className:"googlesitekit-cta__title"},n),o&&"string"==typeof o&&e.createElement("p",{className:"googlesitekit-cta__description"},o),o&&"string"!=typeof o&&e.createElement("div",{className:"googlesitekit-cta__description"},o),u&&"button"===d&&e.createElement(c.Button,{"aria-label":m,href:l,onClick:p},u),u&&"link"===d&&e.createElement(s.a,{href:l,onClick:p,"aria-label":m,external:g,hideExternalIndicator:g,arrow:!0},u),b))}CTA.propTypes={title:i.a.string.isRequired,headerText:i.a.string,description:i.a.oneOfType([i.a.string,i.a.node]),ctaLink:i.a.string,ctaLinkExternal:i.a.bool,ctaLabel:i.a.string,ctaType:i.a.string,"aria-label":i.a.string,error:i.a.bool,onClick:i.a.func,children:i.a.node,headerContent:i.a.node},CTA.defaultProps={title:"",headerText:"",headerContent:"",description:"",ctaLink:"",ctaLabel:"",ctaType:"link",error:!1,onClick:function(){}},t.a=CTA}).call(this,n(4))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return o})),n.d(t,"a",(function(){return a}));var r=n(239),i=n(85),o=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var o=n.invertColor,a=void 0!==o&&o;return Object(r.a)(e.createElement(i.a,{direction:t>0?"up":"down",invertColor:a}))},a=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return g}));var r=n(6),i=n.n(r),o=n(14),a=n(100),c=n(101);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function g(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=l(l({},u),t);i.referenceSiteURL&&(i.referenceSiteURL=i.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(a.a)(i,n),g=Object(c.a)(i,n,s,r),d={},f=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);d[r]||(d[r]=Object(o.once)(g)),d[r].apply(d,t)};return{enableTracking:function(){i.trackingEnabled=!0},disableTracking:function(){i.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!i.trackingEnabled},trackEvent:g,trackEventOnce:f}}}).call(this,n(28))}},[[1296,1,0]]]);