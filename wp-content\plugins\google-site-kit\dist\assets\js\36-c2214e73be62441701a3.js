(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[36],{1220:function(e,t,r){"use strict";r.r(t);var l=r(0);function n(){return(n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var l in r)Object.prototype.hasOwnProperty.call(r,l)&&(e[l]=r[l])}return e}).apply(this,arguments)}var i=l.createElement("g",{filter:"url(#ghost-card-green_svg__filter0_d_4824_80823)"},l.createElement("rect",{width:165,height:90,rx:11,fill:"#fff"}),l.createElement("rect",{x:.5,y:.5,width:164,height:89,rx:10.5,stroke:"#EBEEF0"})),a=l.createElement("rect",{x:16,y:16,width:64,height:8,rx:4,fill:"#EBEEF0"}),o=l.createElement("path",{d:"M16 43c0-5.523 4.477-10 10-10h30c5.523 0 10 4.477 10 10s-4.477 10-10 10H26c-5.523 0-10-4.477-10-10z",fill:"#B8E6CA"}),c=l.createElement("rect",{x:16,y:68,width:133,height:8,rx:4,fill:"#EBEEF0"}),f=l.createElement("path",{d:"M25 46.667l6.593-6.594m0 0L26.392 40m5.201.073l.074 5.202",stroke:"#fff",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"}),h=l.createElement("rect",{x:38,y:40,width:20,height:6,rx:2,fill:"#fff"}),s=l.createElement("defs",null,l.createElement("filter",{id:"ghost-card-green_svg__filter0_d_4824_80823",x:0,y:0,width:169,height:95,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},l.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),l.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),l.createElement("feOffset",{dx:4,dy:5}),l.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),l.createElement("feColorMatrix",{values:"0 0 0 0 0.921569 0 0 0 0 0.933333 0 0 0 0 0.941176 0 0 0 1 0"}),l.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_4824_80823"}),l.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_4824_80823",result:"shape"})));t.default=function SvgGhostCardGreen(e){return l.createElement("svg",n({viewBox:"0 0 169 95",fill:"none"},e),i,a,o,c,f,h,s)}}}]);