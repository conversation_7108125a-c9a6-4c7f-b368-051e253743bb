(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[32],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var a=n(59),r=n(39),i=n(57);function o(t,n){var o,c=Object(a.a)(n),l=t.activeModules,s=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,m=void 0===d?[]:d,g=t.isAuthenticated,f=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(r.b,"]"))),!o){o=!0;var a=(null==m?void 0:m.length)?m.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:s,plugin_version:f||"",enabled_features:Array.from(i.a).join(","),active_modules:l.join(","),authenticated:g?"1":"0",user_properties:{user_roles:a,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(r.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(r.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(r.a)}}}}}).call(this,n(28))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var a=n(5),r=n.n(a),i=n(6),o=n.n(i),c=n(16),l=n.n(c),s=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e,t,n,a){var i=Object(s.a)(t);return function(){var t=l()(r.a.mark((function t(o,c,l,s){var u;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:l,value:s},t.abrupt("return",new Promise((function(e){var t,n,r=setTimeout((function(){a.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),l=function(){clearTimeout(r),e()};i("event",c,d(d({},u),{},{event_callback:l})),(null===(t=a._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&l()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,a,r){return t.apply(this,arguments)}}()}},102:function(e,t,n){"use strict";var a=n(123);n.d(t,"a",(function(){return a.a}));var r=n(124);n.d(t,"c",(function(){return r.a}));var i=n(125);n.d(t,"b",(function(){return i.a}))},104:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return a.createElement("svg",r({viewBox:"0 0 14 14",fill:"none"},e),i)}},105:function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(1),l=n.n(c),s=n(11),u=n.n(s);function VisuallyHidden(t){var n=t.className,a=t.children,i=o()(t,["className","children"]);return a?e.createElement("span",r()({},i,{className:u()("screen-reader-text",n)}),a):null}VisuallyHidden.propTypes={className:l.a.string,children:l.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(4))},107:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var a=n(21),r=n.n(a),i=n(152),o=n.n(i),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(2),m=n(10),g=n(154),f=n(104);function TourTooltip(t){var n=t.backProps,a=t.closeProps,c=t.index,s=t.primaryProps,u=t.size,p=t.step,y=t.tooltipProps,b=u>1?Object(g.a)(u):[],h=function(e){return l()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",r()({className:l()("googlesitekit-tour-tooltip",p.className)},y),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},p.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},p.content)),e.createElement(i.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},b.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:h(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(m.Button,r()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),p.cta,s.title&&e.createElement(m.Button,r()({className:"googlesitekit-tooltip-button",text:!0},s),s.title))),e.createElement(m.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(f.a,{width:"14",height:"14"}),onClick:a.onClick,"aria-label":Object(d.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(4))},109:function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(208),l=n(38),s=n(2),u=n(10),d=n(58);function ModalDialog(t){var n=t.className,a=void 0===n?"":n,r=t.dialogActive,i=void 0!==r&&r,m=t.handleDialog,g=void 0===m?null:m,f=t.onOpen,p=void 0===f?null:f,y=t.onClose,b=void 0===y?null:y,h=t.title,v=void 0===h?null:h,T=t.provides,O=t.handleConfirm,k=t.subtitle,E=t.confirmButton,N=void 0===E?null:E,j=t.dependentModules,S=t.danger,_=void 0!==S&&S,A=t.inProgress,I=void 0!==A&&A,w=t.small,C=void 0!==w&&w,Z=t.medium,M=void 0!==Z&&Z,D=t.buttonLink,P=void 0===D?null:D,L=Object(c.a)(ModalDialog),G="googlesitekit-dialog-description-".concat(L),R=!(!T||!T.length);return e.createElement(u.Dialog,{open:i,onOpen:p,onClose:b,"aria-describedby":R?G:void 0,tabIndex:"-1",className:o()(a,{"googlesitekit-dialog-sm":C,"googlesitekit-dialog-md":M})},e.createElement(u.DialogTitle,null,_&&e.createElement(d.a,{width:28,height:28}),v),k?e.createElement("p",{className:"mdc-dialog__lead"},k):[],e.createElement(u.DialogContent,null,R&&e.createElement("section",{id:G,className:"mdc-dialog__provides"},e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},T.map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t},e.createElement("span",{className:"mdc-list-item__text"},t))})))),j&&e.createElement("p",{className:"mdc-dialog__dependencies"},Object(l.a)(Object(s.sprintf)(
/* translators: %s is replaced with the dependent modules. */
Object(s.__)("<strong>Note:</strong> %s","google-site-kit"),j),{strong:e.createElement("strong",null)}))),e.createElement(u.DialogFooter,null,e.createElement(u.Button,{className:"mdc-dialog__cancel-button",tertiary:!0,onClick:g,disabled:I},Object(s.__)("Cancel","google-site-kit")),P?e.createElement(u.Button,{href:P,onClick:O,target:"_blank",danger:_},N):e.createElement(u.SpinnerButton,{onClick:O,danger:_,disabled:I,isSaving:I},N||Object(s.__)("Disconnect","google-site-kit"))))}ModalDialog.displayName="Dialog",ModalDialog.propTypes={className:r.a.string,dialogActive:r.a.bool,handleDialog:r.a.func,handleConfirm:r.a.func.isRequired,onOpen:r.a.func,onClose:r.a.func,title:r.a.string,confirmButton:r.a.string,danger:r.a.bool,small:r.a.bool,medium:r.a.bool,buttonLink:r.a.string},t.a=ModalDialog}).call(this,n(4))},114:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return u}));var a=n(6),r=n.n(a),i=n(1),o=n.n(i),c=n(11),l=n.n(c),s=n(2),u={DEFAULT:"default",OVERLAY:"overlay",SMALL:"small",SMALL_OVERLAY:"small-overlay",LARGE:"large"};function GatheringDataNotice(t){var n=t.style;return e.createElement("div",{className:l()("googlesitekit-gathering-data-notice",r()({},"googlesitekit-gathering-data-notice--has-style-".concat(n),!!n))},e.createElement("span",null,Object(s.__)("Gathering data…","google-site-kit")))}GatheringDataNotice.propTypes={style:o.a.oneOf(Object.values(u))},t.b=GatheringDataNotice}).call(this,n(4))},1199:function(e,t,n){"use strict";(function(e){var a=n(15),r=n.n(a),i=n(240),o=n(0),c=n(2),l=n(3),s=n(20),u=n(1200),d=n(160),m=n(13);t.a=function WPDashboardApp(){var t=Object(o.useRef)(),n=Object(i.a)(t,{threshold:0}),a=Object(l.useSelect)((function(e){return e(m.c).getAdminURL("googlesitekit-dashboard")})),g=Object(o.useState)({key:"WPDashboardApp",value:!!(null==n?void 0:n.intersectionRatio)}),f=r()(g,2),p=f[0],y=f[1];return Object(o.useEffect)((function(){y({key:"WPDashboardApp",value:!!(null==n?void 0:n.intersectionRatio)})}),[n]),void 0===a?e.createElement("div",{ref:t}):a?e.createElement(d.a,{value:p},e.createElement("div",{className:"googlesitekit-wp-dashboard",ref:t},e.createElement("div",{className:"googlesitekit-wp-dashboard__cta"},e.createElement(s.a,{className:"googlesitekit-wp-dashboard__cta-link",href:a},Object(c.__)("Visit your Site Kit Dashboard","google-site-kit"))),e.createElement(u.a,null))):null}}).call(this,n(4))},120:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var a=n(1),r=n.n(a),i=n(0),o=n(2),c=n(3),l=n(10),s=n(35),u=n(54);function ErrorNotice(t){var n,a=t.error,r=t.hasButton,d=void 0!==r&&r,m=t.storeName,g=t.message,f=void 0===g?a.message:g,p=t.noPrefix,y=void 0!==p&&p,b=t.skipRetryMessage,h=t.Icon,v=Object(c.useDispatch)(),T=Object(c.useSelect)((function(e){return m?e(m).getSelectorDataForError(a):null})),O=Object(i.useCallback)((function(){v(T.storeName).invalidateResolution(T.name,T.args)}),[v,T]);if(!a||Object(s.f)(a))return null;var k=d&&Object(s.d)(a,T);return d||b||(f=Object(o.sprintf)(
/* translators: %1$s: Error message from Google API. */
Object(o.__)("%1$s%2$s Please try again.","google-site-kit"),f,f.endsWith(".")?"":".")),e.createElement(i.Fragment,null,h&&e.createElement("div",{className:"googlesitekit-error-notice__icon"},e.createElement(h,{width:"24",height:"24"})),e.createElement(u.a,{message:f,reconnectURL:null===(n=a.data)||void 0===n?void 0:n.reconnectURL,noPrefix:y}),k&&e.createElement(l.Button,{className:"googlesitekit-error-notice__retry-button",onClick:O},Object(o.__)("Retry","google-site-kit")))}ErrorNotice.propTypes={error:r.a.shape({message:r.a.string}),hasButton:r.a.bool,storeName:r.a.string,message:r.a.string,noPrefix:r.a.bool,skipRetryMessage:r.a.bool,Icon:r.a.elementType}}).call(this,n(4))},1200:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WPDashboardWidgets}));var a=n(11),r=n.n(a),i=n(0),o=n(3),c=n(19),l=n(7),s=n(243),u=n(1201),d=n(1202),m=n(1203),g=n(1204),f=n(1205),p=n(1206),y=n(1207),b=n(507),h=Object(s.b)("wpDashboardImpressions")(u.a),v=Object(s.b)("wpDashboardClicks")(d.a),T=Object(s.b)("wpDashboardUniqueVisitors")(m.a),O=Object(s.b)("wpDashboardSessionDuration")(g.a),k=Object(s.b)("wpDashboardPopularPages")(f.a),E=Object(s.b)("wpDashboardUniqueVisitorsChart")(p.a);function WPDashboardWidgets(){var t=Object(o.useSelect)((function(e){return e(c.a).getModule("analytics-4")})),n=Object(o.useSelect)((function(e){return e(l.a).hasAccessToShareableModule("analytics-4")})),a=Object(o.useSelect)((function(e){return e(l.a).hasAccessToShareableModule("search-console")}));if(void 0===t)return null;var s=t.active,u=t.connected,d=s&&u;return e.createElement(b.a,{className:r()("googlesitekit-wp-dashboard-stats googlesitekit-wp-dashboard-stats--twoup",{"googlesitekit-wp-dashboard-stats--fourup":d})},d&&n&&e.createElement(i.Fragment,null,e.createElement(T,null),e.createElement(O,null)),a&&e.createElement(i.Fragment,null,e.createElement(h,null),e.createElement(v,null)),!d&&e.createElement("div",{className:"googlesitekit-wp-dashboard-stats__cta"},e.createElement(y.a,null)),d&&n&&e.createElement(i.Fragment,null,e.createElement(E,null),e.createElement(k,null)))}}).call(this,n(4))},1201:function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(1),o=n.n(i),c=n(2),l=n(3),s=n(66),u=n(7),d=n(236),m=n(44),g=n(114),f=n(9),p=n(418),y=n(89);function WPDashboardImpressions(t){var n=t.WPDashboardReportError,a=Object(l.useInViewSelect)((function(e){return e(s.b).isGatheringData()})),i=Object(l.useSelect)((function(e){return e(u.a).getDateRangeDates({compare:!0,offsetDays:s.a})})),o=i.compareStartDate,b=i.endDate,h=Object(l.useSelect)((function(e){return e(u.a).getDateRangeNumberOfDays()})),v={startDate:o,endDate:b,dimensions:"date"},T=Object(l.useInViewSelect)((function(e){return e(s.b).getReport(v)}),[v]),O=Object(l.useSelect)((function(e){return e(s.b).getErrorForSelector("getReport",[v])}));if(Object(l.useSelect)((function(e){return!e(s.b).hasFinishedResolution("getReport",[v])}))||void 0===a)return e.createElement(m.a,{width:"48%",height:"92px"});if(O)return e.createElement(n,{moduleSlug:"search-console",error:O});var k=Object(y.a)(T,{dateRangeLength:h}),E=k.compareRange,N=k.currentRange,j=Object(p.a)(N,"impressions"),S=Object(p.a)(E,"impressions"),_=Object(f.g)(S,j),A={gatheringData:a,gatheringDataNoticeStyle:g.a.SMALL};return e.createElement(d.a,r()({className:"googlesitekit-wp-dashboard-stats__data-table overview-total-impressions",title:Object(c.__)("Total Impressions","google-site-kit"),datapoint:j,change:_,changeDataUnit:"%"},A))}WPDashboardImpressions.propTypes={WPDashboardReportError:o.a.elementType.isRequired},t.a=WPDashboardImpressions}).call(this,n(4))},1202:function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(1),o=n.n(i),c=n(2),l=n(3),s=n(66),u=n(7),d=n(9),m=n(418),g=n(89),f=n(236),p=n(44),y=n(114);function WPDashboardClicks(t){var n=t.WPDashboardReportError,a=Object(l.useInViewSelect)((function(e){return e(s.b).isGatheringData()})),i=Object(l.useSelect)((function(e){return e(u.a).getDateRangeDates({compare:!0,offsetDays:s.a})})),o=i.compareStartDate,b=i.endDate,h=Object(l.useSelect)((function(e){return e(u.a).getDateRangeNumberOfDays()})),v={startDate:o,endDate:b,dimensions:"date"},T=Object(l.useInViewSelect)((function(e){return e(s.b).getReport(v)}),[v]),O=Object(l.useSelect)((function(e){return e(s.b).getErrorForSelector("getReport",[v])}));if(Object(l.useSelect)((function(e){return!e(s.b).hasFinishedResolution("getReport",[v])}))||void 0===a)return e.createElement(p.a,{width:"48%",height:"92px"});if(O)return e.createElement(n,{moduleSlug:"search-console",error:O});var k=Object(g.a)(T,{dateRangeLength:h}),E=k.compareRange,N=k.currentRange,j=Object(m.a)(N,"clicks"),S=Object(m.a)(E,"clicks"),_=Object(d.g)(S,j),A={gatheringData:a,gatheringDataNoticeStyle:y.a.SMALL};return e.createElement(f.a,r()({className:"googlesitekit-wp-dashboard-stats__data-table overview-total-clicks",title:Object(c.__)("Total Clicks","google-site-kit"),datapoint:j,change:_,changeDataUnit:"%"},A))}WPDashboardClicks.propTypes={WPDashboardReportError:o.a.elementType.isRequired},t.a=WPDashboardClicks}).call(this,n(4))},1203:function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(6),o=n.n(i),c=n(1),l=n.n(c),s=n(2),u=n(3),d=n(8),m=n(7),g=n(44),f=n(9),p=n(236),y=n(114);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function WPDashboardUniqueVisitorsGA4(t){var n,a,i,o,c,l,b,v,T=t.WPDashboardReportError,O=Object(u.useInViewSelect)((function(e){return e(d.r).isGatheringData()})),k=h(h({},Object(u.useSelect)((function(e){return e(m.a).getDateRangeDates({compare:!0,offsetDays:d.g})}))),{},{metrics:[{name:"totalUsers"}]}),E=Object(u.useInViewSelect)((function(e){return e(d.r).getReport(k)}),[k]),N=Object(u.useSelect)((function(e){return e(d.r).getErrorForSelector("getReport",[k])}));if(Object(u.useSelect)((function(e){return!e(d.r).hasFinishedResolution("getReport",[k])}))||void 0===O)return e.createElement(g.a,{width:"48%",height:"92px"});if(N)return e.createElement(T,{moduleSlug:"analytics-4",error:N});var j=null==E||null===(n=E.totals)||void 0===n||null===(a=n[0])||void 0===a||null===(i=a.metricValues)||void 0===i||null===(o=i[0])||void 0===o?void 0:o.value,S=null==E||null===(c=E.totals)||void 0===c||null===(l=c[1])||void 0===l||null===(b=l.metricValues)||void 0===b||null===(v=b[0])||void 0===v?void 0:v.value,_={gatheringData:O,gatheringDataNoticeStyle:y.a.SMALL};return e.createElement(p.a,r()({className:"googlesitekit-wp-dashboard-stats__data-table overview-total-users",title:Object(s.__)("Total Unique Visitors","google-site-kit"),datapoint:j,change:Object(f.g)(S,j),changeDataUnit:"%"},_))}WPDashboardUniqueVisitorsGA4.propTypes={WPDashboardReportError:l.a.elementType.isRequired},t.a=WPDashboardUniqueVisitorsGA4}).call(this,n(4))},1204:function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(6),o=n.n(i),c=n(1),l=n.n(c),s=n(2),u=n(3),d=n(8),m=n(7),g=n(9),f=n(236),p=n(44),y=n(114);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function WPDashboardSessionDurationGA4(t){var n,a,i,o,c,l,b=t.WPDashboardReportError,v=Object(u.useInViewSelect)((function(e){return e(d.r).isGatheringData()})),T=h(h({},Object(u.useSelect)((function(e){return e(m.a).getDateRangeDates({compare:!0,offsetDays:d.g})}))),{},{dimensions:[{name:"date"}],limit:10,metrics:[{name:"averageSessionDuration"}]}),O=Object(u.useInViewSelect)((function(e){return e(d.r).getReport(T)}),[T]),k=Object(u.useSelect)((function(e){return e(d.r).getErrorForSelector("getReport",[T])}));if(Object(u.useSelect)((function(e){return!e(d.r).hasFinishedResolution("getReport",[T])}))||void 0===v)return e.createElement(p.a,{width:"48%",height:"92px"});if(k)return e.createElement(b,{moduleSlug:"analytics-4",error:k});var E=O.totals,N=null==E||null===(n=E[0])||void 0===n||null===(a=n.metricValues)||void 0===a||null===(i=a[0])||void 0===i?void 0:i.value,j=null==E||null===(o=E[1])||void 0===o||null===(c=o.metricValues)||void 0===c||null===(l=c[0])||void 0===l?void 0:l.value,S=N,_=Object(g.g)(j,N),A={gatheringData:v,gatheringDataNoticeStyle:y.a.SMALL};return e.createElement(f.a,r()({className:"googlesitekit-wp-dashboard-stats__data-table overview-average-session-duration",title:Object(s.__)("Avg. Time on Page","google-site-kit"),datapoint:S,datapointUnit:"s",change:_,changeDataUnit:"%"},A))}WPDashboardSessionDurationGA4.propTypes={WPDashboardReportError:l.a.elementType.isRequired},t.a=WPDashboardSessionDurationGA4}).call(this,n(4))},1205:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WPDashboardPopularPagesGA4}));var a=n(15),r=n.n(a),i=n(6),o=n.n(i),c=n(14),l=n(1),s=n.n(l),u=n(11),d=n.n(u),m=n(2),g=n(3),f=n(8),p=n(150),y=n(7),b=n(503),h=n(505),v=n(504),T=n(678),O=n(9);function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function E(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function WPDashboardPopularPagesGA4(t){var n,a=t.WPDashboardReportError,i=Object(g.useSelect)((function(e){return e(y.a).getDateRangeDates({offsetDays:f.g})})),o=Object(g.useInViewSelect)((function(e){return e(f.r).isGatheringData()})),l=E(E({},i),{},{dimensions:["pagePath"],metrics:[{name:"screenPageViews"}],orderby:[{metric:{metricName:"screenPageViews"},desc:!0}],limit:5}),s=Object(g.useInViewSelect)((function(e){return e(f.r).getReport(l)}),[l]),u=Object(g.useInViewSelect)((function(e){return e(f.r).getPageTitles(s,l)}),[s,l]),k=Object(g.useSelect)((function(e){return e(f.r).getErrorForSelector("getReport",[l])}));if(Object(g.useSelect)((function(e){return!(void 0!==k||void 0!==u)||!e(f.r).hasFinishedResolution("getReport",[l])}))||void 0===o)return e.createElement(b.a,{rows:6});if(k)return e.createElement(a,{moduleSlug:"analytics-4",error:k});var N=(null==s||null===(n=s.rows)||void 0===n?void 0:n.length)?Object(c.cloneDeep)(s.rows):[];N.forEach((function(e){var t=e.dimensionValues[0].value;e.dimensionValues.unshift({value:u[t]})}));var j=[{title:Object(m.__)("Title","google-site-kit"),description:Object(m.__)("Page Title","google-site-kit"),primary:!0,Component:function Component(t){var n=t.row,a=r()(n.dimensionValues,2),i=a[0].value,o=a[1].value;return e.createElement(T.a,{title:i,path:o})}},{title:Object(m.__)("Pageviews","google-site-kit"),description:Object(m.__)("Pageviews","google-site-kit"),field:"metricValues.0.value",Component:function Component(t){var n=t.fieldValue;return e.createElement("span",null,Object(O.B)(n,{style:"decimal"}))}}];return e.createElement("div",{className:d()("googlesitekit-search-console-widget",{"googlesitekit-search-console-widget--empty-data":o||!(null==N?void 0:N.length)})},e.createElement("h3",null,Object(m.__)("Top content over the last 28 days","google-site-kit")),e.createElement(h.a,null,e.createElement(v.a,{rows:N,columns:j,limit:5,gatheringData:o,zeroState:p.h})))}WPDashboardPopularPagesGA4.propTypes={WPDashboardReportError:s.a.elementType.isRequired}}).call(this,n(4))},1206:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WPDashboardUniqueVisitorsChartGA4}));var a=n(6),r=n.n(a),i=n(15),o=n.n(i),c=n(526),l=n.n(c),s=n(1),u=n.n(s),d=n(2),m=n(3),g=n(23),f=n(7),p=n(8),y=n(366),b=n(837),h=n(447),v=n(9);function T(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?T(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):T(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function WPDashboardUniqueVisitorsChartGA4(t){var n=t.WPDashboardReportError,a=Object(m.useInViewSelect)((function(e){return e(p.r).isGatheringData()})),r=Object(m.useSelect)((function(e){return e(g.b).getValue("googleChartsCollisionError")})),i=Object(m.useSelect)((function(e){return e(f.a).getReferenceDate()})),c=Object(m.useSelect)((function(e){return e(f.a).getDateRangeDates({compare:!0,offsetDays:p.g})})),s=c.startDate,u=c.endDate,T=c.compareStartDate,k=c.compareEndDate,E=Object(m.useSelect)((function(e){return e(f.a).getDateRangeNumberOfDays()})),N={startDate:s,endDate:u,compareStartDate:T,compareEndDate:k,metrics:[{name:"totalUsers"}],dimensions:["date"],orderby:[{dimension:{dimensionName:"date"}}]},j=Object(m.useInViewSelect)((function(e){return e(p.r).getReport(N)}),[N]),S=Object(m.useSelect)((function(e){return!e(p.r).hasFinishedResolution("getReport",[N])})),_=Object(m.useSelect)((function(e){return e(p.r).getErrorForSelector("getReport",[N])}));if(r)return null;if(_)return e.createElement(n,{moduleSlug:"analytics-4",error:_});var A=Object(h.a)(j,0,E,i,[Object(d.__)("Unique Visitors","google-site-kit")],[function(e){return parseFloat(e).toLocaleString()}]),I=A.slice(1).map((function(e){return o()(e,1)[0]})),w=l()(I).slice(1),C=O(O({},b.a),{},{hAxis:O(O({},b.a.hAxis),{},{ticks:w})});return!A.slice(1).some((function(e){return e[2]>0||e[3]>0}))&&(C.hAxis.ticks=[Object(v.G)(i)]),e.createElement("div",{className:"googlesitekit-unique-visitors-chart-widget"},e.createElement("h3",null,Object(d.sprintf)(
/* translators: %s: number of days */
Object(d._n)("Unique visitors over the last %s day","Unique visitors over the last %s days",E,"google-site-kit"),E)),e.createElement(y.a,{chartType:"LineChart",data:A,loadingHeight:"270px",loadingWidth:"100%",loaded:!(S||void 0===a),options:C,gatheringData:a}))}WPDashboardUniqueVisitorsChartGA4.propTypes={WPDashboardReportError:u.a.elementType.isRequired}}).call(this,n(4))},1207:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WPDashboardActivateAnalyticsCTA}));var a=n(2),r=n(693),i=n(1208),o=n(576),c=n(416);function WPDashboardActivateAnalyticsCTA(){return e.createElement(o.a,null,e.createElement(c.a,{title:Object(a.__)("Traffic","google-site-kit"),GraphSVG:r.a,showIcons:!1}),e.createElement(c.a,{title:Object(a.__)("Most popular content","google-site-kit"),GraphSVG:i.a,showIcons:!1}))}}).call(this,n(4))},1208:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M96.91 4.813l1.34-4.5h1.305L97.575 6h-.86l.195-1.188zm-1.234-4.5l1.336 4.5L97.215 6h-.867L94.378.312h1.298zm5.527 1.46V6h-1.129V1.773h1.129zM100.004.672c0-.164.057-.3.172-.406a.648.648 0 01.461-.16c.19 0 .342.053.457.16a.526.526 0 01.176.406c0 .164-.059.3-.176.406a.644.644 0 01-.457.16.648.648 0 01-.461-.16.53.53 0 01-.172-.406zm4.074 5.406c-.328 0-.622-.052-.883-.156a1.95 1.95 0 01-.664-.442 1.983 1.983 0 01-.414-.652 2.204 2.204 0 01-.144-.797v-.156c0-.32.045-.613.136-.879.092-.266.222-.496.391-.691.172-.196.38-.345.625-.45.245-.106.521-.16.828-.16.3 0 .565.05.797.149.232.099.426.24.582.422.159.182.279.4.359.656.081.252.121.534.121.844v.468h-3.359v-.75h2.254v-.086a.98.98 0 00-.086-.418.665.665 0 00-.25-.296.774.774 0 00-.43-.11.717.717 0 00-.382.098.756.756 0 00-.262.273 1.496 1.496 0 00-.149.414 2.656 2.656 0 00-.046.516v.156c0 .17.023.326.07.469.049.143.118.267.207.371a.94.94 0 00.328.242c.13.058.277.086.441.086.204 0 .392-.039.567-.117a1.23 1.23 0 00.457-.363l.547.593a1.67 1.67 0 01-.364.368 1.941 1.941 0 01-.546.289c-.214.073-.457.11-.731.11zm3.582-1.172l.879-3.133h.715l-.231 1.23L108.145 6h-.598l.113-1.094zm-.445-3.133l.629 3.13.058 1.097h-.703l-1.07-4.227h1.086zm2.844 3.079l.613-3.079h1.09L110.691 6h-.699l.067-1.148zm-.707-3.079l.875 3.11.121 1.117h-.602l-.879-2.992-.222-1.235h.707zm5.261 3.059a.364.364 0 00-.07-.219.616.616 0 00-.262-.18 2.555 2.555 0 00-.543-.156 4.25 4.25 0 01-.597-.175 2.052 2.052 0 01-.477-.262 1.128 1.128 0 01-.312-.36.994.994 0 01-.114-.48c0-.177.038-.344.114-.5.078-.156.188-.294.332-.414.145-.122.323-.218.531-.285.211-.07.448-.106.711-.106.367 0 .682.059.945.176.266.117.469.279.609.484.144.204.215.435.215.696h-1.125a.6.6 0 00-.07-.293.465.465 0 00-.211-.207.753.753 0 00-.367-.078.734.734 0 00-.324.066.495.495 0 00-.211.172.413.413 0 00-.032.41c.029.05.075.095.137.137.063.041.143.08.242.117.102.034.227.065.375.094.305.062.577.144.817.246.239.099.429.234.57.406.141.17.211.392.211.668 0 .188-.042.36-.125.516a1.242 1.242 0 01-.359.41 1.83 1.83 0 01-.563.27 2.624 2.624 0 01-.73.093c-.394 0-.727-.07-1-.21a1.59 1.59 0 01-.618-.536 1.245 1.245 0 01-.207-.672h1.067a.622.622 0 00.117.371.645.645 0 00.285.203c.117.042.244.063.379.063a.97.97 0 00.363-.059.51.51 0 00.219-.164.397.397 0 00.078-.242z",fill:"#C7C7C7"}),o=a.createElement("rect",{x:103,y:18.6,width:13.3,height:4.2,rx:2.1,fill:"#C7C7C7"}),c=a.createElement("rect",{x:107,y:33,width:9.1,height:4.2,rx:2.1,fill:"#C7C7C7"}),l=a.createElement("rect",{x:107,y:47,width:9.1,height:4.2,rx:2.1,fill:"#C7C7C7"}),s=a.createElement("rect",{x:110,y:61,width:5.6,height:4.2,rx:2.1,fill:"#C7C7C7"}),u=a.createElement("path",{d:"M2.848 17.981v5.72h-.723v-4.817l-1.457.531v-.652l2.066-.782h.114zm1.351 19.125v.594H.477v-.52l1.863-2.074c.229-.255.406-.47.531-.648.128-.18.216-.34.266-.48a1.27 1.27 0 00.078-.438c0-.188-.04-.357-.117-.508a.879.879 0 00-.336-.367 1.011 1.011 0 00-.54-.137c-.252 0-.463.05-.632.149a.913.913 0 00-.375.406 1.38 1.38 0 00-.125.602H.367c0-.32.07-.614.211-.88.14-.265.35-.476.625-.632.276-.159.616-.238 1.02-.238.359 0 .666.063.922.191.255.125.45.302.585.531.138.227.208.492.208.797a1.6 1.6 0 01-.086.508c-.055.17-.132.338-.23.508-.097.169-.21.336-.34.5a7.774 7.774 0 01-.41.484l-1.524 1.652h2.851zM1.527 48.501h.516c.253 0 .46-.042.625-.125a.86.86 0 00.371-.348c.083-.148.125-.315.125-.5 0-.218-.036-.402-.11-.55a.732.732 0 00-.327-.336 1.201 1.201 0 00-.555-.114c-.203 0-.383.04-.54.121a.886.886 0 00-.362.336.997.997 0 00-.13.516H.419c0-.29.073-.552.219-.79.146-.236.35-.425.613-.566.266-.14.573-.21.922-.21.344 0 .644.06.902.183.258.12.459.3.602.54.143.236.215.532.215.886a1.364 1.364 0 01-.41.914c-.139.14-.318.257-.54.348a2.146 2.146 0 01-.796.133h-.618V48.5zm0 .594v-.434h.618c.362 0 .661.043.898.129.237.086.423.2.559.344.138.143.234.3.289.472a1.73 1.73 0 01-.05 1.215c-.09.206-.216.38-.38.524a1.619 1.619 0 01-.57.324c-.22.073-.457.11-.715.11-.248 0-.48-.036-.7-.106a1.813 1.813 0 01-.574-.305 1.427 1.427 0 01-.39-.496 1.56 1.56 0 01-.14-.676h.722c0 .198.043.371.129.52a.914.914 0 00.375.347c.164.081.356.122.578.122.221 0 .411-.038.57-.114a.822.822 0 00.371-.351c.089-.157.133-.353.133-.59s-.05-.431-.148-.582a.87.87 0 00-.422-.34 1.637 1.637 0 00-.637-.113h-.516zm2.79 14.691v.594H.206v-.426l2.547-3.941h.59l-.633 1.14-1.684 2.633h3.29zm-.794-3.773V65.7h-.722v-5.687h.722z",fill:"#C7C7C7"}),d=a.createElement("rect",{x:9.8,y:18.6,width:48.3,height:4.2,rx:2.1,fill:"#C7C7C7"}),m=a.createElement("rect",{x:9.8,y:33,width:72.8,height:4.2,rx:2.1,fill:"#C7C7C7"}),g=a.createElement("rect",{x:9.8,y:47,width:42,height:4.2,rx:2.1,fill:"#C7C7C7"}),f=a.createElement("rect",{x:9.8,y:61,width:55.3,height:4.2,rx:2.1,fill:"#C7C7C7"});t.a=function SvgCtaMostPopularContent(e){return a.createElement("svg",r({viewBox:"0 0 117 70",fill:"none"},e),i,o,c,l,s,u,d,m,g,f)}},121:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var a=n(219),r=n(14),i=n(0);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=Object(a.b)((function(){return r.debounce.apply(void 0,t)}),t);return Object(i.useEffect)((function(){return function(){return o.cancel()}}),[o]),o}},122:function(e,t,n){"use strict";n.d(t,"d",(function(){return c})),n.d(t,"b",(function(){return l})),n.d(t,"a",(function(){return s})),n.d(t,"c",(function(){return u})),n.d(t,"e",(function(){return d}));var a=n(33),r=n.n(a),i=n(14),o=n(178);function c(e){var t=function(e){return"string"==typeof e&&/^[a-zA-Z0-9_]+$/.test(e)};return"string"==typeof e?e.split(",").every(t):Object(o.c)(e,(function(e){var n=e.hasOwnProperty("name")&&t(e.name);if(!e.hasOwnProperty("expression"))return n;var a="string"==typeof e.expression;return n&&a}),t)}function l(e){return Object(o.c)(e,(function(e){return e.hasOwnProperty("name")&&"string"==typeof e.name}))}function s(e){var t=["string"];return Object.keys(e).every((function(n){if(t.includes(r()(e[n])))return!0;if(Array.isArray(e[n]))return e[n].every((function(e){return t.includes(r()(e))}));if(Object(i.isPlainObject)(e[n])){var a=Object.keys(e[n]);return!!a.includes("filterType")&&!("emptyFilter"!==e[n].filterType&&!a.includes("value"))}return!1}))}function u(e){var t=["string"],n=["numericFilter","betweenFilter"];return Object.values(e).every((function(e){if(t.includes(r()(e)))return!0;if(Array.isArray(e))return e.every((function(e){return t.includes(r()(e))}));if(!Object(i.isPlainObject)(e))return!1;var a=e.filterType,o=e.value,c=e.fromValue,l=e.toValue;if(a&&!n.includes(a))return!1;var s=Object.keys(e);return a&&"numericFilter"!==a?"betweenFilter"===a&&(s.includes("fromValue")&&s.includes("toValue")&&[c,l].every((function(e){return!Object(i.isPlainObject)(e)||"int64Value"in e}))):s.includes("operation")&&s.includes("value")&&(!Object(i.isPlainObject)(o)||"int64Value"in o)}))}function d(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(i.isPlainObject)(e)&&((!e.hasOwnProperty("desc")||"boolean"==typeof e.desc)&&(e.metric?!e.dimension&&"string"==typeof(null===(t=e.metric)||void 0===t?void 0:t.metricName):!!e.dimension&&"string"==typeof(null===(n=e.dimension)||void 0===n?void 0:n.dimensionName)));var t,n}))}},123:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var a=n(21),r=n.n(a),i=n(6),o=n.n(i),c=n(25),l=n.n(c),s=n(1),u=n.n(s),d=n(11),m=n.n(d);function Cell(t){var n,a=t.className,i=t.alignTop,c=t.alignMiddle,s=t.alignBottom,u=t.alignRight,d=t.alignLeft,g=t.smAlignRight,f=t.mdAlignRight,p=t.lgAlignRight,y=t.smSize,b=t.smStart,h=t.smOrder,v=t.mdSize,T=t.mdStart,O=t.mdOrder,k=t.lgSize,E=t.lgStart,N=t.lgOrder,j=t.size,S=t.children,_=l()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",r()({},_,{className:m()(a,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":i,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":s,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":g,"mdc-layout-grid__cell--align-right-tablet":f,"mdc-layout-grid__cell--align-right-desktop":p},o()(n,"mdc-layout-grid__cell--span-".concat(j),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--start-".concat(E,"-desktop"),12>=E&&E>0),o()(n,"mdc-layout-grid__cell--order-".concat(N,"-desktop"),12>=N&&N>0),o()(n,"mdc-layout-grid__cell--span-".concat(v,"-tablet"),8>=v&&v>0),o()(n,"mdc-layout-grid__cell--start-".concat(T,"-tablet"),8>=T&&T>0),o()(n,"mdc-layout-grid__cell--order-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--span-".concat(y,"-phone"),4>=y&&y>0),o()(n,"mdc-layout-grid__cell--start-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--order-".concat(h,"-phone"),4>=h&&h>0),n))}),S)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},124:function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),m=Object(d.forwardRef)((function(t,n){var a=t.className,i=t.children,c=o()(t,["className","children"]);return e.createElement("div",r()({ref:n,className:u()("mdc-layout-grid__inner",a)},c),i)}));m.displayName="Row",m.propTypes={className:l.a.string,children:l.a.node},m.defaultProps={className:""},t.a=m}).call(this,n(4))},125:function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),m=Object(d.forwardRef)((function(t,n){var a=t.alignLeft,i=t.fill,c=t.className,l=t.children,s=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",r()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":a,"mdc-layout-grid--collapsed":s,"mdc-layout-grid--fill":i})},d,{ref:n}),l)}));m.displayName="Grid",m.propTypes={alignLeft:l.a.bool,fill:l.a.bool,className:l.a.string,collapsed:l.a.bool,children:l.a.node},m.defaultProps={className:""},t.a=m}).call(this,n(4))},126:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),a.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return a.createElement("svg",r({viewBox:"0 0 13 13"},e),i)}},127:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),a.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return a.createElement("svg",r({viewBox:"0 0 13 13"},e),i)}},128:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return a.createElement("svg",r({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},1289:function(e,t,n){"use strict";n.r(t),function(e){var t=n(332),a=n(144),r=n(224),i=n(22),o=n(1199);Object(t.a)((function(){var t=document.getElementById("js-googlesitekit-wp-dashboard");if(t){var n=t.dataset.viewOnly?i.v:i.u;Object(a.render)(e.createElement(r.a,{viewContext:n},e.createElement(o.a,null)),t)}}))}.call(this,n(4))},129:function(e,t,n){"use strict";n.d(t,"a",(function(){return p})),n.d(t,"c",(function(){return b})),n.d(t,"b",(function(){return h}));var a=n(25),r=n.n(a),i=n(6),o=n.n(i),c=n(5),l=n.n(c),s=n(12),u=n.n(s),d=n(3),m=n.n(d),g=n(37),f=n(9),p=function(e){var t;u()(e,"storeName is required to create a snapshot store.");var n={},a={deleteSnapshot:l.a.mark((function e(){var t;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"DELETE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})),restoreSnapshot:l.a.mark((function e(){var t,n,a,r,i,o,c=arguments;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},n=t.clearAfterRestore,a=void 0===n||n,e.next=4,{payload:{},type:"RESTORE_SNAPSHOT"};case 4:if(r=e.sent,i=r.cacheHit,o=r.value,!i){e.next=13;break}return e.next=10,{payload:{snapshot:o},type:"SET_STATE_FROM_SNAPSHOT"};case 10:if(!a){e.next=13;break}return e.next=13,{payload:{},type:"DELETE_SNAPSHOT"};case 13:return e.abrupt("return",i);case 14:case"end":return e.stop()}}),e)})),createSnapshot:l.a.mark((function e(){var t;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"CREATE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))},i=(t={},o()(t,"DELETE_SNAPSHOT",(function(){return Object(g.c)("datastore::cache::".concat(e))})),o()(t,"CREATE_SNAPSHOT",Object(d.createRegistryControl)((function(t){return function(){return Object(g.f)("datastore::cache::".concat(e),t.stores[e].store.getState())}}))),o()(t,"RESTORE_SNAPSHOT",(function(){return Object(g.d)("datastore::cache::".concat(e),f.b)})),t);return{initialState:n,actions:a,controls:i,reducer:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1?arguments[1]:void 0,a=t.type,i=t.payload;switch(a){case"SET_STATE_FROM_SNAPSHOT":var o=i.snapshot,c=(o.error,r()(o,["error"]));return c;default:return e}}}},y=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m.a;return Object.values(e.stores).filter((function(e){return Object.keys(e.getActions()).includes("restoreSnapshot")}))},b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m.a;return Promise.all(y(e).map((function(e){return e.getActions().createSnapshot()})))},h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m.a;return Promise.all(y(e).map((function(e){return e.getActions().restoreSnapshot()})))}},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}));var a="core/site",r="primary",i="secondary"},130:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var a=n(14),r=function(e){return Object(a.isFinite)(e)?e:0}},134:function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(38),l=n(2),s=n(20),u=n(34);function SourceLink(t){var n=t.name,a=t.href,r=t.className,i=t.external;return Object(u.a)()?null:e.createElement("div",{className:o()("googlesitekit-source-link",r)},Object(c.a)(Object(l.sprintf)(
/* translators: %s: source link */
Object(l.__)("Source: %s","google-site-kit"),"<a>".concat(n,"</a>")),{a:e.createElement(s.a,{key:"link",href:a,external:i})}))}SourceLink.propTypes={name:r.a.string,href:r.a.string,className:r.a.string,external:r.a.bool},SourceLink.defaultProps={name:"",href:"",className:"",external:!1},t.a=SourceLink}).call(this,n(4))},135:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportErrorActions}));var a=n(6),r=n.n(a),i=n(1),o=n.n(i),c=n(0),l=n(38),s=n(2),u=n(3),d=n(10),m=n(13),g=n(19),f=n(35),p=n(34),y=n(20);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportErrorActions(t){var n=t.moduleSlug,a=t.error,r=t.GetHelpLink,i=t.hideGetHelpLink,o=t.buttonVariant,b=t.onRetry,v=t.onRequestAccess,T=t.getHelpClassName,O=t.RequestAccessButton,k=t.RetryButton,E=Object(p.a)(),N=Object(u.useSelect)((function(e){return e(g.a).getModuleStoreName(n)})),j=Object(u.useSelect)((function(e){var t;return"function"==typeof(null===(t=e(N))||void 0===t?void 0:t.getServiceEntityAccessURL)?e(N).getServiceEntityAccessURL():null})),S=Array.isArray(a)?a:[a],_=Object(u.useSelect)((function(e){return S.map((function(t){var n,a=null===(n=e(N))||void 0===n?void 0:n.getSelectorDataForError(t);return h(h({},t),{},{selectorData:a})}))})),A=null==_?void 0:_.filter((function(e){return Object(f.d)(e,e.selectorData)&&"getReport"===e.selectorData.name})),I=!!A.length,w=Object(u.useSelect)((function(e){var t=h({},I?A[0]:S[0]);return Object(f.e)(t)&&(t.code="".concat(n,"_insufficient_permissions")),e(m.c).getErrorTroubleshootingLinkURL(t)})),C=Object(u.useDispatch)(),Z=S.some((function(e){return Object(f.e)(e)})),M=Object(c.useCallback)((function(){A.forEach((function(e){var t=e.selectorData;C(t.storeName).invalidateResolution(t.name,t.args)})),null==b||b()}),[C,A,b]),D=j&&Z&&!E;return e.createElement("div",{className:"googlesitekit-report-error-actions"},D&&("function"==typeof O?e.createElement(O,{requestAccessURL:j}):e.createElement(d.Button,{onClick:v,href:j,target:"_blank",danger:"danger"===o,tertiary:"tertiary"===o},Object(s.__)("Request access","google-site-kit"))),I&&e.createElement(c.Fragment,null,"function"==typeof k?e.createElement(k,{handleRetry:M}):e.createElement(d.Button,{onClick:M,danger:"danger"===o,tertiary:"tertiary"===o},Object(s.__)("Retry","google-site-kit")),!i&&e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(l.a)(Object(s.__)("Retry didn’t work? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(y.a,{href:w,external:!0,hideExternalIndicator:!0},Object(s.__)("Get help","google-site-kit"))}))),!I&&!i&&e.createElement("div",{className:T},"function"==typeof r?e.createElement(r,{linkURL:w}):e.createElement(y.a,{href:w,external:!0,hideExternalIndicator:!0},Object(s.__)("Get help","google-site-kit"))))}ReportErrorActions.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired,GetHelpLink:o.a.elementType,hideGetHelpLink:o.a.bool,buttonVariant:o.a.string,onRetry:o.a.func,onRequestAccess:o.a.func,getHelpClassName:o.a.string,RequestAccessButton:o.a.elementType,RetryButton:o.a.elementType}}).call(this,n(4))},136:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return r})),n.d(t,"d",(function(){return i})),n.d(t,"c",(function(){return o}));function a(e){var t=e.format,n=void 0===t?"small":t,a=e.hasErrorOrWarning,r=e.hasSmallImageSVG,o=e.hasWinImageSVG,c={smSize:4,mdSize:8,lgSize:12},l=i(n);return Object.keys(c).forEach((function(e){var t=c[e];a&&(t-=1),r&&(t-=1),o&&0<t-l[e]&&(t-=l[e]),c[e]=t})),c}var r=function(e){switch(e){case"small":return{};case"larger":return{smOrder:2,mdOrder:2,lgOrder:1};default:return{smOrder:2,mdOrder:1}}},i=function(e){switch(e){case"smaller":return{smSize:4,mdSize:2,lgSize:2};case"larger":return{smSize:4,mdSize:8,lgSize:7};default:return{smSize:4,mdSize:2,lgSize:4}}},o=function(e){switch(e){case"larger":return{smOrder:1,mdOrder:1,lgOrder:2};default:return{smOrder:1,mdOrder:2}}}},139:function(e,t,n){"use strict";var a=n(0),r=Object(a.createContext)(!1);t.a=r},141:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return StoreErrorNotices}));var a=n(1),r=n.n(a),i=n(3),o=n(120),c=n(19),l=n(35),s=n(169);function StoreErrorNotices(t){var n=t.hasButton,a=void 0!==n&&n,r=t.moduleSlug,u=t.storeName,d=Object(i.useSelect)((function(e){return e(u).getErrors()})),m=Object(i.useSelect)((function(e){return e(c.a).getModule(r)})),g=[];return d.filter((function(e){return!(!(null==e?void 0:e.message)||g.includes(e.message))&&(g.push(e.message),!0)})).map((function(t,n){var r=t.message;return Object(l.e)(t)&&(r=Object(s.a)(r,m)),e.createElement(o.a,{key:n,error:t,hasButton:a,storeName:u,message:r})}))}StoreErrorNotices.propTypes={hasButton:r.a.bool,storeName:r.a.string.isRequired,moduleSlug:r.a.string}}).call(this,n(4))},142:function(e,t,n){"use strict";var a=n(166);n.d(t,"c",(function(){return a.a}));var r=n(65);n.d(t,"b",(function(){return r.c})),n.d(t,"a",(function(){return r.a}))},143:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var a=n(0),r=n(3),i=n(47);function o(e,t,n){var o=Object(r.useDispatch)(i.a),c=o.setWidgetState,l=o.unsetWidgetState;Object(a.useEffect)((function(){return c(e,t,n),function(){l(e,t,n)}}),[e,t,n,c,l])}},148:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M3.332 24.75h21.335c2.182 0 3.542-2.366 2.451-4.25L16.451 2.07C15.36.184 12.64.184 11.549 2.07L.882 20.5c-1.091 1.884.269 4.25 2.45 4.25zM14 14.833a1.42 1.42 0 01-1.417-1.416v-2.834c0-.779.638-1.416 1.417-1.416.78 0 1.417.637 1.417 1.416v2.834A1.42 1.42 0 0114 14.833zm1.417 5.667h-2.834v-2.833h2.834V20.5z",fill:"currentColor",fillRule:"nonzero"});t.a=function SvgError(e){return a.createElement("svg",r({viewBox:"0 0 28 25"},e),i)}},150:function(e,t,n){"use strict";n.d(t,"a",(function(){return a.a})),n.d(t,"c",(function(){return r.a})),n.d(t,"b",(function(){return i.a})),n.d(t,"g",(function(){return o.a})),n.d(t,"d",(function(){return c.a})),n.d(t,"e",(function(){return l.a})),n.d(t,"f",(function(){return s.a})),n.d(t,"h",(function(){return ZeroDataMessage}));var a=n(551),r=n(552),i=n(553),o=(n(352),n(554)),c=n(555),l=n(519),s=(n(425),n(394),n(556)),u=n(1),d=n.n(u),m=n(2),g=n(3),f=n(13);function ZeroDataMessage(e){var t=e.skipPrefix,n=Object(g.useSelect)((function(e){return e(f.c).getCurrentEntityURL()}));return t?n?Object(m.__)("Your page hasn’t received any visitors yet","google-site-kit"):Object(m.__)("Your site hasn’t received any visitors yet","google-site-kit"):n?Object(m.__)("No data to display: your page hasn’t received any visitors yet","google-site-kit"):Object(m.__)("No data to display: your site hasn’t received any visitors yet","google-site-kit")}ZeroDataMessage.propTypes={skipPrefix:d.a.bool}},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var a=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},155:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("path",{d:"M2.253 12.252l7.399 5.658A13.055 13.055 0 009 22c0 1.43.229 2.805.652 4.09l-7.4 5.658A22.02 22.02 0 010 22c0-3.506.81-6.814 2.253-9.748z",fill:"#FBBC05"}),a.createElement("path",{d:"M9.652 17.91l-7.4-5.658A21.935 21.935 0 0122 0c5.6 0 10.6 2.1 14.5 5.5l-6.4 6.4C27.9 10.1 25.1 9 22 9c-5.77 0-10.64 3.725-12.348 8.91z",fill:"#EA4335"}),a.createElement("path",{d:"M2.25 31.742l7.396-5.67A12.975 12.975 0 0022 35c6.1 0 10.7-3.1 11.8-8.5H22V18h20.5c.3 1.3.5 2.7.5 4 0 14-10 22-21 22A21.935 21.935 0 012.25 31.742z",fill:"#34A853"}),a.createElement("path",{d:"M36.34 38.52l-7.025-5.437c2.297-1.45 3.895-3.685 4.485-6.583H22V18h20.5c.3 1.3.5 2.7.5 4 0 7.17-2.623 12.767-6.66 16.52z",fill:"#4285F4"}));t.a=function SvgLogoG(e){return a.createElement("svg",r({viewBox:"0 0 43 44"},e),i)}},158:function(e,t,n){"use strict";var a=n(0),r=n(57),i=Object(a.createContext)(r.a);t.a=i},159:function(e,t,n){"use strict";n.d(t,"a",(function(){return y}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(0),l=n(3),s=n(13),u=n(7),d=n(19),m=n(32),g=n(37),f=n(36),p=n(18);function y(e){var t=Object(p.a)(),n=Object(l.useSelect)((function(t){return t(d.a).getModule(e)})),a=Object(l.useSelect)((function(e){return e(u.a).hasCapability(u.K)})),i=Object(l.useDispatch)(d.a).activateModule,y=Object(l.useDispatch)(m.a).navigateTo,b=Object(l.useDispatch)(s.c).setInternalServerError,h=Object(c.useCallback)(o()(r.a.mark((function n(){var a,o,c;return r.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,i(e);case 2:if(a=n.sent,o=a.error,c=a.response,o){n.next=13;break}return n.next=8,Object(f.b)("".concat(t,"_widget-activation-cta"),"activate_module",e);case 8:return n.next=10,Object(g.f)("module_setup",e,{ttl:300});case 10:y(c.moduleReauthURL),n.next=14;break;case 13:b({id:"".concat(e,"-setup-error"),description:o.message});case 14:case"end":return n.stop()}}),n)}))),[i,e,y,b,t]);return(null==n?void 0:n.name)&&a?h:null}},160:function(e,t,n){"use strict";var a=n(139),r=(a.a.Consumer,a.a.Provider);t.a=r},163:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RecoverableModules}));var a=n(1),r=n.n(a),i=n(2),o=n(3),c=n(19),l=n(95);function RecoverableModules(t){var n=t.moduleSlugs,a=Object(o.useSelect)((function(e){var t=e(c.a).getModules();if(void 0!==t)return n.map((function(e){return t[e].name}))}));if(void 0===a)return null;var r=1===a.length?Object(i.sprintf)(
/* translators: %s: Module name */
Object(i.__)("%s data was previously shared by an admin who no longer has access. Please contact another admin to restore it.","google-site-kit"),a[0]):Object(i.sprintf)(
/* translators: %s: List of module names */
Object(i.__)("The data for the following modules was previously shared by an admin who no longer has access: %s. Please contact another admin to restore it.","google-site-kit"),a.join(Object(i._x)(", ","Recoverable modules","google-site-kit")));return e.createElement(l.a,{title:Object(i.__)("Data Unavailable","google-site-kit"),description:r})}RecoverableModules.propTypes={moduleSlugs:r.a.arrayOf(r.a.string).isRequired}}).call(this,n(4))},166:function(e,t,n){"use strict";(function(e){var a=n(11),r=n.n(a),i=n(1),o=n.n(i),c=n(2),l=n(3),s=n(201),u=n(210),d=n(65),m=n(7),g=n(10),f=n(0),p=Object(f.forwardRef)((function(t,n){var a=t.className,i=t.children,o=t.type,f=t.dismiss,p=void 0===f?"":f,y=t.dismissCallback,b=t.dismissLabel,h=void 0===b?Object(c.__)("OK, Got it!","google-site-kit"):b,v=t.Icon,T=void 0===v?Object(d.d)(o):v,O=t.OuterCTA,k=Object(l.useDispatch)(m.a).dismissItem,E=Object(l.useSelect)((function(e){return p?e(m.a).isItemDismissed(p):void 0}));if(p&&E)return null;var N=i?u.a:s.a;return e.createElement("div",{ref:n,className:r()(a,"googlesitekit-settings-notice","googlesitekit-settings-notice--".concat(o),{"googlesitekit-settings-notice--single-row":!i,"googlesitekit-settings-notice--multi-row":i})},e.createElement("div",{className:"googlesitekit-settings-notice__icon"},e.createElement(T,{width:"20",height:"20"})),e.createElement("div",{className:"googlesitekit-settings-notice__body"},e.createElement(N,t)),p&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(g.Button,{tertiary:!0,onClick:function(){"string"==typeof p&&k(p),null==y||y()}},h)),O&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(O,null)))}));p.propTypes={className:o.a.string,children:o.a.node,notice:o.a.node.isRequired,type:o.a.oneOf([d.a,d.c,d.b]),Icon:o.a.elementType,LearnMore:o.a.elementType,CTA:o.a.elementType,OuterCTA:o.a.elementType,dismiss:o.a.string,dismissLabel:o.a.string,dismissCallback:o.a.func},p.defaultProps={type:d.a},t.a=p}).call(this,n(4))},169:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var a=n(2);function r(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},r=n.slug,i=void 0===r?"":r,o=n.name,c=void 0===o?"":o,l=n.owner,s=void 0===l?{}:l;if(!i||!c)return e;var u="",d="";return"analytics-4"===i?e.match(/account/i)?u=Object(a.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?u=Object(a.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(u=Object(a.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):"search-console"===i&&(u=Object(a.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),u||(u=Object(a.sprintf)(
/* translators: %s: module name */
Object(a.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),c)),s&&s.login&&(d=Object(a.sprintf)(
/* translators: %s: owner name */
Object(a.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),s.login)),d||(d=Object(a.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(u," ").concat(d)}},17:function(e,t,n){"use strict";var a=n(254);n.d(t,"i",(function(){return a.a}));var r=n(319);n.d(t,"f",(function(){return r.a}));var i=n(320);n.d(t,"h",(function(){return i.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var l=n(91),s=n.n(l);n.d(t,"b",(function(){return s.a})),n.d(t,"c",(function(){return l.DialogContent})),n.d(t,"d",(function(){return l.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},170:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportError}));var a=n(6),r=n.n(a),i=n(1),o=n.n(i),c=n(14),l=n(0),s=n(2),u=n(3),d=n(19),m=n(35),g=n(169),f=n(84),p=n(54),y=n(95),b=n(135),h=n(34);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function T(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportError(t){var n,a=t.moduleSlug,r=t.error,i=Object(h.a)(),o=Object(u.useSelect)((function(e){return e(d.a).getModule(a)})),v=Array.isArray(r)?r:[r],O=function(e){return Object(m.e)(e)?i?(n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Access lost to %s","google-site-kit"),null==o?void 0:o.name),Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("The administrator sharing this module with you has lost access to the %s service, so you won’t be able to see stats from it on the Site Kit dashboard. You can contact them or another administrator to restore access.","google-site-kit"),null==o?void 0:o.name)):(n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Insufficient permissions in %s","google-site-kit"),null==o?void 0:o.name),Object(g.a)(e.message,o)):Object(m.b)(e)},k=Object(c.uniqWith)(v.map((function(e){var t;return T(T({},e),{},{message:O(e),reconnectURL:null===(t=e.data)||void 0===t?void 0:t.reconnectURL})})),(function(e,t){return e.message===t.message&&e.reconnectURL===t.reconnectURL})),E=v.some((function(e){return Object(m.e)(e)}));E||1!==k.length?!E&&k.length>1&&(n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Data errors in %s","google-site-kit"),null==o?void 0:o.name)):n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Data error in %s","google-site-kit"),null==o?void 0:o.name);var N=e.createElement(l.Fragment,null,k.map((function(t){var n,a=null==r||null===(n=r.data)||void 0===n?void 0:n.reconnectURL;return a?e.createElement(p.a,{key:t.message,message:t.message,reconnectURL:a}):e.createElement("p",{key:t.message},f.a.sanitize(t.message,{ALLOWED_TAGS:[]}))})));return e.createElement(y.a,{title:n,description:N,error:!0},e.createElement(b.a,{moduleSlug:a,error:r}))}ReportError.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired}}).call(this,n(4))},172:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GenericErrorHandlerActions}));var a=n(1),r=n.n(a),i=n(2),o=n(20),c=n(194);function GenericErrorHandlerActions(t){var n=t.message,a=t.componentStack;return e.createElement("div",{className:"googlesitekit-generic-error-handler-actions"},e.createElement(c.a,{message:n,componentStack:a}),e.createElement(o.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0},Object(i.__)("Report this problem","google-site-kit")))}GenericErrorHandlerActions.propTypes={message:r.a.string,componentStack:r.a.string}}).call(this,n(4))},173:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var a=n(22),r=function(e){return a.f.includes(e)}},174:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportZero}));var a=n(1),r=n.n(a),i=n(2),o=n(3),c=n(19),l=n(95);function ReportZero(t){var n=t.moduleSlug,a=Object(o.useSelect)((function(e){return e(c.a).getModule(n)}));return e.createElement(l.a,{title:Object(i.sprintf)(
/* translators: %s: Module name */
Object(i.__)("%s Gathering Data","google-site-kit"),null==a?void 0:a.name),description:Object(i.sprintf)(
/* translators: %s: Module name */
Object(i.__)("%s data is not yet available, please check back later","google-site-kit"),null==a?void 0:a.name)})}ReportZero.propTypes={moduleSlug:r.a.string.isRequired}}).call(this,n(4))},178:function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"d",(function(){return l}));var a=n(33),r=n.n(a);function i(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return!0};return"string"==typeof e?n(e):!("object"!==r()(e)||!t(e))||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e?n(e):"object"===r()(e)&&t(e)}))}function o(e){var t=e.startDate,n=e.endDate,a=t&&t.match(/^\d{4}-\d{2}-\d{2}$/),r=n&&n.match(/^\d{4}-\d{2}-\d{2}$/);return a&&r}function c(e){var t=function(e){var t=e.hasOwnProperty("fieldName")&&!!e.fieldName,n=e.hasOwnProperty("sortOrder")&&/(ASCENDING|DESCENDING)/i.test(e.sortOrder.toString());return t&&n};return Array.isArray(e)?e.every((function(e){return"object"===r()(e)&&t(e)})):"object"===r()(e)&&t(e)}function l(e){return"string"==typeof e||!!Array.isArray(e)&&e.every((function(e){return"string"==typeof e}))}},18:function(e,t,n){"use strict";var a=n(0),r=n(61);t.a=function(){return Object(a.useContext)(r.b)}},186:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M0 0h2v7H0zm0 10h2v2H0z",fill:"currentColor",fillRule:"evenodd"});t.a=function SvgWarningIcon(e){return a.createElement("svg",r({viewBox:"0 0 2 12"},e),i)}},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a="core/modules",r="insufficient_module_dependencies"},194:function(e,t,n){"use strict";(function(e){var a=n(15),r=n.n(a),i=n(195),o=n.n(i),c=n(1),l=n.n(c),s=n(0),u=n(2),d=n(266),m=n(423),g=n(424),f=n(10);function ReportErrorButton(t){var n=t.message,a=t.componentStack,i=Object(s.useState)(!1),c=r()(i,2),l=c[0],p=c[1];return e.createElement(f.Button,{"aria-label":l?Object(u.__)("Error message copied to clipboard. Click to copy the error message again.","google-site-kit"):void 0,onClick:function(){o()("`".concat(n,"\n").concat(a,"`")),p(!0)},trailingIcon:e.createElement(d.a,{className:"mdc-button__icon",icon:l?m.a:g.a})},l?Object(u.__)("Copied to clipboard","google-site-kit"):Object(u.__)("Copy error contents","google-site-kit"))}ReportErrorButton.propTypes={message:l.a.string,componentStack:l.a.string},t.a=ReportErrorButton}).call(this,n(4))},197:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerNotification}));var a=n(21),r=n.n(a),i=n(6),o=n.n(i),c=n(5),l=n.n(c),s=n(16),u=n.n(s),d=n(15),m=n.n(d),g=n(1),f=n.n(g),p=n(11),y=n.n(p),b=n(206),h=n(240),v=n(81),T=n(0),O=n(106),k=n(3),E=n(17),N=n(93),j=n(37),S=n(24),_=n(211),A=n(213),I=n(212),w=n(226),C=n(227),Z=n(86),M=n(136),D=n(130),P=n(32),L=n(228),G=n(79);function BannerNotification(t){var n,a=t.badgeLabel,i=t.children,c=t.className,s=void 0===c?"":c,d=t.ctaLabel,g=t.ctaLink,f=t.ctaTarget,p=t.description,R=t.dismiss,x=t.dismissExpires,B=void 0===x?0:x,U=t.format,W=void 0===U?"":U,V=t.id,F=t.isDismissible,z=void 0===F||F,H=t.learnMoreDescription,q=t.learnMoreLabel,K=t.learnMoreURL,J=t.learnMoreTarget,Y=void 0===J?Z.a.EXTERNAL:J,$=t.logo,X=t.module,Q=t.moduleName,ee=t.onCTAClick,te=t.onView,ne=t.onDismiss,ae=t.onLearnMoreClick,re=t.showOnce,ie=void 0!==re&&re,oe=t.SmallImageSVG,ce=t.title,le=t.type,se=t.WinImageSVG,ue=t.showSmallWinImage,de=void 0===ue||ue,me=t.smallWinImageSVGWidth,ge=void 0===me?75:me,fe=t.smallWinImageSVGHeight,pe=void 0===fe?75:fe,ye=t.mediumWinImageSVGWidth,be=void 0===ye?105:ye,he=t.mediumWinImageSVGHeight,ve=void 0===he?105:he,Te=t.rounded,Oe=void 0!==Te&&Te,ke=t.footer,Ee=t.secondaryPane,Ne=t.ctaComponent,je=Object(T.useState)(!1),Se=m()(je,2),_e=Se[0],Ae=Se[1],Ie=Object(T.useState)(!1),we=m()(Ie,2),Ce=we[0],Ze=we[1],Me="notification::dismissed::".concat(V),De=function(){return Object(j.f)(Me,new Date,{ttl:null})},Pe=Object(G.a)(),Le=Object(S.e)(),Ge=Object(b.a)(),Re=Object(T.useState)(!1),xe=m()(Re,2),Be=xe[0],Ue=xe[1],We=Object(T.useRef)(),Ve=Object(h.a)(We,{rootMargin:"".concat(-Object(D.a)(Object(N.c)(Le)),"px 0px 0px 0px"),threshold:0});Object(T.useEffect)((function(){!Be&&(null==Ve?void 0:Ve.isIntersecting)&&("function"==typeof te&&te(),Ue(!0))}),[V,te,Be,Ve]);var Fe=Pe>=600;Object(v.a)(u()(l.a.mark((function e(){var t,n;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(B>0)){e.next=3;break}return e.next=3,$e();case 3:if(!z){e.next=9;break}return e.next=6,Object(j.d)(Me);case 6:t=e.sent,n=t.cacheHit,Ze(n);case 9:if(!ie){e.next=12;break}return e.next=12,De();case 12:case"end":return e.stop()}}),e)}))));var ze=function(){var e=u()(l.a.mark((function e(t){return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),t.preventDefault(),!ne){e.next=5;break}return e.next=5,ne(t);case 5:qe();case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),He=Object(O.a)(g)&&"_blank"!==f,qe=function(){return He||Ae(!0),new Promise((function(e){setTimeout(u()(l.a.mark((function t(){var n;return l.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,De();case 2:Ge()&&Ze(!0),n=new Event("notificationDismissed"),document.dispatchEvent(n),e();case 6:case"end":return t.stop()}}),t)}))),350)}))},Ke=Object(k.useSelect)((function(e){return!!g&&e(P.a).isNavigatingTo(g)})),Je=Object(k.useDispatch)(P.a).navigateTo,Ye=function(){var e=u()(l.a.mark((function e(t){var n,a,r;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),He&&!t.defaultPrevented&&t.preventDefault(),n=!0,!ee){e.next=12;break}return e.next=6,ee(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:a=e.t0,r=a.dismissOnCTAClick,n=void 0===r||r;case 12:if(!z||!n){e.next=15;break}return e.next=15,qe();case 15:He&&Je(g);case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),$e=function(){var e=u()(l.a.mark((function e(){var t,n,a;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(j.d)(Me);case 2:if(t=e.sent,!(n=t.value)){e.next=10;break}if((a=new Date(n)).setSeconds(a.getSeconds()+parseInt(B,10)),!(a<new Date)){e.next=10;break}return e.next=10,Object(j.c)(Me);case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();if(!Ke&&z&&(void 0===Ce||Ce))return null;var Xe=!Ke&&_e?"is-closed":"is-open",Qe=Object(M.d)(W),et=Object(M.c)(W),tt=Object(M.a)(W),nt=Object(M.b)({format:W,hasErrorOrWarning:"win-error"===le||"win-warning"===le,hasSmallImageSVG:!!oe,hasWinImageSVG:!!se});return e.createElement(_.a,{id:V,className:y()(s,(n={},o()(n,"googlesitekit-publisher-win--".concat(W),W),o()(n,"googlesitekit-publisher-win--".concat(le),le),o()(n,"googlesitekit-publisher-win--".concat(Xe),Xe),o()(n,"googlesitekit-publisher-win--rounded",Oe),n)),secondaryPane:Ee,ref:We},$&&e.createElement(C.a,{module:X,moduleName:Q}),oe&&e.createElement(E.a,{size:1,className:"googlesitekit-publisher-win__small-media"},e.createElement(oe,null)),e.createElement(E.a,r()({},nt,tt,{className:"googlesitekit-publisher-win__content"}),e.createElement(A.a,{title:ce,badgeLabel:a,smallWinImageSVGHeight:pe,smallWinImageSVGWidth:ge,winImageFormat:W,WinImageSVG:!Fe&&de?se:void 0}),e.createElement(L.a,{description:p,learnMoreURL:K,learnMoreLabel:q,learnMoreTarget:Y,learnMoreDescription:H,onLearnMoreClick:ae}),i,e.createElement(I.a,{ctaLink:g,ctaLabel:d,ctaComponent:Ne,ctaTarget:f,ctaCallback:Ye,dismissLabel:z?R:void 0,dismissCallback:ze}),ke&&e.createElement("div",{className:"googlesitekit-publisher-win__footer"},ke)),se&&(Fe||!de)&&e.createElement(E.a,r()({},Qe,et,{alignBottom:"larger"===W,className:"googlesitekit-publisher-win__image"}),e.createElement("div",{className:"googlesitekit-publisher-win__image-".concat(W)},e.createElement(se,{style:{maxWidth:be,maxHeight:ve}}))),e.createElement(w.a,{type:le}))}BannerNotification.propTypes={id:f.a.string.isRequired,className:f.a.string,title:f.a.string.isRequired,description:f.a.node,learnMoreURL:f.a.string,learnMoreDescription:f.a.string,learnMoreLabel:f.a.string,learnMoreTarget:f.a.oneOf(Object.values(Z.a)),WinImageSVG:f.a.elementType,SmallImageSVG:f.a.elementType,format:f.a.string,ctaLink:f.a.string,ctaLabel:f.a.string,type:f.a.string,dismiss:f.a.string,isDismissible:f.a.bool,logo:f.a.bool,module:f.a.string,moduleName:f.a.string,dismissExpires:f.a.number,showOnce:f.a.bool,onCTAClick:f.a.func,onView:f.a.func,onDismiss:f.a.func,onLearnMoreClick:f.a.func,badgeLabel:f.a.string,rounded:f.a.bool,footer:f.a.node,secondaryPane:f.a.node,showSmallWinImage:f.a.bool,smallWinImageSVGWidth:f.a.number,smallWinImageSVGHeight:f.a.number,mediumWinImageSVGWidth:f.a.number,mediumWinImageSVGHeight:f.a.number}}).call(this,n(4))},198:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleIcon}));var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(1),l=n.n(c),s=n(3),u=n(19);function ModuleIcon(t){var n=t.slug,a=t.size,i=o()(t,["slug","size"]),c=Object(s.useSelect)((function(e){return e(u.a).getModuleIcon(n)}));return c?e.createElement(c,r()({width:a,height:a},i)):null}ModuleIcon.propTypes={slug:l.a.string.isRequired,size:l.a.number},ModuleIcon.defaultProps={size:33}}).call(this,n(4))},199:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SupportLink}));var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(1),l=n.n(c),s=n(3),u=n(13),d=n(20);function SupportLink(t){var n=t.path,a=t.query,i=t.hash,c=o()(t,["path","query","hash"]),l=Object(s.useSelect)((function(e){return e(u.c).getGoogleSupportURL({path:n,query:a,hash:i})}));return e.createElement(d.a,r()({},c,{href:l}))}SupportLink.propTypes={path:l.a.string.isRequired,query:l.a.object,hash:l.a.string}}).call(this,n(4))},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(146),m=n(0),g=n(2),f=n(126),p=n(127),y=n(128),b=n(70),h=n(76),v=Object(m.forwardRef)((function(t,n){var a,i=t["aria-label"],c=t.secondary,s=void 0!==c&&c,u=t.arrow,m=void 0!==u&&u,v=t.back,T=void 0!==v&&v,O=t.caps,k=void 0!==O&&O,E=t.children,N=t.className,j=void 0===N?"":N,S=t.danger,_=void 0!==S&&S,A=t.disabled,I=void 0!==A&&A,w=t.external,C=void 0!==w&&w,Z=t.hideExternalIndicator,M=void 0!==Z&&Z,D=t.href,P=void 0===D?"":D,L=t.inverse,G=void 0!==L&&L,R=t.noFlex,x=void 0!==R&&R,B=t.onClick,U=t.small,W=void 0!==U&&U,V=t.standalone,F=void 0!==V&&V,z=t.linkButton,H=void 0!==z&&z,q=t.to,K=t.leadingIcon,J=t.trailingIcon,Y=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),$=P||q||!B?q?"ROUTER_LINK":C?"EXTERNAL_LINK":"LINK":I?"BUTTON_DISABLED":"BUTTON",X="BUTTON"===$||"BUTTON_DISABLED"===$?"button":"ROUTER_LINK"===$?d.b:"a",Q=("EXTERNAL_LINK"===$&&(a=Object(g._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===$&&(a=Object(g._x)("(disabled)","screen reader text","google-site-kit")),a?i?"".concat(i," ").concat(a):"string"==typeof E?"".concat(E," ").concat(a):void 0:i),ee=K,te=J;return T&&(ee=e.createElement(y.a,{width:14,height:14})),C&&!M&&(te=e.createElement(b.a,{width:14,height:14})),m&&!G&&(te=e.createElement(f.a,{width:14,height:14})),m&&G&&(te=e.createElement(p.a,{width:14,height:14})),e.createElement(X,r()({"aria-label":Q,className:l()("googlesitekit-cta-link",j,{"googlesitekit-cta-link--secondary":s,"googlesitekit-cta-link--inverse":G,"googlesitekit-cta-link--small":W,"googlesitekit-cta-link--caps":k,"googlesitekit-cta-link--danger":_,"googlesitekit-cta-link--disabled":I,"googlesitekit-cta-link--standalone":F,"googlesitekit-cta-link--link-button":H,"googlesitekit-cta-link--no-flex":!!x}),disabled:I,href:"LINK"!==$&&"EXTERNAL_LINK"!==$||I?void 0:P,onClick:B,rel:"EXTERNAL_LINK"===$?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===$?"_blank":void 0,to:q},Y),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},E),!!te&&e.createElement(h.a,{marginLeft:5},te))}));v.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=v}).call(this,n(4))},201:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeSingleRow}));var a=n(1),r=n.n(a),i=n(0);function SettingsNoticeSingleRow(t){var n=t.notice,a=t.LearnMore,r=t.CTA;return e.createElement(i.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),a&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(a,null)),r&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(r,null)))}SettingsNoticeSingleRow.propTypes={notice:r.a.node.isRequired,LearnMore:r.a.elementType,CTA:r.a.elementType}}).call(this,n(4))},210:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeMultiRow}));var a=n(1),r=n.n(a),i=n(0);function SettingsNoticeMultiRow(t){var n=t.notice,a=t.LearnMore,r=t.CTA,o=t.children;return e.createElement(i.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),e.createElement("div",{className:"googlesitekit-settings-notice__inner-row"},e.createElement("div",{className:"googlesitekit-settings-notice__children-container"},o),a&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(a,null)),r&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(r,null))))}SettingsNoticeMultiRow.propTypes={children:r.a.node.isRequired,notice:r.a.node.isRequired,LearnMore:r.a.elementType,CTA:r.a.elementType}}).call(this,n(4))},211:function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(0),l=n(17),s=Object(c.forwardRef)((function(t,n){var a=t.id,r=t.className,i=t.children,s=t.secondaryPane;return e.createElement("section",{id:a,className:o()(r,"googlesitekit-publisher-win"),ref:n},e.createElement(l.e,null,e.createElement(l.k,null,i)),s&&e.createElement(c.Fragment,null,e.createElement("div",{className:"googlesitekit-publisher-win__secondary-pane-divider"}),e.createElement(l.e,{className:"googlesitekit-publisher-win__secondary-pane"},e.createElement(l.k,null,e.createElement(l.a,{className:"googlesitekit-publisher-win__secondary-pane",size:12},s)))))}));s.displayName="Banner",s.propTypes={id:r.a.string,className:r.a.string,secondaryPane:r.a.node},t.a=s}).call(this,n(4))},212:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerActions}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(15),l=n.n(c),s=n(1),u=n.n(s),d=n(206),m=n(0),g=n(3),f=n(10),p=n(32);function BannerActions(t){var n=t.ctaLink,a=t.ctaLabel,i=t.ctaComponent,c=t.ctaTarget,s=t.ctaCallback,u=t.dismissLabel,y=t.dismissCallback,b=Object(m.useState)(!1),h=l()(b,2),v=h[0],T=h[1],O=Object(d.a)(),k=Object(g.useSelect)((function(e){return!!n&&e(p.a).isNavigatingTo(n)})),E=function(){var e=o()(r.a.mark((function e(){var t,n,a,i=arguments;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(T(!0),t=i.length,n=new Array(t),a=0;a<t;a++)n[a]=i[a];return e.next=4,null==s?void 0:s.apply(void 0,n);case 4:O()&&T(!1);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return n||u||i?e.createElement("div",{className:"googlesitekit-publisher-win__actions"},i,a&&e.createElement(f.SpinnerButton,{className:"googlesitekit-notification__cta",href:n,target:c,onClick:E,disabled:v||k,isSaving:v||k},a),u&&e.createElement(f.Button,{tertiary:n||i,onClick:y,disabled:v||k},u)):null}BannerActions.propTypes={ctaLink:u.a.string,ctaLabel:u.a.string,ctaComponent:u.a.element,ctaTarget:u.a.string,ctaCallback:u.a.func,dismissLabel:u.a.string,dismissCallback:u.a.func}}).call(this,n(4))},213:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerTitle}));var a=n(6),r=n.n(a),i=n(1),o=n.n(i),c=n(11),l=n.n(c),s=n(77);function BannerTitle(t){var n=t.title,a=t.badgeLabel,i=t.WinImageSVG,o=t.winImageFormat,c=void 0===o?"":o,u=t.smallWinImageSVGWidth,d=void 0===u?75:u,m=t.smallWinImageSVGHeight,g=void 0===m?75:m;return n?e.createElement("div",{className:"googlesitekit-publisher-win__title-image-wrapper"},e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n,a&&e.createElement(s.a,{label:a})),i&&e.createElement("div",{className:l()(r()({},"googlesitekit-publisher-win__image-".concat(c),c))},e.createElement(i,{width:d,height:g}))):null}BannerTitle.propTypes={title:o.a.string,badgeLabel:o.a.string,WinImageSVG:o.a.elementType,winImageFormat:o.a.string,smallWinImageSVGWidth:o.a.number,smallWinImageSVGHeight:o.a.number}}).call(this,n(4))},215:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"d",(function(){return i})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var a=n(14),r=[{countryCode:"AF",displayName:"Afghanistan",defaultTimeZoneId:"Asia/Kabul",timeZone:[{timeZoneId:"Asia/Kabul",displayName:"(GMT+04:30) Afghanistan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"AL",displayName:"Albania",defaultTimeZoneId:"Europe/Tirane",timeZone:[{timeZoneId:"Europe/Tirane",displayName:"(GMT+02:00) Albania Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"DZ",displayName:"Algeria",defaultTimeZoneId:"Africa/Algiers",timeZone:[{timeZoneId:"Africa/Algiers",displayName:"(GMT+01:00) Algeria Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"AS",displayName:"American Samoa",defaultTimeZoneId:"Pacific/Pago_Pago",timeZone:[{timeZoneId:"Pacific/Pago_Pago",displayName:"(GMT-11:00) American Samoa Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"AD",displayName:"Andorra",defaultTimeZoneId:"Europe/Andorra",timeZone:[{timeZoneId:"Europe/Andorra",displayName:"(GMT+02:00) Andorra Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"AQ",displayName:"Antarctica",defaultTimeZoneId:"Antarctica/Palmer",timeZone:[{timeZoneId:"Antarctica/Palmer",displayName:"(GMT-03:00) Palmer Time"},{timeZoneId:"Antarctica/Rothera",displayName:"(GMT-03:00) Rothera Time"},{timeZoneId:"Antarctica/Syowa",displayName:"(GMT+03:00) Syowa Time"},{timeZoneId:"Antarctica/Mawson",displayName:"(GMT+05:00) Mawson Time"},{timeZoneId:"Antarctica/Vostok",displayName:"(GMT+06:00) Vostok Time"},{timeZoneId:"Antarctica/Davis",displayName:"(GMT+07:00) Davis Time"},{timeZoneId:"Antarctica/Casey",displayName:"(GMT+08:00) Casey Time"},{timeZoneId:"Antarctica/DumontDUrville",displayName:"(GMT+10:00) Dumont d’Urville Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"AR",displayName:"Argentina",defaultTimeZoneId:"America/Buenos_Aires",timeZone:[{timeZoneId:"America/Buenos_Aires",displayName:"(GMT-03:00) Buenos Aires Time"},{timeZoneId:"America/Cordoba",displayName:"(GMT-03:00) Cordoba Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"AM",displayName:"Armenia",defaultTimeZoneId:"Asia/Yerevan",timeZone:[{timeZoneId:"Asia/Yerevan",displayName:"(GMT+04:00) Armenia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"AU",displayName:"Australia",defaultTimeZoneId:"Australia/Perth",timeZone:[{timeZoneId:"Australia/Perth",displayName:"(GMT+08:00) Perth Time"},{timeZoneId:"Australia/Adelaide",displayName:"(GMT+09:30) Adelaide Time"},{timeZoneId:"Australia/Darwin",displayName:"(GMT+09:30) Darwin Time"},{timeZoneId:"Australia/Brisbane",displayName:"(GMT+10:00) Brisbane Time"},{timeZoneId:"Australia/Hobart",displayName:"(GMT+10:00) Hobart Time"},{timeZoneId:"Australia/Melbourne",displayName:"(GMT+10:00) Melbourne Time"},{timeZoneId:"Australia/Sydney",displayName:"(GMT+10:00) Sydney Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"AT",displayName:"Austria",defaultTimeZoneId:"Europe/Vienna",timeZone:[{timeZoneId:"Europe/Vienna",displayName:"(GMT+02:00) Austria Time"}],tosLocale:{language:"de",country:"DE"}},{countryCode:"AZ",displayName:"Azerbaijan",defaultTimeZoneId:"Asia/Baku",timeZone:[{timeZoneId:"Asia/Baku",displayName:"(GMT+04:00) Azerbaijan Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"BS",displayName:"Bahamas",defaultTimeZoneId:"America/Nassau",timeZone:[{timeZoneId:"America/Nassau",displayName:"(GMT-04:00) Bahamas Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"BD",displayName:"Bangladesh",defaultTimeZoneId:"Asia/Dhaka",timeZone:[{timeZoneId:"Asia/Dhaka",displayName:"(GMT+06:00) Bangladesh Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"BB",displayName:"Barbados",defaultTimeZoneId:"America/Barbados",timeZone:[{timeZoneId:"America/Barbados",displayName:"(GMT-04:00) Barbados Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"BY",displayName:"Belarus",defaultTimeZoneId:"Europe/Minsk",timeZone:[{timeZoneId:"Europe/Minsk",displayName:"(GMT+03:00) Belarus Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"BE",displayName:"Belgium",defaultTimeZoneId:"Europe/Brussels",timeZone:[{timeZoneId:"Europe/Brussels",displayName:"(GMT+02:00) Belgium Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"BZ",displayName:"Belize",defaultTimeZoneId:"America/Belize",timeZone:[{timeZoneId:"America/Belize",displayName:"(GMT-06:00) Belize Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"BM",displayName:"Bermuda",defaultTimeZoneId:"Atlantic/Bermuda",timeZone:[{timeZoneId:"Atlantic/Bermuda",displayName:"(GMT-03:00) Bermuda Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"BT",displayName:"Bhutan",defaultTimeZoneId:"Asia/Thimphu",timeZone:[{timeZoneId:"Asia/Thimphu",displayName:"(GMT+06:00) Bhutan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"BO",displayName:"Bolivia",defaultTimeZoneId:"America/La_Paz",timeZone:[{timeZoneId:"America/La_Paz",displayName:"(GMT-04:00) Bolivia Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"BA",displayName:"Bosnia & Herzegovina",defaultTimeZoneId:"Europe/Sarajevo",timeZone:[{timeZoneId:"Europe/Sarajevo",displayName:"(GMT+02:00) Bosnia & Herzegovina Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"BR",displayName:"Brazil",defaultTimeZoneId:"America/Rio_Branco",timeZone:[{timeZoneId:"America/Rio_Branco",displayName:"(GMT-05:00) Rio Branco Time"},{timeZoneId:"America/Boa_Vista",displayName:"(GMT-04:00) Boa Vista Time"},{timeZoneId:"America/Campo_Grande",displayName:"(GMT-04:00) Campo Grande Time"},{timeZoneId:"America/Cuiaba",displayName:"(GMT-04:00) Cuiaba Time"},{timeZoneId:"America/Manaus",displayName:"(GMT-04:00) Manaus Time"},{timeZoneId:"America/Porto_Velho",displayName:"(GMT-04:00) Porto Velho Time"},{timeZoneId:"America/Araguaina",displayName:"(GMT-03:00) Araguaina Time"},{timeZoneId:"America/Bahia",displayName:"(GMT-03:00) Bahia Time"},{timeZoneId:"America/Belem",displayName:"(GMT-03:00) Belem Time"},{timeZoneId:"America/Fortaleza",displayName:"(GMT-03:00) Fortaleza Time"},{timeZoneId:"America/Maceio",displayName:"(GMT-03:00) Maceio Time"},{timeZoneId:"America/Recife",displayName:"(GMT-03:00) Recife Time"},{timeZoneId:"America/Sao_Paulo",displayName:"(GMT-03:00) Sao Paulo Time"},{timeZoneId:"America/Noronha",displayName:"(GMT-02:00) Noronha Time"}],tosLocale:{language:"pt",country:"BR"}},{countryCode:"IO",displayName:"British Indian Ocean Territory",defaultTimeZoneId:"Indian/Chagos",timeZone:[{timeZoneId:"Indian/Chagos",displayName:"(GMT+06:00) British Indian Ocean Territory Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"BN",displayName:"Brunei",defaultTimeZoneId:"Asia/Brunei",timeZone:[{timeZoneId:"Asia/Brunei",displayName:"(GMT+08:00) Brunei Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"BG",displayName:"Bulgaria",defaultTimeZoneId:"Europe/Sofia",timeZone:[{timeZoneId:"Europe/Sofia",displayName:"(GMT+03:00) Bulgaria Time"}],tosLocale:{language:"bg",country:"BG"}},{countryCode:"CA",displayName:"Canada",defaultTimeZoneId:"America/Dawson",timeZone:[{timeZoneId:"America/Dawson",displayName:"(GMT-07:00) Dawson Time"},{timeZoneId:"America/Vancouver",displayName:"(GMT-07:00) Vancouver Time"},{timeZoneId:"America/Whitehorse",displayName:"(GMT-07:00) Whitehorse Time"},{timeZoneId:"America/Edmonton",displayName:"(GMT-06:00) Edmonton Time"},{timeZoneId:"America/Yellowknife",displayName:"(GMT-06:00) Yellowknife Time"},{timeZoneId:"America/Dawson_Creek",displayName:"(GMT-07:00) Dawson Creek Time"},{timeZoneId:"America/Winnipeg",displayName:"(GMT-05:00) Winnipeg Time"},{timeZoneId:"America/Regina",displayName:"(GMT-06:00) Regina Time"},{timeZoneId:"America/Iqaluit",displayName:"(GMT-04:00) Iqaluit Time"},{timeZoneId:"America/Toronto",displayName:"(GMT-04:00) Toronto Time"},{timeZoneId:"America/Halifax",displayName:"(GMT-03:00) Halifax Time"},{timeZoneId:"America/St_Johns",displayName:"(GMT-02:30) St. John’s Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"CV",displayName:"Cape Verde",defaultTimeZoneId:"Atlantic/Cape_Verde",timeZone:[{timeZoneId:"Atlantic/Cape_Verde",displayName:"(GMT-01:00) Cape Verde Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"TD",displayName:"Chad",defaultTimeZoneId:"Africa/Ndjamena",timeZone:[{timeZoneId:"Africa/Ndjamena",displayName:"(GMT+01:00) Chad Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"CL",displayName:"Chile",defaultTimeZoneId:"Pacific/Easter",timeZone:[{timeZoneId:"Pacific/Easter",displayName:"(GMT-06:00) Easter Time"},{timeZoneId:"America/Santiago",displayName:"(GMT-04:00) Chile Time"},{timeZoneId:"America/Punta_Arenas",displayName:"(GMT-03:00) Punta Arenas Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"CN",displayName:"China",defaultTimeZoneId:"Asia/Shanghai",timeZone:[{timeZoneId:"Asia/Shanghai",displayName:"(GMT+08:00) China Time"}],tosLocale:{language:"zh",country:"CN"}},{countryCode:"CX",displayName:"Christmas Island",defaultTimeZoneId:"Indian/Christmas",timeZone:[{timeZoneId:"Indian/Christmas",displayName:"(GMT+07:00) Christmas Island Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"CC",displayName:"Cocos (Keeling) Islands",defaultTimeZoneId:"Indian/Cocos",timeZone:[{timeZoneId:"Indian/Cocos",displayName:"(GMT+06:30) Cocos (Keeling) Islands Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"CO",displayName:"Colombia",defaultTimeZoneId:"America/Bogota",timeZone:[{timeZoneId:"America/Bogota",displayName:"(GMT-05:00) Colombia Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"CK",displayName:"Cook Islands",defaultTimeZoneId:"Pacific/Rarotonga",timeZone:[{timeZoneId:"Pacific/Rarotonga",displayName:"(GMT-10:00) Cook Islands Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"CR",displayName:"Costa Rica",defaultTimeZoneId:"America/Costa_Rica",timeZone:[{timeZoneId:"America/Costa_Rica",displayName:"(GMT-06:00) Costa Rica Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"CI",displayName:"Côte d’Ivoire",defaultTimeZoneId:"Africa/Abidjan",timeZone:[{timeZoneId:"Africa/Abidjan",displayName:"(GMT+00:00) Côte d’Ivoire Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"HR",displayName:"Croatia",defaultTimeZoneId:"Europe/Zagreb",timeZone:[{timeZoneId:"Europe/Zagreb",displayName:"(GMT+02:00) Croatia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"CU",displayName:"Cuba",defaultTimeZoneId:"America/Havana",timeZone:[{timeZoneId:"America/Havana",displayName:"(GMT-04:00) Cuba Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"CW",displayName:"Curaçao",defaultTimeZoneId:"America/Curacao",timeZone:[{timeZoneId:"America/Curacao",displayName:"(GMT-04:00) Curaçao Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"CY",displayName:"Cyprus",defaultTimeZoneId:"Asia/Nicosia",timeZone:[{timeZoneId:"Asia/Nicosia",displayName:"(GMT+03:00) Nicosia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"CZ",displayName:"Czechia",defaultTimeZoneId:"Europe/Prague",timeZone:[{timeZoneId:"Europe/Prague",displayName:"(GMT+02:00) Czechia Time"}],tosLocale:{language:"cs",country:"CZ"}},{countryCode:"DK",displayName:"Denmark",defaultTimeZoneId:"Europe/Copenhagen",timeZone:[{timeZoneId:"Europe/Copenhagen",displayName:"(GMT+02:00) Denmark Time"}],tosLocale:{language:"da",country:"DK"}},{countryCode:"DO",displayName:"Dominican Republic",defaultTimeZoneId:"America/Santo_Domingo",timeZone:[{timeZoneId:"America/Santo_Domingo",displayName:"(GMT-04:00) Dominican Republic Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"EC",displayName:"Ecuador",defaultTimeZoneId:"Pacific/Galapagos",timeZone:[{timeZoneId:"Pacific/Galapagos",displayName:"(GMT-06:00) Galapagos Time"},{timeZoneId:"America/Guayaquil",displayName:"(GMT-05:00) Ecuador Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"EG",displayName:"Egypt",defaultTimeZoneId:"Africa/Cairo",timeZone:[{timeZoneId:"Africa/Cairo",displayName:"(GMT+02:00) Egypt Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SV",displayName:"El Salvador",defaultTimeZoneId:"America/El_Salvador",timeZone:[{timeZoneId:"America/El_Salvador",displayName:"(GMT-06:00) El Salvador Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"EE",displayName:"Estonia",defaultTimeZoneId:"Europe/Tallinn",timeZone:[{timeZoneId:"Europe/Tallinn",displayName:"(GMT+03:00) Estonia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"FK",displayName:"Falkland Islands (Islas Malvinas)",defaultTimeZoneId:"Atlantic/Stanley",timeZone:[{timeZoneId:"Atlantic/Stanley",displayName:"(GMT-03:00) Falkland Islands (Islas Malvinas) Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"FO",displayName:"Faroe Islands",defaultTimeZoneId:"Atlantic/Faeroe",timeZone:[{timeZoneId:"Atlantic/Faeroe",displayName:"(GMT+01:00) Faroe Islands Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"FJ",displayName:"Fiji",defaultTimeZoneId:"Pacific/Fiji",timeZone:[{timeZoneId:"Pacific/Fiji",displayName:"(GMT+12:00) Fiji Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"FI",displayName:"Finland",defaultTimeZoneId:"Europe/Helsinki",timeZone:[{timeZoneId:"Europe/Helsinki",displayName:"(GMT+03:00) Finland Time"}],tosLocale:{language:"fi",country:"FI"}},{countryCode:"FR",displayName:"France",defaultTimeZoneId:"Europe/Paris",timeZone:[{timeZoneId:"Europe/Paris",displayName:"(GMT+02:00) France Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"GF",displayName:"French Guiana",defaultTimeZoneId:"America/Cayenne",timeZone:[{timeZoneId:"America/Cayenne",displayName:"(GMT-03:00) French Guiana Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"PF",displayName:"French Polynesia",defaultTimeZoneId:"Pacific/Tahiti",timeZone:[{timeZoneId:"Pacific/Tahiti",displayName:"(GMT-10:00) Tahiti Time"},{timeZoneId:"Pacific/Marquesas",displayName:"(GMT-09:30) Marquesas Time"},{timeZoneId:"Pacific/Gambier",displayName:"(GMT-09:00) Gambier Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"TF",displayName:"French Southern Territories",defaultTimeZoneId:"Indian/Kerguelen",timeZone:[{timeZoneId:"Indian/Kerguelen",displayName:"(GMT+05:00) French Southern Territories Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"GE",displayName:"Georgia",defaultTimeZoneId:"Asia/Tbilisi",timeZone:[{timeZoneId:"Asia/Tbilisi",displayName:"(GMT+04:00) Georgia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"DE",displayName:"Germany",defaultTimeZoneId:"Europe/Berlin",timeZone:[{timeZoneId:"Europe/Berlin",displayName:"(GMT+02:00) Germany Time"}],tosLocale:{language:"de",country:"DE"}},{countryCode:"GH",displayName:"Ghana",defaultTimeZoneId:"Africa/Accra",timeZone:[{timeZoneId:"Africa/Accra",displayName:"(GMT+00:00) Ghana Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"GI",displayName:"Gibraltar",defaultTimeZoneId:"Europe/Gibraltar",timeZone:[{timeZoneId:"Europe/Gibraltar",displayName:"(GMT+02:00) Gibraltar Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"GR",displayName:"Greece",defaultTimeZoneId:"Europe/Athens",timeZone:[{timeZoneId:"Europe/Athens",displayName:"(GMT+03:00) Greece Time"}],tosLocale:{language:"el",country:"GR"}},{countryCode:"GL",displayName:"Greenland",defaultTimeZoneId:"America/Thule",timeZone:[{timeZoneId:"America/Thule",displayName:"(GMT-03:00) Thule Time"},{timeZoneId:"America/Godthab",displayName:"(GMT-02:00) Nuuk Time"},{timeZoneId:"America/Scoresbysund",displayName:"(GMT+00:00) Ittoqqortoormiit Time"},{timeZoneId:"America/Danmarkshavn",displayName:"(GMT+00:00) Danmarkshavn Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"GU",displayName:"Guam",defaultTimeZoneId:"Pacific/Guam",timeZone:[{timeZoneId:"Pacific/Guam",displayName:"(GMT+10:00) Guam Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"GT",displayName:"Guatemala",defaultTimeZoneId:"America/Guatemala",timeZone:[{timeZoneId:"America/Guatemala",displayName:"(GMT-06:00) Guatemala Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"GW",displayName:"Guinea-Bissau",defaultTimeZoneId:"Africa/Bissau",timeZone:[{timeZoneId:"Africa/Bissau",displayName:"(GMT+00:00) Guinea-Bissau Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"GY",displayName:"Guyana",defaultTimeZoneId:"America/Guyana",timeZone:[{timeZoneId:"America/Guyana",displayName:"(GMT-04:00) Guyana Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"HT",displayName:"Haiti",defaultTimeZoneId:"America/Port-au-Prince",timeZone:[{timeZoneId:"America/Port-au-Prince",displayName:"(GMT-04:00) Haiti Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"HN",displayName:"Honduras",defaultTimeZoneId:"America/Tegucigalpa",timeZone:[{timeZoneId:"America/Tegucigalpa",displayName:"(GMT-06:00) Honduras Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"HK",displayName:"Hong Kong",defaultTimeZoneId:"Asia/Hong_Kong",timeZone:[{timeZoneId:"Asia/Hong_Kong",displayName:"(GMT+08:00) Hong Kong Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"HU",displayName:"Hungary",defaultTimeZoneId:"Europe/Budapest",timeZone:[{timeZoneId:"Europe/Budapest",displayName:"(GMT+02:00) Hungary Time"}],tosLocale:{language:"hu",country:"HU"}},{countryCode:"IS",displayName:"Iceland",defaultTimeZoneId:"Atlantic/Reykjavik",timeZone:[{timeZoneId:"Atlantic/Reykjavik",displayName:"(GMT+00:00) Iceland Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"IN",displayName:"India",defaultTimeZoneId:"Asia/Calcutta",timeZone:[{timeZoneId:"Asia/Calcutta",displayName:"(GMT+05:30) India Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"ID",displayName:"Indonesia",defaultTimeZoneId:"Asia/Jakarta",timeZone:[{timeZoneId:"Asia/Jakarta",displayName:"(GMT+07:00) Jakarta Time"},{timeZoneId:"Asia/Makassar",displayName:"(GMT+08:00) Makassar Time"},{timeZoneId:"Asia/Jayapura",displayName:"(GMT+09:00) Jayapura Time"}],tosLocale:{language:"in",country:"ID"}},{countryCode:"IR",displayName:"Iran",defaultTimeZoneId:"Asia/Tehran",timeZone:[{timeZoneId:"Asia/Tehran",displayName:"(GMT+04:30) Iran Time"}]},{countryCode:"IQ",displayName:"Iraq",defaultTimeZoneId:"Asia/Baghdad",timeZone:[{timeZoneId:"Asia/Baghdad",displayName:"(GMT+03:00) Iraq Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"IE",displayName:"Ireland",defaultTimeZoneId:"Europe/Dublin",timeZone:[{timeZoneId:"Europe/Dublin",displayName:"(GMT+01:00) Ireland Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"IL",displayName:"Israel",defaultTimeZoneId:"Asia/Jerusalem",timeZone:[{timeZoneId:"Asia/Jerusalem",displayName:"(GMT+03:00) Israel Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"IT",displayName:"Italy",defaultTimeZoneId:"Europe/Rome",timeZone:[{timeZoneId:"Europe/Rome",displayName:"(GMT+02:00) Italy Time"}],tosLocale:{language:"it",country:"IT"}},{countryCode:"JM",displayName:"Jamaica",defaultTimeZoneId:"America/Jamaica",timeZone:[{timeZoneId:"America/Jamaica",displayName:"(GMT-05:00) Jamaica Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"JP",displayName:"Japan",defaultTimeZoneId:"Asia/Tokyo",timeZone:[{timeZoneId:"Asia/Tokyo",displayName:"(GMT+09:00) Japan Time"}],tosLocale:{language:"ja",country:"JP"}},{countryCode:"JO",displayName:"Jordan",defaultTimeZoneId:"Asia/Amman",timeZone:[{timeZoneId:"Asia/Amman",displayName:"(GMT+03:00) Jordan Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"KZ",displayName:"Kazakhstan",defaultTimeZoneId:"Asia/Aqtau",timeZone:[{timeZoneId:"Asia/Aqtau",displayName:"(GMT+05:00) Aqtau Time"},{timeZoneId:"Asia/Aqtobe",displayName:"(GMT+05:00) Aqtobe Time"},{timeZoneId:"Asia/Almaty",displayName:"(GMT+06:00) Almaty Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"KE",displayName:"Kenya",defaultTimeZoneId:"Africa/Nairobi",timeZone:[{timeZoneId:"Africa/Nairobi",displayName:"(GMT+03:00) Kenya Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"KI",displayName:"Kiribati",defaultTimeZoneId:"Pacific/Tarawa",timeZone:[{timeZoneId:"Pacific/Tarawa",displayName:"(GMT+12:00) Tarawa Time"},{timeZoneId:"Pacific/Enderbury",displayName:"(GMT+13:00) Enderbury Time"},{timeZoneId:"Pacific/Kiritimati",displayName:"(GMT+14:00) Kiritimati Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"KG",displayName:"Kyrgyzstan",defaultTimeZoneId:"Asia/Bishkek",timeZone:[{timeZoneId:"Asia/Bishkek",displayName:"(GMT+06:00) Kyrgyzstan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"LV",displayName:"Latvia",defaultTimeZoneId:"Europe/Riga",timeZone:[{timeZoneId:"Europe/Riga",displayName:"(GMT+03:00) Latvia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"LB",displayName:"Lebanon",defaultTimeZoneId:"Asia/Beirut",timeZone:[{timeZoneId:"Asia/Beirut",displayName:"(GMT+03:00) Lebanon Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"LR",displayName:"Liberia",defaultTimeZoneId:"Africa/Monrovia",timeZone:[{timeZoneId:"Africa/Monrovia",displayName:"(GMT+00:00) Liberia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"LY",displayName:"Libya",defaultTimeZoneId:"Africa/Tripoli",timeZone:[{timeZoneId:"Africa/Tripoli",displayName:"(GMT+02:00) Libya Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"LT",displayName:"Lithuania",defaultTimeZoneId:"Europe/Vilnius",timeZone:[{timeZoneId:"Europe/Vilnius",displayName:"(GMT+03:00) Lithuania Time"}],tosLocale:{language:"lt",country:"LT"}},{countryCode:"LU",displayName:"Luxembourg",defaultTimeZoneId:"Europe/Luxembourg",timeZone:[{timeZoneId:"Europe/Luxembourg",displayName:"(GMT+02:00) Luxembourg Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MO",displayName:"Macao",defaultTimeZoneId:"Asia/Macau",timeZone:[{timeZoneId:"Asia/Macau",displayName:"(GMT+08:00) Macao Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"MY",displayName:"Malaysia",defaultTimeZoneId:"Asia/Kuala_Lumpur",timeZone:[{timeZoneId:"Asia/Kuala_Lumpur",displayName:"(GMT+08:00) Malaysia Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"MV",displayName:"Maldives",defaultTimeZoneId:"Indian/Maldives",timeZone:[{timeZoneId:"Indian/Maldives",displayName:"(GMT+05:00) Maldives Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"MT",displayName:"Malta",defaultTimeZoneId:"Europe/Malta",timeZone:[{timeZoneId:"Europe/Malta",displayName:"(GMT+02:00) Malta Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MH",displayName:"Marshall Islands",defaultTimeZoneId:"Pacific/Kwajalein",timeZone:[{timeZoneId:"Pacific/Kwajalein",displayName:"(GMT+12:00) Kwajalein Time"},{timeZoneId:"Pacific/Majuro",displayName:"(GMT+12:00) Marshall Islands Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"MQ",displayName:"Martinique",defaultTimeZoneId:"America/Martinique",timeZone:[{timeZoneId:"America/Martinique",displayName:"(GMT-04:00) Martinique Time"}],tosLocale:{language:"fr",country:"FR"}},{countryCode:"MU",displayName:"Mauritius",defaultTimeZoneId:"Indian/Mauritius",timeZone:[{timeZoneId:"Indian/Mauritius",displayName:"(GMT+04:00) Mauritius Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MX",displayName:"Mexico",defaultTimeZoneId:"America/Tijuana",timeZone:[{timeZoneId:"America/Tijuana",displayName:"(GMT-07:00) Tijuana Time"},{timeZoneId:"America/Mazatlan",displayName:"(GMT-06:00) Mazatlan Time"},{timeZoneId:"America/Hermosillo",displayName:"(GMT-07:00) Hermosillo Time"},{timeZoneId:"America/Mexico_City",displayName:"(GMT-05:00) Mexico City Time"},{timeZoneId:"America/Cancun",displayName:"(GMT-05:00) Cancun Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"FM",displayName:"Micronesia",defaultTimeZoneId:"Pacific/Truk",timeZone:[{timeZoneId:"Pacific/Truk",displayName:"(GMT+10:00) Chuuk Time"},{timeZoneId:"Pacific/Kosrae",displayName:"(GMT+11:00) Kosrae Time"},{timeZoneId:"Pacific/Ponape",displayName:"(GMT+11:00) Pohnpei Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"MD",displayName:"Moldova",defaultTimeZoneId:"Europe/Chisinau",timeZone:[{timeZoneId:"Europe/Chisinau",displayName:"(GMT+03:00) Moldova Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MC",displayName:"Monaco",defaultTimeZoneId:"Europe/Monaco",timeZone:[{timeZoneId:"Europe/Monaco",displayName:"(GMT+02:00) Monaco Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MN",displayName:"Mongolia",defaultTimeZoneId:"Asia/Hovd",timeZone:[{timeZoneId:"Asia/Hovd",displayName:"(GMT+07:00) Hovd Time"},{timeZoneId:"Asia/Choibalsan",displayName:"(GMT+08:00) Choibalsan Time"},{timeZoneId:"Asia/Ulaanbaatar",displayName:"(GMT+08:00) Ulaanbaatar Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"MA",displayName:"Morocco",defaultTimeZoneId:"Africa/Casablanca",timeZone:[{timeZoneId:"Africa/Casablanca",displayName:"(GMT+01:00) Morocco Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MZ",displayName:"Mozambique",defaultTimeZoneId:"Africa/Maputo",timeZone:[{timeZoneId:"Africa/Maputo",displayName:"(GMT+02:00) Mozambique Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"MM",displayName:"Myanmar (Burma)",defaultTimeZoneId:"Asia/Rangoon",timeZone:[{timeZoneId:"Asia/Rangoon",displayName:"(GMT+06:30) Myanmar (Burma) Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"NA",displayName:"Namibia",defaultTimeZoneId:"Africa/Windhoek",timeZone:[{timeZoneId:"Africa/Windhoek",displayName:"(GMT+02:00) Namibia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"NR",displayName:"Nauru",defaultTimeZoneId:"Pacific/Nauru",timeZone:[{timeZoneId:"Pacific/Nauru",displayName:"(GMT+12:00) Nauru Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"NP",displayName:"Nepal",defaultTimeZoneId:"Asia/Katmandu",timeZone:[{timeZoneId:"Asia/Katmandu",displayName:"(GMT+05:45) Nepal Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"NL",displayName:"Netherlands",defaultTimeZoneId:"Europe/Amsterdam",timeZone:[{timeZoneId:"Europe/Amsterdam",displayName:"(GMT+02:00) Netherlands Time"}],tosLocale:{language:"nl",country:"NL"}},{countryCode:"NC",displayName:"New Caledonia",defaultTimeZoneId:"Pacific/Noumea",timeZone:[{timeZoneId:"Pacific/Noumea",displayName:"(GMT+11:00) New Caledonia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"NZ",displayName:"New Zealand",defaultTimeZoneId:"Pacific/Auckland",timeZone:[{timeZoneId:"Pacific/Auckland",displayName:"(GMT+12:00) New Zealand Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"NI",displayName:"Nicaragua",defaultTimeZoneId:"America/Managua",timeZone:[{timeZoneId:"America/Managua",displayName:"(GMT-06:00) Nicaragua Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"NG",displayName:"Nigeria",defaultTimeZoneId:"Africa/Lagos",timeZone:[{timeZoneId:"Africa/Lagos",displayName:"(GMT+01:00) Nigeria Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"NU",displayName:"Niue",defaultTimeZoneId:"Pacific/Niue",timeZone:[{timeZoneId:"Pacific/Niue",displayName:"(GMT-11:00) Niue Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"NF",displayName:"Norfolk Island",defaultTimeZoneId:"Pacific/Norfolk",timeZone:[{timeZoneId:"Pacific/Norfolk",displayName:"(GMT+11:00) Norfolk Island Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"KP",displayName:"North Korea",defaultTimeZoneId:"Asia/Pyongyang",timeZone:[{timeZoneId:"Asia/Pyongyang",displayName:"(GMT+09:00) North Korea Time"}]},{countryCode:"MK",displayName:"North Macedonia",defaultTimeZoneId:"Europe/Skopje",timeZone:[{timeZoneId:"Europe/Skopje",displayName:"(GMT+02:00) North Macedonia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"NO",displayName:"Norway",defaultTimeZoneId:"Europe/Oslo",timeZone:[{timeZoneId:"Europe/Oslo",displayName:"(GMT+02:00) Norway Time"}],tosLocale:{language:"no",country:"NO"}},{countryCode:"PK",displayName:"Pakistan",defaultTimeZoneId:"Asia/Karachi",timeZone:[{timeZoneId:"Asia/Karachi",displayName:"(GMT+05:00) Pakistan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"PW",displayName:"Palau",defaultTimeZoneId:"Pacific/Palau",timeZone:[{timeZoneId:"Pacific/Palau",displayName:"(GMT+09:00) Palau Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"PS",displayName:"Palestine",defaultTimeZoneId:"Asia/Gaza",timeZone:[{timeZoneId:"Asia/Gaza",displayName:"(GMT+03:00) Gaza Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"PA",displayName:"Panama",defaultTimeZoneId:"America/Panama",timeZone:[{timeZoneId:"America/Panama",displayName:"(GMT-05:00) Panama Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"PG",displayName:"Papua New Guinea",defaultTimeZoneId:"Pacific/Port_Moresby",timeZone:[{timeZoneId:"Pacific/Port_Moresby",displayName:"(GMT+10:00) Port Moresby Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"PY",displayName:"Paraguay",defaultTimeZoneId:"America/Asuncion",timeZone:[{timeZoneId:"America/Asuncion",displayName:"(GMT-04:00) Paraguay Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"PE",displayName:"Peru",defaultTimeZoneId:"America/Lima",timeZone:[{timeZoneId:"America/Lima",displayName:"(GMT-05:00) Peru Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"PH",displayName:"Philippines",defaultTimeZoneId:"Asia/Manila",timeZone:[{timeZoneId:"Asia/Manila",displayName:"(GMT+08:00) Philippines Time"}],tosLocale:{language:"tl",country:"PH"}},{countryCode:"PN",displayName:"Pitcairn Islands",defaultTimeZoneId:"Pacific/Pitcairn",timeZone:[{timeZoneId:"Pacific/Pitcairn",displayName:"(GMT-08:00) Pitcairn Islands Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"PL",displayName:"Poland",defaultTimeZoneId:"Europe/Warsaw",timeZone:[{timeZoneId:"Europe/Warsaw",displayName:"(GMT+02:00) Poland Time"}],tosLocale:{language:"pl",country:"PL"}},{countryCode:"PT",displayName:"Portugal",defaultTimeZoneId:"Atlantic/Azores",timeZone:[{timeZoneId:"Atlantic/Azores",displayName:"(GMT+00:00) Azores Time"},{timeZoneId:"Europe/Lisbon",displayName:"(GMT+01:00) Portugal Time"}],tosLocale:{language:"pt",country:"PT"}},{countryCode:"PR",displayName:"Puerto Rico",defaultTimeZoneId:"America/Puerto_Rico",timeZone:[{timeZoneId:"America/Puerto_Rico",displayName:"(GMT-04:00) Puerto Rico Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"QA",displayName:"Qatar",defaultTimeZoneId:"Asia/Qatar",timeZone:[{timeZoneId:"Asia/Qatar",displayName:"(GMT+03:00) Qatar Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"RE",displayName:"Réunion",defaultTimeZoneId:"Indian/Reunion",timeZone:[{timeZoneId:"Indian/Reunion",displayName:"(GMT+04:00) Réunion Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"RO",displayName:"Romania",defaultTimeZoneId:"Europe/Bucharest",timeZone:[{timeZoneId:"Europe/Bucharest",displayName:"(GMT+03:00) Romania Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"RU",displayName:"Russia",defaultTimeZoneId:"Europe/Kaliningrad",timeZone:[{timeZoneId:"Europe/Kaliningrad",displayName:"(GMT+02:00) Kaliningrad Time"},{timeZoneId:"Europe/Moscow",displayName:"(GMT+03:00) Moscow Time"},{timeZoneId:"Europe/Samara",displayName:"(GMT+04:00) Samara Time"},{timeZoneId:"Asia/Yekaterinburg",displayName:"(GMT+05:00) Yekaterinburg Time"},{timeZoneId:"Asia/Omsk",displayName:"(GMT+06:00) Omsk Time"},{timeZoneId:"Asia/Krasnoyarsk",displayName:"(GMT+07:00) Krasnoyarsk Time"},{timeZoneId:"Asia/Irkutsk",displayName:"(GMT+08:00) Irkutsk Time"},{timeZoneId:"Asia/Yakutsk",displayName:"(GMT+09:00) Yakutsk Time"},{timeZoneId:"Asia/Vladivostok",displayName:"(GMT+10:00) Vladivostok Time"},{timeZoneId:"Asia/Magadan",displayName:"(GMT+11:00) Magadan Time"},{timeZoneId:"Asia/Kamchatka",displayName:"(GMT+12:00) Kamchatka Time"}],tosLocale:{language:"ru",country:"RU"}},{countryCode:"WS",displayName:"Samoa",defaultTimeZoneId:"Pacific/Apia",timeZone:[{timeZoneId:"Pacific/Apia",displayName:"(GMT+13:00) Samoa Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"SM",displayName:"San Marino",defaultTimeZoneId:"Europe/San_Marino",timeZone:[{timeZoneId:"Europe/San_Marino",displayName:"(GMT+02:00) San Marino Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"ST",displayName:"São Tomé & Príncipe",defaultTimeZoneId:"Africa/Sao_Tome",timeZone:[{timeZoneId:"Africa/Sao_Tome",displayName:"(GMT+00:00) São Tomé & Príncipe Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SA",displayName:"Saudi Arabia",defaultTimeZoneId:"Asia/Riyadh",timeZone:[{timeZoneId:"Asia/Riyadh",displayName:"(GMT+03:00) Saudi Arabia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"RS",displayName:"Serbia",defaultTimeZoneId:"Europe/Belgrade",timeZone:[{timeZoneId:"Europe/Belgrade",displayName:"(GMT+02:00) Serbia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SC",displayName:"Seychelles",defaultTimeZoneId:"Indian/Mahe",timeZone:[{timeZoneId:"Indian/Mahe",displayName:"(GMT+04:00) Seychelles Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SG",displayName:"Singapore",defaultTimeZoneId:"Asia/Singapore",timeZone:[{timeZoneId:"Asia/Singapore",displayName:"(GMT+08:00) Singapore Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"SK",displayName:"Slovakia",defaultTimeZoneId:"Europe/Bratislava",timeZone:[{timeZoneId:"Europe/Bratislava",displayName:"(GMT+02:00) Slovakia Time"}],tosLocale:{language:"sk",country:"SK"}},{countryCode:"SI",displayName:"Slovenia",defaultTimeZoneId:"Europe/Ljubljana",timeZone:[{timeZoneId:"Europe/Ljubljana",displayName:"(GMT+02:00) Slovenia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SB",displayName:"Solomon Islands",defaultTimeZoneId:"Pacific/Guadalcanal",timeZone:[{timeZoneId:"Pacific/Guadalcanal",displayName:"(GMT+11:00) Solomon Islands Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"ZA",displayName:"South Africa",defaultTimeZoneId:"Africa/Johannesburg",timeZone:[{timeZoneId:"Africa/Johannesburg",displayName:"(GMT+02:00) South Africa Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"GS",displayName:"South Georgia & South Sandwich Islands",defaultTimeZoneId:"Atlantic/South_Georgia",timeZone:[{timeZoneId:"Atlantic/South_Georgia",displayName:"(GMT-02:00) South Georgia & South Sandwich Islands Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"KR",displayName:"South Korea",defaultTimeZoneId:"Asia/Seoul",timeZone:[{timeZoneId:"Asia/Seoul",displayName:"(GMT+09:00) South Korea Time"}],tosLocale:{language:"ko",country:"KR"}},{countryCode:"ES",displayName:"Spain",defaultTimeZoneId:"Atlantic/Canary",timeZone:[{timeZoneId:"Atlantic/Canary",displayName:"(GMT+01:00) Canary Time"},{timeZoneId:"Africa/Ceuta",displayName:"(GMT+02:00) Ceuta Time"},{timeZoneId:"Europe/Madrid",displayName:"(GMT+02:00) Spain Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"LK",displayName:"Sri Lanka",defaultTimeZoneId:"Asia/Colombo",timeZone:[{timeZoneId:"Asia/Colombo",displayName:"(GMT+05:30) Sri Lanka Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"PM",displayName:"St. Pierre & Miquelon",defaultTimeZoneId:"America/Miquelon",timeZone:[{timeZoneId:"America/Miquelon",displayName:"(GMT-02:00) St. Pierre & Miquelon Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SD",displayName:"Sudan",defaultTimeZoneId:"Africa/Khartoum",timeZone:[{timeZoneId:"Africa/Khartoum",displayName:"(GMT+02:00) Sudan Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SR",displayName:"Suriname",defaultTimeZoneId:"America/Paramaribo",timeZone:[{timeZoneId:"America/Paramaribo",displayName:"(GMT-03:00) Suriname Time"}],tosLocale:{language:"nl",country:"NL"}},{countryCode:"SJ",displayName:"Svalbard & Jan Mayen",defaultTimeZoneId:"Arctic/Longyearbyen",timeZone:[{timeZoneId:"Arctic/Longyearbyen",displayName:"(GMT+02:00) Svalbard & Jan Mayen Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"SE",displayName:"Sweden",defaultTimeZoneId:"Europe/Stockholm",timeZone:[{timeZoneId:"Europe/Stockholm",displayName:"(GMT+02:00) Sweden Time"}],tosLocale:{language:"sv",country:"SE"}},{countryCode:"CH",displayName:"Switzerland",defaultTimeZoneId:"Europe/Zurich",timeZone:[{timeZoneId:"Europe/Zurich",displayName:"(GMT+02:00) Switzerland Time"}],tosLocale:{language:"de",country:"DE"}},{countryCode:"SY",displayName:"Syria",defaultTimeZoneId:"Asia/Damascus",timeZone:[{timeZoneId:"Asia/Damascus",displayName:"(GMT+03:00) Syria Time"}]},{countryCode:"TW",displayName:"Taiwan",defaultTimeZoneId:"Asia/Taipei",timeZone:[{timeZoneId:"Asia/Taipei",displayName:"(GMT+08:00) Taiwan Time"}],tosLocale:{language:"zh",country:"TW"}},{countryCode:"TJ",displayName:"Tajikistan",defaultTimeZoneId:"Asia/Dushanbe",timeZone:[{timeZoneId:"Asia/Dushanbe",displayName:"(GMT+05:00) Tajikistan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"TH",displayName:"Thailand",defaultTimeZoneId:"Asia/Bangkok",timeZone:[{timeZoneId:"Asia/Bangkok",displayName:"(GMT+07:00) Thailand Time"}],tosLocale:{language:"th",country:"TH"}},{countryCode:"TL",displayName:"Timor-Leste",defaultTimeZoneId:"Asia/Dili",timeZone:[{timeZoneId:"Asia/Dili",displayName:"(GMT+09:00) Timor-Leste Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"TK",displayName:"Tokelau",defaultTimeZoneId:"Pacific/Fakaofo",timeZone:[{timeZoneId:"Pacific/Fakaofo",displayName:"(GMT+13:00) Tokelau Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"TO",displayName:"Tonga",defaultTimeZoneId:"Pacific/Tongatapu",timeZone:[{timeZoneId:"Pacific/Tongatapu",displayName:"(GMT+13:00) Tonga Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"TT",displayName:"Trinidad & Tobago",defaultTimeZoneId:"America/Port_of_Spain",timeZone:[{timeZoneId:"America/Port_of_Spain",displayName:"(GMT-04:00) Trinidad & Tobago Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"TN",displayName:"Tunisia",defaultTimeZoneId:"Africa/Tunis",timeZone:[{timeZoneId:"Africa/Tunis",displayName:"(GMT+01:00) Tunisia Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"TR",displayName:"Turkey",defaultTimeZoneId:"Europe/Istanbul",timeZone:[{timeZoneId:"Europe/Istanbul",displayName:"(GMT+03:00) Turkey Time"}],tosLocale:{language:"tr",country:"TR"}},{countryCode:"TM",displayName:"Turkmenistan",defaultTimeZoneId:"Asia/Ashgabat",timeZone:[{timeZoneId:"Asia/Ashgabat",displayName:"(GMT+05:00) Turkmenistan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"TC",displayName:"Turks & Caicos Islands",defaultTimeZoneId:"America/Grand_Turk",timeZone:[{timeZoneId:"America/Grand_Turk",displayName:"(GMT-04:00) Turks & Caicos Islands Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"TV",displayName:"Tuvalu",defaultTimeZoneId:"Pacific/Funafuti",timeZone:[{timeZoneId:"Pacific/Funafuti",displayName:"(GMT+12:00) Tuvalu Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"UM",displayName:"U.S. Outlying Islands",defaultTimeZoneId:"Pacific/Wake",timeZone:[{timeZoneId:"Pacific/Wake",displayName:"(GMT+12:00) Wake Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"UA",displayName:"Ukraine",defaultTimeZoneId:"Europe/Kiev",timeZone:[{timeZoneId:"Europe/Kiev",displayName:"(GMT+03:00) Ukraine Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"AE",displayName:"United Arab Emirates",defaultTimeZoneId:"Asia/Dubai",timeZone:[{timeZoneId:"Asia/Dubai",displayName:"(GMT+04:00) United Arab Emirates Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"GB",displayName:"United Kingdom",defaultTimeZoneId:"Etc/GMT",timeZone:[{timeZoneId:"Etc/GMT",displayName:"(GMT+00:00) GMT"},{timeZoneId:"Europe/London",displayName:"(GMT+01:00) United Kingdom Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"US",displayName:"United States",defaultTimeZoneId:"America/Los_Angeles",timeZone:[{timeZoneId:"Pacific/Honolulu",displayName:"(GMT-10:00) Honolulu Time"},{timeZoneId:"America/Anchorage",displayName:"(GMT-08:00) Anchorage Time"},{timeZoneId:"America/Los_Angeles",displayName:"(GMT-07:00) Los Angeles Time"},{timeZoneId:"America/Boise",displayName:"(GMT-06:00) Boise Time"},{timeZoneId:"America/Denver",displayName:"(GMT-06:00) Denver Time"},{timeZoneId:"America/Phoenix",displayName:"(GMT-07:00) Phoenix Time"},{timeZoneId:"America/Chicago",displayName:"(GMT-05:00) Chicago Time"},{timeZoneId:"America/Detroit",displayName:"(GMT-04:00) Detroit Time"},{timeZoneId:"America/New_York",displayName:"(GMT-04:00) New York Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"UY",displayName:"Uruguay",defaultTimeZoneId:"America/Montevideo",timeZone:[{timeZoneId:"America/Montevideo",displayName:"(GMT-03:00) Uruguay Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"UZ",displayName:"Uzbekistan",defaultTimeZoneId:"Asia/Tashkent",timeZone:[{timeZoneId:"Asia/Tashkent",displayName:"(GMT+05:00) Uzbekistan Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"VU",displayName:"Vanuatu",defaultTimeZoneId:"Pacific/Efate",timeZone:[{timeZoneId:"Pacific/Efate",displayName:"(GMT+11:00) Vanuatu Time"}],tosLocale:{language:"en",country:"US"}},{countryCode:"VA",displayName:"Vatican City",defaultTimeZoneId:"Europe/Vatican",timeZone:[{timeZoneId:"Europe/Vatican",displayName:"(GMT+02:00) Vatican City Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"VE",displayName:"Venezuela",defaultTimeZoneId:"America/Caracas",timeZone:[{timeZoneId:"America/Caracas",displayName:"(GMT-04:00) Venezuela Time"}],tosLocale:{language:"es",country:"ES"}},{countryCode:"VN",displayName:"Vietnam",defaultTimeZoneId:"Asia/Saigon",timeZone:[{timeZoneId:"Asia/Saigon",displayName:"(GMT+07:00) Vietnam Time"}],tosLocale:{language:"vi",country:"VN"}},{countryCode:"WF",displayName:"Wallis & Futuna",defaultTimeZoneId:"Pacific/Wallis",timeZone:[{timeZoneId:"Pacific/Wallis",displayName:"(GMT+12:00) Wallis & Futuna Time"}],tosLocale:{language:"en",country:"GB"}},{countryCode:"EH",displayName:"Western Sahara",defaultTimeZoneId:"Africa/El_Aaiun",timeZone:[{timeZoneId:"Africa/El_Aaiun",displayName:"(GMT+01:00) Western Sahara Time"}],tosLocale:{language:"en",country:"GB"}}],i=r.reduce((function(e,t){return e[t.countryCode]=t.timeZone,e}),{}),o=Object(a.keyBy)(r,"countryCode"),c=r.reduce((function(e,t){return t.timeZone.forEach((function(n){var a=n.timeZoneId;return e[a]=t.countryCode})),e}),{})},22:function(e,t,n){"use strict";n.d(t,"n",(function(){return a})),n.d(t,"l",(function(){return r})),n.d(t,"o",(function(){return i})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return l})),n.d(t,"s",(function(){return s})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return m})),n.d(t,"k",(function(){return g})),n.d(t,"u",(function(){return f})),n.d(t,"v",(function(){return p})),n.d(t,"q",(function(){return y})),n.d(t,"p",(function(){return b})),n.d(t,"b",(function(){return h})),n.d(t,"e",(function(){return v})),n.d(t,"a",(function(){return T})),n.d(t,"d",(function(){return O})),n.d(t,"c",(function(){return k})),n.d(t,"f",(function(){return E})),n.d(t,"g",(function(){return N}));var a="mainDashboard",r="entityDashboard",i="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",l="activation",s="splash",u="adminBar",d="adminBarViewOnly",m="settings",g="adBlockingRecovery",f="wpDashboard",p="wpDashboardViewOnly",y="moduleSetup",b="metricSelection",h="key-metrics",v="traffic",T="content",O="speed",k="monetization",E=[a,r,i,o,c,s,m,y,b],N=[i,o,d,p]},224:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Root}));var a=n(15),r=n.n(a),i=n(1),o=n.n(i),c=n(432),l=n(534),s=n(0),u=n(3),d=n.n(u),m=n(225),g=n(229),f=n(57),p=n(230),y=n(232),b=n(233),h=n(61),v=n(160),T=n(173);function Root(t){var n=t.children,a=t.registry,i=t.viewContext,o=void 0===i?null:i,d=c.a,O=Object(s.useState)({key:"Root",value:!0}),k=r()(O,1)[0];return e.createElement(s.StrictMode,null,e.createElement(v.a,{value:k},e.createElement(u.RegistryProvider,{value:a},e.createElement(g.a,{value:f.a},e.createElement(h.a,{value:o},e.createElement(l.a,{theme:d()},e.createElement(m.a,null,e.createElement(y.a,null,n,o&&e.createElement(b.a,null)),Object(T.a)(o)&&e.createElement(p.a,null))))))))}Root.propTypes={children:o.a.node,registry:o.a.object,viewContext:o.a.string.isRequired},Root.defaultProps={registry:d.a}}).call(this,n(4))},225:function(e,t,n){"use strict";(function(e,a){var r=n(51),i=n.n(r),o=n(53),c=n.n(o),l=n(68),s=n.n(l),u=n(69),d=n.n(u),m=n(49),g=n.n(m),f=n(1),p=n.n(f),y=n(0),b=n(2),h=n(172),v=n(61),T=n(197),O=n(9);function k(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=g()(e);if(t){var r=g()(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return d()(this,n)}}var E=function(t){s()(ErrorHandler,t);var n=k(ErrorHandler);function ErrorHandler(e){var t;return i()(this,ErrorHandler),(t=n.call(this,e)).state={error:null,info:null,copied:!1},t}return c()(ErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t,info:n}),Object(O.I)("react_error","handle_".concat(this.context||"unknown","_error"),"".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,r=t.info;return n?a.createElement(T.a,{id:"googlesitekit-error",className:"googlesitekit-error-handler",title:Object(b.__)("Site Kit encountered an error","google-site-kit"),description:a.createElement(h.a,{message:n.message,componentStack:r.componentStack}),isDismissible:!1,format:"small",type:"win-error"},a.createElement("pre",{className:"googlesitekit-overflow-auto"},n.message,r.componentStack)):e}}]),ErrorHandler}(y.Component);E.contextType=v.b,E.propTypes={children:p.a.node.isRequired},t.a=E}).call(this,n(28),n(4))},226:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var a=n(1),r=n.n(a),i=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var a="win-warning"===n?e.createElement(i.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},a))}BannerIcon.propTypes={type:r.a.string}}).call(this,n(4))},227:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerLogo}));var a=n(1),r=n.n(a),i=n(17),o=n(155),c=n(198);function BannerLogo(t){var n=t.module,a=t.moduleName;return e.createElement(i.a,{size:12},e.createElement("div",{className:"googlesitekit-publisher-win__logo"},n&&e.createElement(c.a,{slug:n,size:19}),!n&&e.createElement(o.a,{height:"34",width:"32"})),a&&e.createElement("div",{className:"googlesitekit-publisher-win__module-name"},a))}BannerLogo.propTypes={module:r.a.string,moduleName:r.a.string}}).call(this,n(4))},228:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerDescription}));var a=n(1),r=n.n(a),i=n(0),o=n(75),c=n(20),l=n(86);function BannerDescription(t){var n=t.description,a=t.learnMoreLabel,r=t.learnMoreURL,s=t.learnMoreTarget,u=t.learnMoreDescription,d=t.onLearnMoreClick;if(!n)return null;var m;return a&&(m=e.createElement(i.Fragment,null,e.createElement(c.a,{onClick:function(e){e.persist(),null==d||d()},href:r,external:s===l.a.EXTERNAL},a),u)),e.createElement("div",{className:"googlesitekit-publisher-win__desc"},Object(i.isValidElement)(n)?e.createElement(i.Fragment,null,n,m&&e.createElement("p",null,m)):e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.a)(n,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",m))}BannerDescription.propTypes={description:r.a.node,learnMoreURL:r.a.string,learnMoreDescription:r.a.string,learnMoreLabel:r.a.string,learnMoreTarget:r.a.oneOf(Object.values(l.a)),onLearnMoreClick:r.a.func}}).call(this,n(4))},229:function(e,t,n){"use strict";var a=n(158),r=(a.a.Consumer,a.a.Provider);t.a=r},23:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return r}));var a="core/ui",r="activeContextID"},230:function(e,t,n){"use strict";(function(e){var a=n(3),r=n(231),i=n(7);t.a=function PermissionsModal(){return Object(a.useSelect)((function(e){return e(i.a).isAuthenticated()}))?e.createElement(r.a,null):null}}).call(this,n(4))},231:function(e,t,n){"use strict";(function(e,a){var r=n(5),i=n.n(r),o=n(16),c=n.n(o),l=n(2),s=n(0),u=n(3),d=n(109),m=n(29),g=n(32),f=n(7),p=n(129),y=n(72);t.a=function AuthenticatedPermissionsModal(){var t,n,r,o,b=Object(u.useRegistry)(),h=Object(u.useSelect)((function(e){return e(f.a).getPermissionScopeError()})),v=Object(u.useSelect)((function(e){return e(f.a).getUnsatisfiedScopes()})),T=Object(u.useSelect)((function(t){var n,a,r;return t(f.a).getConnectURL({additionalScopes:null==h||null===(n=h.data)||void 0===n?void 0:n.scopes,redirectURL:(null==h||null===(a=h.data)||void 0===a?void 0:a.redirectURL)||e.location.href,errorRedirectURL:null==h||null===(r=h.data)||void 0===r?void 0:r.errorRedirectURL})})),O=Object(u.useDispatch)(f.a).clearPermissionScopeError,k=Object(u.useDispatch)(g.a).navigateTo,E=Object(u.useDispatch)(m.a).setValues,N=Object(s.useCallback)((function(){O()}),[O]),j=Object(s.useCallback)(c()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return E(f.d,{permissionsError:h}),e.next=3,Object(p.c)(b);case 3:k(T);case 4:case"end":return e.stop()}}),e)}))),[b,T,k,h,E]);return Object(s.useEffect)((function(){(function(){var e=c()(i.a.mark((function e(){var t,n,a;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(null==h||null===(t=h.data)||void 0===t?void 0:t.skipModal)||!(null==h||null===(n=h.data)||void 0===n||null===(a=n.scopes)||void 0===a?void 0:a.length)){e.next=3;break}return e.next=3,j();case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[j,h]),h?(null==h||null===(t=h.data)||void 0===t||null===(n=t.scopes)||void 0===n?void 0:n.length)?(null==h||null===(r=h.data)||void 0===r?void 0:r.skipModal)||v&&(null==h||null===(o=h.data)||void 0===o?void 0:o.scopes.every((function(e){return v.includes(e)})))?null:a.createElement(y.a,null,a.createElement(d.a,{title:Object(l.__)("Additional Permissions Required","google-site-kit"),subtitle:h.message,confirmButton:Object(l.__)("Proceed","google-site-kit"),dialogActive:!0,handleConfirm:j,handleDialog:N,medium:!0})):(e.console.warn("permissionsError lacks scopes array to use for redirect, so not showing the PermissionsModal. permissionsError was:",h),null):null}}).call(this,n(28),n(4))},232:function(e,t,n){"use strict";var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(15),l=n.n(c),s=n(0),u=n(3),d=n(129);t.a=function RestoreSnapshots(e){var t=e.children,n=Object(u.useRegistry)(),a=Object(s.useState)(!1),i=l()(a,2),c=i[0],m=i[1];return Object(s.useEffect)((function(){c||o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(d.b)(n);case 2:m(!0);case 3:case"end":return e.stop()}}),e)})))()}),[n,c]),c?t:null}},233:function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return FeatureTours}));var r=n(81),i=n(0),o=n(3),c=n(7),l=n(18),s=n(90);function FeatureTours(){var t=Object(l.a)(),n=Object(o.useDispatch)(c.a).triggerTourForView;Object(r.a)((function(){n(t)}));var u=Object(o.useSelect)((function(e){return e(c.a).getCurrentTour()}));return Object(i.useEffect)((function(){if(u){var t=document.getElementById("js-googlesitekit-main-dashboard");if(t){var n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}}),[u]),u?a.createElement(s.a,{tourID:u.slug,steps:u.steps,gaEventCategory:u.gaEventCategory,callback:u.callback}):null}}).call(this,n(28),n(4))},236:function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(0),l=n(114),s=n(9),u=n(402),d=n(77),m=n(403),g=n(134);function DataBlock(t){var n=t.stat,a=void 0===n?null:n,r=t.className,i=void 0===r?"":r,f=t.title,p=void 0===f?"":f,y=t.datapoint,b=void 0===y?null:y,h=t.datapointUnit,v=void 0===h?"":h,T=t.change,O=void 0===T?null:T,k=t.changeDataUnit,E=void 0===k?"":k,N=t.context,j=void 0===N?"default":N,S=t.period,_=void 0===S?"":S,A=t.selected,I=void 0!==A&&A,w=t.source,C=t.sparkline,Z=t.handleStatSelection,M=void 0===Z?null:Z,D=t.invertChangeColor,P=void 0!==D&&D,L=t.gatheringData,G=void 0!==L&&L,R=t.gatheringDataNoticeStyle,x=void 0===R?l.a.DEFAULT:R,B=t.badge,U=Object(c.useCallback)((function(){!G&&M&&M(a)}),[G,M,a]),W=Object(c.useCallback)((function(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),U())}),[U]),V=void 0===b?b:Object(s.B)(b,v),F="button"===j,z=F?"button":"";return e.createElement("div",{className:o()("googlesitekit-data-block",i,"googlesitekit-data-block--".concat(j),{"googlesitekit-data-block--selected":I,"googlesitekit-data-block--is-gathering-data":G}),tabIndex:F&&!G?"0":"-1",role:M&&z,onClick:U,onKeyDown:W,"aria-disabled":G||void 0,"aria-label":M&&p,"aria-pressed":M&&I},e.createElement("div",{className:"googlesitekit-data-block__title-datapoint-wrapper"},e.createElement("h3",{className:" googlesitekit-subheading-1 googlesitekit-data-block__title "},!0===B?e.createElement(d.a,{"aria-hidden":"true",className:"googlesitekit-badge--hidden",label:"X"}):B,e.createElement("span",{className:"googlesitekit-data-block__title-inner"},p)),!G&&e.createElement("div",{className:"googlesitekit-data-block__datapoint"},V)),!G&&C&&e.createElement(u.a,{sparkline:C,invertChangeColor:P}),!G&&e.createElement("div",{className:"googlesitekit-data-block__change-source-wrapper"},e.createElement(m.a,{change:O,changeDataUnit:E,period:_,invertChangeColor:P}),w&&e.createElement(g.a,{className:"googlesitekit-data-block__source",name:w.name,href:w.link,external:null==w?void 0:w.external})),G&&e.createElement(l.b,{style:x}))}DataBlock.propTypes={stat:r.a.number,className:r.a.string,title:r.a.string,datapoint:r.a.oneOfType([r.a.string,r.a.number]),datapointUnit:r.a.string,change:r.a.oneOfType([r.a.string,r.a.number]),changeDataUnit:r.a.oneOfType([r.a.string,r.a.bool]),context:r.a.string,period:r.a.string,selected:r.a.bool,handleStatSelection:r.a.func,invertChangeColor:r.a.bool,gatheringData:r.a.bool,gatheringDataNoticeStyle:r.a.oneOf(Object.values(l.a)),badge:r.a.oneOfType([r.a.bool,r.a.node])},t.a=DataBlock}).call(this,n(4))},24:function(e,t,n){"use strict";n.d(t,"d",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return l}));var a=n(79),r="xlarge",i="desktop",o="tablet",c="small";function l(){var e=Object(a.a)();return e>1280?r:e>960?i:e>600?o:c}},243:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return f})),n.d(t,"c",(function(){return y})),n.d(t,"b",(function(){return b}));var a=n(21),r=n.n(a),i=n(63),o=n.n(i),c=n(267),l=n(322),s=n(323),u=n(244),d=n(268),m=n(324),g=n(0),f=o()((function(e){return{widgetSlug:e,Widget:p(e)(c.a),WidgetRecoverableModules:p(e)(d.a),WidgetReportZero:p(e)(l.a),WidgetReportError:p(e)(s.a),WidgetNull:p(e)(u.a)}}));function p(t){return function(n){var a=Object(g.forwardRef)((function(a,i){return e.createElement(n,r()({},a,{ref:i,widgetSlug:t}))}));return a.displayName="WithWidgetSlug",(n.displayName||n.name)&&(a.displayName+="(".concat(n.displayName||n.name,")")),a}}var y=function(t){var n=f(t);return function(t){function DecoratedComponent(a){return e.createElement(t,r()({},a,n))}return DecoratedComponent.displayName="WithWidgetComponentProps",(t.displayName||t.name)&&(DecoratedComponent.displayName+="(".concat(t.displayName||t.name,")")),DecoratedComponent}},b=function(t){return function(n){function DecoratedComponent(a){return e.createElement(n,r()({},a,{WPDashboardReportError:p(t)(m.a)}))}return DecoratedComponent.displayName="WithWPDashboardWidgetComponentProps",(n.displayName||n.name)&&(DecoratedComponent.displayName+="(".concat(n.displayName||n.name,")")),DecoratedComponent}}}).call(this,n(4))},244:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetNull}));var a=n(6),r=n.n(a),i=n(1),o=n.n(i),c=n(143),l=n(74);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}var u={};function WidgetNull(t){var n=t.widgetSlug;return Object(c.a)(n,l.a,u),e.createElement(l.a,null)}WidgetNull.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:o.a.string.isRequired},l.a.propTypes)}).call(this,n(4))},263:function(e,t,n){"use strict";n.d(t,"d",(function(){return l})),n.d(t,"e",(function(){return s})),n.d(t,"b",(function(){return u})),n.d(t,"a",(function(){return d})),n.d(t,"c",(function(){return m}));var a=n(27),r=n.n(a),i=n(14),o=n(24),c=n(9),l=function(e,t){if(!(null==t?void 0:t.length))return e;var n=[];return(null==e?void 0:e.length)&&(n=e[0].reduce((function(e,t,n){return(null==t?void 0:t.role)?[].concat(r()(e),[n]):e}),[])),e.map((function(e){return e.filter((function(e,a){return 0===a||t.includes(a-1)||n.includes(a-1)}))}))},s=function(e,t,n,a){var r={height:e||t,width:n||a};return r.width&&!r.height&&(r.height="100%"),r.height&&!r.width&&(r.width="100%"),r},u=function(e,t,n){var a=r()(e||[]);return t&&a.push({eventName:"ready",callback:t}),n&&a.push({eventName:"select",callback:n}),a},d=function(e,t,n,a,r,l){var s,u,d,m,g,f,p,y,b=Object(i.cloneDeep)(e);t&&"LineChart"===n&&((null==e||null===(s=e.vAxis)||void 0===s||null===(u=s.viewWindow)||void 0===u?void 0:u.min)||Object(i.set)(b,"vAxis.viewWindow.min",0),(null==e||null===(d=e.vAxis)||void 0===d||null===(m=d.viewWindow)||void 0===m?void 0:m.max)||Object(i.set)(b,"vAxis.viewWindow.max",100),(null==e||null===(g=e.hAxis)||void 0===g||null===(f=g.viewWindow)||void 0===f?void 0:f.min)||(Object(i.set)(b,"hAxis.viewWindow.min",Object(c.G)(a)),delete b.hAxis.ticks),(null==e||null===(p=e.hAxis)||void 0===p||null===(y=p.viewWindow)||void 0===y?void 0:y.max)||(Object(i.set)(b,"hAxis.viewWindow.max",Object(c.G)(r)),delete b.hAxis.ticks));if("LineChart"===n){var h,v,T;if((null==e||null===(h=e.hAxis)||void 0===h?void 0:h.maxTextLines)||Object(i.set)(b,"hAxis.maxTextLines",1),!(null==e||null===(v=e.hAxis)||void 0===v?void 0:v.minTextSpacing)){var O=l===o.b?50:100;Object(i.set)(b,"hAxis.minTextSpacing",O)}void 0===(null==e||null===(T=e.tooltip)||void 0===T?void 0:T.isHtml)&&(Object(i.set)(b,"tooltip.isHtml",!0),Object(i.set)(b,"tooltip.trigger","both"))}return Object(i.merge)(b,{hAxis:{textStyle:{fontSize:10,color:"#5f6561"}},vAxis:{textStyle:{color:"#5f6561",fontSize:10}},legend:{textStyle:{color:"#131418",fontSize:12}}}),b},m=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object(c.r)(),n=Intl.NumberFormat(t,{style:"currency",currency:e}),a=n.formatToParts(1e6);return a.reduce((function(e,t){var n=t.value;switch(t.type){case"group":return e+",";case"decimal":return e+".";case"currency":return e+n;case"literal":return e+(/^\s*$/.test(n)?n:"");case"integer":var r=n.replace(/\d/g,"#");return e+(Object(i.findLast)(a,(function(e){return"integer"===e.type}))===t?r.replace(/#$/,"0"):r);case"fraction":return e+n.replace(/\d/g,"0");default:return e}}),"")}},267:function(e,t,n){"use strict";(function(e){var a=n(11),r=n.n(a),i=n(1),o=n.n(i),c=n(0),l=Object(c.forwardRef)((function(t,n){var a=t.children,i=t.className,o=t.widgetSlug,c=t.noPadding,l=t.Header,s=t.Footer;return e.createElement("div",{className:r()("googlesitekit-widget","googlesitekit-widget--".concat(o),{"googlesitekit-widget--no-padding":c},{"googlesitekit-widget--with-header":l},i),ref:n},l&&e.createElement("div",{className:"googlesitekit-widget__header"},e.createElement(l,null)),e.createElement("div",{className:"googlesitekit-widget__body"},a),s&&e.createElement("div",{className:"googlesitekit-widget__footer"},e.createElement(s,null)))}));l.defaultProps={children:void 0,noPadding:!1},l.propTypes={children:o.a.node,widgetSlug:o.a.string.isRequired,noPadding:o.a.bool,Header:o.a.elementType,Footer:o.a.elementType},t.a=l}).call(this,n(4))},268:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetRecoverableModules}));var a=n(6),r=n.n(a),i=n(21),o=n.n(i),c=n(27),l=n.n(c),s=n(25),u=n.n(s),d=n(1),m=n.n(d),g=n(0),f=n(143),p=n(163);function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function WidgetRecoverableModules(t){var n=t.widgetSlug,a=t.moduleSlugs,r=u()(t,["widgetSlug","moduleSlugs"]),i=Object(g.useMemo)((function(){return{moduleSlug:l()(a).sort().join(","),moduleSlugs:a}}),[a]);return Object(f.a)(n,p.a,i),e.createElement(p.a,o()({moduleSlugs:a},r))}WidgetRecoverableModules.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:m.a.string.isRequired},p.a.propTypes)}).call(this,n(4))},275:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var a=n(6),r=n.n(a),i=n(25),o=n.n(i),c=n(63),l=n.n(c),s=n(14);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var m=l()((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.metrics,n=e.dimensions,a=o()(e,["metrics","dimensions"]);return d({metrics:g(t),dimensions:f(n)},a)})),g=function(e){return Object(s.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(s.isPlainObject)(e)}))},f=function(e){return Object(s.castArray)(e).map((function(e){return"string"==typeof e?{name:e}:e})).filter((function(e){return Object(s.isPlainObject)(e)}))}},29:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var a="core/forms"},3:function(e,t){e.exports=googlesitekit.data},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var a="core/location"},322:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportZero}));var a=n(6),r=n.n(a),i=n(21),o=n.n(i),c=n(25),l=n.n(c),s=n(1),u=n.n(s),d=n(0),m=n(143),g=n(174);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function WidgetReportZero(t){var n=t.widgetSlug,a=t.moduleSlug,r=l()(t,["widgetSlug","moduleSlug"]),i=Object(d.useMemo)((function(){return{moduleSlug:a}}),[a]);return Object(m.a)(n,g.a,i),e.createElement(g.a,o()({moduleSlug:a},r))}WidgetReportZero.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:u.a.string.isRequired},g.a.propTypes)}).call(this,n(4))},323:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportError}));var a=n(6),r=n.n(a),i=n(25),o=n.n(i),c=n(1),l=n.n(c),s=n(170);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function WidgetReportError(t){t.widgetSlug;var n=o()(t,["widgetSlug"]);return e.createElement(s.a,n)}WidgetReportError.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:l.a.string.isRequired},s.a.propTypes)}).call(this,n(4))},324:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WPDashboardReportError}));var a=n(1),r=n.n(a),i=n(550),o=n(208),c=n(3),l=n(23),s=n(170);function WPDashboardReportError(t){var n=t.moduleSlug,a=t.error,r=Object(o.a)(WPDashboardReportError,"WPDashboardReportError"),u=Object(c.useDispatch)(l.b).setValue,d=a.message,m=Object(c.useSelect)((function(e){return e(l.b).getValue("WPDashboardReportError-".concat(n,"-").concat(d))}));return Object(i.a)((function(){u("WPDashboardReportError-".concat(n,"-").concat(d),r)}),(function(){u("WPDashboardReportError-".concat(n,"-").concat(d),void 0)})),m!==r?null:e.createElement(s.a,{moduleSlug:n,error:a})}WPDashboardReportError.propTypes={moduleSlug:r.a.string.isRequired,error:r.a.object.isRequired}}).call(this,n(4))},331:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CreateAccountField}));var a=n(11),r=n.n(a),i=n(10);function CreateAccountField(t){var n=t.hasError,a=t.value,o=t.setValue,c=t.name,l=t.label;return void 0===a?null:e.createElement(i.TextField,{className:r()("mdc-text-field",{"mdc-text-field--error":n}),label:l,name:c,onChange:function(e){o(e.target.value,c)},outlined:!0,value:a,id:"googlesitekit_analytics_account_create_".concat(c)})}}).call(this,n(4))},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var a=n(22),r=n(18);function i(){var e=Object(r.a)();return a.g.includes(e)}},342:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var a=n(14),r=n(122);function i(e){return!!Array.isArray(e)&&e.every((function(e){return!!Object(a.isPlainObject)(e)&&(!(!e.hasOwnProperty("fieldNames")||!Array.isArray(e.fieldNames)||0===e.fieldNames.length)&&(!(!e.hasOwnProperty("limit")||"number"!=typeof e.limit)&&!(e.hasOwnProperty("orderby")&&!Object(r.e)(e.orderby))))}))}},35:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return l})),n.d(t,"c",(function(){return s})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return d}));n(14);var a=n(2),r="missing_required_scopes",i="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===r}function l(e){var t;return[i,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function s(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||l(e)||c(e)||s(e))}function d(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(a.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(a.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},352:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return EnhancedMeasurementSwitch}));var a=n(6),r=n.n(a),i=n(11),o=n.n(i),c=n(1),l=n.n(c),s=n(81),u=n(0),d=n(38),m=n(2),g=n(3),f=n(10),p=n(29),y=n(8),b=n(199),h=n(9),v=n(18),T=n(372);function EnhancedMeasurementSwitch(t){var n=t.className,a=t.onClick,i=t.disabled,c=void 0!==i&&i,l=t.loading,O=void 0!==l&&l,k=t.formName,E=void 0===k?y.j:k,N=t.isEnhancedMeasurementAlreadyEnabled,j=void 0!==N&&N,S=t.showTick,_=void 0!==S&&S,A=Object(g.useSelect)((function(e){return e(p.a).getValue(E,y.i)})),I=Object(v.a)(),w=Object(g.useDispatch)(p.a).setValues,C=Object(u.useCallback)((function(){w(E,r()({},y.i,!A)),Object(h.I)("".concat(I,"_analytics"),A?"deactivate_enhanced_measurement":"activate_enhanced_measurement"),null==a||a()}),[E,A,a,w,I]);return Object(s.a)((function(){w(y.j,r()({},y.k,!0))})),e.createElement("div",{className:o()("googlesitekit-analytics-enable-enhanced-measurement",n,{"googlesitekit-analytics-enable-enhanced-measurement--loading":O})},O&&e.createElement(f.ProgressBar,{small:!0,className:"googlesitekit-analytics-enable-enhanced-measurement__progress--settings-edit"}),!O&&j&&e.createElement("div",{className:"googlesitekit-analytics-enable-enhanced-measurement__already-enabled-label"},_&&e.createElement("div",{className:"googlesitekit-analytics-enable-enhanced-measurement__already-enabled-tick"},e.createElement(T.a,null)),Object(m.__)("Enhanced measurement is enabled for this web data stream","google-site-kit")),!O&&!j&&e.createElement(f.Switch,{label:Object(m.__)("Enable enhanced measurement","google-site-kit"),checked:A,disabled:c,onClick:C,hideLabel:!1}),e.createElement("p",{className:"googlesitekit-module-settings-group__helper-text"},Object(d.a)(Object(m.__)("This allows you to measure interactions with your content (e.g. file downloads, form completions, video views). <a>Learn more</a>","google-site-kit"),{a:e.createElement(b.a,{path:"/analytics/answer/9216061",external:!0})})))}EnhancedMeasurementSwitch.propTypes={onClick:l.a.func,disabled:l.a.bool,loading:l.a.bool,isEnhancedMeasurementAlreadyEnabled:l.a.bool,showTick:l.a.bool}}).call(this,n(4))},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return T})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return v}));var a=n(99),r=e._googlesitekitTrackingData||{},i=r.activeModules,o=void 0===i?[]:i,c=r.isSiteKitScreen,l=r.trackingEnabled,s=r.trackingID,u=r.referenceSiteURL,d=r.userIDHash,m=r.isAuthenticated,g={activeModules:o,trackingEnabled:l,trackingID:s,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:r.userRoles,isAuthenticated:m,pluginVersion:"1.151.0"},f=Object(a.a)(g),p=f.enableTracking,y=f.disableTracking,b=(f.isTrackingEnabled,f.initializeSnippet),h=f.trackEvent,v=f.trackEventOnce;function T(e){e?p():y()}c&&l&&b()}).call(this,n(28))},364:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M0 19h22L11 0 0 19zm12-3h-2v-2h2v2zm0-4h-2V8h2v4z",fill:"currentColor"});t.a=function SvgWarningV2(e){return a.createElement("svg",r({viewBox:"0 0 22 19"},e),i)}},366:function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return GoogleChart}));var r=n(6),i=n.n(r),o=n(27),c=n.n(o),l=n(21),s=n.n(l),u=n(15),d=n.n(u),m=n(25),g=n.n(m),f=(n(594),n(11)),p=n.n(f),y=n(12),b=n.n(y),h=n(1),v=n.n(h),T=n(429),O=n(81),k=n(208),E=n(0),N=n(44),j=n(7),S=n(114),_=n(3),A=n(508),I=n(509),w=n(23),C=n(18),Z=n(173),M=n(263),D=n(9),P=n(24);function L(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function G(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?L(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):L(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function GoogleChart(t){var n=t.chartEvents,r=t.chartType,i=t.children,o=t.className,l=t.data,u=t.dateMarkers,m=t.getChartWrapper,f=t.height,y=t.loaded,h=t.loadingHeight,v=t.loadingWidth,L=t.onMouseOver,G=t.onMouseOut,R=t.onReady,x=t.onSelect,B=t.selectedStats,U=t.width,W=t.options,V=t.gatheringData,F=g()(t,["chartEvents","chartType","children","className","data","dateMarkers","getChartWrapper","height","loaded","loadingHeight","loadingWidth","onMouseOver","onMouseOut","onReady","onSelect","selectedStats","width","options","gatheringData"]),z=Object(k.a)(GoogleChart),H=Object(P.e)(),q=Object(_.useSelect)((function(e){return e(j.a).getDateRangeDates({offsetDays:0})})),K=q.startDate,J=q.endDate,Y=Object(C.a)(),$=Object(_.useSelect)((function(e){return e(w.b).getValue("googleChartsCollisionError")})),X=Object(E.useState)(!1),Q=d()(X,2),ee=Q[0],te=Q[1],ne=Object(_.useDispatch)(w.b).setValue,ae=Object(M.d)(l,B),re="PieChart"===r?"circular":"square",ie=Object(M.e)(h,f,v,U),oe=e.createElement("div",{className:"googlesitekit-chart-loading"},e.createElement(N.a,s()({className:"googlesitekit-chart-loading__wrapper",shape:re},ie))),ce=Object(E.useRef)(),le=Object(E.useRef)();Object(O.a)((function(){var e,t,n,r;void 0===$&&(Object(Z.a)(Y)&&(null===(e=a)||void 0===e||null===(t=e.google)||void 0===t?void 0:t.charts)&&(a.google.charts=void 0),!Object(Z.a)(Y)&&(null===(n=a)||void 0===n||null===(r=n.google)||void 0===r?void 0:r.charts)?ne("googleChartsCollisionError",!0):ne("googleChartsCollisionError",!1))})),Object(E.useEffect)((function(){return function(){if(le.current&&ce.current){var e=le.current.visualization.events;e.removeAllListeners(ce.current.getChart()),e.removeAllListeners(ce.current)}}}),[]),Object(E.useLayoutEffect)((function(){var e,t;L&&(null===(e=le.current)||void 0===e||e.visualization.events.addListener(ce.current.getChart(),"onmouseover",(function(e){L(e,{chartWrapper:ce.current,google:le.current})})));G&&(null===(t=le.current)||void 0===t||t.visualization.events.addListener(ce.current.getChart(),"onmouseout",(function(e){G(e,{chartWrapper:ce.current,google:le.current})})))}),[L,G]);var se=u.filter((function(e){return!!((t=new Date(e.date))&&K&&J)&&!(t.getTime()<Object(D.G)(K).getTime()||t.getTime()>Object(D.G)(J).getTime());var t}));if($)return null;if(!y)return e.createElement("div",{className:p()("googlesitekit-chart","googlesitekit-chart-loading__forced",o)},oe);var ue=Object(M.b)([].concat(c()(n||[]),[{eventName:"ready",callback:function(){var e;if(ce.current&&se.length){var t=ce.current.getChart(),n=null==t?void 0:t.getChartLayoutInterface(),a=null==n?void 0:n.getChartAreaBoundingBox(),r=ce.current.getDataTable();if(n&&a&&r){se.forEach((function(e,t){var r=new Date(e.date),i=document.getElementById("googlesitekit-chart__date-marker-line--".concat(z,"-").concat(t));b()(i,"#googlesitekit-chart__date-marker-line--".concat(z,"-").concat(t," is missing from the DOM, but required to render date markers."));var o=Math.floor(n.getXLocation(Object(D.G)(Object(D.p)(r))));if(Object.assign(i.style,{left:"".concat(o-1,"px"),top:"".concat(Math.floor(a.top),"px"),height:"".concat(Math.floor(a.height),"px"),opacity:1}),e.text){var c=document.getElementById("googlesitekit-chart__date-marker-tooltip--".concat(z,"-").concat(t));b()(c,"#googlesitekit-chart__date-marker-tooltip--".concat(z,"-").concat(t," is missing from the DOM, but required to render date marker tooltips.")),Object.assign(c.style,{left:"".concat(o-9,"px"),top:"".concat(Math.floor(a.top)-18,"px"),opacity:1})}}));var i=null===(e=document.querySelector("#googlesitekit-chart-".concat(z," svg:first-of-type > g:first-of-type > g > g > text")))||void 0===e?void 0:e.parentElement.parentElement.parentElement;!!i&&document.querySelectorAll("#googlesitekit-chart-".concat(z," svg:first-of-type > g")).length>=3&&(i.style.transform="translateY(-10px)")}}}}]),R,x),de=Object(M.a)(W,V,r,K,J,H);return e.createElement(A.a,null,e.createElement("div",{className:p()("googlesitekit-chart","googlesitekit-chart--".concat(r),o),id:"googlesitekit-chart-".concat(z),tabIndex:-1},e.createElement(T.a,s()({className:"googlesitekit-chart__inner",chartEvents:ue,chartLanguage:Object(D.r)(),chartType:r,chartVersion:"49",data:ae,loader:oe,height:f,getChartWrapper:function(e,t){var n,a,r;(ee||te(!0),e!==ce.current)&&(null===(n=le.current)||void 0===n||n.visualization.events.removeAllListeners(null===(a=ce.current)||void 0===a?void 0:a.getChart()),null===(r=le.current)||void 0===r||r.visualization.events.removeAllListeners(ce.current));ce.current=e,le.current=t,m&&m(e,t)},width:U,options:de},F)),V&&ee&&e.createElement(S.b,{style:S.a.OVERLAY}),!!se.length&&se.map((function(t,n){return e.createElement(I.a,{key:"googlesitekit-chart__date-marker--".concat(z,"-").concat(n),id:"".concat(z,"-").concat(n),text:t.text})})),i))}GoogleChart.propTypes={className:v.a.string,children:v.a.node,chartEvents:v.a.arrayOf(v.a.shape({eventName:v.a.string,callback:v.a.func})),chartType:v.a.oneOf(["LineChart","PieChart"]).isRequired,data:v.a.array,dateMarkers:v.a.arrayOf(v.a.shape({date:v.a.string.isRequired,text:v.a.string})),getChartWrapper:v.a.func,height:v.a.string,loaded:v.a.bool,loadingHeight:v.a.string,loadingWidth:v.a.string,onMouseOut:v.a.func,onMouseOver:v.a.func,onReady:v.a.func,onSelect:v.a.func,selectedStats:v.a.arrayOf(v.a.number),width:v.a.string,options:v.a.object,gatheringData:v.a.bool},GoogleChart.defaultProps=G(G({},T.a.defaultProps),{},{dateMarkers:[],gatheringData:!1,loaded:!0})}).call(this,n(4),n(28))},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return v})),n.d(t,"c",(function(){return T})),n.d(t,"e",(function(){return O})),n.d(t,"b",(function(){return k}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=(n(27),n(9));function l(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,r=function(){};return{s:r,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var u,d="googlesitekit_",m="".concat(d).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),g=["sessionStorage","localStorage"],f=[].concat(g),p=function(){var t=o()(r.a.mark((function t(n){var a,i;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(a=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,i="__storage_test__",a.setItem(i,i),a.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==a.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function y(){return b.apply(this,arguments)}function b(){return(b=o()(r.a.mark((function t(){var n,a,i;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=l(f),t.prev=3,n.s();case 5:if((a=n.n()).done){t.next=15;break}if(i=a.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,p(i);case 11:if(!t.sent){t.next=13;break}u=e[i];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=o()(r.a.mark((function e(t){var n,a,i,o,c,l,s;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,y();case 2:if(!(n=e.sent)){e.next=10;break}if(!(a=n.getItem("".concat(m).concat(t)))){e.next=10;break}if(i=JSON.parse(a),o=i.timestamp,c=i.ttl,l=i.value,s=i.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:l,isError:s});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),v=function(){var t=o()(r.a.mark((function t(n,a){var i,o,l,s,u,d,g,f,p=arguments;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=p.length>2&&void 0!==p[2]?p[2]:{},o=i.ttl,l=void 0===o?c.b:o,s=i.timestamp,u=void 0===s?Math.round(Date.now()/1e3):s,d=i.isError,g=void 0!==d&&d,t.next=3,y();case 3:if(!(f=t.sent)){t.next=14;break}return t.prev=5,f.setItem("".concat(m).concat(n),JSON.stringify({timestamp:u,ttl:l,value:a,isError:g})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),T=function(){var t=o()(r.a.mark((function t(n){var a,i;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,y();case 2:if(!(a=t.sent)){t.next=14;break}return t.prev=4,i=n.startsWith(d)?n:"".concat(m).concat(n),a.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),O=function(){var t=o()(r.a.mark((function t(){var n,a,i,o;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,y();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,a=[],i=0;i<n.length;i++)0===(o=n.key(i)).indexOf(d)&&a.push(o);return t.abrupt("return",a);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),k=function(){var e=o()(r.a.mark((function e(){var t,n,a,i;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,y();case 2:if(!e.sent){e.next=25;break}return e.next=6,O();case 6:t=e.sent,n=l(t),e.prev=8,n.s();case 10:if((a=n.n()).done){e.next=16;break}return i=a.value,e.next=14,T(i);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},372:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M1 3.838L4.106 7 10 1",stroke:"currentColor",strokeWidth:1.5});t.a=function SvgTick(e){return a.createElement("svg",r({viewBox:"0 0 11 9",fill:"none"},e),i)}},373:function(e,t,n){"use strict";t.a=function(e){if("string"==typeof e&&e.match(/[0-9]{8}/)){var t=e.slice(0,4),n=Number(e.slice(4,6))-1,a=e.slice(6,8);return new Date(t,n.toString(),a)}return!1}},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a="_googlesitekitDataLayer",r="data-googlesitekit-gtag"},394:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notice}));var a=n(2),r=n(3),i=n(142),o=n(20),c=n(13);function Notice(){var t=Object(r.useSelect)((function(e){return e(c.c).getDocumentationLinkURL("ga4")}));return e.createElement(i.c,{type:i.a,LearnMore:function LearnMore(){return e.createElement(o.a,{href:t,external:!0},Object(a.__)("Learn more here.","google-site-kit"))},notice:Object(a.__)("Got a Google Analytics property and want to find out how to use it with Site Kit?","google-site-kit")})}}).call(this,n(4))},398:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var a=n(0),r=n(3),i=n(7),o=n(19),c=n(32);function l(e){var t=Object(r.useSelect)((function(e){return e(i.a).hasCapability(i.K)})),n=Object(r.useSelect)((function(t){return t(o.a).getModuleStoreName(e)})),l=Object(r.useSelect)((function(e){var t;return null===(t=e(n))||void 0===t?void 0:t.getAdminReauthURL()})),s=Object(r.useDispatch)(c.a).navigateTo,u=Object(a.useCallback)((function(){return s(l)}),[l,s]);return l&&t?u:null}},402:function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(0);function Sparkline(t){var n=t.sparkline,a=t.invertChangeColor,r=n;return r&&a&&(r=Object(i.cloneElement)(n,{invertChangeColor:a})),e.createElement("div",{className:"googlesitekit-data-block__sparkline"},r)}Sparkline.propTypes={sparkline:r.a.element,invertChangeColor:r.a.bool},t.a=Sparkline}).call(this,n(4))},403:function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(2),l=n(9),s=n(85);function Change(t){var n=t.change,a=t.changeDataUnit,r=t.period,i=t.invertChangeColor,u=n;return a&&(u="%"===a?Object(l.B)(n,{style:"percent",signDisplay:"never",maximumFractionDigits:1}):Object(l.B)(n,a)),r&&(u=Object(c.sprintf)(r,u)),e.createElement("div",{className:o()("googlesitekit-data-block__change",{"googlesitekit-data-block__change--no-change":!n})},!!n&&e.createElement("span",{className:"googlesitekit-data-block__arrow"},e.createElement(s.a,{direction:0<parseFloat(n)?"up":"down",invertColor:i})),e.createElement("span",{className:"googlesitekit-data-block__value"},u))}Change.propTypes={change:r.a.oneOfType([r.a.string,r.a.number]),changeDataUnit:r.a.oneOfType([r.a.string,r.a.bool]),period:r.a.string,invertChangeColor:r.a.bool},t.a=Change}).call(this,n(4))},414:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupEnhancedConversionTrackingNotice}));var a=n(11),r=n.n(a),i=n(3),o=n(13);function SetupEnhancedConversionTrackingNotice(t){var n=t.className,a=t.message,c=Object(i.useSelect)((function(e){return e(o.c).isConversionTrackingEnabled()}));return c||void 0===c?null:e.createElement("p",{className:r()(n,"googlesitekit-color--surfaces-on-background-variant")},a)}}).call(this,n(4))},416:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PreviewGraph}));var a=n(1),r=n.n(a),i=n(577);function PreviewGraph(t){var n=t.title,a=t.GraphSVG,r=t.showIcons;return e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graph"},e.createElement("h3",{className:"googlesitekit-analytics-cta__preview-graph--title"},n),e.createElement("div",null,e.createElement(a,null)),r&&e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graph--icons"},e.createElement(i.a,{className:"googlesitekit-analytics-cta__preview-graph--up-arrow"}),e.createElement("span",{className:"googlesitekit-analytics-cta__preview-graph--bar"})))}PreviewGraph.propTypes={title:r.a.string.isRequired,GraphSVG:r.a.elementType.isRequired,showIcons:r.a.bool},PreviewGraph.defaultProps={showIcons:!0}}).call(this,n(4))},418:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var a=n(14);function r(e,t){return Object(a.sumBy)(e,t)||0}},425:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UseSnippetSwitch}));var a=n(1),r=n.n(a),i=n(0),o=n(2),c=n(3),l=n(10),s=n(8),u=n(9),d=n(18);function UseSnippetSwitch(t){var n=t.description,a=Object(d.a)(),r=Object(c.useSelect)((function(e){return e(s.r).getUseSnippet()})),m=Object(c.useDispatch)(s.r).setUseSnippet,g=Object(i.useCallback)((function(){var e=!r;m(e),Object(u.I)("".concat(a,"_analytics"),e?"enable_tag":"disable_tag","ga4")}),[r,m,a]);return void 0===r?null:e.createElement("div",{className:"googlesitekit-analytics-usesnippet"},e.createElement(l.Switch,{label:Object(o.__)("Place Google Analytics code","google-site-kit"),checked:r,onClick:g,hideLabel:!1}),n)}UseSnippetSwitch.propTypes={description:r.a.node}}).call(this,n(4))},44:function(e,t,n){"use strict";(function(e){var a=n(6),r=n.n(a),i=n(1),o=n.n(i),c=n(11),l=n.n(c),s=n(24);function PreviewBlock(t){var n,a,i=t.className,o=t.width,c=t.height,u=t.shape,d=t.padding,m=t.smallWidth,g=t.smallHeight,f=t.tabletWidth,p=t.tabletHeight,y=t.desktopWidth,b=t.desktopHeight,h=Object(s.e)(),v={width:(n={},r()(n,s.b,m),r()(n,s.c,f),r()(n,s.a,y),r()(n,s.d,y),n),height:(a={},r()(a,s.b,g),r()(a,s.c,p),r()(a,s.a,b),r()(a,s.d,y),a)};return e.createElement("div",{className:l()("googlesitekit-preview-block",i,{"googlesitekit-preview-block--padding":d}),style:{width:v.width[h]||o,height:v.height[h]||c}},e.createElement("div",{className:l()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(4))},447:function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var a=n(27),r=n.n(a),i=n(11),o=n.n(i),c=n(14),l=n(2),s=n(83),u=n(9),d=n(15),m=n.n(d),g=n(12),f=n.n(g);function p(e,t){var n=t.dateRangeLength;f()(Array.isArray(e),"report must be an array to partition."),f()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var a=function(t){return e.filter((function(e){return m()(e.dimensionValues,2)[1].value===t}))},r=-1*n;return{currentRange:a("date_range_0").slice(r),compareRange:a("date_range_1").slice(2*r,r)}}var y=n(373);function b(e,t){var n=[];return e.forEach((function(e){if(e.metricValues){var a=e.metricValues[t].value,r=e.dimensionValues[0].value,i=Object(y.a)(r);n.push([i,a])}})),n}function h(e,t,n,a){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[Object(l.__)("Users","google-site-kit"),Object(l.__)("Sessions","google-site-kit"),Object(l.__)("Engagement Rate","google-site-kit"),Object(l.__)("Session Duration","google-site-kit")],d=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[function(e){return parseFloat(e).toLocaleString()},function(e){return parseFloat(e).toLocaleString()},function(e){return Object(u.B)(e/100,{style:"percent",signDisplay:"never",maximumFractionDigits:2})},function(e){return Object(u.B)(e,"s")}],m=arguments.length>6&&void 0!==arguments[6]?arguments[6]:[c.identity,c.identity,function(e){return 100*e},c.identity],g=r()((null==e?void 0:e.rows)||[]),f=g.length;if(2*n>f){for(var y=Object(u.G)(a),h=0;n>h;h++){var v=(y.getMonth()+1).toString(),T=y.getDate().toString(),O=y.getFullYear().toString()+(2>v.length?"0":"")+v+(2>T.length?"0":"")+T;if(h>f){var k=[{dimensionValues:[{value:O},{value:"date_range_0"}],metricValues:[{value:0},{value:0}]},{dimensionValues:[{value:O},{value:"date_range_1"}],metricValues:[{value:0},{value:0}]}];g.unshift.apply(g,k)}y.setDate(y.getDate()-1)}g.push({dimensionValues:[{value:"0"},{value:"date_range_0"}]},{dimensionValues:[{value:"0"},{value:"date_range_1"}]})}var E=i[t]===Object(l.__)("Session Duration","google-site-kit"),N=E?"timeofday":"number",j=[[{type:"date",label:Object(l.__)("Day","google-site-kit")},{type:"string",role:"tooltip",p:{html:!0}},{type:N,label:i[t]},{type:N,label:Object(l.__)("Previous period","google-site-kit")}]],S=p(g,{dateRangeLength:n}),_=S.compareRange,A=S.currentRange,I=b(A,t),w=b(_,t),C=Object(s.b)(),Z={weekday:"short",month:"short",day:"numeric"};return I.forEach((function(e,n){if(e[0]&&e[1]&&w[n]){var a=m[t],r=a(e[1]),c=a(w[n][1]),s=parseFloat(c),g=Object(u.h)(r,s),f=Object(u.o)(g),p=Object(l.sprintf)(
/* translators: 1: date for user stats, 2: previous date for user stats comparison */
Object(l._x)("%1$s vs %2$s","Date range for chart tooltip","google-site-kit"),e[0].toLocaleDateString(C,Z),w[n][0].toLocaleDateString(C,Z)),y=Object(l.sprintf)(
/* translators: 1: selected stat label, 2: numeric value of selected stat, 3: up or down arrow , 4: different change in percentage */
Object(l._x)("%1$s: <strong>%2$s</strong> <em>%3$s %4$s</em>","Stat information for chart tooltip","google-site-kit"),i[t],d[t](r),f,Object(u.B)(Math.abs(g),"%"));j.push([e[0],'<div class="'.concat(o()("googlesitekit-visualization-tooltip",{"googlesitekit-visualization-tooltip--up":g>0,"googlesitekit-visualization-tooltip--down":g<0}),'">\n\t\t\t\t<p>').concat(p,"</p>\n\t\t\t\t<p>").concat(y,"</p>\n\t\t\t</div>"),E?Object(u.j)(r):r,E?Object(u.j)(c):c])}})),j}},45:function(e,t){e.exports=googlesitekit.api},47:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i}));var a={BOXES:"boxes",COMPOSITE:"composite"},r={QUARTER:"quarter",HALF:"half",FULL:"full"},i="core/widgets"},503:function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(44);function PreviewTable(t){for(var n=t.rows,a=t.rowHeight,r=t.padding,i=[],l=0;n>l;l++)i.push(e.createElement("div",{className:"googlesitekit-preview-table__row",key:"table-row-"+l},e.createElement(c.a,{width:"100%",height:a+"px"})));return e.createElement("div",{className:o()("googlesitekit-preview-table",{"googlesitekit-preview-table--padding":r})},i)}PreviewTable.propTypes={rows:r.a.number,rowHeight:r.a.number,padding:r.a.bool},PreviewTable.defaultProps={rows:11,rowHeight:35,padding:!1},t.a=PreviewTable}).call(this,n(4))},504:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportTable}));var a=n(15),r=n.n(a),i=n(11),o=n.n(i),c=n(12),l=n.n(c),s=n(1),u=n.n(s),d=n(14),m=n(0),g=n(10),f=n(114);function ReportTable(t){var n=t.rows,a=t.columns,i=t.className,c=t.limit,s=t.zeroState,u=t.gatheringData,p=void 0!==u&&u,y=t.tabbedLayout,b=void 0!==y&&y;function h(e){return!b&&e}l()(Array.isArray(n),"rows must be an array."),l()(Array.isArray(a),"columns must be an array."),a.forEach((function(e){var t=e.Component,n=e.field,a=void 0===n?null:n;l()(t||null!==a,"each column must define a Component and/or a field.")})),l()(Number.isInteger(c)||void 0===c,"limit must be an integer, if provided.");var v=a.some((function(e){return!!e.badge})),T=Object(m.useState)(0),O=r()(T,2),k=O[0],E=O[1],N=b&&a.slice(1),j=b?[a[0],N[k]]:a,S=j.filter((function(e){return!h(e.hideOnMobile)}));return e.createElement("div",{className:i},b&&e.createElement(g.TabBar,{className:"googlesitekit-tab-bar--start-aligned-high-contrast",activeIndex:k,handleActiveIndexUpdate:E},N.map((function(t){var n=t.title,a=t.badge;return e.createElement(g.Tab,{key:n,"aria-label":n},n,a)}))),e.createElement("div",{className:o()("googlesitekit-table","googlesitekit-table--with-list",{"googlesitekit-table--gathering-data":p})},e.createElement("table",{className:o()("googlesitekit-table__wrapper","googlesitekit-table__wrapper--".concat(j.length,"-col"),"googlesitekit-table__wrapper--mobile-".concat(S.length,"-col"),{"googlesitekit-table__wrapper--tabbed-layout":b})},!b&&e.createElement("thead",{className:"googlesitekit-table__head"},v&&e.createElement("tr",{className:o()("googlesitekit-table__head-badges",{"hidden-on-mobile":!a.some((function(e){var t=e.badge,n=e.hideOnMobile;return!!t&&!h(n)}))})},a.map((function(t,n){var a=t.badge,r=t.primary,i=t.hideOnMobile,c=t.className;return e.createElement("th",{className:o()("googlesitekit-table__head-item","googlesitekit-table__head-item--badge",{"googlesitekit-table__head-item--primary":r,"hidden-on-mobile":h(i)},c),key:"googlesitekit-table__head-row-badge-".concat(n)},a)}))),e.createElement("tr",{className:"googlesitekit-table__head-row"},a.map((function(t,n){var a=t.title,r=t.description,i=t.primary,c=t.hideOnMobile,l=t.className;return e.createElement("th",{className:o()("googlesitekit-table__head-item",{"googlesitekit-table__head-item--primary":i,"hidden-on-mobile":h(c)},l),"data-tooltip":r,key:"googlesitekit-table__head-row-".concat(n)},a)})))),e.createElement("tbody",{className:"googlesitekit-table__body"},p&&e.createElement("tr",{className:"googlesitekit-table__body-row googlesitekit-table__body-row--no-data"},e.createElement("td",{className:"googlesitekit-table__body-item",colSpan:j.length},e.createElement(f.b,null))),!p&&!(null==n?void 0:n.length)&&s&&e.createElement("tr",{className:"googlesitekit-table__body-row googlesitekit-table__body-row--no-data"},e.createElement("td",{className:"googlesitekit-table__body-item",colSpan:j.length},e.createElement(s,null))),!p&&n.slice(0,c).map((function(t,n){return e.createElement("tr",{className:"googlesitekit-table__body-row",key:"googlesitekit-table__body-row-".concat(n)},j.map((function(n,a){var r=n.Component,i=n.field,c=n.hideOnMobile,l=n.className,s=void 0!==i?Object(d.get)(t,i):void 0;return e.createElement("td",{key:"googlesitekit-table__body-item-".concat(a),className:o()("googlesitekit-table__body-item",{"hidden-on-mobile":h(c)},l)},e.createElement("div",{className:"googlesitekit-table__body-item-content"},r&&e.createElement(r,{row:t,fieldValue:s}),!r&&s))})))}))))))}ReportTable.propTypes={rows:u.a.arrayOf(u.a.oneOfType([u.a.array,u.a.object])).isRequired,columns:u.a.arrayOf(u.a.shape({title:u.a.string,description:u.a.string,primary:u.a.bool,className:u.a.string,field:u.a.string,hideOnMobile:u.a.bool,Component:u.a.componentType,badge:u.a.node})).isRequired,className:u.a.string,limit:u.a.number,zeroState:u.a.func,gatheringData:u.a.bool,tabbedLayout:u.a.bool}}).call(this,n(4))},505:function(e,t,n){"use strict";(function(e,a){var r=n(15),i=n.n(r),o=n(1),c=n.n(o),l=n(14),s=n(11),u=n.n(s),d=n(0);function TableOverflowContainer(t){var n=t.children,r=Object(d.useState)(!1),o=i()(r,2),c=o[0],s=o[1],m=Object(d.useRef)();Object(d.useEffect)((function(){g();var t=Object(l.debounce)(g,100);return e.addEventListener("resize",t),function(){return e.removeEventListener("resize",t)}}),[]);var g=function(){if(m.current){var e=m.current,t=e.scrollLeft,n=e.scrollWidth-e.offsetWidth;s(t<n-16&&0<n-16)}};return a.createElement("div",{onScroll:Object(l.debounce)(g,100),className:u()("googlesitekit-table-overflow",{"googlesitekit-table-overflow--gradient":c})},a.createElement("div",{ref:m,className:"googlesitekit-table-overflow__container"},n))}TableOverflowContainer.propTypes={children:c.a.element},t.a=TableOverflowContainer}).call(this,n(28),n(4))},506:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccessibleWarningIcon}));var a=n(1),r=n.n(a),i=n(0),o=n(2),c=n(105),l=n(364);function AccessibleWarningIcon(t){var n=t.height,a=void 0===n?12:n,r=t.screenReaderText,s=void 0===r?Object(o.__)("Error","google-site-kit"):r,u=t.width,d=void 0===u?14:u;return e.createElement(i.Fragment,null,e.createElement(c.a,null,s),e.createElement(l.a,{width:d,height:a}))}AccessibleWarningIcon.propTypes={height:r.a.number,screenReaderText:r.a.string,width:r.a.number}}).call(this,n(4))},507:function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return DataBlockGroup}));var r=n(81),i=n(420),o=n(0),c=n(121);function DataBlockGroup(t){var n=t.className,l=t.children,s=Object(o.useRef)(),u=function(){var t,n,a,r,i=null==s||null===(t=s.current)||void 0===t?void 0:t.querySelectorAll(".googlesitekit-data-block");if(i){var o=null===(n=i[0])||void 0===n?void 0:n.querySelector(".googlesitekit-data-block__datapoint");if(o){d(i,"");var c=parseInt(null===(a=e)||void 0===a||null===(r=a.getComputedStyle(o))||void 0===r?void 0:r.fontSize,10),l=c;i.forEach((function(t){var n,a,r,i=t.querySelector(".googlesitekit-data-block__datapoint");if(i){var o=parseInt(null===(n=e)||void 0===n||null===(a=n.getComputedStyle(i))||void 0===a?void 0:a.fontSize,10),c=null==i||null===(r=i.parentElement)||void 0===r?void 0:r.offsetWidth;if(i.scrollWidth>c&&o>14){for(;i.scrollWidth>c&&o>14;)o-=1,i.style.fontSize="".concat(o,"px");l=o}}})),c!==l&&d(i,"".concat(l,"px"))}}},d=function(e,t){e.forEach((function(e){var n=null==e?void 0:e.querySelector(".googlesitekit-data-block__datapoint");n&&(n.style.fontSize=t)}))},m=Object(c.a)(u,50);return Object(r.a)((function(){u(),e.addEventListener("resize",m)})),Object(i.a)((function(){return e.removeEventListener("resize",m)})),a.createElement("div",{ref:s,className:n},l)}}).call(this,n(28),n(4))},508:function(e,t,n){"use strict";(function(e,a){var r=n(51),i=n.n(r),o=n(53),c=n.n(o),l=n(237),s=n.n(l),u=n(68),d=n.n(u),m=n(69),g=n.n(m),f=n(49),p=n.n(f),y=n(195),b=n.n(y),h=n(1),v=n.n(h),T=n(0),O=n(2),k=n(95),E=n(172),N=n(61),j=n(9);function S(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=p()(e);if(t){var r=p()(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return g()(this,n)}}var _=function(t){d()(GoogleChartErrorHandler,t);var n=S(GoogleChartErrorHandler);function GoogleChartErrorHandler(e){var t;return i()(this,GoogleChartErrorHandler),(t=n.call(this,e)).state={error:null,info:null},t.onErrorClick=t.onErrorClick.bind(s()(t)),t}return c()(GoogleChartErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Google Charts error:",t,n),this.setState({error:t,info:n}),Object(j.I)("google_chart_error","handle_".concat(this.context||"unknown","_error"),"".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500))}},{key:"onErrorClick",value:function(){var e=this.state,t=e.error,n=e.info;b()("`".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack,"`"))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,r=t.info;return n?a.createElement("div",{className:"googlesitekit-googlechart-error-handler"},a.createElement(k.a,{description:a.createElement(T.Fragment,null,a.createElement("p",null,Object(O.__)("An error prevented this Google chart from being displayed properly. Report the exact contents of the error on the support forum to find out what caused it.","google-site-kit")),a.createElement(E.a,{message:n.message,componentStack:r.componentStack})),error:!0,onErrorClick:this.onErrorClick,onClick:this.onErrorClick,title:Object(O.__)("Error in Google Chart","google-site-kit")})):e}}]),GoogleChartErrorHandler}(T.Component);_.contextType=N.b,_.propTypes={children:v.a.node.isRequired},t.a=_}).call(this,n(28),n(4))},509:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DateMarker}));var a=n(0),r=n(266),i=n(589),o=n(10),c=n(18),l=n(121),s=n(9);function DateMarker(t){var n=t.id,u=t.text,d=Object(c.a)(),m="".concat(d,"_ga4-data-collection-line");Object(a.useEffect)((function(){Object(s.I)(m,"chart_line_view")}),[m]);var g=Object(a.useCallback)((function(){Object(s.I)(m,"chart_tooltip_view")}),[m]),f=Object(l.a)(g,5e3,{leading:!0,trailing:!1});return e.createElement(a.Fragment,null,e.createElement("div",{id:"googlesitekit-chart__date-marker-line--".concat(n),className:"googlesitekit-chart__date-marker-line"}),u&&e.createElement("div",{id:"googlesitekit-chart__date-marker-tooltip--".concat(n),className:"googlesitekit-chart__date-marker-tooltip"},e.createElement(o.Tooltip,{title:u,onOpen:f},e.createElement("span",null,e.createElement(r.a,{fill:"currentColor",icon:i.a,size:18})))))}}).call(this,n(4))},519:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return d})),n.d(t,"a",(function(){return TrackingExclusionSwitches}));var a,r=n(6),i=n.n(r),o=n(0),c=n(2),l=n(3),s=n(10),u=n(8),d=(a={},i()(a,"loggedinUsers",Object(c.__)("All logged-in users","google-site-kit")),i()(a,"contentCreators",Object(c.__)("Users that can write posts","google-site-kit")),a);function TrackingExclusionSwitches(){var t,n=Object(l.useSelect)((function(e){return e(u.r).getTrackingDisabled()})),a=Object(l.useDispatch)(u.r).setTrackingDisabled;t=n&&n.includes("loggedinUsers")?Object(c.__)("All logged-in users will be excluded from Analytics tracking","google-site-kit"):n&&n.includes("contentCreators")?Object(c.__)("Users that can write posts will be excluded from Analytics tracking","google-site-kit"):Object(c.__)("All logged-in users will be included in Analytics tracking","google-site-kit");var r=Object(o.useCallback)((function(e,t){var r=t?n.concat(e):n.filter((function(t){return t!==e}));a(r)}),[n,a]),i=Object(o.useCallback)((function(e){var t=e.target.checked;r("contentCreators",t)}),[r]),m=Object(o.useCallback)((function(e){var t=e.target.checked;r("loggedinUsers",t)}),[r]);return Array.isArray(n)?e.createElement("div",{className:"googlesitekit-settings-module__fields-group"},e.createElement("h4",{className:"googlesitekit-settings-module__fields-group-title"},Object(c.__)("Exclude Analytics","google-site-kit")),e.createElement("div",{className:"googlesitekit-settings-module__meta-item"},e.createElement("div",{className:"googlesitekit-settings-module__inline-items"},e.createElement("div",{className:"googlesitekit-settings-module__inline-item"},e.createElement(s.Switch,{label:d.loggedinUsers,checked:n.includes("loggedinUsers"),onClick:m,hideLabel:!1})),!n.includes("loggedinUsers")&&e.createElement("div",{className:"googlesitekit-settings-module__inline-item"},e.createElement(s.Switch,{label:d.contentCreators,checked:n.includes("contentCreators"),onClick:i,hideLabel:!1}))),e.createElement("p",null,t))):null}}).call(this,n(4))},522:function(e,t,n){"use strict";n.d(t,"a",(function(){return m})),n.d(t,"b",(function(){return g}));var a=n(6),r=n.n(a),i=n(12),o=n.n(i),c=n(106),l=n(215),s=n(8);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e){var t=e.siteName,n=e.siteURL,a=e.timezone,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Intl.DateTimeFormat().resolvedOptions().timeZone;o()(Object(c.a)(n),"A valid siteURL is required.");var u=new URL(n),d=u.hostname,m=u.pathname;return r()({accountName:t||d,propertyName:"".concat(d).concat(m).replace(/\/$/,""),dataStreamName:d,countryCode:l.c[a]||l.c[i],timezone:l.c[a]?a:i},s.i,!0)}var g=function(e){return Array.isArray(e)?e.map((function(e){return d(d({},function(e){var t,n=null===(t=e.account)||void 0===t?void 0:t.match(/accounts\/([^/]+)/),a=null==n?void 0:n[1];return d(d({},e),{},{_id:a})}(e)),{},{propertySummaries:(e.propertySummaries||[]).map((function(e){return function(e){var t,n,a=null===(t=e.property)||void 0===t?void 0:t.match(/properties\/([^/]+)/),r=null==a?void 0:a[1],i=null===(n=e.parent)||void 0===n?void 0:n.match(/accounts\/([^/]+)/),o=null==i?void 0:i[1];return d(d({},e),{},{_id:r,_accountID:o})}(e)}))})})):e}},54:function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,a=t.reconnectURL,r=t.noPrefix;if(!n)return null;var l=n;void 0!==r&&r||(l=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),a&&Object(i.a)(a)&&(l=l+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),a));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(l,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:r.a.string.isRequired,reconnectURL:r.a.string,noPrefix:r.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},551:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccountCreate}));var a=n(5),r=n.n(a),i=n(16),o=n.n(i),c=n(15),l=n.n(c),s=n(2),u=n(0),d=n(45),m=n.n(d),g=n(3),f=n(10),p=n(8),y=n(13),b=n(7),h=n(29),v=n(32),T=n(35),O=n(9),k=n(522),E=n(17),N=n(141),j=n(563),S=n(564),_=n(565),A=n(566),I=n(567),w=n(352),C=n(18),Z=n(414);function AccountCreate(){var t=Object(u.useState)(!1),n=l()(t,2),a=n[0],i=n[1],c=Object(g.useSelect)((function(e){return e(p.r).getAccountSummaries()})),d=Object(g.useSelect)((function(e){return e(p.r).hasFinishedResolution("getAccountSummaries")})),M=Object(g.useSelect)((function(e){return e(p.r).getAccountTicketTermsOfServiceURL()})),D=Object(g.useSelect)((function(e){return e(p.r).canSubmitAccountCreate()})),P=Object(g.useSelect)((function(e){return e(p.r).isDoingCreateAccount()})),L=Object(g.useSelect)((function(e){return e(b.a).hasScope(p.h)})),G=Object(g.useSelect)((function(e){return e(b.a).hasScope(p.p)})),R=Object(g.useSelect)((function(e){return e(h.a).hasForm(p.m)})),x=Object(g.useSelect)((function(e){return e(h.a).getValue(p.m,"autoSubmit")})),B=Object(g.useSelect)((function(e){return e(y.c).getReferenceSiteURL()})),U=Object(g.useSelect)((function(e){return e(y.c).getSiteName()})),W=Object(g.useSelect)((function(e){return e(y.c).getTimezone()})),V=Object(C.a)(),F=Object(g.useDispatch)(h.a).setValues,z=Object(g.useDispatch)(v.a).navigateTo,H=Object(g.useDispatch)(p.r).createAccount,q=Object(g.useDispatch)(b.a).setPermissionScopeError,K=Object(g.useDispatch)(y.c),J=K.setConversionTrackingEnabled,Y=K.saveConversionTrackingSettings,$=L;Object(u.useEffect)((function(){M&&o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m.a.invalidateCache("modules","analytics-4");case 2:z(M);case 3:case"end":return e.stop()}}),e)})))()}),[M,z]),Object(u.useEffect)((function(){R||F(p.m,Object(k.a)({siteName:U,siteURL:B,timezone:W}))}),[R,U,B,W,F]);var X=Object(u.useCallback)(o()(r.a.mark((function e(){var t,n;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=[],L||t.push(p.h),G||t.push(p.p),!(t.length>0)){e.next=7;break}return F(p.m,{autoSubmit:!0}),q({code:T.a,message:Object(s.__)("Additional permissions are required to create a new Analytics account.","google-site-kit"),data:{status:403,scopes:t,skipModal:!0}}),e.abrupt("return");case 7:return F(p.m,{autoSubmit:!1}),e.next=10,Object(O.I)("".concat(V,"_analytics"),"create_account","proxy");case 10:return e.next=12,H();case 12:if(n=e.sent,n.error){e.next=19;break}return J(!0),e.next=18,Y();case 18:i(!0);case 19:case"end":return e.stop()}}),e)}))),[L,G,F,V,H,q,J,Y]);Object(u.useEffect)((function(){$&&x&&X()}),[$,x,X]);var Q=Object(g.useDispatch)(p.r).rollbackSettings,ee=Object(u.useCallback)((function(){return Q()}),[Q]);return P||a||!d||void 0===$?e.createElement(f.ProgressBar,null):e.createElement("div",null,e.createElement(N.a,{moduleSlug:"analytics-4",storeName:p.r}),e.createElement("h3",{className:"googlesitekit-heading-4"},Object(s.__)("Create your Analytics account","google-site-kit")),e.createElement("p",null,Object(s.__)("We’ve pre-filled the required information for your new account. Confirm or edit any details:","google-site-kit")),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(E.a,{size:6},e.createElement(S.a,null)),e.createElement(E.a,{size:6},e.createElement(_.a,null)),e.createElement(E.a,{size:6},e.createElement(I.a,null))),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(A.a,null),e.createElement(j.a,null)),e.createElement("div",{className:"googlesitekit-setup-module__inputs"},e.createElement(w.a,{formName:p.m,className:"googlesitekit-margin-bottom-0"}),e.createElement(Z.a,{className:"googlesitekit-margin-top-0",message:Object(s.__)("To track how visitors interact with your site, Site Kit will enable enhanced conversion tracking. You can always disable it in settings.","google-site-kit")})),e.createElement("p",null,$&&e.createElement("span",null,Object(s.__)("You will be redirected to Google Analytics to accept the terms of service.","google-site-kit")),!$&&e.createElement("span",null,Object(s.__)("You will need to give Site Kit permission to create an Analytics account on your behalf and also accept the Google Analytics terms of service.","google-site-kit"))),e.createElement("div",{className:"googlesitekit-setup-module__action"},e.createElement(f.Button,{disabled:!D,onClick:X},Object(s.__)("Create Account","google-site-kit")),c&&!!c.length&&e.createElement(f.Button,{tertiary:!0,className:"googlesitekit-setup-module__sub-action",onClick:ee},Object(s.__)("Back","google-site-kit"))))}}).call(this,n(4))},552:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccountSelect}));var a=n(1),r=n.n(a),i=n(0),o=n(2),c=n(10),l=n(3),s=n(8),u=n(9),d=n(18);function AccountSelect(t){var n=t.hasModuleAccess,a=t.onChange,r=Object(d.a)(),m=Object(l.useSelect)((function(e){return e(s.r).getAccountID()})),g=Object(l.useSelect)((function(e){return e(s.r).getAccountSummaries()})),f=Object(l.useSelect)((function(e){return e(s.r).hasFinishedResolution("getAccountSummaries")})),p=Object(l.useDispatch)(s.r).selectAccount,y=Object(i.useCallback)((function(e,t){var n=t.dataset.value;if(m!==n){p(n);var i=n===s.a?"change_account_new":"change_account";Object(u.I)("".concat(r,"_analytics"),i),a&&a()}}),[m,p,r,a]);return f?!1===n?e.createElement(c.Select,{className:"googlesitekit-analytics__select-account",label:Object(o.__)("Account","google-site-kit"),value:m,enhanced:!0,outlined:!0,disabled:!0},e.createElement(c.Option,{value:m},m)):e.createElement(c.Select,{className:"googlesitekit-analytics__select-account",label:Object(o.__)("Account","google-site-kit"),value:m,onEnhancedChange:y,enhanced:!0,outlined:!0},(g||[]).concat({_id:s.a,displayName:Object(o.__)("Set up a new account","google-site-kit")}).map((function(t,n){var a=t._id,r=t.displayName;return e.createElement(c.Option,{key:n,value:a},r)}))):e.createElement(c.ProgressBar,{small:!0})}AccountSelect.propTypes={hasModuleAccess:r.a.bool}}).call(this,n(4))},553:function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return AccountCreateLegacy}));var r=n(5),i=n.n(r),o=n(16),c=n.n(o),l=n(0),s=n(2),u=n(3),d=n(10),m=n(9),g=n(8),f=n(141),p=n(394),y=n(18);function AccountCreateLegacy(){var t=Object(u.useSelect)((function(e){return e(g.r).getAccountSummaries()})),n=Object(u.useSelect)((function(e){return e(g.r).hasFinishedResolution("getAccountSummaries")})),r=Object(u.useSelect)((function(e){return e(g.r).getAccountID()})),o=g.a===r,b=Object(u.useSelect)((function(e){return e(g.r).getServiceURL({path:"/provision/SignUp"})})),h=Object(y.a)(),v=Object(l.useCallback)(function(){var t=c()(i.a.mark((function t(n){return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n.preventDefault(),t.next=3,Object(m.I)("".concat(h,"_analytics"),"create_account","custom-oauth");case 3:e.open(b,"_blank");case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),[b,h]),T=Object(u.useDispatch)(g.r),O=T.resetAccountSummaries,k=T.resetAccountSettings,E=Object(l.useCallback)((function(){O(),k()}),[k,O]);return n?a.createElement("div",null,a.createElement(p.a,null),a.createElement(f.a,{moduleSlug:"analytics-4",storeName:g.r}),!o&&t&&0===t.length&&a.createElement("p",null,Object(s.__)('Looks like you don’t have an Analytics account yet. Once you create it, click on "Re-fetch my account" and Site Kit will locate it.',"google-site-kit")),o&&a.createElement(l.Fragment,null,a.createElement("p",null,Object(s.__)("To create a new account, click the button below which will open the Google Analytics account creation screen in a new window.","google-site-kit")),a.createElement("p",null,Object(s.__)("Once completed, click the link below to re-fetch your accounts to continue.","google-site-kit"))),a.createElement("div",{className:"googlesitekit-setup-module__action"},a.createElement(d.Button,{onClick:v},Object(s.__)("Create an account","google-site-kit")),a.createElement("div",{className:"googlesitekit-setup-module__sub-action"},a.createElement(d.Button,{tertiary:!0,onClick:E},Object(s.__)("Re-fetch My Account","google-site-kit"))))):a.createElement(d.ProgressBar,null)}}).call(this,n(28),n(4))},554:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WebDataStreamSelect}));var a=n(11),r=n.n(a),i=n(1),o=n.n(i),c=n(0),l=n(2),s=n(10),u=n(3),d=n(8),m=n(67),g=n(9),f=n(18);function WebDataStreamSelect(t){var n=t.hasModuleAccess,a=t.isDisabled,i=t.className,o=t.onChange,p=Object(u.useSelect)((function(e){return e(d.r).getAccountID()})),y=Object(u.useSelect)((function(e){return e(d.r).getSettings()||{}})),b=y.propertyID,h=y.webDataStreamID,v=y.measurementID,T=Object(u.useSelect)((function(e){return Object(m.e)(b)&&!1!==n?e(d.r).getWebDataStreams(b):[]})),O=Object(u.useSelect)((function(e){return!a&&e(d.r).isLoadingWebDataStreams({hasModuleAccess:n})})),k=Object(f.a)(),E=Object(u.useDispatch)(d.r),N=E.setWebDataStreamID,j=E.updateSettingsForMeasurementID,S=Object(c.useCallback)((function(e,t){var n,a,r=t.dataset.value;h!==r&&(N(r),j((null===(n=T.find((function(e){return e._id===r})))||void 0===n||null===(a=n.webStreamData)||void 0===a?void 0:a.measurementId)||""),Object(g.I)("".concat(k,"_analytics"),r===d.z?"change_webdatastream_new":"change_webdatastream","ga4"),o&&o())}),[T,h,N,j,k,o]);if(!Object(m.a)(p))return null;if(O)return e.createElement(s.ProgressBar,{smallHeight:80,desktopHeight:88,small:!0});var _=void 0===h||""===h||Object(m.i)(h);return!1===n?e.createElement(s.Select,{className:r()("googlesitekit-analytics-4__select-webdatastream",i),label:Object(l.__)("Web Data Stream","google-site-kit"),value:v,enhanced:!0,outlined:!0,disabled:!0},e.createElement(s.Option,{value:v},v)):e.createElement(s.Select,{className:r()("googlesitekit-analytics-4__select-webdatastream",i,{"mdc-select--invalid":!_}),label:Object(l.__)("Web Data Stream","google-site-kit"),value:h,onEnhancedChange:S,disabled:a||!Object(m.f)(b),enhanced:!0,outlined:!0},(T||[]).concat({_id:d.z,displayName:Object(l.__)("Set up a new web data stream","google-site-kit")}).map((function(t,n){var a=t._id,r=t.displayName,i=t.webStreamData,o=void 0===i?{}:i;return e.createElement(s.Option,{key:n,value:a},a!==d.z&&(null==o?void 0:o.measurementId)?Object(l.sprintf)(
/* translators: 1: Data stream name. 2: Measurement ID. */
Object(l._x)("%1$s (%2$s)","Analytics data stream name and measurement ID","google-site-kit"),r,o.measurementId):r)})))}WebDataStreamSelect.propTypes={hasModuleAccess:o.a.bool,isDisabled:o.a.bool,className:o.a.string}}).call(this,n(4))},555:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PropertySelect}));var a=n(11),r=n.n(a),i=n(1),o=n.n(i),c=n(0),l=n(2),s=n(10),u=n(3),d=n(8),m=n(67),g=n(9),f=n(18);function PropertySelect(t){var n=t.isDisabled,a=t.hasModuleAccess,i=t.className,o=t.onChange,p=void 0===o?function(){}:o,y=Object(u.useSelect)((function(e){return e(d.r).getAccountID()})),b=Object(u.useSelect)((function(e){return!1===a||n?null:e(d.r).getPropertySummaries(y)||[]})),h=Object(u.useSelect)((function(e){return e(d.r).getPropertyID()})),v=Object(u.useSelect)((function(e){return!n&&(e(d.r).isLoadingPropertySummaries()||e(d.r).isLoadingWebDataStreams({hasModuleAccess:a}))})),T=Object(f.a)(),O=Object(u.useDispatch)(d.r).selectProperty,k=Object(c.useCallback)((function(e,t){var n=t.dataset.value;h!==n&&(O(n),Object(g.I)("".concat(T,"_analytics"),n===d.s?"change_property_new":"change_property","ga4"),p())}),[p,h,O,T]);if(!Object(m.a)(y))return null;if(v)return e.createElement(s.ProgressBar,{smallHeight:80,desktopHeight:88,small:!0});var E=void 0===h||""===h||Object(m.f)(h);return!1===a?e.createElement(s.Select,{className:r()("googlesitekit-analytics-4__select-property",i),label:Object(l.__)("Property","google-site-kit"),value:h,enhanced:!0,outlined:!0,disabled:!0},e.createElement(s.Option,{value:h},h)):e.createElement(s.Select,{className:r()("googlesitekit-analytics-4__select-property",i,{"mdc-select--invalid":!E,"googlesitekit-analytics-4__select-property--loaded":!n&&!v}),label:Object(l.__)("Property","google-site-kit"),value:h,onEnhancedChange:k,disabled:n,enhanced:!0,outlined:!0},(b||[]).concat({_id:d.s,displayName:Object(l.__)("Set up a new property","google-site-kit")}).map((function(t){var n=t._id,a=t.displayName;return e.createElement(s.Option,{key:n,value:n},n===d.s?a:Object(l.sprintf)(
/* translators: 1: Property name. 2: Property ID. */
Object(l._x)("%1$s (%2$s)","Analytics property name and ID","google-site-kit"),a,n))})))}PropertySelect.propTypes={isDisabled:o.a.bool,hasModuleAccess:o.a.bool,className:o.a.string,onChange:o.a.func}}).call(this,n(4))},556:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WebDataStreamNameInput}));var a=n(11),r=n.n(a),i=n(81),o=n(0),c=n(2),l=n(106),s=n(3),u=n(29),d=n(13),m=n(8),g=n(506),f=n(10),p=n(67);function WebDataStreamNameInput(){var t=Object(s.useSelect)((function(e){return e(m.r).getPropertyID()})),n=Object(s.useSelect)((function(e){return e(m.r).getWebDataStreamID()})),a=Object(s.useSelect)((function(e){return e(u.a).getValue(m.o,"webDataStreamName")})),y=Object(s.useSelect)((function(e){return!!Object(p.e)(t)&&e(m.r).doesWebDataStreamExist(t,a)})),b=Object(s.useSelect)((function(e){return e(d.c).getReferenceSiteURL()})),h=Object(s.useDispatch)(u.a).setValues,v=Object(o.useCallback)((function(e){var t=e.currentTarget;h(m.o,{webDataStreamName:t.value})}),[h]);if(Object(i.a)((function(){if(!a&&Object(l.a)(b)){var e=new URL(b).hostname;h(m.o,{webDataStreamName:e})}})),n!==m.z)return null;var T=y||!a||!Object(p.h)(a),O=!1;return y?O=Object(c.__)("A web data stream with this name already exists.","google-site-kit"):a?Object(p.h)(a)||(O=Object(c.__)("This is not a valid web data stream name.","google-site-kit")):O=Object(c.__)("A web data stream name is required.","google-site-kit"),e.createElement("div",{className:"googlesitekit-analytics-webdatastreamname"},e.createElement(f.TextField,{className:r()({"mdc-text-field--error":T}),label:Object(c.__)("Web Data Stream Name","google-site-kit"),outlined:!0,helperText:O,trailingIcon:T&&e.createElement("span",{className:"googlesitekit-text-field-icon--error"},e.createElement(g.a,null)),value:a,onChange:v}))}}).call(this,n(4))},563:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TimezoneSelect}));var a=n(0),r=n(2),i=n(10),o=n(3),c=n(215),l=n(8),s=n(29);function TimezoneSelect(){var t=Object(o.useSelect)((function(e){return e(s.a).getValue(l.m,"countryCode")})),n=Object(o.useSelect)((function(e){return e(s.a).getValue(l.m,"timezone")})),u=Object(o.useDispatch)(s.a).setValues,d=Object(a.useCallback)((function(e,t){u(l.m,{timezone:t.dataset.value})}),[u]);return e.createElement(i.Select,{className:"googlesitekit-analytics__select-timezone",label:Object(r.__)("Timezone","google-site-kit"),value:n,onEnhancedChange:d,disabled:!t,enhanced:!0,outlined:!0},(c.d[t]||[]).map((function(t,n){var a=t.timeZoneId,r=t.displayName;return e.createElement(i.Option,{key:n,value:a},r)})))}}).call(this,n(4))},564:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AccountField}));var a=n(0),r=n(2),i=n(331),o=n(3),c=n(8),l=n(29);function AccountField(){var t=Object(o.useSelect)((function(e){return e(l.a).getValue(c.m,"accountName")})),n=Object(o.useDispatch)(l.a).setValues,s=Object(a.useCallback)((function(e){n(c.m,{accountName:e})}),[n]);return e.createElement(i.a,{label:Object(r.__)("Account","google-site-kit"),hasError:!t,value:t,setValue:s,name:"account"})}}).call(this,n(4))},565:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PropertyField}));var a=n(0),r=n(2),i=n(331),o=n(3),c=n(8),l=n(29);function PropertyField(){var t=Object(o.useSelect)((function(e){return e(l.a).getValue(c.m,"propertyName")})),n=Object(o.useDispatch)(l.a).setValues,s=Object(a.useCallback)((function(e){n(c.m,{propertyName:e})}),[n]);return e.createElement(i.a,{label:Object(r.__)("Property","google-site-kit"),value:t,hasError:!t,setValue:s,name:"property"})}}).call(this,n(4))},566:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CountrySelect}));var a=n(0),r=n(2),i=n(10),o=n(3),c=n(215),l=n(8),s=n(29);function CountrySelect(){var t=Object(o.useSelect)((function(e){return e(s.a).getValue(l.m,"countryCode")})),n=Object(o.useDispatch)(s.a).setValues,u=Object(a.useCallback)((function(e,a){var r=a.dataset.value;r!==t&&c.b[r]&&n(l.m,{countryCode:r,timezone:c.b[r].defaultTimeZoneId})}),[n,t]);return e.createElement(i.Select,{className:"googlesitekit-analytics__select-country",label:Object(r.__)("Country","google-site-kit"),value:t,onEnhancedChange:u,enhanced:!0,outlined:!0},c.a.map((function(t,n){var a=t.countryCode,r=t.displayName;return e.createElement(i.Option,{key:n,value:a},r)})))}}).call(this,n(4))},567:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WebDataStreamField}));var a=n(0),r=n(2),i=n(331),o=n(3),c=n(8),l=n(29);function WebDataStreamField(){var t=Object(o.useSelect)((function(e){return e(l.a).getValue(c.m,"dataStreamName")})),n=Object(o.useDispatch)(l.a).setValues,s=Object(a.useCallback)((function(e){n(c.m,{dataStreamName:e})}),[n]);return e.createElement(i.a,{label:Object(r.__)("Web Data Stream","google-site-kit"),value:t,hasError:!t,setValue:s,name:"dataStream"})}}).call(this,n(4))},57:function(e,t,n){"use strict";(function(e){var a,r;n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var i=new Set((null===(a=e)||void 0===a||null===(r=a._googlesitekitBaseData)||void 0===r?void 0:r.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;return t instanceof Set&&t.has(e)}}).call(this,n(28))},576:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActivateAnalyticsCTA}));var a=n(15),r=n.n(a),i=n(1),o=n.n(i),c=n(0),l=n(2),s=n(10),u=n(3),d=n(19),m=n(8),g=n(32),f=n(159),p=n(398),y=n(121);function ActivateAnalyticsCTA(t){var n=t.children,a=Object(f.a)("analytics-4"),i=Object(p.a)("analytics-4"),o=Object(u.useSelect)((function(e){return e(d.a).isModuleActive("analytics-4")})),b=Object(u.useSelect)((function(e){return(0,e(d.a).isModuleAvailable)("analytics-4")&&!!e(m.r)})),h=Object(c.useState)(!1),v=r()(h,2),T=v[0],O=v[1],k=Object(u.useSelect)((function(e){if(!b)return!1;var t=e(m.r).getAdminReauthURL();return!!t&&e(g.a).isNavigatingTo(t)})),E=Object(u.useSelect)((function(e){return!!b&&e(d.a).isFetchingSetModuleActivation("analytics-4",!0)})),N=Object(y.a)(O,3e3);Object(c.useEffect)((function(){E||k?O(!0):N(!1)}),[E,k,N]);var j=o?i:a;return b&&j?e.createElement("div",{className:"googlesitekit-analytics-cta"},e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graphs"},n),e.createElement("div",{className:"googlesitekit-analytics-cta__details"},e.createElement("p",{className:"googlesitekit-analytics-cta--description"},Object(l.__)("See how many people visit your site from Search and track how you’re achieving your goals","google-site-kit")),e.createElement(s.SpinnerButton,{onClick:j,isSaving:T},o?Object(l.__)("Complete setup","google-site-kit"):Object(l.__)("Set up Google Analytics","google-site-kit")))):null}ActivateAnalyticsCTA.propTypes={children:o.a.node.isRequired}}).call(this,n(4))},577:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M0 3.01l.443.387 1.755-1.534v3.344h.628V1.863L4.578 3.4l.446-.39L2.512.811 0 3.009z",fill:"currentColor"});t.a=function SvgArrowUp(e){return a.createElement("svg",r({viewBox:"0 0 6 6",fill:"none"},e),i)}},58:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=a.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return a.createElement("svg",r({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),i,o)}},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var a=n(39);function r(e){return function(){e[a.a]=e[a.a]||[],e[a.a].push(arguments)}}},594:function(e,t,n){(function(e){Object.prototype.hasOwnProperty.call(e,"google")||(e.google={})}).call(this,n(28))},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var a=n(0),r=Object(a.createContext)(""),i=(r.Consumer,r.Provider);t.b=r},65:function(e,t,n){"use strict";n.d(t,"c",(function(){return p})),n.d(t,"a",(function(){return y})),n.d(t,"b",(function(){return b})),n.d(t,"d",(function(){return v}));var a=n(6),r=n.n(a),i=n(0);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var c=i.createElement("path",{d:"M10 15.27L16.18 19l-1.64-7.03L20 7.24l-7.19-.61L10 0 7.19 6.63 0 7.24l5.46 4.73L3.82 19 10 15.27z"});var l=function SvgInfoIcon(e){return i.createElement("svg",o({viewBox:"0 0 20 20",fill:"currentColor"},e),c)};function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var u=i.createElement("path",{d:"M0 4h2v7H0zm0-4h2v2H0z",fill:"currentColor",fillRule:"evenodd"});var d,m=function SvgSuggestionIcon(e){return i.createElement("svg",s({viewBox:"0 0 2 11"},e),u)},g=n(186),f=n(74),p="warning",y="info",b="suggestion",h=(d={},r()(d,y,l),r()(d,p,g.a),r()(d,b,m),d),v=function(e){return h[e]||f.a}},66:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return r}));var a="modules/search-console",r=1},67:function(e,t,n){"use strict";n.d(t,"b",(function(){return m})),n.d(t,"e",(function(){return g})),n.d(t,"f",(function(){return f})),n.d(t,"g",(function(){return p})),n.d(t,"i",(function(){return y})),n.d(t,"h",(function(){return b})),n.d(t,"d",(function(){return h})),n.d(t,"c",(function(){return v})),n.d(t,"l",(function(){return T})),n.d(t,"k",(function(){return O})),n.d(t,"j",(function(){return k}));var a=n(12),r=n.n(a),i=n(14),o=n(8),c=n(9);n.d(t,"a",(function(){return c.x}));var l=n(178),s=n(275),u=n(122),d=n(342);function m(e){return e===o.a||Object(c.x)(e)}function g(e){return"string"==typeof e&&/^\d+$/.test(e)}function f(e){return e===o.s||g(e)}function p(e){return"string"==typeof e&&/^\d+$/.test(e)}function y(e){return e===o.z||p(e)}function b(e){return"string"==typeof e&&e.trim().length>0}function h(e){return"string"==typeof e&&/^G-[a-zA-Z0-9]+$/.test(e)}function v(e){return"string"==typeof e&&/^(G|GT|AW)-[a-zA-Z0-9]+$/.test(e)}function T(e){r()(Object(i.isPlainObject)(e),"options for Analytics 4 report must be an object."),r()(Object(l.a)(e),"Either date range or start/end dates must be provided for Analytics 4 report.");var t=Object(s.a)(e),n=t.metrics,a=t.dimensions,o=t.dimensionFilters,c=t.metricFilters,d=t.orderby;r()(n.length,"Requests must specify at least one metric for an Analytics 4 report."),r()(Object(u.d)(n),'metrics for an Analytics 4 report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property. Metric names must match the expression ^[a-zA-Z0-9_]+$.'),a&&r()(Object(u.b)(a),'dimensions for an Analytics 4 report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property.'),o&&r()(Object(u.a)(o),"dimensionFilters for an Analytics 4 report must be a map of dimension names as keys and dimension values as values."),c&&r()(Object(u.c)(c),"metricFilters for an Analytics 4 report must be a map of metric names as keys and filter value(s) as numeric fields, depending on the filterType."),d&&r()(Object(u.e)(d),'orderby for an Analytics 4 report must be an array of OrderBy objects where each object should have either a "metric" or "dimension" property, and an optional "desc" property.')}function O(e){r()(Object(i.isPlainObject)(e),"options for Analytics 4 pivot report must be an object."),r()(Object(l.a)(e),"Start/end dates must be provided for Analytics 4 pivot report.");var t=Object(s.a)(e),n=t.metrics,a=t.dimensions,o=t.dimensionFilters,c=t.metricFilters,m=t.pivots,g=t.orderby,f=t.limit;r()(n.length,"Requests must specify at least one metric for an Analytics 4 pivot report."),r()(Object(u.d)(n),'metrics for an Analytics 4 pivot report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property. Metric names must match the expression ^[a-zA-Z0-9_]+$.'),r()(Object(d.a)(m),'pivots for an Analytics 4 pivot report must be an array of objects. Each object must have a "fieldNames" property and a "limit".'),g&&r()(Array.isArray(g),"orderby for an Analytics 4 pivot report must be passed within a pivot."),f&&r()("number"==typeof f,"limit for an Analytics 4 pivot report must be passed within a pivot."),a&&r()(Object(u.b)(a),'dimensions for an Analytics 4 pivot report must be either a string, an array of strings, an object, an array of objects, or a mix of strings and objects. Objects must have a "name" property.'),o&&r()(Object(u.a)(o),"dimensionFilters for an Analytics 4 pivot report must be a map of dimension names as keys and dimension values as values."),c&&r()(Object(u.c)(c),"metricFilters for an Analytics 4 pivot report must be a map of metric names as keys and filter value(s) as numeric fields, depending on the filterType.")}function k(e){var t=["displayName","description","membershipDurationDays","eventTrigger","exclusionDurationMode","filterClauses"];r()(Object(i.isPlainObject)(e),"Audience must be an object."),Object.keys(e).forEach((function(e){r()(t.includes(e),'Audience object must contain only valid keys. Invalid key: "'.concat(e,'"'))})),["displayName","description","membershipDurationDays","filterClauses"].forEach((function(t){r()(e[t],'Audience object must contain required keys. Missing key: "'.concat(t,'"'))})),r()(Object(i.isArray)(e.filterClauses),"filterClauses must be an array with AudienceFilterClause objects.")}},678:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DetailsPermaLinks}));var a=n(11),r=n.n(a),i=n(1),o=n.n(i),c=n(0),l=n(3),s=n(13),u=n(20),d=n(9);function DetailsPermaLinks(t){var n=t.title,a=t.path,i=t.serviceURL,o=Object(l.useSelect)((function(e){return e(s.c).getReferenceSiteURL()})),m=Object(d.q)(o,a),g=Object(l.useSelect)((function(e){return e(s.c).getAdminURL("googlesitekit-dashboard",{permaLink:m})}));return e.createElement(c.Fragment,null,e.createElement(u.a,{className:"googlesitekit-font-weight-medium",href:i||g,external:!!i,hideExternalIndicator:!0},n),e.createElement(u.a,{className:r()("googlesitekit-display-block","googlesitekit-overflow-wrap-break-word"),href:m,target:"_blank",small:!0},a))}DetailsPermaLinks.propTypes={title:o.a.string,path:o.a.string,serviceURL:o.a.string}}).call(this,n(4))},693:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M86.66 6.101a22.375 22.375 0 016.226-1.09l.215 7.871a14.544 14.544 0 00-4.046.709L86.66 6.1z",fill:"#DEDEDE"}),o=a.createElement("path",{d:"M75.423 14.275a22.544 22.544 0 0110.57-7.945l2.628 7.41a14.655 14.655 0 00-6.871 5.164l-6.327-4.63z",fill:"#C6C6C6"}),c=a.createElement("path",{d:"M75.317 40.725a22.482 22.482 0 01-4.226-12.872 22.637 22.637 0 013.925-13l6.47 4.426a14.714 14.714 0 00-2.552 8.45c.035 3.01.995 5.932 2.747 8.367l-6.364 4.63z",fill:"#F1F1F1"}),l=a.createElement("path",{d:"M106.601 45.702a22.401 22.401 0 01-16.346 4.074 22.282 22.282 0 01-14.517-8.485l6.217-4.827a14.483 14.483 0 009.436 5.515 14.562 14.562 0 0010.625-2.648l4.585 6.371z",fill:"#959595"}),s=a.createElement("path",{d:"M93.59 5c4.673 0 9.223 1.466 13.013 4.194a22.369 22.369 0 018.129 11.018 22.624 22.624 0 01-7.567 25.067l-4.783-6.223a14.703 14.703 0 004.919-16.293 14.538 14.538 0 00-5.284-7.162 14.477 14.477 0 00-8.458-2.726L93.59 5z",fill:"#C7C7C7"}),u=a.createElement("circle",{cx:83.5,cy:56.899,r:1.5,fill:"#959595"}),d=a.createElement("circle",{cx:90.5,cy:56.899,r:1.5,fill:"#C7C7C7"}),m=a.createElement("circle",{cx:97.5,cy:56.899,r:1.5,fill:"#DEDEDE"}),g=a.createElement("circle",{cx:104.5,cy:56.899,r:1.5,fill:"#F1F1F1"}),f=a.createElement("path",{stroke:"#ECE9F1",strokeWidth:.937,strokeLinecap:"round",d:"M.468 58.531h55.064"}),p=a.createElement("path",{stroke:"#ECE9F1",strokeWidth:.468,strokeLinecap:"round",d:"M.234 44.765h55.532M.234 30.765h55.532M.234 16.766h55.532"}),y=a.createElement("path",{opacity:.08,d:"M25.531 47.668c-4.138-1.288-5.95-4.746-9.87-5.24-4.053-.51-7.2 12.53-15.661 13.777V59h56V35.07c-2.25-.486-4.367-17.89-9.25-16.601-4.882 1.288-8.475 20.892-12.365 14.578-3.89-6.313-4.716 15.91-8.854 14.622z",fill:"url(#cta-graph-traffic_svg__paint0_linear_435_1677)"}),b=a.createElement("path",{d:"M2.766 1.116L.883 6.3h-.77L2.281.612h.496l-.011.504zM4.344 6.3L2.457 1.116 2.445.612h.496L5.117 6.3h-.773zm-.098-2.106v.618H1.051v-.618h3.195zM6.555.3v6h-.727v-6h.727zM8.5.3v6h-.727v-6H8.5zm5.73 5.023v-3.25h.727V6.3h-.691l-.036-.977zm.137-.89l.301-.008c0 .281-.03.541-.09.781a1.678 1.678 0 01-.281.617c-.13.175-.3.311-.512.41a1.845 1.845 0 01-.77.145c-.205 0-.394-.03-.566-.09a1.132 1.132 0 01-.437-.277 1.262 1.262 0 01-.285-.489 2.355 2.355 0 01-.098-.722V2.073h.723v2.735c0 .19.02.347.062.472.044.123.103.22.176.293.075.07.159.12.25.149.094.028.19.043.289.043.307 0 .55-.059.73-.176.18-.12.309-.28.387-.48.08-.204.121-.429.121-.676zm4.152.746c0-.104-.023-.2-.07-.29-.044-.09-.137-.173-.277-.245-.138-.076-.346-.141-.625-.196a4.95 4.95 0 01-.637-.176 1.931 1.931 0 01-.48-.246c-.13-.096-.23-.21-.301-.34a.948.948 0 01-.106-.457c0-.166.037-.324.11-.472.075-.149.18-.28.316-.395.138-.114.304-.204.496-.27.193-.064.408-.097.645-.097.338 0 .627.06.867.18s.423.28.55.48c.128.198.192.418.192.66h-.722a.62.62 0 00-.106-.34.779.779 0 00-.3-.277.971.971 0 00-.481-.11c-.2 0-.363.032-.488.095a.627.627 0 00-.27.23.58.58 0 00-.043.508c.029.06.078.116.148.168.07.05.17.096.297.14.128.045.29.089.489.133.346.078.631.172.855.282.224.109.39.243.5.402.11.159.164.351.164.578a1.126 1.126 0 01-.45.906 1.65 1.65 0 01-.515.258c-.198.06-.42.09-.668.09-.372 0-.687-.066-.945-.2a1.462 1.462 0 01-.586-.515c-.133-.21-.2-.434-.2-.668h.727c.01.198.068.356.172.473a.877.877 0 00.383.246c.151.047.3.07.45.07.197 0 .363-.026.495-.078a.69.69 0 00.31-.215.498.498 0 00.105-.312zm3.426 1.199a2.08 2.08 0 01-.8-.149 1.817 1.817 0 01-.614-.425c-.169-.183-.3-.399-.39-.649a2.38 2.38 0 01-.137-.82v-.164c0-.344.05-.65.152-.918.102-.27.24-.5.414-.688.175-.187.373-.329.594-.425.221-.097.45-.145.688-.145.302 0 .562.052.78.156.222.105.403.25.544.438.14.185.245.404.312.656.068.25.102.524.102.82v.325h-3.156V3.8h2.433v-.055c-.01-.187-.05-.37-.117-.547a.983.983 0 00-.313-.437c-.143-.115-.338-.172-.585-.172a.984.984 0 00-.809.41c-.099.135-.176.3-.23.496-.055.195-.082.42-.082.676v.164c0 .2.027.39.081.566.058.175.14.328.247.461.109.133.24.237.394.313.156.075.334.113.531.113.256 0 .472-.052.649-.156.177-.104.332-.244.465-.418l.437.348c-.091.138-.207.269-.348.394-.14.125-.313.227-.519.305a2.012 2.012 0 01-.723.117zm3.211-3.64V6.3h-.722V2.073h.703l.02.664zm1.32-.688l-.003.672a1.827 1.827 0 00-.352-.031c-.167 0-.314.025-.441.078a.914.914 0 00-.325.218 1.052 1.052 0 00-.21.336c-.05.128-.082.268-.098.422l-.203.117c0-.255.024-.494.074-.718.052-.224.131-.422.238-.594.107-.175.242-.31.407-.406a1.142 1.142 0 01.914-.094zm3.13 3.129a.61.61 0 00-.07-.29c-.045-.09-.138-.173-.278-.245-.138-.076-.346-.141-.625-.196a4.95 4.95 0 01-.637-.176 1.931 1.931 0 01-.48-.246c-.13-.096-.23-.21-.301-.34a.948.948 0 01-.106-.457c0-.166.037-.324.11-.472.075-.149.18-.28.316-.395.138-.114.303-.204.496-.27.193-.064.408-.097.645-.097.338 0 .627.06.867.18s.423.28.55.48c.128.198.192.418.192.66h-.723a.62.62 0 00-.105-.34.778.778 0 00-.3-.277.97.97 0 00-.481-.11c-.2 0-.363.032-.488.095a.627.627 0 00-.27.23.58.58 0 00-.043.508c.029.06.078.116.148.168.07.05.17.096.297.14.128.045.29.089.489.133.346.078.631.172.855.282.224.109.39.243.5.402.11.159.164.351.164.578a1.126 1.126 0 01-.45.906 1.65 1.65 0 01-.515.258c-.198.06-.42.09-.668.09-.372 0-.687-.066-.945-.2a1.462 1.462 0 01-.586-.515c-.133-.21-.2-.434-.2-.668h.727c.01.198.068.356.172.473a.876.876 0 00.383.246c.151.047.3.07.45.07.197 0 .363-.026.495-.078a.69.69 0 00.309-.215.498.498 0 00.105-.312z",fill:"#B8B8B8"}),h=a.createElement("defs",null,a.createElement("linearGradient",{id:"cta-graph-traffic_svg__paint0_linear_435_1677",x1:19.094,y1:18.399,x2:19.094,y2:66.554,gradientUnits:"userSpaceOnUse"},a.createElement("stop",{stopColor:"#4F4F4F"}),a.createElement("stop",{offset:1,stopColor:"#4F4F4F",stopOpacity:0})));t.a=function SvgCtaGraphTraffic(e){return a.createElement("svg",r({viewBox:"0 0 116 59",fill:"none"},e),i,o,c,l,s,u,d,m,g,f,p,y,b,h)}},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r})),n.d(t,"e",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return l})),n.d(t,"M",(function(){return s})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return m})),n.d(t,"J",(function(){return g})),n.d(t,"I",(function(){return f})),n.d(t,"N",(function(){return p})),n.d(t,"f",(function(){return y})),n.d(t,"g",(function(){return b})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return v})),n.d(t,"l",(function(){return T})),n.d(t,"m",(function(){return O})),n.d(t,"n",(function(){return k})),n.d(t,"o",(function(){return E})),n.d(t,"q",(function(){return N})),n.d(t,"s",(function(){return j})),n.d(t,"r",(function(){return S})),n.d(t,"t",(function(){return _})),n.d(t,"w",(function(){return A})),n.d(t,"u",(function(){return I})),n.d(t,"v",(function(){return w})),n.d(t,"x",(function(){return C})),n.d(t,"y",(function(){return Z})),n.d(t,"A",(function(){return M})),n.d(t,"B",(function(){return D})),n.d(t,"C",(function(){return P})),n.d(t,"D",(function(){return L})),n.d(t,"k",(function(){return G})),n.d(t,"F",(function(){return R})),n.d(t,"z",(function(){return x})),n.d(t,"G",(function(){return B})),n.d(t,"E",(function(){return U})),n.d(t,"i",(function(){return W})),n.d(t,"p",(function(){return V})),n.d(t,"Q",(function(){return F})),n.d(t,"P",(function(){return z}));var a="core/user",r="connected_url_mismatch",i="__global",o="temporary_persist_permission_error",c="adblocker_active",l="googlesitekit_authenticate",s="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",m="googlesitekit_read_shared_module_data",g="googlesitekit_manage_module_sharing_options",f="googlesitekit_delegate_module_sharing_management",p="googlesitekit_update_plugins",y="kmAnalyticsAdSenseTopEarningContent",b="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",v="kmAnalyticsNewVisitors",T="kmAnalyticsPopularAuthors",O="kmAnalyticsPopularContent",k="kmAnalyticsPopularProducts",E="kmAnalyticsReturningVisitors",N="kmAnalyticsTopCities",j="kmAnalyticsTopCitiesDrivingLeads",S="kmAnalyticsTopCitiesDrivingAddToCart",_="kmAnalyticsTopCitiesDrivingPurchases",A="kmAnalyticsTopDeviceDrivingPurchases",I="kmAnalyticsTopConvertingTrafficSource",w="kmAnalyticsTopCountries",C="kmAnalyticsTopPagesDrivingLeads",Z="kmAnalyticsTopRecentTrendingPages",M="kmAnalyticsTopTrafficSource",D="kmAnalyticsTopTrafficSourceDrivingAddToCart",P="kmAnalyticsTopTrafficSourceDrivingLeads",L="kmAnalyticsTopTrafficSourceDrivingPurchases",G="kmAnalyticsPagesPerVisit",R="kmAnalyticsVisitLength",x="kmAnalyticsTopReturningVisitorPages",B="kmSearchConsolePopularKeywords",U="kmAnalyticsVisitsPerVisitor",W="kmAnalyticsMostEngagingPages",V="kmAnalyticsTopCategories",F=[y,b,h,v,T,O,k,E,V,N,j,S,_,A,I,w,Z,M,D,G,R,x,U,W,V],z=[].concat(F,[B])},70:function(e,t,n){"use strict";var a=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}var i=a.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return a.createElement("svg",r({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},72:function(e,t,n){"use strict";var a=n(15),r=n.n(a),i=n(265),o=n(1),c=n.n(o),l=n(0),s=n(144);function Portal(e){var t=e.children,n=e.slug,a=Object(l.useState)(document.createElement("div")),o=r()(a,1)[0];return Object(i.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(s.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},74:function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var a=n(33),r=n.n(a),i=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:i.a.sanitize(e,t)}};function c(e){var t,n="object"===r()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var a=n(1),r=n.n(a);function IconWrapper(t){var n=t.children,a=t.marginLeft,r=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:a,marginRight:r}},n)}IconWrapper.propTypes={children:r.a.node.isRequired,marginLeft:r.a.number,marginRight:r.a.number}}).call(this,n(4))},77:function(e,t,n){"use strict";(function(e){var a=n(21),r=n.n(a),i=n(25),o=n.n(i),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(0),m=Object(d.forwardRef)((function(t,n){var a=t.label,i=t.className,c=t.hasLeftSpacing,s=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",r()({ref:n},u,{className:l()("googlesitekit-badge",i,{"googlesitekit-badge--has-left-spacing":s})}),a)}));m.displayName="Badge",m.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=m}).call(this,n(4))},79:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var a=n(15),r=n.n(a),i=n(188),o=n(133),c={},l=void 0===e?null:e,s=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,a=e.initialWidth,u=void 0===a?0:a,d=e.initialHeight,m=void 0===d?0:d,g=Object(i.a)("undefined"==typeof document?[u,m]:s,t,n),f=r()(g,2),p=f[0],y=f[1],b=function(){return y(s)};return Object(o.a)(l,"resize",b),Object(o.a)(l,"orientationchange",b),p},d=function(e){return u(e)[0]}}).call(this,n(28))},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return a})),n.d(t,"a",(function(){return r})),n.d(t,"s",(function(){return i})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return l})),n.d(t,"g",(function(){return s})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return m})),n.d(t,"k",(function(){return g})),n.d(t,"m",(function(){return f})),n.d(t,"n",(function(){return p})),n.d(t,"h",(function(){return y})),n.d(t,"x",(function(){return b})),n.d(t,"w",(function(){return h})),n.d(t,"y",(function(){return v})),n.d(t,"u",(function(){return T})),n.d(t,"v",(function(){return O})),n.d(t,"f",(function(){return k})),n.d(t,"l",(function(){return E})),n.d(t,"e",(function(){return N})),n.d(t,"t",(function(){return j})),n.d(t,"c",(function(){return S})),n.d(t,"d",(function(){return _})),n.d(t,"b",(function(){return A}));var a="modules/analytics-4",r="account_create",i="property_create",o="webdatastream_create",c="analyticsSetup",l=10,s=1,u="https://www.googleapis.com/auth/tagmanager.readonly",d="enhanced-measurement-form",m="enhanced-measurement-enabled",g="enhanced-measurement-should-dismiss-activation-banner",f="analyticsAccountCreate",p="analyticsCustomDimensionsCreate",y="https://www.googleapis.com/auth/analytics.edit",b="dashboardAllTrafficWidgetDimensionName",h="dashboardAllTrafficWidgetDimensionColor",v="dashboardAllTrafficWidgetDimensionValue",T="dashboardAllTrafficWidgetActiveRowIndex",O="dashboardAllTrafficWidgetLoaded",k={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},E={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},N=[E.CONTACT,E.GENERATE_LEAD,E.SUBMIT_LEAD_FORM],j={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},S="audiencePermissionsSetup",_="audienceTileCustomDimensionCreate",A="audience-selection-panel-expirable-new-badge-"},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var a=n(106);function r(e){try{return new URL(e).pathname}catch(e){}return null}function i(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(a.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),r=e.replace(n.origin,"");if(r.length<t)return r;var i=r.length-Math.floor(t)+1;return"…"+r.substr(i)}},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return E})),n.d(t,"d",(function(){return N})),n.d(t,"e",(function(){return S})),n.d(t,"c",(function(){return _})),n.d(t,"b",(function(){return A}));var a=n(15),r=n.n(a),i=n(33),o=n.n(i),c=n(6),l=n.n(c),s=n(25),u=n.n(s),d=n(14),m=n(63),g=n.n(m),f=n(2);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var b=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=T(e,t),a=n.formatUnit,r=n.formatDecimal;try{return a()}catch(e){return r()}},h=function(e){var t=v(e),n=t.hours,a=t.minutes,r=t.seconds;return r=("0"+r).slice(-2),a=("0"+a).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(a,":").concat(r):"".concat(n,":").concat(a,":").concat(r)},v=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},T=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=v(e),a=n.hours,r=n.minutes,i=n.seconds;return{hours:a,minutes:r,seconds:i,formatUnit:function(){var n=t.unitDisplay,o=y(y({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?S(i,y(y({},o),{},{unit:"second"})):Object(f.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(f._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?S(i,y(y({},o),{},{unit:"second"})):"",r?S(r,y(y({},o),{},{unit:"minute"})):"",a?S(a,y(y({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(f.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(f.__)("%ds","google-site-kit"),i);if(0===e)return t;var n=Object(f.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(f.__)("%dm","google-site-kit"),r),o=Object(f.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(f.__)("%dh","google-site-kit"),a);return Object(f.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(f._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?t:"",r?n:"",a?o:"").trim()}}},O=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},k=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(f.sprintf)(// translators: %s: an abbreviated number in millions.
Object(f.__)("%sM","google-site-kit"),S(O(e),e%10==0?{}:t)):1e4<=e?Object(f.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(f.__)("%sK","google-site-kit"),S(O(e))):1e3<=e?Object(f.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(f.__)("%sK","google-site-kit"),S(O(e),e%10==0?{}:t)):S(e,{signDisplay:"never",maximumFractionDigits:1})};function E(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=y({},e)),t}function N(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=E(t),a=n.style,r=void 0===a?"metric":a;return"metric"===r?k(e):"duration"===r?b(e,n):"durationISO"===r?h(e):S(e,n)}var j=g()(console.warn),S=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,a=void 0===n?A():n,i=u()(t,["locale"]);try{return new Intl.NumberFormat(a,i).format(e)}catch(t){j("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(a),", ").concat(JSON.stringify(i)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},l=["signDisplay","compactDisplay"],s={},d=0,m=Object.entries(i);d<m.length;d++){var g=r()(m[d],2),f=g[0],p=g[1];c[f]&&p===c[f]||(l.includes(f)||(s[f]=p))}try{return new Intl.NumberFormat(a,s).format(e)}catch(t){return new Intl.NumberFormat(a).format(e)}},_=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,a=void 0===n?A():n,r=t.style,i=void 0===r?"long":r,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var l=new Intl.ListFormat(a,{style:i,type:c});return l.format(e)}
/* translators: used between list items, there is a space after the comma. */var s=Object(f.__)(", ","google-site-kit");return e.join(s)},A=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var a=n.match(/^(\w{2})?(_)?(\w{2})/);if(a&&a[0])return a[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},837:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var a=n(2),r={animation:{startup:!0},chart:{title:Object(a.__)("Unique visitors","google-site-kit")},curveType:"function",height:270,width:"100%",chartArea:{height:"80%",left:20,right:20},legend:{position:"top",textStyle:{color:"#616161",fontSize:12}},hAxis:{format:"MMM d",gridlines:{color:"#fff"},textStyle:{color:"#616161",fontSize:12}},vAxis:{textPosition:"none",viewWindow:{min:0},gridlines:{color:"#eee"}},series:{0:{color:"#6380b8",targetAxisIndex:0},1:{color:"#6380b8",targetAxisIndex:0,lineDashStyle:[3,3],lineWidth:1}},focusTarget:"category",crosshair:{color:"gray",opacity:.1,orientation:"vertical",trigger:"both"},tooltip:{isHtml:!0,trigger:"both"}}},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return r}));var a=n(149),r=n.n(a)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i);function ChangeArrow(t){var n=t.direction,a=t.invertColor,r=t.width,i=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":a}),width:r,height:i,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:r.a.string,invertColor:r.a.bool,width:r.a.number,height:r.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},86:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var a={EXTERNAL:"external",INTERNAL:"internal"}},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var a=n(12),r=n.n(a),i=function(e,t){var n=t.dateRangeLength;r()(Array.isArray(e),"report must be an array to partition."),r()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var a=-1*n;return{currentRange:e.slice(a),compareRange:e.slice(2*a,a)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return r.b})),n.d(t,"J",(function(){return r.c})),n.d(t,"F",(function(){return i.a})),n.d(t,"K",(function(){return i.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return p})),n.d(t,"j",(function(){return y})),n.d(t,"i",(function(){return b})),n.d(t,"d",(function(){return E})),n.d(t,"c",(function(){return N})),n.d(t,"e",(function(){return j})),n.d(t,"b",(function(){return S})),n.d(t,"a",(function(){return _})),n.d(t,"f",(function(){return A})),n.d(t,"n",(function(){return I})),n.d(t,"w",(function(){return w})),n.d(t,"p",(function(){return C})),n.d(t,"G",(function(){return Z})),n.d(t,"s",(function(){return M})),n.d(t,"v",(function(){return D})),n.d(t,"k",(function(){return P})),n.d(t,"o",(function(){return L.b})),n.d(t,"h",(function(){return L.a})),n.d(t,"t",(function(){return G.b})),n.d(t,"q",(function(){return G.a})),n.d(t,"A",(function(){return G.c})),n.d(t,"x",(function(){return R})),n.d(t,"u",(function(){return x})),n.d(t,"E",(function(){return W})),n.d(t,"D",(function(){return V.a})),n.d(t,"g",(function(){return F})),n.d(t,"L",(function(){return z})),n.d(t,"l",(function(){return H}));var a=n(14),r=n(36),i=n(75),o=n(33),c=n.n(o),l=n(96),s=n.n(l),u=function(e){return s()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(a){var r=t[a];r&&"object"===c()(r)&&!Array.isArray(r)&&(r=e(r)),n[a]=r})),n}(e)))};n(97);var d=n(83);function m(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function g(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function f(e){return e.replace(/\n/gi,"<br>")}function p(e){for(var t=e,n=0,a=[m,g,f];n<a.length;n++){t=(0,a[n])(t)}return t}var y=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},b=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(15),v=n.n(h),T=n(12),O=n.n(T),k=n(2),E="Invalid dateString parameter, it must be a string.",N='Invalid date range, it must be a string with the format "last-x-days".',j=60,S=60*j,_=24*S,A=7*_;function I(){var e=function(e){return Object(k.sprintf)(
/* translators: %s: number of days */
Object(k._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function w(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(a.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(a.isDate)(n)&&!isNaN(n)}function C(e){O()(Object(a.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function Z(e){O()(w(e),E);var t=e.split("-"),n=v()(t,3),a=n[0],r=n[1],i=n[2];return new Date(a,r-1,i)}function M(e,t){return C(P(e,t*_))}function D(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function P(e,t){O()(w(e)||Object(a.isDate)(e)&&!isNaN(e),E);var n=w(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var L=n(98),G=n(80);function R(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function x(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var B=n(27),U=n.n(B),W=function(e){return Array.isArray(e)?U()(e).sort():e},V=n(89);function F(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var a=(t-e)/e;return Number.isNaN(a)||!Number.isFinite(a)?null:a}var z=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},H=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(a.unescape)(t)}},90:function(e,t,n){"use strict";(function(e,a){n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return T})),n.d(t,"a",(function(){return TourTooltips}));var r=n(6),i=n.n(r),o=n(81),c=n(30),l=n(1),s=n.n(l),u=n(2),d=n(3),m=n(23),g=n(7),f=n(36),p=n(107),y=n(18);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}var h={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},v={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},T={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},O="feature_tooltip_view",k="feature_tooltip_advance",E="feature_tooltip_return",N="feature_tooltip_dismiss",j="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,r=t.tourID,l=t.gaEventCategory,s=t.callback,u="".concat(r,"-step"),S="".concat(r,"-run"),_=Object(d.useDispatch)(m.b).setValue,A=Object(d.useDispatch)(g.a).dismissTour,I=Object(d.useRegistry)(),w=Object(y.a)(),C=Object(d.useSelect)((function(e){return e(m.b).getValue(u)})),Z=Object(d.useSelect)((function(e){return e(m.b).getValue(S)&&!1===e(g.a).isTourDismissed(r)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(r)),_(S,!0)}));var M=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return a.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,a=e.lifecycle,r=e.size,i=e.status,o=e.type,s=t+1,u="function"==typeof l?l(w):l;o===c.b.TOOLTIP&&a===c.c.TOOLTIP?Object(f.b)(u,O,s):n===c.a.CLOSE&&a===c.c.COMPLETE?Object(f.b)(u,N,s):n===c.a.NEXT&&i===c.d.FINISHED&&o===c.b.TOUR_END&&r===s&&Object(f.b)(u,j,s),a===c.c.COMPLETE&&i!==c.d.FINISHED&&(n===c.a.PREV&&Object(f.b)(u,E,s),n===c.a.NEXT&&Object(f.b)(u,k,s))}(t);var n=t.action,a=t.index,i=t.status,o=t.step,d=t.type,m=n===c.a.CLOSE,g=!m&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),p=[c.d.FINISHED,c.d.SKIPPED].includes(i),y=m&&d===c.b.STEP_AFTER,b=p||y;if(c.b.STEP_BEFORE===d){var h,v,T=o.target;"string"==typeof o.target&&(T=e.document.querySelector(o.target)),null===(h=T)||void 0===h||null===(v=h.scrollIntoView)||void 0===v||v.call(h,{block:"center"})}g?function(e,t){_(u,e+(t===c.a.PREV?-1:1))}(a,n):b&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(r)),A(r)),s&&s(t,I)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:T,locale:v,run:Z,showProgress:!0,stepIndex:C,steps:M,styles:h,tooltipComponent:p.a})}TourTooltips.propTypes={steps:s.a.arrayOf(s.a.object).isRequired,tourID:s.a.string.isRequired,gaEventCategory:s.a.oneOfType([s.a.string,s.a.func]).isRequired,callback:s.a.func}}).call(this,n(28),n(4))},93:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var a=n(24),r=n(130);function i(t,n){var a=document.querySelector(t);if(!a)return 0;var r=a.getBoundingClientRect().top,i=o(n);return r+e.scrollY-i}function o(e){var t=c(e),n=document.querySelectorAll(".googlesitekit-navigation, .googlesitekit-entity-header");return t+=Array.from(n).reduce((function(e,t){return e+t.offsetHeight}),0)}function c(t){var n=0,i=document.querySelector(".googlesitekit-header");return n=!!i&&"sticky"===e.getComputedStyle(i).position?function(e){var t=document.querySelector(".googlesitekit-header");if(t){if(e===a.b)return t.offsetHeight;var n=t.getBoundingClientRect().bottom;return n<0?0:n}return 0}(t):function(e){var t=document.querySelector("#wpadminbar");return t&&e!==a.b?t.offsetHeight:0}(t),(n=Object(r.a)(n))<0?0:n}}).call(this,n(28))},95:function(e,t,n){"use strict";(function(e){var a=n(1),r=n.n(a),i=n(11),o=n.n(i),c=n(10),l=n(20);function CTA(t){var n=t.title,a=t.headerText,r=t.headerContent,i=t.description,s=t.ctaLink,u=t.ctaLabel,d=t.ctaLinkExternal,m=t.ctaType,g=t.error,f=t.onClick,p=t["aria-label"],y=t.children;return e.createElement("div",{className:o()("googlesitekit-cta",{"googlesitekit-cta--error":g})},(a||r)&&e.createElement("div",{className:"googlesitekit-cta__header"},a&&e.createElement("h2",{className:"googlesitekit-cta__header_text"},a),r),e.createElement("div",{className:"googlesitekit-cta__body"},n&&e.createElement("h3",{className:"googlesitekit-cta__title"},n),i&&"string"==typeof i&&e.createElement("p",{className:"googlesitekit-cta__description"},i),i&&"string"!=typeof i&&e.createElement("div",{className:"googlesitekit-cta__description"},i),u&&"button"===m&&e.createElement(c.Button,{"aria-label":p,href:s,onClick:f},u),u&&"link"===m&&e.createElement(l.a,{href:s,onClick:f,"aria-label":p,external:d,hideExternalIndicator:d,arrow:!0},u),y))}CTA.propTypes={title:r.a.string.isRequired,headerText:r.a.string,description:r.a.oneOfType([r.a.string,r.a.node]),ctaLink:r.a.string,ctaLinkExternal:r.a.bool,ctaLabel:r.a.string,ctaType:r.a.string,"aria-label":r.a.string,error:r.a.bool,onClick:r.a.func,children:r.a.node,headerContent:r.a.node},CTA.defaultProps={title:"",headerText:"",headerContent:"",description:"",ctaLink:"",ctaLabel:"",ctaType:"link",error:!1,onClick:function(){}},t.a=CTA}).call(this,n(4))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o}));var a=n(239),r=n(85),i=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var i=n.invertColor,o=void 0!==i&&i;return Object(a.a)(e.createElement(r.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var a=n(6),r=n.n(a),i=n(14),o=n(100),c=n(101);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,r=s(s({},u),t);r.referenceSiteURL&&(r.referenceSiteURL=r.referenceSiteURL.toString().replace(/\/+$/,""));var l=Object(o.a)(r,n),d=Object(c.a)(r,n,l,a),m={},g=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=JSON.stringify(t);m[a]||(m[a]=Object(i.once)(d)),m[a].apply(m,t)};return{enableTracking:function(){r.trackingEnabled=!0},disableTracking:function(){r.trackingEnabled=!1},initializeSnippet:l,isTrackingEnabled:function(){return!!r.trackingEnabled},trackEvent:d,trackEventOnce:g}}}).call(this,n(28))}},[[1289,1,0]]]);