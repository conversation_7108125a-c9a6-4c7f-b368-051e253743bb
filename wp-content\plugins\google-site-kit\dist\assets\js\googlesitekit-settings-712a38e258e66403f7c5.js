(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[28],[,,function(e,t){e.exports=googlesitekit.i18n},function(e,t){e.exports=googlesitekit.data},,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return r})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return l})),n.d(t,"M",(function(){return s})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return g})),n.d(t,"J",(function(){return f})),n.d(t,"I",(function(){return m})),n.d(t,"N",(function(){return p})),n.d(t,"f",(function(){return h})),n.d(t,"g",(function(){return b})),n.d(t,"h",(function(){return v})),n.d(t,"j",(function(){return E})),n.d(t,"l",(function(){return _})),n.d(t,"m",(function(){return O})),n.d(t,"n",(function(){return k})),n.d(t,"o",(function(){return y})),n.d(t,"q",(function(){return j})),n.d(t,"s",(function(){return S})),n.d(t,"r",(function(){return w})),n.d(t,"t",(function(){return C})),n.d(t,"w",(function(){return A})),n.d(t,"u",(function(){return N})),n.d(t,"v",(function(){return T})),n.d(t,"x",(function(){return x})),n.d(t,"y",(function(){return D})),n.d(t,"A",(function(){return R})),n.d(t,"B",(function(){return M})),n.d(t,"C",(function(){return I})),n.d(t,"D",(function(){return P})),n.d(t,"k",(function(){return L})),n.d(t,"F",(function(){return B})),n.d(t,"z",(function(){return z})),n.d(t,"G",(function(){return F})),n.d(t,"E",(function(){return V})),n.d(t,"i",(function(){return W})),n.d(t,"p",(function(){return H})),n.d(t,"Q",(function(){return U})),n.d(t,"P",(function(){return q}));var i="core/user",r="connected_url_mismatch",a="__global",o="temporary_persist_permission_error",c="adblocker_active",l="googlesitekit_authenticate",s="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",g="googlesitekit_read_shared_module_data",f="googlesitekit_manage_module_sharing_options",m="googlesitekit_delegate_module_sharing_management",p="googlesitekit_update_plugins",h="kmAnalyticsAdSenseTopEarningContent",b="kmAnalyticsEngagedTrafficSource",v="kmAnalyticsLeastEngagingPages",E="kmAnalyticsNewVisitors",_="kmAnalyticsPopularAuthors",O="kmAnalyticsPopularContent",k="kmAnalyticsPopularProducts",y="kmAnalyticsReturningVisitors",j="kmAnalyticsTopCities",S="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",C="kmAnalyticsTopCitiesDrivingPurchases",A="kmAnalyticsTopDeviceDrivingPurchases",N="kmAnalyticsTopConvertingTrafficSource",T="kmAnalyticsTopCountries",x="kmAnalyticsTopPagesDrivingLeads",D="kmAnalyticsTopRecentTrendingPages",R="kmAnalyticsTopTrafficSource",M="kmAnalyticsTopTrafficSourceDrivingAddToCart",I="kmAnalyticsTopTrafficSourceDrivingLeads",P="kmAnalyticsTopTrafficSourceDrivingPurchases",L="kmAnalyticsPagesPerVisit",B="kmAnalyticsVisitLength",z="kmAnalyticsTopReturningVisitorPages",F="kmSearchConsolePopularKeywords",V="kmAnalyticsVisitsPerVisitor",W="kmAnalyticsMostEngagingPages",H="kmAnalyticsTopCategories",U=[h,b,v,E,_,O,k,y,H,j,S,w,C,A,N,T,D,R,M,L,B,z,V,W,H],q=[].concat(U,[F])},function(e,t,n){"use strict";n.d(t,"r",(function(){return i})),n.d(t,"a",(function(){return r})),n.d(t,"s",(function(){return a})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return l})),n.d(t,"g",(function(){return s})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"m",(function(){return m})),n.d(t,"n",(function(){return p})),n.d(t,"h",(function(){return h})),n.d(t,"x",(function(){return b})),n.d(t,"w",(function(){return v})),n.d(t,"y",(function(){return E})),n.d(t,"u",(function(){return _})),n.d(t,"v",(function(){return O})),n.d(t,"f",(function(){return k})),n.d(t,"l",(function(){return y})),n.d(t,"e",(function(){return j})),n.d(t,"t",(function(){return S})),n.d(t,"c",(function(){return w})),n.d(t,"d",(function(){return C})),n.d(t,"b",(function(){return A}));var i="modules/analytics-4",r="account_create",a="property_create",o="webdatastream_create",c="analyticsSetup",l=10,s=1,u="https://www.googleapis.com/auth/tagmanager.readonly",d="enhanced-measurement-form",g="enhanced-measurement-enabled",f="enhanced-measurement-should-dismiss-activation-banner",m="analyticsAccountCreate",p="analyticsCustomDimensionsCreate",h="https://www.googleapis.com/auth/analytics.edit",b="dashboardAllTrafficWidgetDimensionName",v="dashboardAllTrafficWidgetDimensionColor",E="dashboardAllTrafficWidgetDimensionValue",_="dashboardAllTrafficWidgetActiveRowIndex",O="dashboardAllTrafficWidgetLoaded",k={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},y={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},j=[y.CONTACT,y.GENERATE_LEAD,y.SUBMIT_LEAD_FORM],S={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},w="audiencePermissionsSetup",C="audienceTileCustomDimensionCreate",A="audience-selection-panel-expirable-new-badge-"},function(e,t,n){"use strict";n.d(t,"I",(function(){return r.b})),n.d(t,"J",(function(){return r.c})),n.d(t,"F",(function(){return a.a})),n.d(t,"K",(function(){return a.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return p})),n.d(t,"j",(function(){return h})),n.d(t,"i",(function(){return b})),n.d(t,"d",(function(){return y})),n.d(t,"c",(function(){return j})),n.d(t,"e",(function(){return S})),n.d(t,"b",(function(){return w})),n.d(t,"a",(function(){return C})),n.d(t,"f",(function(){return A})),n.d(t,"n",(function(){return N})),n.d(t,"w",(function(){return T})),n.d(t,"p",(function(){return x})),n.d(t,"G",(function(){return D})),n.d(t,"s",(function(){return R})),n.d(t,"v",(function(){return M})),n.d(t,"k",(function(){return I})),n.d(t,"o",(function(){return P.b})),n.d(t,"h",(function(){return P.a})),n.d(t,"t",(function(){return L.b})),n.d(t,"q",(function(){return L.a})),n.d(t,"A",(function(){return L.c})),n.d(t,"x",(function(){return B})),n.d(t,"u",(function(){return z})),n.d(t,"E",(function(){return W})),n.d(t,"D",(function(){return H.a})),n.d(t,"g",(function(){return U})),n.d(t,"L",(function(){return q})),n.d(t,"l",(function(){return G}));var i=n(14),r=n(36),a=n(75),o=n(33),c=n.n(o),l=n(96),s=n.n(l),u=function(e){return s()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(i){var r=t[i];r&&"object"===c()(r)&&!Array.isArray(r)&&(r=e(r)),n[i]=r})),n}(e)))};n(97);var d=n(83);function g(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function f(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function m(e){return e.replace(/\n/gi,"<br>")}function p(e){for(var t=e,n=0,i=[g,f,m];n<i.length;n++){t=(0,i[n])(t)}return t}var h=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},b=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},v=n(15),E=n.n(v),_=n(12),O=n.n(_),k=n(2),y="Invalid dateString parameter, it must be a string.",j='Invalid date range, it must be a string with the format "last-x-days".',S=60,w=60*S,C=24*w,A=7*C;function N(){var e=function(e){return Object(k.sprintf)(
/* translators: %s: number of days */
Object(k._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function T(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(i.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(i.isDate)(n)&&!isNaN(n)}function x(e){O()(Object(i.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function D(e){O()(T(e),y);var t=e.split("-"),n=E()(t,3),i=n[0],r=n[1],a=n[2];return new Date(i,r-1,a)}function R(e,t){return x(I(e,t*C))}function M(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function I(e,t){O()(T(e)||Object(i.isDate)(e)&&!isNaN(e),y);var n=T(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var P=n(98),L=n(80);function B(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function z(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var F=n(27),V=n.n(F),W=function(e){return Array.isArray(e)?V()(e).sort():e},H=n(89);function U(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var i=(t-e)/e;return Number.isNaN(i)||!Number.isFinite(i)?null:i}var q=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},G=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(i.unescape)(t)}},function(e,t){e.exports=googlesitekit.components},,,function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var i="core/site",r="primary",a="secondary"},,,,function(e,t,n){"use strict";var i=n(254);n.d(t,"i",(function(){return i.a}));var r=n(319);n.d(t,"f",(function(){return r.a}));var a=n(320);n.d(t,"h",(function(){return a.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var l=n(91),s=n.n(l);n.d(t,"b",(function(){return s.a})),n.d(t,"c",(function(){return l.DialogContent})),n.d(t,"d",(function(){return l.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},function(e,t,n){"use strict";var i=n(0),r=n(61);t.a=function(){return Object(i.useContext)(r.b)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return r}));var i="core/modules",r="insufficient_module_dependencies"},function(e,t,n){"use strict";(function(e){var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(146),g=n(0),f=n(2),m=n(126),p=n(127),h=n(128),b=n(70),v=n(76),E=Object(g.forwardRef)((function(t,n){var i,a=t["aria-label"],c=t.secondary,s=void 0!==c&&c,u=t.arrow,g=void 0!==u&&u,E=t.back,_=void 0!==E&&E,O=t.caps,k=void 0!==O&&O,y=t.children,j=t.className,S=void 0===j?"":j,w=t.danger,C=void 0!==w&&w,A=t.disabled,N=void 0!==A&&A,T=t.external,x=void 0!==T&&T,D=t.hideExternalIndicator,R=void 0!==D&&D,M=t.href,I=void 0===M?"":M,P=t.inverse,L=void 0!==P&&P,B=t.noFlex,z=void 0!==B&&B,F=t.onClick,V=t.small,W=void 0!==V&&V,H=t.standalone,U=void 0!==H&&H,q=t.linkButton,G=void 0!==q&&q,K=t.to,Y=t.leadingIcon,X=t.trailingIcon,$=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),Z=I||K||!F?K?"ROUTER_LINK":x?"EXTERNAL_LINK":"LINK":N?"BUTTON_DISABLED":"BUTTON",Q="BUTTON"===Z||"BUTTON_DISABLED"===Z?"button":"ROUTER_LINK"===Z?d.b:"a",J=("EXTERNAL_LINK"===Z&&(i=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===Z&&(i=Object(f._x)("(disabled)","screen reader text","google-site-kit")),i?a?"".concat(a," ").concat(i):"string"==typeof y?"".concat(y," ").concat(i):void 0:a),ee=Y,te=X;return _&&(ee=e.createElement(h.a,{width:14,height:14})),x&&!R&&(te=e.createElement(b.a,{width:14,height:14})),g&&!L&&(te=e.createElement(m.a,{width:14,height:14})),g&&L&&(te=e.createElement(p.a,{width:14,height:14})),e.createElement(Q,r()({"aria-label":J,className:l()("googlesitekit-cta-link",S,{"googlesitekit-cta-link--secondary":s,"googlesitekit-cta-link--inverse":L,"googlesitekit-cta-link--small":W,"googlesitekit-cta-link--caps":k,"googlesitekit-cta-link--danger":C,"googlesitekit-cta-link--disabled":N,"googlesitekit-cta-link--standalone":U,"googlesitekit-cta-link--link-button":G,"googlesitekit-cta-link--no-flex":!!z}),disabled:N,href:"LINK"!==Z&&"EXTERNAL_LINK"!==Z||N?void 0:I,onClick:F,rel:"EXTERNAL_LINK"===Z?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===Z?"_blank":void 0,to:K},$),!!ee&&e.createElement(v.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},y),!!te&&e.createElement(v.a,{marginLeft:5},te))}));E.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=E}).call(this,n(4))},,function(e,t,n){"use strict";n.d(t,"n",(function(){return i})),n.d(t,"l",(function(){return r})),n.d(t,"o",(function(){return a})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return l})),n.d(t,"s",(function(){return s})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"u",(function(){return m})),n.d(t,"v",(function(){return p})),n.d(t,"q",(function(){return h})),n.d(t,"p",(function(){return b})),n.d(t,"b",(function(){return v})),n.d(t,"e",(function(){return E})),n.d(t,"a",(function(){return _})),n.d(t,"d",(function(){return O})),n.d(t,"c",(function(){return k})),n.d(t,"f",(function(){return y})),n.d(t,"g",(function(){return j}));var i="mainDashboard",r="entityDashboard",a="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",l="activation",s="splash",u="adminBar",d="adminBarViewOnly",g="settings",f="adBlockingRecovery",m="wpDashboard",p="wpDashboardViewOnly",h="moduleSetup",b="metricSelection",v="key-metrics",E="traffic",_="content",O="speed",k="monetization",y=[i,r,a,o,c,s,g,h,b],j=[a,o,d,p]},function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return r}));var i="core/ui",r="activeContextID"},function(e,t,n){"use strict";n.d(t,"d",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return l}));var i=n(79),r="xlarge",a="desktop",o="tablet",c="small";function l(){var e=Object(i.a)();return e>1280?r:e>960?a:e>600?o:c}},,function(e,t,n){"use strict";n.d(t,"l",(function(){return r})),n.d(t,"k",(function(){return a})),n.d(t,"j",(function(){return o})),n.d(t,"i",(function(){return c})),n.d(t,"a",(function(){return l})),n.d(t,"o",(function(){return s})),n.d(t,"n",(function(){return u})),n.d(t,"m",(function(){return d})),n.d(t,"c",(function(){return g})),n.d(t,"g",(function(){return f})),n.d(t,"h",(function(){return m})),n.d(t,"d",(function(){return p})),n.d(t,"e",(function(){return h})),n.d(t,"f",(function(){return b})),n.d(t,"b",(function(){return v}));var i=n(2),r="key-metrics-setup-cta-widget",a="googlesitekit-key-metrics-selection-panel-opened",o="key-metrics-selection-form",c="key-metrics-selected",l="key-metrics-effective-selection",s="key-metrics-unstaged-selection",u=2,d=8,g={SLUG:"current-selection",LABEL:Object(i.__)("Current selection","google-site-kit")},f={SLUG:"suggested",LABEL:Object(i.__)("Suggested","google-site-kit")},m={SLUG:"visitors",LABEL:Object(i.__)("Visitors","google-site-kit")},p={SLUG:"driving-traffic",LABEL:Object(i.__)("Driving traffic","google-site-kit")},h={SLUG:"generating-leads",LABEL:Object(i.__)("Generating leads","google-site-kit")},b={SLUG:"selling-products",LABEL:Object(i.__)("Selling products","google-site-kit")},v={SLUG:"content-performance",LABEL:Object(i.__)("Content performance","google-site-kit")}},,,function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i="core/forms"},,,function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i="core/location"},,function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n(22),r=n(18);function a(){var e=Object(r.a)();return i.g.includes(e)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return l})),n.d(t,"c",(function(){return s})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return d}));n(14);var i=n(2),r="missing_required_scopes",a="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===r}function l(e){var t;return[a,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function s(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||l(e)||c(e)||s(e))}function d(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(i.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(i.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return _})),n.d(t,"b",(function(){return v})),n.d(t,"c",(function(){return E}));var i=n(99),r=e._googlesitekitTrackingData||{},a=r.activeModules,o=void 0===a?[]:a,c=r.isSiteKitScreen,l=r.trackingEnabled,s=r.trackingID,u=r.referenceSiteURL,d=r.userIDHash,g=r.isAuthenticated,f={activeModules:o,trackingEnabled:l,trackingID:s,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:r.userRoles,isAuthenticated:g,pluginVersion:"1.151.0"},m=Object(i.a)(f),p=m.enableTracking,h=m.disableTracking,b=(m.isTrackingEnabled,m.initializeSnippet),v=m.trackEvent,E=m.trackEventOnce;function _(e){e?p():h()}c&&l&&b()}).call(this,n(28))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return v})),n.d(t,"f",(function(){return E})),n.d(t,"c",(function(){return _})),n.d(t,"e",(function(){return O})),n.d(t,"b",(function(){return k}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=(n(27),n(9));function l(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var i=0,r=function(){};return{s:r,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}var u,d="googlesitekit_",g="".concat(d).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),f=["sessionStorage","localStorage"],m=[].concat(f),p=function(){var t=o()(r.a.mark((function t(n){var i,a;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(i=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,a="__storage_test__",i.setItem(a,a),i.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==i.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function h(){return b.apply(this,arguments)}function b(){return(b=o()(r.a.mark((function t(){var n,i,a;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=l(m),t.prev=3,n.s();case 5:if((i=n.n()).done){t.next=15;break}if(a=i.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,p(a);case 11:if(!t.sent){t.next=13;break}u=e[a];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var v=function(){var e=o()(r.a.mark((function e(t){var n,i,a,o,c,l,s;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h();case 2:if(!(n=e.sent)){e.next=10;break}if(!(i=n.getItem("".concat(g).concat(t)))){e.next=10;break}if(a=JSON.parse(i),o=a.timestamp,c=a.ttl,l=a.value,s=a.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:l,isError:s});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),E=function(){var t=o()(r.a.mark((function t(n,i){var a,o,l,s,u,d,f,m,p=arguments;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=p.length>2&&void 0!==p[2]?p[2]:{},o=a.ttl,l=void 0===o?c.b:o,s=a.timestamp,u=void 0===s?Math.round(Date.now()/1e3):s,d=a.isError,f=void 0!==d&&d,t.next=3,h();case 3:if(!(m=t.sent)){t.next=14;break}return t.prev=5,m.setItem("".concat(g).concat(n),JSON.stringify({timestamp:u,ttl:l,value:i,isError:f})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),_=function(){var t=o()(r.a.mark((function t(n){var i,a;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,h();case 2:if(!(i=t.sent)){t.next=14;break}return t.prev=4,a=n.startsWith(d)?n:"".concat(g).concat(n),i.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),O=function(){var t=o()(r.a.mark((function t(){var n,i,a,o;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,h();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,i=[],a=0;a<n.length;a++)0===(o=n.key(a)).indexOf(d)&&i.push(o);return t.abrupt("return",i);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),k=function(){var e=o()(r.a.mark((function e(){var t,n,i,a;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h();case 2:if(!e.sent){e.next=25;break}return e.next=6,O();case 6:t=e.sent,n=l(t),e.prev=8,n.s();case 10:if((i=n.n()).done){e.next=16;break}return a=i.value,e.next=14,_(a);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},,function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return r}));var i="_googlesitekitDataLayer",r="data-googlesitekit-gtag"},function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"i",(function(){return r})),n.d(t,"h",(function(){return a})),n.d(t,"f",(function(){return o})),n.d(t,"g",(function(){return c})),n.d(t,"d",(function(){return l})),n.d(t,"e",(function(){return s})),n.d(t,"k",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"b",(function(){return g})),n.d(t,"c",(function(){return f}));var i="audience-segmentation-add-group-notice",r="googlesitekit-audience-selection-panel-opened",a="audience-selection-form",o="audience-selected",c="audience-selection-changed",l="audience-segmentation-creation-notice",s="audience-segmentation-creation-success-notice",u=1,d=3,g="audience-creation-edit-scope-notice",f="audience-creation-form"},function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var i=n(22),r="core/notifications",a={ERRORS:"notification-area-errors",BANNERS_ABOVE_NAV:"notification-area-banners-above-nav",BANNERS_BELOW_NAV:"notification-area-banners-below-nav"},o={DEFAULT:"default",SETUP_CTAS:"setup-ctas"},c=[i.s,i.n,i.l,i.o,i.m]},,,function(e,t,n){"use strict";(function(e){var i=n(6),r=n.n(i),a=n(1),o=n.n(a),c=n(11),l=n.n(c),s=n(24);function PreviewBlock(t){var n,i,a=t.className,o=t.width,c=t.height,u=t.shape,d=t.padding,g=t.smallWidth,f=t.smallHeight,m=t.tabletWidth,p=t.tabletHeight,h=t.desktopWidth,b=t.desktopHeight,v=Object(s.e)(),E={width:(n={},r()(n,s.b,g),r()(n,s.c,m),r()(n,s.a,h),r()(n,s.d,h),n),height:(i={},r()(i,s.b,f),r()(i,s.c,p),r()(i,s.a,b),r()(i,s.d,h),i)};return e.createElement("div",{className:l()("googlesitekit-preview-block",a,{"googlesitekit-preview-block--padding":d}),style:{width:E.width[v]||o,height:E.height[v]||c}},e.createElement("div",{className:l()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(4))},,,function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a}));var i={BOXES:"boxes",COMPOSITE:"composite"},r={QUARTER:"quarter",HALF:"half",FULL:"full"},a="core/widgets"},,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var i=n(3),r=n(19),a=n(82);function o(t){var n=t.moduleName,o=t.FallbackComponent,c=t.IncompleteComponent;return function(t){function WhenActiveComponent(a){var l=Object(i.useSelect)((function(e){return e(r.a).getModule(n)}),[n]);if(!l)return null;var s=o||a.WidgetNull||null;if(!1===l.active)return s&&e.createElement(s,a);if(!1===l.connected){var u=c||s;return u&&e.createElement(u,a)}return e.createElement(t,a)}return WhenActiveComponent.displayName="When".concat(Object(a.c)(n),"Active"),(t.displayName||t.name)&&(WhenActiveComponent.displayName+="(".concat(t.displayName||t.name,")")),WhenActiveComponent}}}).call(this,n(4))},,function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return c}));var i=n(22),r=n(18),a=i.n,o=i.l;function c(){var e=Object(r.a)();return e===i.n||e===i.o?a:e===i.l||e===i.m?o:null}},,function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,i=t.reconnectURL,r=t.noPrefix;if(!n)return null;var l=n;void 0!==r&&r||(l=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),i&&Object(a.a)(i)&&(l=l+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),i));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(l,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:r.a.string.isRequired,reconnectURL:r.a.string,noPrefix:r.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},,,function(e,t,n){"use strict";(function(e){var i,r;n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=new Set((null===(i=e)||void 0===i||null===(r=i._googlesitekitBaseData)||void 0===r?void 0:r.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,n(28))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=i.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return i.createElement("svg",r({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),a,o)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n(39);function r(e){return function(){e[i.a]=e[i.a]||[],e[i.a].push(arguments)}}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n(0),r=Object(i.createContext)(""),a=(r.Consumer,r.Provider);t.b=r},,,,function(e,t,n){"use strict";n.d(t,"c",(function(){return p})),n.d(t,"a",(function(){return h})),n.d(t,"b",(function(){return b})),n.d(t,"d",(function(){return E}));var i=n(6),r=n.n(i),a=n(0);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var c=a.createElement("path",{d:"M10 15.27L16.18 19l-1.64-7.03L20 7.24l-7.19-.61L10 0 7.19 6.63 0 7.24l5.46 4.73L3.82 19 10 15.27z"});var l=function SvgInfoIcon(e){return a.createElement("svg",o({viewBox:"0 0 20 20",fill:"currentColor"},e),c)};function s(){return(s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var u=a.createElement("path",{d:"M0 4h2v7H0zm0-4h2v2H0z",fill:"currentColor",fillRule:"evenodd"});var d,g=function SvgSuggestionIcon(e){return a.createElement("svg",s({viewBox:"0 0 2 11"},e),u)},f=n(186),m=n(74),p="warning",h="info",b="suggestion",v=(d={},r()(d,h,l),r()(d,p,f.a),r()(d,b,g),d),E=function(e){return v[e]||m.a}},function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return r}));var i="modules/search-console",r=1},,,,function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return i.createElement("svg",r({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},function(e,t,n){"use strict";n.r(t),n.d(t,"CONTEXT_MAIN_DASHBOARD_KEY_METRICS",(function(){return i})),n.d(t,"CONTEXT_MAIN_DASHBOARD_TRAFFIC",(function(){return r})),n.d(t,"CONTEXT_MAIN_DASHBOARD_CONTENT",(function(){return a})),n.d(t,"CONTEXT_MAIN_DASHBOARD_SPEED",(function(){return o})),n.d(t,"CONTEXT_MAIN_DASHBOARD_MONETIZATION",(function(){return c})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_TRAFFIC",(function(){return l})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_CONTENT",(function(){return s})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_SPEED",(function(){return u})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_MONETIZATION",(function(){return d}));var i="mainDashboardKeyMetrics",r="mainDashboardTraffic",a="mainDashboardContent",o="mainDashboardSpeed",c="mainDashboardMonetization",l="entityDashboardTraffic",s="entityDashboardContent",u="entityDashboardSpeed",d="entityDashboardMonetization";t.default={CONTEXT_MAIN_DASHBOARD_KEY_METRICS:i,CONTEXT_MAIN_DASHBOARD_TRAFFIC:r,CONTEXT_MAIN_DASHBOARD_CONTENT:a,CONTEXT_MAIN_DASHBOARD_SPEED:o,CONTEXT_MAIN_DASHBOARD_MONETIZATION:c,CONTEXT_ENTITY_DASHBOARD_TRAFFIC:l,CONTEXT_ENTITY_DASHBOARD_CONTENT:s,CONTEXT_ENTITY_DASHBOARD_SPEED:u,CONTEXT_ENTITY_DASHBOARD_MONETIZATION:d}},function(e,t,n){"use strict";var i=n(15),r=n.n(i),a=n(265),o=n(1),c=n.n(o),l=n(0),s=n(144);function Portal(e){var t=e.children,n=e.slug,i=Object(l.useState)(document.createElement("div")),o=r()(i,1)[0];return Object(a.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(s.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n(0),r=n(18),a=n(9);function o(e,t){var n=Object(r.a)(),o=null!=t?t:"".concat(n,"_").concat(e);return{view:Object(i.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"view_notification"].concat(t))}),[o]),confirm:Object(i.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"confirm_notification"].concat(t))}),[o]),dismiss:Object(i.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"dismiss_notification"].concat(t))}),[o]),clickLearnMore:Object(i.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"click_learn_more_link"].concat(t))}),[o])}}},function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))},function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var i=n(33),r=n.n(i),a=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,n="object"===r()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var i=n(1),r=n.n(i);function IconWrapper(t){var n=t.children,i=t.marginLeft,r=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:i,marginRight:r}},n)}IconWrapper.propTypes={children:r.a.node.isRequired,marginLeft:r.a.number,marginRight:r.a.number}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(0),g=Object(d.forwardRef)((function(t,n){var i=t.label,a=t.className,c=t.hasLeftSpacing,s=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",r()({ref:n},u,{className:l()("googlesitekit-badge",a,{"googlesitekit-badge--has-left-spacing":s})}),i)}));g.displayName="Badge",g.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=g}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var i=n(15),r=n.n(i),a=n(188),o=n(133),c={},l=void 0===e?null:e,s=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,i=e.initialWidth,u=void 0===i?0:i,d=e.initialHeight,g=void 0===d?0:d,f=Object(a.a)("undefined"==typeof document?[u,g]:s,t,n),m=r()(f,2),p=m[0],h=m[1],b=function(){return h(s)};return Object(o.a)(l,"resize",b),Object(o.a)(l,"orientationchange",b),p},d=function(e){return u(e)[0]}}).call(this,n(28))},function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var i=n(106);function r(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(i.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),r=e.replace(n.origin,"");if(r.length<t)return r;var a=r.length-Math.floor(t)+1;return"…"+r.substr(a)}},,function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return r})),n.d(t,"c",(function(){return a}));var i=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},r=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function a(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return y})),n.d(t,"d",(function(){return j})),n.d(t,"e",(function(){return w})),n.d(t,"c",(function(){return C})),n.d(t,"b",(function(){return A}));var i=n(15),r=n.n(i),a=n(33),o=n.n(a),c=n(6),l=n.n(c),s=n(25),u=n.n(s),d=n(14),g=n(63),f=n.n(g),m=n(2);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var b=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=_(e,t),i=n.formatUnit,r=n.formatDecimal;try{return i()}catch(e){return r()}},v=function(e){var t=E(e),n=t.hours,i=t.minutes,r=t.seconds;return r=("0"+r).slice(-2),i=("0"+i).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(i,":").concat(r):"".concat(n,":").concat(i,":").concat(r)},E=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},_=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=E(e),i=n.hours,r=n.minutes,a=n.seconds;return{hours:i,minutes:r,seconds:a,formatUnit:function(){var n=t.unitDisplay,o=h(h({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(a,h(h({},o),{},{unit:"second"})):Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?w(a,h(h({},o),{},{unit:"second"})):"",r?w(r,h(h({},o),{},{unit:"minute"})):"",i?w(i,h(h({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(m.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(m.__)("%ds","google-site-kit"),a);if(0===e)return t;var n=Object(m.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(m.__)("%dm","google-site-kit"),r),o=Object(m.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(m.__)("%dh","google-site-kit"),i);return Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",r?n:"",i?o:"").trim()}}},O=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},k=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in millions.
Object(m.__)("%sM","google-site-kit"),w(O(e),e%10==0?{}:t)):1e4<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),w(O(e))):1e3<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),w(O(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function y(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=h({},e)),t}function j(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=y(t),i=n.style,r=void 0===i?"metric":i;return"metric"===r?k(e):"duration"===r?b(e,n):"durationISO"===r?v(e):w(e,n)}var S=f()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,i=void 0===n?A():n,a=u()(t,["locale"]);try{return new Intl.NumberFormat(i,a).format(e)}catch(t){S("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(i),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},l=["signDisplay","compactDisplay"],s={},d=0,g=Object.entries(a);d<g.length;d++){var f=r()(g[d],2),m=f[0],p=f[1];c[m]&&p===c[m]||(l.includes(m)||(s[m]=p))}try{return new Intl.NumberFormat(i,s).format(e)}catch(t){return new Intl.NumberFormat(i).format(e)}},C=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,i=void 0===n?A():n,r=t.style,a=void 0===r?"long":r,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var l=new Intl.ListFormat(i,{style:a,type:c});return l.format(e)}
/* translators: used between list items, there is a space after the comma. */var s=Object(m.__)(", ","google-site-kit");return e.join(s)},A=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var i=n.match(/^(\w{2})?(_)?(\w{2})/);if(i&&i[0])return i[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return r}));var i=n(149),r=n.n(i)()(e)}).call(this,n(28))},function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(11),o=n.n(a);function ChangeArrow(t){var n=t.direction,i=t.invertColor,r=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":i}),width:r,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:r.a.string,invertColor:r.a.bool,width:r.a.number,height:r.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i={EXTERNAL:"external",INTERNAL:"internal"}},function(e,t,n){"use strict";n.d(t,"i",(function(){return l})),n.d(t,"j",(function(){return s})),n.d(t,"g",(function(){return u})),n.d(t,"h",(function(){return d})),n.d(t,"e",(function(){return g})),n.d(t,"c",(function(){return f})),n.d(t,"a",(function(){return m})),n.d(t,"b",(function(){return p})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return b})),n.d(t,"m",(function(){return v})),n.d(t,"k",(function(){return E})),n.d(t,"l",(function(){return _}));var i,r=n(6),a=n.n(r),o=n(2),c=n(8),l="purpose",s="postFrequency",u="goals",d=[l,s,u],g=(i={},a()(i,l,1),a()(i,s,1),a()(i,u,3),i),f="googlesitekit-user-input-currently-editing",m="user_input_question_number",p="user_input_question_snapshot",h="user-input-legacy-site-purpose-dismissed-item",b={publish_blog:[c.l.CONTACT,c.l.GENERATE_LEAD,c.l.SUBMIT_LEAD_FORM],publish_news:[c.l.CONTACT,c.l.GENERATE_LEAD,c.l.SUBMIT_LEAD_FORM],monetize_content:[],sell_products_or_service:[c.l.PURCHASE,c.l.ADD_TO_CART],sell_products:[c.l.PURCHASE,c.l.ADD_TO_CART],provide_services:[c.l.CONTACT,c.l.GENERATE_LEAD,c.l.SUBMIT_LEAD_FORM],share_portfolio:[c.l.CONTACT,c.l.GENERATE_LEAD,c.l.SUBMIT_LEAD_FORM],other:[]};function v(){var e=Object(o.__)("Based on your answer, Site Kit will suggest the metrics you see on your dashboard to help you track how close you’re getting to your specific goals","google-site-kit");return[{title:Object(o.__)("What is the main purpose of this site?","google-site-kit"),description:e},{title:Object(o.__)("How often do you create new content for this site?","google-site-kit"),description:e},{title:Object(o.__)("What are your top 3 goals for this site?","google-site-kit"),description:e}]}function E(){return{USER_INPUT_ANSWERS_PURPOSE:{sell_products_or_service:Object(o.__)("Sell products or services","google-site-kit"),sell_products:Object(o.__)("Sell products","google-site-kit"),provide_services:Object(o.__)("Provide services","google-site-kit"),monetize_content:Object(o.__)("Monetize content","google-site-kit"),publish_blog:Object(o.__)("Publish a blog","google-site-kit"),publish_news:Object(o.__)("Publish news content","google-site-kit"),share_portfolio:Object(o.__)("Portfolio or business card","google-site-kit"),other:Object(o.__)("Other","google-site-kit")},USER_INPUT_ANSWERS_POST_FREQUENCY:{never:Object(o.__)("Never","google-site-kit"),daily:Object(o.__)("Daily","google-site-kit"),weekly:Object(o.__)("Weekly","google-site-kit"),monthly:Object(o.__)("Monthly","google-site-kit"),other:Object(o.__)("Other","google-site-kit")},USER_INPUT_ANSWERS_GOALS:{retaining_visitors:Object(o.__)("Retain visitors, turn them into loyal readers or customers","google-site-kit"),improving_performance:Object(o.__)("Improve speed and performance","google-site-kit"),finding_new_topics:Object(o.__)("Find new topics to write about that connect with my audience","google-site-kit"),growing_audience:Object(o.__)("Grow my audience","google-site-kit"),expanding_business:Object(o.__)("Expand my business into new cities, states or markets","google-site-kit"),generating_revenue:Object(o.__)("Generate more revenue","google-site-kit"),generating_leads:Object(o.__)("Generate leads","google-site-kit"),help_better_rank:Object(o.__)("Help my content rank in a better position in Google search results","google-site-kit"),understanding_content_performance:Object(o.__)("Understand which content is performing best","google-site-kit"),encourage_to_post:Object(o.__)("Encouragement to post more frequently","google-site-kit"),other:Object(o.__)("Other","google-site-kit")}}}function _(){return{USER_INPUT_ANSWERS_PURPOSE:{sell_products_or_service:Object(o.__)("E.g. selling products like devices, apparel, equipment, etc. or offering services like courses, consulting, tutoring, etc.","google-site-kit"),sell_products:Object(o.__)("E.g. selling devices, apparel, equipment, etc.","google-site-kit"),provide_services:Object(o.__)("E.g. offering courses, consulting, tutoring, etc.","google-site-kit"),monetize_content:Object(o.__)("Using display ads, affiliate links, sponsored content, etc.","google-site-kit"),publish_blog:Object(o.__)("Writing on a topic you’re passionate about, no focus on monetizing content","google-site-kit"),publish_news:Object(o.__)("E.g. local news, investigative pieces, interviews, etc.","google-site-kit"),share_portfolio:Object(o.__)("My website represents me or my company","google-site-kit"),other:void 0}}}},function(e,t,n){"use strict";n.r(t),n.d(t,"AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY",(function(){return i})),n.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY",(function(){return r})),n.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION",(function(){return a})),n.d(t,"AREA_MAIN_DASHBOARD_CONTENT_PRIMARY",(function(){return o})),n.d(t,"AREA_MAIN_DASHBOARD_SPEED_PRIMARY",(function(){return c})),n.d(t,"AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY",(function(){return l})),n.d(t,"AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY",(function(){return s})),n.d(t,"AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY",(function(){return u})),n.d(t,"AREA_ENTITY_DASHBOARD_SPEED_PRIMARY",(function(){return d})),n.d(t,"AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY",(function(){return g}));var i="mainDashboardKeyMetricsPrimary",r="mainDashboardTrafficPrimary",a="mainDashboardTrafficAudienceSegmentation",o="mainDashboardContentPrimary",c="mainDashboardSpeedPrimary",l="mainDashboardMonetizationPrimary",s="entityDashboardTrafficPrimary",u="entityDashboardContentPrimary",d="entityDashboardSpeedPrimary",g="entityDashboardMonetizationPrimary";t.default={AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY:i,AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY:r,AREA_MAIN_DASHBOARD_CONTENT_PRIMARY:o,AREA_MAIN_DASHBOARD_SPEED_PRIMARY:c,AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY:l,AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY:s,AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY:u,AREA_ENTITY_DASHBOARD_SPEED_PRIMARY:d,AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY:g}},function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n(12),r=n.n(i),a=function(e,t){var n=t.dateRangeLength;r()(Array.isArray(e),"report must be an array to partition."),r()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var i=-1*n;return{currentRange:e.slice(i),compareRange:e.slice(2*i,i)}}},function(e,t,n){"use strict";(function(e,i){n.d(t,"c",(function(){return v})),n.d(t,"b",(function(){return _})),n.d(t,"a",(function(){return TourTooltips}));var r=n(6),a=n.n(r),o=n(81),c=n(30),l=n(1),s=n.n(l),u=n(2),d=n(3),g=n(23),f=n(7),m=n(36),p=n(107),h=n(18);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}var v={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},E={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},_={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},O="feature_tooltip_view",k="feature_tooltip_advance",y="feature_tooltip_return",j="feature_tooltip_dismiss",S="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,r=t.tourID,l=t.gaEventCategory,s=t.callback,u="".concat(r,"-step"),w="".concat(r,"-run"),C=Object(d.useDispatch)(g.b).setValue,A=Object(d.useDispatch)(f.a).dismissTour,N=Object(d.useRegistry)(),T=Object(h.a)(),x=Object(d.useSelect)((function(e){return e(g.b).getValue(u)})),D=Object(d.useSelect)((function(e){return e(g.b).getValue(w)&&!1===e(f.a).isTourDismissed(r)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(r)),C(w,!0)}));var R=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return i.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,i=e.lifecycle,r=e.size,a=e.status,o=e.type,s=t+1,u="function"==typeof l?l(T):l;o===c.b.TOOLTIP&&i===c.c.TOOLTIP?Object(m.b)(u,O,s):n===c.a.CLOSE&&i===c.c.COMPLETE?Object(m.b)(u,j,s):n===c.a.NEXT&&a===c.d.FINISHED&&o===c.b.TOUR_END&&r===s&&Object(m.b)(u,S,s),i===c.c.COMPLETE&&a!==c.d.FINISHED&&(n===c.a.PREV&&Object(m.b)(u,y,s),n===c.a.NEXT&&Object(m.b)(u,k,s))}(t);var n=t.action,i=t.index,a=t.status,o=t.step,d=t.type,g=n===c.a.CLOSE,f=!g&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),p=[c.d.FINISHED,c.d.SKIPPED].includes(a),h=g&&d===c.b.STEP_AFTER,b=p||h;if(c.b.STEP_BEFORE===d){var v,E,_=o.target;"string"==typeof o.target&&(_=e.document.querySelector(o.target)),null===(v=_)||void 0===v||null===(E=v.scrollIntoView)||void 0===E||E.call(v,{block:"center"})}f?function(e,t){C(u,e+(t===c.a.PREV?-1:1))}(i,n):b&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(r)),A(r)),s&&s(t,N)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:_,locale:E,run:D,showProgress:!0,stepIndex:x,steps:R,styles:v,tooltipComponent:p.a})}TourTooltips.propTypes={steps:s.a.arrayOf(s.a.object).isRequired,tourID:s.a.string.isRequired,gaEventCategory:s.a.oneOfType([s.a.string,s.a.func]).isRequired,callback:s.a.func}}).call(this,n(28),n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Dismiss}));var i=n(5),r=n.n(i),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(1),u=n.n(s),d=n(2),g=n(3),f=n(73),m=n(41),p=n(10);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Dismiss(t){var n=t.id,i=t.primary,a=void 0===i||i,o=t.dismissLabel,c=void 0===o?Object(d.__)("OK, Got it!","google-site-kit"):o,s=t.dismissExpires,u=void 0===s?0:s,h=t.disabled,v=t.onDismiss,E=void 0===v?function(){}:v,_=t.gaTrackingEventArgs,O=t.dismissOptions,k=Object(f.a)(n,null==_?void 0:_.category),y=Object(g.useDispatch)(m.a).dismissNotification,j=function(){var e=l()(r.a.mark((function e(t){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==E?void 0:E(t);case 2:k.dismiss(null==_?void 0:_.label,null==_?void 0:_.value),y(n,b(b({},O),{},{expiresInSeconds:u}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(p.Button,{tertiary:!a,onClick:j,disabled:h},c)}Dismiss.propTypes={id:u.a.string,primary:u.a.bool,dismissLabel:u.a.string,dismissExpires:u.a.number,disabled:u.a.bool,onDismiss:u.a.func,gaTrackingEventArgs:u.a.shape({label:u.a.string,value:u.a.string})}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var i=n(24),r=n(130);function a(t,n){var i=document.querySelector(t);if(!i)return 0;var r=i.getBoundingClientRect().top,a=o(n);return r+e.scrollY-a}function o(e){var t=c(e),n=document.querySelectorAll(".googlesitekit-navigation, .googlesitekit-entity-header");return t+=Array.from(n).reduce((function(e,t){return e+t.offsetHeight}),0)}function c(t){var n=0,a=document.querySelector(".googlesitekit-header");return n=!!a&&"sticky"===e.getComputedStyle(a).position?function(e){var t=document.querySelector(".googlesitekit-header");if(t){if(e===i.b)return t.offsetHeight;var n=t.getBoundingClientRect().bottom;return n<0?0:n}return 0}(t):function(e){var t=document.querySelector("#wpadminbar");return t&&e!==i.b?t.offsetHeight:0}(t),(n=Object(r.a)(n))<0?0:n}}).call(this,n(28))},,function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(10),l=n(20);function CTA(t){var n=t.title,i=t.headerText,r=t.headerContent,a=t.description,s=t.ctaLink,u=t.ctaLabel,d=t.ctaLinkExternal,g=t.ctaType,f=t.error,m=t.onClick,p=t["aria-label"],h=t.children;return e.createElement("div",{className:o()("googlesitekit-cta",{"googlesitekit-cta--error":f})},(i||r)&&e.createElement("div",{className:"googlesitekit-cta__header"},i&&e.createElement("h2",{className:"googlesitekit-cta__header_text"},i),r),e.createElement("div",{className:"googlesitekit-cta__body"},n&&e.createElement("h3",{className:"googlesitekit-cta__title"},n),a&&"string"==typeof a&&e.createElement("p",{className:"googlesitekit-cta__description"},a),a&&"string"!=typeof a&&e.createElement("div",{className:"googlesitekit-cta__description"},a),u&&"button"===g&&e.createElement(c.Button,{"aria-label":p,href:s,onClick:m},u),u&&"link"===g&&e.createElement(l.a,{href:s,onClick:m,"aria-label":p,external:d,hideExternalIndicator:d,arrow:!0},u),h))}CTA.propTypes={title:r.a.string.isRequired,headerText:r.a.string,description:r.a.oneOfType([r.a.string,r.a.node]),ctaLink:r.a.string,ctaLinkExternal:r.a.bool,ctaLabel:r.a.string,ctaType:r.a.string,"aria-label":r.a.string,error:r.a.bool,onClick:r.a.func,children:r.a.node,headerContent:r.a.node},CTA.defaultProps={title:"",headerText:"",headerContent:"",description:"",ctaLink:"",ctaLabel:"",ctaType:"link",error:!1,onClick:function(){}},t.a=CTA}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o}));var i=n(239),r=n(85),a=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=n.invertColor,o=void 0!==a&&a;return Object(i.a)(e.createElement(r.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var i=n(6),r=n.n(i),a=n(14),o=n(100),c=n(101);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,r=s(s({},u),t);r.referenceSiteURL&&(r.referenceSiteURL=r.referenceSiteURL.toString().replace(/\/+$/,""));var l=Object(o.a)(r,n),d=Object(c.a)(r,n,l,i),g={},f=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=JSON.stringify(t);g[i]||(g[i]=Object(a.once)(d)),g[i].apply(g,t)};return{enableTracking:function(){r.trackingEnabled=!0},disableTracking:function(){r.trackingEnabled=!1},initializeSnippet:l,isTrackingEnabled:function(){return!!r.trackingEnabled},trackEvent:d,trackEventOnce:f}}}).call(this,n(28))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var i=n(59),r=n(39),a=n(57);function o(t,n){var o,c=Object(i.a)(n),l=t.activeModules,s=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,g=void 0===d?[]:d,f=t.isAuthenticated,m=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(r.b,"]"))),!o){o=!0;var i=(null==g?void 0:g.length)?g.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:s,plugin_version:m||"",enabled_features:Array.from(a.a).join(","),active_modules:l.join(","),authenticated:f?"1":"0",user_properties:{user_roles:i,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(r.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(r.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(r.a)}}}}}).call(this,n(28))},function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var i=n(5),r=n.n(i),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n,i){var a=Object(s.a)(t);return function(){var t=l()(r.a.mark((function t(o,c,l,s){var u;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:l,value:s},t.abrupt("return",new Promise((function(e){var t,n,r=setTimeout((function(){i.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),l=function(){clearTimeout(r),e()};a("event",c,d(d({},u),{},{event_callback:l})),(null===(t=i._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&l()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,i,r){return t.apply(this,arguments)}}()}},function(e,t,n){"use strict";var i=n(123);n.d(t,"a",(function(){return i.a}));var r=n(124);n.d(t,"c",(function(){return r.a}));var a=n(125);n.d(t,"b",(function(){return a.a}))},,function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return i.createElement("svg",r({viewBox:"0 0 14 14",fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e){var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s);function VisuallyHidden(t){var n=t.className,i=t.children,a=o()(t,["className","children"]);return i?e.createElement("span",r()({},a,{className:u()("screen-reader-text",n)}),i):null}VisuallyHidden.propTypes={className:l.a.string,children:l.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var i=n(21),r=n.n(i),a=n(152),o=n.n(a),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(2),g=n(10),f=n(154),m=n(104);function TourTooltip(t){var n=t.backProps,i=t.closeProps,c=t.index,s=t.primaryProps,u=t.size,p=t.step,h=t.tooltipProps,b=u>1?Object(f.a)(u):[],v=function(e){return l()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",r()({className:l()("googlesitekit-tour-tooltip",p.className)},h),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},p.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},p.content)),e.createElement(a.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},b.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:v(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(g.Button,r()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),p.cta,s.title&&e.createElement(g.Button,r()({className:"googlesitekit-tooltip-button",text:!0},s),s.title))),e.createElement(g.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(m.a,{width:"14",height:"14"}),onClick:i.onClick,"aria-label":Object(d.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(208),l=n(38),s=n(2),u=n(10),d=n(58);function ModalDialog(t){var n=t.className,i=void 0===n?"":n,r=t.dialogActive,a=void 0!==r&&r,g=t.handleDialog,f=void 0===g?null:g,m=t.onOpen,p=void 0===m?null:m,h=t.onClose,b=void 0===h?null:h,v=t.title,E=void 0===v?null:v,_=t.provides,O=t.handleConfirm,k=t.subtitle,y=t.confirmButton,j=void 0===y?null:y,S=t.dependentModules,w=t.danger,C=void 0!==w&&w,A=t.inProgress,N=void 0!==A&&A,T=t.small,x=void 0!==T&&T,D=t.medium,R=void 0!==D&&D,M=t.buttonLink,I=void 0===M?null:M,P=Object(c.a)(ModalDialog),L="googlesitekit-dialog-description-".concat(P),B=!(!_||!_.length);return e.createElement(u.Dialog,{open:a,onOpen:p,onClose:b,"aria-describedby":B?L:void 0,tabIndex:"-1",className:o()(i,{"googlesitekit-dialog-sm":x,"googlesitekit-dialog-md":R})},e.createElement(u.DialogTitle,null,C&&e.createElement(d.a,{width:28,height:28}),E),k?e.createElement("p",{className:"mdc-dialog__lead"},k):[],e.createElement(u.DialogContent,null,B&&e.createElement("section",{id:L,className:"mdc-dialog__provides"},e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},_.map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t},e.createElement("span",{className:"mdc-list-item__text"},t))})))),S&&e.createElement("p",{className:"mdc-dialog__dependencies"},Object(l.a)(Object(s.sprintf)(
/* translators: %s is replaced with the dependent modules. */
Object(s.__)("<strong>Note:</strong> %s","google-site-kit"),S),{strong:e.createElement("strong",null)}))),e.createElement(u.DialogFooter,null,e.createElement(u.Button,{className:"mdc-dialog__cancel-button",tertiary:!0,onClick:f,disabled:N},Object(s.__)("Cancel","google-site-kit")),I?e.createElement(u.Button,{href:I,onClick:O,target:"_blank",danger:C},j):e.createElement(u.SpinnerButton,{onClick:O,danger:C,disabled:N,isSaving:N},j||Object(s.__)("Disconnect","google-site-kit"))))}ModalDialog.displayName="Dialog",ModalDialog.propTypes={className:r.a.string,dialogActive:r.a.bool,handleDialog:r.a.func,handleConfirm:r.a.func.isRequired,onOpen:r.a.func,onClose:r.a.func,title:r.a.string,confirmButton:r.a.string,danger:r.a.bool,small:r.a.bool,medium:r.a.bool,buttonLink:r.a.string},t.a=ModalDialog}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var i=n(1),r=n.n(i),a=n(0),o=n(9),c=n(54);function Description(t){var n=t.className,i=void 0===n?"googlesitekit-publisher-win__desc":n,r=t.text,l=t.learnMoreLink,s=t.errorText,u=t.children;return e.createElement(a.Fragment,null,e.createElement("div",{className:i},e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.F)(r,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",l)),s&&e.createElement(c.a,{message:s}),u)}Description.propTypes={className:r.a.string,text:r.a.string,learnMoreLink:r.a.node,errorText:r.a.string,children:r.a.node}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n(392),r=function(e,t,n){Object(i.a)((function(n){return e.includes(n.keyCode)&&t.current.contains(n.target)}),n)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return h}));var i=n(6),r=n.n(i),a=n(21),o=n.n(a),c=n(15),l=n.n(c),s=n(25),u=n.n(s),d=n(240),g=n(1),f=n.n(g),m=n(0);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function h(t){function WithIntersectionObserverComponent(n){var i=n.onInView,r=u()(n,["onInView"]),a=Object(m.useRef)(),c=Object(d.a)(a,{root:null,threshold:.45}),s=Object(m.useState)(!1),g=l()(s,2),f=g[0],p=g[1],h=!!(null==c?void 0:c.isIntersecting)&&!!(null==c?void 0:c.intersectionRatio);return Object(m.useEffect)((function(){c&&h&&!f&&(i(),p(!0))}),[f,h,c,i]),e.createElement(t,o()({ref:a},r))}return WithIntersectionObserverComponent.displayName="WithIntersectionObserverComponent",(t.displayName||t.name)&&(WithIntersectionObserverComponent.displayName+="(".concat(t.displayName||t.name,")")),WithIntersectionObserverComponent.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({onInView:f.a.func.isRequired},t.propTypes),WithIntersectionObserverComponent}}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(0),l=n(137),s=n(58),u=n(131),d=n(17),g=Object(c.forwardRef)((function(t,n){var i=t.className,r=t.title,a=t.description,c=t.dismissCTA,g=t.additionalCTA,f=t.reverseCTAs,m=void 0!==f&&f,p=t.type,h=void 0===p?"success":p,b=t.icon;return e.createElement(d.e,{ref:n},e.createElement(d.k,null,e.createElement(d.a,{alignMiddle:!0,size:12,className:o()("googlesitekit-subtle-notification",i,{"googlesitekit-subtle-notification--success":"success"===h,"googlesitekit-subtle-notification--warning":"warning"===h,"googlesitekit-subtle-notification--new-feature":"new-feature"===h})},e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},b,"success"===h&&!b&&e.createElement(l.a,{width:24,height:24}),"warning"===h&&!b&&e.createElement(s.a,{width:24,height:24}),"new-feature"===h&&!b&&e.createElement(u.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,r),e.createElement("p",{className:"googlesitekit-subtle-notification__secondary_description"},a)),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},!m&&c,m&&g,!m&&g,m&&c))))}));g.propTypes={className:r.a.string,title:r.a.node,description:r.a.node,dismissCTA:r.a.node,additionalCTA:r.a.node,reverseCTAs:r.a.bool,type:r.a.oneOf(["success","warning","new-feature"]),icon:r.a.object},t.a=g}).call(this,n(4))},,function(e,t,n){"use strict";var i=n(326),r=n(314);n.d(t,"b",(function(){return r.a}));var a=n(315);n.d(t,"c",(function(){return a.a}));var o=n(316);n.d(t,"d",(function(){return o.a}));var c=n(317);n.d(t,"a",(function(){return c.a})),t.e=i.a},function(e,t,n){"use strict";n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return d}));var i,r=n(6),a=n.n(r),o=n(47),c=n(174),l=n(163),s=(i={},a()(i,o.c.QUARTER,3),a()(i,o.c.HALF,6),a()(i,o.c.FULL,12),i),u="googlesitekit-hidden",d=[c.a,l.a]},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var i=n(1),r=n.n(i),a=n(0),o=n(2),c=n(3),l=n(10),s=n(35),u=n(54);function ErrorNotice(t){var n,i=t.error,r=t.hasButton,d=void 0!==r&&r,g=t.storeName,f=t.message,m=void 0===f?i.message:f,p=t.noPrefix,h=void 0!==p&&p,b=t.skipRetryMessage,v=t.Icon,E=Object(c.useDispatch)(),_=Object(c.useSelect)((function(e){return g?e(g).getSelectorDataForError(i):null})),O=Object(a.useCallback)((function(){E(_.storeName).invalidateResolution(_.name,_.args)}),[E,_]);if(!i||Object(s.f)(i))return null;var k=d&&Object(s.d)(i,_);return d||b||(m=Object(o.sprintf)(
/* translators: %1$s: Error message from Google API. */
Object(o.__)("%1$s%2$s Please try again.","google-site-kit"),m,m.endsWith(".")?"":".")),e.createElement(a.Fragment,null,v&&e.createElement("div",{className:"googlesitekit-error-notice__icon"},e.createElement(v,{width:"24",height:"24"})),e.createElement(u.a,{message:m,reconnectURL:null===(n=i.data)||void 0===n?void 0:n.reconnectURL,noPrefix:h}),k&&e.createElement(l.Button,{className:"googlesitekit-error-notice__retry-button",onClick:O},Object(o.__)("Retry","google-site-kit")))}ErrorNotice.propTypes={error:r.a.shape({message:r.a.string}),hasButton:r.a.bool,storeName:r.a.string,message:r.a.string,noPrefix:r.a.bool,skipRetryMessage:r.a.bool,Icon:r.a.elementType}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n(219),r=n(14),a=n(0);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=Object(i.b)((function(){return r.debounce.apply(void 0,t)}),t);return Object(a.useEffect)((function(){return function(){return o.cancel()}}),[o]),o}},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var i=n(21),r=n.n(i),a=n(6),o=n.n(a),c=n(25),l=n.n(c),s=n(1),u=n.n(s),d=n(11),g=n.n(d);function Cell(t){var n,i=t.className,a=t.alignTop,c=t.alignMiddle,s=t.alignBottom,u=t.alignRight,d=t.alignLeft,f=t.smAlignRight,m=t.mdAlignRight,p=t.lgAlignRight,h=t.smSize,b=t.smStart,v=t.smOrder,E=t.mdSize,_=t.mdStart,O=t.mdOrder,k=t.lgSize,y=t.lgStart,j=t.lgOrder,S=t.size,w=t.children,C=l()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",r()({},C,{className:g()(i,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":s,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":f,"mdc-layout-grid__cell--align-right-tablet":m,"mdc-layout-grid__cell--align-right-desktop":p},o()(n,"mdc-layout-grid__cell--span-".concat(S),12>=S&&S>0),o()(n,"mdc-layout-grid__cell--span-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--start-".concat(y,"-desktop"),12>=y&&y>0),o()(n,"mdc-layout-grid__cell--order-".concat(j,"-desktop"),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(E,"-tablet"),8>=E&&E>0),o()(n,"mdc-layout-grid__cell--start-".concat(_,"-tablet"),8>=_&&_>0),o()(n,"mdc-layout-grid__cell--order-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--span-".concat(h,"-phone"),4>=h&&h>0),o()(n,"mdc-layout-grid__cell--start-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--order-".concat(v,"-phone"),4>=v&&v>0),n))}),w)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),g=Object(d.forwardRef)((function(t,n){var i=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",r()({ref:n,className:u()("mdc-layout-grid__inner",i)},c),a)}));g.displayName="Row",g.propTypes={className:l.a.string,children:l.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),g=Object(d.forwardRef)((function(t,n){var i=t.alignLeft,a=t.fill,c=t.className,l=t.children,s=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",r()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":i,"mdc-layout-grid--collapsed":s,"mdc-layout-grid--fill":a})},d,{ref:n}),l)}));g.displayName="Grid",g.propTypes={alignLeft:l.a.bool,fill:l.a.bool,className:l.a.string,collapsed:l.a.bool,children:l.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{fill:"none",fillRule:"evenodd"},i.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),i.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return i.createElement("svg",r({viewBox:"0 0 13 13"},e),a)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{fill:"none",fillRule:"evenodd"},i.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),i.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return i.createElement("svg",r({viewBox:"0 0 13 13"},e),a)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return i.createElement("svg",r({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},function(e,t,n){"use strict";n.d(t,"a",(function(){return p})),n.d(t,"c",(function(){return b})),n.d(t,"b",(function(){return v}));var i=n(25),r=n.n(i),a=n(6),o=n.n(a),c=n(5),l=n.n(c),s=n(12),u=n.n(s),d=n(3),g=n.n(d),f=n(37),m=n(9),p=function(e){var t;u()(e,"storeName is required to create a snapshot store.");var n={},i={deleteSnapshot:l.a.mark((function e(){var t;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"DELETE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})),restoreSnapshot:l.a.mark((function e(){var t,n,i,r,a,o,c=arguments;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},n=t.clearAfterRestore,i=void 0===n||n,e.next=4,{payload:{},type:"RESTORE_SNAPSHOT"};case 4:if(r=e.sent,a=r.cacheHit,o=r.value,!a){e.next=13;break}return e.next=10,{payload:{snapshot:o},type:"SET_STATE_FROM_SNAPSHOT"};case 10:if(!i){e.next=13;break}return e.next=13,{payload:{},type:"DELETE_SNAPSHOT"};case 13:return e.abrupt("return",a);case 14:case"end":return e.stop()}}),e)})),createSnapshot:l.a.mark((function e(){var t;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"CREATE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))},a=(t={},o()(t,"DELETE_SNAPSHOT",(function(){return Object(f.c)("datastore::cache::".concat(e))})),o()(t,"CREATE_SNAPSHOT",Object(d.createRegistryControl)((function(t){return function(){return Object(f.f)("datastore::cache::".concat(e),t.stores[e].store.getState())}}))),o()(t,"RESTORE_SNAPSHOT",(function(){return Object(f.d)("datastore::cache::".concat(e),m.b)})),t);return{initialState:n,actions:i,controls:a,reducer:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1?arguments[1]:void 0,i=t.type,a=t.payload;switch(i){case"SET_STATE_FROM_SNAPSHOT":var o=a.snapshot,c=(o.error,r()(o,["error"]));return c;default:return e}}}},h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Object.values(e.stores).filter((function(e){return Object.keys(e.getActions()).includes("restoreSnapshot")}))},b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Promise.all(h(e).map((function(e){return e.getActions().createSnapshot()})))},v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Promise.all(h(e).map((function(e){return e.getActions().restoreSnapshot()})))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n(14),r=function(e){return Object(i.isFinite)(e)?e:0}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return i.createElement("svg",r({viewBox:"0 0 24 24",fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InfoTooltip}));var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(10),l=n(325);function InfoTooltip(t){var n=t.onOpen,i=t.title,a=t.tooltipClassName;return i?e.createElement(c.Tooltip,{className:"googlesitekit-info-tooltip",tooltipClassName:r()("googlesitekit-info-tooltip__content",a),title:i,placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,interactive:!0,onOpen:n},e.createElement("span",null,e.createElement(l.a,{width:"16",height:"16"}))):null}InfoTooltip.propTypes={onOpen:o.a.func,title:o.a.oneOfType([o.a.string,o.a.element]),tooltipClassName:o.a.string}}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(38),l=n(2),s=n(20),u=n(34);function SourceLink(t){var n=t.name,i=t.href,r=t.className,a=t.external;return Object(u.a)()?null:e.createElement("div",{className:o()("googlesitekit-source-link",r)},Object(c.a)(Object(l.sprintf)(
/* translators: %s: source link */
Object(l.__)("Source: %s","google-site-kit"),"<a>".concat(n,"</a>")),{a:e.createElement(s.a,{key:"link",href:i,external:a})}))}SourceLink.propTypes={name:r.a.string,href:r.a.string,className:r.a.string,external:r.a.bool},SourceLink.defaultProps={name:"",href:"",className:"",external:!1},t.a=SourceLink}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportErrorActions}));var i=n(6),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=n(38),s=n(2),u=n(3),d=n(10),g=n(13),f=n(19),m=n(35),p=n(34),h=n(20);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportErrorActions(t){var n=t.moduleSlug,i=t.error,r=t.GetHelpLink,a=t.hideGetHelpLink,o=t.buttonVariant,b=t.onRetry,E=t.onRequestAccess,_=t.getHelpClassName,O=t.RequestAccessButton,k=t.RetryButton,y=Object(p.a)(),j=Object(u.useSelect)((function(e){return e(f.a).getModuleStoreName(n)})),S=Object(u.useSelect)((function(e){var t;return"function"==typeof(null===(t=e(j))||void 0===t?void 0:t.getServiceEntityAccessURL)?e(j).getServiceEntityAccessURL():null})),w=Array.isArray(i)?i:[i],C=Object(u.useSelect)((function(e){return w.map((function(t){var n,i=null===(n=e(j))||void 0===n?void 0:n.getSelectorDataForError(t);return v(v({},t),{},{selectorData:i})}))})),A=null==C?void 0:C.filter((function(e){return Object(m.d)(e,e.selectorData)&&"getReport"===e.selectorData.name})),N=!!A.length,T=Object(u.useSelect)((function(e){var t=v({},N?A[0]:w[0]);return Object(m.e)(t)&&(t.code="".concat(n,"_insufficient_permissions")),e(g.c).getErrorTroubleshootingLinkURL(t)})),x=Object(u.useDispatch)(),D=w.some((function(e){return Object(m.e)(e)})),R=Object(c.useCallback)((function(){A.forEach((function(e){var t=e.selectorData;x(t.storeName).invalidateResolution(t.name,t.args)})),null==b||b()}),[x,A,b]),M=S&&D&&!y;return e.createElement("div",{className:"googlesitekit-report-error-actions"},M&&("function"==typeof O?e.createElement(O,{requestAccessURL:S}):e.createElement(d.Button,{onClick:E,href:S,target:"_blank",danger:"danger"===o,tertiary:"tertiary"===o},Object(s.__)("Request access","google-site-kit"))),N&&e.createElement(c.Fragment,null,"function"==typeof k?e.createElement(k,{handleRetry:R}):e.createElement(d.Button,{onClick:R,danger:"danger"===o,tertiary:"tertiary"===o},Object(s.__)("Retry","google-site-kit")),!a&&e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(l.a)(Object(s.__)("Retry didn’t work? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(h.a,{href:T,external:!0,hideExternalIndicator:!0},Object(s.__)("Get help","google-site-kit"))}))),!N&&!a&&e.createElement("div",{className:_},"function"==typeof r?e.createElement(r,{linkURL:T}):e.createElement(h.a,{href:T,external:!0,hideExternalIndicator:!0},Object(s.__)("Get help","google-site-kit"))))}ReportErrorActions.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired,GetHelpLink:o.a.elementType,hideGetHelpLink:o.a.bool,buttonVariant:o.a.string,onRetry:o.a.func,onRequestAccess:o.a.func,getHelpClassName:o.a.string,RequestAccessButton:o.a.elementType,RetryButton:o.a.elementType}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return r})),n.d(t,"d",(function(){return a})),n.d(t,"c",(function(){return o}));function i(e){var t=e.format,n=void 0===t?"small":t,i=e.hasErrorOrWarning,r=e.hasSmallImageSVG,o=e.hasWinImageSVG,c={smSize:4,mdSize:8,lgSize:12},l=a(n);return Object.keys(c).forEach((function(e){var t=c[e];i&&(t-=1),r&&(t-=1),o&&0<t-l[e]&&(t-=l[e]),c[e]=t})),c}var r=function(e){switch(e){case"small":return{};case"larger":return{smOrder:2,mdOrder:2,lgOrder:1};default:return{smOrder:2,mdOrder:1}}},a=function(e){switch(e){case"smaller":return{smSize:4,mdSize:2,lgSize:2};case"larger":return{smSize:4,mdSize:8,lgSize:7};default:return{smSize:4,mdSize:2,lgSize:4}}},o=function(e){switch(e){case"larger":return{smOrder:1,mdOrder:1,lgOrder:2};default:return{smOrder:1,mdOrder:2}}}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return i.createElement("svg",r({viewBox:"0 0 24 24",fill:"none"},e),a)}},,function(e,t,n){"use strict";var i=n(0),r=Object(i.createContext)(!1);t.a=r},,,function(e,t,n){"use strict";var i=n(166);n.d(t,"c",(function(){return i.a}));var r=n(65);n.d(t,"b",(function(){return r.c})),n.d(t,"a",(function(){return r.a}))},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n(0),r=n(3),a=n(47);function o(e,t,n){var o=Object(r.useDispatch)(a.a),c=o.setWidgetState,l=o.unsetWidgetState;Object(i.useEffect)((function(){return c(e,t,n),function(){l(e,t,n)}}),[e,t,n,c,l])}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var i=n(6),r=n.n(i),a=n(2),o=n(7),c=n(13),l=n(8);function s(e,t,n){return e(l.r).hasConversionReportingEvents(this.requiredConversionEventName)||e(o.a).isKeyMetricActive(n)}var u,d=n(26);function g(e,t){return!t||!(!t||!e(l.r).getAdSenseLinked())}function f(e,t){return!t||e(l.r).hasCustomDimensions(this.requiredCustomDimensions)}var m=(u={},r()(u,o.f,{title:Object(a.__)("Top earning pages","google-site-kit"),description:Object(a.__)("Pages that generated the most AdSense revenue","google-site-kit"),infoTooltip:Object(a.__)("Pages that generated the most AdSense revenue","google-site-kit"),displayInSelectionPanel:g,displayInList:g,metadata:{group:d.b.SLUG}}),r()(u,o.y,{title:Object(a.__)("Top recent trending pages","google-site-kit"),description:Object(a.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),infoTooltip:Object(a.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_date"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:d.b.SLUG}}),r()(u,o.l,{title:Object(a.__)("Most popular authors by pageviews","google-site-kit"),description:Object(a.__)("Authors whose posts got the most visits","google-site-kit"),infoTooltip:Object(a.__)("Authors whose posts got the most visits","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_author"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:d.b.SLUG}}),r()(u,o.p,{title:Object(a.__)("Top categories by pageviews","google-site-kit"),description:Object(a.__)("Categories that your site visitors viewed the most","google-site-kit"),infoTooltip:Object(a.__)("Categories that your site visitors viewed the most","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_categories"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:d.b.SLUG}}),r()(u,o.m,{title:Object(a.__)("Most popular content by pageviews","google-site-kit"),description:Object(a.__)("Pages that brought in the most visitors","google-site-kit"),infoTooltip:Object(a.__)("Pages your visitors read the most","google-site-kit"),metadata:{group:d.b.SLUG}}),r()(u,o.n,{title:Object(a.__)("Most popular products by pageviews","google-site-kit"),description:Object(a.__)("Products that brought in the most visitors","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_type"],displayInSelectionPanel:function(e){return e(o.a).isKeyMetricActive(o.n)||e(c.c).getProductPostType()},displayInWidgetArea:f,metadata:{group:d.f.SLUG}}),r()(u,o.k,{title:Object(a.__)("Pages per visit","google-site-kit"),description:Object(a.__)("Number of pages visitors viewed per session on average","google-site-kit"),infoTooltip:Object(a.__)("Number of pages visitors viewed per session on average","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.F,{title:Object(a.__)("Visit length","google-site-kit"),description:Object(a.__)("Average duration of engaged visits","google-site-kit"),infoTooltip:Object(a.__)("Average duration of engaged visits","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.E,{title:Object(a.__)("Visits per visitor","google-site-kit"),description:Object(a.__)("Average number of sessions per site visitor","google-site-kit"),infoTooltip:Object(a.__)("Average number of sessions per site visitor","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.i,{title:Object(a.__)("Most engaging pages","google-site-kit"),description:Object(a.__)("Pages with the highest engagement rate","google-site-kit"),infoTooltip:Object(a.__)("Pages with the highest engagement rate","google-site-kit"),metadata:{group:d.b.SLUG}}),r()(u,o.h,{title:Object(a.__)("Least engaging pages","google-site-kit"),description:Object(a.__)("Pages with the highest percentage of visitors that left without engagement with your site","google-site-kit"),infoTooltip:Object(a.__)("Percentage of visitors that left without engagement with your site","google-site-kit"),metadata:{group:d.b.SLUG}}),r()(u,o.z,{title:Object(a.__)("Top pages by returning visitors","google-site-kit"),description:Object(a.__)("Pages that attracted the most returning visitors","google-site-kit"),infoTooltip:Object(a.__)("Pages that attracted the most returning visitors","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.j,{title:Object(a.__)("New visitors","google-site-kit"),description:Object(a.__)("How many new visitors you got and how the overall audience changed","google-site-kit"),infoTooltip:Object(a.__)("Portion of visitors who visited your site for the first time in this timeframe","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.o,{title:Object(a.__)("Returning visitors","google-site-kit"),description:Object(a.__)("Portion of people who visited your site more than once","google-site-kit"),infoTooltip:Object(a.__)("Portion of your site’s visitors that returned at least once in this timeframe","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.A,{title:Object(a.__)("Top traffic source","google-site-kit"),description:Object(a.__)("Channel which brought in the most visitors to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most visitors to your site","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.B,{title:Object(a.__)("Top traffic source driving add to cart","google-site-kit"),description:Object(a.__)("Channel which brought in the most add to cart events to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most add to cart events to your site","google-site-kit"),requiredConversionEventName:[l.l.ADD_TO_CART],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.C,{title:Object(a.__)("Top traffic source driving leads","google-site-kit"),description:Object(a.__)("Channel which brought in the most leads to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most leads to your site","google-site-kit"),requiredConversionEventName:[l.l.SUBMIT_LEAD_FORM,l.l.CONTACT,l.l.GENERATE_LEAD],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.e.SLUG}}),r()(u,o.D,{title:Object(a.__)("Top traffic source driving purchases","google-site-kit"),description:Object(a.__)("Channel which brought in the most purchases to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most purchases to your site","google-site-kit"),requiredConversionEventName:[l.l.PURCHASE],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.g,{title:Object(a.__)("Most engaged traffic source","google-site-kit"),description:Object(a.__)("Visitors coming via this channel spent the most time on your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most visitors who had a meaningful engagement with your site","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.u,{title:Object(a.__)("Top converting traffic source","google-site-kit"),description:Object(a.__)("Channel which brought in the most visits that resulted in conversions","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in visitors who generated the most conversions","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.q,{title:Object(a.__)("Top cities driving traffic","google-site-kit"),description:Object(a.__)("Which cities you get the most visitors from","google-site-kit"),infoTooltip:Object(a.__)("The cities where most of your visitors came from","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.s,{title:Object(a.__)("Top cities driving leads","google-site-kit"),description:Object(a.__)("Cities driving the most contact form submissions","google-site-kit"),infoTooltip:Object(a.__)("Cities driving the most contact form submissions","google-site-kit"),requiredConversionEventName:[l.l.SUBMIT_LEAD_FORM,l.l.CONTACT,l.l.GENERATE_LEAD],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.e.SLUG}}),r()(u,o.r,{title:Object(a.__)("Top cities driving add to cart","google-site-kit"),description:Object(a.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),infoTooltip:Object(a.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),requiredConversionEventName:[l.l.ADD_TO_CART],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.t,{title:Object(a.__)("Top cities driving purchases","google-site-kit"),description:Object(a.__)("Cities driving the most purchases","google-site-kit"),infoTooltip:Object(a.__)("Cities driving the most purchases","google-site-kit"),requiredConversionEventName:[l.l.PURCHASE],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.w,{title:Object(a.__)("Top device driving purchases","google-site-kit"),description:Object(a.__)("Top device driving the most purchases","google-site-kit"),infoTooltip:Object(a.__)("Top device driving the most purchases","google-site-kit"),requiredConversionEventName:[l.l.PURCHASE],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.v,{title:Object(a.__)("Top countries driving traffic","google-site-kit"),description:Object(a.__)("Which countries you get the most visitors from","google-site-kit"),infoTooltip:Object(a.__)("The countries where most of your visitors came from","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.G,{title:Object(a.__)("Top performing keywords","google-site-kit"),description:Object(a.__)("What people searched for before they came to your site","google-site-kit"),infoTooltip:Object(a.__)("The top search queries for your site by highest clickthrough rate","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.x,{title:Object(a.__)("Top pages driving leads","google-site-kit"),description:Object(a.__)("Pages on which forms are most frequently submitted","google-site-kit"),requiredConversionEventName:[l.l.SUBMIT_LEAD_FORM,l.l.CONTACT,l.l.GENERATE_LEAD],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.e.SLUG}}),u)},,function(e,t,n){"use strict";(function(e){var i=n(6),r=n.n(i),a=n(15),o=n.n(a),c=n(0),l=n(409),s=n(157);t.a=function(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=Object(c.useState)(Object(l.a)(i.location.href,t)||n),u=o()(a,2),d=u[0],g=u[1],f=function(e){g(e);var n=Object(s.a)(i.location.href,r()({},t,e));i.history.replaceState(null,"",n)};return[d,f]}}).call(this,n(28))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M3.332 24.75h21.335c2.182 0 3.542-2.366 2.451-4.25L16.451 2.07C15.36.184 12.64.184 11.549 2.07L.882 20.5c-1.091 1.884.269 4.25 2.45 4.25zM14 14.833a1.42 1.42 0 01-1.417-1.416v-2.834c0-.779.638-1.416 1.417-1.416.78 0 1.417.637 1.417 1.416v2.834A1.42 1.42 0 0114 14.833zm1.417 5.667h-2.834v-2.833h2.834V20.5z",fill:"currentColor",fillRule:"nonzero"});t.a=function SvgError(e){return i.createElement("svg",r({viewBox:"0 0 28 25"},e),a)}},,,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{fill:"none",fillRule:"evenodd"},i.createElement("path",{d:"M2.253 12.252l7.399 5.658A13.055 13.055 0 009 22c0 1.43.229 2.805.652 4.09l-7.4 5.658A22.02 22.02 0 010 22c0-3.506.81-6.814 2.253-9.748z",fill:"#FBBC05"}),i.createElement("path",{d:"M9.652 17.91l-7.4-5.658A21.935 21.935 0 0122 0c5.6 0 10.6 2.1 14.5 5.5l-6.4 6.4C27.9 10.1 25.1 9 22 9c-5.77 0-10.64 3.725-12.348 8.91z",fill:"#EA4335"}),i.createElement("path",{d:"M2.25 31.742l7.396-5.67A12.975 12.975 0 0022 35c6.1 0 10.7-3.1 11.8-8.5H22V18h20.5c.3 1.3.5 2.7.5 4 0 14-10 22-21 22A21.935 21.935 0 012.25 31.742z",fill:"#34A853"}),i.createElement("path",{d:"M36.34 38.52l-7.025-5.437c2.297-1.45 3.895-3.685 4.485-6.583H22V18h20.5c.3 1.3.5 2.7.5 4 0 7.17-2.623 12.767-6.66 16.52z",fill:"#4285F4"}));t.a=function SvgLogoG(e){return i.createElement("svg",r({viewBox:"0 0 43 44"},e),a)}},,,function(e,t,n){"use strict";var i=n(0),r=n(57),a=Object(i.createContext)(r.a);t.a=a},function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(0),l=n(3),s=n(13),u=n(7),d=n(19),g=n(32),f=n(37),m=n(36),p=n(18);function h(e){var t=Object(p.a)(),n=Object(l.useSelect)((function(t){return t(d.a).getModule(e)})),i=Object(l.useSelect)((function(e){return e(u.a).hasCapability(u.K)})),a=Object(l.useDispatch)(d.a).activateModule,h=Object(l.useDispatch)(g.a).navigateTo,b=Object(l.useDispatch)(s.c).setInternalServerError,v=Object(c.useCallback)(o()(r.a.mark((function n(){var i,o,c;return r.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,a(e);case 2:if(i=n.sent,o=i.error,c=i.response,o){n.next=13;break}return n.next=8,Object(m.b)("".concat(t,"_widget-activation-cta"),"activate_module",e);case 8:return n.next=10,Object(f.f)("module_setup",e,{ttl:300});case 10:h(c.moduleReauthURL),n.next=14;break;case 13:b({id:"".concat(e,"-setup-error"),description:o.message});case 14:case"end":return n.stop()}}),n)}))),[a,e,h,b,t]);return(null==n?void 0:n.name)&&i?v:null}},function(e,t,n){"use strict";var i=n(139),r=(i.a.Consumer,i.a.Provider);t.a=r},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n(3),r=n(23),a=function(e){return"notification/".concat(e,"/viewed")};function o(e){return Object(i.useSelect)((function(t){return!!t(r.b).getValue(a(e))}),[e])}o.getKey=a},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RecoverableModules}));var i=n(1),r=n.n(i),a=n(2),o=n(3),c=n(19),l=n(95);function RecoverableModules(t){var n=t.moduleSlugs,i=Object(o.useSelect)((function(e){var t=e(c.a).getModules();if(void 0!==t)return n.map((function(e){return t[e].name}))}));if(void 0===i)return null;var r=1===i.length?Object(a.sprintf)(
/* translators: %s: Module name */
Object(a.__)("%s data was previously shared by an admin who no longer has access. Please contact another admin to restore it.","google-site-kit"),i[0]):Object(a.sprintf)(
/* translators: %s: List of module names */
Object(a.__)("The data for the following modules was previously shared by an admin who no longer has access: %s. Please contact another admin to restore it.","google-site-kit"),i.join(Object(a._x)(", ","Recoverable modules","google-site-kit")));return e.createElement(l.a,{title:Object(a.__)("Data Unavailable","google-site-kit"),description:r})}RecoverableModules.propTypes={moduleSlugs:r.a.arrayOf(r.a.string).isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(1),l=n.n(c),s=n(0),u=n(20),d=n(9),g=n(18);function HelpMenuLink(t){var n=t.children,i=t.href,a=t.gaEventLabel,c=Object(g.a)(),l=Object(s.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!a){e.next=3;break}return e.next=3,Object(d.I)("".concat(c,"_headerbar_helpmenu"),"click_outgoing_link",a);case 3:case"end":return e.stop()}}),e)}))),[a,c]);return e.createElement("li",{className:"googlesitekit-help-menu-link mdc-list-item",role:"none"},e.createElement(u.a,{className:"mdc-list-item__text",href:i,external:!0,hideExternalIndicator:!0,role:"menuitem",onClick:l},n))}HelpMenuLink.propTypes={children:l.a.node.isRequired,href:l.a.string.isRequired,gaEventLabel:l.a.string},t.a=HelpMenuLink}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var i=n(2),r="audience-segmentation-info-notice-ui",a="audience-segmentation-info-notice",o=[{slug:"new-visitors",content:Object(i.__)("The higher the portion of new visitors you have, the more your audience is growing. Looking at what content brings them to your site may give you insights on how to reach even more people.","google-site-kit")},{slug:"compare-metrics",content:Object(i.__)("Select up to three visitor groups to display on the dashboard and easily compare metrics between them.","google-site-kit")},{slug:"custom-audiences",content:Object(i.__)("Configure your own custom audiences in Analytics to gain deeper insights into visitor behavior, for example consider creating an “Existing customers” or “Subscribers” segment, depending on what goals you have for your site.","google-site-kit")},{slug:"purchasers",content:Object(i.__)("Select the Purchasers visitor group to gain insights into which visitors bring the most revenue to your site.","google-site-kit")},{slug:"returning-visitors",content:Object(i.__)("The more returning visitors your site has, the stronger and more loyal an audience you’re building. Check which content brings people back to your site - it might help you create a strategy to build a community.","google-site-kit")},{slug:"compare-new-returning",content:Object(i.__)("Compare the ratio of “new” to “returning” visitors – this can give you insights on whether you have more people stopping by as a one-off, or more loyal visitors.","google-site-kit")},{slug:"compare-cities",content:Object(i.__)("Check the cities which bring you more new vs more returning visitors – there might be new audiences you could engage with in locations you hadn’t thought about.","google-site-kit")}]},function(e,t,n){"use strict";(function(e){var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(2),l=n(3),s=n(201),u=n(210),d=n(65),g=n(7),f=n(10),m=n(0),p=Object(m.forwardRef)((function(t,n){var i=t.className,a=t.children,o=t.type,m=t.dismiss,p=void 0===m?"":m,h=t.dismissCallback,b=t.dismissLabel,v=void 0===b?Object(c.__)("OK, Got it!","google-site-kit"):b,E=t.Icon,_=void 0===E?Object(d.d)(o):E,O=t.OuterCTA,k=Object(l.useDispatch)(g.a).dismissItem,y=Object(l.useSelect)((function(e){return p?e(g.a).isItemDismissed(p):void 0}));if(p&&y)return null;var j=a?u.a:s.a;return e.createElement("div",{ref:n,className:r()(i,"googlesitekit-settings-notice","googlesitekit-settings-notice--".concat(o),{"googlesitekit-settings-notice--single-row":!a,"googlesitekit-settings-notice--multi-row":a})},e.createElement("div",{className:"googlesitekit-settings-notice__icon"},e.createElement(_,{width:"20",height:"20"})),e.createElement("div",{className:"googlesitekit-settings-notice__body"},e.createElement(j,t)),p&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(f.Button,{tertiary:!0,onClick:function(){"string"==typeof p&&k(p),null==h||h()}},v)),O&&e.createElement("div",{className:"googlesitekit-settings-notice__button"},e.createElement(O,null)))}));p.propTypes={className:o.a.string,children:o.a.node,notice:o.a.node.isRequired,type:o.a.oneOf([d.a,d.c,d.b]),Icon:o.a.elementType,LearnMore:o.a.elementType,CTA:o.a.elementType,OuterCTA:o.a.elementType,dismiss:o.a.string,dismissLabel:o.a.string,dismissCallback:o.a.func},p.defaultProps={type:d.a},t.a=p}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notifications}));var i=n(6),r=n.n(i),a=n(1),o=n.n(a),c=n(3),l=n(18),s=n(41),u=n(283);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function Notifications(t){var n,i=t.areaSlug,a=t.groupID,o=void 0===a?s.c.DEFAULT:a,g=Object(l.a)(),f=Object(c.useSelect)((function(e){return e(s.a).getQueuedNotifications(g,o)}));if(void 0===(null==f?void 0:f[0])||(null==f||null===(n=f[0])||void 0===n?void 0:n.areaSlug)!==i)return null;var m=f[0],p=m.id,h=m.Component,b=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},Object(u.a)(p));return e.createElement(h,b)}Notifications.propTypes={viewContext:o.a.string,areaSlug:o.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALinkSubtle}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(73),g=n(10),f=n(70);function CTALinkSubtle(t){var n=t.id,i=t.ctaLink,a=t.ctaLabel,c=t.onCTAClick,l=t.isCTALinkExternal,s=void 0!==l&&l,m=t.gaTrackingEventArgs,p=t.tertiary,h=void 0!==p&&p,b=t.isSaving,v=void 0!==b&&b,E=Object(d.a)(n,null==m?void 0:m.category),_=function(){var e=o()(r.a.mark((function e(t){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==c?void 0:c(t);case 2:E.confirm(null==m?void 0:m.label,null==m?void 0:m.value);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(g.Button,{className:u()("googlesitekit-subtle-notification__cta",{"googlesitekit-subtle-notification__cta--spinner__running":v}),href:i,onClick:_,target:s?"_blank":"_self",trailingIcon:s?e.createElement(f.a,{width:14,height:14}):void 0,icon:v?e.createElement(g.CircularProgress,{size:14}):void 0,tertiary:h},a)}CTALinkSubtle.propTypes={id:l.a.string,ctaLink:l.a.string,ctaLabel:l.a.string,onCTAClick:l.a.func,isCTALinkExternal:l.a.bool,gaTrackingEventArgs:l.a.shape({label:l.a.string,value:l.a.string}),tertiary:l.a.bool,isSaving:l.a.bool}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n(2);function r(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},r=n.slug,a=void 0===r?"":r,o=n.name,c=void 0===o?"":o,l=n.owner,s=void 0===l?{}:l;if(!a||!c)return e;var u="",d="";return"analytics-4"===a?e.match(/account/i)?u=Object(i.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?u=Object(i.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(u=Object(i.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):"search-console"===a&&(u=Object(i.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),u||(u=Object(i.sprintf)(
/* translators: %s: module name */
Object(i.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),c)),s&&s.login&&(d=Object(i.sprintf)(
/* translators: %s: owner name */
Object(i.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),s.login)),d||(d=Object(i.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(u," ").concat(d)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportError}));var i=n(6),r=n.n(i),a=n(1),o=n.n(a),c=n(14),l=n(0),s=n(2),u=n(3),d=n(19),g=n(35),f=n(169),m=n(84),p=n(54),h=n(95),b=n(135),v=n(34);function E(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?E(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):E(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportError(t){var n,i=t.moduleSlug,r=t.error,a=Object(v.a)(),o=Object(u.useSelect)((function(e){return e(d.a).getModule(i)})),E=Array.isArray(r)?r:[r],O=function(e){return Object(g.e)(e)?a?(n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Access lost to %s","google-site-kit"),null==o?void 0:o.name),Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("The administrator sharing this module with you has lost access to the %s service, so you won’t be able to see stats from it on the Site Kit dashboard. You can contact them or another administrator to restore access.","google-site-kit"),null==o?void 0:o.name)):(n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Insufficient permissions in %s","google-site-kit"),null==o?void 0:o.name),Object(f.a)(e.message,o)):Object(g.b)(e)},k=Object(c.uniqWith)(E.map((function(e){var t;return _(_({},e),{},{message:O(e),reconnectURL:null===(t=e.data)||void 0===t?void 0:t.reconnectURL})})),(function(e,t){return e.message===t.message&&e.reconnectURL===t.reconnectURL})),y=E.some((function(e){return Object(g.e)(e)}));y||1!==k.length?!y&&k.length>1&&(n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Data errors in %s","google-site-kit"),null==o?void 0:o.name)):n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Data error in %s","google-site-kit"),null==o?void 0:o.name);var j=e.createElement(l.Fragment,null,k.map((function(t){var n,i=null==r||null===(n=r.data)||void 0===n?void 0:n.reconnectURL;return i?e.createElement(p.a,{key:t.message,message:t.message,reconnectURL:i}):e.createElement("p",{key:t.message},m.a.sanitize(t.message,{ALLOWED_TAGS:[]}))})));return e.createElement(h.a,{title:n,description:j,error:!0},e.createElement(b.a,{moduleSlug:i,error:r}))}ReportError.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired}}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GenericErrorHandlerActions}));var i=n(1),r=n.n(i),a=n(2),o=n(20),c=n(194);function GenericErrorHandlerActions(t){var n=t.message,i=t.componentStack;return e.createElement("div",{className:"googlesitekit-generic-error-handler-actions"},e.createElement(c.a,{message:n,componentStack:i}),e.createElement(o.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0},Object(a.__)("Report this problem","google-site-kit")))}GenericErrorHandlerActions.propTypes={message:r.a.string,componentStack:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n(22),r=function(e){return i.f.includes(e)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportZero}));var i=n(1),r=n.n(i),a=n(2),o=n(3),c=n(19),l=n(95);function ReportZero(t){var n=t.moduleSlug,i=Object(o.useSelect)((function(e){return e(c.a).getModule(n)}));return e.createElement(l.a,{title:Object(a.sprintf)(
/* translators: %s: Module name */
Object(a.__)("%s Gathering Data","google-site-kit"),null==i?void 0:i.name),description:Object(a.sprintf)(
/* translators: %s: Module name */
Object(a.__)("%s data is not yet available, please check back later","google-site-kit"),null==i?void 0:i.name)})}ReportZero.propTypes={moduleSlug:r.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(216);n.d(t,"b",(function(){return i.a}));var r=n(221);n.d(t,"a",(function(){return r.a}))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileLoading}));var i=n(44);function AudienceTileLoading(){return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-loading"},e.createElement(i.a,{width:"100%",height:"20px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}))}}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActionsCTALinkDismiss}));var i=n(1),r=n.n(i),a=n(2),o=n(0),c=n(3),l=n(32),s=n(92),u=n(187);function ActionsCTALinkDismiss(t){var n=t.id,i=t.className,r=void 0===i?"googlesitekit-publisher-win__actions":i,d=t.ctaLink,g=t.ctaLabel,f=t.ctaDisabled,m=void 0!==f&&f,p=t.onCTAClick,h=t.ctaDismissOptions,b=t.isSaving,v=void 0!==b&&b,E=t.onDismiss,_=void 0===E?function(){}:E,O=t.dismissLabel,k=void 0===O?Object(a.__)("OK, Got it!","google-site-kit"):O,y=t.dismissOnCTAClick,j=void 0===y||y,S=t.dismissExpires,w=void 0===S?0:S,C=t.dismissOptions,A=void 0===C?{}:C,N=t.gaTrackingEventArgs,T=void 0===N?{}:N,x=Object(c.useSelect)((function(e){return!!d&&e(l.a).isNavigatingTo(d)}));return e.createElement(o.Fragment,null,e.createElement("div",{className:r},e.createElement(u.a,{id:n,ctaLink:d,ctaLabel:g,onCTAClick:p,dismissOnCTAClick:j,dismissExpires:w,dismissOptions:h,gaTrackingEventArgs:T,isSaving:v,isDisabled:m}),e.createElement(s.a,{id:n,primary:!1,dismissLabel:k,dismissExpires:w,disabled:x,onDismiss:_,dismissOptions:A,gaTrackingEventArgs:T})))}ActionsCTALinkDismiss.propTypes={id:r.a.string,className:r.a.string,ctaDisabled:r.a.bool,ctaLink:r.a.string,ctaLabel:r.a.string,onCTAClick:r.a.func,isSaving:r.a.bool,onDismiss:r.a.func,ctaDismissOptions:r.a.object,dismissLabel:r.a.string,dismissOnCTAClick:r.a.bool,dismissExpires:r.a.number,dismissOptions:r.a.object,gaTrackingEventArgs:r.a.object}}).call(this,n(4))},,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LoadingWrapper}));var i=n(6),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(44);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function LoadingWrapper(t){var n=t.loading,i=t.children,r=o()(t,["loading","children"]);return n?e.createElement(s.a,r):i}LoadingWrapper.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({loading:l.a.bool,children:l.a.node},s.a.propTypes)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeBadge}));var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(9);function ChangeBadge(t){var n=t.previousValue,i=t.currentValue,r=t.isAbsolute?i-n:Object(c.g)(n,i),a=r<0,l=0===r;return null===r?null:e.createElement("div",{className:o()("googlesitekit-change-badge",{"googlesitekit-change-badge--negative":a,"googlesitekit-change-badge--zero":l})},Object(c.B)(r,{style:"percent",signDisplay:"exceptZero",maximumFractionDigits:1}))}ChangeBadge.propTypes={isAbsolute:r.a.bool,previousValue:r.a.number.isRequired,currentValue:r.a.number.isRequired}}).call(this,n(4))},,function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M0 0h2v7H0zm0 10h2v2H0z",fill:"currentColor",fillRule:"evenodd"});t.a=function SvgWarningIcon(e){return i.createElement("svg",r({viewBox:"0 0 2 12"},e),a)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALink}));var i=n(5),r=n.n(i),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(15),u=n.n(s),d=n(1),g=n.n(d),f=n(206),m=n(0),p=n(3),h=n(41),b=n(32),v=n(13),E=n(73),_=n(10);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function CTALink(t){var n=t.id,i=t.ctaLink,a=t.ctaLabel,o=t.onCTAClick,c=t.isSaving,s=t.dismissOnCTAClick,d=void 0!==s&&s,g=t.dismissExpires,O=void 0===g?0:g,y=t.dismissOptions,j=void 0===y?{}:y,S=t.gaTrackingEventArgs,w=t.isDisabled,C=void 0!==w&&w,A=Object(m.useState)(!1),N=u()(A,2),T=N[0],x=N[1],D=Object(f.a)(),R=Object(E.a)(n,null==S?void 0:S.category),M=Object(p.useSelect)((function(e){return!!i&&e(b.a).isNavigatingTo(i)})),I=Object(p.useDispatch)(v.c),P=I.clearError,L=I.receiveError,B=Object(p.useDispatch)(h.a).dismissNotification,z=Object(p.useDispatch)(b.a).navigateTo,F=function(){var e=l()(r.a.mark((function e(t){var a,c,l;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return P("notificationAction",[n]),t.persist(),!t.defaultPrevented&&i&&t.preventDefault(),x(!0),e.next=6,null==o?void 0:o(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:if(a=e.t0,c=a.error,D()&&x(!1),!c){e.next=15;break}return L(c,"notificationAction",[n]),e.abrupt("return");case 15:return l=[R.confirm()],d&&l.push(B(n,k(k({},j),{},{expiresInSeconds:O}))),e.next=19,Promise.all(l);case 19:i&&z(i);case 20:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(_.SpinnerButton,{className:"googlesitekit-notification__cta",href:i,onClick:F,disabled:T||M||C,isSaving:T||M||c},a)}CTALink.propTypes={id:g.a.string,ctaLink:g.a.string,ctaLabel:g.a.string,onCTAClick:g.a.func,dismissOnCTAClick:g.a.bool,dismissExpires:g.a.number,dismissOptions:g.a.object,isDisabled:g.a.bool}}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){function Title(t){var n=t.title;return e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n)}n.d(t,"a",(function(){return Title}))}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Notification}));var i=n(15),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=n(282),s=n(161),u=n(73);function Notification(t){var n=t.id,i=t.className,a=t.gaTrackingEventArgs,o=t.children,d=t.onView,g=Object(c.useRef)(),f=Object(s.a)(n),m=Object(u.a)(n,null==a?void 0:a.category),p=Object(c.useState)(!1),h=r()(p,2),b=h[0],v=h[1];return Object(c.useEffect)((function(){!b&&f&&(m.view(null==a?void 0:a.label,null==a?void 0:a.value),null==d||d(),v(!0))}),[f,m,b,a,d]),e.createElement("section",{id:n,ref:g,className:i},o,!f&&e.createElement(l.a,{id:n,observeRef:g,threshold:.5}))}Notification.propTypes={id:o.a.string,className:o.a.string,gaTrackingEventArgs:o.a.shape({category:o.a.string,label:o.a.string,value:o.a.string}),children:o.a.node,onView:o.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(51),r=n.n(i),a=n(53),o=n.n(a),c=n(68),l=n.n(c),s=n(69),u=n.n(s),d=n(49),g=n.n(d),f=n(1),m=n.n(f),p=n(11),h=n.n(p),b=n(0),v=n(329),E=n(330);function _(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=g()(e);if(t){var r=g()(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return u()(this,n)}}var O=function(t){l()(Layout,t);var n=_(Layout);function Layout(){return r()(this,Layout),n.apply(this,arguments)}return o()(Layout,[{key:"render",value:function(){var t=this.props,n=t.header,i=t.footer,r=t.children,a=t.title,o=t.badge,c=t.headerCTALabel,l=t.headerCTALink,s=t.footerCTALabel,u=t.footerCTALink,d=t.footerContent,g=t.className,f=t.fill,m=t.relative,p=t.rounded,b=void 0!==p&&p,_=t.transparent,O=void 0!==_&&_;return e.createElement("div",{className:h()("googlesitekit-layout",g,{"googlesitekit-layout--fill":f,"googlesitekit-layout--relative":m,"googlesitekit-layout--rounded":b,"googlesitekit-layout--transparent":O})},n&&e.createElement(v.a,{title:a,badge:o,ctaLabel:c,ctaLink:l}),r,i&&e.createElement(E.a,{ctaLabel:s,ctaLink:u,footerContent:d}))}}]),Layout}(b.Component);O.propTypes={header:m.a.bool,footer:m.a.bool,children:m.a.node.isRequired,title:m.a.string,badge:m.a.node,headerCTALabel:m.a.string,headerCTALink:m.a.string,footerCTALabel:m.a.string,footerCTALink:m.a.string,footerContent:m.a.node,className:m.a.string,fill:m.a.bool,relative:m.a.bool,rounded:m.a.bool,transparent:m.a.bool},O.defaultProps={header:!1,footer:!1,title:"",badge:null,headerCTALabel:"",headerCTALink:"",footerCTALabel:"",footerCTALink:"",footerContent:null,className:"",fill:!1,relative:!1},t.a=O}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){var i=n(15),r=n.n(i),a=n(195),o=n.n(a),c=n(1),l=n.n(c),s=n(0),u=n(2),d=n(266),g=n(423),f=n(424),m=n(10);function ReportErrorButton(t){var n=t.message,i=t.componentStack,a=Object(s.useState)(!1),c=r()(a,2),l=c[0],p=c[1];return e.createElement(m.Button,{"aria-label":l?Object(u.__)("Error message copied to clipboard. Click to copy the error message again.","google-site-kit"):void 0,onClick:function(){o()("`".concat(n,"\n").concat(i,"`")),p(!0)},trailingIcon:e.createElement(d.a,{className:"mdc-button__icon",icon:l?g.a:f.a})},l?Object(u.__)("Copied to clipboard","google-site-kit"):Object(u.__)("Copy error contents","google-site-kit"))}ReportErrorButton.propTypes={message:l.a.string,componentStack:l.a.string},t.a=ReportErrorButton}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationError}));var i=n(17),r=n(222),a=n(189);function NotificationError(t){var n=t.title,o=t.description,c=t.actions;return e.createElement(i.e,null,e.createElement(i.k,null,e.createElement(i.a,{smSize:3,mdSize:7,lgSize:11,className:"googlesitekit-publisher-win__content"},e.createElement(a.a,{title:n}),o,c),e.createElement(r.a,{type:"win-error"})))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerNotification}));var i=n(21),r=n.n(i),a=n(6),o=n.n(a),c=n(5),l=n.n(c),s=n(16),u=n.n(s),d=n(15),g=n.n(d),f=n(1),m=n.n(f),p=n(11),h=n.n(p),b=n(206),v=n(240),E=n(81),_=n(0),O=n(106),k=n(3),y=n(17),j=n(93),S=n(37),w=n(24),C=n(211),A=n(213),N=n(212),T=n(226),x=n(227),D=n(86),R=n(136),M=n(130),I=n(32),P=n(228),L=n(79);function BannerNotification(t){var n,i=t.badgeLabel,a=t.children,c=t.className,s=void 0===c?"":c,d=t.ctaLabel,f=t.ctaLink,m=t.ctaTarget,p=t.description,B=t.dismiss,z=t.dismissExpires,F=void 0===z?0:z,V=t.format,W=void 0===V?"":V,H=t.id,U=t.isDismissible,q=void 0===U||U,G=t.learnMoreDescription,K=t.learnMoreLabel,Y=t.learnMoreURL,X=t.learnMoreTarget,$=void 0===X?D.a.EXTERNAL:X,Z=t.logo,Q=t.module,J=t.moduleName,ee=t.onCTAClick,te=t.onView,ne=t.onDismiss,ie=t.onLearnMoreClick,re=t.showOnce,ae=void 0!==re&&re,oe=t.SmallImageSVG,ce=t.title,le=t.type,se=t.WinImageSVG,ue=t.showSmallWinImage,de=void 0===ue||ue,ge=t.smallWinImageSVGWidth,fe=void 0===ge?75:ge,me=t.smallWinImageSVGHeight,pe=void 0===me?75:me,he=t.mediumWinImageSVGWidth,be=void 0===he?105:he,ve=t.mediumWinImageSVGHeight,Ee=void 0===ve?105:ve,_e=t.rounded,Oe=void 0!==_e&&_e,ke=t.footer,ye=t.secondaryPane,je=t.ctaComponent,Se=Object(_.useState)(!1),we=g()(Se,2),Ce=we[0],Ae=we[1],Ne=Object(_.useState)(!1),Te=g()(Ne,2),xe=Te[0],De=Te[1],Re="notification::dismissed::".concat(H),Me=function(){return Object(S.f)(Re,new Date,{ttl:null})},Ie=Object(L.a)(),Pe=Object(w.e)(),Le=Object(b.a)(),Be=Object(_.useState)(!1),ze=g()(Be,2),Fe=ze[0],Ve=ze[1],We=Object(_.useRef)(),He=Object(v.a)(We,{rootMargin:"".concat(-Object(M.a)(Object(j.c)(Pe)),"px 0px 0px 0px"),threshold:0});Object(_.useEffect)((function(){!Fe&&(null==He?void 0:He.isIntersecting)&&("function"==typeof te&&te(),Ve(!0))}),[H,te,Fe,He]);var Ue=Ie>=600;Object(E.a)(u()(l.a.mark((function e(){var t,n;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(F>0)){e.next=3;break}return e.next=3,Ze();case 3:if(!q){e.next=9;break}return e.next=6,Object(S.d)(Re);case 6:t=e.sent,n=t.cacheHit,De(n);case 9:if(!ae){e.next=12;break}return e.next=12,Me();case 12:case"end":return e.stop()}}),e)}))));var qe=function(){var e=u()(l.a.mark((function e(t){return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),t.preventDefault(),!ne){e.next=5;break}return e.next=5,ne(t);case 5:Ke();case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Ge=Object(O.a)(f)&&"_blank"!==m,Ke=function(){return Ge||Ae(!0),new Promise((function(e){setTimeout(u()(l.a.mark((function t(){var n;return l.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Me();case 2:Le()&&De(!0),n=new Event("notificationDismissed"),document.dispatchEvent(n),e();case 6:case"end":return t.stop()}}),t)}))),350)}))},Ye=Object(k.useSelect)((function(e){return!!f&&e(I.a).isNavigatingTo(f)})),Xe=Object(k.useDispatch)(I.a).navigateTo,$e=function(){var e=u()(l.a.mark((function e(t){var n,i,r;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),Ge&&!t.defaultPrevented&&t.preventDefault(),n=!0,!ee){e.next=12;break}return e.next=6,ee(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:i=e.t0,r=i.dismissOnCTAClick,n=void 0===r||r;case 12:if(!q||!n){e.next=15;break}return e.next=15,Ke();case 15:Ge&&Xe(f);case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Ze=function(){var e=u()(l.a.mark((function e(){var t,n,i;return l.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(S.d)(Re);case 2:if(t=e.sent,!(n=t.value)){e.next=10;break}if((i=new Date(n)).setSeconds(i.getSeconds()+parseInt(F,10)),!(i<new Date)){e.next=10;break}return e.next=10,Object(S.c)(Re);case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();if(!Ye&&q&&(void 0===xe||xe))return null;var Qe=!Ye&&Ce?"is-closed":"is-open",Je=Object(R.d)(W),et=Object(R.c)(W),tt=Object(R.a)(W),nt=Object(R.b)({format:W,hasErrorOrWarning:"win-error"===le||"win-warning"===le,hasSmallImageSVG:!!oe,hasWinImageSVG:!!se});return e.createElement(C.a,{id:H,className:h()(s,(n={},o()(n,"googlesitekit-publisher-win--".concat(W),W),o()(n,"googlesitekit-publisher-win--".concat(le),le),o()(n,"googlesitekit-publisher-win--".concat(Qe),Qe),o()(n,"googlesitekit-publisher-win--rounded",Oe),n)),secondaryPane:ye,ref:We},Z&&e.createElement(x.a,{module:Q,moduleName:J}),oe&&e.createElement(y.a,{size:1,className:"googlesitekit-publisher-win__small-media"},e.createElement(oe,null)),e.createElement(y.a,r()({},nt,tt,{className:"googlesitekit-publisher-win__content"}),e.createElement(A.a,{title:ce,badgeLabel:i,smallWinImageSVGHeight:pe,smallWinImageSVGWidth:fe,winImageFormat:W,WinImageSVG:!Ue&&de?se:void 0}),e.createElement(P.a,{description:p,learnMoreURL:Y,learnMoreLabel:K,learnMoreTarget:$,learnMoreDescription:G,onLearnMoreClick:ie}),a,e.createElement(N.a,{ctaLink:f,ctaLabel:d,ctaComponent:je,ctaTarget:m,ctaCallback:$e,dismissLabel:q?B:void 0,dismissCallback:qe}),ke&&e.createElement("div",{className:"googlesitekit-publisher-win__footer"},ke)),se&&(Ue||!de)&&e.createElement(y.a,r()({},Je,et,{alignBottom:"larger"===W,className:"googlesitekit-publisher-win__image"}),e.createElement("div",{className:"googlesitekit-publisher-win__image-".concat(W)},e.createElement(se,{style:{maxWidth:be,maxHeight:Ee}}))),e.createElement(T.a,{type:le}))}BannerNotification.propTypes={id:m.a.string.isRequired,className:m.a.string,title:m.a.string.isRequired,description:m.a.node,learnMoreURL:m.a.string,learnMoreDescription:m.a.string,learnMoreLabel:m.a.string,learnMoreTarget:m.a.oneOf(Object.values(D.a)),WinImageSVG:m.a.elementType,SmallImageSVG:m.a.elementType,format:m.a.string,ctaLink:m.a.string,ctaLabel:m.a.string,type:m.a.string,dismiss:m.a.string,isDismissible:m.a.bool,logo:m.a.bool,module:m.a.string,moduleName:m.a.string,dismissExpires:m.a.number,showOnce:m.a.bool,onCTAClick:m.a.func,onView:m.a.func,onDismiss:m.a.func,onLearnMoreClick:m.a.func,badgeLabel:m.a.string,rounded:m.a.bool,footer:m.a.node,secondaryPane:m.a.node,showSmallWinImage:m.a.bool,smallWinImageSVGWidth:m.a.number,smallWinImageSVGHeight:m.a.number,mediumWinImageSVGWidth:m.a.number,mediumWinImageSVGHeight:m.a.number}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleIcon}));var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(3),u=n(19);function ModuleIcon(t){var n=t.slug,i=t.size,a=o()(t,["slug","size"]),c=Object(s.useSelect)((function(e){return e(u.a).getModuleIcon(n)}));return c?e.createElement(c,r()({width:i,height:i},a)):null}ModuleIcon.propTypes={slug:l.a.string.isRequired,size:l.a.number},ModuleIcon.defaultProps={size:33}}).call(this,n(4))},,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeSingleRow}));var i=n(1),r=n.n(i),a=n(0);function SettingsNoticeSingleRow(t){var n=t.notice,i=t.LearnMore,r=t.CTA;return e.createElement(a.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),i&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(i,null)),r&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(r,null)))}SettingsNoticeSingleRow.propTypes={notice:r.a.node.isRequired,LearnMore:r.a.elementType,CTA:r.a.elementType}}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){var i=n(14),r=n(1),a=n.n(r),o=n(0),c=n(50),l=n(3),s=n(35),u=n(23),d=n(165),g=n(457),f=n(113),m=n(9),p=n(18),h=Object(f.a)(g.a);function AudienceSegmentationErrorWidget(t){var n=t.Widget,r=t.errors,a=t.onRetry,c=t.showRetryButton,g=Object(p.a)(),f=Object(l.useDispatch)(u.b).setValue,b=r?Object(i.castArray)(r):[],v=b.some(s.e);return Object(o.useEffect)((function(){f(d.b,!0)}),[f]),e.createElement(h,{Widget:n,errors:b,onRetry:function(){Object(m.I)("".concat(g,"_audiences-all-tiles"),"data_loading_error_retry").finally((function(){f(d.b,!1),null==a||a()}))},onRequestAccess:function(){Object(m.I)("".concat(g,"_audiences-all-tiles"),"insufficient_permissions_error_request_access")},showRetryButton:c,onInView:function(){var e=v?"insufficient_permissions_error":"data_loading_error";Object(m.I)("".concat(g,"_audiences-all-tiles"),e)}})}AudienceSegmentationErrorWidget.propTypes={Widget:a.a.elementType.isRequired,errors:a.a.oneOfType([a.a.object,a.a.arrayOf(a.a.object)]).isRequired,onRetry:a.a.func,showRetryButton:a.a.bool},t.a=Object(c.a)({moduleName:"analytics-4"})(AudienceSegmentationErrorWidget)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OverlayNotification}));var i=n(591),r=n(11),a=n.n(r),o=n(1),c=n.n(o),l=n(0),s=n(3),u=n(23),d=n(24);function OverlayNotification(t){var n=t.className,r=t.children,o=t.GraphicDesktop,c=t.GraphicMobile,g=t.notificationID,f=t.onShow,m=t.shouldShowNotification,p=Object(d.e)(),h=Object(s.useSelect)((function(e){return e(u.b).isShowingOverlayNotification(g)})),b=Object(s.useDispatch)(u.b).setOverlayNotificationToShow;if(Object(l.useEffect)((function(){m&&!h&&(b(g),null==f||f())}),[h,g,f,b,m]),!m||!h)return null;var v=a()("googlesitekit-overlay-notification",n);return p===d.b?e.createElement("div",{className:v},r,c&&e.createElement(c,null)):e.createElement(i.a,{direction:"up",in:h},e.createElement("div",{className:v},o&&e.createElement(o,null),r))}OverlayNotification.propTypes={className:c.a.string,children:c.a.node,GraphicDesktop:c.a.elementType,GraphicMobile:c.a.elementType,onShow:c.a.func,notificationID:c.a.string.isRequired,shouldShowNotification:c.a.bool}}).call(this,n(4))},,,function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5 22c-1.522 0-2.952-.284-4.29-.852a11.303 11.303 0 01-3.493-2.366 11.303 11.303 0 01-2.365-3.492A10.86 10.86 0 01.5 11c0-1.522.284-2.952.853-4.29a11.302 11.302 0 012.364-3.493A10.92 10.92 0 017.21.88 10.567 10.567 0 0111.5 0c1.522 0 2.952.293 4.29.88a10.92 10.92 0 013.492 2.337c.99.99 1.77 2.155 2.338 3.493.587 1.338.88 2.768.88 4.29 0 1.522-.293 2.952-.88 4.29a10.92 10.92 0 01-2.338 3.492c-.99.99-2.154 1.779-3.492 2.366A10.86 10.86 0 0111.5 22zm0-14.3c.312 0 .569-.1.77-.303.22-.22.33-.485.33-.797a.999.999 0 00-.33-.77.999.999 0 00-.77-.33c-.311 0-.577.11-.797.33a1.043 1.043 0 00-.303.77c0 .312.101.578.303.798.22.201.486.302.797.302zm-1.1 8.8V9.9h2.2v6.6h-2.2z",fill:"currentColor"});t.a=function SvgInfoCircle(e){return i.createElement("svg",r({viewBox:"0 0 23 22",fill:"none"},e),a)}},,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsNoticeMultiRow}));var i=n(1),r=n.n(i),a=n(0);function SettingsNoticeMultiRow(t){var n=t.notice,i=t.LearnMore,r=t.CTA,o=t.children;return e.createElement(a.Fragment,null,e.createElement("div",{className:"googlesitekit-settings-notice__text"},n),e.createElement("div",{className:"googlesitekit-settings-notice__inner-row"},e.createElement("div",{className:"googlesitekit-settings-notice__children-container"},o),i&&e.createElement("div",{className:"googlesitekit-settings-notice__learn-more"},e.createElement(i,null)),r&&e.createElement("div",{className:"googlesitekit-settings-notice__cta"},e.createElement(r,null))))}SettingsNoticeMultiRow.propTypes={children:r.a.node.isRequired,notice:r.a.node.isRequired,LearnMore:r.a.elementType,CTA:r.a.elementType}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(0),l=n(17),s=Object(c.forwardRef)((function(t,n){var i=t.id,r=t.className,a=t.children,s=t.secondaryPane;return e.createElement("section",{id:i,className:o()(r,"googlesitekit-publisher-win"),ref:n},e.createElement(l.e,null,e.createElement(l.k,null,a)),s&&e.createElement(c.Fragment,null,e.createElement("div",{className:"googlesitekit-publisher-win__secondary-pane-divider"}),e.createElement(l.e,{className:"googlesitekit-publisher-win__secondary-pane"},e.createElement(l.k,null,e.createElement(l.a,{className:"googlesitekit-publisher-win__secondary-pane",size:12},s)))))}));s.displayName="Banner",s.propTypes={id:r.a.string,className:r.a.string,secondaryPane:r.a.node},t.a=s}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerActions}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(1),u=n.n(s),d=n(206),g=n(0),f=n(3),m=n(10),p=n(32);function BannerActions(t){var n=t.ctaLink,i=t.ctaLabel,a=t.ctaComponent,c=t.ctaTarget,s=t.ctaCallback,u=t.dismissLabel,h=t.dismissCallback,b=Object(g.useState)(!1),v=l()(b,2),E=v[0],_=v[1],O=Object(d.a)(),k=Object(f.useSelect)((function(e){return!!n&&e(p.a).isNavigatingTo(n)})),y=function(){var e=o()(r.a.mark((function e(){var t,n,i,a=arguments;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(_(!0),t=a.length,n=new Array(t),i=0;i<t;i++)n[i]=a[i];return e.next=4,null==s?void 0:s.apply(void 0,n);case 4:O()&&_(!1);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return n||u||a?e.createElement("div",{className:"googlesitekit-publisher-win__actions"},a,i&&e.createElement(m.SpinnerButton,{className:"googlesitekit-notification__cta",href:n,target:c,onClick:y,disabled:E||k,isSaving:E||k},i),u&&e.createElement(m.Button,{tertiary:n||a,onClick:h,disabled:E||k},u)):null}BannerActions.propTypes={ctaLink:u.a.string,ctaLabel:u.a.string,ctaComponent:u.a.element,ctaTarget:u.a.string,ctaCallback:u.a.func,dismissLabel:u.a.string,dismissCallback:u.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerTitle}));var i=n(6),r=n.n(i),a=n(1),o=n.n(a),c=n(11),l=n.n(c),s=n(77);function BannerTitle(t){var n=t.title,i=t.badgeLabel,a=t.WinImageSVG,o=t.winImageFormat,c=void 0===o?"":o,u=t.smallWinImageSVGWidth,d=void 0===u?75:u,g=t.smallWinImageSVGHeight,f=void 0===g?75:g;return n?e.createElement("div",{className:"googlesitekit-publisher-win__title-image-wrapper"},e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n,i&&e.createElement(s.a,{label:i})),a&&e.createElement("div",{className:l()(r()({},"googlesitekit-publisher-win__image-".concat(c),c))},e.createElement(a,{width:d,height:f}))):null}BannerTitle.propTypes={title:o.a.string,badgeLabel:o.a.string,WinImageSVG:o.a.elementType,winImageFormat:o.a.string,smallWinImageSVGWidth:o.a.number,smallWinImageSVGHeight:o.a.number}}).call(this,n(4))},,,function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var i=n(5),r=n.n(i),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(0),u=n(3),d=n(13),g=n(23);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e){var t=Object(u.useDispatch)(g.b).setValue,n=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.2")})),i=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.4")}));return Object(s.useCallback)(l()(r.a.mark((function a(){var o,c,l,s;return r.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(o=document.querySelector("#adminmenu").offsetHeight>0){r.next=7;break}if(!(c=document.getElementById("wp-admin-bar-menu-toggle"))){r.next=7;break}return c.firstChild.click(),r.next=7,new Promise((function(e){setTimeout(e,0)}));case 7:"#adminmenu [href*='page=googlesitekit-dashboard']",(l=!!document.querySelector("".concat("#adminmenu [href*='page=googlesitekit-dashboard']","[aria-haspopup=true]")))&&document.querySelector("#adminmenu [href*='page=googlesitekit-dashboard']").click(),n&&!i&&(s=document.hasFocus,document.hasFocus=function(){return document.hasFocus=s,!1}),t("admin-menu-tooltip",m({isTooltipVisible:!0,rehideAdminMenu:!o,rehideAdminSubMenu:l},e));case 12:case"end":return r.stop()}}),a)}))),[n,i,t,e])}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WarningNotice}));var i=n(11),r=n.n(i),a=n(1),o=n.n(a);function WarningNotice(t){var n=t.children,i=t.className;return e.createElement("div",{className:r()("googlesitekit-warning-notice",i)},n)}WarningNotice.propTypes={children:o.a.node.isRequired,className:o.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OptIn}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),g=n(38),f=n(2),m=n(3),p=n(10),h=n(7),b=n(36),v=n(20),E=n(18);function OptIn(t){var n=t.id,i=void 0===n?"googlesitekit-opt-in":n,a=t.name,c=void 0===a?"optIn":a,l=t.className,s=t.trackEventCategory,_=t.alignLeftCheckbox,O=void 0!==_&&_,k=Object(m.useSelect)((function(e){return e(h.a).isTrackingEnabled()})),y=Object(m.useSelect)((function(e){return e(h.a).isSavingTrackingEnabled()})),j=Object(m.useSelect)((function(e){return e(h.a).getErrorForAction("setTrackingEnabled",[!k])})),S=Object(m.useDispatch)(h.a).setTrackingEnabled,w=Object(E.a)(),C=Object(d.useCallback)(function(){var e=o()(r.a.mark((function e(t){var n,i;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S(!!t.target.checked);case 2:n=e.sent,i=n.response,n.error||(Object(b.a)(i.enabled),i.enabled&&Object(b.b)(s||w,"tracking_optin"));case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[S,s,w]);return e.createElement("div",{className:u()("googlesitekit-opt-in",l)},e.createElement(p.Checkbox,{id:i,name:c,value:"1",checked:k,disabled:y,onChange:C,loading:void 0===k,alignLeft:O},Object(g.a)(Object(f.__)("<span>Help us improve Site Kit by sharing anonymous usage data.</span> <span>All collected data is treated in accordance with the <a>Google Privacy Policy.</a></span>","google-site-kit"),{a:e.createElement(v.a,{key:"link",href:"https://policies.google.com/privacy",external:!0}),span:e.createElement("span",null)})),(null==j?void 0:j.message)&&e.createElement("div",{className:"googlesitekit-error-text"},null==j?void 0:j.message))}OptIn.propTypes={id:l.a.string,name:l.a.string,className:l.a.string,trackEventCategory:l.a.string,alignLeftCheckbox:l.a.bool}}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Item}));var i=n(1),r=n.n(i);function Item(t){var n=t.icon,i=t.label;return e.createElement("div",{className:"googlesitekit-user-menu__item"},e.createElement("div",{className:"googlesitekit-user-menu__item-icon"},n),e.createElement("span",{className:"googlesitekit-user-menu__item-label"},i))}Item.propTypes={icon:r.a.node,label:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdminMenuTooltip}));var i=n(0),r=n(3),a=n(223),o=n(23),c=n(9),l=n(18);function AdminMenuTooltip(){var t=Object(l.a)(),n=Object(r.useDispatch)(o.b).setValue,s=Object(r.useSelect)((function(e){return e(o.b).getValue("admin-menu-tooltip")||{isTooltipVisible:!1}})),u=s.isTooltipVisible,d=void 0!==u&&u,g=s.rehideAdminMenu,f=void 0!==g&&g,m=s.rehideAdminSubMenu,p=void 0!==m&&m,h=s.tooltipSlug,b=s.title,v=s.content,E=s.dismissLabel,_=Object(i.useCallback)((function(){var e;f&&(document.querySelector("#adminmenu").offsetHeight>0&&(null===(e=document.getElementById("wp-admin-bar-menu-toggle"))||void 0===e||e.click()));p&&document.querySelector("body").click(),h&&Object(c.I)("".concat(t,"_").concat(h),"tooltip_dismiss"),n("admin-menu-tooltip",void 0)}),[f,p,n,h,t]);return d?e.createElement(a.a,{target:'#adminmenu [href*="page=googlesitekit-settings"]',slug:"ga4-activation-banner-admin-menu-tooltip",title:b,content:v,dismissLabel:E,onView:function(){Object(c.I)("".concat(t,"_").concat(h),"tooltip_view")},onDismiss:_}):null}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var i=n(1),r=n.n(i),a=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var i="win-warning"===n?e.createElement(a.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},i))}BannerIcon.propTypes={type:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return JoyrideTooltip}));var r=n(6),a=n.n(r),o=n(15),c=n.n(o),l=n(1),s=n(30),u=n(421),d=n(0),g=n(107),f=n(72),m=n(90);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function JoyrideTooltip(t){var n=t.title,r=t.content,a=t.dismissLabel,o=t.target,l=t.cta,p=void 0!==l&&l,b=t.className,v=t.styles,E=void 0===v?{}:v,_=t.slug,O=void 0===_?"":_,k=t.onDismiss,y=void 0===k?function(){}:k,j=t.onView,S=void 0===j?function(){}:j,w=t.onTourStart,C=void 0===w?function(){}:w,A=t.onTourEnd,N=void 0===A?function(){}:A,T=function(){return!!e.document.querySelector(o)},x=Object(d.useState)(T),D=c()(x,2),R=D[0],M=D[1];if(Object(u.a)((function(){T()&&M(!0)}),R?null:250),Object(d.useEffect)((function(){if(R&&e.ResizeObserver){var t=e.document.querySelector(o),n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}),[o,R]),!R)return null;var I=[{title:n,target:o,content:r,disableBeacon:!0,isFixed:!0,placement:"auto",cta:p,className:b}],P={close:a,last:a};return i.createElement(f.a,{slug:O},i.createElement(s.e,{callback:function(t){switch(t.type){case s.b.TOUR_START:C(),e.document.body.classList.add("googlesitekit-showing-tooltip");break;case s.b.TOUR_END:N(),e.document.body.classList.remove("googlesitekit-showing-tooltip");break;case s.b.STEP_AFTER:y();break;case s.b.TOOLTIP:S()}},disableOverlay:!0,disableScrolling:!0,spotlightPadding:0,floaterProps:m.b,locale:P,steps:I,styles:h(h(h({},m.c),E),{},{options:h(h({},m.c.options),null==E?void 0:E.options),spotlight:h(h({},m.c.spotlight),null==E?void 0:E.spotlight)}),tooltipComponent:g.a,run:!0}))}JoyrideTooltip.propTypes={title:l.PropTypes.node,content:l.PropTypes.string,dismissLabel:l.PropTypes.string,target:l.PropTypes.string.isRequired,onDismiss:l.PropTypes.func,onShow:l.PropTypes.func,className:l.PropTypes.string,styles:l.PropTypes.object,slug:l.PropTypes.string,onView:l.PropTypes.func}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Root}));var i=n(15),r=n.n(i),a=n(1),o=n.n(a),c=n(432),l=n(534),s=n(0),u=n(3),d=n.n(u),g=n(225),f=n(229),m=n(57),p=n(230),h=n(232),b=n(233),v=n(61),E=n(160),_=n(173);function Root(t){var n=t.children,i=t.registry,a=t.viewContext,o=void 0===a?null:a,d=c.a,O=Object(s.useState)({key:"Root",value:!0}),k=r()(O,1)[0];return e.createElement(s.StrictMode,null,e.createElement(E.a,{value:k},e.createElement(u.RegistryProvider,{value:i},e.createElement(f.a,{value:m.a},e.createElement(v.a,{value:o},e.createElement(l.a,{theme:d()},e.createElement(g.a,null,e.createElement(h.a,null,n,o&&e.createElement(b.a,null)),Object(_.a)(o)&&e.createElement(p.a,null))))))))}Root.propTypes={children:o.a.node,registry:o.a.object,viewContext:o.a.string.isRequired},Root.defaultProps={registry:d.a}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,i){var r=n(51),a=n.n(r),o=n(53),c=n.n(o),l=n(68),s=n.n(l),u=n(69),d=n.n(u),g=n(49),f=n.n(g),m=n(1),p=n.n(m),h=n(0),b=n(2),v=n(172),E=n(61),_=n(197),O=n(9);function k(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=f()(e);if(t){var r=f()(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return d()(this,n)}}var y=function(t){s()(ErrorHandler,t);var n=k(ErrorHandler);function ErrorHandler(e){var t;return a()(this,ErrorHandler),(t=n.call(this,e)).state={error:null,info:null,copied:!1},t}return c()(ErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t,info:n}),Object(O.I)("react_error","handle_".concat(this.context||"unknown","_error"),"".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,r=t.info;return n?i.createElement(_.a,{id:"googlesitekit-error",className:"googlesitekit-error-handler",title:Object(b.__)("Site Kit encountered an error","google-site-kit"),description:i.createElement(v.a,{message:n.message,componentStack:r.componentStack}),isDismissible:!1,format:"small",type:"win-error"},i.createElement("pre",{className:"googlesitekit-overflow-auto"},n.message,r.componentStack)):e}}]),ErrorHandler}(h.Component);y.contextType=E.b,y.propTypes={children:p.a.node.isRequired},t.a=y}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var i=n(1),r=n.n(i),a=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var i="win-warning"===n?e.createElement(a.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},i))}BannerIcon.propTypes={type:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerLogo}));var i=n(1),r=n.n(i),a=n(17),o=n(155),c=n(198);function BannerLogo(t){var n=t.module,i=t.moduleName;return e.createElement(a.a,{size:12},e.createElement("div",{className:"googlesitekit-publisher-win__logo"},n&&e.createElement(c.a,{slug:n,size:19}),!n&&e.createElement(o.a,{height:"34",width:"32"})),i&&e.createElement("div",{className:"googlesitekit-publisher-win__module-name"},i))}BannerLogo.propTypes={module:r.a.string,moduleName:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerDescription}));var i=n(1),r=n.n(i),a=n(0),o=n(75),c=n(20),l=n(86);function BannerDescription(t){var n=t.description,i=t.learnMoreLabel,r=t.learnMoreURL,s=t.learnMoreTarget,u=t.learnMoreDescription,d=t.onLearnMoreClick;if(!n)return null;var g;return i&&(g=e.createElement(a.Fragment,null,e.createElement(c.a,{onClick:function(e){e.persist(),null==d||d()},href:r,external:s===l.a.EXTERNAL},i),u)),e.createElement("div",{className:"googlesitekit-publisher-win__desc"},Object(a.isValidElement)(n)?e.createElement(a.Fragment,null,n,g&&e.createElement("p",null,g)):e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.a)(n,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",g))}BannerDescription.propTypes={description:r.a.node,learnMoreURL:r.a.string,learnMoreDescription:r.a.string,learnMoreLabel:r.a.string,learnMoreTarget:r.a.oneOf(Object.values(l.a)),onLearnMoreClick:r.a.func}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(158),r=(i.a.Consumer,i.a.Provider);t.a=r},function(e,t,n){"use strict";(function(e){var i=n(3),r=n(231),a=n(7);t.a=function PermissionsModal(){return Object(i.useSelect)((function(e){return e(a.a).isAuthenticated()}))?e.createElement(r.a,null):null}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,i){var r=n(5),a=n.n(r),o=n(16),c=n.n(o),l=n(2),s=n(0),u=n(3),d=n(109),g=n(29),f=n(32),m=n(7),p=n(129),h=n(72);t.a=function AuthenticatedPermissionsModal(){var t,n,r,o,b=Object(u.useRegistry)(),v=Object(u.useSelect)((function(e){return e(m.a).getPermissionScopeError()})),E=Object(u.useSelect)((function(e){return e(m.a).getUnsatisfiedScopes()})),_=Object(u.useSelect)((function(t){var n,i,r;return t(m.a).getConnectURL({additionalScopes:null==v||null===(n=v.data)||void 0===n?void 0:n.scopes,redirectURL:(null==v||null===(i=v.data)||void 0===i?void 0:i.redirectURL)||e.location.href,errorRedirectURL:null==v||null===(r=v.data)||void 0===r?void 0:r.errorRedirectURL})})),O=Object(u.useDispatch)(m.a).clearPermissionScopeError,k=Object(u.useDispatch)(f.a).navigateTo,y=Object(u.useDispatch)(g.a).setValues,j=Object(s.useCallback)((function(){O()}),[O]),S=Object(s.useCallback)(c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return y(m.d,{permissionsError:v}),e.next=3,Object(p.c)(b);case 3:k(_);case 4:case"end":return e.stop()}}),e)}))),[b,_,k,v,y]);return Object(s.useEffect)((function(){(function(){var e=c()(a.a.mark((function e(){var t,n,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(null==v||null===(t=v.data)||void 0===t?void 0:t.skipModal)||!(null==v||null===(n=v.data)||void 0===n||null===(i=n.scopes)||void 0===i?void 0:i.length)){e.next=3;break}return e.next=3,S();case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[S,v]),v?(null==v||null===(t=v.data)||void 0===t||null===(n=t.scopes)||void 0===n?void 0:n.length)?(null==v||null===(r=v.data)||void 0===r?void 0:r.skipModal)||E&&(null==v||null===(o=v.data)||void 0===o?void 0:o.scopes.every((function(e){return E.includes(e)})))?null:i.createElement(h.a,null,i.createElement(d.a,{title:Object(l.__)("Additional Permissions Required","google-site-kit"),subtitle:v.message,confirmButton:Object(l.__)("Proceed","google-site-kit"),dialogActive:!0,handleConfirm:S,handleDialog:j,medium:!0})):(e.console.warn("permissionsError lacks scopes array to use for redirect, so not showing the PermissionsModal. permissionsError was:",v),null):null}}).call(this,n(28),n(4))},function(e,t,n){"use strict";var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(0),u=n(3),d=n(129);t.a=function RestoreSnapshots(e){var t=e.children,n=Object(u.useRegistry)(),i=Object(s.useState)(!1),a=l()(i,2),c=a[0],g=a[1];return Object(s.useEffect)((function(){c||o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(d.b)(n);case 2:g(!0);case 3:case"end":return e.stop()}}),e)})))()}),[n,c]),c?t:null}},function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return FeatureTours}));var r=n(81),a=n(0),o=n(3),c=n(7),l=n(18),s=n(90);function FeatureTours(){var t=Object(l.a)(),n=Object(o.useDispatch)(c.a).triggerTourForView;Object(r.a)((function(){n(t)}));var u=Object(o.useSelect)((function(e){return e(c.a).getCurrentTour()}));return Object(a.useEffect)((function(){if(u){var t=document.getElementById("js-googlesitekit-main-dashboard");if(t){var n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}}),[u]),u?i.createElement(s.a,{tourID:u.slug,steps:u.steps,gaEventCategory:u.gaEventCategory,callback:u.callback}):null}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){var i=n(15),r=n.n(i),a=n(1),o=n.n(a),c=n(11),l=n.n(c),s=n(590),u=n(2),d=n(0),g=n(3),f=n(256),m=n(276),p=n(280),h=n(7),b=n(17),v=n(284),E=n(291),_=n(293),O=n(34),k=n(52),y=n(20),j=n(299),S=n(13),w=n(300);function Header(t){var n,i=t.children,a=t.subHeader,o=t.showNavigation,c=!!Object(k.c)(),C=Object(O.a)();Object(w.a)();var A=Object(g.useSelect)((function(e){return e(S.c).getAdminURL("googlesitekit-dashboard")})),N=Object(g.useSelect)((function(e){return e(h.a).isAuthenticated()})),T=Object(s.a)({childList:!0}),x=r()(T,2),D=x[0],R=!!(null===(n=x[1].target)||void 0===n?void 0:n.childElementCount);return e.createElement(d.Fragment,null,e.createElement("header",{className:l()("googlesitekit-header",{"googlesitekit-header--has-subheader":R,"googlesitekit-header--has-navigation":o})},e.createElement(b.e,null,e.createElement(b.k,null,e.createElement(b.a,{smSize:1,mdSize:2,lgSize:4,className:"googlesitekit-header__logo",alignMiddle:!0},e.createElement(y.a,{"aria-label":Object(u.__)("Go to dashboard","google-site-kit"),className:"googlesitekit-header__logo-link",href:A},e.createElement(f.a,null))),e.createElement(b.a,{smSize:3,mdSize:6,lgSize:8,className:"googlesitekit-header__children",alignMiddle:!0},i,!N&&c&&C&&e.createElement(_.a,null),N&&!C&&e.createElement(m.a,null))))),e.createElement("div",{className:"googlesitekit-subheader",ref:D},e.createElement(p.a,null),a),o&&e.createElement(v.a,null),c&&e.createElement(j.a,null),e.createElement(E.a,null))}Header.displayName="Header",Header.propTypes={children:o.a.node,subHeader:o.a.element,showNavigation:o.a.bool},Header.defaultProps={children:null,subHeader:null},t.a=Header}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return HelpMenu}));var i=n(15),r=n.n(i),a=n(1),o=n.n(a),c=n(209),l=n(0),s=n(56),u=n(2),d=n(3),g=n(10),f=n(301),m=n(112),p=n(9),h=n(164),b=n(19),v=n(18),E=n(13);function HelpMenu(t){var n=t.children,i=Object(l.useState)(!1),a=r()(i,2),o=a[0],_=a[1],O=Object(l.useRef)(),k=Object(v.a)();Object(c.a)(O,(function(){return _(!1)})),Object(m.a)([s.c,s.f],O,(function(){return _(!1)}));var y=Object(d.useSelect)((function(e){return e(b.a).isModuleActive("adsense")})),j=Object(l.useCallback)((function(){o||Object(p.I)("".concat(k,"_headerbar"),"open_helpmenu"),_(!o)}),[o,k]),S=Object(l.useCallback)((function(){_(!1)}),[]),w=Object(d.useSelect)((function(e){return e(E.c).getDocumentationLinkURL("fix-common-issues")}));return e.createElement("div",{ref:O,className:"googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},e.createElement(g.Button,{"aria-controls":"googlesitekit-help-menu","aria-expanded":o,"aria-label":Object(u.__)("Help","google-site-kit"),"aria-haspopup":"menu",className:"googlesitekit-header__dropdown googlesitekit-border-radius-round googlesitekit-button-icon googlesitekit-help-menu__button mdc-button--dropdown",icon:e.createElement(f.a,{width:"20",height:"20"}),onClick:j,text:!0,tooltipEnterDelayInMS:500}),e.createElement(g.Menu,{className:"googlesitekit-width-auto",menuOpen:o,id:"googlesitekit-help-menu",onSelected:S},n,e.createElement(h.a,{gaEventLabel:"fix_common_issues",href:w},Object(u.__)("Fix common issues","google-site-kit")),e.createElement(h.a,{gaEventLabel:"documentation",href:"https://sitekit.withgoogle.com/documentation/"},Object(u.__)("Read help docs","google-site-kit")),e.createElement(h.a,{gaEventLabel:"support_forum",href:"https://wordpress.org/support/plugin/google-site-kit/"},Object(u.__)("Get support","google-site-kit")),y&&e.createElement(h.a,{gaEventLabel:"adsense_help",href:"https://support.google.com/adsense/"},Object(u.__)("Get help with AdSense","google-site-kit"))))}HelpMenu.propTypes={children:o.a.node}}).call(this,n(4))},,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationWithSVG}));var i=n(21),r=n.n(i),a=n(11),o=n.n(a),c=n(24),l=n(17),s=n(255);function NotificationWithSVG(t){var n=t.id,i=t.title,a=t.description,u=t.actions,d=t.SVG,g=t.primaryCellSizes,f=t.SVGCellSizes,m=Object(c.e)(),p={mdSize:(null==f?void 0:f.md)||8,lgSize:(null==f?void 0:f.lg)||6};return m===c.c&&(p={mdSize:(null==f?void 0:f.md)||8}),m===c.b&&(p={smSize:(null==f?void 0:f.sm)||12}),e.createElement("div",{className:"googlesitekit-widget-context"},e.createElement(l.e,{className:"googlesitekit-widget-area"},e.createElement(l.k,null,e.createElement(l.a,{size:12},e.createElement("div",{className:o()("googlesitekit-widget","googlesitekit-widget--no-padding","googlesitekit-setup-cta-banner","googlesitekit-setup-cta-banner--".concat(n))},e.createElement("div",{className:"googlesitekit-widget__body"},e.createElement(l.e,{collapsed:!0},e.createElement(l.k,null,e.createElement(l.a,{smSize:(null==g?void 0:g.sm)||12,mdSize:(null==g?void 0:g.md)||8,lgSize:(null==g?void 0:g.lg)||6,className:"googlesitekit-setup-cta-banner__primary-cell"},e.createElement("h3",{className:"googlesitekit-setup-cta-banner__title"},i),a,e.createElement(s.a,{id:n}),u),e.createElement(l.a,r()({alignBottom:!0,className:"googlesitekit-setup-cta-banner__svg-wrapper--".concat(n)},p),e.createElement(d,null))))))))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return m})),n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return b}));var i=n(21),r=n.n(i),a=n(63),o=n.n(a),c=n(267),l=n(322),s=n(323),u=n(244),d=n(268),g=n(324),f=n(0),m=o()((function(e){return{widgetSlug:e,Widget:p(e)(c.a),WidgetRecoverableModules:p(e)(d.a),WidgetReportZero:p(e)(l.a),WidgetReportError:p(e)(s.a),WidgetNull:p(e)(u.a)}}));function p(t){return function(n){var i=Object(f.forwardRef)((function(i,a){return e.createElement(n,r()({},i,{ref:a,widgetSlug:t}))}));return i.displayName="WithWidgetSlug",(n.displayName||n.name)&&(i.displayName+="(".concat(n.displayName||n.name,")")),i}}var h=function(t){var n=m(t);return function(t){function DecoratedComponent(i){return e.createElement(t,r()({},i,n))}return DecoratedComponent.displayName="WithWidgetComponentProps",(t.displayName||t.name)&&(DecoratedComponent.displayName+="(".concat(t.displayName||t.name,")")),DecoratedComponent}},b=function(t){return function(n){function DecoratedComponent(i){return e.createElement(n,r()({},i,{WPDashboardReportError:p(t)(g.a)}))}return DecoratedComponent.displayName="WithWPDashboardWidgetComponentProps",(n.displayName||n.name)&&(DecoratedComponent.displayName+="(".concat(n.displayName||n.name,")")),DecoratedComponent}}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetNull}));var i=n(6),r=n.n(i),a=n(1),o=n.n(a),c=n(143),l=n(74);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}var u={};function WidgetNull(t){var n=t.widgetSlug;return Object(c.a)(n,l.a,u),e.createElement(l.a,null)}WidgetNull.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:o.a.string.isRequired},l.a.propTypes)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdBlockerWarning}));var i=n(1),r=n.n(i),a=n(3),o=n(13),c=n(19),l=n(395);function AdBlockerWarning(t){var n=t.moduleSlug,i=t.className,r=Object(a.useSelect)((function(e){return e(c.a).getModuleStoreName(n)})),s=Object(a.useSelect)((function(e){var t;return null===(t=e(r))||void 0===t?void 0:t.getAdBlockerWarningMessage()})),u=Object(a.useSelect)((function(e){return e(o.c).getDocumentationLinkURL("".concat(n,"-ad-blocker-detected"))}));return e.createElement(l.a,{className:i,getHelpLink:u,warningMessage:s})}AdBlockerWarning.propTypes={className:r.a.string,moduleSlug:r.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"d",(function(){return g})),n.d(t,"b",(function(){return m})),n.d(t,"c",(function(){return p.a})),n.d(t,"g",(function(){return p.c})),n.d(t,"a",(function(){return c.a})),n.d(t,"f",(function(){return h})),n.d(t,"e",(function(){return s}));var i=n(15),r=n.n(i),a=n(27),o=n.n(a),c=n(118),l=n(74);function s(e){return!!e&&e.Component===l.a}function u(e,t){if(9!==t)return[e,t];for(var n=(e=o()(e)).length-1;0!==t&&n>=0;)3===e[n]?(t-=3,e[n]=4):6===e[n]&&(t-=6,e[n]=8),n--;return[e,t]}function d(e,t){return(Array.isArray(t.width)?t.width:[t.width]).map((function(t){return{counter:e+c.c[t],width:t}}))}function g(e,t){var n=[],i=[];if(!(null==e?void 0:e.length))return{columnWidths:n,rowIndexes:i};var a=0,o=0,l=function(e,t){return e.counter-t.counter},g=function(e,t){var n=e.counter;return t.counter-n},f=function(e){return e.counter<=12};if(e.forEach((function(m,p){if(s(t[m.slug]))return n.push(0),void i.push(o);var h=d(a,m),b=function(e,t,n){for(;++e<t.length;)if(!s(n[t[e].slug]))return t[e];return null}(p,e,t);null!==b&&0!==d(h.sort(l)[0].counter,b).filter(f).length||h.some(f)&&(h=(h=h.sort(g)).filter(f));var v=h[0].width;if(i.push(o),(a+=c.c[v])>12){if(a-=c.c[v],i[p]++,9===a){var E=u(n,a),_=r()(E,2);n=_[0],a=_[1]}a=c.c[v],o++}else 12===a&&(a=0,o++);n.push(c.c[v])})),9===a){var m=u(n,a),p=r()(m,2);n=p[0],a=p[1]}return{columnWidths:n,rowIndexes:i}}var f=n(14);function m(e,t,n){var i=n.columnWidths,r=n.rowIndexes,a=[],l=o()(i);if(!(null==e?void 0:e.length))return{gridColumnWidths:l,overrideComponents:a};var s=null,u=-1,d=[];if(function(e,t){for(var n={},i=0;i<e.length;i++){var r,a=e[i],o=null==t?void 0:t[a.slug],l=null==o?void 0:o.Component,s=null==o||null===(r=o.metadata)||void 0===r?void 0:r.moduleSlug,u=c.b.includes(l);if(!l||!s||!u)return!1;if(n[s]){if(n[s]!==l)return!1}else n[s]=l}return!(Object.keys(n).length>1)}(e,t)){var g=Array.from({length:e.length-1}).fill(0);return{overrideComponents:[t[e[0].slug]],gridColumnWidths:[12].concat(o()(g))}}return e.forEach((function(n,o){var c,g,m,p,h;if(a.push(null),s=t[n.slug],u=r[o],s)if(g=s,m=t[null===(c=e[o+1])||void 0===c?void 0:c.slug],p=u,h=r[o+1],p===h&&Object(f.isEqual)(g,m))d.push(i[o]),l[o]=0;else if(d.length>0){d.push(i[o]);var b=d.reduce((function(e,t){return e+t}),0);a[o]=s,l[o]=b,d=[]}})),{gridColumnWidths:l,overrideComponents:a}}var p=n(243);function h(e){return(Array.isArray(e)?e:[e]).filter((function(e){return"string"==typeof e&&e.length>0}))}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceErrorModal}));var i=n(1),r=n.n(i),a=n(38),o=n(2),c=n(3),l=n(20),s=n(109),u=n(72),d=n(13),g=n(8),f=n(35),m=n(9);function AudienceErrorModal(t){var n=t.apiErrors,i=t.hasOAuthError,r=t.inProgress,p=t.title,h=t.description,b=t.trackEventCategory,v=t.onCancel,E=void 0===v?function(){}:v,_=t.onRetry,O=void 0===_?function(){}:_,k=Array.isArray(n)?n:[n],y=Object(c.useSelect)((function(e){return e(d.c).getErrorTroubleshootingLinkURL({code:"analytics-4_insufficient_permissions"})})),j=Object(c.useSelect)((function(e){return e(g.r).getServiceEntityAccessURL()})),S=Object(c.useSelect)((function(e){return e(d.c).getErrorTroubleshootingLinkURL({code:"access_denied"})}));if(!k.length&&!i)return null;var w,C,A,N,T=k.some((function(e){return Object(f.e)(e)}));return i?(w=Object(o.__)("Analytics update failed","google-site-kit"),C=Object(a.a)(Object(o.__)("Setup was interrupted because you did not grant the necessary permissions. <HelpLink />","google-site-kit"),{HelpLink:e.createElement(l.a,{href:S,external:!0,hideExternalIndicator:!0},Object(o.__)("Get help","google-site-kit"))}),A=Object(o.__)("Retry","google-site-kit")):T?(w=Object(o.__)("Insufficient permissions","google-site-kit"),C=Object(a.a)(Object(o.__)("You’ll need to contact your administrator. Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(l.a,{href:y,external:!0,hideExternalIndicator:!0},Object(o.__)("Get help","google-site-kit"))}),A=Object(o.__)("Request access","google-site-kit"),N=j):(w=p||Object(o.__)("Failed to set up visitor groups","google-site-kit"),C=h||Object(o.__)("Oops! Something went wrong. Retry enabling groups.","google-site-kit"),A=Object(o.__)("Retry","google-site-kit")),e.createElement(u.a,null,e.createElement(s.a,{dialogActive:!0,buttonLink:N,title:w,subtitle:C,handleConfirm:function(){var e;e=i?"auth_error_retry":T?"insufficient_permissions_error_request_access":"setup_error_retry",Object(m.I)(b,e).finally((function(){T||O()}))},confirmButton:A,handleDialog:function(){var e;e=i?"auth_error_cancel":T?"insufficient_permissions_error_cancel":"setup_error_cancel",Object(m.I)(b,e).finally(E)},onOpen:function(){var e;e=i?"auth_error":T?"insufficient_permissions_error":"setup_error",Object(m.I)(b,e)},onClose:E,danger:!0,inProgress:r}))}AudienceErrorModal.propTypes={apiErrors:r.a.oneOfType([r.a.arrayOf(r.a.object),r.a.object,r.a.array]),hasOAuthError:r.a.bool,inProgress:r.a.bool,title:r.a.string,description:r.a.string,trackEventCategory:r.a.string,onCancel:r.a.func,onRetry:r.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=n(10),s=n(492),u=Object(c.forwardRef)((function(t,n){var i=t.className,a=t.content,o=t.dismissLabel,c=t.Icon,u=void 0===c?s.a:c,d=t.onDismiss;return e.createElement("div",{ref:n,className:r()("googlesitekit-audience-segmentation-info-notice",i)},e.createElement(u,{width:"20",height:"20"}),e.createElement("div",{className:"googlesitekit-audience-segmentation-info-notice__body"},e.createElement("p",null,a),o&&e.createElement(l.Button,{tertiary:!0,onClick:d,className:"googlesitekit-audience-segmentation-info-notice__dismiss"},o)))}));u.propTypes={className:o.a.string,content:o.a.string.isRequired,dismissLabel:o.a.string,Icon:o.a.elementType,onDismiss:o.a.func},t.a=u}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(15),r=n.n(i),a=n(0);t.a=function(t,n){var i=Object(a.useState)(null),o=r()(i,2),c=o[0],l=o[1];return Object(a.useEffect)((function(){if(t.current&&"function"==typeof e.IntersectionObserver){var i=new e.IntersectionObserver((function(e){l(e[e.length-1])}),n);return i.observe(t.current),function(){l(null),i.disconnect()}}return function(){}}),[t.current,n.threshold,n.root,n.rootMargin]),c}}).call(this,n(28))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BadgeWithTooltip}));var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(132);function BadgeWithTooltip(t){var n=t.className,i=void 0===n?"":n,r=t.label,a=t.onTooltipOpen,l=t.tooltipTitle;return e.createElement("span",{className:o()("googlesitekit-badge-with-tooltip","googlesitekit-badge",i)},r,l&&e.createElement(c.a,{onOpen:a,title:l}))}BadgeWithTooltip.propTypes={onTooltipOpen:r.a.func,tooltipTitle:r.a.node,className:r.a.string,label:r.a.node.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return SurveyViewTrigger}));var i=n(0),r=n(1),a=n.n(r),o=n(3),c=n(13),l=n(7);function SurveyViewTrigger(e){var t=e.triggerID,n=e.ttl,r=void 0===n?0:n,a=Object(o.useSelect)((function(e){return e(c.c).isUsingProxy()})),s=Object(o.useDispatch)(l.a).triggerSurvey;return Object(i.useEffect)((function(){a&&s(t,{ttl:r})}),[a,t,r,s]),null}SurveyViewTrigger.propTypes={triggerID:a.a.string.isRequired,ttl:a.a.number}},,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Error}));var i=n(1),r=n.n(i),a=n(0),o=n(3),c=n(13),l=n(54);function Error(t){var n=t.id,i=Object(o.useSelect)((function(e){return e(c.c).getError("notificationAction",[n])})),r=Object(o.useDispatch)(c.c).clearError;return Object(a.useEffect)((function(){return function(){r("notificationAction",[n])}}),[r,n]),i?e.createElement(l.a,{message:i.message}):null}Error.propTypes={id:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(2),r=n(155),a=n(257),o=n(105);t.a=function Logo(){return e.createElement("div",{className:"googlesitekit-logo","aria-hidden":"true"},e.createElement(r.a,{className:"googlesitekit-logo__logo-g",height:"34",width:"32"}),e.createElement(a.a,{className:"googlesitekit-logo__logo-sitekit",height:"26",width:"99"}),e.createElement(o.a,null,Object(i.__)("Site Kit by Google Logo","google-site-kit")))}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M62.09 1.664h3.038v.1L58.34 9.593l7.241 10.224v.1H62.7L56.755 11.4 53.95 14.64v5.278h-2.351V1.664h2.35v9.415h.1l8.04-9.415zM69.984 3.117c0 .454-.166.853-.487 1.175-.322.322-.71.488-1.176.488-.455 0-.854-.166-1.175-.488a1.599 1.599 0 01-.488-1.175c0-.466.166-.854.488-1.176.321-.322.71-.488 1.175-.488.455 0 .854.166 1.176.488.332.333.487.72.487 1.176zm-.476 4.313v12.498h-2.351V7.43h2.35zM77.016 20.128c-1.02 0-1.864-.31-2.54-.943-.676-.632-1.02-1.508-1.031-2.628V9.57h-2.196V7.43h2.196V3.603h2.35V7.43h3.061v2.14h-3.06v6.222c0 .831.166 1.397.488 1.696.321.3.687.444 1.097.444.189 0 .366-.022.555-.067.188-.044.344-.1.499-.166l.743 2.096c-.632.222-1.342.333-2.162.333zM2.673 18.952C1.375 18.009.488 16.678 0 14.97l2.883-1.176c.289 1.076.799 1.94 1.542 2.628.732.677 1.619 1.02 2.65 1.02.965 0 1.774-.244 2.45-.742.677-.5 1.01-1.187 1.01-2.052 0-.798-.3-1.453-.887-1.974-.588-.521-1.62-1.042-3.094-1.564l-1.22-.432C4.025 10.224 2.928 9.57 2.04 8.716 1.153 7.862.71 6.742.71 5.346c0-.966.266-1.853.787-2.673C2.018 1.852 2.75 1.209 3.693.72 4.624.244 5.678 0 6.864 0c1.708 0 3.072.41 4.081 1.242 1.02.832 1.697 1.752 2.04 2.795L10.236 5.2c-.2-.621-.576-1.164-1.142-1.63-.565-.477-1.286-.71-2.173-.71s-1.641.222-2.251.676c-.61.455-.91 1.032-.91 1.742 0 .676.278 1.22.82 1.663.544.432 1.398.854 2.563 1.253l1.22.41c1.674.577 2.96 1.342 3.88 2.274.921.931 1.376 2.184 1.376 3.748 0 1.275-.322 2.34-.976 3.193a6.01 6.01 0 01-2.495 1.919 8.014 8.014 0 01-3.116.621c-1.62 0-3.072-.466-4.358-1.408zM15.969 3.449a1.95 1.95 0 01-.588-1.43c0-.566.2-1.043.588-1.431A1.95 1.95 0 0117.399 0c.566 0 1.043.2 1.43.588.389.388.588.865.588 1.43 0 .566-.2 1.043-.587 1.43a1.95 1.95 0 01-1.43.589c-.566-.012-1.043-.2-1.431-.588zm-.067 2.595h2.994v13.883h-2.994V6.044zM25.405 19.85c-.543-.2-.986-.466-1.33-.788-.776-.776-1.176-1.84-1.176-3.182V8.683h-2.428v-2.64h2.428V2.13h2.994v3.926h3.372v2.639h-3.372v6.531c0 .743.145 1.276.433 1.575.277.366.743.543 1.42.543.31 0 .576-.044.82-.122.233-.077.488-.21.765-.399v2.917c-.599.277-1.32.41-2.173.41a5.01 5.01 0 01-1.753-.3zM33.623 19.407a6.63 6.63 0 01-2.529-2.628c-.61-1.12-.909-2.373-.909-3.77 0-1.332.3-2.551.887-3.693.588-1.132 1.409-2.04 2.462-2.706 1.053-.666 2.251-1.01 3.593-1.01 1.397 0 2.606.311 3.637.921a6.123 6.123 0 012.34 2.528c.532 1.076.799 2.274.799 3.627 0 .255-.023.576-.078.953H33.179c.111 1.287.566 2.285 1.375 2.983a4.162 4.162 0 002.817 1.043c.854 0 1.597-.189 2.218-.588a4.266 4.266 0 001.508-1.597l2.528 1.198c-.654 1.142-1.508 2.04-2.561 2.694-1.054.655-2.318.976-3.782.976-1.364.022-2.584-.288-3.66-.931zm7.23-8.051a3.332 3.332 0 00-.466-1.453c-.277-.477-.687-.887-1.242-1.208-.554-.322-1.23-.488-2.03-.488-.964 0-1.773.288-2.439.853-.665.566-1.12 1.342-1.375 2.296h7.552z",fill:"#5F6368"});t.a=function SvgLogoSitekit(e){return i.createElement("svg",r({viewBox:"0 0 80 21",fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return f}));var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=n(10),s=n(137),u=n(70),d=n(58),g=n(207),f={SUCCESS:"success",WARNING:"warning",INFO:"info"},m=Object(c.forwardRef)((function(t,n){var i=t.title,a=t.description,o=t.Icon,c=t.ctaLink,m=t.ctaLabel,p=t.className,h=t.onCTAClick,b=t.isCTALinkExternal,v=t.dismissLabel,E=t.onDismiss,_=t.variant,O=void 0===_?f.SUCCESS:_,k=t.hideIcon,y=void 0!==k&&k;return e.createElement("div",{ref:n,className:r()("googlesitekit-subtle-notification",{"googlesitekit-subtle-notification--success":O===f.SUCCESS,"googlesitekit-subtle-notification--warning":O===f.WARNING,"googlesitekit-subtle-notification--info":O===f.INFO},p)},!y&&e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},o&&e.createElement(o,{width:24,height:24}),!o&&O===f.SUCCESS&&e.createElement(s.a,{width:24,height:24}),!o&&O===f.WARNING&&e.createElement(d.a,{width:24,height:24}),!o&&O===f.INFO&&e.createElement(g.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,i),a&&e.createElement("p",{className:"googlesitekit-subtle-notification__secondary_description"},a)),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},v&&e.createElement(l.Button,{tertiary:!0,onClick:E},v),m&&e.createElement(l.Button,{className:"googlesitekit-subtle-notification__cta",href:c,onClick:h,target:b?"_blank":"_self",trailingIcon:b?e.createElement(u.a,{width:14,height:14}):void 0},m)))}));m.propTypes={title:o.a.node.isRequired,description:o.a.string,Icon:o.a.elementType,ctaLink:o.a.string,ctaLabel:o.a.string,className:o.a.string,onCTAClick:o.a.func,isCTALinkExternal:o.a.bool,dismissLabel:o.a.string,onDismiss:o.a.func,variant:o.a.oneOf(Object.values(f)),hideIcon:o.a.bool},t.b=m}).call(this,n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return f})),n.d(t,"b",(function(){return SpinnerButton}));var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(344),g=n(261),f={BEFORE:"before",AFTER:"after"};function SpinnerButton(t){var n=t.className,i=t.onClick,a=void 0===i?function(){}:i,c=t.isSaving,l=void 0!==c&&c,s=t.spinnerPosition,m=void 0===s?f.AFTER:s,p=o()(t,["className","onClick","isSaving","spinnerPosition"]);return e.createElement(d.a,r()({className:u()(n,"googlesitekit-button-icon--spinner",{"googlesitekit-button-icon--spinner__running":l,"googlesitekit-button-icon--spinner__before":m===f.BEFORE,"googlesitekit-button-icon--spinner__after":m===f.AFTER}),icon:l&&m===f.BEFORE?e.createElement(g.a,{size:14}):void 0,trailingIcon:l&&m===f.AFTER?e.createElement(g.a,{size:14}):void 0,onClick:a},p))}SpinnerButton.propTypes={className:l.a.string,onClick:l.a.func,isSaving:l.a.bool,spinnerPosition:l.a.oneOf(Object.values(f))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CircularProgress}));var i=n(640);function CircularProgress(t){return e.createElement(i.a,t)}}).call(this,n(4))},,,,,,function(e,t,n){"use strict";(function(e){var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=Object(c.forwardRef)((function(t,n){var i=t.children,a=t.className,o=t.widgetSlug,c=t.noPadding,l=t.Header,s=t.Footer;return e.createElement("div",{className:r()("googlesitekit-widget","googlesitekit-widget--".concat(o),{"googlesitekit-widget--no-padding":c},{"googlesitekit-widget--with-header":l},a),ref:n},l&&e.createElement("div",{className:"googlesitekit-widget__header"},e.createElement(l,null)),e.createElement("div",{className:"googlesitekit-widget__body"},i),s&&e.createElement("div",{className:"googlesitekit-widget__footer"},e.createElement(s,null)))}));l.defaultProps={children:void 0,noPadding:!1},l.propTypes={children:o.a.node,widgetSlug:o.a.string.isRequired,noPadding:o.a.bool,Header:o.a.elementType,Footer:o.a.elementType},t.a=l}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetRecoverableModules}));var i=n(6),r=n.n(i),a=n(21),o=n.n(a),c=n(27),l=n.n(c),s=n(25),u=n.n(s),d=n(1),g=n.n(d),f=n(0),m=n(143),p=n(163);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function WidgetRecoverableModules(t){var n=t.widgetSlug,i=t.moduleSlugs,r=u()(t,["widgetSlug","moduleSlugs"]),a=Object(f.useMemo)((function(){return{moduleSlug:l()(i).sort().join(","),moduleSlugs:i}}),[i]);return Object(m.a)(n,p.a,a),e.createElement(p.a,o()({moduleSlugs:i},r))}WidgetRecoverableModules.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:g.a.string.isRequired},p.a.propTypes)}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));n(8);function i(e){var t;return 400===(null==e?void 0:e.code)&&(null==e||null===(t=e.message)||void 0===t?void 0:t.includes("is not a valid dimension"))}},,,,,,,function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return UserMenu}));var r=n(5),a=n.n(r),o=n(16),c=n.n(o),l=n(15),s=n.n(l),u=n(209),d=n(0),g=n(2),f=n(56),m=n(3),p=n(10),h=n(109),b=n(9),v=n(37),E=n(72),_=n(277),O=n(220),k=n(278),y=n(279),j=n(29),S=n(13),w=n(7),C=n(32),A=n(8),N=n(112),T=n(18);function UserMenu(){var t=Object(m.useSelect)((function(e){return e(S.c).getProxyPermissionsURL()})),n=Object(m.useSelect)((function(e){return e(w.a).getEmail()})),r=Object(m.useSelect)((function(e){return e(w.a).getPicture()})),o=Object(m.useSelect)((function(e){return e(w.a).getFullName()})),l=Object(m.useSelect)((function(e){return e(S.c).getAdminURL("googlesitekit-splash",{googlesitekit_context:"revoked"})})),x=Object(m.useSelect)((function(e){return e(j.a).getValue(A.d,"isAutoCreatingCustomDimensionsForAudience")})),D=Object(d.useState)(!1),R=s()(D,2),M=R[0],I=R[1],P=Object(d.useState)(!1),L=s()(P,2),B=L[0],z=L[1],F=Object(d.useRef)(),V=Object(d.useRef)(),W=Object(T.a)(),H=Object(m.useDispatch)(C.a).navigateTo;Object(u.a)(F,(function(){return z(!1)})),Object(N.a)([f.c,f.f],F,(function(){var e;z(!1),null===(e=V.current)||void 0===e||e.focus()})),Object(d.useEffect)((function(){var t=function(e){f.c===e.keyCode&&(I(!1),z(!1))};return e.addEventListener("keyup",t),function(){e.removeEventListener("keyup",t)}}),[]);var U,q=Object(d.useCallback)((function(){B||Object(b.I)("".concat(W,"_headerbar"),"open_usermenu"),z(!B)}),[B,W]),G=Object(d.useCallback)((function(){I(!M),z(!1)}),[M]),K=Object(d.useCallback)(function(){var e=c()(a.a.mark((function e(n,i){var r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=i.detail.item,e.t0=null==r?void 0:r.id,e.next="manage-sites"===e.t0?4:"disconnect"===e.t0?9:11;break;case 4:if(!t){e.next=8;break}return e.next=7,Object(b.I)("".concat(W,"_headerbar_usermenu"),"manage_sites");case 7:H(t);case 8:return e.abrupt("break",12);case 9:return G(),e.abrupt("break",12);case 11:q();case 12:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),[t,q,G,H,W]),Y=Object(d.useCallback)(c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return I(!1),e.next=3,Object(v.b)();case 3:return e.next=5,Object(b.I)("".concat(W,"_headerbar_usermenu"),"disconnect_user");case 5:H(l);case 6:case"end":return e.stop()}}),e)}))),[l,H,W]);return n?(o&&n&&(U=Object(g.sprintf)(
/* translators: Account info text. 1: User's (full) name 2: User's email address. */
Object(g.__)("Google Account for %1$s (Email: %2$s)","google-site-kit"),o,n)),o&&!n&&(U=Object(g.sprintf)(
/* translators: Account info text. 1: User's (full) name. */
Object(g.__)("Google Account for %1$s","google-site-kit"),o)),!o&&n&&(U=Object(g.sprintf)(
/* translators: Account info text. 1: User's email address. */
Object(g.__)("Google Account (Email: %1$s)","google-site-kit"),n)),i.createElement(d.Fragment,null,i.createElement("div",{ref:F,className:"googlesitekit-user-selector googlesitekit-dropdown-menu googlesitekit-dropdown-menu__icon-menu mdc-menu-surface--anchor"},i.createElement(p.Button,{disabled:x,ref:V,className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--tablet googlesitekit-border-radius-round--phone googlesitekit-border-radius-round googlesitekit-button-icon",text:!0,onClick:q,icon:!!r&&i.createElement("i",{className:"mdc-button__icon mdc-button__account","aria-hidden":"true"},i.createElement("img",{className:"mdc-button__icon--image",src:r,alt:Object(g.__)("User Avatar","google-site-kit")})),"aria-haspopup":"menu","aria-expanded":B,"aria-controls":"user-menu","aria-label":x?void 0:Object(g.__)("Account","google-site-kit"),tooltip:!0,tooltipEnterDelayInMS:500,customizedTooltip:x?null:i.createElement("span",{"aria-label":U},i.createElement("strong",null,Object(g.__)("Google Account","google-site-kit")),i.createElement("br",null),i.createElement("br",null),o,o&&i.createElement("br",null),n)}),i.createElement(p.Menu,{className:"googlesitekit-user-menu",menuOpen:B,onSelected:K,id:"user-menu"},i.createElement("li",null,i.createElement(_.a,null)),!!t&&i.createElement("li",{id:"manage-sites",className:"mdc-list-item",role:"menuitem"},i.createElement(O.a,{icon:i.createElement(y.a,{width:"22"}),label:Object(g.__)("Manage Sites","google-site-kit")})),i.createElement("li",{id:"disconnect",className:"mdc-list-item",role:"menuitem"},i.createElement(O.a,{icon:i.createElement(k.a,{width:"22"}),label:Object(g.__)("Disconnect","google-site-kit")})))),i.createElement(E.a,null,i.createElement(h.a,{dialogActive:M,handleConfirm:Y,handleDialog:G,title:Object(g.__)("Disconnect","google-site-kit"),subtitle:Object(g.__)("Disconnecting Site Kit by Google will remove your access to all services. After disconnecting, you will need to re-authorize to restore service.","google-site-kit"),confirmButton:Object(g.__)("Disconnect","google-site-kit"),danger:!0,small:!0})))):null}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Details}));var i=n(2),r=n(3),a=n(7);function Details(){var t=Object(r.useSelect)((function(e){return e(a.a).getPicture()})),n=Object(r.useSelect)((function(e){return e(a.a).getFullName()})),o=Object(r.useSelect)((function(e){return e(a.a).getEmail()}));return e.createElement("div",{className:"googlesitekit-user-menu__details","aria-label":Object(i.__)("Google account","google-site-kit")},!!t&&e.createElement("img",{className:"googlesitekit-user-menu__details-avatar",src:t,alt:""}),e.createElement("div",{className:"googlesitekit-user-menu__details-info"},e.createElement("p",{className:"googlesitekit-user-menu__details-info__name"},n),e.createElement("p",{className:"googlesitekit-user-menu__details-info__email","aria-label":Object(i.__)("Email","google-site-kit")},o)))}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M6.83 2H20a2 2 0 012 2v12c0 .34-.09.66-.23.94L20 15.17V6h-9.17l-4-4zm13.66 19.31L17.17 18H4a2 2 0 01-2-2V4c0-.34.08-.66.23-.94L.69 1.51 2.1.1l19.8 19.8-1.41 1.41zM15.17 16l-10-10H4v10h11.17z",fill:"currentColor"});t.a=function SvgDisconnect(e){return i.createElement("svg",r({viewBox:"0 0 22 22",fill:"none"},e),a)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M20 0H2C.9 0 0 .9 0 2v12c0 1.1.9 2 2 2h5v2h8v-2h5c1.1 0 2-.9 2-2V2c0-1.1-.9-2-2-2zm0 14H2V2h18v12zm-2-9H7v2h11V5zm0 4H7v2h11V9zM6 5H4v2h2V5zm0 4H4v2h2V9z",fill:"currentColor"});t.a=function SvgManageSites(e){return i.createElement("svg",r({viewBox:"0 0 22 18",fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotifications}));var i=n(0),r=n(281),a=n(167),o=n(41);function ErrorNotifications(){return e.createElement(i.Fragment,null,e.createElement(r.a,null),e.createElement(a.a,{areaSlug:o.b.ERRORS}))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InternalServerError}));var i=n(3),r=n(13),a=n(196),o=n(191),c=n(111);function InternalServerError(){var t=Object(i.useSelect)((function(e){return e(r.c).getInternalServerError()}));return t?e.createElement(o.a,{className:"googlesitekit-publisher-win googlesitekit-publisher-win--win-error"},e.createElement(a.a,{title:t.title,description:e.createElement(c.a,{text:t.description})})):null}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return ViewedStateObserver}));var i=n(1),r=n.n(i),a=n(0),o=n(3),c=n(23),l=n(249),s=n(161);function ViewedStateObserver(e){var t=e.id,n=e.observeRef,i=e.threshold,r=Object(l.a)(n,{threshold:i}),u=Object(o.useDispatch)(c.b).setValue,d=!!(null==r?void 0:r.isIntersecting),g=Object(s.a)(t);return Object(a.useEffect)((function(){!g&&d&&u(s.a.getKey(t),!0)}),[g,d,u,t]),null}ViewedStateObserver.propTypes={id:r.a.string,observeRef:r.a.object,threshold:r.a.number}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return l}));var i=n(21),r=n.n(i),a=n(63),o=n.n(a),c=n(191),l=o()((function(e){return{id:e,Notification:s(e)(c.a)}}));function s(t){return function(n){function WithNotificationID(i){return e.createElement(n,r()({},i,{id:t}))}return WithNotificationID.displayName="WithNotificationID",(n.displayName||n.name)&&(WithNotificationID.displayName+="(".concat(n.displayName||n.name,")")),WithNotificationID}}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return DashboardNavigation}));var i=n(3),r=n(7),a=n(34),o=n(183),c=n(285);function DashboardNavigation(){var t=Object(a.a)(),n=Object(i.useSelect)((function(e){return t?e(r.a).getViewableModules():null})),l=Object(i.useSelect)((function(e){return e(r.a).getKeyMetrics()}));return e.createElement(o.a,{loading:void 0===n||void 0===l,width:"100%",smallHeight:"59px",height:"71px"},e.createElement(c.a,null))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return Navigation}));var r=n(27),a=n.n(r),o=n(15),c=n.n(o),l=n(11),s=n.n(l),u=n(14),d=n(81),g=n(153),f=n(0),m=n(2),p=n(3),h=n(286),b=n(287),v=n(288),E=n(289),_=n(290),O=n(22),k=n(7),y=n(47),j=n(23),S=n(71),w=n(52),C=n(24),A=n(93),N=n(9),T=n(18),x=n(34);function D(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return R(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return R(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var i=0,r=function(){};return{s:r,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function Navigation(){var t,n=Object(w.c)(),r=Object(f.useRef)(),o=Object(C.e)(),l=null===(t=e.location.hash)||void 0===t?void 0:t.substring(1),R=Object(f.useState)(l),M=c()(R,2),I=M[0],P=M[1],L=Object(f.useState)(l||void 0),B=c()(L,2),z=B[0],F=B[1],V=Object(f.useState)(!1),W=c()(V,2),H=W[0],U=W[1],q=Object(T.a)(),G=Object(x.a)(),K=Object(p.useDispatch)(j.b).setValue,Y=Object(p.useSelect)((function(e){return G?e(k.a).getViewableModules():null})),X=Object(p.useSelect)((function(e){return e(k.a).isKeyMetricsWidgetHidden()})),$={modules:Y||void 0},Z=Object(p.useSelect)((function(e){return n===w.b&&!0!==X&&e(y.a).isWidgetContextActive(S.CONTEXT_MAIN_DASHBOARD_KEY_METRICS,$)})),Q=Object(p.useSelect)((function(e){return e(y.a).isWidgetContextActive(n===w.b?S.CONTEXT_MAIN_DASHBOARD_TRAFFIC:S.CONTEXT_ENTITY_DASHBOARD_TRAFFIC,$)})),J=Object(p.useSelect)((function(e){return e(y.a).isWidgetContextActive(n===w.b?S.CONTEXT_MAIN_DASHBOARD_CONTENT:S.CONTEXT_ENTITY_DASHBOARD_CONTENT,$)})),ee=Object(p.useSelect)((function(e){return e(y.a).isWidgetContextActive(n===w.b?S.CONTEXT_MAIN_DASHBOARD_SPEED:S.CONTEXT_ENTITY_DASHBOARD_SPEED,$)})),te=Object(p.useSelect)((function(e){return e(y.a).isWidgetContextActive(n===w.b?S.CONTEXT_MAIN_DASHBOARD_MONETIZATION:S.CONTEXT_ENTITY_DASHBOARD_MONETIZATION,$)})),ne=Object(f.useCallback)((function(){return Z?O.b:G?Q?O.e:J?O.a:ee?O.d:te?O.c:"":O.e}),[Z,Q,J,ee,te,G]),ie=Object(f.useCallback)((function(t){var n,i=t.target.closest(".mdc-chip"),r=null==i||null===(n=i.dataset)||void 0===n?void 0:n.contextId;e.history.replaceState({},"","#".concat(r)),F(r),Object(N.I)("".concat(q,"_navigation"),"tab_select",r),e.scrollTo({top:r!==ne()?Object(A.a)("#".concat(r),o):0,behavior:"smooth"}),setTimeout((function(){K(j.a,r)}),50)}),[o,q,K,ne]);return Object(d.a)((function(){var t=ne();if(!l)return P(t),void setTimeout((function(){return e.history.replaceState({},"","#".concat(t))}));var n=l;(function(e){return!(!Z||e!==O.b)||(!(!Q||e!==O.e)||(!(!J||e!==O.a)||(!(!ee||e!==O.d)||!(!te||e!==O.c))))})(n)||(n=t),K(j.a,n),P(n),setTimeout((function(){var i=n!==t?Object(A.a)("#".concat(n),o):0;e.scrollY!==i?e.scrollTo({top:i,behavior:"smooth"}):K(j.a,void 0)}),50)})),Object(f.useEffect)((function(){var t=function(e){K(j.a,void 0),P(e),F(void 0)},n=Object(u.throttle)((function(n){var i,o,c,l,s=e.scrollY,u=null===(i=document.querySelector(".googlesitekit-entity-header"))||void 0===i||null===(o=i.getBoundingClientRect())||void 0===o?void 0:o.bottom,d=null==r||null===(c=r.current)||void 0===c?void 0:c.getBoundingClientRect(),g=d.bottom,f=d.top,m=[].concat(a()(Z?[O.b]:[]),a()(Q?[O.e]:[]),a()(J?[O.a]:[]),a()(ee?[O.d]:[]),a()(te?[O.c]:[])),p=ne();if(0===s)U(!1);else{var h,b=null===(h=document.querySelector(".googlesitekit-header"))||void 0===h?void 0:h.getBoundingClientRect().bottom;U(f===b)}var v,E=D(m);try{for(E.s();!(v=E.n()).done;){var _=v.value,k=document.getElementById(_);if(k){var y=k.getBoundingClientRect().top-20-(u||g||0);y<0&&(void 0===l||l<y)&&(l=y,p=_)}}}catch(e){E.e(e)}finally{E.f()}if(z)z===p&&t(p);else{var j=e.location.hash;p!==(null==j?void 0:j.substring(1))&&(n&&Object(N.I)("".concat(q,"_navigation"),"tab_scroll",p),e.history.replaceState({},"","#".concat(p)),t(p))}}),150);return e.addEventListener("scroll",n),function(){e.removeEventListener("scroll",n)}}),[z,Z,Q,J,ee,te,q,K,ne]),i.createElement("nav",{className:s()("mdc-chip-set","googlesitekit-navigation","googlesitekit-navigation--".concat(n),{"googlesitekit-navigation--is-sticky":H}),ref:r},Z&&i.createElement(g.Chip,{id:O.b,label:Object(m.__)("Key metrics","google-site-kit"),leadingIcon:i.createElement(h.a,{width:"18",height:"16"}),onClick:ie,selected:I===O.b,"data-context-id":O.b}),Q&&i.createElement(g.Chip,{id:O.e,label:Object(m.__)("Traffic","google-site-kit"),leadingIcon:i.createElement(b.a,{width:"18",height:"16"}),onClick:ie,selected:I===O.e,"data-context-id":O.e}),J&&i.createElement(g.Chip,{id:O.a,label:Object(m.__)("Content","google-site-kit"),leadingIcon:i.createElement(v.a,{width:"18",height:"18"}),onClick:ie,selected:I===O.a,"data-context-id":O.a}),ee&&i.createElement(g.Chip,{id:O.d,label:Object(m.__)("Speed","google-site-kit"),leadingIcon:i.createElement(E.a,{width:"20",height:"16"}),onClick:ie,selected:I===O.d,"data-context-id":O.d}),te&&i.createElement(g.Chip,{id:O.c,label:Object(m.__)("Monetization","google-site-kit"),leadingIcon:i.createElement(_.a,{width:"18",height:"16"}),onClick:ie,selected:I===O.c,"data-context-id":O.c}))}}).call(this,n(28),n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("rect",{x:.5,width:5,height:5,rx:1,fill:"currentColor"}),o=i.createElement("rect",{x:7.5,width:5,height:5,rx:1,fill:"currentColor"}),c=i.createElement("rect",{x:.5,y:7,width:5,height:5,rx:1,fill:"currentColor"}),l=i.createElement("rect",{x:7.5,y:7,width:5,height:5,rx:1,fill:"currentColor"});t.a=function SvgNavKeyMetricsIcon(e){return i.createElement("svg",r({viewBox:"0 0 13 12",fill:"none"},e),a,o,c,l)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 0h3.971v16H7V0zM0 8h4v8H0V8zm18-3h-4v11h4V5z",fill:"currentColor"});t.a=function SvgNavTrafficIcon(e){return i.createElement("svg",r({viewBox:"0 0 18 16",fill:"none"},e),a)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 16V2c0-1.1-1-2-2.222-2H2.222C1 0 0 .9 0 2v14c0 1.1 1 2 2.222 2h13.556C17 18 18 17.1 18 16zM9 7h5V5H9v2zm7-5H2v14h14V2zM4 4h4v4H4V4zm10 7H9v2h5v-2zM4 10h4v4H4v-4z",fill:"currentColor"});t.a=function SvgNavContentIcon(e){return i.createElement("svg",r({viewBox:"0 0 18 18",fill:"none"},e),a)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M18.378 4.543l-1.232 1.854a8.024 8.024 0 01-.22 7.598H3.043A8.024 8.024 0 014.154 4.49 8.011 8.011 0 0113.57 2.82l1.853-1.233A10.01 10.01 0 003.117 2.758a10.026 10.026 0 00-1.797 12.24A2.004 2.004 0 003.043 16h13.873a2.003 2.003 0 001.742-1.002 10.03 10.03 0 00-.27-10.465l-.01.01z",fill:"currentColor"}),o=i.createElement("path",{d:"M8.572 11.399a2.003 2.003 0 002.835 0l5.669-8.51-8.504 5.673a2.005 2.005 0 000 2.837z",fill:"currentColor"});t.a=function SvgNavSpeedIcon(e){return i.createElement("svg",r({viewBox:"0 0 20 16",fill:"none"},e),a,o)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M16.1 0v2h2.967l-5.946 5.17-4.6-4L0 10.59 1.621 12l6.9-6 4.6 4L20.7 3.42V6H23V0h-6.9z",fill:"currentColor"});t.a=function SvgNavMonetizationIcon(e){return i.createElement("svg",r({viewBox:"0 0 23 12",fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e,i){var r=n(15),a=n.n(r),o=n(14),c=n(2),l=n(0),s=n(3),u=n(10),d=n(13),g=n(292),f=n(32),m=n(20),p=n(80),h=n(9),b=n(52),v=n(18);t.a=function EntityHeader(){var t=Object(v.a)(),n=Object(b.c)(),r=Object(s.useSelect)((function(e){return e(d.c).getCurrentEntityTitle()})),E=Object(s.useSelect)((function(e){return e(d.c).getCurrentEntityURL()})),_=Object(l.useRef)(),O=Object(l.useState)(E),k=a()(O,2),y=k[0],j=k[1];Object(l.useEffect)((function(){var t=function(){if(_.current){var t=_.current.clientWidth-40,n=e.getComputedStyle(_.current.lastChild,null).getPropertyValue("font-size"),i=2*t/parseFloat(n);j(Object(p.d)(E,i))}},n=Object(o.throttle)(t,100);return t(),e.addEventListener("resize",n),function(){e.removeEventListener("resize",n)}}),[E,_,j]);var S=Object(s.useDispatch)(f.a).navigateTo,w=Object(s.useSelect)((function(e){return e(d.c).getAdminURL("googlesitekit-dashboard")})),C=Object(l.useCallback)((function(){Object(h.I)("".concat(t,"_navigation"),"return_to_dashboard"),S(w)}),[w,S,t]);return b.a!==n||null===E||null===r?null:i.createElement("div",{className:"googlesitekit-entity-header"},i.createElement("div",{className:"googlesitekit-entity-header__back"},i.createElement(u.Button,{icon:i.createElement(g.a,{width:24,height:24}),"aria-label":Object(c.__)("Back to dashboard","google-site-kit"),onClick:C,text:!0,tertiary:!0},Object(c.__)("Back to dashboard","google-site-kit"))),i.createElement("div",{ref:_,className:"googlesitekit-entity-header__details"},i.createElement("p",null,r),i.createElement(m.a,{secondary:!0,href:E,"aria-label":E,external:!0},y)))}}).call(this,n(28),n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=i.createElement("path",{d:"M21 11H6.83l3.58-3.59L9 6l-6 6 6 6 1.41-1.41L6.83 13H21z",fill:"currentColor"});t.a=function SvgKeyboardBackspace(e){return i.createElement("svg",r({viewBox:"0 0 24 24"},e),a,o)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ViewOnlyMenu}));var i=n(15),r=n.n(i),a=n(209),o=n(11),c=n.n(o),l=n(0),s=n(2),u=n(56),d=n(10),g=n(18),f=n(112),m=n(9),p=n(294),h=n(295),b=n(296),v=n(298),E=n(3),_=n(7);function ViewOnlyMenu(){var t=Object(l.useState)(!1),n=r()(t,2),i=n[0],o=n[1],O=Object(l.useRef)(),k=Object(g.a)();Object(a.a)(O,(function(){return o(!1)})),Object(f.a)([u.c,u.f],O,(function(){return o(!1)}));var y=Object(l.useCallback)((function(){i||Object(m.I)("".concat(k,"_headerbar"),"open_viewonly"),o(!i)}),[i,k]),j=Object(E.useSelect)((function(e){return e(_.a).hasCapability(_.H)}));return e.createElement("div",{ref:O,className:c()("googlesitekit-view-only-menu","googlesitekit-dropdown-menu","googlesitekit-dropdown-menu__icon-menu","mdc-menu-surface--anchor",{"googlesitekit-view-only-menu--user-can-authenticate":j})},e.createElement(d.Button,{className:"googlesitekit-header__dropdown mdc-button--dropdown googlesitekit-border-radius-round--phone googlesitekit-button-icon",text:!0,onClick:y,icon:e.createElement("span",{className:"mdc-button__icon","aria-hidden":"true"},e.createElement(p.a,{className:"mdc-button__icon--image"})),"aria-haspopup":"menu","aria-expanded":i,"aria-controls":"view-only-menu","aria-label":Object(s.__)("View only","google-site-kit"),tooltip:!0,tooltipEnterDelayInMS:500},Object(s.__)("View only","google-site-kit")),e.createElement(d.Menu,{menuOpen:i,nonInteractive:!0,onSelected:y,id:"view-only-menu"},e.createElement(h.a,null),e.createElement(b.a,null),e.createElement("li",{className:"mdc-list-divider",role:"separator"}),e.createElement(v.a,null)))}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M8 1.333c2.756 0 5.214 1.42 6.415 3.667-1.2 2.247-3.659 3.667-6.415 3.667-2.756 0-5.215-1.42-6.415-3.667C2.785 2.753 5.244 1.333 8 1.333zM8 0C4.364 0 1.258 2.073 0 5c1.258 2.927 4.364 5 8 5s6.742-2.073 8-5c-1.258-2.927-4.364-5-8-5zm0 3.333c1.004 0 1.818.747 1.818 1.667S9.004 6.667 8 6.667 6.182 5.92 6.182 5 6.996 3.333 8 3.333zM8 2C6.196 2 4.727 3.347 4.727 5S6.197 8 8 8c1.804 0 3.273-1.347 3.273-3S9.803 2 8 2z",fill:"currentColor"});t.a=function SvgView(e){return i.createElement("svg",r({viewBox:"0 0 16 10",fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Description}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(0),l=n(38),s=n(2),u=n(3),d=n(10),g=n(32),f=n(13),m=n(7),p=n(9),h=n(20),b=n(18),v=n(37);function Description(){var t=Object(b.a)(),n=Object(u.useSelect)((function(e){return e(m.a).hasCapability(m.H)})),i=Object(u.useSelect)((function(e){return e(f.c).getProxySetupURL()})),a=Object(u.useSelect)((function(e){return e(f.c).getDocumentationLinkURL("dashboard-sharing")})),E=Object(u.useDispatch)(g.a).navigateTo,_=Object(c.useCallback)(function(){var e=o()(r.a.mark((function e(n){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.preventDefault(),e.next=3,Promise.all([Object(v.f)("start_user_setup",!0),Object(p.I)("".concat(t,"_headerbar_viewonly"),"start_user_setup",i?"proxy":"custom-oauth")]);case 3:E(i);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[i,E,t]),O=Object(c.useCallback)((function(){Object(p.I)("".concat(t,"_headerbar_viewonly"),"click_learn_more_link")}),[t]),k=n?Object(l.a)(Object(s.__)("You can see stats from all shared Google services, but you can't make any changes. <strong>Sign in to connect more services and control sharing access.</strong>","google-site-kit"),{strong:e.createElement("strong",null)}):Object(l.a)(Object(s.__)("You can see stats from all shared Google services, but you can't make any changes. <a>Learn more</a>","google-site-kit"),{a:e.createElement(h.a,{href:a,external:!0,onClick:O,"aria-label":Object(s.__)("Learn more about dashboard sharing","google-site-kit")})});return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item googlesitekit-view-only-menu__description"},e.createElement("p",null,k),n&&e.createElement(d.Button,{onClick:_},Object(s._x)("Sign in with Google","Service name","google-site-kit")))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SharedServices}));var i=n(2),r=n(3),a=n(7),o=n(297);function SharedServices(){var t=Object(r.useSelect)((function(e){return e(a.a).getViewableModules()}));return void 0===t?null:e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("h4",null,Object(i.__)("Shared services","google-site-kit")),e.createElement("ul",null,t.map((function(t){return e.createElement(o.a,{key:t,module:t})}))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Service}));var i=n(1),r=n.n(i),a=n(38),o=n(2),c=n(3),l=n(19),s=n(7);function Service(t){var n=t.module,i=Object(c.useSelect)((function(e){return e(s.a).hasCapability(s.H)})),r=Object(c.useSelect)((function(e){return e(l.a).getModule(n)||{}})),u=r.name,d=r.owner,g=Object(c.useSelect)((function(e){return e(l.a).getModuleIcon(n)}));return e.createElement("li",{className:"googlesitekit-view-only-menu__service"},e.createElement("span",{className:"googlesitekit-view-only-menu__service--icon"},e.createElement(g,{height:26})),e.createElement("span",{className:"googlesitekit-view-only-menu__service--name"},u),i&&(null==d?void 0:d.login)&&e.createElement("span",{className:"googlesitekit-view-only-menu__service--owner"},Object(a.a)(Object(o.sprintf)(
/* translators: %s: module owner Google Account email address */
Object(o.__)("Shared by <strong>%s</strong>","google-site-kit"),d.login),{strong:e.createElement("strong",{title:d.login})})))}Service.propTypes={module:r.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Tracking}));var i=n(38),r=n(2),a=n(218),o=n(18);function Tracking(){var t=Object(o.a)();return e.createElement("li",{className:"googlesitekit-view-only-menu__list-item"},e.createElement("p",null,Object(i.a)(Object(r.__)("Thanks for using Site Kit!<br />Help us make it even better","google-site-kit"),{br:e.createElement("br",null)})),e.createElement(a.a,{trackEventCategory:"".concat(t,"_headerbar_viewonly"),alignCheckboxLeft:!0}))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SubtleNotifications}));var i=n(167),r=n(41);function SubtleNotifications(){return e.createElement(i.a,{areaSlug:r.b.BANNERS_BELOW_NAV})}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(0),l=n(3),s=n(13),u=n(18),d=n(37),g=n(9),f=function(){var e=Object(u.a)(),t=Object(l.useSelect)((function(e){return e(s.c).isUsingProxy()})),n=Object(l.useSelect)((function(e){return e(s.c).getSetupErrorMessage()}));Object(c.useEffect)((function(){n||void 0===t||function(){var n=o()(r.a.mark((function n(){var i,a;return r.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,Object(d.d)("start_user_setup");case 2:return i=n.sent,n.next=5,Object(d.d)("start_site_setup");case 5:if(a=n.sent,!i.cacheHit){n.next=10;break}return n.next=9,Object(d.c)("start_user_setup");case 9:Object(g.I)("".concat(e,"_setup"),"complete_user_setup",t?"proxy":"custom-oauth");case 10:if(!a.cacheHit){n.next=14;break}return n.next=13,Object(d.c)("start_site_setup");case 13:Object(g.I)("".concat(e,"_setup"),"complete_site_setup",t?"proxy":"custom-oauth");case 14:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}()()}),[e,t,n])}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M9 16h2v-2H9v2zm1-16C4.48 0 0 4.48 0 10s4.48 10 10 10 10-4.48 10-10S15.52 0 10 0zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14C7.79 4 6 5.79 6 8h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z",fill:"currentColor"});t.a=function SvgHelp(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(0),l=n(2),s=n(10),u=n(77),d=n(20);function NewBadge(t){var n=t.tooltipTitle,i=t.learnMoreLink,r=t.forceOpen,a=t.hasLeftSpacing,g=t.hasNoSpacing,f=t.onLearnMoreClick,m=void 0===f?function(){}:f,p=e.createElement(u.a,{className:o()("googlesitekit-new-badge",{"googlesitekit-new-badge--has-no-spacing":g}),label:Object(l.__)("New","google-site-kit"),hasLeftSpacing:a});return n?e.createElement(s.Tooltip,{tooltipClassName:"googlesitekit-new-badge__tooltip",title:e.createElement(c.Fragment,null,n,e.createElement("br",null),e.createElement(d.a,{href:i,onClick:m,external:!0,hideExternalIndicator:!0},Object(l.__)("Learn more","google-site-kit"))),placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,interactive:!0,open:r},p):p}NewBadge.propTypes={tooltipTitle:r.a.string,learnMoreLink:r.a.string,forceOpen:r.a.bool,onLearnMoreClick:r.a.func,hasLeftSpacing:r.a.bool,hasNoSpacing:r.a.bool},t.a=NewBadge}).call(this,n(4))},,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileMetric}));var i=n(1),r=n.n(i),a=n(9);function AudienceTileMetric(t){var n=t.TileIcon,i=t.title,r=t.metricValue,o=t.Badge,c=t.metricValueFormat;return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__icon"},e.createElement(n,null)),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__container"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__value"},Object(a.B)(r,c)),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__title"},i)),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__badge-container"},e.createElement(o,null)))}AudienceTileMetric.propTypes={TileIcon:r.a.elementType.isRequired,title:r.a.string.isRequired,metricValue:r.a.number.isRequired,Badge:r.a.elementType.isRequired,metricValueFormat:r.a.object}}).call(this,n(4))},,,,function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.149 7.96l-5.166 5.166a.344.344 0 00-.094.176l-.35 1.755a.344.344 0 00.404.404l1.755-.35a.344.344 0 00.175-.095l5.166-5.165-1.89-1.89zm2.301-1.814a1.031 1.031 0 00-1.458 0L6.497 12.64a1.031 1.031 0 00-.282.527l-.35 1.755a1.031 1.031 0 001.213 1.213l1.754-.35c.2-.04.383-.139.527-.283l6.495-6.494a1.031 1.031 0 000-1.459L14.45 6.146z"}),o=i.createElement("path",{d:"M12.149 7.96l.117-.116a.165.165 0 00-.234 0l.117.117zm-5.166 5.166l-.116-.116.116.116zm-.094.176l.162.033-.162-.033zm-.35 1.755l.161.032-.162-.032zm.404.404l.032.162-.032-.162zm1.755-.35l.032.161-.032-.162zm.175-.095l.117.117-.117-.117zm5.166-5.165l.116.116a.165.165 0 000-.233l-.116.117zm-1.047-3.705l.116.116-.116-.116zm1.458 0l-.116.116.116-.116zM6.497 12.64l.117.117-.117-.117zm-.282.527l-.162-.032.162.032zm-.35 1.755l.161.032-.162-.032zm1.213 1.213l-.033-.162.033.162zm1.754-.35l.033.161-.033-.162zm.527-.283l.117.117-.117-.117zm6.495-6.494l-.117-.117.117.117zm0-1.459l.117-.116-.117.116zm-3.822.295L6.867 13.01l.233.233 5.166-5.165-.234-.234zM6.867 13.01a.509.509 0 00-.14.26l.324.065a.18.18 0 01.05-.092l-.234-.233zm-.14.26l-.35 1.754.323.065.351-1.755-.323-.064zm-.35 1.754a.509.509 0 00.598.599l-.064-.324a.179.179 0 01-.21-.21l-.324-.065zm.598.599l1.755-.35-.065-.325-1.754.351.064.324zm1.755-.35a.508.508 0 00.26-.14l-.233-.233a.18.18 0 01-.092.048l.065.324zm.26-.14l5.165-5.166-.233-.233L8.757 14.9l.233.233zm3.042-7.055l1.89 1.89.233-.234-1.89-1.89-.233.234zm1.076-1.816a.866.866 0 011.226 0l.233-.233a1.196 1.196 0 00-1.692 0l.233.233zm-6.494 6.495l6.494-6.495-.233-.233-6.494 6.495.233.233zm-.237.443a.866.866 0 01.237-.443l-.233-.233c-.167.167-.281.38-.328.61l.324.066zm-.35 1.754l.35-1.754-.324-.065-.35 1.755.323.064zm1.018 1.02a.866.866 0 01-1.019-1.02l-.323-.065a1.196 1.196 0 001.407 1.408l-.065-.324zm1.755-.351l-1.755.35.065.324 1.755-.35-.065-.324zm.443-.237a.866.866 0 01-.443.237l.065.323c.231-.046.444-.16.611-.327l-.233-.233zm6.494-6.495l-6.494 6.495.233.233 6.495-6.494-.234-.234zm0-1.225a.866.866 0 010 1.225l.234.234a1.196 1.196 0 000-1.692l-.234.233zm-1.403-1.404l1.403 1.404.234-.233-1.404-1.404-.233.233z"});t.a=function SvgPencilAlt(e){return i.createElement("svg",r({viewBox:"0 0 22 22",fill:"currentColor"},e),a,o)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{fill:"currentColor",fillRule:"evenodd"},i.createElement("path",{d:"M0 6.414L1.415 5l5.292 5.292-1.414 1.415z"}),i.createElement("path",{d:"M14.146.146l1.415 1.414L5.414 11.707 4 10.292z"}));t.a=function SvgConnected(e){return i.createElement("svg",r({viewBox:"0 0 16 12"},e),a)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{fill:"currentColor",fillRule:"evenodd"},i.createElement("path",{d:"M0 0h2v7H0zM0 10h2v2H0z"}));t.a=function SvgExclamation(e){return i.createElement("svg",r({viewBox:"0 0 2 12"},e),a)}},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelHeader}));var i=n(1),r=n.n(i),a=n(20),o=n(104);function SelectionPanelHeader(t){var n=t.children,i=t.title,r=t.onCloseClick;return e.createElement("header",{className:"googlesitekit-selection-panel-header"},e.createElement("div",{className:"googlesitekit-selection-panel-header__row"},e.createElement("h3",null,i),e.createElement(a.a,{className:"googlesitekit-selection-panel-header__close",onClick:r,linkButton:!0},e.createElement(o.a,{width:"15",height:"15"}))),n)}SelectionPanelHeader.propTypes={children:r.a.node,title:r.a.string,onCloseClick:r.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelItem}));var i=n(1),r=n.n(i),a=n(2),o=n(328),c=n(77);function SelectionPanelItem(t){var n=t.children,i=t.id,r=t.slug,l=t.title,s=t.description,u=t.isItemSelected,d=t.isItemDisabled,g=t.onCheckboxChange,f=t.subtitle,m=t.suffix,p=t.badge,h=t.isNewlyDetected;return e.createElement("div",{className:"googlesitekit-selection-panel-item"},e.createElement(o.a,{badge:p,checked:u,disabled:d,id:i,onChange:g,title:l,value:r},f&&e.createElement("span",{className:"googlesitekit-selection-panel-item__subtitle"},f),s,n),h&&e.createElement(c.a,{label:Object(a.__)("New","google-site-kit")}),m&&e.createElement("span",{className:"googlesitekit-selection-panel-item__suffix"},m))}SelectionPanelItem.propTypes={children:r.a.node,id:r.a.string,slug:r.a.string,title:r.a.string,description:r.a.string,isItemSelected:r.a.bool,isItemDisabled:r.a.bool,onCheckboxChange:r.a.func,subtitle:r.a.string,suffix:r.a.node,badge:r.a.node,isNewlyDetected:r.a.bool}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelItems}));var i=n(21),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=n(2);function SelectionPanelItems(t){var n=t.currentSelectionTitle,i=void 0===n?Object(l.__)("Current selection","google-site-kit"):n,a=t.availableItemsTitle,o=void 0===a?Object(l.__)("Additional items","google-site-kit"):a,s=t.savedItemSlugs,u=void 0===s?[]:s,d=t.availableSavedItems,g=void 0===d?{}:d,f=t.availableUnsavedItems,m=void 0===f?{}:f,p=t.ItemComponent,h=t.notice,b=function(t){return Object.keys(t).map((function(n){return e.createElement(p,r()({key:n,slug:n,savedItemSlugs:u},t[n]))}))},v=Object.keys(m).length;return e.createElement("div",{className:"googlesitekit-selection-panel-items"},0!==u.length&&e.createElement(c.Fragment,null,e.createElement("p",{className:"googlesitekit-selection-panel-items__subheading"},i),e.createElement("div",{className:"googlesitekit-selection-panel-items__subsection"},b(g)),v>0&&e.createElement("p",{className:"googlesitekit-selection-panel-items__subheading"},o)),v>0&&e.createElement("div",{className:"googlesitekit-selection-panel-items__subsection"},b(m)),h)}SelectionPanelItems.propTypes={currentSelectionTitle:o.a.string,availableItemsTitle:o.a.string,savedItemSlugs:o.a.array,availableSavedItems:o.a.object,availableUnsavedItems:o.a.object,ItemComponent:o.a.elementType,notice:o.a.node}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelFooter}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(14),u=n(1),d=n.n(u),g=n(0),f=n(38),m=n(2),p=n(3),h=n(10),b=n(120),v=n(9),E=n(8),_=n(44),O=n(54);function SelectionPanelFooter(t){var n=t.savedItemSlugs,i=void 0===n?[]:n,a=t.selectedItemSlugs,c=void 0===a?[]:a,u=t.saveSettings,d=void 0===u?function(){}:u,k=t.saveError,y=t.itemLimitError,j=t.minSelectedItemCount,S=void 0===j?0:j,w=t.maxSelectedItemCount,C=void 0===w?0:w,A=t.isBusy,N=t.onSaveSuccess,T=void 0===N?function(){}:N,x=t.onCancel,D=void 0===x?function(){}:x,R=t.isOpen,M=t.closePanel,I=void 0===M?function(){}:M,P=Object(g.useState)(null),L=l()(P,2),B=L[0],z=L[1],F=Object(g.useState)(!1),V=l()(F,2),W=V[0],H=V[1],U=Object(p.useSelect)((function(e){return e(E.r).isFetchingSyncAvailableAudiences()})),q=Object(g.useMemo)((function(){return!Object(s.isEqual)(Object(v.E)(c),Object(v.E)(i))}),[i,c]),G=(null==i?void 0:i.length)>0&&q?Object(m.__)("Apply changes","google-site-kit"):Object(m.__)("Save selection","google-site-kit"),K=Object(g.useCallback)(o()(r.a.mark((function e(){var t;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,d(c);case 2:t=e.sent,t.error||(T(),I(),z(G),H(!0));case 5:case"end":return e.stop()}}),e)}))),[d,c,T,I,G]),Y=Object(g.useCallback)((function(){I(),D()}),[I,D]),X=Object(g.useState)(null),$=l()(X,2),Z=$[0],Q=$[1];Object(g.useEffect)((function(){null!==Z&&Z!==R&&R&&(z(null),H(!1)),Q(R)}),[R,Z]);var J=(null==c?void 0:c.length)||0,ee=U?e.createElement(_.a,{width:"89px",height:"20px"}):e.createElement("p",{className:"googlesitekit-selection-panel-footer__item-count"},Object(f.a)(Object(m.sprintf)(
/* translators: 1: Number of selected items. 2: Maximum number of items that can be selected. */
Object(m.__)("%1$d selected <MaxCount>(up to %2$d)</MaxCount>","google-site-kit"),J,C),{MaxCount:e.createElement("span",{className:"googlesitekit-selection-panel-footer__item-count--max-count"})}));return e.createElement("footer",{className:"googlesitekit-selection-panel-footer"},k&&e.createElement(b.a,{error:k}),e.createElement("div",{className:"googlesitekit-selection-panel-footer__content"},q&&y?e.createElement(O.a,{noPrefix:!0,message:y}):ee,e.createElement("div",{className:"googlesitekit-selection-panel-footer__actions"},e.createElement(h.Button,{tertiary:!0,onClick:Y,disabled:A},Object(m.__)("Cancel","google-site-kit")),e.createElement(h.SpinnerButton,{onClick:K,isSaving:A,disabled:J<S||J>C||A||!R&&W},B||G))))}SelectionPanelFooter.propTypes={savedItemSlugs:d.a.array,selectedItemSlugs:d.a.array,saveSettings:d.a.func,saveError:d.a.object,itemLimitError:d.a.string,minSelectedItemCount:d.a.number,maxSelectedItemCount:d.a.number,isBusy:d.a.bool,onSaveSuccess:d.a.func,onCancel:d.a.func,isOpen:d.a.bool,closePanel:d.a.func}}).call(this,n(4))},,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportZero}));var i=n(6),r=n.n(i),a=n(21),o=n.n(a),c=n(25),l=n.n(c),s=n(1),u=n.n(s),d=n(0),g=n(143),f=n(174);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function WidgetReportZero(t){var n=t.widgetSlug,i=t.moduleSlug,r=l()(t,["widgetSlug","moduleSlug"]),a=Object(d.useMemo)((function(){return{moduleSlug:i}}),[i]);return Object(g.a)(n,f.a,a),e.createElement(f.a,o()({moduleSlug:i},r))}WidgetReportZero.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:u.a.string.isRequired},f.a.propTypes)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportError}));var i=n(6),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(170);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function WidgetReportError(t){t.widgetSlug;var n=o()(t,["widgetSlug"]);return e.createElement(s.a,n)}WidgetReportError.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:l.a.string.isRequired},s.a.propTypes)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WPDashboardReportError}));var i=n(1),r=n.n(i),a=n(550),o=n(208),c=n(3),l=n(23),s=n(170);function WPDashboardReportError(t){var n=t.moduleSlug,i=t.error,r=Object(o.a)(WPDashboardReportError,"WPDashboardReportError"),u=Object(c.useDispatch)(l.b).setValue,d=i.message,g=Object(c.useSelect)((function(e){return e(l.b).getValue("WPDashboardReportError-".concat(n,"-").concat(d))}));return Object(a.a)((function(){u("WPDashboardReportError-".concat(n,"-").concat(d),r)}),(function(){u("WPDashboardReportError-".concat(n,"-").concat(d),void 0)})),g!==r?null:e.createElement(s.a,{moduleSlug:n,error:i})}WPDashboardReportError.propTypes={moduleSlug:r.a.string.isRequired,error:r.a.object.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M7.334 11.333h1.333v-4H7.334v4zM8.001 6a.658.658 0 00.667-.667.605.605 0 00-.2-.467.605.605 0 00-.467-.2.658.658 0 00-.667.667c0 .189.061.35.183.483A.69.69 0 008.001 6zm0 8.666a6.583 6.583 0 01-2.6-.516 6.85 6.85 0 01-2.117-1.434A6.85 6.85 0 011.851 10.6 6.582 6.582 0 011.334 8c0-.923.172-1.79.517-2.6a6.85 6.85 0 011.433-2.117c.6-.6 1.306-1.072 2.117-1.417A6.404 6.404 0 018 1.333c.922 0 1.789.178 2.6.533a6.618 6.618 0 012.116 1.417c.6.6 1.072 1.306 1.417 2.117.355.81.533 1.677.533 2.6 0 .922-.178 1.789-.533 2.6a6.619 6.619 0 01-1.417 2.116 6.85 6.85 0 01-2.116 1.434 6.583 6.583 0 01-2.6.516zm0-1.333c1.489 0 2.75-.517 3.783-1.55s1.55-2.294 1.55-3.783c0-1.49-.517-2.75-1.55-3.784-1.033-1.033-2.294-1.55-3.783-1.55-1.49 0-2.75.517-3.784 1.55C3.184 5.25 2.667 6.511 2.667 8c0 1.489.517 2.75 1.55 3.783 1.034 1.033 2.295 1.55 3.784 1.55z",fill:"currentColor"});t.a=function SvgInfoGreen(e){return i.createElement("svg",r({viewBox:"0 0 16 16",fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanel}));var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(327);function SelectionPanel(t){var n=t.children,i=t.isOpen,a=t.isLoading,o=t.onOpen,l=t.closePanel,s=t.className,u=null==s?void 0:s.split(/\s+/).map((function(e){return".".concat(e)})).join(""),d=u?"".concat(u," .googlesitekit-selection-panel-item .googlesitekit-selection-box input"):".googlesitekit-selection-panel-item .googlesitekit-selection-box input";return e.createElement(c.a,{className:r()("googlesitekit-selection-panel",s),isOpen:i,isLoading:a,onOpen:o,closeSheet:l,focusTrapOptions:{initialFocus:d}},n)}SelectionPanel.propTypes={children:o.a.node,isOpen:o.a.bool,isLoading:o.a.bool,onOpen:o.a.func,closePanel:o.a.func,className:o.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SideSheet}));var i=n(6),r=n.n(i),a=n(11),o=n.n(a),c=n(405),l=n.n(c),s=n(1),u=n.n(s),d=n(209),g=n(392),f=n(0),m=n(56),p=n(72);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function SideSheet(t){var n=t.className,i=t.children,r=t.isOpen,a=t.isLoading,c=t.onOpen,s=void 0===c?function(){}:c,u=t.closeSheet,h=void 0===u?function(){}:u,v=t.focusTrapOptions,E=void 0===v?{}:v,_=Object(f.useRef)();return Object(f.useEffect)((function(){r?(s(),document.body.classList.add("googlesitekit-side-sheet-scroll-lock")):document.body.classList.remove("googlesitekit-side-sheet-scroll-lock")}),[r,s]),Object(d.a)(_,h),Object(g.a)((function(e){return r&&m.c===e.keyCode}),h),e.createElement(p.a,null,e.createElement(l.a,{active:!!r&&!a,focusTrapOptions:b({fallbackFocus:"body"},E)},e.createElement("section",{ref:_,className:o()("googlesitekit-side-sheet",n,{"googlesitekit-side-sheet--open":r}),role:"dialog","aria-modal":"true","aria-hidden":!r,tabIndex:"0"},i)),r&&e.createElement("span",{className:"googlesitekit-side-sheet-overlay"}))}SideSheet.propTypes={className:u.a.string,children:u.a.node,isOpen:u.a.bool,isLoading:u.a.bool,onOpen:u.a.func,closeSheet:u.a.func,focusTrapOptions:u.a.object}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionBox}));var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(10);function SelectionBox(t){var n=t.badge,i=t.checked,r=t.children,a=t.disabled,l=t.id,s=t.onChange,u=t.title,d=t.value;return e.createElement("div",{className:o()("googlesitekit-selection-box",{"googlesitekit-selection-box--disabled":a})},e.createElement(c.Checkbox,{checked:i,description:r,disabled:a,id:l,name:l,onChange:s,value:d,badge:n},u))}SelectionBox.propTypes={badge:r.a.node,checked:r.a.bool,children:r.a.node,disabled:r.a.bool,id:r.a.string,onChange:r.a.func,title:r.a.string,value:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(51),r=n.n(i),a=n(53),o=n.n(a),c=n(68),l=n.n(c),s=n(69),u=n.n(s),d=n(49),g=n.n(d),f=n(1),m=n.n(f),p=n(0),h=n(17),b=n(20);function v(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=g()(e);if(t){var r=g()(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return u()(this,n)}}var E=function(t){l()(LayoutHeader,t);var n=v(LayoutHeader);function LayoutHeader(){return r()(this,LayoutHeader),n.apply(this,arguments)}return o()(LayoutHeader,[{key:"render",value:function(){var t=this.props,n=t.title,i=t.badge,r=t.ctaLabel,a=t.ctaLink,o=a?{alignMiddle:!0,smSize:4,lgSize:6}:{alignMiddle:!0,smSize:4,mdSize:8,lgSize:12};return e.createElement("header",{className:"googlesitekit-layout__header"},e.createElement(h.e,null,e.createElement(h.k,null,n&&e.createElement(h.a,o,e.createElement("h3",{className:"googlesitekit-subheading-1 googlesitekit-layout__header-title"},n,i)),a&&e.createElement(h.a,{alignMiddle:!0,mdAlignRight:!0,smSize:4,lgSize:6},e.createElement(b.a,{href:a,external:!0},r)))))}}]),LayoutHeader}(p.Component);E.propTypes={title:m.a.string,badge:m.a.node,ctaLabel:m.a.string,ctaLink:m.a.string},E.defaultProps={title:"",badge:null,ctaLabel:"",ctaLink:""},t.a=E}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(51),r=n.n(i),a=n(53),o=n.n(a),c=n(68),l=n.n(c),s=n(69),u=n.n(s),d=n(49),g=n.n(d),f=n(1),m=n.n(f),p=n(0),h=n(17),b=n(134);function v(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=g()(e);if(t){var r=g()(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return u()(this,n)}}var E=function(t){l()(LayoutFooter,t);var n=v(LayoutFooter);function LayoutFooter(){return r()(this,LayoutFooter),n.apply(this,arguments)}return o()(LayoutFooter,[{key:"render",value:function(){var t=this.props,n=t.ctaLabel,i=t.ctaLink,r=t.footerContent;return e.createElement("footer",{className:"googlesitekit-layout__footer"},e.createElement(h.e,null,e.createElement(h.k,null,e.createElement(h.a,{size:12},i&&n&&e.createElement(b.a,{className:"googlesitekit-data-block__source",name:n,href:i,external:!0}),r))))}}]),LayoutFooter}(p.Component);E.propTypes={ctaLabel:m.a.string,ctaLink:m.a.string},t.a=E}).call(this,n(4))},,,,,,,,,,,,,function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(3),o=n(50),c=n(8),l=n(7),s=n(486),u=n(113),d=n(9),g=n(18),f=Object(u.a)(s.a);function NoAudienceBannerWidget(t){var n=t.Widget,i=t.WidgetNull,r=Object(g.a)(),o=Object(a.useSelect)((function(e){var t=e(c.r).getAvailableAudiences();return null==t?void 0:t.map((function(e){return e.name}))})),s=Object(a.useSelect)((function(e){return e(l.a).getConfiguredAudiences()})),u=Object(a.useSelect)((function(e){return e(l.a).didSetAudiences()})),m=null==s?void 0:s.every((function(e){return Array.isArray(o)&&!o.includes(e)}));return s&&(0===(null==s?void 0:s.length)||m)?e.createElement(n,{noPadding:!0},e.createElement(f,{onInView:function(){Object(d.I)("".concat(r,"_audiences-no-audiences"),"view_banner",u?"no-longer-available":"none-selected")}})):e.createElement(i,null)}NoAudienceBannerWidget.propTypes={Widget:r.a.elementType.isRequired,WidgetNull:r.a.elementType.isRequired},t.a=Object(o.a)({moduleName:"analytics-4"})(NoAudienceBannerWidget)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(406),g=n(0),f=n(2),m=n(17),p=n(345),h=Object(g.forwardRef)((function(t,n){var i=t.children,a=t.href,c=t.text,s=t.className,u=t.danger,h=t.disabled,b=t.target,v=t.icon,E=t.trailingIcon,_=t["aria-label"],O=t.title,k=t.customizedTooltip,y=t.tooltip,j=t.inverse,S=t.hideTooltipTitle,w=void 0!==S&&S,C=t.tooltipEnterDelayInMS,A=void 0===C?100:C,N=t.tertiary,T=void 0!==N&&N,x=t.callout,D=t.calloutStyle,R=o()(t,["children","href","text","className","danger","disabled","target","icon","trailingIcon","aria-label","title","customizedTooltip","tooltip","inverse","hideTooltipTitle","tooltipEnterDelayInMS","tertiary","callout","calloutStyle"]),M=Object(g.useCallback)((function(e){null!==e&&m.i.attachTo(e)}),[]),I=Object(d.a)(n,M),P=a&&!h?"a":"button",L=e.createElement(P,r()({className:l()("mdc-button",s,{"mdc-button--raised":!c&&!T&&!x,"mdc-button--danger":u,"mdc-button--inverse":j,"mdc-button--tertiary":T,"mdc-button--callout":x,"mdc-button--callout-primary":x||"primary"===D,"mdc-button--callout-warning":"warning"===D,"mdc-button--callout-error":"error"===D}),href:h?void 0:a,ref:I,disabled:!!h,"aria-label":function(){var e=_;if("_blank"!==b)return e;var t=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit");return"string"==typeof i&&(e=e||i),e?"".concat(e," ").concat(t):t}(),target:b||"_self",role:"a"===P?"button":void 0},R),v,i&&e.createElement("span",{className:"mdc-button__label"},i),E),B=w?null:O||k||_;return!h&&(y&&B||v&&B&&void 0===i)?e.createElement(p.a,{title:B,enterDelay:A},L):L}));h.displayName="Button",h.propTypes={onClick:u.a.func,children:u.a.node,href:u.a.string,text:u.a.bool,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,icon:u.a.element,trailingIcon:u.a.element,title:u.a.string,customizedTooltip:u.a.element,tooltip:u.a.bool,inverse:u.a.bool,hideTooltipTitle:u.a.bool,callout:u.a.bool,calloutStyle:u.a.oneOf(["primary","warning","error"])},h.defaultProps={onClick:null,href:null,text:!1,className:"",danger:!1,disabled:!1,icon:null,trailingIcon:null,title:null,customizedTooltip:null,tooltip:!1,inverse:!1,calloutStyle:null,callout:null},t.a=h}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Tooltip}));var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(641),g=n(0);function Tooltip(t){var n=t.children,i=t.popperClassName,a=t.tooltipClassName,c=t.onOpen,l=t.onClose,s=o()(t,["children","popperClassName","tooltipClassName","onOpen","onClose"]),f=Object(g.useRef)(!1),m=c?function(){f.current||(f.current=!0,null==c||c())}:void 0,p=c?function(){f.current=!1,null==l||l()}:l;return e.createElement(d.a,r()({classes:{popper:u()("googlesitekit-tooltip-popper",i),tooltip:u()("googlesitekit-tooltip",a)},arrow:!0,onOpen:m,onClose:p},s),n)}Tooltip.propTypes={children:l.a.node,popperClassName:l.a.string,tooltipClassName:l.a.string,onOpen:l.a.func,onClose:l.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GetHelpLink}));var i=n(1),r=n.n(i),a=n(38),o=n(2),c=n(20);function GetHelpLink(t){var n=t.linkURL;return Object(a.a)(Object(o.__)("Contact your administrator. Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(c.a,{href:n,external:!0,hideExternalIndicator:!0},Object(o.__)("Get help","google-site-kit"))})}GetHelpLink.propTypes={linkURL:r.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileNoData}));var i=n(2);function AudienceTileNoData(){return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__no-data"},Object(i.__)("No data to show yet","google-site-kit"))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PartialDataNotice}));var i=n(1),r=n.n(i);function PartialDataNotice(t){var n=t.content;return e.createElement("span",{className:"googlesitekit-audience-segmentation-partial-data-notice"},n)}PartialDataNotice.propTypes={content:r.a.node}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M2.675 72.31a29.248 29.248 0 005.678 8.74c9.451 9.955 23.416 10.799 36.223 8.308a88.838 88.838 0 0035.776-15.752c6.09-4.513 12.104-10.113 20.167-10.363 3.027-.093 6.158.741 8.445 2.71 4.753 4.063 4.668 11.012 8.377 15.829 4.932 6.405 12.026 8.389 19.764 9.128 21.862 2.086 47.902-4.758 62.939-21.412 13.426-14.868 15.038-38.526-1.214-52.08-7.425-6.192-17.606-9.03-27.216-7.584-7.345 1.105-14.801 4.467-22.404 5.401-8.954 1.103-14.49-2.659-21.734-7.04C114.852.58 98.164-2.345 83.874 2.072 70.195 6.301 60.35 17.846 47.04 22.918c-11.502 4.385-25.089 3.717-35.082 10.86C.133 42.228-2.84 59.286 2.675 72.31z",fill:"#B8E6CA"}),o=i.createElement("path",{d:"M108.273 109c54.612 0 98.883-1.735 98.883-3.874 0-2.14-44.271-3.875-98.883-3.875-54.611 0-98.882 1.735-98.882 3.875 0 2.139 44.27 3.874 98.882 3.874z",fill:"#161B18",opacity:.1}),c=i.createElement("path",{d:"M108.273 109c54.612 0 98.883-1.735 98.883-3.874 0-2.14-44.271-3.875-98.883-3.875-54.611 0-98.882 1.735-98.882 3.875 0 2.139 44.27 3.874 98.882 3.874z",fill:"#CBD0D3"}),l=i.createElement("path",{d:"M134.765 53.225c-1.065 16.927-6.936 32.112-3.012 51.193h-4.468M139.814 104.418h-4.47l7.9-51.193",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),s=i.createElement("path",{d:"M120.504 36.651c-3.814 1.73-11.135 5.58-11.135 13.398M147.266 35.787c3.493 1.787 11.06 7.678 11.977 13.225",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),u=i.createElement("path",{d:"M151.555 75.952l-3.102.282-18.926 1.719-11.063 1.002-1.049-24.735-1.825-43.046 34.528-1.262.697 32.058.094 4.316.642 29.565.004.1z",fill:"#77AD8C"}),d=i.createElement("path",{d:"M148.453 76.234l1.78-.162 1.323-.12-1.439-66.042-1.983.091 1.281 63.2-24.976 2.127.862-19.15-7.844-1.074.86 20.438.107 2.465.041.946 11.063-1.002 18.927-1.719-.002.002z",fill:"#5C9271"}),g=i.createElement("path",{d:"M135.891 70.752c.032.916-.392-20.197-.629-27.044-6.628-3.008-13.797-3.559-20.67-1.228l.628 27.043c6.874-2.33 14.043-1.779 20.671 1.23z",fill:"#CBD0D3"}),f=i.createElement("path",{d:"M135.926 70.752c.01.916-.547-20.194-.629-27.044 6.481-3.306 13.617-4.182 20.592-2.166l.629 27.043c-6.975-2.015-14.111-1.139-20.592 2.167z",fill:"#EBEEF0"}),m=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M133.233 60.796c-.083-3.907-.202-9.298-.333-14.96l.196-.005a8403.731 8403.731 0 01.43 19.559l.023 1.18.004.262a.337.337 0 010 .046v.004l-.001.008a.179.179 0 01-.*************** 0 01-.1-.08l-.002-.007v-.004-.003l-.001-.02h.001v-.025l-.004-.26-.023-1.18-.097-4.598zm.126 6.063a.19.19 0 01.093-.08.19.19 0 01.097.073l-.19.007zm.194.01v-.001.001zM130.917 60.007c-.084-3.912-.203-9.29-.334-14.911l.197-.004a10934.46 10934.46 0 01.432 19.528l.024 1.198.004.271.001.05v.008l-.004.02c-.054.062-.17.035-.189-.002a.054.054 0 01-.003-.015l-.001-.005v-.018-.035l-.005-.27-.024-1.198c-.022-1.071-.055-2.66-.098-4.617zm.129 6.12l.193-.006-.001-.006c-.02-.037-.135-.063-.19-.002a.079.079 0 00-.002.014zM128.613 59.401c-.086-3.923-.206-9.285-.335-14.834l.197-.005a9889.942 9889.942 0 01.459 20.704l.005.284.001.055v.008l-.001.006a.179.179 0 01-.*************** 0 01-.1-.082l-.001-.007-.001-.017h.001l-.001-.047-.005-.283-.025-1.221-.1-4.648zm.131 6.199l.195-.007a.192.192 0 00-.1-.081.187.187 0 00-.095.086v.002zM126.303 58.977c-.087-3.938-.207-9.284-.334-14.744l.196-.005c.127 5.46.248 10.807.335 14.744l.102 4.688a412.921 412.921 0 01.032 1.545l.001.061v.012l-.001.009c-.005.02-.109.084-.191.017a.156.156 0 01-.004-.02v-.004-.002-.002l-.001-.007h.001v-.001l-.001-.06-.006-.296-.026-1.248-.103-4.688zm.136 6.292l.195-.007a.08.08 0 00-.004-.018c-.081-.066-.186-.003-.19.017l-.001.008zm.196-.002zM124.002 58.73c-.088-3.958-.209-9.292-.334-14.653l.196-.004a14128.617 14128.617 0 01.439 19.391l.028 1.28.006.312.002.068v.015l-.002.014c-.08.078-.19.014-.194-.004l-.001-.007v-.002-.007l.196-.006v-.005c-.005-.018-.114-.082-.194-.004a.157.157 0 00-.002.014v-.006l-.001-.066-.006-.312-.028-1.28-.105-4.738zM121.709 58.698c-.09-3.97-.211-9.281-.333-14.54l.196-.005a15730.945 15730.945 0 01.44 19.316l.029 1.306.006.327.002.074v.021a.191.191 0 01-.021.056.165.165 0 01-.175-.05v-.006l-.001-.004.197-.007-.001-.003a.163.163 0 00-.174-.05.193.193 0 00-.021.056v.003l-.001-.013-.001-.073-.007-.326-.028-1.307-.107-4.775zM119.414 58.855c-.091-3.986-.212-9.28-.332-14.44l.196-.005c.12 5.16.242 10.454.333 14.44l.109 4.818.03 1.336.**************.001.017v.006c0 .004-.098.096-.196.009l-.001-.007v-.002l.197-.006-.001-.006c-.098-.087-.196.005-.196.01v.001V65.43l-.002-.081-.007-.34-.03-1.336-.11-4.819zM116.797 44.873l.196-.004.489 21.033h-.01a271.267 271.267 0 01-.186.005l-.489-21.034z",fill:"#CBD0D3"}),p=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M133.317 67.22c-5.153-1.86-10.563-2.237-15.85-.939a.388.388 0 11-.188-.754c5.447-1.338 11.016-.948 16.306.963a.388.388 0 11-.268.73zM133.267 64.222c-5.159-1.867-10.574-2.245-15.866-.946a.388.388 0 11-.189-.754c5.454-1.34 11.027-.947 16.323.97a.387.387 0 01.234.498.392.392 0 01-.502.232zM133.204 61.217c-5.162-1.867-10.576-2.245-15.866-.946a.393.393 0 01-.475-.284.389.389 0 01.287-.47c5.45-1.339 11.023-.946 16.323.97a.388.388 0 01.234.498.393.393 0 01-.503.232zM133.134 58.212c-5.163-1.866-10.576-2.245-15.866-.946a.388.388 0 11-.189-.755c5.451-1.338 11.024-.945 16.323.97a.386.386 0 01.234.499.392.392 0 01-.502.232zM133.071 55.213c-5.163-1.872-10.584-2.252-15.881-.95a.388.388 0 11-.189-.754c5.459-1.342 11.038-.947 16.339.974a.388.388 0 01.234.498.393.393 0 01-.503.232zM133.001 52.208c-5.164-1.87-10.584-2.252-15.882-.95a.389.389 0 11-.188-.755c5.458-1.34 11.038-.945 16.339.975a.388.388 0 11-.269.73z",fill:"#B8BDB9"}),h=i.createElement("path",{d:"M116.886 44.87c5.378-1.32 10.878-.934 16.11.963l.054 2.999c-5.227-1.891-10.721-2.276-16.094-.957l-.07-3.004z",fill:"#B8BDB9"}),b=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M117.284 45.175l.051 2.211c5.118-1.161 10.331-.806 15.312.893l-.039-2.17c-4.989-1.759-10.21-2.127-15.324-.934zm-.494-.68c5.459-1.342 11.038-.948 16.339.974l.253.091.069 3.832-.536-.194c-5.159-1.867-10.574-2.245-15.866-.945l-.475.116-.089-3.8.305-.075z",fill:"#B8BDB9"}),v=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M137.87 60.692a7073.26 7073.26 0 01-.362-14.961l.196-.005a8741.186 8741.186 0 00.48 19.558 754.986 754.986 0 00.04 1.44v.025h.001l.001.02-.001.003v.012a.187.187 0 01-.*************** 0 01-.097-.08l-.002-.008v-.003l-.002-.046-.008-.262-.032-1.18c-.028-1.06-.068-2.644-.117-4.597zm.352 6.057a.189.189 0 00-.096-.076.184.184 0 00-.093.078l.189-.002zm-.193.018v0zM140.148 59.798c-.098-3.912-.228-9.29-.359-14.91l.196-.005a10306.992 10306.992 0 00.507 20.724l.008.27.001.036h.001v.023a.13.13 0 01-.002.015c-.018.038-.132.07-.189.01l-.005-.02v-.004-.004l-.002-.05a1047.874 1047.874 0 01-.04-1.47c-.027-1.07-.068-2.659-.116-4.615zm.352 6.115l-.003-.014c-.057-.06-.172-.027-.189.01l-.001.006.193-.002zM142.426 59.087c-.097-3.922-.227-9.284-.356-14.834l.197-.004c.129 5.55.258 10.911.355 14.834l.116 4.647.032 1.22.008.284.002.047v.024a.185.185 0 01-.*************** 0 01-.099-.082l-.001-.006v-.008l-.002-.055a22.176 22.176 0 01-.008-.284l-.032-1.22-.116-4.648zm.353 6.194v-.002a.187.187 0 00-.099-.082.18.18 0 00-.095.086l.194-.002zM144.711 58.559c-.096-3.937-.225-9.283-.352-14.744l.197-.004c.127 5.46.255 10.806.351 14.743l.115 4.688.032 1.247.008.297.002.059v.001h.001V64.857l-.001.004a.121.121 0 01-.003.02c-.078.07-.186.012-.191-.008l-.002-.01v-.004-.007l-.002-.06-.008-.298-.032-1.248-.115-4.687zm.353 6.287l-.001-.008c-.006-.02-.113-.078-.191-.008l-.004.018.196-.002zm-.196.007c0 .001 0 0 0 0z",fill:"#EBEEF0"}),E=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M137.979 66.391c5.195-2.149 10.74-2.792 16.244-1.703a.389.389 0 11-.153.762c-5.342-1.057-10.728-.435-15.789 1.659a.394.394 0 01-.513-.21.388.388 0 01.211-.508zM137.889 63.393c5.201-2.155 10.751-2.8 16.261-1.71a.39.39 0 01.308.457.392.392 0 01-.461.305c-5.348-1.058-10.739-.434-15.806 1.665a.392.392 0 01-.512-.209.387.387 0 01.21-.508zM137.823 60.389c5.2-2.155 10.749-2.8 16.256-1.71a.388.388 0 11-.153.762c-5.345-1.057-10.735-.434-15.801 1.665a.394.394 0 01-.513-.21.388.388 0 01.211-.508zM137.749 57.383c5.2-2.154 10.748-2.8 16.256-1.71a.388.388 0 11-.154.762c-5.344-1.057-10.735-.433-15.8 1.665a.393.393 0 01-.513-.209.388.388 0 01.211-.508zM137.663 54.385c5.206-2.16 10.761-2.807 16.276-1.716a.389.389 0 11-.153.763c-5.352-1.06-10.75-.434-15.821 1.67a.394.394 0 01-.513-.208.388.388 0 01.211-.509zM137.596 51.38c5.206-2.16 10.762-2.808 16.277-1.716a.39.39 0 01.308.457.393.393 0 01-.462.305c-5.352-1.06-10.749-.432-15.82 1.67a.393.393 0 01-.513-.208.387.387 0 01.21-.508zM137.542 48.368c5.201-2.154 10.751-2.8 16.26-1.71a.389.389 0 11-.153.763c-5.346-1.058-10.739-.434-15.805 1.665a.393.393 0 01-.513-.21.388.388 0 01.211-.508zM137.456 45.37c5.206-2.16 10.761-2.808 16.276-1.716a.39.39 0 01.308.457.392.392 0 01-.461.306c-5.353-1.06-10.75-.434-15.821 1.67a.394.394 0 01-.513-.209.388.388 0 01.211-.508z",fill:"#CBD0D3"}),_=i.createElement("path",{d:"M137.608 45.729c5.139-2.133 10.618-2.768 16.049-1.693l.21 9.014c-5.434-1.075-10.91-.439-16.049 1.693l-.21-9.014z",fill:"#CBD0D3"}),O=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M138.005 45.986l.19 8.184c4.901-1.928 10.097-2.52 15.267-1.592l-.191-8.22c-5.162-.96-10.364-.356-15.266 1.628zm-.55-.616c5.206-2.16 10.765-2.807 16.277-1.716a.39.39 0 01.315.372l.21 9.015a.387.387 0 01-.14.307.394.394 0 01-.329.083c-5.352-1.06-10.749-.434-15.82 1.67a.395.395 0 01-.365-.032.387.387 0 01-.178-.317l-.21-9.015a.389.389 0 01.24-.367z",fill:"#CBD0D3"}),k=i.createElement("path",{d:"M159.24 49.011c.761 4.603-4.117 7.506-7.486 6.434M109.37 50.05c.001 4.55 5.159 7.83 8.838 6.226",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),y=i.createElement("path",{d:"M31.757 63.326l-5.175 13.74a22.86 22.86 0 00-.534 1.593c-1.686 5.718-.919 11.872 1.862 17.155l4.678 8.598h-4.473",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),j=i.createElement("path",{d:"M39.988 67.196l.768 37.221",stroke:"#161B18",strokeWidth:1.472,strokeMiterlimit:10,strokeLinecap:"round"}),S=i.createElement("path",{d:"M31.757 63.326a27.536 27.536 0 00-2.058 5.225",stroke:"#1967D2",strokeWidth:2.748,strokeLinejoin:"round"}),w=i.createElement("path",{d:"M47.8 52.642c4.738 2.161 9.71 4.53 10.766 11.237M13.781 61.718c-2.557 3.62-6.986 9.225-5.039 14.72",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),C=i.createElement("path",{d:"M1.535 51.315L54.34 35.316s5.107 29.415-22.04 34.95c-.009 0-18.561 4.185-30.764-18.952z",fill:"#70B2F5"}),A=i.createElement("path",{d:"M1.535 51.315L54.34 35.316s5.107 29.415-22.04 34.95c-.009 0-18.561 4.185-30.764-18.952z",fill:"#77AD8C"}),N=i.createElement("path",{d:"M33.856 67.557S16.353 71.503 4.163 50.519l-2.628.796C13.738 74.452 32.29 70.266 32.29 70.266c10.329-2.105 15.985-7.67 19.032-13.753-3.297 4.975-8.696 9.256-17.466 11.044z",fill:"#5C9271"}),T=i.createElement("path",{d:"M45.22 104.418h-4.47",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),x=i.createElement("path",{d:"M38.095 85.802c.172.861-3.494-19.049-4.776-25.49-6.736-1.845-13.604-1.284-19.746 1.959l4.776 25.49c6.143-3.243 13.01-3.804 19.746-1.96z",fill:"#CBD0D3"}),D=i.createElement("path",{d:"M38.129 85.796c.152.865-3.64-19.022-4.776-25.491 5.62-4.106 12.236-6.013 19.146-5.159l4.776 25.49c-6.91-.853-13.525 1.053-19.146 5.16z",fill:"#EBEEF0"}),R=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M34.042 76.784c-.683-3.684-1.629-8.766-2.629-14.104l.185-.033A7851.454 7851.454 0 0135.233 82.2a55.731 55.731 0 01.052.29v.011a.174.174 0 01-.*************** 0 01-.106-.06l-.003-.007v-.003l-.001-.003v-.001a3796.705 3796.705 0 00-1.057-5.736zm1.057 5.717a.18.18 0 01.075-.09c.*************.103.055l-.178.035zm.184-.02zM31.73 76.386A9015.12 9015.12 0 0029.11 62.33l.184-.034a9007.929 9007.929 0 013.637 19.54 80.445 80.445 0 01.055.307v.024c-.042.066-.155.058-.18.026a.162.162 0 01-.004-.013l-.001-.006a.528.528 0 01-.004-.016h.001l-.006-.034a4684.226 4684.226 0 00-1.06-5.738zm1.068 5.771l.181-.035a.058.058 0 00-.002-.005c-.024-.032-.137-.04-.178.026l-.001.014zM29.453 76.162c-.687-3.698-1.63-8.753-2.61-13.984l.185-.033a10403.315 10403.315 0 013.636 19.517 66.685 66.685 0 01.058.32v.008l.001.006a.176.176 0 01-.*************** 0 01-.107-.063l-.002-.006-.003-.016a4941.913 4941.913 0 00-1.082-5.845zm1.083 5.845l.183-.035a.181.181 0 00-.107-.063.176.176 0 00-.076.096v.002zM27.204 76.11c-.69-3.712-1.631-8.752-2.596-13.898l.185-.034a13167.584 13167.584 0 013.687 19.775l.01.057.002.007v.013c0 .02-.09.095-.177.045a.185.185 0 01-.008-.023v-.001-.002l-.002-.007a6801.028 6801.028 0 00-1.1-5.932zm1.101 5.932l.184-.035a.257.257 0 00-.006-.017c-.088-.05-.177.025-.178.045v.007zm.185-.03v-.001zM24.99 76.224c-.695-3.732-1.634-8.76-2.58-13.813l.184-.033a16040.945 16040.945 0 013.637 19.486l.055.294c.005.03.01.052.011.064a.39.39 0 01.003.028c-.063.086-.176.042-.183.025a.13.13 0 01-.002-.006v-.003l-.002-.005.184-.036v-.005c-.008-.016-.12-.06-.184.026V82.254a16.67 16.67 0 00-.066-.357l-.224-1.206-.832-4.467zM22.813 76.54c-.698-3.743-1.634-8.749-2.563-13.706l.185-.034a20172.54 20172.54 0 013.701 19.818l.003.014v.005a.19.19 0 01-.01.057c-.09.047-.172-.018-.174-.022v-.006l-.002-.002.185-.037-.001-.003c-.001-.004-.083-.069-.173-.021a.191.191 0 00-.01.059l-.003-.012-.012-.07a302.24 302.24 0 00-.057-.307l-.23-1.232-.839-4.501zM20.668 77.034l-2.547-13.61.184-.034a29372.29 29372.29 0 013.69 19.733l.015.076.003.017v.005c.001.004-.077.105-.183.038a.117.117 0 01-.001-.006v-.002l.184-.035-.002-.006c-.106-.067-.184.035-.183.038v.002-.002l-.003-.016-.014-.076-.06-.32-.235-1.26-.848-4.542zM16.023 64.202l.185-.034 3.715 19.826-.01.001-.165.03-.01.002-3.715-19.825z",fill:"#CBD0D3"}),M=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M35.112 82.85c-5.163-.983-10.34-.522-15.14 1.505a.377.377 0 01-.493-.197.37.37 0 01.198-.488c4.947-2.089 10.276-2.56 15.576-1.551a.373.373 0 01.298.436.375.375 0 01-.44.295zM34.601 80.02c-5.17-.987-10.35-.527-15.157 1.502a.377.377 0 01-.493-.197.37.37 0 01.198-.488c4.953-2.09 10.287-2.561 15.593-1.548a.373.373 0 01.299.436.375.375 0 01-.44.296zM34.078 77.187c-5.173-.986-10.353-.526-15.157 1.501a.377.377 0 01-.493-.197.37.37 0 01.198-.488c4.95-2.09 10.283-2.56 15.593-1.547a.373.373 0 11-.141.731zM33.547 74.355c-5.173-.986-10.354-.527-15.157 1.5a.377.377 0 01-.493-.196.371.371 0 01.198-.489c4.95-2.089 10.282-2.559 15.593-1.547a.373.373 0 01.298.436.375.375 0 01-.44.296zM33.023 71.527c-5.175-.991-10.362-.532-15.172 1.5a.377.377 0 01-.493-.197.37.37 0 01.198-.489c4.957-2.093 10.296-2.563 15.609-1.545a.373.373 0 11-.142.73zM32.492 68.695c-5.175-.99-10.362-.532-15.172 1.5a.377.377 0 01-.494-.198.37.37 0 01.198-.488c4.957-2.093 10.297-2.562 15.61-1.546a.373.373 0 11-.142.732z",fill:"#B8BDB9"}),I=i.createElement("path",{d:"M16.109 64.187c4.884-2.062 10.147-2.527 15.39-1.523l.515 2.83c-5.238-1-10.495-.535-15.374 1.525l-.531-2.832z",fill:"#B8BDB9"}),P=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.538 64.414l.39 2.084c4.663-1.872 9.65-2.323 14.625-1.468l-.373-2.047c-4.991-.91-9.988-.47-14.642 1.431zm-.572-.57c4.957-2.093 10.296-2.563 15.609-1.545l.253.048.658 3.614-.538-.102c-5.169-.987-10.35-.527-15.157 1.502l-.43.182-.672-3.582.277-.117z",fill:"#B8BDB9"}),L=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M38.41 75.985c-.698-3.682-1.656-8.762-2.656-14.1l.185-.033a7931.958 7931.958 0 003.477 18.43 592.343 592.343 0 00.26 1.357l.005.023h.001a.711.711 0 01.004.022v.011a.173.173 0 01-.*************** 0 01-.105-.06.16.16 0 01-.003-.01 3677.586 3677.586 0 01-1.092-5.733zm1.269 5.677a.182.182 0 00-.102-.057.181.181 0 00-.077.088l.179-.03zm-.18.046v0zM40.426 74.796c-.697-3.687-1.652-8.755-2.645-14.053l.185-.034a9325.56 9325.56 0 003.684 19.53l.049.255.006.034h.001a.53.53 0 01.003.016v.019c-.01.04-.113.087-.176.04a.164.164 0 01-.008-.023l-.001-.004-.01-.046-.049-.256c-.046-.242-.12-.624-.215-1.128a5105.31 5105.31 0 01-.824-4.35zm1.278 5.732a.194.194 0 00-.005-.013c-.063-.047-.166 0-.177.039v.005l.182-.031zM42.474 73.78c-.698-3.697-1.65-8.75-2.63-13.98l.184-.034a10585.806 10585.806 0 003.678 19.51l.05.266.01.044.003.016a.07.07 0 010 .006.176.176 0 01-.*************** 0 01-.106-.063l-.002-.006a2.407 2.407 0 01-.012-.06l-.051-.266-.219-1.15c-.194-1.023-.48-2.532-.828-4.38zm1.291 5.806v-.001a.178.178 0 00-.106-.063.178.178 0 00-.077.096l.183-.032zM44.553 72.934c-.7-3.71-1.647-8.748-2.612-13.895l.185-.034a13096.468 13096.468 0 003.722 19.768l.011.055v.001l.002.007v.027c-.062.078-.172.039-.181.021a.116.116 0 01-.005-.02l-.011-.056a264.523 264.523 0 01-.277-1.456l-.834-4.418zm1.306 5.895l-.002-.007c-.008-.017-.119-.057-.182.021v.018l.184-.032zm-.184.037z",fill:"#EBEEF0"}),B=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M39.398 81.36c4.582-2.818 9.729-4.263 15.104-*************.37.18.362.386a.374.374 0 01-.389.358c-5.217-.193-10.217 1.208-14.681 3.953a.378.378 0 01-.517-.12.37.37 0 01.12-.513zM38.848 78.537c4.587-2.824 9.738-4.273 15.12-*************.369.18.361.386a.374.374 0 01-.389.358c-5.222-.193-10.227 1.211-14.695 3.962a.378.378 0 01-.518-.12.37.37 0 01.12-.513zM38.316 75.705c4.587-2.824 9.737-4.272 15.115-*************.37.18.362.386a.374.374 0 01-.389.358c-5.22-.193-10.223 1.211-14.69 3.962a.378.378 0 01-.518-.12.37.37 0 01.12-.513zM37.789 72.873c4.587-2.824 9.736-4.272 15.115-*************.37.18.362.386a.374.374 0 01-.389.358c-5.22-.193-10.223 1.211-14.691 3.962a.378.378 0 01-.517-.12.37.37 0 01.12-.513zM37.243 70.05c4.591-2.83 9.747-4.282 15.134-*************.37.18.362.386a.374.374 0 01-.39.358c-5.227-.194-10.236 1.213-14.709 3.97a.378.378 0 01-.517-.12.37.37 0 01.12-.513zM36.712 67.216c4.591-2.829 9.747-4.281 15.134-************.369.18.361.385a.374.374 0 01-.389.358c-5.227-.194-10.236 1.214-14.71 3.97a.378.378 0 01-.516-.12.37.37 0 01.12-.513zM36.195 64.376c4.588-2.824 9.74-4.273 15.12-4.074.207.008.37.181.362.386a.374.374 0 01-.39.359c-5.22-.194-10.226 1.21-14.695 3.961a.378.378 0 01-.517-.12.37.37 0 01.12-.513zM35.65 61.552c4.59-2.83 9.747-4.281 15.133-*************.37.18.362.386a.374.374 0 01-.39.358c-5.227-.194-10.236 1.213-14.709 3.97a.378.378 0 01-.517-.12.37.37 0 01.12-.513z",fill:"#CBD0D3"}),z=i.createElement("path",{d:"M35.851 61.868c4.532-2.793 9.618-4.222 14.922-4.025l1.592 8.497c-5.307-.198-10.39 1.232-14.922 4.025l-1.592-8.497z",fill:"#CBD0D3"}),F=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M36.268 62.051l1.446 7.715c4.338-2.565 9.162-3.91 14.197-3.812l-1.452-7.749c-5.032-.127-9.86 1.23-14.191 3.846zm-.615-.5c4.591-2.83 9.75-4.28 15.134-4.08a.375.375 0 01.355.304l1.593 8.497a.37.37 0 01-.085.31.376.376 0 01-.298.13c-5.228-.195-10.237 1.212-14.71 3.97a.378.378 0 01-.568-.25l-1.592-8.496a.37.37 0 01.171-.384z",fill:"#CBD0D3"}),V=i.createElement("path",{d:"M58.565 63.879c.876 5.566-4.736 9.076-8.612 7.78M8.738 76.438c1.616 4.56 7.623 6.458 13.652 0",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),W=i.createElement("path",{d:"M160.68 68.356c.934 6.676 1.531 14.409 0 20.996M171.536 72.568l-3.781 31.849h-4.47",stroke:"#161B18",strokeWidth:1.472,strokeMiterlimit:10,strokeLinecap:"round"}),H=i.createElement("path",{d:"M187.455 104.418h-4.471c.637-10.18 1.817-24.67 1.817-24.67",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),U=i.createElement("path",{d:"M199.766 66.904c2.35 3.645 6.395 13.017 4.381 17.69M161.004 59.99c-3.656 2.734-9.85 8.336-9.904 15.127",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),q=i.createElement("path",{d:"M161.004 59.99c-3.656 2.734-9.85 8.336-9.904 15.127-.045 5.634 4.35 10.804 12.101 6.915",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),G=i.createElement("path",{d:"M188.416 36.69c5.324 1.935 9.926 5.533 12.45 10.684 5.586 11.402.195 27.178-11.38 32.714-10.499 5.032-24.499 1.152-30.83-8.532-6.33-9.683-4.157-23.882 4.792-31.286 6.7-5.538 16.706-6.574 24.968-3.58z",fill:"#77AD8C"}),K=i.createElement("path",{d:"M200.868 47.374a19.785 19.785 0 00-4.03-5.505c.638.885 1.2 1.822 1.68 2.8 5.587 11.402.196 27.177-11.38 32.714-9.081 4.35-20.778 2.028-27.868-4.939 6.584 9.03 20.044 12.517 30.218 7.648 11.568-5.54 16.966-21.316 11.38-32.718z",fill:"#5C9271"}),Y=i.createElement("path",{d:"M182.034 67.78c-2.79 3.71-7.987 4.925-11.944.505",stroke:"#161B18",strokeWidth:1.105,strokeMiterlimit:10,strokeLinecap:"round"}),X=i.createElement("path",{d:"M175.979 96.185c-.089.946 2.303-20.818 2.977-27.89-6.413-3.959-13.71-5.464-21.09-3.967l-2.976 27.888c7.38-1.496 14.677.009 21.089 3.969z",fill:"#CBD0D3"}),$=i.createElement("path",{d:"M176.015 96.189c-.112.943 2.142-20.835 2.976-27.89 7.107-2.55 14.561-2.518 21.463.468l-2.976 27.888c-6.902-2.985-14.356-3.017-21.463-.466z",fill:"#EBEEF0"}),Z=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M174.58 85.6c.437-4.028 1.037-9.587 1.66-15.427l.203.022a8024.607 8024.607 0 01-2.178 20.167 469.931 469.931 0 01-.165 1.486l-.006.048-.001.004a.035.035 0 01-.*************** 0 01-.*************** 0 01-.093-.094v-.009-.003l.001-.003v-.001l.002-.02h.001l.003-.026.03-.268.135-1.217.517-4.74zm-.683 6.25a.196.196 0 01.106-.07.2.2 0 01.09.089l-.196-.018zm.198.037v-.002.002zM172.305 84.486c.438-4.034 1.035-9.579 1.654-15.376l.203.022a10437.849 10437.849 0 01-2.172 20.136l-.136 1.235-.031.28-.006.05-.001.005-.001.004-.007.02c-.063.057-.179.014-.194-.027l-.001-.015.001-.006.001-.018h.001l.004-.036.032-.28.135-1.234c.121-1.104.3-2.743.518-4.76zm-.688 6.31l.2.019-.001-.006c-.015-.04-.13-.083-.194-.028a.101.101 0 00-.005.015zM170.011 83.561c.437-4.044 1.033-9.574 1.643-15.297l.203.022a10697.615 10697.615 0 01-2.163 20.089l-.138 1.259-.033.292-.007.057-.001.008-.001.007a.196.196 0 01-.************* 0 01-.092-.097v-.007l.001-.018h.001l.006-.049.032-.291.138-1.258.52-4.793zm-.696 6.391l.201.019a.192.192 0 00-.091-.097.189.189 0 00-.109.077l-.001.001zM167.698 82.823c.438-4.06 1.03-9.573 1.631-15.204l.202.021a14091.811 14091.811 0 01-2.153 20.038l-.141 1.287-.033.306-.008.063-.001.007v.004a.05.05 0 01-.002.01c-.008.02-.124.072-.199-.008-.001-.008-.002-.019-.001-.021v-.004-.002-.002l.001-.007v-.001l.007-.061.034-.306.14-1.287c.124-1.136.304-2.803.523-4.833zm-.704 6.488l.202.018a.171.171 0 00-.002-.018c-.074-.08-.19-.028-.198-.008l-.002.008zm.202.024zM165.365 82.269c.44-4.083 1.03-9.583 1.62-15.111l.202.021a15535.246 15535.246 0 01-2.146 19.997l-.143 1.319-.035.322-.008.07-.002.015-.004.015c-.092.07-.196-.01-.198-.03v-.01l.001-.006.202.019v-.005c-.002-.02-.106-.1-.198-.03a.088.088 0 00-.004.014v.002l.001-.008.007-.068.036-.322.143-1.319.526-4.885zM163.009 81.934c.44-4.093 1.027-9.57 1.606-14.994l.202.021a31558.74 31558.74 0 01-2.135 19.918l-.146 1.348-.037.336-.008.077-.002.016-.001.006a.204.204 0 01-.029.055.17.17 0 01-.173-.075v-.007l.001-.003.202.019.001-.004c0-.005-.066-.097-.173-.075a.225.225 0 00-.03.055v.003l.001-.012.009-.076.037-.337.145-1.347.53-4.924zM160.626 81.796c.441-4.111 1.025-9.57 1.593-14.891l.203.02c-.568 5.322-1.153 10.781-1.593 14.892l-.533 4.968-.148 1.378-.038.35a8.768 8.768 0 01-.009.084l-.002.018-.001.006c0 .004-.113.086-.202-.016V88.596l.203.02v-.007c-.089-.102-.202-.02-.202-.016l-.001.002.001-.002.001-.017a8.44 8.44 0 00.009-.084l.038-.35.148-1.378.533-4.968zM159.809 67.077l.202.02-2.315 21.692h-.01l-.182-.02h-.01l2.315-21.692z",fill:"#CBD0D3"}),Q=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M173.801 92.217c-5.049-2.588-10.56-3.683-16.171-3.04a.406.406 0 01-.45-.354.405.405 0 01.358-.447c5.78-.662 11.454.468 16.637 3.125a.401.401 0 01.174.543.408.408 0 01-.548.173zM174.149 89.126c-5.054-2.595-10.57-3.692-16.187-3.048a.406.406 0 01-.45-.354.405.405 0 01.358-.447c5.787-.663 11.465.47 16.654 3.134a.401.401 0 01.173.543.408.408 0 01-.548.172zM174.49 86.028c-5.059-2.594-10.575-3.691-16.188-3.048a.405.405 0 01-.45-.354.405.405 0 01.358-.447c5.783-.662 11.461.47 16.653 3.134a.4.4 0 01.174.543.407.407 0 01-.547.172zM174.822 82.93c-5.059-2.595-10.575-3.692-16.188-3.049a.405.405 0 01-.45-.354.405.405 0 01.358-.446c5.783-.663 11.461.47 16.653 3.133a.4.4 0 01.174.543.407.407 0 01-.547.173zM175.157 79.838c-5.059-2.6-10.581-3.7-16.202-3.055a.404.404 0 11-.093-.8c5.792-.665 11.476.47 16.669 3.14a.4.4 0 01.174.543.409.409 0 01-.548.172zM175.485 76.739c-5.059-2.6-10.581-3.7-16.202-3.056a.404.404 0 11-.093-.8c5.792-.665 11.476.472 16.67 3.14a.401.401 0 01.173.543.408.408 0 01-.548.173z",fill:"#B8BDB9"}),J=i.createElement("path",{d:"M159.902 67.086c5.707-.655 11.31.463 16.436 3.098l-.346 3.09c-5.121-2.628-10.719-3.743-16.42-3.09l.33-3.098z",fill:"#B8BDB9"}),ee=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M160.27 67.451l-.243 2.28c5.417-.524 10.73.523 15.624 2.922l.251-2.237c-4.894-2.461-10.214-3.522-15.632-2.965zm-.416-.765c5.792-.665 11.476.47 16.67 3.14l.247.127-.442 3.949-.526-.27c-5.054-2.594-10.571-3.691-16.187-3.047l-.504.057.419-3.919.323-.037z",fill:"#B8BDB9"}),te=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M179.36 86.1c.423-4.031 1.009-9.591 1.632-15.431l.203.021c-.623 5.84-1.21 11.4-1.632 15.43-.211 2.015-.382 3.648-.495 4.742l-.125 1.218-.027.269-.002.025h.001a.222.222 0 01-.003.02l-.001.004v.004a.035.035 0 01-.*************** 0 01-.************** 0 01-.09-.094v-.01-.002l.004-.049.027-.27.125-1.217.495-4.743zm-.449 6.274a.193.193 0 00-.088-.09.197.197 0 00-.107.067l.195.023zm-.201-.007v0zM181.821 85.478c.424-4.035 1.01-9.582 1.628-15.378l.203.02a9452.743 9452.743 0 00-2.255 21.376l-.029.28a.595.595 0 01-.003.036h.001a.189.189 0 01-.003.018l-.001.006-.004.015c-.023.037-.145.055-.195-.013-.002-.008-.002-.02-.002-.022v-.004-.004l.005-.052.028-.28.128-1.236.499-4.762zm-.457 6.334a.084.084 0 00-.002-.015c-.051-.068-.172-.05-.196-.013l-.001.005.199.023zM184.261 85.046c.426-4.045 1.011-9.576 1.622-15.299l.202.021c-.61 5.723-1.195 11.254-1.621 15.3-.213 2.022-.387 3.674-.503 4.793l-.131 1.26-.03.291-.005.05h.001l-.002.017a.024.024 0 01-.************* 0 01-.************** 0 01-.091-.097l.001-.007v-.008l.005-.057.03-.293.131-1.26.503-4.794zm-.466 6.415v-.002a.189.189 0 00-.09-.097.193.193 0 00-.11.076l.2.023zM186.679 84.801c.429-4.06 1.013-9.574 1.614-15.205l.202.02a14313.317 14313.317 0 00-2.288 21.634l-.007.061v.001h.001l-.001.008v.002l-.001.002v.003l-.006.021c-.09.062-.193-.012-.196-.033v-.01-.005l.001-.007.006-.063.031-.306.135-1.288.509-4.835zm-.479 6.511v-.008c-.003-.02-.106-.095-.195-.033l-.006.018.201.023zm-.202-.018z",fill:"#EBEEF0"}),ne=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M178.707 91.973c5.63-1.53 11.417-1.465 16.931.375.212.07.327.3.255.51a.408.408 0 01-.515.254c-5.351-1.786-10.973-1.852-16.457-.361a.407.407 0 01-.499-.282.403.403 0 01.285-.496zM179.019 88.88c5.636-1.536 11.429-1.473 16.948.368a.403.403 0 11-.259.764c-5.357-1.787-10.984-1.851-16.475-.356a.403.403 0 11-.214-.777zM179.351 85.78c5.635-1.534 11.427-1.471 16.943.369a.403.403 0 11-.259.764c-5.354-1.786-10.98-1.85-16.47-.356a.403.403 0 11-.214-.777zM179.683 82.682c5.635-1.535 11.427-1.472 16.943.368a.403.403 0 11-.259.764c-5.354-1.786-10.98-1.85-16.469-.355a.403.403 0 11-.215-.777zM179.994 79.588c5.643-1.54 11.442-1.479 16.966.365.213.071.327.3.255.51a.407.407 0 01-.515.254c-5.361-1.79-10.994-1.853-16.49-.352a.403.403 0 11-.216-.777zM180.323 76.489c5.642-1.54 11.441-1.479 16.965.365.213.072.327.3.255.51a.407.407 0 01-.515.254c-5.361-1.79-10.994-1.852-16.49-.352a.403.403 0 11-.215-.777zM180.671 73.385c5.637-1.534 11.43-1.471 16.949.37.212.071.327.3.255.51a.407.407 0 01-.515.254c-5.356-1.787-10.984-1.852-16.474-.357a.402.402 0 11-.215-.777zM180.987 70.291c5.642-1.54 11.441-1.478 16.965.366.213.071.327.3.255.51a.406.406 0 01-.514.254c-5.362-1.79-10.995-1.853-16.491-.353a.403.403 0 11-.215-.777z",fill:"#CBD0D3"}),ie=i.createElement("path",{d:"M181.096 70.68c5.569-1.52 11.288-1.457 16.728.358l-.992 9.297c-5.443-1.817-11.159-1.88-16.728-.36l.992-9.296z",fill:"#CBD0D3"}),re=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M181.469 70.996l-.901 8.44c5.297-1.34 10.719-1.27 15.911.361l.905-8.477c-5.179-1.662-10.609-1.722-15.915-.323zm-.482-.705c5.642-1.54 11.444-1.477 16.965.366.18.06.294.236.274.424l-.992 9.296a.404.404 0 01-.534.34c-5.361-1.79-10.994-1.853-16.49-.352a.409.409 0 01-.371-.082.402.402 0 01-.141-.349l.992-9.296a.404.404 0 01.297-.347z",fill:"#CBD0D3"}),ae=i.createElement("path",{d:"M204.146 84.595c-1.671 3.879-7.751 2.74-10.354-.297M151.096 75.116c-.045 5.635 4.349 10.805 12.1 6.915",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),oe=i.createElement("path",{d:"M91.429 81.618c-.729 8.2-.457 15.965 1.975 22.796h4.47M80.227 81.238c-.76 8.178-.245 15.966 2.153 23.178h-4.478",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),ce=i.createElement("path",{d:"M118.418 79.006c7.32 3.89 10.2 8.445 9.473 12.335M60.719 71.227c-7.51 3.313-11.627 6.373-11.627 13.398",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),le=i.createElement("path",{d:"M57.84 49.763l-3.38 33.985 68.342 6.303 1.443-32.81-66.405-7.478z",fill:"#77AD8C"}),se=i.createElement("path",{d:"M57.476 82.268l2.638-32.25-2.273-.255-3.38 33.985 68.341 6.303.127-2.876-65.453-4.907z",fill:"#5C9271"}),ue=i.createElement("path",{d:"M80.625 72.749c3.832 4.721 11.357 6.736 17.468 1.896",stroke:"#161B18",strokeWidth:1.105,strokeMiterlimit:10,strokeLinecap:"round"}),de=i.createElement("path",{d:"M86.74 98.236c-.1.945 2.55-20.79 3.308-27.852-6.365-4.035-13.644-5.624-21.041-4.213L65.7 94.023c7.397-1.41 14.676.179 21.04 4.213z",fill:"#CBD0D3"}),ge=i.createElement("path",{d:"M86.776 98.24c-.123.942 2.39-20.808 3.308-27.852 7.137-2.468 14.59-2.35 21.455.717l-3.307 27.851c-6.865-3.065-14.319-3.184-21.456-.716z",fill:"#EBEEF0"}),fe=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M85.467 87.637c.485-4.024 1.151-9.575 1.844-15.407l.202.023a8546.627 8546.627 0 01-2.418 20.141 653.021 653.021 0 01-.192 1.544.19.19 0 01-.*************** 0 01-.091-.095v-.009-.003-.005l.002-.02h.001l.004-.025.033-.268.15-1.215.573-4.733zm-.757 6.242a.197.197 0 01.106-.069c.*************.089.09l-.195-.021zm.197.038v-.001.001zM83.205 86.496c.485-4.028 1.149-9.566 1.836-15.355l.202.023a9660.973 9660.973 0 01-2.561 21.343 80.824 80.824 0 01-.042.335l-.001.004a.187.187 0 01-.007.02c-.065.056-.18.012-.194-.03a.164.164 0 010-.02c0-.005 0-.011.002-.019l.005-.036.035-.278.15-1.233.575-4.754zm-.763 6.302l.199.02v-.005c-.015-.041-.13-.085-.194-.03a.183.183 0 00-.005.015zM80.927 85.544c.485-4.04 1.146-9.561 1.825-15.277l.202.023A11320.95 11320.95 0 0180.4 91.61a113.556 113.556 0 01-.************ 0 01-.*************** 0 01-.************ 0 01-.09-.1.14.14 0 01.001-.023l.007-.05.036-.29.153-1.257.577-4.786zm-.772 6.383l.201.02a.193.193 0 00-.09-.097.194.194 0 00-.11.075v.002zM78.619 84.779c.486-4.055 1.144-9.56 1.811-15.184l.203.024a14137.83 14137.83 0 01-2.593 21.664l-.001.007a.093.093 0 01-.003.014c-.008.02-.124.07-.198-.01a.187.187 0 01-.002-.025v-.002-.001l.001-.008.001-.001.008-.06.037-.306.156-1.285.58-4.827zm-.781 6.48l.202.02-.002-.019c-.074-.08-.19-.03-.198-.01l-.002.008zm.201.026zM76.295 84.198c.488-4.077 1.144-9.57 1.8-15.091l.201.023a17204.58 17204.58 0 01-2.59 21.68l-.002.015-.004.014c-.093.07-.196-.012-.198-.032V90.798v-.007l.203.022v-.005c-.001-.02-.104-.101-.198-.033a.244.244 0 00-.004.015v.001l.001-.007.009-.069c.008-.067.02-.174.039-.32l.158-1.318c.14-1.157.341-2.84.585-4.88zM73.94 83.836c.489-4.088 1.14-9.558 1.784-14.975l.202.024a21886.98 21886.98 0 01-2.584 21.65l-.002.017v.005a.206.206 0 01-.03.055.17.17 0 01-.172-.077v-.01l.203.021v-.003a.17.17 0 00-.172-.077.208.208 0 00-.03.054v.004l.001-.013.01-.076.04-.336.162-1.346.588-4.917zM71.563 83.67l1.77-14.872.201.024a33930.12 33930.12 0 01-2.568 21.56l-.01.083-.002.018v.006c-.001.004-.115.084-.203-.019V90.461l.203.022v-.006c-.088-.103-.201-.023-.202-.019v.002-.001l.002-.018.01-.083.042-.35.165-1.376.592-4.962zM70.918 68.942l.202.023-2.572 21.664-.01-.001a70.908 70.908 0 01-.182-.022h-.01l2.572-21.664z",fill:"#CBD0D3"}),me=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M84.613 94.243c-5.017-2.647-10.516-3.805-16.133-3.227a.405.405 0 01-.446-.36.404.404 0 01.362-.442c5.789-.596 11.448.6 16.6 3.318a.401.401 0 01.167.545.408.408 0 01-.55.166zM84.997 91.157c-5.023-2.653-10.527-3.815-16.15-3.236a.405.405 0 01-.446-.36.404.404 0 01.363-.442c5.794-.596 11.459.603 16.615 3.327a.4.4 0 01.167.545.408.408 0 01-.55.166zM85.372 88.063c-5.028-2.653-10.53-3.814-16.15-3.236a.405.405 0 01-.446-.36.404.404 0 01.363-.442c5.791-.595 11.455.604 16.615 3.327a.4.4 0 01.167.545.408.408 0 01-.55.166zM85.739 84.969c-5.027-2.653-10.53-3.815-16.15-3.237a.405.405 0 01-.446-.359.404.404 0 01.363-.442c5.791-.596 11.455.603 16.615 3.326a.401.401 0 01.168.545.408.408 0 01-.55.167zM86.113 81.88c-5.027-2.658-10.536-3.822-16.165-3.243a.405.405 0 01-.445-.359.404.404 0 01.362-.442c5.8-.598 11.47.604 16.631 3.333a.401.401 0 01.167.546.408.408 0 01-.55.165zM86.477 78.786c-5.028-2.658-10.537-3.823-16.165-3.243a.405.405 0 01-.446-.36.404.404 0 01.362-.442c5.8-.597 11.47.606 16.632 3.334a.401.401 0 01.167.545.408.408 0 01-.55.166z",fill:"#B8BDB9"}),pe=i.createElement("path",{d:"M71.012 68.953c5.714-.588 11.303.594 16.398 3.288l-.383 3.087c-5.09-2.688-10.674-3.868-16.383-3.28l.368-3.095z",fill:"#B8BDB9"}),he=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M71.377 69.322l-.27 2.277c5.424-.461 10.724.647 15.589 3.103l.277-2.234c-4.864-2.518-10.171-3.64-15.596-3.146zm-.406-.77c5.8-.597 11.47.604 16.63 3.334l.247.13-.49 3.943-.522-.276c-5.023-2.652-10.526-3.814-16.15-3.235l-.504.052.465-3.915.323-.033z",fill:"#B8BDB9"}),be=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M90.243 88.191c.47-4.025 1.123-9.578 1.816-15.41l.202.023a8576.285 8576.285 0 00-2.367 20.146 755.542 755.542 0 00-.172 1.51h.001a.8.8 0 01-.003.02v.006l-.001.002a.145.145 0 01-.************* 0 01-.************* 0 01-.089-.096v-.008-.004l.005-.047.03-.27.14-1.216.551-4.737zm-.523 6.269a.194.194 0 00-.088-.091.197.197 0 00-.107.066l.195.025zm-.201-.01zM92.712 87.598c.472-4.03 1.124-9.57 1.811-15.359l.203.024a9910.386 9910.386 0 00-2.51 21.348l-.031.279-.004.036c0 .008 0 .014-.002.018v.006l-.005.015c-.024.036-.146.053-.195-.016a.181.181 0 01-.002-.021v-.005-.004l.006-.051.031-.28.143-1.234.555-4.756zm-.532 6.328c0-.004 0-.01-.002-.015-.05-.069-.171-.052-.195-.016l-.002.006.199.025zM95.153 87.195c.475-4.041 1.125-9.564 1.804-15.28l.202.023a11129.421 11129.421 0 00-2.509 21.325 89.368 89.368 0 00-.038.34.504.504 0 01-.002.018l-.002.007a.192.192 0 01-.************* 0 01-.09-.098v-.007l.001-.008.007-.057.033-.293a5635.92 5635.92 0 01.706-6.046zm-.542 6.409v-.002c0-.007-.03-.07-.09-.099a.194.194 0 00-.11.075l.2.026zM97.576 86.978c.478-4.056 1.127-9.562 1.795-15.186l.202.023a13876.904 13876.904 0 00-2.546 21.606l-.007.06v.013l-.001.004a.203.203 0 01-.007.02c-.09.062-.192-.014-.195-.035v-.01-.004l.001-.008.007-.063.035-.306.15-1.285.566-4.83zm-.556 6.505v-.008c-.003-.021-.104-.097-.195-.036a.18.18 0 00-.006.018l.201.026zm-.202-.02z",fill:"#EBEEF0"}),ve=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M89.522 94.057c5.648-1.465 11.434-1.333 16.925.57a.401.401 0 01.249.514.407.407 0 01-.517.247c-5.33-1.848-10.95-1.978-16.452-.552a.403.403 0 11-.205-.78zM89.865 90.966c5.655-1.47 11.446-1.34 16.943.566a.4.4 0 01.249.514.407.407 0 01-.517.247c-5.336-1.85-10.961-1.979-16.47-.547a.403.403 0 11-.206-.78zM90.236 87.872c5.654-1.47 11.444-1.34 16.938.565a.4.4 0 01.249.513.407.407 0 01-.517.248c-5.333-1.849-10.957-1.978-16.464-.547a.403.403 0 11-.205-.78zM90.603 84.776c5.654-1.469 11.444-1.339 16.939.566a.402.402 0 11-.269.76c-5.332-1.848-10.957-1.977-16.464-.546a.403.403 0 11-.206-.78zM90.954 81.687c5.66-1.474 11.458-1.346 16.96.563a.402.402 0 01.249.513.408.408 0 01-.518.247c-5.339-1.852-10.971-1.98-16.485-.544a.403.403 0 11-.206-.78zM91.317 78.592c5.66-1.473 11.458-1.346 16.961.563a.402.402 0 11-.269.76c-5.34-1.852-10.972-1.979-16.486-.544a.403.403 0 11-.206-.78zM91.705 75.492c5.654-1.469 11.447-1.339 16.943.567a.402.402 0 01.249.513.407.407 0 01-.518.247c-5.334-1.849-10.96-1.978-16.469-.548a.403.403 0 11-.205-.78zM92.056 72.402c5.66-1.474 11.458-1.345 16.96.563a.402.402 0 01.249.513.407.407 0 01-.518.248c-5.34-1.852-10.972-1.98-16.486-.544a.403.403 0 11-.206-.78z",fill:"#CBD0D3"}),Ee=i.createElement("path",{d:"M92.158 72.792c5.587-1.455 11.305-1.326 16.723.553l-1.103 9.284c-5.421-1.88-11.136-2.008-16.723-.553l1.103-9.284z",fill:"#CBD0D3"}),_e=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M92.53 73.113l-1.002 8.43c5.313-1.28 10.734-1.147 15.906.545l1.005-8.467c-5.159-1.721-10.587-1.844-15.91-.508zm-.475-.71c5.66-1.475 11.461-1.345 16.961.562.179.062.291.24.269.427l-1.103 9.285a.4.4 0 01-.188.294.41.41 0 01-.35.039c-5.339-1.852-10.971-1.98-16.485-.544a.408.408 0 01-.37-.086.4.4 0 01-.136-.35l1.102-9.285c.02-.164.139-.3.3-.343z",fill:"#CBD0D3"}),Oe=i.createElement("path",{d:"M127.892 91.34c-1.329 7.115-12.918 8.843-24.256 0M49.088 84.625c0 6.05 9.181 11.081 24.545 3.457",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"});t.a=function SvgNoAudienceBannerGraphic(e){return i.createElement("svg",r({viewBox:"0 0 211 109",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p,h,b,v,E,_,O,k,y,j,S,w,C,A,N,T,x,D,R,M,I,P,L,B,z,F,V,W,H,U,q,G,K,Y,X,$,Z,Q,J,ee,te,ne,ie,re,ae,oe,ce,le,se,ue,de,ge,fe,me,pe,he,be,ve,Ee,_e,Oe)}},function(e,t,n){"use strict";(function(e){var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=Object(c.forwardRef)((function(t,n){var i=t.className,a=t.children,o=t.Icon,c=t.SVGGraphic;return e.createElement("div",{ref:n,className:r()("googlesitekit-lean-cta-banner",i)},e.createElement("div",{className:"googlesitekit-lean-cta-banner__body"},o&&e.createElement("div",{className:"googlesitekit-lean-cta-banner__body-icon"},e.createElement(o,{width:"32",height:"32"})),e.createElement("div",{className:"googlesitekit-lean-cta-banner__body-content"},a)),c&&e.createElement("div",{className:"googlesitekit-lean-cta-banner__graphic"},e.createElement(c,null)))}));l.propTypes={className:o.a.string,children:o.a.node.isRequired,Icon:o.a.elementType,SVGGraphic:o.a.elementType},t.a=l}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M2.956 80.014a32.365 32.365 0 006.283 9.673c10.459 11.015 25.911 11.949 40.083 9.193A98.307 98.307 0 0088.91 81.449c6.738-4.994 13.394-11.19 22.316-11.467 3.35-.103 6.814.82 9.345 2.998 5.259 4.497 5.165 12.186 9.269 17.516 5.458 7.088 13.308 9.283 21.87 10.101 24.191 2.309 53.006-5.265 69.646-23.694 14.857-16.452 16.64-42.63-1.343-57.629-8.216-6.852-19.483-9.992-30.117-8.392-8.127 1.223-16.378 4.942-24.791 5.977-9.908 1.22-16.033-2.943-24.05-7.79C127.086.641 108.62-2.597 92.807 2.292 77.671 6.972 66.777 19.747 52.048 25.36c-12.727 4.852-27.762 4.114-38.82 12.017C.143 46.727-3.146 65.603 2.956 80.014z",fill:"#F3F5F7"}),o=i.createElement("path",{d:"M118.945 116.194c32.642 0 59.104-1.654 59.104-3.694s-26.462-3.694-59.104-3.694c-32.643 0-59.105 1.654-59.105 3.694s26.462 3.694 59.105 3.694z",fill:"#161B18",opacity:.1}),c=i.createElement("path",{d:"M118.945 116.194c32.642 0 59.104-1.654 59.104-3.694s-26.462-3.694-59.104-3.694c-32.643 0-59.105 1.654-59.105 3.694s26.462 3.694 59.105 3.694z",fill:"#CBD0D3"}),l=i.createElement("path",{d:"M99.725 51.387c1.758 6.518 7.872 11.126 14.356 13.01 6.484 1.882 13.377 1.514 20.12 1.177 3.188-.158 6.449-.298 9.503.627 3.054.925 5.912 3.137 6.724 6.222.466 1.773.121 3.686-.787 5.274",stroke:"#161B18",strokeWidth:1.396,strokeLinecap:"round",strokeLinejoin:"round"}),s=i.createElement("path",{d:"M87.114 62.487c-1.015 16.075-6.61 30.497-2.87 48.618h-4.26M91.929 111.105h-4.261l7.53-48.618",stroke:"#161B18",strokeWidth:1.4,strokeLinecap:"round",strokeLinejoin:"round"}),u=i.createElement("path",{d:"M73.527 57.419c-3.635 1.642-10.613 5.299-10.613 12.724",stroke:"#000",strokeWidth:1.396,strokeLinecap:"round"}),d=i.createElement("path",{d:"M103.118 84.07l-2.957.269-18.04 1.632-10.545.952-1-23.491-1.74-40.88 32.912-1.199.664 30.445.09 4.099.612 28.078.004.095z",fill:"#CBD0D3"}),g=i.createElement("path",{d:"M100.163 84.338l1.697-.154 1.261-.114-1.371-62.719-1.891.087 1.017 59.457-29.439 2.786.103 2.34.04.9 10.544-.952 18.041-1.632-.002.001z",fill:"#999F9B"}),f=i.createElement("path",{d:"M62.912 70.143c0 4.321 4.917 7.437 8.424 5.913",stroke:"#000",strokeWidth:1.396,strokeLinecap:"round"}),m=i.createElement("path",{d:"M159.169 21.79l-22.985 89.068",stroke:"#7B807D",strokeWidth:3.607,strokeMiterlimit:10,strokeLinecap:"round"}),p=i.createElement("path",{d:"M157.57 14.896l-34.151 34.351a3.61 3.61 0 00.016 5.1 3.608 3.608 0 001.62.929l46.822 12.4a3.606 3.606 0 004.404-4.435l-12.674-46.745a3.599 3.599 0 00-2.557-2.542 3.605 3.605 0 00-3.48.942z",fill:"#E77D5B"}),h=i.createElement("path",{d:"M153.345 35.252l2.003-7.566 3.905 1.034-2.003 7.566-2.874 9.163-3.103-.822 2.072-9.375zm-2.709 18.123a2.77 2.77 0 01-1.715-1.274 2.768 2.768 0 01-.259-2.121c.197-.744.619-1.304 1.265-1.68a2.77 2.77 0 012.121-.259c.744.197 1.304.619 1.68 1.266.375.646.465 1.342.268 2.085a2.77 2.77 0 01-1.275 1.715c-.646.376-1.342.465-2.085.268z",fill:"#962C0A"}),b=i.createElement("path",{d:"M149.639 77.697a6.848 6.848 0 01-3.747 3.098c-3.335 1.14-7.399-.673-8.778-3.916",stroke:"#161B18",strokeWidth:1.396,strokeLinecap:"round",strokeLinejoin:"round"});t.a=function SvgAudienceSegmentationErrorFullWidth(e){return i.createElement("svg",r({viewBox:"0 0 233 117",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p,h,b)}},,,,,,,,,,,,,function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M0 19h22L11 0 0 19zm12-3h-2v-2h2v2zm0-4h-2V8h2v4z",fill:"currentColor"});t.a=function SvgWarningV2(e){return i.createElement("svg",r({viewBox:"0 0 22 19"},e),a)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PageHeader}));var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(17),l=n(311),s=n(312),u=n(76);function PageHeader(t){var n=t.title,i=t.icon,a=t.className,o=t.status,d=t.statusText,g=t.fullWidth,f=t.children,m=g?{size:12}:{smSize:4,mdSize:4,lgSize:6},p=""!==o||Boolean(f);return e.createElement("header",{className:"googlesitekit-page-header"},e.createElement(c.k,null,n&&e.createElement(c.a,m,i,e.createElement("h1",{className:r()("googlesitekit-page-header__title",a)},n)),p&&e.createElement(c.a,{alignBottom:!0,mdAlignRight:!0,smSize:4,mdSize:4,lgSize:6},e.createElement("div",{className:"googlesitekit-page-header__details"},o&&e.createElement("span",{className:r()("googlesitekit-page-header__status","googlesitekit-page-header__status--".concat(o))},d,e.createElement(u.a,null,"connected"===o?e.createElement(l.a,{width:10,height:8}):e.createElement(s.a,{width:2,height:12}))),f))))}PageHeader.propTypes={title:o.a.string,icon:o.a.node,className:o.a.string,status:o.a.string,statusText:o.a.string,fullWidth:o.a.bool},PageHeader.defaultProps={title:"",icon:null,className:"googlesitekit-heading-3",status:"",statusText:"",fullWidth:!1}}).call(this,n(4))},,,,,,function(e,t,n){"use strict";(function(e,i){var r=n(2),a=n(10),o=n(3),c=n(399),l=n(400),s=n(204),u=n(93),d=n(24),g=n(23),f=n(7),m=n(19),p=n(18),h=n(34),b=n(9),v=n(8),E=n(52),_=n(50);t.a=Object(_.a)({moduleName:"analytics-4"})((function AudienceSegmentationIntroductoryOverlayNotification(){var t=Object(p.a)(),n=Object(h.a)(),_=Object(d.e)(),O=Object(E.c)(),k=Object(o.useSelect)((function(e){return e(f.a).isDismissingItem("audienceSegmentationIntroductoryOverlayNotification")})),y=Object(o.useSelect)((function(e){var t=e(f.a).isItemDismissed("audienceSegmentationIntroductoryOverlayNotification"),i=e(f.a).isAudienceSegmentationWidgetHidden(),r=e(m.a).isModuleActive("analytics-4"),a=!n||e(f.a).canViewSharedModule("analytics-4"),o=e(v.r).getAudienceSegmentationSetupCompletedBy(),c=e(f.a).getID();return E.b===O&&!1===t&&!1===i&&r&&a&&Number.isInteger(o)&&o!==c})),j=Object(o.useDispatch)(g.b).dismissOverlayNotification,S=function(){j("audienceSegmentationIntroductoryOverlayNotification")};return i.createElement(s.a,{shouldShowNotification:y,GraphicDesktop:c.a,GraphicMobile:l.a,notificationID:"audienceSegmentationIntroductoryOverlayNotification",onShow:function(){Object(b.I)("".concat(t,"_audiences-secondary-user-intro"),"view_notification")}},i.createElement("div",{className:"googlesitekit-overlay-notification__body"},i.createElement("h3",null,Object(r.__)("New! Visitor groups","google-site-kit")),i.createElement("p",null,Object(r.__)("You can now learn more about your site visitor groups by comparing different metrics","google-site-kit"))),i.createElement("div",{className:"googlesitekit-overlay-notification__actions"},i.createElement(a.Button,{tertiary:!0,disabled:k,onClick:function(){Object(b.I)("".concat(t,"_audiences-secondary-user-intro"),"dismiss_notification").finally((function(){S()}))}},Object(r.__)("Got it","google-site-kit")),i.createElement(a.Button,{disabled:k,onClick:function(n){n.preventDefault();setTimeout((function(){e.scrollTo({top:Object(u.a)(".googlesitekit-widget-area--mainDashboardTrafficAudienceSegmentation",_),behavior:"smooth"})}),0),Object(b.I)("".concat(t,"_audiences-secondary-user-intro"),"confirm_notification").finally((function(){S()}))}},Object(r.__)("Show me","google-site-kit"))))}))}).call(this,n(28),n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M1 3.838L4.106 7 10 1",stroke:"currentColor",strokeWidth:1.5});t.a=function SvgTick(e){return i.createElement("svg",r({viewBox:"0 0 11 9",fill:"none"},e),a)}},,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return I}));var i,r=n(21),a=n.n(r),o=n(15),c=n.n(o),l=n(6),s=n.n(l),u=n(1),d=n.n(u),g=n(377),f=n(2),m=n(0),p=n(3),h=n(50),b=n(29),v=n(7),E=n(13),_=n(41),O=n(8),k=n(412),y=n(18),j=n(175),S=n(246),w=n(9),C=n(413),A=n(247),N=n(242),T=n(179),x=n(459),D=n(460),R=n(461),M=n(24),I="audience_segmentation_setup_cta-notification",P=(i={},s()(i,M.b,R.a),s()(i,M.c,D.a),i);function AudienceSegmentationSetupCTAWidget(t){var n,i=t.id,r=t.Notification,o=Object(y.a)(),l=Object(M.e)(),s="".concat(o,"_audiences-setup-cta-dashboard"),u=Object(p.useDispatch)(_.a),d=u.invalidateResolution,g=u.dismissNotification,h=Object(p.useDispatch)(b.a).setValues,S={tooltipSlug:i,title:Object(f.__)("You can always enable groups in Settings later","google-site-kit"),content:Object(f.__)("The visitors group section will be added to your dashboard once you set it up.","google-site-kit"),dismissLabel:Object(f.__)("Got it","google-site-kit")},D=Object(j.b)(S),R=Object(p.useSelect)((function(e){return e(_.a).isNotificationDismissalFinal(i)})),I=Object(p.useSelect)((function(e){return e(b.a).getValue(O.c,"autoSubmit")})),L=Object(m.useState)(!1),B=c()(L,2),z=B[0],F=B[1],V=Object(p.useDispatch)(v.a).dismissItem,W=Object(m.useCallback)((function(){d("getQueuedNotifications",[o,_.c.DEFAULT]),g(i),V(k.a)}),[V,i,d,g,o]),H=Object(m.useCallback)((function(){F(!0)}),[F]),U=Object(C.a)({onSuccess:W,onError:H}),q=U.apiErrors,G=U.failedAudiences,K=U.isSaving,Y=U.onEnableGroups,X=Object(p.useDispatch)(v.a).clearPermissionScopeError,$=Object(p.useDispatch)(E.c).setSetupErrorCode,Z=Object(m.useCallback)((function(){h(O.c,{autoSubmit:!1}),X(),$(null),F(!1)}),[X,$,h]),Q=Object(p.useSelect)((function(e){return e(E.c).getSetupErrorCode()})),J=I&&"access_denied"===Q,ee={gaTrackingEventArgs:{category:s}};return e.createElement(m.Fragment,null,e.createElement(r,ee,e.createElement(N.a,{id:i,title:Object(f.__)("Learn how different types of visitors interact with your site","google-site-kit"),description:e.createElement("p",null,Object(f.__)("Understand what brings new visitors to your site and keeps them coming back. Site Kit can now group your site visitors into relevant segments like “new“ and “returning“. To set up these new groups, Site Kit needs to update your Google Analytics property.","google-site-kit")),actions:e.createElement(T.a,a()({id:i,className:"googlesitekit-setup-cta-banner__actions-wrapper",ctaLabel:K?Object(f.__)("Enabling groups","google-site-kit"):Object(f.__)("Enable groups","google-site-kit"),onCTAClick:Y,isSaving:K,dismissOnCTAClick:!1,dismissLabel:R?Object(f.__)("Don’t show again","google-site-kit"):Object(f.__)("Maybe later","google-site-kit"),onDismiss:D,ctaDismissOptions:{skipHidingFromQueue:!0},dismissExpires:R?0:2*w.f},ee)),SVG:null!==(n=P[l])&&void 0!==n?n:x.a,primaryCellSizes:{lg:7,md:8},SVGCellSizes:{lg:5}})),(z||J)&&e.createElement(A.a,{hasOAuthError:J,apiErrors:q.length?q:G,onRetry:Y,inProgress:K,onCancel:J?Z:function(){return F(!1)},trackEventCategory:"".concat(o,"_audiences-setup")}))}AudienceSegmentationSetupCTAWidget.propTypes={id:d.a.string,Notification:d.a.elementType},t.b=Object(g.a)(Object(h.a)({moduleName:"analytics-4"}),Object(S.g)("audienceSegmentationSetupCTA"))(AudienceSegmentationSetupCTAWidget)}).call(this,n(4))},,,,function(e,t,n){"use strict";function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,n=e.filter((function(e){var t=e.dimensionValues;return"(not set)"!==t[0].value&&""!==t[0].value}));return n.slice(0,t)}n.d(t,"a",(function(){return i}))},,function(e,t,n){"use strict";var i=n(247);n.d(t,"b",(function(){return i.a}));n(203),n(371),n(378);var r=n(440);n.d(t,"d",(function(){return r.a}));var a=n(441);n.d(t,"c",(function(){return a.a}));var o=n(442);n.d(t,"a",(function(){return o.a}));var c=n(443);n.d(t,"e",(function(){return c.a}));var l=n(444);n.d(t,"f",(function(){return l.a}));var s=n(445);n.d(t,"g",(function(){return s.a}));n(248),n(343);var u=n(446);n.d(t,"h",(function(){return u.a}))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTooltipMessage}));var i=n(1),r=n.n(i),a=n(0),o=n(38),c=n(2),l=n(3),s=n(20),u=n(13);function AudienceTooltipMessage(t){var n=t.audienceSlug,i=t.audienceName,r=Object(l.useSelect)((function(e){return e(u.c).getDocumentationLinkURL("visitor-group-insights")}));return Object(a.useMemo)((function(){switch(n){case"new-visitors":return Object(o.a)(Object(c.sprintf)(
/* translators: %s: is the audience name */
Object(c.__)('%s are people who visited your site for the first time. Note that under some circumstances it\'s possible for a visitor to be counted in both the "new" and "returning" groups. <link>Learn more</link>',"google-site-kit"),"<strong>New visitors</strong>"),{strong:e.createElement("strong",null),link:e.createElement(s.a,{href:r,external:!0,hideExternalIndicator:!0})});case"returning-visitors":return Object(o.a)(Object(c.sprintf)(
/* translators: %s: is the audience name */
Object(c.__)('%s are people who have visited your site at least once before. Note that under some circumstances it\'s possible for a visitor to be counted in both the "new" and "returning" groups. <link>Learn more</link>',"google-site-kit"),"<strong>Returning visitors</strong>"),{strong:e.createElement("strong",null),link:e.createElement(s.a,{href:r,external:!0,hideExternalIndicator:!0})});default:return Object(o.a)(Object(c.sprintf)(
/* translators: %s: is the audience name */
Object(c.__)("%s is an audience that already exists in your Analytics property. Note that it's possible for a visitor to be counted in more than one group. <link>Learn more</link>","google-site-kit"),"<strong>".concat(i,"</strong>")),{strong:e.createElement("strong",null),link:e.createElement(s.a,{href:r,external:!0,hideExternalIndicator:!0})})}}),[n,i,r])}AudienceTooltipMessage.propTypes={audienceSlug:r.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RetryButton}));var i=n(1),r=n.n(i),a=n(2),o=n(10),c=n(9),l=n(18);function RetryButton(t){var n=t.handleRetry,i=Object(l.a)();return e.createElement(o.Button,{className:"googlesitekit-audience-selection-panel__error-notice-action",onClick:function(){n(),Object(c.I)("".concat(i,"_audiences-sidebar"),"data_loading_error_retry")},tertiary:!0},Object(a.__)("Retry","google-site-kit"))}RetryButton.propTypes={handleRetry:r.a.func.isRequired}}).call(this,n(4))},,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdBlockerWarningMessage}));var i=n(1),r=n.n(i),a=n(2),o=n(38),c=n(20),l=n(217),s=n(396);function AdBlockerWarningMessage(t){var n=t.className,i=void 0===n?"":n,r=t.getHelpLink,u=void 0===r?"":r,d=t.warningMessage,g=void 0===d?null:d;return g?e.createElement(l.a,{className:i},Object(o.a)(Object(a.sprintf)(
/* translators: 1: The warning message. 2: "Get help" text. */
Object(a.__)("%1$s. <Link>%2$s</Link>","google-site-kit"),g,Object(a.__)("Get help","google-site-kit")),{Link:e.createElement(c.a,{href:u,external:!0,hideExternalIndicator:!0,trailingIcon:e.createElement(s.a,{width:15,height:15})})})):null}AdBlockerWarningMessage.propTypes={className:r.a.string,getHelpLink:r.a.string,warningMessage:r.a.string}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M4.5 1.5H3a2 2 0 00-2 2v7a2 2 0 002 2h7a2 2 0 002-2V9M7 1.5h5v5M5 8.5L11.5 2",stroke:"currentColor",strokeWidth:1.5});t.a=function SvgExternalRounded(e){return i.createElement("svg",r({viewBox:"0 0 13 14",fill:"none"},e),a)}},,,function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{clipPath:"url(#audience-segmentation-introductory-graphic-desktop_svg__clip0_1395_20972)"},i.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h264c8.837 0 16 7.163 16 16v147H0V16z",fill:"#B8E6CA"}),i.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-desktop_svg__filter0_d_1395_20972)"},i.createElement("rect",{x:-10,y:25,width:153,height:174,rx:11,fill:"#fff"})),i.createElement("rect",{x:9.031,y:110.641,width:53.016,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:9.031,y:95.688,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:9.031,y:148.703,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("path",{d:"M94.672 108.602a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 010 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),i.createElement("rect",{x:9,y:46,width:36,height:9,rx:4.5,fill:"#EBEEF0"}),i.createElement("path",{d:"M94.672 161.617a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 110 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#FFDED3"}),i.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-desktop_svg__filter1_d_1395_20972)"},i.createElement("rect",{x:152,y:25,width:153,height:174,rx:11,fill:"#fff"})),i.createElement("rect",{x:170.955,y:110.641,width:52.805,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:170.955,y:95.688,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:170.955,y:148.703,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("path",{d:"M256.256 108.602a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),i.createElement("rect",{x:171,y:46,width:36,height:9,rx:4.5,fill:"#EBEEF0"}),i.createElement("path",{d:"M295 73.5H152",stroke:"#EBEEF0",strokeWidth:2}),i.createElement("path",{d:"M256.256 161.617a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836z",fill:"#FFDED3"}),i.createElement("path",{d:"M143 73.5H0",stroke:"#EBEEF0",strokeWidth:2})),o=i.createElement("defs",null,i.createElement("filter",{id:"audience-segmentation-introductory-graphic-desktop_svg__filter0_d_1395_20972",x:-26,y:13,width:185,height:206,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1395_20972"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1395_20972",result:"shape"})),i.createElement("filter",{id:"audience-segmentation-introductory-graphic-desktop_svg__filter1_d_1395_20972",x:136,y:13,width:185,height:206,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1395_20972"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1395_20972",result:"shape"})),i.createElement("clipPath",{id:"audience-segmentation-introductory-graphic-desktop_svg__clip0_1395_20972"},i.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h264c8.837 0 16 7.163 16 16v147H0V16z",fill:"#fff"})));t.a=function SvgAudienceSegmentationIntroductoryGraphicDesktop(e){return i.createElement("svg",r({viewBox:"0 0 296 163",fill:"none"},e),a,o)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M41.064 27.146a64.127 64.127 0 016.51-5.868C69.418 4.126 87.464 4.153 112.45 7.283c16.891 2.116 26.759 10.166 49.788 8.9 23.029-1.266 28.929-7.127 57.117-5.25 22.315 1.487 32.324 5.897 52.163 16.213 18.36 9.549 35.031 26.324 43.408 48.509 14.361 38.026-11.243 106.466-45.58 109.693-24.881 2.339-45.414-25.243-70.527-18.855-15.47 3.936-24.646 20.444-36.581 31.339-13.925 12.711-43.922 11.912-60.227 5.129-15.538-6.464-30.653-19.276-35.728-38.145-3.863-14.369-4.916-31.498-15.733-44.622-13.09-15.883-21.087-22.968-25.581-44.54-3.903-18.734 4.494-36.505 16.095-48.508z",fill:"#B8E6CA"}),o=i.createElement("path",{d:"M41.064 27.146a64.127 64.127 0 016.51-5.868C69.418 4.126 87.464 4.153 112.45 7.283c16.891 2.116 26.759 10.166 49.788 8.9 23.029-1.266 28.929-7.127 57.117-5.25 22.315 1.487 32.324 5.897 52.163 16.213 18.36 9.549 35.031 26.324 43.408 48.509 14.361 38.026-11.243 106.466-45.58 109.693-24.881 2.339-45.414-25.243-70.527-18.855-15.47 3.936-24.646 20.444-36.581 31.339-13.925 12.711-43.922 11.912-60.227 5.129-15.538-6.464-30.653-19.276-35.728-38.145-3.863-14.369-4.916-31.498-15.733-44.622-13.09-15.883-21.087-22.968-25.581-44.54-3.903-18.734 4.494-36.505 16.095-48.508z",fill:"#B8E6CA"}),c=i.createElement("g",{mask:"url(#audience-segmentation-introductory-graphic-mobile_svg__a)"},i.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-mobile_svg__filter0_d_2898_16651)"},i.createElement("rect",{x:71.449,y:21.433,width:100.401,height:136.493,rx:7.218,fill:"#fff"})),i.createElement("rect",{x:83.941,y:77.631,width:34.79,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("rect",{x:83.941,y:67.819,width:12.489,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("rect",{x:83.941,y:99.983,width:12.489,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("path",{d:"M140.133 76.293a5.798 5.798 0 015.798-5.798h8.921a5.798 5.798 0 010 11.596h-8.921a5.798 5.798 0 01-5.798-5.798z",fill:"#B8E6CA"}),i.createElement("rect",{x:83.926,y:35.213,width:23.624,height:5.906,rx:2.953,fill:"#EBEEF0"}),i.createElement("path",{d:"M140.133 108.458a5.798 5.798 0 015.798-5.798h8.921a5.798 5.798 0 010 11.597h-8.921a5.798 5.798 0 01-5.798-5.799z",fill:"#FFDED3"}),i.createElement("rect",{x:83.043,y:109.796,width:36.574,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("path",{d:"M171.848 53.259H72.103",stroke:"#EBEEF0",strokeWidth:1.312}),i.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-mobile_svg__filter1_d_2898_16651)"},i.createElement("rect",{x:184.973,y:21.433,width:100.401,height:136.493,rx:7.218,fill:"#fff"})),i.createElement("rect",{x:197.414,y:77.631,width:34.652,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("rect",{x:197.414,y:67.819,width:12.439,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("rect",{x:197.41,y:99.983,width:12.439,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("path",{d:"M253.391 76.293a5.798 5.798 0 015.798-5.798h8.839a5.798 5.798 0 010 11.596h-8.839a5.798 5.798 0 01-5.798-5.798z",fill:"#B8E6CA"}),i.createElement("rect",{x:197.449,y:35.213,width:23.624,height:5.906,rx:2.953,fill:"#EBEEF0"}),i.createElement("path",{d:"M278.82 53.259h-93.838",stroke:"#EBEEF0",strokeWidth:1.312}),i.createElement("path",{d:"M253.391 108.458a5.798 5.798 0 015.798-5.798h8.839a5.798 5.798 0 010 11.597h-8.839a5.798 5.798 0 01-5.798-5.799z",fill:"#FFDED3"}),i.createElement("rect",{x:196.523,y:109.796,width:36.429,height:6.244,rx:3.122,fill:"#EBEEF0"})),l=i.createElement("defs",null,i.createElement("filter",{id:"audience-segmentation-introductory-graphic-mobile_svg__filter0_d_2898_16651",x:55.449,y:9.433,width:132.402,height:168.493,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2898_16651"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2898_16651",result:"shape"})),i.createElement("filter",{id:"audience-segmentation-introductory-graphic-mobile_svg__filter1_d_2898_16651",x:168.973,y:9.433,width:132.402,height:168.493,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2898_16651"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2898_16651",result:"shape"})),i.createElement("clipPath",{id:"audience-segmentation-introductory-graphic-mobile_svg__clip0_2898_16651"},i.createElement("path",{fill:"#fff",d:"M0 0h343v128H0z"})));t.a=function SvgAudienceSegmentationIntroductoryGraphicMobile(e){return i.createElement("svg",r({viewBox:"0 0 343 123",fill:"none"},e),i.createElement("g",{clipPath:"url(#audience-segmentation-introductory-graphic-mobile_svg__clip0_2898_16651)"},a,i.createElement("mask",{id:"audience-segmentation-introductory-graphic-mobile_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:24,y:5,width:295,height:203},o),c),l)}},,,,,,,,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var i=n(15),r=n.n(i),a=n(535),o=n(356),c=n(0),l=n(139),s=n(23),u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.sticky,n=void 0!==t&&t,i=Object(c.useContext)(l.a),u=Object(c.useState)(!1),d=r()(u,2),g=d[0],f=d[1],m=Object(o.a)((function(e){return e(s.b).getInViewResetCount()})),p=Object(o.a)((function(e){return e(s.b).getValue("forceInView")}));return Object(c.useEffect)((function(){i.value&&!g&&f(!0)}),[g,i,f]),Object(c.useEffect)((function(){p&&f(!0)}),[p]),Object(a.a)((function(){f(!1)}),[m]),!(!n||!g)||!!i.value}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return SetupSuccess}));var i=n(0),r=n(3),a=n(7),o=n(18),c=n(113),l=n(9),s=n(458),u=Object(c.a)(s.a),d="settings_visitor_groups_setup_success_notification";function SetupSuccess(){var t=Object(o.a)(),n=Object(r.useDispatch)(a.a).dismissItem,c=Object(r.useSelect)((function(e){return e(a.a).isAudienceSegmentationWidgetHidden()})),s=Object(r.useSelect)((function(e){return e(a.a).isItemDismissed(d)})),g=c&&!1===s;return Object(i.useEffect)((function(){g&&n(d)}),[n,g]),void 0===s||s||g?null:e.createElement(u,{onInView:function(){Object(l.I)("".concat(t,"_audiences-setup-cta-settings-success"),"view_notification")}})}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return v}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(206),u=n(2),d=n(0),g=n(157),f=n(3),m=n(29),p=n(7),h=n(35),b=n(8);function v(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.redirectURL,i=t.onSuccess,a=t.onError,c=Object(s.a)(),v=Object(d.useState)([]),E=l()(v,2),_=E[0],O=E[1],k=Object(d.useState)([]),y=l()(k,2),j=y[0],S=y[1],w=Object(d.useState)(!1),C=l()(w,2),A=C[0],N=C[1],T=Object(f.useSelect)((function(e){return e(p.a).hasScope(b.h)})),x=Object(f.useSelect)((function(e){return e(m.a).getValue(b.c,"autoSubmit")})),D=Object(f.useDispatch)(m.a),R=D.setValues,M=Object(f.useDispatch)(p.a),I=M.setPermissionScopeError,P=Object(f.useDispatch)(b.r),L=P.enableAudienceGroup,B=P.fetchSyncAvailableCustomDimensions,z=P.determineNeedForAnalytics4EditScope,F=P.syncAvailableAudiences;n||(n=Object(g.a)(e.location.href,{notification:"audience_segmentation"}));var V=Object(d.useCallback)(o()(r.a.mark((function e(){var t,n,i,a,o,c,l,s,u,d;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,F();case 2:if(t=e.sent,!(n=t.error)){e.next=6;break}return e.abrupt("return",{error:n});case 6:return e.next=8,B();case 8:if(i=e.sent,!(a=i.error)){e.next=12;break}return e.abrupt("return",{error:a});case 12:if(T){e.next=24;break}return e.next=15,z();case 15:if(o=e.sent,c=o.error,l=o.needsScope,!c){e.next=22;break}return e.abrupt("return",{error:c});case 22:if(!l){e.next=24;break}return e.abrupt("return",{needsScope:!0});case 24:return R(b.c,{autoSubmit:!1}),e.next=27,L(j);case 27:if(e.t0=e.sent,e.t0){e.next=30;break}e.t0={};case 30:return s=e.t0,u=s.error,d=s.failedSiteKitAudienceSlugs,e.abrupt("return",{error:u,failedSiteKitAudienceSlugs:d});case 34:case"end":return e.stop()}}),e)}))),[L,j,B,T,z,R,F]),W=Object(d.useCallback)(o()(r.a.mark((function t(){var o,l,s,d,g;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return N(!0),t.next=3,V();case 3:if(o=t.sent,l=o.error,s=o.needsScope,d=o.failedSiteKitAudienceSlugs,!s){t.next=11;break}return R(b.c,{autoSubmit:!0}),I({code:h.a,message:Object(u.__)("Additional permissions are required to create new audiences in Analytics.","google-site-kit"),data:{status:403,scopes:[b.h],skipModal:!0,skipDefaultErrorNotifications:!0,redirectURL:n,errorRedirectURL:e.location.href}}),t.abrupt("return");case 11:l||d?null==a||a():null==i||i(),c()&&(g=function _newArrayIfNotEmpty(e){return e.length?[]:e},l?(O([l]),S(g)):Array.isArray(d)?(S(d),O(g)):(O(g),S(g)),N(!1));case 13:case"end":return t.stop()}}),t)}))),[V,c,R,I,n,a,i]);return Object(d.useEffect)((function(){T&&x&&W()}),[T,x,W]),{apiErrors:_,failedAudiences:j,isSaving:A,onEnableGroups:W}}}).call(this,n(28))},,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";(function(e){var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(1),u=n.n(s),d=n(0),g=n(3),f=n(50),m=n(8),p=n(7),h=n(462),b=n(177),v=n(203),E=n(343),_=n(244),O=n(35);function AudienceTilesWidget(t){var n=t.Widget,i=Object(g.useSelect)((function(e){var t=e(m.r).getAvailableAudiences();return null==t?void 0:t.map((function(e){return e.name}))})),a=Object(g.useSelect)((function(e){return e(p.a).getConfiguredAudiences()})),c=Object(d.useState)(!1),s=l()(c,2),u=s[0],f=s[1],k=Object(g.useDispatch)(m.r),y=k.clearErrors,j=k.maybeSyncAvailableAudiences,S=k.syncAvailableAudiences,w=Object(g.useSelect)((function(e){return e(m.r).isSettingUpAudiences()})),C=Object(g.useSelect)((function(e){return e(m.r).getErrorForAction("syncAvailableAudiences")}));if(Object(d.useEffect)((function(){u||w||function(){var e=o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,j();case 2:f(!0);case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()()}),[u,w,j]),C){var A=Object(O.e)(C);return e.createElement(v.a,{errors:C,Widget:n,onRetry:A?void 0:o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,y("syncAvailableAudiences");case 2:return e.next=4,S();case 4:case"end":return e.stop()}}),e)}))),showRetryButton:!A})}return(null==a?void 0:a.some((function(e){return null==i?void 0:i.includes(e)})))?e.createElement(h.a,{Widget:n,widgetLoading:!u||!i||!a}):u?e.createElement(E.a,{Widget:n,WidgetNull:_.a}):e.createElement(n,{className:"googlesitekit-widget-audience-tiles",noPadding:!0},e.createElement("div",{className:"googlesitekit-widget-audience-tiles__body"},e.createElement(n,{noPadding:!0},e.createElement(b.a,null)),e.createElement(n,{noPadding:!0},e.createElement(b.a,null))))}AudienceTilesWidget.propTypes={Widget:u.a.elementType.isRequired,WidgetNull:u.a.elementType.isRequired},t.a=Object(f.a)({moduleName:"analytics-4"})(AudienceTilesWidget)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceSelectionPanel}));var i=n(3),r=n(40),a=n(23),o=n(160),c=n(487);function AudienceSelectionPanel(){var t=Object(i.useSelect)((function(e){return e(a.b).getValue(r.i)}));return e.createElement(o.a,{value:{key:"AudienceSelectionPanel",value:!!t}},e.createElement(c.a,null))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceAreaFooter}));var i=n(2),r=n(3),a=n(7),o=n(8),c=n(19),l=n(134),s=n(34);function AudienceAreaFooter(){var t=Object(s.a)(),n=Object(r.useSelect)((function(e){return e(a.a).getDateRangeDates({offsetDays:o.g})})),u=Object(r.useSelect)((function(e){return t?null:e(o.r).getServiceReportURL("audiences",{dates:n})}));return Object(r.useSelect)((function(e){return e(c.a).isModuleConnected("analytics-4")}))?e.createElement(l.a,{className:"googlesitekit-audience-widget__source",name:Object(i._x)("Analytics","Service name","google-site-kit"),href:u,external:!0}):null}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeGroupsLink}));var i=n(0),r=n(2),a=n(3),o=n(18),c=n(9),l=n(40),s=n(23),u=n(8),d=n(20),g=n(310);function ChangeGroupsLink(){var t=Object(o.a)(),n=Object(a.useInViewSelect)((function(e){return e(u.r).getConfigurableAudiences()}),[]),f=Object(a.useDispatch)(s.b).setValue,m=Object(i.useCallback)((function(){f(l.i,!0),Object(c.I)("".concat(t,"_audiences-sidebar"),"change_groups")}),[f,t]);return Array.isArray(n)&&(null==n?void 0:n.length)>0?e.createElement(i.Fragment,null,e.createElement(d.a,{secondary:!0,linkButton:!0,className:"googlesitekit-widget-area__cta-link",onClick:m,leadingIcon:e.createElement(g.a,{width:22,height:22})},Object(r.__)("Change groups","google-site-kit"))):null}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConnectAnalyticsCTAWidget}));var i=n(1),r=n.n(i),a=n(38),o=n(0),c=n(2),l=n(3),s=n(501),u=n(502),d=n(20),g=n(19),f=n(159),m=n(24),p=n(350);function ConnectAnalyticsCTAWidget(t){var n=t.Widget,i=Object(m.e)()===m.c,r=Object(f.a)("analytics-4"),h=Object(l.useSelect)((function(e){return e(g.a).getModuleIcon("analytics-4")})),b=i?e.createElement("p",null,Object(a.a)(Object(c.__)("Google Analytics is disconnected, your audience metrics can’t be displayed. <a>Connect Google Analytics</a>","google-site-kit"),{a:e.createElement(d.a,{secondary:!0,onClick:r})})):e.createElement(o.Fragment,null,e.createElement("p",null,Object(c.__)("Google Analytics is disconnected, your audience metrics can’t be displayed","google-site-kit")),e.createElement(d.a,{secondary:!0,onClick:r},Object(c.__)("Connect Google Analytics","google-site-kit")));return e.createElement(n,{noPadding:!0},e.createElement(p.a,{Icon:h,SVGGraphic:i?u.a:s.a},b))}ConnectAnalyticsCTAWidget.propTypes={Widget:r.a.elementType.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(0),o=n(2),c=n(3),l=n(7),s=n(9),u=n(50),d=n(248),g=n(165),f=n(8),m=n(23),p=n(113),h=n(18),b=Object(p.a)(d.a);function InfoNoticeWidget(t){var n=t.Widget,i=t.WidgetNull,r=Object(h.a)(),u=Object(c.useInViewSelect)((function(e){var t=e(f.r).getAvailableAudiences();return null==t?void 0:t.map((function(e){return e.name}))}),[]),d=Object(c.useInViewSelect)((function(e){return e(l.a).getConfiguredAudiences()}),[]),p=null==d?void 0:d.some((function(e){return null==u?void 0:u.includes(e)})),v=g.a.length,E=Object(c.useInViewSelect)((function(e){return e(l.a).isPromptDismissed(g.c)}),[]),_=Object(c.useSelect)((function(e){return e(m.b).getValue(g.b)})),O=Object(c.useInViewSelect)((function(e){return e(l.a).getPromptDismissCount(g.c)}),[]),k=Object(c.useDispatch)(l.a).dismissPrompt,y=Object(a.useCallback)((function(){void 0!==O&&Object(s.I)("".concat(r,"_audiences-info-notice"),"dismiss_notice",g.a[O].slug).finally((function(){var e=2*s.f,t=O+1<v?e:0;k(g.c,{expiresInSeconds:t})}))}),[O,k,v,r]);if(!0!==p||E||void 0===O||O>=v||!0===_)return e.createElement(i,null);var j=g.a[O],S=j.slug,w=j.content;return e.createElement(n,{noPadding:!0},e.createElement(b,{content:w,dismissLabel:Object(o.__)("Got it","google-site-kit"),onDismiss:y,onInView:function(){Object(s.I)("".concat(r,"_audiences-info-notice"),"view_notice",S)}}))}InfoNoticeWidget.propTypes={Widget:r.a.elementType.isRequired,WidgetNull:r.a.elementType.isRequired},t.a=Object(u.a)({moduleName:"analytics-4"})(InfoNoticeWidget)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SecondaryUserSetupWidget}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(1),u=n.n(s),d=n(81),g=n(0),f=n(3),m=n(177),p=n(8),h=n(203),b=n(35);function SecondaryUserSetupWidget(t){var n=t.Widget,i=Object(g.useState)(null),a=l()(i,2),c=a[0],s=a[1],u=Object(f.useSelect)((function(e){return e(p.r).isSettingUpAudiences()})),v=Object(f.useDispatch)(p.r).enableSecondaryUserAudienceGroup,E=function(){var e=o()(r.a.mark((function e(){var t,n;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return s(null),e.next=3,v();case 3:t=e.sent,(n=t.error)&&s(n);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return Object(d.a)((function(){u||o()(r.a.mark((function e(){var t,n;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v();case 2:t=e.sent,(n=t.error)&&s(n);case 5:case"end":return e.stop()}}),e)})))()})),c?e.createElement(h.a,{Widget:n,errors:c,onRetry:E,showRetryButton:!Object(b.e)(c)}):e.createElement(n,{className:"googlesitekit-widget-audience-tiles",noPadding:!0},e.createElement("div",{className:"googlesitekit-widget-audience-tiles__body"},e.createElement(n,{noPadding:!0},e.createElement(m.a,null)),e.createElement(n,{noPadding:!0},e.createElement(m.a,null))))}SecondaryUserSetupWidget.propTypes={Widget:u.a.elementType.isRequired}}).call(this,n(4))},,,,,,,,,,,function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(0),o=n(2),c=n(10),l=n(17),s=n(24),u=n(351),d=n(35),g=n(135),f=n(346),m=Object(a.forwardRef)((function(t,n){var i=t.Widget,r=t.errors,a=t.onRetry,m=t.onRequestAccess,p=t.showRetryButton,h=Object(s.e)(),b=h===s.b,v=h===s.c,E=r.some(d.e);return e.createElement(i,{ref:n,noPadding:!0,className:"googlesitekit-audience-segmentation-error-widget"},e.createElement(l.e,{collapsed:!0,className:"googlesitekit-audience-segmentation-error__widget-primary-cell"},e.createElement(l.k,null,e.createElement(l.a,{smSize:6,mdSize:8,lgSize:7},e.createElement("h3",{className:"googlesitekit-publisher-win__title"},E?Object(o.__)("Insufficient permissions","google-site-kit"):Object(o.__)("Your visitor groups data loading failed","google-site-kit")),e.createElement("div",{className:"googlesitekit-widget-audience-segmentation-error__actions"},p&&a?e.createElement(c.Button,{onClick:a,danger:!0},Object(o.__)("Retry","google-site-kit")):e.createElement(g.a,{moduleSlug:"analytics-4",error:r,GetHelpLink:E?f.a:void 0,hideGetHelpLink:!E,buttonVariant:"danger",getHelpClassName:"googlesitekit-error-retry-text",onRetry:a,onRequestAccess:m}))),!b&&!v&&e.createElement(l.a,{className:"googlesitekit-widget-audience-segmentation-error__svg-wrapper",smSize:6,mdSize:3,lgSize:5},e.createElement(u.a,{width:"233px"})),v&&e.createElement(l.a,{className:"googlesitekit-widget-audience-segmentation-error__svg-wrapper",mdSize:8},e.createElement(u.a,{width:"233px"})),b&&e.createElement(l.a,{className:"googlesitekit-widget-audience-segmentation-error__svg-wrapper",smSize:8},e.createElement(u.a,{width:"233px"})))))}));m.propTypes={Widget:r.a.elementType.isRequired,errors:r.a.arrayOf(r.a.object).isRequired,onRetry:r.a.func.isRequired,onRequestAccess:r.a.func.isRequired,showRetryButton:r.a.bool},t.a=m}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(0),l=n(2),s=n(157),u=n(88),d=n(32),g=n(13),f=n(7),m=n(9),p=n(18),h=n(3),b=n(10),v=n(137),E=Object(c.forwardRef)((function(t,n){var i=Object(p.a)(),a=Object(h.useSelect)((function(e){var t=e(g.c).getAdminURL("googlesitekit-dashboard");return Object(s.a)(t,{widgetArea:u.AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION})})),c=Object(h.useDispatch)(d.a).navigateTo,E=Object(h.useDispatch)(f.a).dismissItem;function _(){return E("settings_visitor_groups_setup_success_notification")}return e.createElement("div",{ref:n,className:"googlesitekit-settings-visitor-groups__setup-success googlesitekit-subtle-notification"},e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},e.createElement(v.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,Object(l.__)("We’ve added the audiences section to your dashboard!","google-site-kit"))),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},e.createElement(b.Button,{tertiary:!0,onClick:function(){Object(m.I)("".concat(i,"_audiences-setup-cta-settings-success"),"dismiss_notification").finally(_)}},Object(l.__)("Got it","google-site-kit")),e.createElement(b.Button,{onClick:function(){Object(m.I)("".concat(i,"_audiences-setup-cta-settings-success"),"confirm_notification").finally(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,_();case 2:c(a);case 3:case"end":return e.stop()}}),e)}))))}},Object(l.__)("Show me","google-site-kit"))))}));t.a=E}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{clipPath:"url(#audience-segmentation-setup-desktop_svg__clip0_1782_21770)"},i.createElement("path",{d:"M39.03 49.706a97.776 97.776 0 019.93-8.943c33.321-26.138 60.851-26.096 98.964-21.325C173.69 22.662 188.743 34.928 223.871 33 259 31.07 268 22.14 311 25c34.038 2.265 49.308 8.985 79.57 24.706 28.007 14.55 53.437 40.114 66.216 73.922 21.906 57.948-17.152 162.243-69.53 167.16-37.953 3.565-69.275-38.468-107.583-28.733-23.599 5.998-37.595 31.155-55.802 47.757-21.241 19.371-66.999 18.153-91.871 7.816-23.702-9.851-46.759-29.374-54.5-58.128-5.893-21.897-7.5-48-24-68-19.968-24.204-32.167-35-39.022-67.872-5.954-28.55 6.856-55.631 24.552-73.922z",fill:"#B8E6CA"}),i.createElement("rect",{x:85,y:41,width:153,height:208,rx:11,fill:"#fff"}),i.createElement("rect",{x:104.031,y:126.641,width:53.016,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:104.031,y:111.688,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:104.031,y:160.703,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:104.031,y:207.703,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("path",{d:"M189.672 124.602a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 110 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),i.createElement("rect",{x:104,y:62,width:36,height:9,rx:4.5,fill:"#EBEEF0"}),i.createElement("path",{d:"M189.672 173.617a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 110 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#FFDED3"}),i.createElement("path",{d:"M189.672 220.617a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 110 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),i.createElement("rect",{x:102.672,y:175.656,width:55.734,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("path",{d:"M238 89.5H86",stroke:"#EBEEF0",strokeWidth:2}),i.createElement("rect",{x:258,y:41,width:153,height:208,rx:11,fill:"#fff"}),i.createElement("rect",{x:276.955,y:126.641,width:52.805,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:276.955,y:111.688,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:276.955,y:160.703,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:276.955,y:207.703,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("path",{d:"M362.256 124.602a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),i.createElement("rect",{x:277,y:62,width:36,height:9,rx:4.5,fill:"#EBEEF0"}),i.createElement("path",{d:"M401 89.5H258",stroke:"#EBEEF0",strokeWidth:2}),i.createElement("path",{d:"M362.256 173.617a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836zM362.256 220.617a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836z",fill:"#FFDED3"}),i.createElement("rect",{x:275.602,y:175.656,width:55.513,height:9.516,rx:4.758,fill:"#EBEEF0"})),o=i.createElement("defs",null,i.createElement("clipPath",{id:"audience-segmentation-setup-desktop_svg__clip0_1782_21770"},i.createElement("path",{fill:"#fff",d:"M0 0h496v216H0z"})));t.a=function SvgAudienceSegmentationSetupDesktop(e){return i.createElement("svg",r({viewBox:"0 0 496 216",fill:"none"},e),a,o)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{clipPath:"url(#audience-segmentation-setup-tablet_svg__clip0_1925_26844)"},i.createElement("path",{d:"M67.728 34.59a71.982 71.982 0 017.31-6.584c24.527-19.24 44.792-19.21 72.848-15.698 18.966 2.374 30.047 11.404 55.905 9.984C229.65 20.872 234.5 10.117 271 14.308 307.5 18.5 322.5-1.5 360.5 1.5s52 32 72 42 37 8.5 50 35 4 83.5-36 106-99.355 25.386-122.439 27.553c-27.938 2.624-50.995-28.317-79.194-21.151-17.371 4.415-27.674 22.934-41.076 35.155-15.636 14.258-49.319 13.362-67.627 5.752-17.448-7.25-34.42-21.622-40.118-42.788-4.338-16.119-5.521-35.333-17.667-50.056-14.698-17.816-23.679-25.763-28.725-49.961-4.382-21.016 5.047-40.95 18.074-54.414z",fill:"#93C9A8"}),i.createElement("rect",{x:93,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:107.008,y:92.222,width:39.025,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:107.008,y:81.214,width:14.009,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:107.008,y:117.295,width:14.009,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M170.051 90.72a6.504 6.504 0 016.504-6.504h10.007a6.504 6.504 0 010 13.009h-10.007a6.504 6.504 0 01-6.504-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:106.984,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M170.051 126.802a6.504 6.504 0 016.504-6.505h10.007a6.504 6.504 0 010 13.009h-10.007a6.504 6.504 0 01-6.504-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:106.008,y:128.303,width:41.027,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M205.625 64.882H93.736",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("rect",{x:220.348,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:234.301,y:92.222,width:38.871,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:234.301,y:81.214,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:234.301,y:117.295,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M297.094 90.72a6.504 6.504 0 016.504-6.504h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.504-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:234.332,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M325.613 64.882H220.349",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("path",{d:"M297.094 126.802a6.504 6.504 0 016.504-6.505h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.504-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:233.305,y:128.303,width:40.864,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:347.695,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:361.648,y:92.222,width:38.871,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:361.648,y:81.214,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:361.648,y:117.295,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M424.441 90.72a6.504 6.504 0 016.505-6.504h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.505-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:361.68,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M452.961 64.882H347.697",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("path",{d:"M424.441 126.802a6.505 6.505 0 016.505-6.505h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.505-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:360.652,y:128.303,width:40.864,height:7.005,rx:3.502,fill:"#EBEEF0"})),o=i.createElement("defs",null,i.createElement("clipPath",{id:"audience-segmentation-setup-tablet_svg__clip0_1925_26844"},i.createElement("path",{fill:"#fff",d:"M0 0h553v158H0z"})));t.a=function SvgAudienceSegmentationSetupTablet(e){return i.createElement("svg",r({viewBox:"0 0 553 146",fill:"none"},e),a,o)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{clipPath:"url(#audience-segmentation-setup-mobile_svg__clip0_1657_37513)"},i.createElement("g",{filter:"url(#audience-segmentation-setup-mobile_svg__filter0_d_1657_37513)"},i.createElement("rect",{x:-16.047,width:111.697,height:151.85,rx:8.031,fill:"#fff"}),i.createElement("rect",{x:-15.682,y:.365,width:110.967,height:151.12,rx:7.665,stroke:"#EBEEF0",strokeWidth:.73})),i.createElement("rect",{x:-2.152,y:62.522,width:38.704,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("rect",{x:-2.152,y:51.605,width:13.894,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("rect",{x:-2.152,y:90.309,width:13.894,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("path",{d:"M60.367 61.033a6.45 6.45 0 016.45-6.45h9.925a6.45 6.45 0 010 12.9h-9.924a6.45 6.45 0 01-6.45-6.45z",fill:"#B8E6CA"}),i.createElement("rect",{x:-2.176,y:15.331,width:26.282,height:6.57,rx:3.285,fill:"#EBEEF0"}),i.createElement("path",{d:"M60.367 99.737a6.45 6.45 0 016.45-6.45h9.925a6.45 6.45 0 010 12.9h-9.924a6.45 6.45 0 01-6.45-6.45z",fill:"#FFDED3"}),i.createElement("rect",{x:-3.145,y:101.226,width:40.689,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("path",{d:"M95.648 35.407H-15.319",stroke:"#EBEEF0",strokeWidth:1.46}),i.createElement("g",{filter:"url(#audience-segmentation-setup-mobile_svg__filter1_d_1657_37513)"},i.createElement("rect",{x:115.648,width:111.697,height:151.85,rx:8.031,fill:"#fff"}),i.createElement("rect",{x:116.013,y:.365,width:110.967,height:151.12,rx:7.665,stroke:"#EBEEF0",strokeWidth:.73})),i.createElement("rect",{x:129.488,y:62.522,width:38.55,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("rect",{x:129.488,y:51.605,width:13.839,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("rect",{x:129.488,y:90.309,width:13.839,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("path",{d:"M191.762 61.033a6.45 6.45 0 016.45-6.45h9.834a6.451 6.451 0 010 12.9h-9.834a6.45 6.45 0 01-6.45-6.45z",fill:"#B8E6CA"}),i.createElement("rect",{x:129.52,y:15.331,width:26.282,height:6.57,rx:3.285,fill:"#EBEEF0"}),i.createElement("path",{d:"M220.047 35.407H115.65",stroke:"#EBEEF0",strokeWidth:1.46}),i.createElement("path",{d:"M191.762 99.737a6.45 6.45 0 016.45-6.45h9.834a6.451 6.451 0 010 12.9h-9.834a6.45 6.45 0 01-6.45-6.45z",fill:"#FFDED3"}),i.createElement("rect",{x:128.5,y:101.226,width:40.527,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("g",{filter:"url(#audience-segmentation-setup-mobile_svg__filter2_d_1657_37513)"},i.createElement("rect",{x:247.348,width:111.697,height:151.85,rx:8.031,fill:"#fff"}),i.createElement("rect",{x:247.713,y:.365,width:110.967,height:151.12,rx:7.665,stroke:"#EBEEF0",strokeWidth:.73})),i.createElement("rect",{x:261.188,y:62.522,width:38.55,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("rect",{x:261.188,y:51.605,width:13.839,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("rect",{x:261.188,y:90.309,width:13.839,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("path",{d:"M323.461 61.033a6.45 6.45 0 016.451-6.45h9.833a6.45 6.45 0 110 12.9h-9.833a6.45 6.45 0 01-6.451-6.45z",fill:"#B8E6CA"}),i.createElement("rect",{x:261.219,y:15.331,width:26.282,height:6.57,rx:3.285,fill:"#EBEEF0"}),i.createElement("path",{d:"M351.746 35.407H247.349",stroke:"#EBEEF0",strokeWidth:1.46}),i.createElement("path",{d:"M323.461 99.737a6.45 6.45 0 016.451-6.45h9.833a6.45 6.45 0 110 12.9h-9.833a6.45 6.45 0 01-6.451-6.45z",fill:"#FFDED3"}),i.createElement("rect",{x:260.199,y:101.226,width:40.527,height:6.947,rx:3.473,fill:"#EBEEF0"})),o=i.createElement("defs",null,i.createElement("filter",{id:"audience-segmentation-setup-mobile_svg__filter0_d_1657_37513",x:-16.047,y:0,width:114.616,height:155.5,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dx:2.92,dy:3.65}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0.921569 0 0 0 0 0.933333 0 0 0 0 0.941176 0 0 0 1 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1657_37513"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1657_37513",result:"shape"})),i.createElement("filter",{id:"audience-segmentation-setup-mobile_svg__filter1_d_1657_37513",x:115.648,y:0,width:114.616,height:155.5,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dx:2.92,dy:3.65}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0.921569 0 0 0 0 0.933333 0 0 0 0 0.941176 0 0 0 1 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1657_37513"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1657_37513",result:"shape"})),i.createElement("filter",{id:"audience-segmentation-setup-mobile_svg__filter2_d_1657_37513",x:247.348,y:0,width:114.616,height:155.5,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dx:2.92,dy:3.65}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0.921569 0 0 0 0 0.933333 0 0 0 0 0.941176 0 0 0 1 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1657_37513"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1657_37513",result:"shape"})),i.createElement("clipPath",{id:"audience-segmentation-setup-mobile_svg__clip0_1657_37513"},i.createElement("path",{fill:"#fff",d:"M0 0h343v157.69H0z"})));t.a=function SvgAudienceSegmentationSetupMobile(e){return i.createElement("svg",r({viewBox:"0 0 343 120",fill:"none"},e),a,o)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTiles}));var i=n(27),r=n.n(i),a=n(15),o=n.n(a),c=n(1),l=n.n(c),s=n(0),u=n(10),d=n(3),g=n(24),f=n(7),m=n(8),p=n(463),h=n(132),b=n(385),v=n(203),E=n(480),_=n(177),O=n(483),k=n(485),y=n(269),j=n(18),S=n(34),w=n(9),C=n(382),A=function(e,t){var n,i,r,a=null==e||null===(n=e.rows)||void 0===n?void 0:n.find((function(e){var n,i;return(null===(n=e.dimensionValues)||void 0===n||null===(i=n[0])||void 0===i?void 0:i.value)===t}));return 0===((null==a||null===(i=a.metricValues)||void 0===i||null===(r=i[0])||void 0===r?void 0:r.value)||0)};function AudienceTiles(t){var n=t.Widget,i=t.widgetLoading,a=Object(j.a)(),c=Object(S.a)(),l=Object(g.e)(),N=l===g.b||l===g.c,T=Object(d.useInViewSelect)((function(e){return e(f.a).getConfiguredAudiences()}),[]),x=Object(d.useInViewSelect)((function(e){return e(m.r).getAvailableAudiences()}),[]),D=Object(d.useSelect)((function(e){return e(m.r).getConfiguredSiteKitAndOtherAudiences()}))||[[],[]],R=o()(D,2),M=R[0],I=R[1],P=Object(d.useSelect)((function(e){return e(m.r).hasAudiencePartialData(M)})),L=Object(k.a)({isSiteKitAudiencePartialData:P,siteKitAudiences:M,otherAudiences:I}),B=L.report,z=L.reportLoaded,F=L.reportError,V=L.siteKitAudiencesReport,W=L.siteKitAudiencesReportLoaded,H=L.siteKitAudiencesReportError,U=L.totalPageviews,q=L.totalPageviewsReportLoaded,G=L.totalPageviewsReportError,K=L.topCitiesReport,Y=L.topCitiesReportLoaded,X=L.topCitiesReportErrors,$=L.topContentReport,Z=L.topContentReportLoaded,Q=L.topContentReportErrors,J=L.topContentPageTitlesReport,ee=L.topContentPageTitlesReportLoaded,te=L.topContentPageTitlesReportErrors,ne=function(e,t){var n,i,r,a,o,c,l=(null==x||null===(n=x.filter((function(t){return t.name===e})))||void 0===n||null===(i=n[0])||void 0===i?void 0:i.displayName)||"",s=(null==x||null===(r=x.filter((function(t){return t.name===e})))||void 0===r||null===(a=r[0])||void 0===a?void 0:a.audienceSlug)||"",u=function(e){var t,n=M.some((function(t){return t.name===e})),i=null===(t=M.find((function(t){return t.name===e})))||void 0===t?void 0:t.audienceSlug,r=function(t){var r,a,o,c,l,s,u,d,g,f,m,p,h;if(n&&P){var b,v="new-visitors"===i?"new":"returning";h=null==V||null===(b=V.rows)||void 0===b?void 0:b.find((function(e){var n,i,r=e.dimensionValues;return(null==r||null===(n=r[0])||void 0===n?void 0:n.value)===v&&(null==r||null===(i=r[1])||void 0===i?void 0:i.value)===t}))}else{var E;h=null==B||null===(E=B.rows)||void 0===E?void 0:E.find((function(n){var i,r,a=n.dimensionValues;return(null==a||null===(i=a[0])||void 0===i?void 0:i.value)===e&&(null==a||null===(r=a[1])||void 0===r?void 0:r.value)===t}))}return[Number((null===(r=h)||void 0===r||null===(a=r.metricValues)||void 0===a||null===(o=a[0])||void 0===o?void 0:o.value)||0),Number((null===(c=h)||void 0===c||null===(l=c.metricValues)||void 0===l||null===(s=l[1])||void 0===s?void 0:s.value)||0),Number((null===(u=h)||void 0===u||null===(d=u.metricValues)||void 0===d||null===(g=d[2])||void 0===g?void 0:g.value)||0),Number((null===(f=h)||void 0===f||null===(m=f.metricValues)||void 0===m||null===(p=m[3])||void 0===p?void 0:p.value)||0)]};return{current:r("date_range_0"),previous:r("date_range_1")}}(e),d=u.current,g=u.previous,f=d[0],m=g[0],p=d[1],h=g[1],b=d[2],v=g[2],E=d[3],_=g[3],O=null==K?void 0:K[t],k=null==$?void 0:$[t],y=(null==J||null===(o=J[t])||void 0===o||null===(c=o.rows)||void 0===c?void 0:c.reduce((function(e,t){return e[t.dimensionValues[0].value]=t.dimensionValues[1].value,e}),{}))||{},j=M.some((function(t){return t.name===e})),S=B,w=e;return j&&P&&(S=V,w="new-visitors"===s?"new":"returning"),{audienceName:l,audienceSlug:s,visitors:f,prevVisitors:m,visitsPerVisitors:p,prevVisitsPerVisitors:h,pagesPerVisit:b,prevPagesPerVisit:v,pageviews:E,prevPageviews:_,topCities:O,topContent:k,topContentTitles:y,isZeroData:A(S,w),isPartialData:!j&&le[e]}},ie=null==T?void 0:T.reduce((function(e,t){return e[t]=[],[X,Q,te].forEach((function(n){var i=n[t];i&&!Object(y.a)(i)&&e[t].push(i)})),e}),{}),re=Object(d.useSelect)((function(e){return e(f.a).getDismissedItems()})),ae=Object(d.useSelect)((function(e){return e(f.a)})).isDismissingItem,oe=Object(d.useDispatch)(f.a).dismissItem,ce=Object(s.useCallback)((function(e){oe("audience-tile-".concat(e))}),[oe]),le=Object(d.useInViewSelect)((function(e){return null==T?void 0:T.reduce((function(t,n){return t[n]=e(m.r).isAudiencePartialData(n),t}),{})}),[T]),se=Object(s.useRef)({}),ue=Object(s.useMemo)((function(){for(var e=[],t=[],n=null==T?void 0:T.slice().filter((function(e){return x.some((function(t){return t.name===e}))})),i=function(){var i,r=n.shift(),a=null==re?void 0:re.includes("audience-tile-".concat(r)),o=M.some((function(e){return e.name===r})),c=B,l=r;o&&P&&(c=V,l="new-visitors"===(null===(i=M.find((function(e){return e.name===r})))||void 0===i?void 0:i.audienceSlug)?"new":"returning");var s=A(c,l),u=n.length+t.length>0;if(a&&s&&u)return"continue";a&&!s&&e.push(r),t.push(r)};(null==n?void 0:n.length)>0;)i();return[e,t]}),[x,T,re,P,B,M,V]),de=o()(ue,2),ge=de[0],fe=de[1];var me,pe=(me=[],B&&me.push(F),V&&me.push(H),!(!me.every(Boolean)&&!G)||(null==T?void 0:T.every((function(e){return ie[e].length>0}))));Object(s.useEffect)((function(){ge.forEach((function(e){var t="audience-tile-".concat(e);se.current[t]||(oe(t,{expiresInSeconds:1}),se.current[t]=!0)}))}),[ge,oe,ae]);var he=Object(d.useSelect)((function(e){return e(m.r).isFetchingSyncAvailableCustomDimensions()})),be=Object(d.useDispatch)(m.r).fetchSyncAvailableCustomDimensions,ve=Object.values(Q).some(y.a)||Object.values(te).some(y.a);Object(s.useEffect)((function(){!c&&ve&&be()}),[be,ve,c]);var Ee=Object(s.useState)(fe[0]),_e=o()(Ee,2),Oe=_e[0],ke=_e[1],ye=Object(s.useCallback)((function(e){var t=fe.indexOf(e);return-1===t?0:t}),[fe]);Object(s.useEffect)((function(){fe.includes(Oe)||ke(fe[0])}),[Oe,fe]);var je=ye(Oe),Se=i||!z||!W||!q||!Y||!Z||!ee||he,we=0;return e.createElement(n,{className:"googlesitekit-widget-audience-tiles",noPadding:!0},!1===pe&&!Se&&N&&fe.length>0&&e.createElement(u.TabBar,{key:fe.length,className:"googlesitekit-widget-audience-tiles__tabs googlesitekit-tab-bar--start-aligned-high-contrast",activeIndex:je,handleActiveIndexUpdate:function(e){return ke(fe[e])}},fe.map((function(t,n){var i,r,o,c,l=(null==x||null===(i=x.filter((function(e){return e.name===t})))||void 0===i||null===(r=i[0])||void 0===r?void 0:r.displayName)||"",s=(null==x||null===(o=x.filter((function(e){return e.name===t})))||void 0===o||null===(c=o[0])||void 0===c?void 0:c.audienceSlug)||"",d=e.createElement(b.a,{audienceName:l,audienceSlug:s});return e.createElement(u.Tab,{key:n,"aria-label":l},l,e.createElement(h.a,{title:d,tooltipClassName:"googlesitekit-info-tooltip__content--audience",onOpen:function(){Object(w.I)("".concat(a,"_audiences-tile"),"view_tile_tooltip",s)}}))}))),e.createElement("div",{className:"googlesitekit-widget-audience-tiles__body"},pe&&!Se&&e.createElement(v.a,{Widget:n,errors:[].concat(r()(Object.values(ie).flat(2)),[F,G])}),(!1===pe||Se)&&fe.map((function(t,i){var r,a,o,c,l,s,u,d,g,f,m,h,v,O,k,y,j,S,w,A,T,x,D,R,M,I,P,L,B,z;if(N&&i!==je)return null;var F=ne(t,i),V=F.audienceName,W=F.audienceSlug,H=F.visitors,q=F.prevVisitors,G=F.visitsPerVisitors,K=F.prevVisitsPerVisitors,Y=F.pagesPerVisit,X=F.prevPagesPerVisit,$=F.pageviews,Z=F.prevPageviews,Q=F.topCities,J=F.topContent,ee=F.topContentTitles,te=F.isZeroData,re=F.isPartialData,ae=(null==Q?void 0:Q.rows)?Object(C.a)(Q.rows):[];return Se||void 0===te||void 0===re?e.createElement(n,{key:t,noPadding:!0},e.createElement(_.a,null)):ie[t].length>0?e.createElement(E.a,{key:t,audienceSlug:W,errors:ie[t]}):e.createElement(p.a,{key:t,audienceTileNumber:we++,audienceSlug:W,title:V,infoTooltip:e.createElement(b.a,{audienceName:V,audienceSlug:W}),visitors:{currentValue:H,previousValue:q},visitsPerVisitor:{currentValue:G,previousValue:K},pagesPerVisit:{currentValue:Y,previousValue:X},pageviews:{currentValue:$,previousValue:Z},percentageOfTotalPageViews:0!==U?$/U:0,topCities:{dimensionValues:[null==ae||null===(r=ae[0])||void 0===r||null===(a=r.dimensionValues)||void 0===a?void 0:a[0],null==ae||null===(o=ae[1])||void 0===o||null===(c=o.dimensionValues)||void 0===c?void 0:c[0],null==ae||null===(l=ae[2])||void 0===l||null===(s=l.dimensionValues)||void 0===s?void 0:s[0]],metricValues:[null==ae||null===(u=ae[0])||void 0===u||null===(d=u.metricValues)||void 0===d?void 0:d[0],null==ae||null===(g=ae[1])||void 0===g||null===(f=g.metricValues)||void 0===f?void 0:f[0],null==ae||null===(m=ae[2])||void 0===m||null===(h=m.metricValues)||void 0===h?void 0:h[0]],total:H},topContent:{dimensionValues:[null==J||null===(v=J.rows)||void 0===v||null===(O=v[0])||void 0===O||null===(k=O.dimensionValues)||void 0===k?void 0:k[0],null==J||null===(y=J.rows)||void 0===y||null===(j=y[1])||void 0===j||null===(S=j.dimensionValues)||void 0===S?void 0:S[0],null==J||null===(w=J.rows)||void 0===w||null===(A=w[2])||void 0===A||null===(T=A.dimensionValues)||void 0===T?void 0:T[0]],metricValues:[null==J||null===(x=J.rows)||void 0===x||null===(D=x[0])||void 0===D||null===(R=D.metricValues)||void 0===R?void 0:R[0],null==J||null===(M=J.rows)||void 0===M||null===(I=M[1])||void 0===I||null===(P=I.metricValues)||void 0===P?void 0:P[0],null==J||null===(L=J.rows)||void 0===L||null===(B=L[2])||void 0===B||null===(z=B.metricValues)||void 0===z?void 0:z[0]]},topContentTitles:ee,hasInvalidCustomDimensionError:ve,Widget:n,audienceResourceName:t,isZeroData:te,isPartialData:re,isTileHideable:fe.length>1,onHideTile:function(){return ce(t)}})})),!N&&e.createElement(O.a,{Widget:n,loading:Se,allTilesError:pe,visibleAudienceCount:fe.length})))}AudienceTiles.propTypes={Widget:l.a.elementType.isRequired,widgetLoading:l.a.bool.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTile}));var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(2),l=n(3),s=n(24),u=n(34),d=n(8),g=n(464),f=n(465),m=n(466),p=n(467),h=n(468),b=n(469),v=n(306),E=n(470),_=n(471),O=n(184),k=n(132),y=n(348),j=n(9),S=n(250),w=n(18),C=n(474);function AudienceTile(t){var n=t.audienceTileNumber,i=void 0===n?0:n,a=t.audienceSlug,o=t.title,A=t.infoTooltip,N=t.visitors,T=t.visitsPerVisitor,x=t.pagesPerVisit,D=t.pageviews,R=t.percentageOfTotalPageViews,M=t.topCities,I=t.topContent,P=t.topContentTitles,L=t.hasInvalidCustomDimensionError,B=t.Widget,z=t.audienceResourceName,F=t.isZeroData,V=t.isPartialData,W=t.isTileHideable,H=t.onHideTile,U=Object(s.e)(),q=Object(w.a)(),G=Object(u.a)(),K=Object(l.useInViewSelect)((function(e){var t=e(d.r).getPropertyID();return t&&e(d.r).isPropertyPartialData(t)})),Y=Object(l.useSelect)((function(e){return e(d.r).isSiteKitAudience(z)})),X=Object(l.useInViewSelect)((function(e){return!Y&&void 0!==K&&(!K&&z&&e(d.r).isAudiencePartialData(z))}),[K,Y,z]),$=Object(l.useInViewSelect)((function(e){return void 0!==K&&(!K&&!X&&e(d.r).isCustomDimensionPartialData("googlesitekit_post_type"))}),[X]),Z=Object(l.useInViewSelect)((function(e){return e(d.r).hasCustomDimensions("googlesitekit_post_type")}),[]),Q=[s.b,s.c].includes(U);return V&&F?e.createElement(C.a,{Widget:B,audienceSlug:a,title:o,infoTooltip:A,isMobileBreakpoint:Q,isTileHideable:W,onHideTile:H}):e.createElement(B,{noPadding:!0},e.createElement("div",{className:r()("googlesitekit-audience-segmentation-tile",{"googlesitekit-audience-segmentation-tile--partial-data":X})},!Q&&e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__header"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__header-title"},o,A&&e.createElement(k.a,{title:A,tooltipClassName:"googlesitekit-info-tooltip__content--audience",onOpen:function(){return Object(j.I)("".concat(q,"_audiences-tile"),"view_tile_tooltip",a)}})),X&&e.createElement(S.a,{className:"googlesitekit-audience-segmentation-partial-data-badge",label:Object(c.__)("Partial data","google-site-kit"),tooltipTitle:Object(c.__)("Still collecting full data for this timeframe, partial data is displayed for this group","google-site-kit"),onTooltipOpen:function(){Object(j.I)("".concat(q,"_audiences-tile"),"view_tile_partial_data_tooltip",a)}})),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__metrics"},Q&&X&&e.createElement(y.a,{content:Object(c.__)("Still collecting full data for this timeframe, partial data is displayed for this group","google-site-kit")}),e.createElement(v.a,{TileIcon:g.a,title:Object(c.__)("Visitors","google-site-kit"),metricValue:N.currentValue,Badge:function Badge(){return e.createElement(O.a,{previousValue:N.previousValue,currentValue:N.currentValue})}}),e.createElement(v.a,{TileIcon:f.a,title:Object(c.__)("Visits per visitor","google-site-kit"),metricValue:T.currentValue,Badge:function Badge(){return e.createElement(O.a,{previousValue:T.previousValue,currentValue:T.currentValue})}}),e.createElement(v.a,{TileIcon:m.a,title:Object(c.__)("Pages per visit","google-site-kit"),metricValue:x.currentValue,Badge:function Badge(){return e.createElement(O.a,{previousValue:x.previousValue,currentValue:x.currentValue})},metricValueFormat:{style:"decimal",maximumFractionDigits:2}}),e.createElement(v.a,{TileIcon:p.a,title:Object(c.sprintf)(
/* translators: %s: is a percentage value such as 33.3%. */
Object(c.__)("%s of total pageviews","google-site-kit"),Object(j.B)(R,{style:"percent",maximumFractionDigits:1})),metricValue:D.currentValue,Badge:function Badge(){return e.createElement(O.a,{previousValue:D.previousValue,currentValue:D.currentValue})}}),e.createElement(E.a,{TileIcon:h.a,title:Object(c.__)("Cities with the most visitors","google-site-kit"),topCities:M}),(!G||Z&&!L)&&e.createElement(_.a,{audienceTileNumber:i,audienceSlug:a,TileIcon:b.a,title:Object(c.__)("Top content by pageviews","google-site-kit"),topContentTitles:P,topContent:I,isTopContentPartialData:$}))))}AudienceTile.propTypes={audienceTileNumber:o.a.number,audienceSlug:o.a.string.isRequired,title:o.a.string.isRequired,infoTooltip:o.a.oneOfType([o.a.string,o.a.element]),visitors:o.a.object,visitsPerVisitor:o.a.object,pagesPerVisit:o.a.object,pageviews:o.a.object,percentageOfTotalPageViews:o.a.number,topCities:o.a.object,topContent:o.a.object,topContentTitles:o.a.object,hasInvalidCustomDimensionError:o.a.bool,Widget:o.a.elementType.isRequired,audienceResourceName:o.a.string.isRequired,isZeroData:o.a.bool,isPartialData:o.a.bool,isTileHideable:o.a.bool,onHideTile:o.a.func}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M.833 16.667v-2.334c0-.472.118-.902.354-1.291a2.48 2.48 0 01.98-.917c.86-.43 1.735-.75 2.624-.958a11.126 11.126 0 012.709-.333c.916 0 1.82.11 2.708.333.889.208 1.764.528 2.625.958.403.209.722.514.958.917.25.389.375.82.375 1.292v2.333H.833zm15 0v-2.5c0-.611-.174-1.195-.52-1.75-.334-.57-.813-1.056-1.438-1.458.708.083 1.375.229 2 .437a9.852 9.852 0 011.75.73c.5.277.882.59 1.145.937.264.333.396.701.396 1.104v2.5h-3.333zM7.5 10a3.21 3.21 0 01-2.354-.979 3.21 3.21 0 01-.98-2.354c0-.917.327-1.702.98-2.354a3.21 3.21 0 012.354-.98 3.21 3.21 0 012.354.98 3.21 3.21 0 01.979 2.354 3.21 3.21 0 01-.98 2.354 3.21 3.21 0 01-2.353.98zm8.333-3.333a3.21 3.21 0 01-.98 2.354 3.21 3.21 0 01-2.353.98c-.153 0-.348-.015-.584-.042a6.732 6.732 0 01-.583-.125c.375-.445.66-.938.854-1.48a4.662 4.662 0 00.313-1.687c0-.583-.104-1.146-.313-1.688a4.784 4.784 0 00-.854-1.479c.194-.07.389-.11.583-.125a4.12 4.12 0 01.584-.042 3.21 3.21 0 012.354.98 3.21 3.21 0 01.979 2.354zM2.5 15h10v-.666a.735.735 0 00-.125-.417.737.737 0 00-.292-.292 10.446 10.446 0 00-2.27-.833 9.342 9.342 0 00-4.626 0c-.764.18-1.52.458-2.27.833a.894.894 0 00-.313.292.843.843 0 00-.104.417V15zm5-6.666c.458 0 .847-.16 1.166-.48.334-.333.5-.729.5-1.187 0-.458-.166-.847-.5-1.167-.32-.333-.708-.5-1.166-.5-.459 0-.854.167-1.188.5-.32.32-.479.708-.479 1.167 0 .458.16.854.48 1.187.333.32.728.48 1.187.48z",fill:"currentColor"});t.a=function SvgAudienceMetricIconVisitors(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M10 17.5a7.443 7.443 0 01-2.938-.583 8.045 8.045 0 01-2.375-1.605 8.045 8.045 0 01-1.604-2.374A7.443 7.443 0 012.5 10c0-1.042.194-2.014.583-2.917a7.7 7.7 0 011.604-2.375 7.548 7.548 0 012.375-1.604A7.221 7.221 0 0110 2.5c1.139 0 2.215.243 3.23.73a7.252 7.252 0 012.603 2.062V3.333H17.5v5h-5V6.667h2.292a6.194 6.194 0 00-2.104-1.834A5.625 5.625 0 0010 4.167c-1.625 0-3.007.57-4.146 1.708C4.73 7 4.167 8.375 4.167 10s.562 3.007 1.687 4.146c1.14 1.125 2.521 1.687 4.146 1.687 1.458 0 2.73-.472 3.813-1.416 1.097-.945 1.743-2.14 1.937-3.584h1.708c-.208 1.903-1.027 3.493-2.458 4.771-1.417 1.264-3.083 1.896-5 1.896zm2.333-4l-3.166-3.167v-4.5h1.666v3.834l2.667 2.666-1.167 1.167z",fill:"currentColor"});t.a=function SvgAudienceMetricIconVisitsPerVisitor(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M13.125 16.667H3.334c-.459 0-.855-.16-1.188-.48-.32-.333-.479-.729-.479-1.187V5c0-.458.16-.847.48-1.167.332-.333.728-.5 1.187-.5h13.333c.458 0 .847.167 1.167.5.333.32.5.709.5 1.167v10c0 .459-.167.854-.5 1.188-.32.32-.709.479-1.167.479H15.5l-3.916-3.917c-.292.195-.611.34-.959.438-.333.097-.68.146-1.041.146-1.042 0-1.93-.362-2.667-1.084-.722-.736-1.083-1.625-1.083-2.666 0-1.042.36-1.924 1.083-2.646a3.633 3.633 0 012.667-1.104c1.041 0 1.923.368 2.646 1.104.736.722 1.104 1.604 1.104 2.646 0 .375-.049.729-.146 1.062a3.393 3.393 0 01-.438.938L16.167 15h.5V5H3.334v10h8.125l1.666 1.667zm-3.541-5c.583 0 1.076-.202 1.479-.604.403-.403.604-.896.604-1.48 0-.583-.201-1.076-.604-1.479a2.012 2.012 0 00-1.48-.604c-.583 0-1.076.202-1.479.604a2.012 2.012 0 00-.604 1.48c0 .583.202 1.076.604 1.479.403.402.896.604 1.48.604zM3.334 15V5v10z",fill:"currentColor"});t.a=function SvgAudienceMetricIconPagesPerVisit(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M4.167 17.5c-.459 0-.854-.16-1.188-.48-.32-.332-.479-.728-.479-1.187V4.167c0-.459.16-.848.48-1.167.333-.333.728-.5 1.187-.5h11.666c.459 0 .848.167 1.167.5.333.32.5.708.5 1.167v11.666c0 .459-.167.854-.5 1.188-.32.32-.708.479-1.167.479H4.167zm0-1.667h11.666v-10H4.167v10zM10 14.167c-1.139 0-2.16-.306-3.063-.917A5.398 5.398 0 015 10.833a5.271 5.271 0 011.938-2.396C7.84 7.814 8.86 7.5 10 7.5c1.139 0 2.153.313 3.042.938A5.229 5.229 0 0115 10.832a5.353 5.353 0 01-1.958 2.417c-.89.611-1.903.917-3.042.917zm0-1.25c.778 0 1.486-.18 2.125-.542a4 4 0 001.5-1.542 3.854 3.854 0 00-1.5-1.52A4.12 4.12 0 0010 8.75a4.12 4.12 0 00-2.125.563 3.854 3.854 0 00-1.5 1.52 4 4 0 001.5 1.542 4.243 4.243 0 002.125.542zm0-.834a1.26 1.26 0 01-.896-.354 1.26 1.26 0 01-.354-.896c0-.347.118-.639.354-.875.25-.25.549-.375.896-.375s.639.125.875.375c.25.236.375.528.375.875 0 .348-.125.646-.375.896a1.189 1.189 0 01-.875.354z",fill:"currentColor"});t.a=function SvgAudienceMetricIconPageviews(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M10 18.333a.776.776 0 01-.5-.166 1.012 1.012 0 01-.312-.438 11.597 11.597 0 00-1-2.188c-.39-.68-.938-1.479-1.646-2.395-.708-.917-1.285-1.792-1.73-2.625-.43-.834-.645-1.84-.645-3.021 0-1.625.562-3 1.687-4.125C6.994 2.235 8.375 1.667 10 1.667s3 .569 4.125 1.708c1.14 1.125 1.709 2.5 1.709 4.125 0 1.264-.243 2.32-.73 3.166-.472.834-1.02 1.66-1.645 2.48-.75 1-1.32 1.833-1.709 2.5a11.765 11.765 0 00-.937 2.083.94.94 0 01-.334.458.814.814 0 01-.479.146zm0-2.979c.236-.472.5-.938.792-1.396.306-.458.75-1.07 1.333-1.833a19.693 19.693 0 001.459-2.146c.389-.667.583-1.493.583-2.48 0-1.152-.41-2.131-1.23-2.937-.805-.82-1.784-1.229-2.937-1.229-1.152 0-2.139.41-2.958 1.23-.806.805-1.208 1.784-1.208 2.937 0 .986.187 1.812.562 2.479.39.653.882 1.368 1.48 2.146.583.764 1.02 1.375 1.312 1.833.305.458.576.924.812 1.396zm0-5.77c.584 0 1.077-.202 1.48-.605.402-.403.604-.896.604-1.48 0-.582-.202-1.076-.604-1.478A2.012 2.012 0 0010 5.417c-.583 0-1.076.2-1.479.604A2.012 2.012 0 007.917 7.5c0 .583.201 1.076.604 1.479.403.403.896.604 1.48.604z",fill:"currentColor"});t.a=function SvgAudienceMetricIconCities(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M3.334 16.667c-.459 0-.855-.16-1.188-.48-.32-.333-.479-.729-.479-1.187V5c0-.458.16-.847.48-1.167.332-.333.728-.5 1.187-.5h13.333c.458 0 .847.167 1.167.5.333.32.5.709.5 1.167v10c0 .459-.167.854-.5 1.188-.32.32-.709.479-1.167.479H3.334zm0-1.667h8.75v-2.916h-8.75V15zm10.416 0h2.917V7.5H13.75V15zM3.334 10.417h8.75V7.5h-8.75v2.917z",fill:"currentColor"});t.a=function SvgAudienceMetricIconTopContent(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileCitiesMetric}));var i=n(1),r=n.n(i),a=n(9),o=n(347);function AudienceTileCitiesMetric(t){var n,i=t.TileIcon,r=t.title,c=t.topCities,l=(null==c||null===(n=c.dimensionValues)||void 0===n?void 0:n.filter(Boolean))||[],s=!!l.length;return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric googlesitekit-audience-segmentation-tile-metric--cities"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__icon"},e.createElement(i,null)),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__container"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__title"},r),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__content"},!s&&e.createElement(o.a,null),s&&l.map((function(t,n){var i;return e.createElement("div",{key:null==t?void 0:t.value,className:"googlesitekit-audience-segmentation-tile-metric__cities-metric"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__cities-metric-name"},null==t?void 0:t.value),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__cities-metric-value"},Object(a.B)((null==c||null===(i=c.metricValues[n])||void 0===i?void 0:i.value)/(null==c?void 0:c.total),{style:"percent",maximumFractionDigits:1})))})))))}AudienceTileCitiesMetric.propTypes={TileIcon:r.a.elementType.isRequired,title:r.a.string.isRequired,topCities:r.a.object}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return AudienceTilePagesMetric}));var r=n(1),a=n.n(r),o=n(0),c=n(157),l=n(2),s=n(3),u=n(24),d=n(29),g=n(13),f=n(7),m=n(8),p=n(35),h=n(250),b=n(472),v=n(247),E=n(88),_=n(18),O=n(9);function AudienceTilePagesMetric(t){var n=t.audienceTileNumber,r=t.audienceSlug,a=t.TileIcon,k=t.title,y=t.topContent,j=t.topContentTitles,S=t.isTopContentPartialData,w=Object(u.e)(),C=Object(_.a)(),A=m.f.googlesitekit_post_type.parameterName,N=Object(s.useSelect)((function(e){return!e(m.r).hasCustomDimensions(A)})),T=Object(s.useSelect)((function(e){return e(f.a).hasScope(m.h)})),x=Object(c.a)(e.location.href,{notification:"audience_segmentation",widgetArea:E.AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION}),D=Object(c.a)(e.location.href,{widgetArea:E.AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION}),R=Object(s.useSelect)((function(e){return e(d.a).getValue(m.d,"isAutoCreatingCustomDimensionsForAudience")})),M=Object(s.useSelect)((function(e){return e(m.r).isCreatingCustomDimension(A)})),I=Object(s.useSelect)((function(e){return e(m.r).isFetchingSyncAvailableCustomDimensions()})),P=Object(s.useSelect)((function(e){return e(m.r).getCreateCustomDimensionError(A)})),L=Object(s.useSelect)((function(e){return e(m.r).getPropertyID()})),B=Object(s.useDispatch)(m.r).clearError,z=Object(s.useDispatch)(d.a).setValues,F=Object(s.useDispatch)(f.a),V=F.setPermissionScopeError,W=F.clearPermissionScopeError,H=Object(s.useSelect)((function(e){return e(d.a).getValue(m.d,"isRetrying")})),U=Object(s.useSelect)((function(e){return e(d.a).getValue(m.d,"autoSubmit")})),q=Object(s.useSelect)((function(e){return e(g.c).getSetupErrorCode()})),G=Object(s.useDispatch)(g.c).setSetupErrorCode,K=U&&"access_denied"===q,Y=Object(o.useCallback)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.isRetrying;z(m.d,{autoSubmit:!0,isRetrying:t}),T||V({code:p.a,message:Object(l.__)("Additional permissions are required to create new audiences in Analytics.","google-site-kit"),data:{status:403,scopes:[m.h],skipModal:!0,skipDefaultErrorNotifications:!0,redirectURL:x,errorRedirectURL:D}})}),[T,x,D,V,z]),X=Object(o.useCallback)((function(){z(m.d,{autoSubmit:!1,isRetrying:!1}),G(null),W(),B("createCustomDimension",[L,m.f.googlesitekit_post_type])}),[B,W,L,G,z]),$=[u.b,u.c].includes(w),Z=R||M||I;return i.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric googlesitekit-audience-segmentation-tile-metric--top-content"},i.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__icon"},i.createElement(a,null)),i.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__container"},i.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__title"},k,!$&&S&&i.createElement(h.a,{className:"googlesitekit-audience-segmentation-partial-data-badge",label:Object(l.__)("Partial data","google-site-kit"),onTooltipOpen:function(){Object(O.I)("".concat(C,"_audiences-tile"),"view_top_content_partial_data_tooltip",r)},tooltipTitle:Object(l.__)("Still collecting full data for this timeframe, partial data is displayed for this metric","google-site-kit")})),i.createElement(b.a,{topContentTitles:j,topContent:y,isTopContentPartialData:S,hasCustomDimension:!N,onCreateCustomDimension:Y,isSaving:Z}),0===n&&(P&&!Z||H&&!R||K)&&i.createElement(v.a,{apiErrors:[P],title:Object(l.__)("Failed to enable metric","google-site-kit"),description:Object(l.__)("Oops! Something went wrong. Retry enabling the metric.","google-site-kit"),onRetry:function(){return Y({isRetrying:!0})},onCancel:X,inProgress:Z,hasOAuthError:K,trackEventCategory:"".concat(C,"_audiences-top-content-cta")})))}AudienceTilePagesMetric.propTypes={audienceTileNumber:a.a.number,audienceSlug:a.a.string.isRequired,TileIcon:a.a.elementType.isRequired,title:a.a.string.isRequired,topContent:a.a.object,topContentTitles:a.a.object,isTopContentPartialData:a.a.bool}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTilePagesMetricContent}));var i=n(1),r=n.n(i),a=n(2),o=n(3),c=n(24),l=n(7),s=n(8),u=n(347),d=n(20),g=n(348),f=n(9),m=n(113),p=n(18),h=n(34),b=n(473),v=Object(m.a)(b.a);function AudienceTilePagesMetricContent(t){var n,i=t.topContentTitles,r=t.topContent,m=t.isTopContentPartialData,b=t.hasCustomDimension,E=t.onCreateCustomDimension,_=t.isSaving,O=Object(p.a)(),k=Object(h.a)(),y=Object(c.e)(),j=[c.b,c.c].includes(y),S=(null==r||null===(n=r.dimensionValues)||void 0===n?void 0:n.filter(Boolean))||[],w=!!S.length,C=Object(o.useSelect)((function(e){return e(l.a).getDateRangeDates({offsetDays:s.g})}));function ContentLinkComponent(t){var n=t.content,r=i[null==n?void 0:n.value],a=null==n?void 0:n.value,c=Object(o.useSelect)((function(e){return k?null:e(s.r).getServiceReportURL("all-pages-and-screens",{filters:{unifiedPagePathScreen:a},dates:C})}));return k?e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__top-content-metric-name"},r):e.createElement(d.a,{href:c,title:r,external:!0,hideExternalIndicator:!0},r)}return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__content"},!b&&e.createElement(v,{onClick:function(){Object(f.I)("".concat(O,"_audiences-top-content-cta"),"create_custom_dimension").finally(E)},isSaving:_,onInView:function(){Object(f.I)("".concat(O,"_audiences-top-content-cta"),"view_cta")}}),b&&!w&&e.createElement(u.a,null),b&&w&&S.map((function(t,n){var i;return e.createElement("div",{key:null==t?void 0:t.value,className:"googlesitekit-audience-segmentation-tile-metric__page-metric-container"},e.createElement(ContentLinkComponent,{content:t}),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__page-metric-value"},Object(f.B)(null==r||null===(i=r.metricValues[n])||void 0===i?void 0:i.value)))})),j&&m&&e.createElement(g.a,{content:Object(a.__)("Still collecting full data for this timeframe, partial data is displayed for this metric","google-site-kit")}))}AudienceTilePagesMetricContent.propTypes={topContentTitles:r.a.object,topContent:r.a.object,isTopContentPartialData:r.a.bool,hasCustomDimension:r.a.bool,onCreateCustomDimension:r.a.func,isSaving:r.a.bool}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(0),o=n(2),c=n(10),l=Object(a.forwardRef)((function(t,n){var i=t.onClick,r=t.isSaving;return e.createElement("div",{ref:n,className:"googlesitekit-audience-segmentation-tile-metric__no-data"},Object(o.__)("No data to show","google-site-kit"),e.createElement("p",null,Object(o.__)("Update Analytics to track metric","google-site-kit")),e.createElement(c.SpinnerButton,{danger:!0,onClick:i,isSaving:r,disabled:r},Object(o.__)("Update","google-site-kit")))}));l.propTypes={onClick:r.a.func.isRequired,isSaving:r.a.bool},t.a=l}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileZeroData}));var i=n(1),r=n.n(i),a=n(18),o=n(113),c=n(9),l=n(475),s=Object(o.a)(l.a);function AudienceTileZeroData(t){var n=t.Widget,i=t.audienceSlug,r=t.title,o=t.infoTooltip,l=t.isMobileBreakpoint,u=t.isTileHideable,d=t.onHideTile,g=Object(a.a)();return e.createElement(s,{Widget:n,audienceSlug:i,title:r,infoTooltip:o,isMobileBreakpoint:l,isTileHideable:u,onHideTile:function(){Object(c.I)("".concat(g,"_audiences-tile"),"temporarily_hide",i).finally(d)},onInView:function(){Object(c.I)("".concat(g,"_audiences-tile"),"view_tile_collecting_data",i)}})}AudienceTileZeroData.propTypes={Widget:r.a.elementType.isRequired,audienceSlug:r.a.string.isRequired,title:r.a.string.isRequired,infoTooltip:r.a.oneOfType([r.a.string,r.a.element]),isMobileBreakpoint:r.a.bool,isTileHideable:r.a.bool,onHideTile:r.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(0),o=n(18),c=n(9),l=n(132),s=n(476),u=n(478),d=Object(a.forwardRef)((function(t,n){var i=t.Widget,r=t.audienceSlug,a=t.title,d=t.infoTooltip,g=t.isMobileBreakpoint,f=t.isTileHideable,m=t.onHideTile,p=Object(o.a)();return e.createElement(i,{ref:n,noPadding:!0},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__zero-data-container"},!g&&e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__header"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__header-title"},a,d&&e.createElement(l.a,{title:d,tooltipClassName:"googlesitekit-info-tooltip__content--audience",onOpen:function(){return Object(c.I)("".concat(p,"_audiences-tile"),"view_tile_tooltip",r)}}))),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__zero-data-content"},e.createElement(s.a,null),f&&e.createElement(u.a,{onHideTile:m})))))}));d.propTypes={Widget:r.a.elementType.isRequired,audienceSlug:r.a.string.isRequired,title:r.a.string.isRequired,infoTooltip:r.a.oneOfType([r.a.string,r.a.element]),isMobileBreakpoint:r.a.bool,isTileHideable:r.a.bool,onHideTile:r.a.func},t.a=d}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileCollectingData}));var i=n(0),r=n(2),a=n(477);function AudienceTileCollectingData(){return e.createElement(i.Fragment,null,e.createElement(a.a,{className:"googlesitekit-audience-segmentation-tile__zero-data-image"}),e.createElement("p",{className:"googlesitekit-audience-segmentation-tile__zero-data-description"},Object(r.__)("Site Kit is collecting data for this group.","google-site-kit")))}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M54.323 93.529c21.908 0 39.667-17.76 39.667-39.667 0-21.908-17.76-39.667-39.667-39.667s-39.667 17.76-39.667 39.667 17.76 39.667 39.667 39.667z",fill:"#EBEEF0"}),o=i.createElement("path",{d:"M37.717 38.469l52.162 18.445a12.955 12.955 0 0016.533-7.896v0a12.948 12.948 0 00-3.553-13.955 12.952 12.952 0 00-4.349-2.582L87.023 28.42l-.014.042c.3-1.877.323-3.787.07-5.67",stroke:"#161B18",strokeWidth:2.578,strokeLinecap:"round",strokeLinejoin:"round"}),c=i.createElement("path",{d:"M70.947 38.469L18.785 56.914A12.955 12.955 0 012.25 49.018v0a12.955 12.955 0 017.902-16.537L21.64 28.42l.016.042a19.431 19.431 0 01-.07-5.668",stroke:"#161B18",strokeWidth:2.578,strokeLinecap:"round",strokeLinejoin:"round"}),l=i.createElement("path",{d:"M27.61.51l.497 83.126a39.625 39.625 0 0053.598-1.071l1.19-82.11L27.61.51z",fill:"#70B2F5"}),s=i.createElement("path",{d:"M27.61.51l.497 83.126a39.625 39.625 0 0053.598-1.071l1.19-82.11L27.61.51z",fill:"#77AD8C"}),u=i.createElement("path",{d:"M82.648 17.112l.24-16.66h-5.853l-1.033 86.633a39.782 39.782 0 005.702-4.526l.944-65.447z",fill:"#77AD8C",opacity:.2}),d=i.createElement("path",{d:"M44.723 46.377c4.916 3.946 11.868 4.892 19.218.273",stroke:"#161B18",strokeWidth:1.785,strokeLinecap:"round",strokeLinejoin:"round"}),g=i.createElement("path",{d:"M80.38 24.992c0-9.563-11.446-17.056-26.059-17.056-14.613 0-26.06 7.49-26.06 17.056h52.12z",fill:"#7B807D"}),f=i.createElement("path",{d:"M39.588 39.736c8.143 0 14.744-6.6 14.744-14.744 0-8.143-6.601-14.744-14.744-14.744s-14.744 6.601-14.744 14.744 6.601 14.744 14.744 14.744z",fill:"#fff",stroke:"#464B48",strokeWidth:4.363,strokeLinejoin:"round"}),m=i.createElement("path",{d:"M69.076 39.736c8.143 0 14.745-6.6 14.745-14.744 0-8.143-6.602-14.744-14.745-14.744-8.143 0-14.744 6.601-14.744 14.744s6.601 14.744 14.744 14.744z",fill:"#fff",stroke:"#464B48",strokeWidth:4.363,strokeLinejoin:"round"}),p=i.createElement("path",{d:"M30.86 24.992a8.739 8.739 0 018.726-8.726M60.348 24.992a8.738 8.738 0 018.726-8.726",stroke:"#B8BDB9",strokeWidth:3.173,strokeLinejoin:"round"});t.a=function SvgAudienceSegmentationCollectingData(e){return i.createElement("svg",r({viewBox:"0 0 109 94",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileCollectingDataHideable}));var i=n(1),r=n.n(i),a=n(0),o=n(2),c=n(20),l=n(479);function AudienceTileCollectingDataHideable(t){var n=t.onHideTile;return e.createElement(a.Fragment,null,e.createElement("p",{className:"googlesitekit-audience-segmentation-tile__zero-data-description"},Object(o.__)("You can hide this group until data is available.","google-site-kit")),e.createElement(c.a,{secondary:!0,linkButton:!0,className:"googlesitekit-audience-segmentation-tile-hide-cta",onClick:n,leadingIcon:e.createElement(l.a,{width:22,height:22})},Object(o.__)("Temporarily hide","google-site-kit")))}AudienceTileCollectingDataHideable.propTypes={onHideTile:r.a.func.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M12.907 10.523l-1.088-1.088c.113-.587-.056-1.137-.506-1.65-.45-.512-1.031-.712-1.743-.6L8.482 6.098c.213-.1.425-.175.638-.225.225-.05.462-.075.712-.075.938 0 1.731.331 2.381.994.663.65.994 1.443.994 2.381 0 .25-.025.487-.075.712-.05.213-.125.425-.225.638zm2.4 2.362l-1.088-1.05a8.11 8.11 0 001.257-1.18 6.69 6.69 0 00.956-1.482 7.287 7.287 0 00-2.7-3c-1.162-.75-2.462-1.125-3.9-1.125-.362 0-.719.025-1.069.075-.35.05-.693.125-1.031.225L6.57 4.185a7.851 7.851 0 011.575-.468 8.22 8.22 0 011.687-.169c1.887 0 3.569.525 5.044 1.575a8.87 8.87 0 013.206 4.05 8.766 8.766 0 01-1.144 2.063 8.16 8.16 0 01-1.631 1.65zm.375 4.613l-3.15-3.113a9.803 9.803 0 01-1.331.32 9.675 9.675 0 01-1.369.093c-1.887 0-3.569-.519-5.044-1.556a8.983 8.983 0 01-3.206-4.07 8.663 8.663 0 01.994-1.837c.4-.575.856-1.087 1.369-1.537l-2.063-2.1 1.05-1.05 13.8 13.8-1.05 1.05zM4.995 6.848A8.054 8.054 0 004 7.917c-.3.387-.556.806-.769 1.256a7.46 7.46 0 002.681 3.019c1.175.737 2.482 1.106 3.919 1.106.25 0 .494-.013.731-.038.238-.037.481-.075.731-.112l-.675-.713a5.889 5.889 0 01-.393.094 3.96 3.96 0 01-.394.019c-.937 0-1.737-.325-2.4-.975-.65-.662-.975-1.463-.975-2.4 0-.138.006-.269.019-.394.025-.125.056-.256.094-.394L4.995 6.848z",fill:"currentColor"});t.a=function SvgVisibility(e){return i.createElement("svg",r({viewBox:"0 0 19 19",fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileError}));var i=n(1),r=n.n(i),a=n(35),o=n(481),c=n(113),l=n(18),s=n(9),u=Object(c.a)(o.a);function AudienceTileError(t){var n=t.audienceSlug,i=t.errors,r=Object(l.a)(),o=i.some((function(e){return Object(a.e)(e)}));return e.createElement(u,{errors:i,onInView:function(){var e=o?"insufficient_permissions_error":"data_loading_error";Object(s.I)("".concat(r,"_audiences-tile"),e,n)},onRetry:function(){Object(s.I)("".concat(r,"_audiences-tile"),"data_loading_error_retry",n)},onRequestAccess:function(){Object(s.I)("".concat(r,"_audiences-tile"),"insufficient_permissions_error_request_access",n)}})}AudienceTileError.propTypes={audienceSlug:r.a.string.isRequired,errors:r.a.array.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(0),o=n(2),c=n(35),l=n(482),s=n(135),u=n(346),d=Object(a.forwardRef)((function(t,n){var i=t.errors,r=t.onRetry,a=t.onRequestAccess,d=i.some((function(e){return Object(c.e)(e)}));return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error",ref:n},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error__container"},e.createElement(l.a,{className:"googlesitekit-audience-segmentation-tile-error__image"}),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error__body"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error__message"},e.createElement("h3",{className:"googlesitekit-audience-segmentation-tile-error__title"},d?Object(o.__)("Insufficient permissions","google-site-kit"):Object(o.__)("Data loading failed","google-site-kit"))),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error__actions"},e.createElement(s.a,{moduleSlug:"analytics-4",error:i,GetHelpLink:d?u.a:void 0,hideGetHelpLink:!d,buttonVariant:"danger",onRetry:r,onRequestAccess:a})))))}));d.propTypes={errors:r.a.array.isRequired,onRetry:r.a.func.isRequired,onRequestAccess:r.a.func.isRequired},t.a=d}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M11.755 110.799a44.227 44.227 0 004.457 4.095c14.958 11.965 27.316 11.946 44.424 9.762 15.862-2.025 17.862-10.923 35.362-10.923 17.5 0 25.665 6.38 46 5s38.081-23.761 44.757-41.774c9.833-26.528-4.519-57.596-24.82-66.096-18.699-8.5-31.437.01-51.437-3.63C84.998 2.59 79.998-4.051 60.636 4c-20.53 8.701-20.455 23.533-32.699 38.667C18.974 53.747 4.956 56.312.734 76.959c-2.673 13.07 3.077 25.467 11.021 33.84z",fill:"#F3F5F7"}),o=i.createElement("path",{d:"M96.07 141.772c39.765 0 72-2.014 72-4.5 0-2.485-32.235-4.5-72-4.5-39.764 0-72 2.015-72 4.5 0 2.486 32.236 4.5 72 4.5z",fill:"#161B18",opacity:.1}),c=i.createElement("path",{d:"M96.07 141.772c39.765 0 72-2.014 72-4.5 0-2.485-32.235-4.5-72-4.5-39.764 0-72 2.015-72 4.5 0 2.486 32.236 4.5 72 4.5z",fill:"#CBD0D3"}),l=i.createElement("path",{d:"M72.657 62.826c2.14 7.94 9.59 13.553 17.488 15.847 7.898 2.295 16.295 1.846 24.51 1.435 3.883-.192 7.855-.363 11.576.764 3.72 1.127 7.202 3.821 8.191 7.58.568 2.16.147 4.49-.959 6.424",stroke:"#161B18",strokeWidth:1.7,strokeLinecap:"round",strokeLinejoin:"round"}),s=i.createElement("path",{d:"M57.306 76.348c-1.237 19.582-8.053 37.15-3.497 59.224h-5.188M63.171 135.572h-5.19l9.173-59.224",stroke:"#161B18",strokeWidth:1.705,strokeLinecap:"round",strokeLinejoin:"round"}),u=i.createElement("path",{d:"M40.754 70.174c-4.429 2-12.93 6.455-12.929 15.5",stroke:"#000",strokeWidth:1.7,strokeLinecap:"round"}),d=i.createElement("path",{d:"M76.804 102.64l-3.602.327-21.976 1.988-12.845 1.16-1.22-28.616-2.118-49.8 40.092-1.46.81 37.088.109 4.993.745 34.204.005.116z",fill:"#CBD0D3"}),g=i.createElement("path",{d:"M73.203 102.967l2.067-.188 1.537-.139-1.671-76.403-2.303.105 1.24 72.43-35.862 3.393.125 2.852.048 1.095 12.845-1.159 21.977-1.989-.003.003z",fill:"#999F9B"}),f=i.createElement("path",{d:"M27.827 85.674c0 5.264 5.99 9.06 10.262 7.203",stroke:"#000",strokeWidth:1.7,strokeLinecap:"round"}),m=i.createElement("path",{d:"M145.07 26.773l-28 108.499",stroke:"#7B807D",strokeWidth:4.393,strokeMiterlimit:10,strokeLinecap:"round"}),p=i.createElement("path",{d:"M143.121 18.374L101.519 60.22a4.387 4.387 0 00-1.124 4.247 4.395 4.395 0 003.116 3.096l57.038 15.105a4.394 4.394 0 005.365-5.402l-15.439-56.943a4.393 4.393 0 00-7.354-1.949z",fill:"#E77D5B"}),h=i.createElement("path",{d:"M138.137 42.556l2.44-9.216 4.756 1.26-2.44 9.215-3.501 11.163-3.78-1.001 2.525-11.421zm-3.301 22.078a3.372 3.372 0 01-2.088-1.553 3.37 3.37 0 01-.316-2.584c.24-.906.753-1.588 1.541-2.046a3.375 3.375 0 012.584-.316c.906.24 1.588.754 2.046 1.542.458.788.567 1.635.327 2.54a3.375 3.375 0 01-1.553 2.09c-.788.457-1.635.567-2.541.327z",fill:"#962C0A"}),b=i.createElement("path",{d:"M133.461 94.876a8.345 8.345 0 01-4.565 3.774c-4.063 1.39-9.013-.82-10.694-4.77",stroke:"#161B18",strokeWidth:1.7,strokeLinecap:"round",strokeLinejoin:"round"});t.a=function SvgAnalyticsAudienceSegmentationTileError(e){return i.createElement("svg",r({viewBox:"0 0 190 142",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p,h,b)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MaybePlaceholderTile}));var i=n(1),r=n.n(i),a=n(177),o=n(484);function MaybePlaceholderTile(t){var n=t.Widget,i=t.loading,r=t.allTilesError,c=t.visibleAudienceCount;return!1!==r&&!i||1!==c?null:i?e.createElement(n,{noPadding:!0},e.createElement(a.a,null)):e.createElement(o.a,{Widget:n})}MaybePlaceholderTile.propTypes={Widget:r.a.elementType.isRequired,loading:r.a.bool.isRequired,allTilesError:r.a.bool,visibleAudienceCount:r.a.number.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PlaceholderTile}));var i=n(11),r=n.n(i),a=n(38),o=n(2),c=n(3),l=n(13),s=n(23),u=n(7),d=n(8),g=n(20),f=n(349),m=n(40);function PlaceholderTile(t){var n=t.Widget,i=Object(c.useSelect)((function(e){var t=e(u.a).getConfiguredAudiences();return e(d.r).getConfigurableAudiences().some((function(e){return"DEFAULT_AUDIENCE"!==e.audienceType&&!t.includes(e.name)}))})),p=Object(c.useSelect)((function(e){return e(l.c).getGoogleSupportURL({path:"/analytics/answer/12799087"})})),h=Object(c.useDispatch)(s.b).setValue,b=e.createElement(g.a,{secondary:!0,href:p,external:!0});return e.createElement(n,{className:"googlesitekit-audience-segmentation-tile-placeholder"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-placeholder__container"},e.createElement(f.a,{className:"googlesitekit-audience-segmentation-tile-placeholder__image"}),e.createElement("div",{className:r()("googlesitekit-audience-segmentation-tile-placeholder__body",{"googlesitekit-audience-segmentation-tile-placeholder__body--without-selectable-audiences":!i})},e.createElement("h3",{className:"googlesitekit-audience-segmentation-tile-placeholder__title"},i?Object(o.__)("Compare your group to other groups","google-site-kit"):Object(o.__)("Create more visitor groups","google-site-kit")),e.createElement("p",{className:"googlesitekit-audience-segmentation-tile-placeholder__description"},i?Object(a.a)(Object(o.__)("<SelectGroupLink>Select</SelectGroupLink> another group to compare with your current group or learn more about how to group site visitors in <AnalyticsLink>Analytics</AnalyticsLink>","google-site-kit"),{AnalyticsLink:b,SelectGroupLink:e.createElement(g.a,{secondary:!0,onClick:function(){return h(m.i,!0)}})}):Object(a.a)(Object(o.__)("Learn more about how to group site visitors in <AnalyticsLink>Analytics</AnalyticsLink>","google-site-kit"),{AnalyticsLink:b})))))}}).call(this,n(4))},function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var i=n(6),r=n.n(i),a=n(3),o=n(7),c=n(8);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t){return Object(a.useSelect)((function(n){return t.every((function(t){var i=n(c.r).getPartialDataSiteKitAudience(t);if(void 0===i)return!1;var r={};return i?r.newVsReturning="new-visitors"===i.audienceSlug?"new":"returning":r.audienceResourceName=t,n(c.r).hasFinishedResolution("getReport",[s(s({},e),{},{dimensionFilters:s(s({},e.dimensionFilters),r)})])}))}))}function d(e,t){return Object(a.useSelect)((function(n){return t.reduce((function(t,i){var r=n(c.r).getPartialDataSiteKitAudience(i);if(void 0===r)return t;var a={};r?a.newVsReturning="new-visitors"===r.audienceSlug?"new":"returning":a.audienceResourceName=i;var o=n(c.r).getErrorForSelector("getReport",[s(s({},e),{},{dimensionFilters:s(s({},e.dimensionFilters),a)})]);return o&&(t[i]=o),t}),{})}))}function g(e){var t,n,i,r,l=e.isSiteKitAudiencePartialData,g=e.siteKitAudiences,f=e.otherAudiences,m=Object(a.useSelect)((function(e){return e(o.a).getConfiguredAudiences()})),p={audienceResourceName:m},h=Object(a.useSelect)((function(e){return e(o.a).getDateRangeDates({offsetDays:c.g,compare:!0})})),b=h.startDate,v=h.endDate,E=void 0===l?void 0:f.length>0||!1===l,_=g.length>0&&l,O=s(s({},h),{},{dimensions:[{name:"audienceResourceName"}],dimensionFilters:p,metrics:[{name:"totalUsers"},{name:"sessionsPerUser"},{name:"screenPageViewsPerSession"},{name:"screenPageViews"}]}),k=Object(a.useInViewSelect)((function(e){if(void 0!==E)return E?e(c.r).getReport(O):null}),[E,O]),y=Object(a.useSelect)((function(e){if(void 0!==E)return!E||e(c.r).hasFinishedResolution("getReport",[O])})),j=Object(a.useSelect)((function(e){if(void 0!==E)return E?e(c.r).getErrorForSelector("getReport",[O]):null})),S=s(s({},h),{},{dimensions:[{name:"newVsReturning"}],dimensionFilters:{newVsReturning:["new","returning"]},metrics:[{name:"totalUsers"},{name:"sessionsPerUser"},{name:"screenPageViewsPerSession"},{name:"screenPageViews"}]}),w=Object(a.useInViewSelect)((function(e){if(void 0!==_)return _?e(c.r).getReport(S):null}),[_,S]),C=Object(a.useSelect)((function(e){if(void 0!==_)return!_||e(c.r).hasFinishedResolution("getReport",[S])})),A=Object(a.useSelect)((function(e){if(void 0!==_)return _?e(c.r).getErrorForSelector("getReport",[S]):null})),N={startDate:b,endDate:v,metrics:[{name:"screenPageViews"}]},T=Object(a.useInViewSelect)((function(e){return e(c.r).getReport(N)})),x=Object(a.useSelect)((function(e){return e(c.r).hasFinishedResolution("getReport",[N])})),D=Object(a.useSelect)((function(e){return e(c.r).getErrorForSelector("getReport",[N])})),R=Number(null==T||null===(t=T.totals)||void 0===t||null===(n=t[0])||void 0===n||null===(i=n.metricValues)||void 0===i||null===(r=i[0])||void 0===r?void 0:r.value)||0,M={startDate:b,endDate:v,dimensions:["city"],metrics:[{name:"totalUsers"}],orderby:[{metric:{metricName:"totalUsers"},desc:!0}],limit:4},I=Object(a.useInViewSelect)((function(e){return e(c.r).getReportForAllAudiences(M,m)})),P=u(M,m),L=d(M,m),B={startDate:b,endDate:v,dimensions:["pagePath"],metrics:[{name:"screenPageViews"}],dimensionFilters:{"customEvent:googlesitekit_post_type":{filterType:"stringFilter",matchType:"EXACT",value:"post"}},orderby:[{metric:{metricName:"screenPageViews"},desc:!0}],limit:3},z=Object(a.useInViewSelect)((function(e){return e(c.r).getReportForAllAudiences(B,m)})),F=u(B,m),V=d(B,m),W={startDate:b,endDate:v,dimensions:["pagePath","pageTitle"],metrics:[{name:"screenPageViews"}],dimensionFilters:{"customEvent:googlesitekit_post_type":{filterType:"stringFilter",matchType:"EXACT",value:"post"}},orderby:[{metric:{metricName:"screenPageViews"},desc:!0}],limit:15};return{report:k,reportLoaded:y,reportError:j,siteKitAudiencesReport:w,siteKitAudiencesReportLoaded:C,siteKitAudiencesReportError:A,totalPageviews:R,totalPageviewsReportLoaded:x,totalPageviewsReportError:D,topCitiesReport:I,topCitiesReportLoaded:P,topCitiesReportErrors:L,topContentReport:z,topContentReportLoaded:F,topContentReportErrors:V,topContentPageTitlesReport:Object(a.useInViewSelect)((function(e){return e(c.r).getReportForAllAudiences(W,m)})),topContentPageTitlesReportLoaded:u(W,m),topContentPageTitlesReportErrors:d(W,m)}}},function(e,t,n){"use strict";(function(e){var i=n(0),r=n(38),a=n(2),o=n(3),c=n(349),l=n(20),s=n(19),u=n(350),d=n(32),g=n(13),f=n(23),m=n(7),p=n(40),h=n(18),b=n(34),v=n(9),E=Object(i.forwardRef)((function(t,n){var i=Object(h.a)(),E=Object(b.a)(),_=Object(o.useSelect)((function(e){return e(m.a).didSetAudiences()})),O=Object(o.useSelect)((function(e){return e(s.a).getModuleIcon("analytics-4")})),k=Object(o.useSelect)((function(e){return e(g.c).getAdminURL("googlesitekit-settings")})),y=Object(o.useDispatch)(f.b).setValue,j=Object(o.useDispatch)(d.a).navigateTo,S=_?"no-longer-available":"none-selected";function w(){Object(v.I)("".concat(i,"_audiences-no-audiences"),"select_groups",S).finally((function(){y(p.i,!0)}))}return e.createElement(u.a,{ref:n,className:"googlesitekit-no-audience-banner",Icon:O,SVGGraphic:c.a},e.createElement("p",null,_&&Object(r.a)(Object(a.__)("It looks like your visitor groups aren’t available anymore. <a>Select other groups</a>.","google-site-kit"),{a:e.createElement(l.a,{secondary:!0,onClick:w})}),!_&&Object(r.a)(Object(a.__)("You don’t have any visitor groups selected. <a>Select groups</a>.","google-site-kit"),{a:e.createElement(l.a,{secondary:!0,onClick:w})})),!E&&e.createElement("p",null,Object(r.a)(Object(a.__)("You can deactivate this widget in <a>Settings</a>.","google-site-kit"),{a:e.createElement(l.a,{secondary:!0,onClick:function(){Object(v.I)("".concat(i,"_audiences-no-audiences"),"change_settings",S).finally((function(){j("".concat(k,"#/admin-settings"))}))}})})))}));t.a=E}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Panel}));var i=n(6),r=n.n(i),a=n(0),o=n(3),c=n(18),l=n(9),s=n(40),u=n(29),d=n(23),g=n(7),f=n(8),m=n(488),p=n(495),h=n(497),b=n(498),v=n(499),E=n(117),_=n(500);function Panel(){var t=Object(c.a)(),n=Object(o.useSelect)((function(e){return e(d.b).getValue(s.i)})),i=Object(o.useSelect)((function(e){return e(f.r).isFetchingSyncAvailableAudiences()})),O=Object(o.useInViewSelect)((function(e){var t=e(f.r).getConfigurableAudiences,n=(0,e(g.a).getConfiguredAudiences)()||[],i=t()||[];return i.length&&n.length?i.filter((function(e){var t=e.name;return n.includes(t)})).map((function(e){return e.name})):[]})),k=Object(o.useSelect)((function(e){return e(u.a).getValue(s.c,"autoSubmit")})),y=Object(o.useDispatch)(u.a).setValues,j=Object(o.useDispatch)(d.b).setValue,S=Object(a.useCallback)((function(){var e;y(s.h,(e={},r()(e,s.f,O),r()(e,s.g,!1),e)),Object(l.I)("".concat(t,"_audiences-sidebar"),"audiences_sidebar_view")}),[O,y,t]),w=Object(a.useCallback)((function(){n&&(j(s.i,!1),j(s.e,!1))}),[j,n]);return e.createElement(E.e,{className:"googlesitekit-audience-selection-panel",closePanel:w,isOpen:n||k,isLoading:i,onOpen:S},e.createElement(b.a,{closePanel:w}),e.createElement(m.a,{savedItemSlugs:O}),e.createElement(v.a,null),e.createElement(p.a,null),e.createElement(_.a,null),e.createElement(h.a,{closePanel:w,isOpen:n,savedItemSlugs:O}))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceItems}));var i=n(6),r=n.n(i),a=n(5),o=n.n(a),c=n(16),l=n.n(c),s=n(15),u=n.n(s),d=n(1),g=n.n(d),f=n(812),m=n(2),p=n(0),h=n(3),b=n(40),v=n(23),E=n(7),_=n(8),O=n(9),k=n(489),y=n(117),j=n(490),S=n(491),w=n(34),C=n(493);function A(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function N(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?A(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):A(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function AudienceItems(t){var n=t.savedItemSlugs,i=void 0===n?[]:n,a=Object(p.useState)(!0),c=u()(a,2),s=c[0],d=c[1],g=Object(h.useDispatch)(E.a).setExpirableItemTimers,A=Object(h.useDispatch)(_.r).syncAvailableAudiences,T=Object(w.a)(),x=Object(h.useSelect)((function(e){return e(v.b).getValue(b.i)})),D=Object(h.useSelect)((function(e){return e(_.r).isFetchingSyncAvailableAudiences()}));Object(p.useEffect)((function(){if(s&&x){var e=function(){var e=l()(o.a.mark((function e(){return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,A();case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();d(!1),e()}}),[s,x,A]);var R=Object(h.useInViewSelect)((function(e){var t=e(_.r),n=t.getConfigurableAudiences,i=t.getReport,r=t.getAudiencesUserCountReportOptions,a=t.getConfigurableSiteKitAndOtherAudiences,o=t.hasAudiencePartialData,c=n();if(void 0!==c){if(!c.length)return[];var l=a(),s=u()(l,2),d=s[0],g=s[1],f=o(d),m=e(E.a).getDateRangeDates({offsetDays:_.g}),p=f&&i(N(N({},m),{},{metrics:[{name:"totalUsers"}],dimensions:[{name:"newVsReturning"}]})),h=!1===f||!0===f&&(null==g?void 0:g.length)>0?i(r(f?g:c)):{},b=(p||{}).rows,v=void 0===b?[]:b,O=(h||{}).rows,k=void 0===O?[]:O;return c.map((function(e){var t,n,i,r;return r="SITE_KIT_AUDIENCE"===e.audienceType&&f?y(v,"new-visitors"===e.audienceSlug?"new":"returning"):y(k,e.name),N(N({},e),{},{userCount:Number(null===(t=r)||void 0===t||null===(n=t.metricValues)||void 0===n||null===(i=n[0])||void 0===i?void 0:i.value)||0})}))}function y(e,t){return e.find((function(e){var n,i;return(null==e||null===(n=e.dimensionValues)||void 0===n||null===(i=n[0])||void 0===i?void 0:i.value)===t}))}})),M=function(e,t){var n=t.audienceType,i=t.description,a=t.displayName,o=t.name,c=t.userCount,l="";switch(n){case"DEFAULT_AUDIENCE":l=Object(m.__)("Created by default by Google Analytics","google-site-kit"),i="";break;case"SITE_KIT_AUDIENCE":l=Object(m.__)("Created by Site Kit","google-site-kit");break;case"USER_AUDIENCE":l=Object(m.__)("Already exists in your Analytics property","google-site-kit")}return N(N({},e),{},r()({},o,{title:a,subtitle:i,description:l,userCount:c,audienceType:n}))},I=null==R?void 0:R.filter((function(e){var t=e.name;return i.includes(t)})).reduce(M,{}),P=null==R?void 0:R.filter((function(e){var t=e.name;return!i.includes(t)})).reduce(M,{}),L=Object(h.useSelect)((function(e){if(void 0!==R){var t=e(E.a),n=t.hasFinishedResolution,i=t.hasExpirableItem;if(n("getExpirableItems"))return R.filter((function(e){var t=e.audienceType,n=e.name;return"DEFAULT_AUDIENCE"!==t&&!i("".concat(_.b).concat(n))})).map((function(e){var t=e.name;return"".concat(_.b).concat(t)}))}}));return Object(f.a)((function(){x&&void 0!==L&&L.length&&g(L.map((function(e){return{slug:e,expiresInSeconds:4*O.f}})))}),[x,g,L]),e.createElement(y.d,{availableItemsTitle:Object(m.__)("Additional groups","google-site-kit"),availableSavedItems:I,availableUnsavedItems:P,ItemComponent:D?j.a:k.a,savedItemSlugs:i,notice:e.createElement(p.Fragment,null,e.createElement(S.a,null),!T&&e.createElement(C.a,null))})}AudienceItems.propTypes={savedItemSlugs:g.a.array}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceItem}));var i=n(6),r=n.n(i),a=n(15),o=n.n(a),c=n(1),l=n.n(c),s=n(0),u=n(2),d=n(3),g=n(40),f=n(29),m=n(7),p=n(8),h=n(9),b=n(302),v=n(117),E=n(250);function AudienceItem(t){var n=t.slug,i=t.title,a=t.description,c=t.subtitle,l=t.userCount,_=t.audienceType,O="".concat(p.b).concat(n),k=Object(d.useSelect)((function(e){return e(f.a).getValue(g.h,g.f)})),y=Object(d.useSelect)((function(e){return e(m.a).hasExpirableItem(O)})),j=Object(d.useSelect)((function(e){return e(m.a).isExpirableItemActive(O)})),S=Object(d.useSelect)((function(e){return e(p.r).getAudienceUserCountReportErrors()}))||[],w=o()(S,2),C=w[0],A=w[1],N=[];A&&N.push(A),C&&N.push(C);var T=Object(d.useDispatch)(f.a).setValues,x=Object(d.useSelect)((function(e){return e(m.a).isItemDismissed("audience-tile-".concat(n))})),D=Object(s.useCallback)((function(e){var t;T(g.h,(t={},r()(t,g.f,e.target.checked?k.concat([n]):k.filter((function(e){return e!==n}))),r()(t,g.g,!0),t))}),[k,T,n]),R="DEFAULT_AUDIENCE"!==_&&(!1===y||j),M=null==k?void 0:k.includes(n),I="audience-selection-checkbox-".concat(n);function ItemBadge(){return x?e.createElement(E.a,{label:Object(u.__)("Temporarily hidden","google-site-kit"),tooltipTitle:Object(u.__)("Site Kit is collecting data for this group. Once data is available the group will be added to your dashboard.","google-site-kit")}):R?e.createElement(b.a,null):null}return e.createElement(v.c,{id:I,slug:n,title:i,subtitle:c,description:a,isItemSelected:M,onCheckboxChange:D,suffix:N.length?"-":Object(h.B)(l),badge:(x||R)&&e.createElement(ItemBadge,null)})}AudienceItem.propTypes={slug:l.a.string.isRequired,title:l.a.string.isRequired,description:l.a.string.isRequired,subtitle:l.a.string,userCount:l.a.number.isRequired,audienceType:l.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceItemPreviewBlock}));var i=n(44);function AudienceItemPreviewBlock(){return e.createElement("div",{className:"googlesitekit-selection-panel__loading"},e.createElement("div",{className:"googlesitekit-selection-panel__loading-left"},e.createElement(i.a,{width:"90px",height:"20px",className:"googlesitekit-selection-panel__loading-item"}),e.createElement(i.a,{width:"293px",height:"15px",className:"googlesitekit-selection-panel__loading-item"})),e.createElement("div",{className:"googlesitekit-selection-panel__loading-right"},e.createElement(i.a,{width:"43px",height:"20px",className:"googlesitekit-selection-panel__loading-item"})))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AddGroupNotice}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(1),u=n.n(s),d=n(0),g=n(2),f=n(3),m=n(40),p=n(29),h=n(7),b=n(207),v=n(248),E=n(23),_=n(8);function AddGroupNotice(){var t=Object(d.useState)(!1),n=l()(t,2),i=n[0],a=n[1],c=Object(f.useInViewSelect)((function(e){return e(h.a).isItemDismissed(m.a)})),s=Object(f.useSelect)((function(e){return e(E.b).getValue(m.i)})),u=Object(f.useSelect)((function(e){return e(_.r).isFetchingSyncAvailableAudiences()})),O=Object(f.useSelect)((function(e){return e(p.a).getValue(m.h,m.f)})),k=Object(f.useDispatch)(h.a).dismissItem,y=Object(d.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,k(m.a);case 2:case"end":return e.stop()}}),e)}))),[k]);return Object(d.useEffect)((function(){Array.isArray(O)&&(O.length>1&&a(!0),s||1!==(null==O?void 0:O.length)||a(!1))}),[O,s,a]),c||i||u||!(null==O?void 0:O.length)?null:e.createElement(v.a,{className:"googlesitekit-audience-selection-panel__add-group-notice",content:Object(g.__)("By adding another group to your dashboard, you will be able to compare them and understand which content brings back users from each group","google-site-kit"),dismissLabel:Object(g.__)("Got it","google-site-kit"),Icon:b.a,onDismiss:y})}AddGroupNotice.propTypes={savedItemSlugs:u.a.array}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fill:"currentColor",d:"M10 18.333c-.458 0-.854-.16-1.188-.479a1.66 1.66 0 01-.479-1.188h3.334c0 .459-.167.855-.5 1.188-.32.32-.709.48-1.167.48zm-3.333-2.5v-1.667h6.666v1.667H6.667zm.208-2.5a6.47 6.47 0 01-2.292-2.292c-.555-.958-.833-2-.833-3.125 0-1.736.604-3.208 1.813-4.416C6.784 2.278 8.262 1.667 10 1.667c1.736 0 3.208.61 4.417 1.833 1.222 1.208 1.833 2.68 1.833 4.417a6.008 6.008 0 01-.854 3.124 6.303 6.303 0 01-2.271 2.292h-6.25zm.5-1.667h5.25a4.528 4.528 0 001.438-1.645c.347-.653.52-1.355.52-2.105 0-1.277-.444-2.36-1.333-3.25-.889-.888-1.972-1.333-3.25-1.333s-2.361.445-3.25 1.333c-.889.89-1.333 1.973-1.333 3.25 0 .75.166 1.452.5 2.105a4.722 4.722 0 001.458 1.645z"});t.a=function SvgLightbulb(e){return i.createElement("svg",r({fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return AudienceCreationNotice}));var r=n(5),a=n.n(r),o=n(16),c=n.n(o),l=n(15),s=n.n(l),u=n(2),d=n(0),g=n(157),f=n(3),m=n(18),p=n(9),h=n(40),b=n(29),v=n(13),E=n(7),_=n(23),O=n(8),k=n(35),y=n(20),j=n(104),S=n(260),w=n(258),C=n(494);function AudienceCreationNotice(){var t=Object(m.a)(),n=Object(d.useState)(!1),r=s()(n,2),o=r[0],l=r[1],A=Object(f.useInViewSelect)((function(e){var t=(0,e(O.r).getConfigurableAudiences)();if(void 0!==t)return t.length?t.filter((function(e){return"SITE_KIT_AUDIENCE"===e.audienceType})):[]})),N=Object(f.useDispatch)(E.a).dismissItem,T=Object(f.useDispatch)(_.b).setValue,x=Object(f.useInViewSelect)((function(e){return e(E.a).isItemDismissed(h.d)})),D=Object(f.useInViewSelect)((function(e){return e(E.a).isItemDismissed(h.b)})),R=Object(f.useInViewSelect)((function(e){return e(E.a).hasScope(O.h)})),M=Object(f.useSelect)((function(e){return e(_.b).getValue(h.i)})),I=Object(g.a)(e.location.href,{notification:"audience_segmentation"}),P=Object(f.useDispatch)(b.a).setValues,L=Object(f.useDispatch)(E.a).setPermissionScopeError,B=Object(f.useDispatch)(O.r),z=B.createAudience,F=B.syncAvailableAudiences,V=Object(f.useSelect)((function(e){return e(b.a).getValue(h.c,"autoSubmit")})),W=Object(f.useSelect)((function(e){return e(b.a).getValue(h.c,"audienceToCreate")})),H=Object(d.useState)([]),U=s()(H,2),q=U[0],G=U[1],K=Object(d.useCallback)(function(){var e=c()(a.a.mark((function e(t){var n,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(l(t),R){e.next=5;break}return P(h.c,{autoSubmit:!0,audienceToCreate:t}),L({code:k.a,message:Object(u.__)("Additional permissions are required to create a new audience in Analytics.","google-site-kit"),data:{status:403,scopes:[O.h],skipModal:!0,redirectURL:I}}),e.abrupt("return");case 5:return P(h.c,{autoSubmit:!1,audienceToCreate:void 0}),e.next=8,z(O.t[t]);case 8:return n=e.sent,i=n.error,G(i?[i]:[]),e.next=13,F();case 13:l(!1),i||T(h.e,!0);case 15:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[R,z,F,P,L,I,T]),Y=Object(f.useSelect)((function(e){return e(v.c).getSetupErrorCode()})),X=V&&"access_denied"===Y;Object(d.useEffect)((function(){function e(){return(e=c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!R||!V){e.next=4;break}return T(h.i,!0),e.next=4,K(W);case 4:case"end":return e.stop()}}),e)})))).apply(this,arguments)}!function(){e.apply(this,arguments)}()}),[W,K,R,V,T]);var $=!x&&(null==A?void 0:A.length)<2;if(Object(d.useEffect)((function(){M&&$&&Object(p.I)("".concat(t,"_audiences-sidebar-create-audiences"),"view_notice")}),[M,$,t]),Object(d.useEffect)((function(){!M||R||D||Object(p.I)("".concat(t,"_audiences-sidebar-create-audiences"),"view_oauth_notice")}),[R,D,M,t]),!$)return null;var Z=Object.keys(O.t).filter((function(e){return!A.some((function(t){return t.audienceSlug===e}))}));return i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice"},i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-header"},i.createElement("p",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-title"},Object(u.__)("Create groups suggested by Site Kit","google-site-kit")),i.createElement(y.a,{className:"googlesitekit-audience-selection-panel__audience-creation-notice-close",onClick:function(){N(h.d)},linkButton:!0},i.createElement(j.a,{width:"15",height:"15"}))),i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-body"},Z&&Z.map((function(e){return i.createElement("div",{key:e,className:"googlesitekit-audience-selection-panel__audience-creation-notice-audience"},i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-audience-details"},i.createElement("h3",null,O.t[e].displayName),i.createElement("p",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-audience-description"},O.t[e].description)),i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-audience-button"},i.createElement(S.b,{spinnerPosition:S.a.BEFORE,onClick:function(){Object(p.I)("".concat(t,"_audiences-sidebar-create-audiences"),"create_audience",e).finally((function(){K(e)}))},isSaving:o===e},Object(u.__)("Create","google-site-kit"))))}))),!R&&!D&&i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-info"},i.createElement(w.b,{title:Object(u.__)("Creating these groups require more data tracking. You will be directed to update your Analytics property.","google-site-kit"),dismissLabel:Object(u.__)("Got it","google-site-kit"),onDismiss:function(){Object(p.I)("".concat(t,"_audiences-sidebar-create-audiences"),"dismiss_oauth_notice").finally((function(){N(h.b)}))},variant:w.a.WARNING,hideIcon:!0})),(q.length>0||X)&&i.createElement(C.a,{apiErrors:q,hasOAuthError:X}))}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceCreationErrorNotice}));var i=n(1),r=n.n(i),a=n(0),o=n(38),c=n(2),l=n(3),s=n(18),u=n(35),d=n(9),g=n(40),f=n(13),m=n(23),p=n(8),h=n(10),b=n(20),v=n(58);function AudienceCreationErrorNotice(t){var n,i,r=t.apiErrors,E=t.hasOAuthError,_=Object(s.a)(),O=Array.isArray(r)?r:[r],k=Object(l.useSelect)((function(e){return e(f.c).getErrorTroubleshootingLinkURL({code:"analytics-4_insufficient_permissions"})})),y=Object(l.useSelect)((function(e){return e(p.r).getServiceEntityAccessURL()})),j=Object(l.useSelect)((function(e){return e(f.c).getErrorTroubleshootingLinkURL({code:"access_denied"})})),S=Object(l.useSelect)((function(e){return e(m.b).getValue(g.i)})),w=O.length>0,C=O.some((function(e){return Object(u.e)(e)}));return Object(a.useEffect)((function(){if(S&&(w||E)){var e="setup_error";E?e="auth_error":C&&(e="insufficient_permissions_error"),Object(d.I)("".concat(_,"_audiences-sidebar-create-audiences"),e)}}),[w,C,E,S,_]),O.length||E?(E?i=Object(o.a)(Object(c.__)("Setup was interrupted because you didn’t grant the necessary permissions. Click on Create again to retry. If that doesn’t work, <HelpLink />","google-site-kit"),{HelpLink:e.createElement(b.a,{href:j,external:!0,hideExternalIndicator:!0},Object(c.__)("get help","google-site-kit"))}):C?(n=Object(c.__)("Insufficient permissions","google-site-kit"),i=Object(o.a)(Object(c.__)("Contact your administrator. Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(b.a,{href:k,external:!0,hideExternalIndicator:!0},Object(c.__)("Get help","google-site-kit"))})):(n=Object(c.__)("Analytics update failed","google-site-kit"),i=Object(c.__)("Click on Create to try again.","google-site-kit")),e.createElement("div",{className:"googlesitekit-audience-creation-error-notice"},e.createElement(v.a,{width:24,height:24}),e.createElement("div",{className:"googlesitekit-audience-creation-error-notice__content"},n&&e.createElement("p",{className:"googlesitekit-audience-creation-error-notice__title"},n),e.createElement("p",{className:"googlesitekit-audience-creation-error-notice__description"},i)),C&&e.createElement("div",{className:"googlesitekit-audience-creation-error-notice__actions"},e.createElement(h.Button,{href:y,target:"_blank",danger:!0,onClick:function(){Object(d.I)("".concat(_,"_audiences-sidebar-create-audiences"),"insufficient_permissions_error_request_access")}},Object(c.__)("Request access","google-site-kit"))))):null}AudienceCreationErrorNotice.propTypes={apiErrors:r.a.oneOfType([r.a.arrayOf(r.a.object),r.a.object,r.a.array]),hasOAuthError:r.a.bool}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(0),u=n(38),d=n(2),g=n(3),f=n(18),m=n(9),p=n(40),h=n(13),b=n(23),v=n(8),E=n(35),_=n(20),O=n(135),k=n(496),y=n(386);function ErrorNotice(){var t=Object(f.a)(),n=Object(g.useSelect)((function(e){return e(v.r).getErrorForAction("syncAvailableAudiences")})),i=Object(g.useInViewSelect)((function(e){return e(v.r).getAudienceUserCountReportErrors()}))||[],a=l()(i,2),c=a[0],j=a[1],S=Object(g.useSelect)((function(e){return e(h.c).getErrorTroubleshootingLinkURL({code:"analytics-4_insufficient_permissions"})})),w=Object(g.useSelect)((function(e){return e(b.b).getValue(p.i)})),C=Object(g.useDispatch)(v.r),A=C.clearError,N=C.syncAvailableAudiences,T=Object(s.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,A("syncAvailableAudiences");case 2:N();case 3:case"end":return e.stop()}}),e)}))),[A,N]),x=[];n&&x.push(n),j&&x.push(j),c&&x.push(c);var D=x.length>0,R=x.some((function(e){return Object(E.e)(e)}));if(Object(s.useEffect)((function(){w&&D&&Object(m.I)("".concat(t,"_audiences-sidebar"),R?"insufficient_permissions_error":"data_loading_error")}),[D,R,w,t]),!x.length)return null;var M=[j,c].some((function(e){return!!e}));return e.createElement("div",{className:"googlesitekit-audience-selection-panel__error-notice"},e.createElement("p",null,R?Object(u.a)(Object(d.__)("Insufficient permissions, contact your administrator. Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(_.a,{href:S,external:!0,hideExternalIndicator:!0},Object(d.__)("Get help","google-site-kit"))}):Object(d.__)("Data loading failed","google-site-kit")),e.createElement("div",{className:"googlesitekit-audience-selection-panel__error-notice-actions"},R||M?e.createElement(O.a,{moduleSlug:"analytics-4",error:x,hideGetHelpLink:!0,buttonVariant:"danger",RequestAccessButton:k.a,RetryButton:y.a}):e.createElement(y.a,{handleRetry:T})))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RequestAccessButton}));var i=n(1),r=n.n(i),a=n(2),o=n(10),c=n(9),l=n(18);function RequestAccessButton(t){var n=t.requestAccessURL,i=Object(l.a)();return e.createElement(o.Button,{className:"googlesitekit-audience-selection-panel__error-notice-action",tertiary:!0,href:n,target:"_blank",onClick:function(){Object(c.I)("".concat(i,"_audiences-sidebar"),"insufficient_permissions_error_request_access")}},Object(a.__)("Request access","google-site-kit"))}RequestAccessButton.propTypes={requestAccessURL:r.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Footer}));var i=n(5),r=n.n(i),a=n(27),o=n.n(a),c=n(16),l=n.n(c),s=n(15),u=n.n(s),d=n(6),g=n.n(d),f=n(1),m=n.n(f),p=n(0),h=n(2),b=n(3),v=n(18),E=n(9),_=n(40),O=n(29),k=n(7),y=n(8),j=n(117);function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach((function(t){g()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Footer(t){var n,i=t.isOpen,a=t.closePanel,c=t.savedItemSlugs,s=Object(v.a)(),d=Object(b.useSelect)((function(e){return e(O.a).getValue(_.h,_.f)})),g=Object(b.useInViewSelect)((function(e){return e(k.a).getUserAudienceSettings()})),f=Object(b.useSelect)((function(e){return e(k.a).getErrorForAction("saveUserAudienceSettings",[w(w({},g),{},{configuredAudiences:d})])})),m=Object(b.useSelect)((function(e){return e(k.a).isSavingUserAudienceSettings()})),S=Object(b.useInViewSelect)((function(e){var t=e(k.a).getDismissedItems();return null==t?void 0:t.filter((function(e){return e.startsWith("audience-tile-")}))})),C=Object(b.useSelect)((function(e){return e(y.r).getAvailableAudiences()})),A=Object(b.useDispatch)(k.a),N=A.saveUserAudienceSettings,T=A.removeDismissedItems,x=Object(b.useSelect)(k.a).getConfiguredAudiences,D=(null==d?void 0:d.length)||0;D<_.k?n=Object(h.sprintf)(
/* translators: 1: Minimum number of groups that can be selected. 2: Number of selected groups. */
Object(h._n)("Select at least %1$d group (%2$d selected)","Select at least %1$d groups (%2$d selected)",_.k,"google-site-kit"),_.k,D):D>_.j&&(n=Object(h.sprintf)(
/* translators: 1: Maximum number of groups that can be selected. 2: Number of selected groups. */
Object(h.__)("Select up to %1$d groups (%2$d selected)","google-site-kit"),_.j,D));var R=Object(p.useState)(null),M=u()(R,2),I=M[0],P=M[1],L=Object(p.useCallback)(function(){var e=l()(r.a.mark((function e(t){var n,i,a,c;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return P(null),e.next=3,N({configuredAudiences:t});case 3:if(n=e.sent,i=n.error){e.next=14;break}if(a=(null==S?void 0:S.filter((function(e){var n=e.replace("audience-tile-","");return!t.includes(n)})))||[],t.every((function(e){return null==S?void 0:S.includes("audience-tile-".concat(e))}))&&a.push("audience-tile-".concat(t[0])),!((null==a?void 0:a.length)>0)){e.next=14;break}return e.next=11,T.apply(void 0,o()(a));case 11:c=e.sent,(i=c.error)&&P(i);case 14:return e.abrupt("return",{error:i});case 15:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[S,T,N]),B=Object(p.useCallback)((function(){var e={USER_AUDIENCE:"user",SITE_KIT_AUDIENCE:"site-kit",DEFAULT_AUDIENCE:"default"},t=x(),n=Object.keys(e).map((function(n){var i=t.filter((function(e){var t=null==C?void 0:C.find((function(t){var n=t.name;return e===n}));return(null==t?void 0:t.audienceType)===n}));return"".concat(e[n],":").concat(i.length)})).join(",");Object(E.I)("".concat(s,"_audiences-sidebar"),"audiences_sidebar_save",n)}),[C,x,s]),z=Object(p.useCallback)((function(){Object(E.I)("".concat(s,"_audiences-sidebar"),"audiences_sidebar_cancel")}),[s]);return e.createElement(j.a,{savedItemSlugs:c,selectedItemSlugs:d,saveSettings:L,saveError:f||I,itemLimitError:n,minSelectedItemCount:_.k,maxSelectedItemCount:_.j,isBusy:m,isOpen:i,closePanel:a,onSaveSuccess:B,onCancel:z})}Footer.propTypes={isOpen:m.a.bool,closePanel:m.a.func.isRequired,savedItemSlugs:m.a.array}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Header}));var i=n(1),r=n.n(i),a=n(0),o=n(38),c=n(2),l=n(3),s=n(32),u=n(13),d=n(7),g=n(34),f=n(20),m=n(117);function Header(t){var n=t.closePanel,i=Object(g.a)(),r=Object(l.useSelect)((function(e){return e(u.c).getAdminURL("googlesitekit-settings")})),p=Object(l.useSelect)((function(e){return e(d.a).isSavingUserAudienceSettings()})),h=Object(l.useDispatch)(s.a).navigateTo,b=Object(a.useCallback)((function(){return h("".concat(r,"#/admin-settings"))}),[h,r]);return e.createElement(m.b,{title:Object(c.__)("Select visitor groups","google-site-kit"),onCloseClick:n},!i&&e.createElement("p",null,Object(o.a)(Object(c.__)("You can deactivate this widget in <link><strong>Settings</strong></link>","google-site-kit"),{link:e.createElement(f.a,{secondary:!0,onClick:b,disabled:p}),strong:e.createElement("strong",null)})))}Header.propTypes={closePanel:r.a.func.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LearnMoreLink}));var i=n(2),r=n(38),a=n(3),o=n(13),c=n(20);function LearnMoreLink(){var t=Object(a.useSelect)((function(e){return e(o.c).getGoogleSupportURL({path:"/analytics/answer/12799087"})}));return e.createElement("div",{className:"googlesitekit-audience-selection-panel__learn-more"},Object(r.a)(Object(i.__)("Learn more about grouping site visitors and audiences in <link><strong>Analytics</strong></link>","google-site-kit"),{link:e.createElement(c.a,{secondary:!0,href:t,external:!0}),strong:e.createElement("strong",null)}))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceCreationSuccessNotice}));var i=n(0),r=n(2),a=n(3),o=n(18),c=n(9),l=n(40),s=n(23),u=n(10),d=n(137);function AudienceCreationSuccessNotice(){var t=Object(o.a)(),n=Object(a.useDispatch)(s.b).setValue,g=Object(a.useSelect)((function(e){return e(s.b).getValue(l.e)})),f=Object(a.useSelect)((function(e){return e(s.b).getValue(l.i)}));return Object(i.useEffect)((function(){f&&g&&Object(c.I)("".concat(t,"_audiences-sidebar-create-audiences-success"),"view_notification")}),[f,g,t]),g?e.createElement("div",{className:"googlesitekit-audience-selection-panel__success-notice"},e.createElement("div",{className:"googlesitekit-audience-selection-panel__success-notice-icon"},e.createElement(d.a,{width:24,height:24})),e.createElement("p",{className:"googlesitekit-audience-selection-panel__success-notice-message"},Object(r.__)("Visitor group created successfully!","google-site-kit")),e.createElement("div",{className:"googlesitekit-audience-selection-panel__success-notice-actions"},e.createElement(u.Button,{tertiary:!0,onClick:function(){Object(c.I)("".concat(t,"_audiences-sidebar-create-audiences-success"),"dismiss_notification").finally((function(){n(l.e,!1)}))}},Object(r.__)("Got it","google-site-kit")))):null}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("defs",null,i.createElement("filter",{id:"audience-connect-analytics-cta-graphic_svg__c",x:109.551,y:18.171,width:144.59,height:185.064,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1731_24094"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1731_24094",result:"shape"})),i.createElement("filter",{id:"audience-connect-analytics-cta-graphic_svg__d",x:236.859,y:18.171,width:144.59,height:185.064,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1731_24094"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1731_24094",result:"shape"})),i.createElement("clipPath",{id:"audience-connect-analytics-cta-graphic_svg__a"},i.createElement("path",{fill:"#fff",d:"M0 0h480v150H0z"}))),o=i.createElement("path",{d:"M91.722 36.579a71.937 71.937 0 017.307-6.582c24.521-19.234 44.779-19.204 72.826-15.693 18.961 2.373 30.038 11.4 55.889 9.98 25.851-1.42 32.474-7.992 64.117-5.887 25.048 1.667 36.285 6.612 58.554 18.182 20.61 10.707 39.324 29.519 48.728 54.397 16.12 42.644-12.622 119.393-51.166 123.012-27.93 2.623-50.979-28.308-79.169-21.145-17.366 4.414-27.666 22.927-41.064 35.144-15.631 14.255-49.304 13.359-67.607 5.751-17.442-7.248-34.409-21.615-40.106-42.775-4.337-16.114-5.519-35.322-17.661-50.04-14.694-17.811-23.672-25.756-28.716-49.947-4.382-21.009 5.045-40.938 18.068-54.397z",fill:"#B8E6CA"}),c=i.createElement("path",{d:"M91.722 36.579a71.937 71.937 0 017.307-6.582c24.521-19.234 44.779-19.204 72.826-15.693 18.961 2.373 30.038 11.4 55.889 9.98 25.851-1.42 32.474-7.992 64.117-5.887 25.048 1.667 36.285 6.612 58.554 18.182 20.61 10.707 39.324 29.519 48.728 54.397 16.12 42.644-12.622 119.393-51.166 123.012-27.93 2.623-50.979-28.308-79.169-21.145-17.366 4.414-27.666 22.927-41.064 35.144-15.631 14.255-49.304 13.359-67.607 5.751-17.442-7.248-34.409-21.615-40.106-42.775-4.337-16.114-5.519-35.322-17.661-50.04-14.694-17.811-23.672-25.756-28.716-49.947-4.382-21.009 5.045-40.938 18.068-54.397z",fill:"#B8E6CA"}),l=i.createElement("g",{mask:"url(#audience-connect-analytics-cta-graphic_svg__b)"},i.createElement("g",{filter:"url(#audience-connect-analytics-cta-graphic_svg__c)"},i.createElement("rect",{x:125.551,y:30.171,width:112.591,height:153.065,rx:8.095,fill:"#fff"})),i.createElement("rect",{x:139.555,y:93.193,width:39.014,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("rect",{x:139.555,y:82.189,width:14.005,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("rect",{x:139.555,y:118.259,width:14.005,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("path",{d:"M202.578 91.693a6.502 6.502 0 016.502-6.503h10.004a6.502 6.502 0 010 13.005H209.08a6.502 6.502 0 01-6.502-6.502z",fill:"#B8E6CA"}),i.createElement("rect",{x:139.535,y:45.625,width:26.492,height:6.623,rx:3.311,fill:"#EBEEF0"}),i.createElement("path",{d:"M202.578 127.763a6.502 6.502 0 016.502-6.502h10.004a6.502 6.502 0 110 13.004H209.08a6.502 6.502 0 01-6.502-6.502z",fill:"#FFDED3"}),i.createElement("rect",{x:138.555,y:129.263,width:41.014,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("path",{d:"M238.141 65.862H126.286",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("g",{filter:"url(#audience-connect-analytics-cta-graphic_svg__d)"},i.createElement("rect",{x:252.859,y:30.171,width:112.591,height:153.065,rx:8.095,fill:"#fff"})),i.createElement("rect",{x:266.809,y:93.193,width:38.859,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("rect",{x:266.809,y:82.189,width:13.949,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("rect",{x:266.805,y:118.259,width:13.949,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("path",{d:"M329.582 91.693a6.502 6.502 0 016.502-6.503h9.912a6.502 6.502 0 110 13.005h-9.912a6.502 6.502 0 01-6.502-6.502z",fill:"#B8E6CA"}),i.createElement("rect",{x:266.844,y:45.625,width:26.492,height:6.623,rx:3.311,fill:"#EBEEF0"}),i.createElement("path",{d:"M358.094 65.862H252.862",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("path",{d:"M329.582 127.763a6.502 6.502 0 016.502-6.502h9.912a6.502 6.502 0 110 13.004h-9.912a6.502 6.502 0 01-6.502-6.502z",fill:"#FFDED3"}),i.createElement("rect",{x:265.812,y:129.263,width:40.852,height:7.002,rx:3.501,fill:"#EBEEF0"}));t.a=function SvgAudienceConnectAnalyticsCtaGraphic(e){return i.createElement("svg",r({viewBox:"-3 1 333.666 149.252",fill:"none"},e),a,i.createElement("g",{clipPath:"url(#audience-connect-analytics-cta-graphic_svg__a)",transform:"translate(-73)"},o,i.createElement("mask",{id:"audience-connect-analytics-cta-graphic_svg__b",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:72,y:12,width:332,height:228},c),l))}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M67.728 34.59a71.982 71.982 0 017.31-6.584c24.527-19.24 44.792-19.21 72.848-15.698 18.966 2.374 30.047 11.404 55.905 9.984C229.65 20.872 234.5 10.117 271 14.308 307.5 18.5 322.5-1.5 360.5 1.5s52 32 72 42 37 8.5 50 35 4 83.5-36 106-99.355 25.386-122.439 27.553c-27.938 2.624-50.995-28.317-79.194-21.151-17.371 4.415-27.674 22.934-41.076 35.155-15.636 14.258-49.319 13.362-67.627 5.752-17.448-7.25-34.42-21.622-40.118-42.788-4.338-16.119-5.521-35.333-17.667-50.056-14.698-17.816-23.679-25.763-28.725-49.961-4.382-21.016 5.047-40.95 18.074-54.414z",fill:"#B8E6CA"}),o=i.createElement("path",{d:"M67.728 34.59a71.982 71.982 0 017.31-6.584c24.527-19.24 44.792-19.21 72.848-15.698 18.966 2.374 30.047 11.404 55.905 9.984C229.65 20.872 234.5 10.117 271 14.308 307.5 18.5 322.5-1.5 360.5 1.5s52 32 72 42 37 8.5 50 35 4 83.5-36 106-99.355 25.386-122.439 27.553c-27.938 2.624-50.995-28.317-79.194-21.151-17.371 4.415-27.674 22.934-41.076 35.155-15.636 14.258-49.319 13.362-67.627 5.752-17.448-7.25-34.42-21.622-40.118-42.788-4.338-16.119-5.521-35.333-17.667-50.056-14.698-17.816-23.679-25.763-28.725-49.961-4.382-21.016 5.047-40.95 18.074-54.414z",fill:"#B8E6CA"}),c=i.createElement("g",{filter:"url(#audience-connect-analytics-cta-graphic-tablet_svg__filter0_d_2898_16714)",mask:"url(#audience-connect-analytics-cta-graphic-tablet_svg__a)"},i.createElement("rect",{x:93,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:107.008,y:92.222,width:39.025,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:107.008,y:81.214,width:14.009,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:107.008,y:117.295,width:14.009,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M170.051 90.72a6.504 6.504 0 016.504-6.504h10.007a6.504 6.504 0 010 13.009h-10.007a6.504 6.504 0 01-6.504-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:106.984,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M170.051 126.802a6.504 6.504 0 016.504-6.505h10.007a6.504 6.504 0 010 13.009h-10.007a6.504 6.504 0 01-6.504-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:106.008,y:128.303,width:41.027,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M205.625 64.882H93.736",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("rect",{x:220.348,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:234.301,y:92.222,width:38.871,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:234.301,y:81.214,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:234.301,y:117.295,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M297.094 90.72a6.504 6.504 0 016.504-6.504h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.504-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:234.332,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M325.613 64.882H220.349",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("path",{d:"M297.094 126.802a6.504 6.504 0 016.504-6.505h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.504-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:233.305,y:128.303,width:40.864,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:347.695,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:361.648,y:92.222,width:38.871,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:361.648,y:81.214,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:361.648,y:117.295,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M424.441 90.72a6.504 6.504 0 016.505-6.504h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.505-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:361.68,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M452.961 64.882H347.697",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("path",{d:"M424.441 126.802a6.505 6.505 0 016.505-6.505h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.505-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:360.652,y:128.303,width:40.864,height:7.005,rx:3.502,fill:"#EBEEF0"})),l=i.createElement("defs",null,i.createElement("clipPath",{id:"audience-connect-analytics-cta-graphic-tablet_svg__clip0_2898_16714"},i.createElement("path",{fill:"#fff",d:"M0 0h553v158H0z"})),i.createElement("filter",{id:"audience-connect-analytics-cta-graphic-tablet_svg__filter0_d_2898_16714",x:77,y:17.181,width:399.32,height:185.111,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2898_16714"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2898_16714",result:"shape"})));t.a=function SvgAudienceConnectAnalyticsCtaGraphicTablet(e){return i.createElement("svg",r({viewBox:"0 0 553 146",fill:"none"},e),i.createElement("g",{clipPath:"url(#audience-connect-analytics-cta-graphic-tablet_svg__clip0_2898_16714)"},a,i.createElement("mask",{id:"audience-connect-analytics-cta-graphic-tablet_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:48,y:1,width:441,height:237},o),c),l)}},,,,,,,,function(e,t,n){"use strict";(function(e){var i=n(6),r=n.n(i),a=n(1),o=n.n(a);function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Spinner(t){var n=t.isSaving,i=t.style,r=void 0===i?{}:i;return e.createElement("span",{className:"spinner",style:l({display:n?"inline-block":"none",float:"none",marginTop:"0",visibility:"visible"},r)})}Spinner.propTypes={isSaving:o.a.bool,style:o.a.object},t.a=Spinner}).call(this,n(4))},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a}));var i=n(2);function r(e){return 0===e.length}function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return 0===e.length?1===t?Object(i.__)("Please select an answer","google-site-kit"):Object(i.__)("Please select at least 1 answer","google-site-kit"):null}},,,,,,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleSettingsWarning}));var i=n(3),r=n(19),a=n(7),o=n(217),c=n(245);function ModuleSettingsWarning(t){var n=t.slug,l=Object(i.useSelect)((function(e){var t;return null===(t=e(r.a))||void 0===t?void 0:t.getCheckRequirementsError(n)}));return l?a.c===l.code?e.createElement(c.a,{moduleSlug:n}):e.createElement(o.a,null,l.message):null}}).call(this,n(4))},,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i="consent-mode-setup-cta-widget"},,,,,,,,function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var i=["ads","reader-revenue-manager","sign-in-with-google"],r=["sign-in-with-google"],a=[]},,,,,,,,,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UserInputSelectOptions}));var i=n(6),r=n.n(i),a=n(27),o=n.n(a),c=n(1),l=n.n(c),s=n(0),u=n(56),d=n(2),g=n(3),f=n(10),m=n(7),p=n(29),h=n(32),b=n(17),v=n(87),E=n(9),_=n(18);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function UserInputSelectOptions(t){var n=t.slug,i=t.descriptions,a=t.options,c=t.max,l=t.next,k=t.showInstructions,y=t.alignLeftOptions,j=Object(_.a)(),S=Object(g.useSelect)((function(e){return e(m.a).getUserInputSetting(n)||[]})),w=Object(g.useSelect)((function(e){return e(m.a).isSavingUserInputSettings(S)})),C=Object(g.useSelect)((function(e){return e(h.a).isNavigating()})),A=Object(g.useDispatch)(m.a).setUserInputSetting,N=Object(s.useRef)();Object(s.useEffect)((function(){if(null==N?void 0:N.current){var e=function(e){e&&setTimeout((function(){e.focus()}),50)},t=1===c?"radio":"checkbox",n=N.current.querySelector('input[type="'.concat(t,'"]:checked'));if(n)e(n);else e(N.current.querySelector('input[type="'.concat(t,'"]')))}}),[c]);var T=Object(g.useDispatch)(p.a).setValues,x=Object(s.useCallback)((function(e){var t=e.target,i=t.value,a=t.checked,l=new Set([i].concat(o()(S)));a||l.delete(i);var s=n===v.j?"content_frequency_question_answer":"site_".concat(n,"_question_answer"),u=Array.from(l).slice(0,c);Object(E.I)("".concat(j,"_kmw"),s,u.join()),n===v.i&&T(v.b,r()({},n,S)),A(n,u)}),[c,A,n,S,j,T]),D=Object(s.useCallback)((function(e){e.keyCode===u.b&&S.length>0&&S.length<=c&&!S.includes("")&&l&&"function"==typeof l&&l()}),[S,l,c]),R=r()({},c>1?"onChange":"onClick",x),M=1===c?f.Radio:f.Checkbox,I=Object.keys(a).map((function(t){if("sell_products_or_service"===t)return!1;var o=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({id:"".concat(n,"-").concat(t),value:t,description:null==i?void 0:i[t],checked:S.includes(t),onKeyDown:D,alignLeft:y},R);return c>1?(o.disabled=S.length>=c&&!S.includes(t),o.name="".concat(n,"-").concat(t)):o.name=n,(w||C)&&(o.disabled=!0),e.createElement("div",{key:t,className:"googlesitekit-user-input__select-option"},e.createElement(M,o,a[t]))}));return e.createElement(b.a,{className:"googlesitekit-user-input__select-options-wrapper",lgStart:6,lgSize:6,mdSize:8,smSize:4},k&&e.createElement("p",{className:"googlesitekit-user-input__select-instruction"},e.createElement("span",null,Object(d.sprintf)(
/* translators: %s: number of answers allowed. */
Object(d._n)("Select only %d answer","Select up to %d answers",c,"google-site-kit"),c))),e.createElement("div",{className:"googlesitekit-user-input__select-options",ref:N},I))}UserInputSelectOptions.propTypes={slug:l.a.string.isRequired,descriptions:l.a.shape({}),options:l.a.shape({}).isRequired,max:l.a.number,next:l.a.func,showInstructions:l.a.bool,alignLeftOptions:l.a.bool},UserInputSelectOptions.defaultProps={max:1,showInstructions:!1,alignLeftOptions:!1}}).call(this,n(4))},,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return h})),n.d(t,"b",(function(){return AudienceSegmentationSetupSuccessSubtleNotification}));var r=n(1),a=n.n(r),o=n(2),c=n(0),l=n(115),s=n(93),u=n(24),d=n(92),g=n(168),f=n(3),m=n(41),p=n(7),h="setup-success-notification-audiences";function AudienceSegmentationSetupSuccessSubtleNotification(t){var n=t.id,r=t.Notification,a=Object(u.e)(),b=Object(f.useDispatch)(m.a).dismissNotification,v=Object(f.useSelect)((function(e){return e(p.a).isAudienceSegmentationWidgetHidden()}));Object(c.useEffect)((function(){v&&b(h)}),[b,v]);return void 0===v?null:i.createElement(r,null,i.createElement(l.a,{title:Object(o.__)("Success! Visitor groups added to your dashboard","google-site-kit"),description:Object(o.__)("Get to know how different types of visitors interact with your site, e.g. which pages they visit and for how long","google-site-kit"),dismissCTA:i.createElement(d.a,{id:n,primary:!1,dismissLabel:Object(o.__)("Got it","google-site-kit")}),additionalCTA:i.createElement(g.a,{id:n,ctaLabel:Object(o.__)("Show me","google-site-kit"),onCTAClick:function(t){t.preventDefault(),b(h),setTimeout((function(){e.scrollTo({top:Object(s.a)(".googlesitekit-widget-area--mainDashboardTrafficAudienceSegmentation",a),behavior:"smooth"})}),50)}})}))}AudienceSegmentationSetupSuccessSubtleNotification.propTypes={id:a.a.string.isRequired,Notification:a.a.elementType.isRequired}}).call(this,n(28),n(4))},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UserInputQuestionNotice}));var i=n(11),r=n.n(i),a=n(2);function UserInputQuestionNotice(t){var n=t.className;return e.createElement("p",{className:r()(n,"googlesitekit-user-input__question-notice")},Object(a.__)("You can always edit your answers later in Settings","google-site-kit"))}}).call(this,n(4))},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M10.6.6L12 2 6 8 0 2 1.4.6 6 5.2z",fill:"currentColor",fillRule:"evenodd"});t.a=function SvgChevronDownV2(e){return i.createElement("svg",r({viewBox:"0 0 12 8"},e),a)}},,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UserInputQuestionAuthor}));var i=n(1),r=n.n(i),a=n(2),o=n(3),c=n(7);function UserInputQuestionAuthor(t){var n=t.slug,i=Object(o.useSelect)((function(e){return e(c.a).getUserInputSettingAuthor(n)}));return i&&i.photo&&i.login?e.createElement("div",{className:"googlesitekit-user-input__author"},e.createElement("p",null,Object(a.__)("This question has been answered by:","google-site-kit")),e.createElement("div",{className:"googlesitekit-user-input__author-info"},e.createElement("img",{alt:i.login,src:i.photo}),i.login)):null}UserInputQuestionAuthor.propTypes={slug:r.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CancelUserInputButton}));var i=n(1),r=n.n(i),a=n(2),o=n(3),c=n(10),l=n(13),s=n(32);function CancelUserInputButton(t){var n=t.disabled,i=Object(o.useSelect)((function(e){return e(l.c).getAdminURL("googlesitekit-dashboard")})),r=Object(o.useDispatch)(s.a).navigateTo;return e.createElement(c.Button,{tertiary:!0,className:"googlesitekit-user-input__buttons--cancel",onClick:function(){return r(i)},disabled:n},Object(a.__)("Cancel","google-site-kit"))}CancelUserInputButton.propTypes={disabled:r.a.bool}}).call(this,n(4))},,,,,,,,,,,function(e,t,n){"use strict";(function(e,i){var r=n(5),a=n.n(r),o=n(16),c=n.n(o),l=n(15),s=n.n(l),u=n(2),d=n(0),g=n(38),f=n(56),m=n(121),p=n(3),h=n(109),b=n(37),v=n(72),E=n(20),_=n(13),O=n(32),k=n(36),y=n(18);t.a=function ResetButton(t){var n=t.children,r=Object(p.useSelect)((function(e){return e(_.c).getAdminURL("googlesitekit-splash",{notification:"reset_success"})})),o=Object(p.useSelect)((function(e){return e(_.c).isDoingReset()})),l=Object(p.useSelect)((function(e){return e(O.a).isNavigatingTo(r||"")})),j=Object(d.useState)(!1),S=s()(j,2),w=S[0],C=S[1],A=Object(d.useState)(!1),N=s()(A,2),T=N[0],x=N[1],D=Object(m.a)(C,3e3);Object(d.useEffect)((function(){o||l?C(!0):D(!1)}),[o,l,D]),Object(d.useEffect)((function(){var t=function(e){f.c===e.keyCode&&x(!1)};return T&&e.addEventListener("keyup",t,!1),function(){T&&e.removeEventListener("keyup",t)}}),[T]);var R=Object(p.useDispatch)(_.c).reset,M=Object(p.useDispatch)(O.a).navigateTo,I=Object(y.a)(),P=Object(d.useCallback)(c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,R();case 2:return e.next=4,Object(b.b)();case 4:return e.next=6,Object(k.b)(I,"reset_plugin");case 6:M(r);case 7:case"end":return e.stop()}}),e)}))),[M,r,R,I]),L=Object(d.useCallback)((function(){x(!T)}),[T]),B=Object(d.useCallback)((function(){x(!0)}),[]);return i.createElement(d.Fragment,null,i.createElement(E.a,{className:"googlesitekit-reset-button",onClick:B},n||Object(u.__)("Reset Site Kit","google-site-kit")),i.createElement(v.a,null,i.createElement(h.a,{dialogActive:T,handleConfirm:P,handleDialog:L,title:Object(u.__)("Reset Site Kit","google-site-kit"),subtitle:Object(g.a)(Object(u.__)("Resetting will disconnect all users and remove all Site Kit settings and data within WordPress. <br />You and any other users who wish to use Site Kit will need to reconnect to restore access.","google-site-kit"),{br:i.createElement("br",null)}),confirmButton:Object(u.__)("Reset","google-site-kit"),danger:!0,small:!0,inProgress:w})))}}).call(this,n(28),n(4))},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UserInputPreviewGroup}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(6),l=n.n(c),s=n(1),u=n.n(s),d=n(11),g=n.n(d),f=n(0),m=n(422),p=n(2),h=n(10),b=n(3),v=n(23),E=n(7),_=n(32),O=n(9),k=n(549),y=n(18),j=n(87),S=n(120),w=n(20),C=n(183),A=n(604),N=n(682),T=n(1132),x=n(29);function UserInputPreviewGroup(t){var n=t.slug,i=t.title,a=t.subtitle,c=t.values,s=t.options,u=void 0===s?{}:s,d=t.loading,D=void 0!==d&&d,R=t.settingsView,M=void 0!==R&&R,I=t.onChange,P=Object(y.a)(),L=Object(b.useSelect)((function(e){return e(_.a).isNavigating()})),B=Object(b.useSelect)((function(e){return e(v.b).getValue(j.c)})),z=Object(b.useSelect)((function(e){return e(E.a).hasUserInputSettingChanged(n)})),F=Object(b.useSelect)((function(e){var t=e(E.a).getUserInputSettings();return e(E.a).isSavingUserInputSettings(t)})),V=Object(b.useSelect)((function(e){return e(E.a).getErrorForAction("saveUserInputSettings",[])})),W=Object(b.useSelect)((function(e){return e(x.a).getValue(j.b,j.i)})),H=Object(m.a)(W);Object(f.useEffect)((function(){n===j.i&&H!==W&&void 0===W&&setTimeout((function(){var e,t;null===(e=ee.current)||void 0===e||null===(t=e.focus)||void 0===t||t.call(e)}),100)}),[W,H,n]);var U=Object(b.useDispatch)(v.b).setValues,q=Object(b.useDispatch)(E.a),G=q.saveUserInputSettings,K=q.resetUserInputSettings,Y=B===n,X=F||L,$="".concat(P,"_kmw"),Z=Object(f.useCallback)((function(){var e,t;Y?(U(l()({},j.c,void 0)),null===(e=ee.current)||void 0===e||null===(t=e.focus)||void 0===t||t.call(e)):(Object(O.I)($,"question_edit",n),U(l()({},j.c,n)))}),[$,Y,U,n]),Q=Object(k.a)(c,j.e[n]),J=Object(k.b)(c),ee=Object(f.useRef)(),te=Object(f.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!J){e.next=2;break}return e.abrupt("return");case 2:if(j.i!==n||!I){e.next=6;break}I(),e.next=10;break;case 6:return e.next=8,G();case 8:e.sent.error||(Object(O.I)($,"question_update",n),Z());case 10:case"end":return e.stop()}}),e)}))),[J,$,G,n,Z,I]),ne=Object(f.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!M){e.next=6;break}if(!(X||B&&!Y)){e.next=3;break}return e.abrupt("return");case 3:if(!Y){e.next=6;break}return e.next=6,K();case 6:Z();case 7:case"end":return e.stop()}}),e)}))),[M,X,B,Y,K,Z]),ie=Object(f.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!X){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,K();case 4:Z();case 5:case"end":return e.stop()}}),e)}))),[X,K,Z]),re="function"==typeof a?a:void 0,ae=Object(j.l)().USER_INPUT_ANSWERS_PURPOSE;return e.createElement("div",{className:g()("googlesitekit-user-input__preview-group",{"googlesitekit-user-input__preview-group--editing":Y,"googlesitekit-user-input__preview-group--individual-cta":M})},e.createElement("div",{className:g()("googlesitekit-user-input__preview-group-title",{"googlesitekit-user-input__preview-group-title-with-subtitle":re||a})},e.createElement(C.a,{loading:D,width:"340px",height:"21px"},e.createElement("p",null,i)),e.createElement(C.a,{loading:D,className:"googlesitekit-margin-left-auto",width:"60px",height:"26px"},e.createElement(w.a,{secondary:!0,onClick:ne,ref:ee,disabled:X||!!B&&!Y,linkButton:!0,trailingIcon:e.createElement(T.a,{width:20,height:20})},Y&&Object(p.__)("Close","google-site-kit"),!Y&&Object(p.__)("Edit","google-site-kit")))),e.createElement(C.a,null,e.createElement("div",{className:"googlesitekit-user-input__preview-group-subtitle"},re&&e.createElement("div",{className:"googlesitekit-user-input__preview-group-subtitle-component"},e.createElement(re,null)),!re&&e.createElement("p",null,a))),!Y&&e.createElement("div",{className:"googlesitekit-user-input__preview-answers"},e.createElement(C.a,{loading:D,width:"340px",height:"36px"},Q&&e.createElement("p",{className:"googlesitekit-error-text"},Q),!Q&&c.map((function(t){return e.createElement("div",{key:t,className:"googlesitekit-user-input__preview-answer"},u[t])})))),Y&&e.createElement(f.Fragment,null,e.createElement(A.a,{slug:n,max:j.e[n],options:u,alignLeftOptions:!0,descriptions:ae}),Q&&e.createElement("p",{className:"googlesitekit-error-text"},Q),M&&e.createElement(f.Fragment,null,e.createElement(N.a,{slug:n}),V&&e.createElement(S.a,{error:V}),e.createElement("div",{className:"googlesitekit-user-input__preview-actions"},e.createElement(h.SpinnerButton,{disabled:J,onClick:z?te:Z,isSaving:X},z||F?Object(p.__)("Apply changes","google-site-kit"):Object(p.__)("Save","google-site-kit")),e.createElement(h.Button,{tertiary:!0,disabled:X,onClick:ie},Object(p.__)("Cancel","google-site-kit"))))))}UserInputPreviewGroup.propTypes={slug:u.a.string.isRequired,title:u.a.string.isRequired,subtitle:u.a.oneOfType([u.a.string,u.a.elementType]),values:u.a.arrayOf(u.a.string).isRequired,options:u.a.shape({}),loading:u.a.bool,settingsView:u.a.bool,onChange:u.a.func}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsActiveModules}));var i=n(3),r=n(19),a=n(192),o=n(1138);function SettingsActiveModules(){var t=Object(i.useSelect)((function(e){return e(r.a).getModules()}));if(!t)return null;var n=Object.keys(t).map((function(e){return t[e]})).filter((function(e){var t=e.internal,n=e.active;return!t&&n})).sort((function(e,t){return e.order-t.order}));return e.createElement(a.a,{rounded:!0},n.map((function(t){var n=t.slug;return e.createElement(o.a,{key:n,slug:n})})))}}).call(this,n(4))},,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WPConsentAPIRequirement}));var i=n(1),r=n.n(i);function WPConsentAPIRequirement(t){var n=t.title,i=t.description,r=t.footer;return e.createElement("div",{className:"googlesitekit-settings-consent-mode-requirement"},e.createElement("h4",null,n),e.createElement("p",{className:"googlesitekit-settings-consent-mode-requirement__description"},i),e.createElement("footer",{className:"googlesitekit-settings-consent-mode-requirement__footer"},r))}WPConsentAPIRequirement.propTypes={title:r.a.string.isRequired,description:r.a.node.isRequired,footer:r.a.node.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsKeyMetrics}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(1),l=n.n(c),s=n(0),u=n(2),d=n(10),g=n(3),f=n(7),m=n(17),p=n(44);function SettingsKeyMetrics(t){var n=t.loading,i=void 0!==n&&n,a=Object(g.useSelect)((function(e){return e(f.a).isKeyMetricsWidgetHidden()})),c=Object(g.useSelect)((function(e){return e(f.a).getKeyMetrics()})),l=Object(g.useDispatch)(f.a),h=l.setKeyMetricsSetting,b=l.saveKeyMetricsSettings,v=Object(s.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h("isWidgetHidden",!a);case 2:return e.next=4,b({widgetSlugs:void 0});case 4:case"end":return e.stop()}}),e)}))),[a,b,h]);return i?e.createElement(m.e,null,e.createElement(m.k,null,e.createElement(m.a,{size:12,className:"googlesitekit-overflow-hidden"},e.createElement(p.a,{width:"260px",height:"21.3px"})))):void 0!==a&&(null==c?void 0:c.length)?e.createElement(m.e,null,e.createElement(m.k,null,e.createElement(m.a,{size:12},e.createElement(d.Switch,{label:Object(u.__)("Display key metrics in dashboard","google-site-kit"),checked:!a,onClick:v,hideLabel:!1})))):null}SettingsKeyMetrics.propTypes={loading:l.a.bool}}).call(this,n(4))},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";(function(e){var i=n(15),r=n.n(i),a=n(185),o=n(146),c=n(0),l=n(2),s=n(10),u=n(234),d=n(365),g=n(192),f=n(1123),m=n(17),p=n(235),h=n(36),b=n(18);function SettingsApp(){var t=Object(a.h)().pathname.split("/"),n=r()(t,2)[1],i=SettingsApp.basePathToTabIndex[n],v=Object(b.a)(),E=Object(c.useCallback)((function(){Object(h.b)(v,"tab_select",n)}),[n,v]);return e.createElement(c.Fragment,null,e.createElement(u.a,null,e.createElement(p.a,null)),e.createElement("div",{className:"googlesitekit-module-page"},e.createElement(m.e,null,e.createElement(m.k,null,e.createElement(m.a,{size:12},e.createElement(d.a,{title:Object(l.__)("Settings","google-site-kit")})),e.createElement(m.a,{size:12},e.createElement(g.a,{transparent:!0,rounded:!0},e.createElement(s.TabBar,{activeIndex:i,className:"googlesitekit-tab-bar__settings",handleActiveIndexUpdate:E},e.createElement(s.Tab,{tag:o.b,to:"/connected-services",replace:!1},e.createElement("span",{className:"mdc-tab__text-label"},Object(l.__)("Connected Services","google-site-kit"))),e.createElement(s.Tab,{tag:o.b,to:"/connect-more-services",replace:!1},e.createElement("span",{className:"mdc-tab__text-label"},Object(l.__)("Connect More Services","google-site-kit"))),e.createElement(s.Tab,{tag:o.b,to:"/admin-settings",replace:!1},e.createElement("span",{className:"mdc-tab__text-label"},Object(l.__)("Admin Settings","google-site-kit")))))),e.createElement(m.a,{size:12},e.createElement(f.a,null))))))}SettingsApp.propTypes={},SettingsApp.basePathToTabIndex={"connected-services":0,"connect-more-services":1,"admin-settings":2},t.a=Object(a.j)(SettingsApp)}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(185),r=n(3),a=n(19),o=n(1124),c=n(807),l=n(1147);t.a=function SettingsModules(){var t=Object(r.useSelect)((function(e){return e(a.a).getModules()}));return void 0!==t&&Object.values(t).length?e.createElement(i.d,null,e.createElement(i.b,{path:"/connected-services/:moduleSlug/:action"},e.createElement(c.a,null)),e.createElement(i.b,{path:"/connected-services/:moduleSlug"},e.createElement(c.a,null)),e.createElement(i.b,{path:"/connected-services"},e.createElement(c.a,null)),e.createElement(i.b,{path:"/connect-more-services"},e.createElement(l.a,null)),e.createElement(i.b,{path:"/admin-settings"},e.createElement(o.a,null)),e.createElement(i.a,{from:"/settings/:moduleSlug/edit",to:"/connected-services/:moduleSlug/edit"}),e.createElement(i.a,{from:"/settings/:moduleSlug",to:"/connected-services/:moduleSlug"}),e.createElement(i.a,{from:"/settings",to:"/connected-services"}),e.createElement(i.a,{from:"/connect",to:"/connect-more-services"}),e.createElement(i.a,{from:"/admin",to:"/admin-settings"}),e.createElement(i.a,{to:"/connected-services"})):null}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsAdmin}));var i=n(2),r=n(3),a=n(19),o=n(7),c=n(66),l=n(8),s=n(192),u=n(17),d=n(218),g=n(694),f=n(1125),m=n(1129),p=n(1135),h=n(311),b=n(44),v=n(1136);function SettingsAdmin(){var t=Object(r.useSelect)((function(e){return e(o.a).getConfiguredAudiences()})),n=Object(r.useSelect)((function(e){return e(a.a).isModuleConnected("analytics-4")})),E=Object(r.useSelect)((function(e){return e(c.b).isGatheringData()})),_=Object(r.useSelect)((function(e){return!!n&&e(l.r).isGatheringData()})),O=n&&!1===E&&!1===_;return Object(r.useSelect)((function(e){return!e(a.a).hasFinishedResolution("isModuleConnected",["analytics-4"])||!1!==n&&(!e(c.b).hasFinishedResolution("isGatheringData")||!e(l.r).hasFinishedResolution("isGatheringData"))}))?e.createElement(u.k,null,e.createElement(u.a,{size:12},e.createElement(b.a,{width:"100%",smallHeight:"100px",tabletHeight:"100px",desktopHeight:"200px"})),e.createElement(u.a,{size:12},e.createElement(b.a,{width:"100%",smallHeight:"100px",tabletHeight:"100px",desktopHeight:"200px"})),e.createElement(u.a,{size:12},e.createElement(b.a,{width:"100%",smallHeight:"100px",tabletHeight:"100px",desktopHeight:"200px"})),e.createElement(u.a,{size:12},e.createElement(b.a,{width:"100%",smallHeight:"100px",tabletHeight:"100px",desktopHeight:"200px"}))):e.createElement(u.k,null,e.createElement(u.a,{size:12},e.createElement(f.a,null)),O&&e.createElement(u.a,{size:12},e.createElement(m.a,null)),(n||!!t)&&e.createElement(u.a,{size:12},e.createElement(v.a,null)),e.createElement(u.a,{size:12},e.createElement(s.a,{title:Object(i.__)("Plugin Status","google-site-kit"),header:!0,rounded:!0},e.createElement("div",{className:"googlesitekit-settings-module googlesitekit-settings-module--active"},e.createElement(u.e,null,e.createElement(u.k,null,e.createElement(u.a,{size:12},e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("p",{className:"googlesitekit-settings-module__status"},Object(i.__)("Site Kit is connected","google-site-kit"),e.createElement("span",{className:"googlesitekit-settings-module__status-icon googlesitekit-settings-module__status-icon--connected"},e.createElement(h.a,{width:10,height:8}))))))),e.createElement("footer",{className:"googlesitekit-settings-module__footer"},e.createElement(u.e,null,e.createElement(u.k,null,e.createElement(u.a,{size:12},e.createElement(g.a,null)))))))),e.createElement(u.a,{size:12},e.createElement(p.a,null)),e.createElement(u.a,{size:12},e.createElement(s.a,{className:"googlesitekit-settings-meta",title:Object(i.__)("Tracking","google-site-kit"),header:!0,fill:!0,rounded:!0},e.createElement("div",{className:"googlesitekit-settings-module googlesitekit-settings-module--active"},e.createElement(u.e,null,e.createElement(u.k,null,e.createElement(u.a,{size:12},e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item googlesitekit-settings-module__meta-item--nomargin"},e.createElement(d.a,null))))))))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsCardConsentMode}));var i=n(15),r=n.n(i),a=n(11),o=n.n(a),c=n(240),l=n(0),s=n(2),u=n(3),d=n(13),g=n(7),f=n(17),m=n(77),p=n(1126),h=n(1128),b=n(192),v=n(142),E=n(9),_=n(18);function SettingsCardConsentMode(){var t=Object(_.a)(),n=Object(u.useSelect)((function(e){return e(d.c).isAdsConnectedUncached()})),i=Object(u.useSelect)((function(e){return e(d.c).isConsentModeEnabled()})),a=Object(u.useSelect)((function(e){return e(d.c).getConsentAPIInfo()})),O=Object(u.useSelect)((function(e){var t=e(d.c),n=t.isResolving,i=t.hasFinishedResolution;return!i("getConsentModeSettings")||!i("getConsentAPIInfo")||n("getConsentModeSettings")||n("getConsentAPIInfo")})),k=Object(l.useRef)(),y=Object(c.a)(k,{threshold:.25}),j=Object(l.useState)(!1),S=r()(j,2),w=S[0],C=S[1],A=!!(null==y?void 0:y.intersectionRatio),N=Object(u.useSelect)((function(e){return e(d.c).isUsingProxy()})),T=Object(u.useDispatch)(g.a).triggerSurvey;return Object(l.useEffect)((function(){A&&!w&&(Object(E.I)("".concat(t,"_CoMo"),"view_requirements"),n&&!1===i&&N&&T("view_como_setup_cta",{ttl:E.a}),C(!0))}),[A,w,t,N,T,n,i]),e.createElement(b.a,{title:Object(s.__)("Consent Mode","google-site-kit"),badge:n?e.createElement(m.a,{className:"googlesitekit-badge--primary",label:Object(s.__)("Recommended","google-site-kit")}):null,header:!0,rounded:!0},e.createElement("div",{className:"googlesitekit-settings-module googlesitekit-settings-module--active googlesitekit-settings-consent-mode",ref:k},e.createElement(f.e,null,e.createElement(f.k,null,e.createElement(f.a,{size:12,className:o()({"googlesitekit-overflow-hidden":O})},e.createElement(p.a,{loading:O}))),!O&&e.createElement(l.Fragment,null,n&&!i&&e.createElement(f.k,null,e.createElement(f.a,{size:12},e.createElement(v.c,{className:"googlesitekit-settings-consent-mode__recommendation-notice",type:v.a,notice:Object(s.__)("If you have Google Ads campaigns for this site, it’s highly recommended to enable Consent mode - otherwise, you won’t be able to collect any metrics on the effectiveness of your campaigns in regions like the European Economic Area.","google-site-kit")}))),!!a&&i&&e.createElement(f.k,null,e.createElement(f.a,{size:12},e.createElement(h.a,null)))))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConsentModeSwitch}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(0),u=n(38),d=n(2),g=n(10),f=n(3),m=n(13),p=n(7),h=n(54),b=n(20),v=n(183),E=n(1127),_=n(9),O=n(18),k=n(580);function ConsentModeSwitch(t){var n=t.loading,i=Object(O.a)(),a=Object(s.useState)(null),c=l()(a,2),y=c[0],j=c[1],S=Object(s.useState)(!1),w=l()(S,2),C=w[0],A=w[1],N=Object(f.useSelect)((function(e){return e(m.c).isConsentModeEnabled()})),T=Object(f.useSelect)((function(e){return e(m.c).getDocumentationLinkURL("consent-mode")})),x=Object(f.useSelect)((function(e){return e(m.c).isFetchingSaveConsentModeSettings()})),D=Object(f.useDispatch)(m.c),R=D.setConsentModeEnabled,M=D.saveConsentModeSettings,I=Object(f.useSelect)((function(e){return e(m.c).isUsingProxy()})),P=Object(f.useDispatch)(p.a),L=P.dismissPrompt,B=P.triggerSurvey,z=Object(f.useSelect)((function(e){return e(p.a).isPromptDismissed(k.a)}));function F(){return V.apply(this,arguments)}function V(){return(V=o()(r.a.mark((function e(){var t,n,i,a;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return j(null),t=[M()],I&&t.push(B("enable_como",{ttl:_.a})),e.next=5,Promise.all(t);case 5:if(n=e.sent,i=l()(n,1),!(a=i[0].error)){e.next=11;break}return j(a),e.abrupt("return");case 11:if(z){e.next=14;break}return e.next=14,L(k.a);case 14:case"end":return e.stop()}}),e)})))).apply(this,arguments)}return e.createElement(s.Fragment,null,e.createElement("div",null,e.createElement(v.a,{loading:n,width:"180px",height:"21.3px"},e.createElement(g.Switch,{label:Object(d.__)("Enable consent mode","google-site-kit"),checked:N,disabled:n||x,onClick:function(){N?(Object(_.I)("".concat(i,"_CoMo"),"como_disable"),A(!0)):(Object(_.I)("".concat(i,"_CoMo"),"como_enable"),R(!0),F())},hideLabel:!1})),y&&e.createElement(h.a,{message:y.message}),!n&&N&&e.createElement("p",{className:"googlesitekit-settings-consent-mode-switch__enabled-notice"},Object(d.__)("Site Kit added the necessary code to your tag to comply with Consent Mode.","google-site-kit")),e.createElement(v.a,{className:"googlesitekit-settings-consent-mode-switch-description--loading",loading:n,width:"750px",height:"42px",smallWidth:"386px",smallHeight:"84px",tabletWidth:"540px",tabletHeight:"84px"},e.createElement("p",null,Object(u.a)(Object(d.__)("Consent mode will help adjust tracking on your site, so only visitors who have explicitly given consent are tracked. <br />This is required in some parts of the world, like the European Economic Area. <a>Learn more</a>","google-site-kit"),{br:e.createElement("br",null),a:e.createElement(b.a,{href:T,external:!0,"aria-label":Object(d.__)("Learn more about consent mode","google-site-kit"),onClick:o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(_.I)("".concat(i,"_CoMo"),"como_learn_more");case 2:case"end":return e.stop()}}),e)})))})})))),C&&e.createElement(E.a,{onConfirm:function(){Object(_.I)("".concat(i,"_CoMo"),"confirm_disconnect"),R(!1),A(!1),F()},onCancel:function(){Object(_.I)("".concat(i,"_CoMo"),"cancel_disconnect"),A(!1)}}))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConfirmDisableConsentModeDialog}));var i=n(27),r=n.n(i),a=n(1),o=n.n(a),c=n(81),l=n(2),s=n(3),u=n(19),d=n(13),g=n(9),f=n(109),m=n(18);function ConfirmDisableConsentModeDialog(t){var n=t.onConfirm,i=t.onCancel,a=Object(m.a)(),o=Object(s.useSelect)((function(e){return e(d.c).isAdsConnectedUncached()})),p=Object(s.useSelect)((function(e){return["analytics-4","ads"].reduce((function(t,n){return e(u.a).isModuleConnected(n)?[].concat(r()(t),[e(u.a).getModule(n).name]):t}),[])})),h=p.length>0?Object(l.sprintf)(
/* translators: %s: list of dependent modules */
Object(l.__)("these active modules depend on consent mode and will be affected: %s","google-site-kit"),Object(g.y)(p)):null;Object(c.a)((function(){Object(g.I)("".concat(a,"_CoMo"),"view_modal")}));var b=[Object(l.__)("Track how visitors interact with your site","google-site-kit")],v=Object(l.__)("Disabling consent mode may affect your ability in the European Economic Area, the UK and Switzerland to:","google-site-kit");return o&&(b=[Object(l.__)("Performance of your Ad campaigns","google-site-kit"),Object(l.__)("How visitors interact with your site via Analytics","google-site-kit")],v=Object(l.__)("Disabling consent mode may affect your ability to track these in the European Economic Area, the UK and Switzerland:","google-site-kit")),e.createElement(f.a,{className:"googlesitekit-settings-module__confirm-disconnect-modal",dialogActive:!0,title:Object(l.__)("Disable consent mode?","google-site-kit"),subtitle:v,handleConfirm:n,handleDialog:i,provides:b,dependentModules:h,confirmButton:Object(l.__)("Disable","google-site-kit"),danger:!0})}ConfirmDisableConsentModeDialog.propTypes={onConfirm:o.a.func.isRequired,onCancel:o.a.func.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WPConsentAPIRequirements}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(0),l=n(38),s=n(2),u=n(260),d=n(3),g=n(17),f=n(13),m=n(207),p=n(20),h=n(54),b=n(142),v=n(834),E=n(372),_=n(9),O=n(18);function WPConsentAPIRequirements(){var t,n=Object(O.a)(),i=Object(d.useSelect)((function(e){return e(f.c).getDocumentationLinkURL("wp-consent-api")})),a=Object(d.useSelect)((function(e){return e(f.c).getDocumentationLinkURL("consent-management-platform")})),k=Object(d.useSelect)((function(e){return e(f.c).getConsentAPIInfo()})),y=k.hasConsentAPI,j=k.wpConsentPlugin,S=Object(d.useDispatch)(f.c),w=S.installActivateWPConsentAPI,C=S.activateConsentAPI,A=Object(d.useSelect)((function(e){return e(f.c).getErrorForAction("installActivateWPConsentAPI")})),N=Object(d.useSelect)((function(e){return e(f.c).isApiFetching()})),T=Object(d.useSelect)((function(e){return e(f.c).isFetchingActivateConsentAPI()})),x=Object(d.useSelect)((function(e){return e(f.c).getApiInstallResponse()})),D=(A?A.message:null)||(null==x?void 0:x.error),R={smSize:4,mdSize:4,lgSize:6};return Object(c.useEffect)((function(){y&&Object(_.I)("".concat(n,"_CoMo"),"wp_consent_api_active")}),[y,n]),e.createElement(c.Fragment,null,e.createElement("p",{className:"googlesitekit-settings-consent-mode-requirements__description"},Object(s.__)("In order for consent mode to work properly, these requirements must be met:","google-site-kit")),e.createElement(g.e,{className:"googlesitekit-settings-consent-mode-requirements__grid"},e.createElement(g.k,null,e.createElement(g.a,R,e.createElement(v.a,{title:(null==j?void 0:j.installed)?Object(s.__)("Activate WP Consent API","google-site-kit"):Object(s.__)("Install WP Consent API","google-site-kit"),description:Object(l.a)(Object(s.__)("WP Consent API is a plugin that standardizes the communication of accepted consent categories between plugins. <a>Learn more</a>","google-site-kit"),{a:e.createElement(p.a,{href:i,external:!0,"aria-label":Object(s.__)("Learn more about the WP Consent API","google-site-kit"),onClick:o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(_.I)("".concat(n,"_CoMo"),"wp_consent_api_learn_more");case 2:case"end":return e.stop()}}),e)})))})}),footer:e.createElement(c.Fragment,null,y&&e.createElement("div",{className:"googlesitekit-settings-consent-mode-requirement__consent-api-detected-wrapper"},e.createElement("span",{className:"googlesitekit-settings-consent-mode-requirement__consent-api-detected-icon"},e.createElement(E.a,null)),Object(s.__)("Site Kit detected WP Consent API for your site","google-site-kit")),!y&&e.createElement(c.Fragment,null,j.installed&&e.createElement(c.Fragment,null,!!(null==x?void 0:x.error)&&e.createElement(h.a,{message:null==x||null===(t=x.error)||void 0===t?void 0:t.message}),e.createElement(u.b,{className:"googlesitekit-settings-consent-mode-requirement__install-button",isSaving:T,disabled:T,onClick:o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return C(),e.next=3,Object(_.I)("".concat(n,"_CoMo"),"wp_consent_api_activate");case 3:case"end":return e.stop()}}),e)})))},(null==x?void 0:x.error)?Object(s.__)("Retry","google-site-kit"):Object(s.__)("Activate","google-site-kit"))),!j.installed&&e.createElement(c.Fragment,null,D&&e.createElement(h.a,{message:D}),e.createElement(u.b,{className:"googlesitekit-settings-consent-mode-requirement__install-button",isSaving:N,disabled:N,onClick:o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return w(),e.next=3,Object(_.I)("".concat(n,"_CoMo"),"wp_consent_api_install");case 3:case"end":return e.stop()}}),e)})))},D?Object(s.__)("Retry","google-site-kit"):Object(s.__)("Install","google-site-kit")))))})),e.createElement(g.a,R,e.createElement(v.a,{title:Object(s.__)("Install consent management plugin","google-site-kit"),description:Object(l.a)(Object(s.__)("You’ll need a plugin compatible with the WP Consent API to display a notice to site visitors and get their consent for tracking. WordPress offers a variety of consent plugins you can choose from. <a>See suggested plugins</a>","google-site-kit"),{a:e.createElement(p.a,{href:a,external:!0,"aria-label":Object(s.__)("Suggested consent management plugins","google-site-kit"),onClick:o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(_.I)("".concat(n,"_CoMo"),"consent_mgmt_plugin_learn_more");case 2:case"end":return e.stop()}}),e)})))})}),footer:e.createElement(b.c,{className:"googlesitekit-settings-consent-mode-requirement__consent-management-plugin-notice",type:b.a,Icon:m.a,notice:Object(s.__)("Make sure you have installed a plugin compatible with WP Consent API (Site Kit isn't able to verify the compatibility of all WP plugins).","google-site-kit")})})))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsCardKeyMetrics}));var i=n(0),r=n(2),a=n(3),o=n(7),c=n(9),l=n(1130),s=n(835),u=n(1131),d=n(192),g=n(17),f=n(251),m=n(44),p=n(411),h=n(18);function SettingsCardKeyMetrics(){var t=Object(h.a)(),n=Object(p.a)(),b=Object(a.useSelect)((function(e){return e(o.a).isUserInputCompleted()})),v=Object(a.useSelect)((function(e){return e(o.a).getUserInputSettings(),e(o.a).isResolving("getUserInputSettings",[])})),E=Object(a.useSelect)((function(e){return!e(o.a).hasFinishedResolution("isUserInputCompleted")})),_="".concat(t,"_kmw");return Object(i.useEffect)((function(){b&&Object(c.I)(_,"summary_view")}),[b,_]),e.createElement(d.a,{title:Object(r.__)("Key Metrics","google-site-kit"),header:!0,rounded:!0},e.createElement("div",{className:"googlesitekit-settings-module googlesitekit-settings-module--active googlesitekit-settings-user-input"},E&&e.createElement(m.a,{width:"100%",smallHeight:"100px",tabletHeight:"100px",desktopHeight:"117px"}),b&&e.createElement(i.Fragment,null,e.createElement(s.a,{loading:v}),e.createElement(g.e,null,e.createElement(g.k,null,e.createElement(g.a,{size:12},e.createElement(u.a,{settingsView:!0,loading:v}))))),!1===b&&e.createElement(i.Fragment,null,e.createElement(s.a,null),e.createElement(i.Fragment,null,e.createElement(l.a,null),n&&e.createElement(f.a,{triggerID:"view_kmw_setup_cta",ttl:c.f})))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConversionReportingSettingsSubtleNotification}));var i=n(15),r=n.n(i),a=n(240),o=n(2),c=n(0),l=n(3),s=n(10),u=n(131),d=n(115),g=n(13),f=n(9),m=n(18);function ConversionReportingSettingsSubtleNotification(){var t=Object(m.a)(),n=Object(c.useState)(!1),i=r()(n,2),p=i[0],h=i[1],b=Object(c.useState)(!1),v=r()(b,2),E=v[0],_=v[1],O=Object(c.useRef)(),k=Object(a.a)(O,{threshold:.25}),y=!!(null==k?void 0:k.intersectionRatio);Object(c.useEffect)((function(){!E&&y&&(Object(f.I)("".concat(t,"_kmw-settings-change-from-manual-to-tailored"),"view_notification","conversion_reporting"),_(!0))}),[E,y,t]);var j=Object(l.useSelect)((function(e){return e(g.c).getAdminURL("googlesitekit-user-input")})),S=Object(c.useCallback)((function(){h(!0),Object(f.I)("".concat(t,"_kmw-settings-change-from-manual-to-tailored"),"confirm_get_tailored_metrics","conversion_reporting")}),[h,t]);return e.createElement(d.a,{ref:O,className:"googlesitekit-acr-subtle-notification",title:Object(o.__)("Personalize your metrics","google-site-kit"),description:Object(o.__)("Set up your goals by answering 3 quick questions to help us show the most relevant data for your site","google-site-kit"),additionalCTA:e.createElement(s.SpinnerButton,{onClick:S,href:j,isSaving:p},Object(o.__)("Get tailored metrics","google-site-kit")),icon:e.createElement(u.a,{width:24,height:24})})}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return UserInputPreview}));var i=n(27),r=n.n(i),a=n(5),o=n.n(a),c=n(6),l=n.n(c),s=n(16),u=n.n(s),d=n(15),g=n.n(d),f=n(1),m=n.n(f),p=n(11),h=n.n(p),b=n(2),v=n(0),E=n(10),_=n(3),O=n(7),k=n(32),y=n(23),j=n(87),S=n(806),w=n(624),C=n(147),A=n(120),N=n(183),T=n(683),x=n(549),D=n(72),R=n(1133),M=n(29),I=n(8),P=n(1134);function UserInputPreview(t){var n,i,a,c,s=t.goBack,d=t.submitChanges,f=t.error,m=t.loading,p=void 0!==m&&m,L=t.settingsView,B=void 0!==L&&L,z=Object(v.useRef)(),F=Object(v.useState)(!1),V=g()(F,2),W=V[0],H=V[1],U=Object(v.useCallback)((function(){H(!1)}),[H]),q=Object(_.useSelect)((function(e){return e(O.a).getSavedUserInputSettings()})),G=Object(_.useSelect)((function(e){return e(O.a).isSavingUserInputSettings(q)})),K=Object(_.useSelect)((function(e){return e(k.a).isNavigating()})),Y=Object(_.useSelect)((function(e){return!!e(y.b).getValue(j.c)})),X=G||K,$=Object(j.k)(),Z=$.USER_INPUT_ANSWERS_PURPOSE,Q=$.USER_INPUT_ANSWERS_POST_FREQUENCY,J=$.USER_INPUT_ANSWERS_GOALS,ee=Object(C.a)("page"),te=g()(ee,1)[0],ne=j.h.some((function(e){var t;return Object(x.b)((null==q||null===(t=q[e])||void 0===t?void 0:t.values)||[])})),ie=Object(v.useCallback)((function(){ne||X||d()}),[ne,X,d]),re=Object(_.useDispatch)(O.a).saveUserInputSettings,ae=Object(_.useSelect)((function(e){return e(M.a).getValue(j.b,j.i)})),oe=Object(_.useSelect)((function(e){return e(O.a).getSavedUserInputSettings()})),ce=Object(_.useSelect)((function(e){var t,n,i,r;return void 0!==oe&&(null==oe||null===(t=oe.purpose)||void 0===t||null===(n=t.values)||void 0===n?void 0:n.length)?e(O.a).getAnswerBasedMetrics(null==oe||null===(i=oe.purpose)||void 0===i||null===(r=i.values)||void 0===r?void 0:r[0]):[]})),le=Object(_.useSelect)((function(e){return e(I.r).shouldIncludeConversionTailoredMetrics()})),se=Object(_.useSelect)((function(e){return e(O.a).getAnswerBasedMetrics(null,le)})),ue=Object(_.useDispatch)(O.a).resetUserInputSettings,de=Object(_.useDispatch)(M.a).setValues,ge=Object(_.useDispatch)(y.b).setValues,fe=function(){var e=u()(o.a.mark((function e(){return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0===se.filter((function(e){return!ce.includes(e)})).length){e.next=5;break}H(!0),e.next=12;break;case 5:return e.next=7,re();case 7:if(!(null==ae?void 0:ae.length)){e.next=11;break}return e.next=10,ue();case 10:de(j.b,l()({},j.i,void 0));case 11:ge(l()({},j.c,void 0));case 12:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();Object(v.useEffect)((function(){if((null==z?void 0:z.current)&&!(null==te?void 0:te.startsWith("googlesitekit-settings"))){var e=z.current.querySelector(".mdc-button");e&&setTimeout((function(){e.focus()}),50)}}),[te]);var me=Object(_.useDispatch)(O.a).setUserInputSetting,pe=Object(_.useSelect)((function(e){return e(y.b).getValue(j.c)}));return Object(v.useEffect)((function(){var e,t=r()((null==q||null===(e=q.purpose)||void 0===e?void 0:e.values)||[]);j.i===pe&&t.includes("sell_products_or_service")&&(me(j.i,["sell_products"]),de(j.b,l()({},j.i,["sell_products_or_service"])))}),[q,me,pe,de]),e.createElement("div",{className:h()("googlesitekit-user-input__preview",{"googlesitekit-user-input__preview--editing":Y}),ref:z},e.createElement("div",{className:"googlesitekit-user-input__preview-contents"},!B&&e.createElement("p",{className:"googlesitekit-user-input__preview-subheader"},Object(b.__)("Review your answers","google-site-kit")),B&&e.createElement("div",{className:"googlesitekit-settings-user-input__heading-container"},e.createElement(N.a,{loading:p,width:"275px",height:"16px"},e.createElement("p",{className:"googlesitekit-settings-user-input__heading"},Object(b.__)("Edit your answers for more personalized metrics:","google-site-kit")))),e.createElement(S.a,{slug:j.i,title:Object(b.__)("What is the main purpose of this site?","google-site-kit"),subtitle:(null==q||null===(n=q.purpose)||void 0===n?void 0:n.values.includes("sell_products_or_service"))?P.a:null,values:(null==q||null===(i=q.purpose)||void 0===i?void 0:i.values)||[],options:Z,loading:p,settingsView:B,onChange:fe}),e.createElement(S.a,{slug:j.j,title:Object(b.__)("How often do you create new content for this site?","google-site-kit"),values:(null==q||null===(a=q.postFrequency)||void 0===a?void 0:a.values)||[],options:Q,loading:p,settingsView:B}),e.createElement(S.a,{slug:j.g,title:Object(b.__)("What are your top 3 goals for this site?","google-site-kit"),values:(null==q||null===(c=q.goals)||void 0===c?void 0:c.values)||[],options:J,loading:p,settingsView:B}),f&&e.createElement(A.a,{error:f})),!B&&e.createElement(v.Fragment,null,e.createElement("div",{className:"googlesitekit-user-input__preview-notice"},e.createElement(w.a,null)),e.createElement("div",{className:"googlesitekit-user-input__footer googlesitekit-user-input__buttons"},e.createElement("div",{className:"googlesitekit-user-input__footer-nav"},e.createElement(E.SpinnerButton,{className:"googlesitekit-user-input__buttons--next",onClick:ie,disabled:ne||X,isSaving:X},Object(b.__)("Save","google-site-kit")),e.createElement(E.Button,{tertiary:!0,className:"googlesitekit-user-input__buttons--back",onClick:s,disabled:X},Object(b.__)("Back","google-site-kit"))),e.createElement("div",{className:"googlesitekit-user-input__footer-cancel"},e.createElement(T.a,{disabled:X})))),e.createElement(D.a,null,e.createElement(R.a,{dialogActive:W,handleDialog:U})))}UserInputPreview.propTypes={submitChanges:m.a.func,goBack:m.a.func,error:m.a.object,loading:m.a.bool,settingsView:m.a.bool}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{stroke:"currentColor",strokeLinecap:"square",strokeWidth:2,d:"M15.835 8.333l-5.834 5-5.833-5"});t.a=function SvgChevronDown(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e){var i=n(5),r=n.n(i),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(15),u=n.n(s),d=n(1),g=n.n(d),f=n(2),m=n(0),p=n(422),h=n(10),b=n(3),v=n(7),E=n(29),_=n(145),O=n(87),k=n(23),y=n(8),j=n(19),S=n(9),w=n(18);function ConfirmSitePurposeChangeModal(t){var n=t.dialogActive,i=void 0!==n&&n,a=t.handleDialog,c=void 0===a?null:a,s=Object(w.a)(),d=Object(m.useState)(!1),g=u()(d,2),C=g[0],A=g[1],N=Object(m.useState)(null),T=u()(N,2),x=T[0],D=T[1],R=Object(b.useSelect)((function(e){return e(y.r).shouldIncludeConversionTailoredMetrics()})),M=Object(b.useSelect)((function(e){return e(v.a).getAnswerBasedMetrics(null,R)})),I=Object(b.useSelect)((function(e){return e(E.a).getValue(O.b,O.i)})),P=Object(b.useSelect)((function(e){return e(v.a).getSavedUserInputSettings()})),L=Object(b.useSelect)((function(e){var t,n,i,r,a,o;return(null==P||null===(t=P.purpose)||void 0===t||null===(n=t.values)||void 0===n?void 0:n.length)?"other"===(null==P||null===(i=P.purpose)||void 0===i||null===(r=i.values)||void 0===r?void 0:r[0])?e(v.a).getKeyMetrics():e(v.a).getAnswerBasedMetrics(null==P||null===(a=P.purpose)||void 0===a||null===(o=a.values)||void 0===o?void 0:o[0]):[]})),B=Object(b.useDispatch)(E.a).setValues,z=Object(b.useDispatch)(k.b).setValues,F=Object(b.useDispatch)(v.a).resetUserInputSettings,V=Object(m.useCallback)(l()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(null==I?void 0:I.length)){e.next=4;break}return e.next=3,F();case 3:B(O.b,o()({},O.i,void 0));case 4:z(o()({},O.c,void 0)),c(),A(!1);case 7:case"end":return e.stop()}}),e)}))),[c,I,F,B,z]),W=Object(b.useSelect)((function(e){return e(j.a).isModuleConnected("analytics-4")?e(y.r).getUserInputPurposeConversionEvents():[]})),H=Object(b.useDispatch)(v.a),U=H.setUserInputSetting,q=H.saveUserInputSettings,G=Object(m.useCallback)(l()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return A(!0),U("includeConversionEvents",W),e.next=4,q();case 4:V();case 5:case"end":return e.stop()}}),e)}))),[q,V,A,U,W]),K=Object(p.a)(i);Object(m.useEffect)((function(){!0===K&&!1===i&&(C?Object(S.I)("".concat(s,"_kmw-settings-tailored-metrics-suggestions"),"confirm_update_metrics_selection","conversion_reporting"):Object(S.I)("".concat(s,"_kmw-settings-tailored-metrics-suggestions"),"cancel_update_metrics_selection","conversion_reporting"))}),[K,i,C,s]),Object(m.useEffect)((function(){var e,t;(null==P||null===(e=P.purpose)||void 0===e||null===(t=e.values)||void 0===t?void 0:t[0])&&null===x&&void 0!==L&&D(L)}),[P,x,L,D]);var Y=Object(p.a)(C);return Object(m.useEffect)((function(){!Y||C||i||null===x||setTimeout((function(){D(null)}),50)}),[Y,C,i,x,D]),e.createElement(h.Dialog,{open:i,"aria-describedby":void 0,tabIndex:"-1",className:"googlesitekit-dialog-confirm-site-purpose-change",onClose:V},e.createElement(h.DialogTitle,null,Object(f.__)("Tailored metrics suggestions","google-site-kit")),e.createElement("p",null,Object(f.__)("You have changed your website purpose. We can suggest new tailored metrics for you based on your answers or you can keep your current metrics selection on your dashboard.","google-site-kit"),e.createElement("br",null),Object(f.__)("You can always edit your metrics selection from the dashboard.","google-site-kit")),e.createElement(h.DialogContent,null,e.createElement("div",{className:"mdc-layout-grid__inner"},e.createElement("div",{className:"mdc-layout-grid__cell mdc-layout-grid__cell--span-6-desktop mdc-layout-grid__cell--span-4-tablet mdc-layout-grid__cell--span-4-phone"},e.createElement("h3",null,Object(f.__)("Current metrics","google-site-kit")),!!x&&e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},x.map((function(t){var n;return e.createElement("li",{key:t,className:"mdc-list-item"},e.createElement("span",{className:"mdc-list-item__text"},null===(n=_.a[t])||void 0===n?void 0:n.title))})))),e.createElement("div",{className:"mdc-layout-grid__cell mdc-layout-grid__cell--span-6-desktop mdc-layout-grid__cell--span-4-tablet mdc-layout-grid__cell--span-4-phone"},e.createElement("h3",null,Object(f.__)("New tailored metrics","google-site-kit")),!!M&&e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},M.map((function(t){var n;return e.createElement("li",{key:t,className:"mdc-list-item"},e.createElement("span",{className:"mdc-list-item__text"},null===(n=_.a[t])||void 0===n?void 0:n.title))})))))),e.createElement(h.DialogFooter,null,e.createElement(h.Button,{className:"mdc-dialog__cancel-button",tertiary:!0,onClick:V},Object(f.__)("Keep current selection","google-site-kit")),e.createElement(h.SpinnerButton,{isSaving:C,onClick:G},Object(f.__)("Update metrics selection","google-site-kit"))))}ConfirmSitePurposeChangeModal.propTypes={dialogActive:g.a.bool,handleDialog:g.a.func},t.a=ConfirmSitePurposeChangeModal}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsSettingsSellProductsSubtleNotification}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(240),u=n(2),d=n(0),g=n(18),f=n(3),m=n(10),p=n(115),h=n(7),b=n(87),v=n(9),E=n(58);function KeyMetricsSettingsSellProductsSubtleNotification(){var t=Object(g.a)(),n=Object(f.useDispatch)(h.a).dismissItem,i=Object(d.useState)(!1),a=l()(i,2),c=a[0],_=a[1],O=Object(d.useRef)(),k=Object(s.a)(O,{threshold:.25}),y=!!(null==k?void 0:k.intersectionRatio);Object(d.useEffect)((function(){!c&&y&&(Object(v.I)("".concat(t,"_kmw-settings-suggested-site-purpose-edit-notification"),"view_notification","conversion_reporting"),_(!0))}),[c,y,t]);var j=Object(f.useSelect)((function(e){return e(h.a).isItemDismissed(b.d)})),S=Object(d.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n(b.d);case 2:Object(v.I)("".concat(t,"_kmw-settings-suggested-site-purpose-edit-notification"),"confirm_notification","conversion_reporting");case 3:case"end":return e.stop()}}),e)}))),[n,t]);return j?null:e.createElement(p.a,{ref:O,className:"googlesitekit-subtle-notification--warning",description:Object(u.__)("To allow better personalization of suggested metrics, we have updated the answers list for this question with more accurate options. We recommend that you edit your answer.","google-site-kit"),dismissCTA:e.createElement(m.Button,{tertiary:!0,onClick:S},Object(u.__)("Got it","google-site-kit")),icon:e.createElement(E.a,{width:24,height:24})})}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsPlugin}));var i=n(2),r=n(0),a=n(3),o=n(10),c=n(13),l=n(17),s=n(192),u=n(9),d=n(18);function SettingsPlugin(){var t=Object(a.useSelect)((function(e){return e(c.c).getShowAdminBar()})),n=Object(a.useDispatch)(c.c).setShowAdminBar,g=Object(d.a)(),f=Object(r.useCallback)((function(e){var t=e.target,i=t.checked?"enable_admin_bar_menu":"disable_admin_bar_menu";n(!!t.checked),Object(u.I)(g,i)}),[n,g]);return e.createElement(s.a,{className:"googlesitekit-settings-meta",title:Object(i.__)("Plugin Settings","google-site-kit"),header:!0,fill:!0,rounded:!0},e.createElement("div",{className:"googlesitekit-settings-module googlesitekit-settings-module--active"},e.createElement(l.e,null,e.createElement(l.k,null,e.createElement(l.a,{size:12},e.createElement("div",{className:"googlesitekit-settings-module__meta-items"},e.createElement("div",{className:"googlesitekit-settings-module__meta-item googlesitekit-settings-module__meta-item--nomargin"},e.createElement(o.Checkbox,{id:"admin-bar-toggle",name:"admin-bar-toggle",value:"1",checked:t,onChange:f,disabled:void 0===t,loading:void 0===t},e.createElement("span",null,Object(i.__)("Display relevant page stats in the Admin bar","google-site-kit"))))))))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsCardVisitorGroups}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(0),l=n(2),s=n(3),u=n(10),d=n(7),g=n(8),f=n(17),m=n(18),p=n(9),h=n(192),b=n(1137),v=n(412);function SettingsCardVisitorGroups(){var t=Object(m.a)(),n=Object(s.useSelect)((function(e){return e(d.a).isAudienceSegmentationWidgetHidden()})),i=Object(s.useSelect)((function(e){return e(d.a).getConfiguredAudiences()})),a=Object(s.useSelect)((function(e){return e(g.r).getAudienceSegmentationSetupCompletedBy()})),E=Object(s.useDispatch)(d.a),_=E.setAudienceSegmentationWidgetHidden,O=E.saveUserAudienceSettings,k=Object(c.useCallback)((function(){var e=n?"audience_widgets_enable":"audience_widgets_disable";Object(p.I)("".concat(t,"_audiences-settings"),e).finally(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,_(!n);case 2:return e.next=4,O();case 4:case"end":return e.stop()}}),e)}))))}),[n,O,_,t]);if(void 0===i||void 0===a)return null;var y=!i&&null===a;return e.createElement(h.a,{className:"googlesitekit-settings-meta",title:Object(l.__)("Visitor groups","google-site-kit"),header:!0,fill:!0,rounded:!0},e.createElement("div",{className:"googlesitekit-settings-module googlesitekit-settings-module--active"},e.createElement(f.e,null,e.createElement(f.k,null,e.createElement(f.a,{size:12},y&&e.createElement(b.a,null),!y&&e.createElement(c.Fragment,null,e.createElement(v.b,null),e.createElement(u.Switch,{label:Object(l.__)("Display visitor groups in dashboard","google-site-kit"),checked:!n,onClick:k,hideLabel:!1})))))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return SetupCTA}));var r=n(15),a=n.n(r),o=n(0),c=n(2),l=n(3),s=n(10),u=n(29),d=n(13),g=n(7),f=n(41),m=n(8),p=n(622),h=n(20),b=n(384),v=n(413),E=n(18),_=n(9);function SetupCTA(){var t=Object(E.a)(),n=Object(o.useState)(!1),r=a()(n,2),O=r[0],k=r[1],y=Object(l.useDispatch)(f.a).dismissNotification,j=Object(o.useCallback)((function(){y(p.a)}),[y]),S=Object(o.useCallback)((function(){k(!0)}),[k]),w=Object(v.a)({redirectURL:e.location.href,onSuccess:j,onError:S}),C=w.apiErrors,A=w.failedAudiences,N=w.isSaving,T=w.onEnableGroups,x=Object(l.useSelect)((function(e){return e(d.c).getSetupErrorCode()})),D=Object(l.useSelect)((function(e){return e(u.a).getValue(m.c,"autoSubmit")}))&&"access_denied"===x,R=Object(l.useDispatch)(u.a).setValues,M=Object(l.useDispatch)(d.c).setSetupErrorCode,I=Object(l.useDispatch)(g.a).clearPermissionScopeError;return i.createElement("div",{className:"googlesitekit-settings-visitor-groups__setup"},i.createElement("p",null,Object(c.__)("To set up new visitor groups for your site, Site Kit needs to update your Google Analytics property.","google-site-kit")),N&&i.createElement("div",{className:"googlesitekit-settings-visitor-groups__setup-progress"},i.createElement("p",null,Object(c.__)("Enabling groups","google-site-kit")),i.createElement(s.ProgressBar,{compress:!0})),!N&&i.createElement(h.a,{onClick:function(){Object(_.I)("".concat(t,"_audiences-setup-cta-settings"),"enable_groups").finally(T)}},Object(c.__)("Enable groups","google-site-kit")),(O||D)&&i.createElement(b.b,{hasOAuthError:D,apiErrors:C.length?C:A,onRetry:T,inProgress:N,onCancel:D?function(){R(m.c,{autoSubmit:!1}),I(),M(null),k(!1)}:function(){return k(!1)}}))}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsActiveModule}));var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(185),l=n(3),s=n(19),u=n(23),d=n(17),g=n(1139),f=n(1141),m=n(1142),p=n(1143),h=n(1146);function SettingsActiveModule(t){var n=t.slug,i=Object(c.i)(),r=i.action,a=i.moduleSlug,b="edit"===r,v=a===n,E=a!==n&&b,_="module-".concat(n,"-error"),O=Object(l.useSelect)((function(e){return e(s.a).getErrorForAction("deactivateModule",[n])})),k=Object(l.useSelect)((function(e){return e(u.b).getValue(_)}));return e.createElement("div",{className:o()("googlesitekit-settings-module","googlesitekit-settings-module--active","googlesitekit-settings-module--".concat(n),{"googlesitekit-settings-module--error":(k||O)&&b})},E&&e.createElement(g.a,{compress:!v}),e.createElement(m.a,{slug:n}),v&&e.createElement("div",{id:"googlesitekit-settings-module__content--".concat(n),className:"googlesitekit-settings-module__content googlesitekit-settings-module__content--open",role:"tabpanel","aria-labelledby":"googlesitekit-settings-module__header--".concat(n)},e.createElement(d.e,null,e.createElement(d.k,null,e.createElement(d.a,{size:12},e.createElement(f.a,{slug:n})))),e.createElement(p.a,{slug:n})),e.createElement(h.a,{slug:n}))}SettingsActiveModule.propTypes={slug:r.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(2),l=n(1140);function SettingsOverlay(t){var n=t.compress;return e.createElement("div",{className:o()("googlesitekit-overlay",{"googlesitekit-overlay--compress":n})},e.createElement("div",{className:"googlesitekit-overlay__wrapper"},e.createElement("div",{className:"googlesitekit-overlay__icon"},e.createElement(l.a,{width:"22",height:"30"})),e.createElement("h3",{className:" googlesitekit-heading-2 googlesitekit-overlay__title "},Object(c.__)("Section locked while editing","google-site-kit"))))}SettingsOverlay.propTypes={compress:r.a.bool},SettingsOverlay.defaultProps={compress:!1},t.a=SettingsOverlay}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M20 10h-1.429V7.143C18.571 3.2 15.371 0 11.43 0a7.145 7.145 0 00-7.143 7.143V10H2.857A2.866 2.866 0 000 12.857v14.286A2.866 2.866 0 002.857 30H20a2.866 2.866 0 002.857-2.857V12.857A2.866 2.866 0 0020 10zm-8.571 12.857A2.866 2.866 0 018.57 20a2.866 2.866 0 012.858-2.857A2.866 2.866 0 0114.286 20a2.866 2.866 0 01-2.857 2.857zM15.857 10H7V7.143a4.432 4.432 0 014.429-4.429 4.432 4.432 0 014.428 4.429V10z",fill:"currentColor"});t.a=function SvgLock(e){return i.createElement("svg",r({viewBox:"0 0 23 30",fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsRenderer}));var i=n(6),r=n.n(i),a=n(15),o=n.n(a),c=n(0),l=n(185),s=n(3),u=n(19);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function SettingsRenderer(t){var n=t.slug,i=Object(l.i)(),r=i.action,a=i.moduleSlug,d="edit"===r,f=a===n,m=Object(c.useState)(),p=o()(m,2),h=p[0],b=p[1],v=Object(s.useSelect)((function(e){return e(u.a).isDoingSubmitChanges(n)})),E=Object(s.useSelect)((function(e){var t=e(u.a).getModule(n);return g(g({},t),{},{moduleLoaded:!!t})})),_=E.SettingsEditComponent,O=E.SettingsViewComponent,k=E.SettingsSetupIncompleteComponent,y=E.moduleLoaded,j=E.connected;Object(c.useEffect)((function(){y&&void 0===h&&b(j)}),[y,h,j]);var S=Object(s.useDispatch)(u.a).rollbackChanges;return Object(c.useEffect)((function(){v||d||S(n)}),[n,S,v,d]),f&&y?f&&!1===h?e.createElement(k,{slug:n}):d&&_?e.createElement(_,null):O?e.createElement(O,null):null:null}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Header}));var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(185),l=n(0),s=n(56),u=n(2),d=n(3),g=n(10),f=n(19),m=n(588),p=n(17),h=n(112),b=n(198),v=n(77),E=n(302),_=n(9),O=n(18),k=n(311),y=n(364),j=n(680),S=n(76);function Header(t){var n=t.slug,i=Object(O.a)(),r=Object(c.g)(),a=Object(l.useRef)(),w=Object(c.i)().moduleSlug===n,C=Object(d.useSelect)((function(e){return e(f.a).getModuleStoreName(n)})),A=Object(d.useSelect)((function(e){var t,n;return null===(t=e(C))||void 0===t||null===(n=t.getAdminReauthURL)||void 0===n?void 0:n.call(t)})),N=Object(d.useSelect)((function(e){return e(f.a).getModule(n)})),T=Object(d.useSelect)((function(e){var t;return null===(t=e(f.a))||void 0===t?void 0:t.getCheckRequirementsError(n)})),x=Object(l.useCallback)((function(){w||(r.push("/connected-services/".concat(n)),Object(_.I)("".concat(i,"_module-list"),"view_module_settings",n))}),[r,n,i,w]),D=Object(l.useCallback)((function(){w&&(r.push("/connected-services"),Object(_.I)("".concat(i,"_module-list"),"close_module_settings",n))}),[r,n,i,w]),R=Object(l.useCallback)((function(e){return e.stopPropagation()}),[]);Object(h.a)([s.b],a,w?D:x),Object(h.a)([s.c],a,D);var M=N.name,I=N.connected;if(!N)return null;var P=null;return P=I?e.createElement("p",null,Object(u.__)("Connected","google-site-kit")):e.createElement(g.Button,{href:A,onClick:R,disabled:!!T,inverse:!0},Object(u.sprintf)(
/* translators: %s: module name. */
Object(u.__)("Complete setup for %s","google-site-kit"),M)),e.createElement("div",{className:o()("googlesitekit-settings-module__header",{"googlesitekit-settings-module__header--open":w}),id:"googlesitekit-settings-module__header--".concat(n),type:"button",role:"tab","aria-selected":w,"aria-expanded":w,"aria-controls":"googlesitekit-settings-module__content--".concat(n),to:"/connected-services".concat(w?"":"/".concat(n)),onClick:w?D:x,ref:a,tabIndex:"0"},e.createElement(p.e,null,e.createElement(p.k,null,e.createElement(p.a,{lgSize:6,mdSize:4,smSize:4,className:"googlesitekit-settings-module__heading"},e.createElement(b.a,{slug:n,size:40,className:"googlesitekit-settings-module__heading-icon"}),e.createElement("h3",{className:"googlesitekit-heading-4 googlesitekit-settings-module__title"},M),e.createElement("div",{className:"googlesitekit-settings-module__heading-badges"},m.b.includes(n)&&e.createElement(v.a,{label:Object(u.__)("Experimental","google-site-kit"),hasLeftSpacing:!0}),m.a.includes(n)&&e.createElement(v.a,{className:"googlesitekit-badge--beta",label:Object(u.__)("Beta","google-site-kit"),hasLeftSpacing:!0}),m.c.includes(n)&&e.createElement(E.a,{hasLeftSpacing:!0}))),e.createElement(p.a,{lgSize:6,mdSize:4,smSize:4,alignMiddle:!0,mdAlignRight:!0},e.createElement("div",{className:o()("googlesitekit-settings-module__status",{"googlesitekit-settings-module__status--connected":I,"googlesitekit-settings-module__status--not-connected":!I})},P,e.createElement("span",{className:o()("googlesitekit-settings-module__status-icon",{"googlesitekit-settings-module__status-icon--connected":I,"googlesitekit-settings-module__status-icon--not-connected":!I})},I?e.createElement(k.a,{width:10,height:8}):e.createElement(y.a,{width:19,height:17})))))),e.createElement(S.a,null,e.createElement(j.a,{width:12,height:8,className:"icon-chevron-down"})))}Header.propTypes={slug:r.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Footer}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(1),l=n.n(c),s=n(185),u=n(2),d=n(0),g=n(10),f=n(3),m=n(19),p=n(17),h=n(1144),b=n(1145),v=n(20),E=n(9),_=n(37),O=n(23),k=n(18);function Footer(t){var n=t.slug,i=Object(k.a)(),a=Object(s.g)(),c=Object(s.i)(),l=c.action,y=c.moduleSlug,j="edit"===l&&y===n,S="module-".concat(n,"-error"),w="module-".concat(n,"-dialogActive"),C="module-".concat(n,"-isSaving"),A=Object(f.useSelect)((function(e){return e(m.a).areSettingsEditDependenciesLoaded(n)})),N=Object(f.useSelect)((function(e){return e(m.a).canSubmitChanges(n)})),T=Object(f.useSelect)((function(e){return e(m.a).haveSettingsChanged(n)})),x=Object(f.useSelect)((function(e){return e(m.a).getModule(n)})),D=Object(f.useSelect)((function(e){return e(m.a).isModuleConnected(n)})),R=Object(f.useSelect)((function(e){return e(O.b).getValue(w)})),M=Object(f.useSelect)((function(e){return e(O.b).getValue(C)})),I=Object(f.useSelect)((function(e){return e(m.a).getDetailsLinkURL(n)})),P=Object(f.useDispatch)(m.a).submitChanges,L=(Object(f.useDispatch)(null==x?void 0:x.storeName)||{}).clearErrors,B=Object(f.useDispatch)(O.b).setValue,z=!!(null==x?void 0:x.SettingsEditComponent),F=Object(d.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(E.I)("".concat(i,"_module-list"),"cancel_module_settings",n);case 2:return e.next=4,null==L?void 0:L();case 4:a.push("/connected-services/".concat(n));case 5:case"end":return e.stop()}}),e)}))),[L,a,i,n]),V=Object(d.useCallback)(function(){var e=o()(r.a.mark((function e(t){var o,c;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.preventDefault(),B(C,!0),e.next=4,P(n);case 4:if(o=e.sent,c=o.error,B(C,!1),!c){e.next=11;break}B(S,c),e.next=19;break;case 11:return e.next=13,Object(E.I)("".concat(i,"_module-list"),"update_module_settings",n);case 13:return B(S,void 0),e.next=16,null==L?void 0:L();case 16:return a.push("/connected-services/".concat(n)),e.next=19,Object(_.b)();case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[B,C,P,n,S,L,a,i]),W=Object(d.useCallback)((function(){B(w,!R)}),[R,w,B]),H=Object(d.useCallback)((function(){Object(E.I)("".concat(i,"_module-list"),"edit_module_settings",n)}),[n,i]),U=Object(u.__)("Save","google-site-kit");if(T&&(U=Object(u.__)("Confirm changes","google-site-kit")),M&&(U=Object(u.__)("Saving…","google-site-kit")),!x)return null;var q=x.name,G=x.forceActive,K=null,Y=null;return j||M?K=e.createElement(d.Fragment,null,z&&D?e.createElement(g.SpinnerButton,{disabled:M||!A||!N&&T,onClick:V,isSaving:M},U):e.createElement(g.Button,{onClick:F},Object(u.__)("Close","google-site-kit")),z&&e.createElement(g.Button,{tertiary:!0,className:"googlesitekit-settings-module__footer-cancel",onClick:F},Object(u.__)("Cancel","google-site-kit"))):!z&&G||(K=e.createElement(v.a,{className:"googlesitekit-settings-module__edit-button",to:"/connected-services/".concat(n,"/edit"),onClick:H,"aria-label":Object(u.sprintf)(
/* translators: %s: module name */
Object(u.__)("Edit %s settings","google-site-kit"),q),trailingIcon:e.createElement(h.a,{className:"googlesitekit-settings-module__edit-button-icon",width:10,height:10})},Object(u.__)("Edit","google-site-kit"))),j&&!G?Y=e.createElement(v.a,{className:"googlesitekit-settings-module__remove-button",onClick:W,danger:!0,trailingIcon:e.createElement(b.a,{className:"googlesitekit-settings-module__remove-button-icon",width:13,height:13})},Object(u.sprintf)(
/* translators: %s: module name */
Object(u.__)("Disconnect %s from Site Kit","google-site-kit"),q)):!j&&I&&(Y=e.createElement(v.a,{href:I,className:"googlesitekit-settings-module__cta-button",external:!0},Object(u.sprintf)(
/* translators: %s: module name */
Object(u.__)("See full details in %s","google-site-kit"),q))),e.createElement("footer",{className:"googlesitekit-settings-module__footer"},e.createElement(p.e,null,e.createElement(p.k,null,e.createElement(p.a,{lgSize:6,mdSize:8,smSize:4},K),e.createElement(p.a,{lgSize:6,mdSize:8,smSize:4,alignMiddle:!0,lgAlignRight:!0},Y))))}Footer.propTypes={slug:l.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M0 7.917V10h2.083l6.144-6.144-2.083-2.083L0 7.917zm9.838-5.671a.553.553 0 000-.784l-1.3-1.3a.553.553 0 00-.784 0L6.738 1.18 8.82 3.262l1.017-1.016z",fill:"currentColor"});t.a=function SvgPencil(e){return i.createElement("svg",r({viewBox:"0 0 10 10",fill:"none"},e),a)}},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M.722 11.556c0 .794.65 1.444 1.445 1.444h5.777c.795 0 1.445-.65 1.445-1.444V2.889H.722v8.667zM10.112.722H7.582L6.861 0H3.25l-.722.722H0v1.445h10.111V.722z",fill:"currentColor"});t.a=function SvgTrash(e){return i.createElement("svg",r({viewBox:"0 0 11 13",fill:"none"},e),a)}},function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return ConfirmDisconnect}));var r=n(5),a=n.n(r),o=n(16),c=n.n(o),l=n(15),s=n.n(l),u=n(1),d=n.n(u),g=n(2),f=n(0),m=n(56),p=n(3),h=n(109),b=n(32),v=n(19),E=n(13),_=n(23),O=n(37),k=n(9),y=n(18);function ConfirmDisconnect(t){var n=t.slug,r=Object(y.a)(),o=Object(f.useState)(!1),l=s()(o,2),u=l[0],d=l[1],j=Object(p.useDispatch)(_.b).setValue,S="module-".concat(n,"-dialogActive"),w=Object(p.useSelect)((function(e){return e(v.a).getModuleDependantNames(n)})),C=Object(p.useSelect)((function(e){return e(v.a).getModuleFeatures(n)})),A=Object(p.useSelect)((function(e){return e(v.a).getModule(n)})),N=Object(p.useSelect)((function(e){return e(E.c).getAdminURL("googlesitekit-settings")})),T=Object(p.useSelect)((function(e){return e(_.b).getValue(S)})),x=Object(f.useCallback)((function(){j(S,!T)}),[T,S,j]);Object(f.useEffect)((function(){var t=function(e){m.c===e.keyCode&&T&&x()};return e.addEventListener("keydown",t),function(){e.removeEventListener("keydown",t)}}),[T,x]);var D=Object(p.useDispatch)(v.a).deactivateModule,R=Object(p.useDispatch)(b.a).navigateTo,M=Object(f.useCallback)(c()(a.a.mark((function e(){var t;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!A.forceActive){e.next=2;break}return e.abrupt("return");case 2:return d(!0),e.next=5,D(n);case 5:if(t=e.sent,t.error){e.next=15;break}return e.next=10,Object(O.b)();case 10:return e.next=12,Object(k.I)("".concat(r,"_module-list"),"deactivate_module",n);case 12:R(N),e.next=16;break;case 15:d(!1);case 16:case"end":return e.stop()}}),e)}))),[n,null==A?void 0:A.forceActive,N,D,R,r]);if(!A||!T)return null;var I=A.name,P=Object(g.sprintf)(
/* translators: %s: module name */
Object(g.__)("Disconnect %s from Site Kit?","google-site-kit"),I),L=null;return w.length>0&&(L=Object(g.sprintf)(
/* translators: 1: module name, 2: list of dependent modules */
Object(g.__)("these active modules depend on %1$s and will also be disconnected: %2$s","google-site-kit"),I,Object(k.y)(w))),i.createElement(h.a,{className:"googlesitekit-settings-module__confirm-disconnect-modal",dialogActive:!0,handleDialog:x,title:P,provides:C,handleConfirm:M,dependentModules:L,inProgress:u,danger:!0})}ConfirmDisconnect.propTypes={slug:d.a.string.isRequired}}).call(this,n(28),n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SettingsInactiveModules}));var i=n(2),r=n(19),a=n(3),o=n(192),c=n(197),l=n(1148),s=n(17),u=n(1149);function SettingsInactiveModules(){var t=Object(a.useSelect)((function(e){return e(r.a).getModules()}));if(!t)return null;var n=Object.keys(t).map((function(e){return t[e]})).filter((function(e){var t=e.internal,n=e.active;return!t&&!n})).sort((function(e,t){return e.order-t.order}));return 0===n.length?e.createElement(c.a,{id:"no-more-modules",title:Object(i.__)("Congrats, you’ve connected all services!","google-site-kit"),description:Object(i.__)("We’re working on adding new services to Site Kit by Google all the time, so please check back in the future","google-site-kit"),format:"small",SmallImageSVG:u.a,type:"win-success",rounded:!0}):e.createElement(o.a,{header:!0,title:Object(i.__)("Connect More Services to Gain More Insights","google-site-kit"),rounded:!0,relative:!0},e.createElement(s.e,null,e.createElement(s.k,null,n.map((function(t){var n=t.slug,i=t.name,r=t.description;return e.createElement(s.a,{key:n,size:4},e.createElement(l.a,{slug:n,name:i,description:r}))})))))}}).call(this,n(4))},function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupModule}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(1),u=n.n(s),d=n(11),g=n.n(d),f=n(2),m=n(0),p=n(3),h=n(198),b=n(510),v=n(20),E=n(77),_=n(562),O=n(302),k=n(13),y=n(19),j=n(32),S=n(588),w=n(37),C=n(9),A=n(18);function SetupModule(t){var n=t.slug,i=t.name,a=t.description,c=Object(A.a)(),s=Object(m.useState)(!1),u=l()(s,2),d=u[0],N=u[1],T=Object(p.useDispatch)(y.a).activateModule,x=Object(p.useDispatch)(j.a).navigateTo,D=Object(p.useDispatch)(k.c).setInternalServerError,R=Object(m.useCallback)(o()(r.a.mark((function e(){var t,i,a;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return N(!0),e.next=3,T(n);case 3:if(t=e.sent,i=t.error,a=t.response,i){e.next=14;break}return e.next=9,Object(C.I)("".concat(c,"_module-list"),"activate_module",n);case 9:return e.next=11,Object(w.f)("module_setup",n,{ttl:300});case 11:x(a.moduleReauthURL),e.next=16;break;case 14:D({id:"activate-module-error",description:i.message}),N(!1);case 16:case"end":return e.stop()}}),e)}))),[T,x,D,n,c]),M=Object(p.useSelect)((function(e){return e(y.a).canActivateModule(n)}));return e.createElement("div",{className:g()("googlesitekit-settings-connect-module","googlesitekit-settings-connect-module--".concat(n)),key:n},e.createElement("div",{className:"googlesitekit-settings-connect-module__switch"},e.createElement(b.a,{isSaving:d})),e.createElement("div",{className:"googlesitekit-settings-connect-module__logo"},e.createElement(h.a,{slug:n})),e.createElement("div",{className:"googlesitekit-settings-connect-module__heading"},e.createElement("h3",{className:" googlesitekit-subheading-1 googlesitekit-settings-connect-module__title "},i),e.createElement("div",{className:"googlesitekit-settings-connect-module__badges"},S.b.includes(n)&&e.createElement(E.a,{label:Object(f.__)("Experimental","google-site-kit")}),S.a.includes(n)&&e.createElement(E.a,{className:"googlesitekit-badge--beta",label:Object(f.__)("Beta","google-site-kit")}),S.c.includes(n)&&e.createElement(O.a,{hasNoSpacing:!0}))),e.createElement("p",{className:"googlesitekit-settings-connect-module__text"},a),e.createElement("p",{className:"googlesitekit-settings-connect-module__cta"},e.createElement(v.a,{onClick:R,href:"",disabled:!M,arrow:!0},Object(f.sprintf)(
/* translators: %s: module name */
Object(f.__)("Set up %s","google-site-kit"),i))),e.createElement(_.a,{slug:n}))}SetupModule.propTypes={slug:u.a.string.isRequired,name:u.a.string.isRequired,description:u.a.string.isRequired}}).call(this,n(4))},function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{stroke:"#E8EAED",strokeLinecap:"round",strokeMiterlimit:10,strokeWidth:6,d:"M382 94.2h-20.91M364.25 33.77l-17.59 11.3M197.01 9.74l13.7 15.8M162.97 62.74l20.06 5.88M162.98 125.72l20.06-5.89M316.7 195.86l-8.69-19.02M364.28 154.59l-17.59-11.3"}),o=i.createElement("ellipse",{cx:154.11,cy:381.74,rx:103.5,ry:8.26,fill:"#F1F3F4"}),c=i.createElement("path",{d:"M166.37 181.8c54.51-10.47 66-55.59 28.92-92.64",stroke:"#FBBC04",strokeLinejoin:"round",strokeWidth:9}),l=i.createElement("path",{d:"M148.8 254.55l16.54 51.35a90 90 0 012 47.76l-6.46 28.08h23M125.3 261.45l8.46 34.92a89.84 89.84 0 01-.27 43.48l-10.68 41.83 20 .17",stroke:"#F29900",strokeLinejoin:"round",strokeWidth:9}),s=i.createElement("path",{stroke:"#EA8600",strokeLinejoin:"round",strokeWidth:9,d:"M148.8 254.55l9.53 29.6M128.39 274.19l4.41 17.52"}),u=i.createElement("path",{fill:"#F9AB00",d:"M165.19 159.52L130.41 58.66 29.56 93.45 64.34 194.3l34.79 100.85 100.85-34.78-34.79-100.85z"}),d=i.createElement("path",{d:"M71.7 138.64c16.69 24.27 52.79 17.16 59.56-16.13M75.78 131.55a17.14 17.14 0 01-3.87 6.45 28 28 0 01-7.23 4.93",stroke:"#FFF",strokeMiterlimit:10,strokeWidth:6}),g=i.createElement("path",{stroke:"#BDC1C6",strokeMiterlimit:10,strokeWidth:20,d:"M269.28 98.8l-24.66 91.66"}),f=i.createElement("path",{d:"M197.37 113.58a11.4 11.4 0 101.41-22.31 38.5 38.5 0 01-8.49-.63 22.81 22.81 0 1128-16.1M324.07 147.67a11.4 11.4 0 1110-20s4.61 2.89 7.65 3.71a22.8 22.8 0 10-16.09-27.95",stroke:"#BDC1C6",strokeMiterlimit:10,strokeWidth:6}),m=i.createElement("path",{d:"M253.5 157.45c37.27 10 80.69-30.92 97-91.47l-135-36.32c-16.27 60.55.73 117.76 38 127.79z",fill:"#DADCE0"}),p=i.createElement("path",{d:"M224 109.65c-5.87-19.74-6.24-43.45-1.16-67.66M241.18 139.46a54.17 54.17 0 01-11.61-15.67",stroke:"#F1F3F4",strokeMiterlimit:10,strokeWidth:6}),h=i.createElement("path",{d:"M248 177.83c-13.07-3.52-26 2.23-28.83 12.84l47.33 12.73c2.86-10.61-5.42-22.06-18.5-25.57z",fill:"#DADCE0"}),b=i.createElement("path",{d:"M266 110.86l-17.44 3.92a2.18 2.18 0 01-2.46-3l7.5-16.23-9.12-15.37a2.19 2.19 0 012.14-3.28L264.4 79l11.8-13.42a2.18 2.18 0 013.78 1l3.48 17.53 16.41 7.08a2.18 2.18 0 01.2 3.91l-15.6 8.72-1.66 17.79a2.18 2.18 0 01-3.66 1.4z",stroke:"#BDC1C6",strokeMiterlimit:10,strokeWidth:6}),v=i.createElement("path",{d:"M74.48 209.27c74.32 45.28 152.4-1.07 170.14-18.81M87.61 216.33a129.71 129.71 0 0013.13 5.3",stroke:"#FCC934",strokeLinejoin:"round",strokeWidth:9}),E=i.createElement("path",{d:"M74.48 209.27a136.76 136.76 0 0013.13 7.06",stroke:"#FBBC04",strokeLinejoin:"round",strokeWidth:9});t.a=function SvgAward(e){return i.createElement("svg",r({viewBox:"0 0 400 400",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p,h,b,v,E)}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";n.r(t),function(e){var t=n(146),i=n(332),r=n(144),a=n(224),o=n(1122),c=n(22);Object(i.a)((function(){var n=document.getElementById("js-googlesitekit-settings");n&&Object(r.render)(e.createElement(a.a,{viewContext:c.r},e.createElement(t.a,null,e.createElement(o.a,null))),n)}))}.call(this,n(4))}],[[1285,1,0]]]);