(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[4],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var r=n(59),a=n(39),i=n(57);function o(t,n){var o,c=Object(r.a)(n),s=t.activeModules,l=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,g=void 0===d?[]:d,f=t.isAuthenticated,p=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(a.b,"]"))),!o){o=!0;var r=(null==g?void 0:g.length)?g.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:l,plugin_version:p||"",enabled_features:Array.from(i.a).join(","),active_modules:s.join(","),authenticated:f?"1":"0",user_properties:{user_roles:r,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(a.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(a.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(a.a)}}}}}).call(this,n(28))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var r=n(5),a=n.n(r),i=n(6),o=n.n(i),c=n(16),s=n.n(c),l=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n,r){var i=Object(l.a)(t);return function(){var t=s()(a.a.mark((function t(o,c,s,l){var u;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:s,value:l},t.abrupt("return",new Promise((function(e){var t,n,a=setTimeout((function(){r.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(a),e()};i("event",c,d(d({},u),{},{event_callback:s})),(null===(t=r._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,a){return t.apply(this,arguments)}}()}},102:function(e,t,n){"use strict";var r=n(123);n.d(t,"a",(function(){return r.a}));var a=n(124);n.d(t,"c",(function(){return a.a}));var i=n(125);n.d(t,"b",(function(){return i.a}))},104:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return r.createElement("svg",a({viewBox:"0 0 14 14",fill:"none"},e),i)}},107:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var r=n(21),a=n.n(r),i=n(152),o=n.n(i),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(2),g=n(10),f=n(154),p=n(104);function TourTooltip(t){var n=t.backProps,r=t.closeProps,c=t.index,l=t.primaryProps,u=t.size,m=t.step,b=t.tooltipProps,v=u>1?Object(f.a)(u):[],h=function(e){return s()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",a()({className:s()("googlesitekit-tour-tooltip",m.className)},b),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},m.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},m.content)),e.createElement(i.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},v.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:h(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(g.Button,a()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),m.cta,l.title&&e.createElement(g.Button,a()({className:"googlesitekit-tooltip-button",text:!0},l),l.title))),e.createElement(g.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(p.a,{width:"14",height:"14"}),onClick:r.onClick,"aria-label":Object(d.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(4))},109:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(208),s=n(38),l=n(2),u=n(10),d=n(58);function ModalDialog(t){var n=t.className,r=void 0===n?"":n,a=t.dialogActive,i=void 0!==a&&a,g=t.handleDialog,f=void 0===g?null:g,p=t.onOpen,m=void 0===p?null:p,b=t.onClose,v=void 0===b?null:b,h=t.title,O=void 0===h?null:h,y=t.provides,E=t.handleConfirm,k=t.subtitle,j=t.confirmButton,_=void 0===j?null:j,S=t.dependentModules,w=t.danger,N=void 0!==w&&w,D=t.inProgress,T=void 0!==D&&D,R=t.small,C=void 0!==R&&R,A=t.medium,P=void 0!==A&&A,L=t.buttonLink,x=void 0===L?null:L,I=Object(c.a)(ModalDialog),M="googlesitekit-dialog-description-".concat(I),B=!(!y||!y.length);return e.createElement(u.Dialog,{open:i,onOpen:m,onClose:v,"aria-describedby":B?M:void 0,tabIndex:"-1",className:o()(r,{"googlesitekit-dialog-sm":C,"googlesitekit-dialog-md":P})},e.createElement(u.DialogTitle,null,N&&e.createElement(d.a,{width:28,height:28}),O),k?e.createElement("p",{className:"mdc-dialog__lead"},k):[],e.createElement(u.DialogContent,null,B&&e.createElement("section",{id:M,className:"mdc-dialog__provides"},e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},y.map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t},e.createElement("span",{className:"mdc-list-item__text"},t))})))),S&&e.createElement("p",{className:"mdc-dialog__dependencies"},Object(s.a)(Object(l.sprintf)(
/* translators: %s is replaced with the dependent modules. */
Object(l.__)("<strong>Note:</strong> %s","google-site-kit"),S),{strong:e.createElement("strong",null)}))),e.createElement(u.DialogFooter,null,e.createElement(u.Button,{className:"mdc-dialog__cancel-button",tertiary:!0,onClick:f,disabled:T},Object(l.__)("Cancel","google-site-kit")),x?e.createElement(u.Button,{href:x,onClick:E,target:"_blank",danger:N},_):e.createElement(u.SpinnerButton,{onClick:E,danger:N,disabled:T,isSaving:T},_||Object(l.__)("Disconnect","google-site-kit"))))}ModalDialog.displayName="Dialog",ModalDialog.propTypes={className:a.a.string,dialogActive:a.a.bool,handleDialog:a.a.func,handleConfirm:a.a.func.isRequired,onOpen:a.a.func,onClose:a.a.func,title:a.a.string,confirmButton:a.a.string,danger:a.a.bool,small:a.a.bool,medium:a.a.bool,buttonLink:a.a.string},t.a=ModalDialog}).call(this,n(4))},1115:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdminBarApp}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(0),s=n(2),l=n(3),u=n(20),d=n(17),g=n(13),f=n(7),p=n(9),m=n(1116),b=n(18);function AdminBarApp(){var t=Object(b.a)(),n=Object(l.useSelect)((function(e){return e(g.c).getCurrentEntityURL()})),r=Object(l.useSelect)((function(e){return e(g.c).getCurrentEntityTitle()})),i=Object(l.useSelect)((function(e){return e(g.c).getAdminURL("googlesitekit-dashboard",{permaLink:n})})),v=Object(l.useSelect)((function(e){return e(f.a).getDateRangeNumberOfDays()})),h=Object(c.useCallback)(o()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(p.I)(t,"open_urldetails");case 2:document.location.assign(i);case 3:case"end":return e.stop()}}),e)}))),[i,t]);return i&&n?e.createElement(c.Fragment,null,e.createElement(d.e,null,e.createElement(d.k,null,e.createElement(d.a,{alignMiddle:!0,size:3},e.createElement("div",{className:"googlesitekit-adminbar__subtitle"},Object(s.__)("Stats for","google-site-kit")),e.createElement("div",{className:"googlesitekit-adminbar__title"},r?Object(p.l)(r):n,e.createElement("p",{className:"googlesitekit-adminbar__title--date-range"},Object(s.sprintf)(
/* translators: %s: number of days */
Object(s._n)("over the last %s day","over the last %s days",v,"google-site-kit"),v)))),e.createElement(d.a,{alignMiddle:!0,mdSize:8,lgSize:7},e.createElement(m.a,null)),e.createElement(d.a,{alignMiddle:!0,size:2},e.createElement(u.a,{className:"googlesitekit-adminbar__link",href:"#",onClick:h},Object(s.__)("More details","google-site-kit"))))),e.createElement(u.a,{className:"googlesitekit-adminbar__link googlesitekit-adminbar__link--mobile",href:"#",onClick:h},Object(s.__)("More details","google-site-kit"))):null}}).call(this,n(4))},1116:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdminBarWidgets}));var r=n(0),a=n(3),i=n(1117),o=n(1118),c=n(1119),s=n(1120),l=n(1121),u=n(19),d=n(7),g=n(17),f=n(243),p=Object(f.c)("adminBarImpressions")(i.a),m=Object(f.c)("adminBarClicks")(o.a),b=Object(f.c)("adminBarUniqueVisitors")(c.a),v=Object(f.c)("adminBarSessions")(s.a);function AdminBarWidgets(){var t=Object(a.useSelect)((function(e){return e(u.a).isModuleAvailable("analytics-4")})),n=Object(a.useSelect)((function(e){return e(u.a).isModuleConnected("analytics-4")})),i=Object(a.useSelect)((function(e){return e(u.a).isModuleActive("analytics-4")})),o=Object(a.useSelect)((function(e){return e(d.a).hasAccessToShareableModule("analytics-4")})),c=Object(a.useSelect)((function(e){return e(d.a).hasAccessToShareableModule("search-console")})),s=o?{lg:3,md:2}:{lg:6,md:4},f=c?{lg:3,md:2}:{lg:6,md:4};return e.createElement(r.Fragment,null,e.createElement(g.k,null,c&&e.createElement(r.Fragment,null,e.createElement(g.a,{lgSize:s.lg,mdSize:s.md},e.createElement(p,null)),e.createElement(g.a,{lgSize:s.lg,mdSize:s.md},e.createElement(m,null))),n&&i&&o&&e.createElement(r.Fragment,null,e.createElement(r.Fragment,null,e.createElement(g.a,{lgSize:f.lg,mdSize:f.md},e.createElement(b,null)),e.createElement(g.a,{lgSize:f.lg,mdSize:f.md},e.createElement(v,null)))),t&&(!n||!i)&&e.createElement(g.a,{lgSize:6,mdSize:4},e.createElement(l.a,null))))}}).call(this,n(4))},1117:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(2),o=n(3),c=n(236),s=n(44),l=n(114),u=n(7),d=n(13),g=n(66),f=n(9),p=n(418),m=n(89);t.a=function AdminBarImpressions(t){var n=t.WidgetReportError,r=Object(o.useSelect)((function(e){return e(g.b).isGatheringData()})),b=Object(o.useSelect)((function(e){return e(d.c).getCurrentEntityURL()})),v=Object(o.useSelect)((function(e){return e(u.a).getDateRangeDates({compare:!0,offsetDays:g.a})})),h=v.compareStartDate,O=v.endDate,y=Object(o.useSelect)((function(e){return e(u.a).getDateRangeNumberOfDays()})),E={startDate:h,endDate:O,dimensions:"date",url:b},k=Object(o.useSelect)((function(e){return e(g.b).getReport(E)})),j=Object(o.useSelect)((function(e){return e(g.b).hasFinishedResolution("getReport",[E])})),_=Object(o.useSelect)((function(e){return e(g.b).getErrorForSelector("getReport",[E])}));if(!j||void 0===r)return e.createElement(s.a,{width:"auto",height:"59px"});if(_)return e.createElement(n,{moduleSlug:"search-console",error:_});var S=Object(m.a)(k,{dateRangeLength:y}),w=S.compareRange,N=S.currentRange,D=Object(p.a)(N,"impressions"),T=Object(p.a)(w,"impressions"),R=Object(f.g)(T,D),C={gatheringData:r,gatheringDataNoticeStyle:l.a.SMALL};return e.createElement(c.a,a()({className:"overview-total-impressions",title:Object(i.__)("Total Impressions","google-site-kit"),datapoint:D,change:R,changeDataUnit:"%"},C))}}).call(this,n(4))},1118:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(2),o=n(3),c=n(7),s=n(13),l=n(66),u=n(9),d=n(44),g=n(236),f=n(114),p=n(418),m=n(89);t.a=function AdminBarClicks(t){var n=t.WidgetReportError,r=Object(o.useSelect)((function(e){return e(l.b).isGatheringData()})),b=Object(o.useSelect)((function(e){return e(s.c).getCurrentEntityURL()})),v=Object(o.useSelect)((function(e){return e(c.a).getDateRangeDates({compare:!0,offsetDays:l.a})})),h=v.compareStartDate,O=v.endDate,y=Object(o.useSelect)((function(e){return e(c.a).getDateRangeNumberOfDays()})),E={startDate:h,endDate:O,dimensions:"date",url:b},k=Object(o.useSelect)((function(e){return e(l.b).getReport(E)})),j=Object(o.useSelect)((function(e){return e(l.b).hasFinishedResolution("getReport",[E])})),_=Object(o.useSelect)((function(e){return e(l.b).getErrorForSelector("getReport",[E])}));if(!j||void 0===r)return e.createElement(d.a,{width:"auto",height:"59px"});if(_)return e.createElement(n,{moduleSlug:"search-console",error:_});var S=Object(m.a)(k,{dateRangeLength:y}),w=S.compareRange,N=S.currentRange,D=Object(p.a)(N,"clicks"),T=Object(p.a)(w,"clicks"),R=Object(u.g)(T,D),C={gatheringData:r,gatheringDataNoticeStyle:f.a.SMALL};return e.createElement(g.a,a()({className:"overview-total-clicks",title:Object(i.__)("Total Clicks","google-site-kit"),datapoint:D,change:R,changeDataUnit:"%"},C))}}).call(this,n(4))},1119:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(6),o=n.n(i),c=n(2),s=n(236),l=n(3),u=n(44),d=n(114),g=n(7),f=n(13),p=n(9),m=n(8);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}t.a=function AdminBarUniqueVisitorsGA4(t){var n,r,i,o,b,h,O,y,E=t.WidgetReportError,k=Object(l.useSelect)((function(e){return e(m.r).isGatheringData()})),j=Object(l.useSelect)((function(e){return e(f.c).getCurrentEntityURL()})),_=v(v({},Object(l.useSelect)((function(e){return e(g.a).getDateRangeDates({compare:!0,offsetDays:m.g})}))),{},{metrics:[{name:"totalUsers"}],url:j}),S=Object(l.useSelect)((function(e){return e(m.r).getReport(_)})),w=Object(l.useSelect)((function(e){return e(m.r).hasFinishedResolution("getReport",[_])})),N=Object(l.useSelect)((function(e){return e(m.r).getErrorForSelector("getReport",[_])}));if(!w||void 0===k)return e.createElement(u.a,{width:"auto",height:"59px"});if(N)return e.createElement(E,{moduleSlug:"analytics-4",error:N});var D=null==S||null===(n=S.totals)||void 0===n||null===(r=n[0])||void 0===r||null===(i=r.metricValues)||void 0===i||null===(o=i[0])||void 0===o?void 0:o.value,T=null==S||null===(b=S.totals)||void 0===b||null===(h=b[1])||void 0===h||null===(O=h.metricValues)||void 0===O||null===(y=O[0])||void 0===y?void 0:y.value,R={gatheringData:k,gatheringDataNoticeStyle:d.a.SMALL};return e.createElement(s.a,a()({className:"overview-total-users",title:Object(c.__)("Total Users","google-site-kit"),datapoint:D,change:Object(p.g)(T,D),changeDataUnit:"%"},R))}}).call(this,n(4))},1120:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(6),o=n.n(i),c=n(2),s=n(3),l=n(236),u=n(44),d=n(114),g=n(7),f=n(13),p=n(9),m=n(8);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}t.a=function AdminBarSessionsGA4(t){var n,r,i,o,b,h,O=t.WidgetReportError,y=Object(s.useSelect)((function(e){return e(m.r).isGatheringData()})),E=Object(s.useSelect)((function(e){return e(f.c).getCurrentEntityURL()})),k=v(v({},Object(s.useSelect)((function(e){return e(g.a).getDateRangeDates({compare:!0,offsetDays:m.g})}))),{},{dimensions:[{name:"date"}],limit:10,metrics:[{name:"sessions"}],url:E}),j=Object(s.useSelect)((function(e){return e(m.r).getReport(k)})),_=Object(s.useSelect)((function(e){return e(m.r).hasFinishedResolution("getReport",[k])})),S=Object(s.useSelect)((function(e){return e(m.r).getErrorForSelector("getReport",[k])})),w={gatheringData:y,gatheringDataNoticeStyle:d.a.SMALL};if(!_||void 0===y)return e.createElement(u.a,{width:"auto",height:"59px"});if(S)return e.createElement(O,{moduleSlug:"analytics-4",error:S});var N=j.totals,D=null==N||null===(n=N[0])||void 0===n||null===(r=n.metricValues)||void 0===r||null===(i=r[0])||void 0===i?void 0:i.value,T=null==N||null===(o=N[1])||void 0===o||null===(b=o.metricValues)||void 0===b||null===(h=b[0])||void 0===h?void 0:h.value,R=D,C=Object(p.g)(T,D);return e.createElement(l.a,a()({className:"overview-total-sessions",title:Object(c.__)("Total Sessions","google-site-kit"),datapoint:R,change:C,changeDataUnit:"%"},w))}}).call(this,n(4))},1121:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdminBarActivateAnalyticsCTA}));var r=n(2),a=n(693),i=n(681),o=n(576),c=n(416);function AdminBarActivateAnalyticsCTA(){return e.createElement(o.a,null,e.createElement(c.a,{title:Object(r.__)("Traffic","google-site-kit"),GraphSVG:a.a,showIcons:!1}),e.createElement(c.a,{title:Object(r.__)("Unique visitors from Search","google-site-kit"),GraphSVG:i.a}))}}).call(this,n(4))},114:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return u}));var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(11),s=n.n(c),l=n(2),u={DEFAULT:"default",OVERLAY:"overlay",SMALL:"small",SMALL_OVERLAY:"small-overlay",LARGE:"large"};function GatheringDataNotice(t){var n=t.style;return e.createElement("div",{className:s()("googlesitekit-gathering-data-notice",a()({},"googlesitekit-gathering-data-notice--has-style-".concat(n),!!n))},e.createElement("span",null,Object(l.__)("Gathering data…","google-site-kit")))}GatheringDataNotice.propTypes={style:o.a.oneOf(Object.values(u))},t.b=GatheringDataNotice}).call(this,n(4))},121:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(219),a=n(14),i=n(0);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=Object(r.b)((function(){return a.debounce.apply(void 0,t)}),t);return Object(i.useEffect)((function(){return function(){return o.cancel()}}),[o]),o}},123:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var r=n(21),a=n.n(r),i=n(6),o=n.n(i),c=n(25),s=n.n(c),l=n(1),u=n.n(l),d=n(11),g=n.n(d);function Cell(t){var n,r=t.className,i=t.alignTop,c=t.alignMiddle,l=t.alignBottom,u=t.alignRight,d=t.alignLeft,f=t.smAlignRight,p=t.mdAlignRight,m=t.lgAlignRight,b=t.smSize,v=t.smStart,h=t.smOrder,O=t.mdSize,y=t.mdStart,E=t.mdOrder,k=t.lgSize,j=t.lgStart,_=t.lgOrder,S=t.size,w=t.children,N=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",a()({},N,{className:g()(r,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":i,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":l,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":f,"mdc-layout-grid__cell--align-right-tablet":p,"mdc-layout-grid__cell--align-right-desktop":m},o()(n,"mdc-layout-grid__cell--span-".concat(S),12>=S&&S>0),o()(n,"mdc-layout-grid__cell--span-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--start-".concat(j,"-desktop"),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--order-".concat(_,"-desktop"),12>=_&&_>0),o()(n,"mdc-layout-grid__cell--span-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--start-".concat(y,"-tablet"),8>=y&&y>0),o()(n,"mdc-layout-grid__cell--order-".concat(E,"-tablet"),8>=E&&E>0),o()(n,"mdc-layout-grid__cell--span-".concat(b,"-phone"),4>=b&&b>0),o()(n,"mdc-layout-grid__cell--start-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--order-".concat(h,"-phone"),4>=h&&h>0),n))}),w)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},124:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),g=Object(d.forwardRef)((function(t,n){var r=t.className,i=t.children,c=o()(t,["className","children"]);return e.createElement("div",a()({ref:n,className:u()("mdc-layout-grid__inner",r)},c),i)}));g.displayName="Row",g.propTypes={className:s.a.string,children:s.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(4))},125:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(11),u=n.n(l),d=n(0),g=Object(d.forwardRef)((function(t,n){var r=t.alignLeft,i=t.fill,c=t.className,s=t.children,l=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",a()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":r,"mdc-layout-grid--collapsed":l,"mdc-layout-grid--fill":i})},d,{ref:n}),s)}));g.displayName="Grid",g.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(4))},126:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return r.createElement("svg",a({viewBox:"0 0 13 13"},e),i)}},127:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),r.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return r.createElement("svg",a({viewBox:"0 0 13 13"},e),i)}},128:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},1284:function(e,t,n){"use strict";n.r(t),function(e){var t=n(14),r=n(332),a=n(144),i=n(9),o=n(224),c=n(1115),s=n(22),l=Object(t.once)((function(){var t=document.getElementById("js-googlesitekit-adminbar-modules");if(t){var n=t.dataset.viewOnly?s.j:s.i;Object(a.render)(e.createElement(o.a,{viewContext:n},e.createElement(c.a,null)),t),Object(i.I)(n,"view_urlsummary")}}));Object(r.a)((function(){var e=document.getElementById("wp-admin-bar-google-site-kit");e&&(e.addEventListener("mouseover",l,{once:!0}),e.addEventListener("focusin",l,{once:!0}))}))}.call(this,n(4))},129:function(e,t,n){"use strict";n.d(t,"a",(function(){return m})),n.d(t,"c",(function(){return v})),n.d(t,"b",(function(){return h}));var r=n(25),a=n.n(r),i=n(6),o=n.n(i),c=n(5),s=n.n(c),l=n(12),u=n.n(l),d=n(3),g=n.n(d),f=n(37),p=n(9),m=function(e){var t;u()(e,"storeName is required to create a snapshot store.");var n={},r={deleteSnapshot:s.a.mark((function e(){var t;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"DELETE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)})),restoreSnapshot:s.a.mark((function e(){var t,n,r,a,i,o,c=arguments;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},n=t.clearAfterRestore,r=void 0===n||n,e.next=4,{payload:{},type:"RESTORE_SNAPSHOT"};case 4:if(a=e.sent,i=a.cacheHit,o=a.value,!i){e.next=13;break}return e.next=10,{payload:{snapshot:o},type:"SET_STATE_FROM_SNAPSHOT"};case 10:if(!r){e.next=13;break}return e.next=13,{payload:{},type:"DELETE_SNAPSHOT"};case 13:return e.abrupt("return",i);case 14:case"end":return e.stop()}}),e)})),createSnapshot:s.a.mark((function e(){var t;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,{payload:{},type:"CREATE_SNAPSHOT"};case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e)}))},i=(t={},o()(t,"DELETE_SNAPSHOT",(function(){return Object(f.c)("datastore::cache::".concat(e))})),o()(t,"CREATE_SNAPSHOT",Object(d.createRegistryControl)((function(t){return function(){return Object(f.f)("datastore::cache::".concat(e),t.stores[e].store.getState())}}))),o()(t,"RESTORE_SNAPSHOT",(function(){return Object(f.d)("datastore::cache::".concat(e),p.b)})),t);return{initialState:n,actions:r,controls:i,reducer:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=arguments.length>1?arguments[1]:void 0,r=t.type,i=t.payload;switch(r){case"SET_STATE_FROM_SNAPSHOT":var o=i.snapshot,c=(o.error,a()(o,["error"]));return c;default:return e}}}},b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Object.values(e.stores).filter((function(e){return Object.keys(e.getActions()).includes("restoreSnapshot")}))},v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Promise.all(b(e).map((function(e){return e.getActions().createSnapshot()})))},h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g.a;return Promise.all(b(e).map((function(e){return e.getActions().restoreSnapshot()})))}},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var r="core/site",a="primary",i="secondary"},130:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(14),a=function(e){return Object(r.isFinite)(e)?e:0}},134:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(38),s=n(2),l=n(20),u=n(34);function SourceLink(t){var n=t.name,r=t.href,a=t.className,i=t.external;return Object(u.a)()?null:e.createElement("div",{className:o()("googlesitekit-source-link",a)},Object(c.a)(Object(s.sprintf)(
/* translators: %s: source link */
Object(s.__)("Source: %s","google-site-kit"),"<a>".concat(n,"</a>")),{a:e.createElement(l.a,{key:"link",href:r,external:i})}))}SourceLink.propTypes={name:a.a.string,href:a.a.string,className:a.a.string,external:a.a.bool},SourceLink.defaultProps={name:"",href:"",className:"",external:!1},t.a=SourceLink}).call(this,n(4))},135:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportErrorActions}));var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(0),s=n(38),l=n(2),u=n(3),d=n(10),g=n(13),f=n(19),p=n(35),m=n(34),b=n(20);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportErrorActions(t){var n=t.moduleSlug,r=t.error,a=t.GetHelpLink,i=t.hideGetHelpLink,o=t.buttonVariant,v=t.onRetry,O=t.onRequestAccess,y=t.getHelpClassName,E=t.RequestAccessButton,k=t.RetryButton,j=Object(m.a)(),_=Object(u.useSelect)((function(e){return e(f.a).getModuleStoreName(n)})),S=Object(u.useSelect)((function(e){var t;return"function"==typeof(null===(t=e(_))||void 0===t?void 0:t.getServiceEntityAccessURL)?e(_).getServiceEntityAccessURL():null})),w=Array.isArray(r)?r:[r],N=Object(u.useSelect)((function(e){return w.map((function(t){var n,r=null===(n=e(_))||void 0===n?void 0:n.getSelectorDataForError(t);return h(h({},t),{},{selectorData:r})}))})),D=null==N?void 0:N.filter((function(e){return Object(p.d)(e,e.selectorData)&&"getReport"===e.selectorData.name})),T=!!D.length,R=Object(u.useSelect)((function(e){var t=h({},T?D[0]:w[0]);return Object(p.e)(t)&&(t.code="".concat(n,"_insufficient_permissions")),e(g.c).getErrorTroubleshootingLinkURL(t)})),C=Object(u.useDispatch)(),A=w.some((function(e){return Object(p.e)(e)})),P=Object(c.useCallback)((function(){D.forEach((function(e){var t=e.selectorData;C(t.storeName).invalidateResolution(t.name,t.args)})),null==v||v()}),[C,D,v]),L=S&&A&&!j;return e.createElement("div",{className:"googlesitekit-report-error-actions"},L&&("function"==typeof E?e.createElement(E,{requestAccessURL:S}):e.createElement(d.Button,{onClick:O,href:S,target:"_blank",danger:"danger"===o,tertiary:"tertiary"===o},Object(l.__)("Request access","google-site-kit"))),T&&e.createElement(c.Fragment,null,"function"==typeof k?e.createElement(k,{handleRetry:P}):e.createElement(d.Button,{onClick:P,danger:"danger"===o,tertiary:"tertiary"===o},Object(l.__)("Retry","google-site-kit")),!i&&e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(s.a)(Object(l.__)("Retry didn’t work? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(b.a,{href:R,external:!0,hideExternalIndicator:!0},Object(l.__)("Get help","google-site-kit"))}))),!T&&!i&&e.createElement("div",{className:y},"function"==typeof a?e.createElement(a,{linkURL:R}):e.createElement(b.a,{href:R,external:!0,hideExternalIndicator:!0},Object(l.__)("Get help","google-site-kit"))))}ReportErrorActions.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired,GetHelpLink:o.a.elementType,hideGetHelpLink:o.a.bool,buttonVariant:o.a.string,onRetry:o.a.func,onRequestAccess:o.a.func,getHelpClassName:o.a.string,RequestAccessButton:o.a.elementType,RetryButton:o.a.elementType}}).call(this,n(4))},136:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"d",(function(){return i})),n.d(t,"c",(function(){return o}));function r(e){var t=e.format,n=void 0===t?"small":t,r=e.hasErrorOrWarning,a=e.hasSmallImageSVG,o=e.hasWinImageSVG,c={smSize:4,mdSize:8,lgSize:12},s=i(n);return Object.keys(c).forEach((function(e){var t=c[e];r&&(t-=1),a&&(t-=1),o&&0<t-s[e]&&(t-=s[e]),c[e]=t})),c}var a=function(e){switch(e){case"small":return{};case"larger":return{smOrder:2,mdOrder:2,lgOrder:1};default:return{smOrder:2,mdOrder:1}}},i=function(e){switch(e){case"smaller":return{smSize:4,mdSize:2,lgSize:2};case"larger":return{smSize:4,mdSize:8,lgSize:7};default:return{smSize:4,mdSize:2,lgSize:4}}},o=function(e){switch(e){case"larger":return{smOrder:1,mdOrder:1,lgOrder:2};default:return{smOrder:1,mdOrder:2}}}},139:function(e,t,n){"use strict";var r=n(0),a=Object(r.createContext)(!1);t.a=a},143:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(0),a=n(3),i=n(47);function o(e,t,n){var o=Object(a.useDispatch)(i.a),c=o.setWidgetState,s=o.unsetWidgetState;Object(r.useEffect)((function(){return c(e,t,n),function(){s(e,t,n)}}),[e,t,n,c,s])}},148:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M3.332 24.75h21.335c2.182 0 3.542-2.366 2.451-4.25L16.451 2.07C15.36.184 12.64.184 11.549 2.07L.882 20.5c-1.091 1.884.269 4.25 2.45 4.25zM14 14.833a1.42 1.42 0 01-1.417-1.416v-2.834c0-.779.638-1.416 1.417-1.416.78 0 1.417.637 1.417 1.416v2.834A1.42 1.42 0 0114 14.833zm1.417 5.667h-2.834v-2.833h2.834V20.5z",fill:"currentColor",fillRule:"nonzero"});t.a=function SvgError(e){return r.createElement("svg",a({viewBox:"0 0 28 25"},e),i)}},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},155:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("g",{fill:"none",fillRule:"evenodd"},r.createElement("path",{d:"M2.253 12.252l7.399 5.658A13.055 13.055 0 009 22c0 1.43.229 2.805.652 4.09l-7.4 5.658A22.02 22.02 0 010 22c0-3.506.81-6.814 2.253-9.748z",fill:"#FBBC05"}),r.createElement("path",{d:"M9.652 17.91l-7.4-5.658A21.935 21.935 0 0122 0c5.6 0 10.6 2.1 14.5 5.5l-6.4 6.4C27.9 10.1 25.1 9 22 9c-5.77 0-10.64 3.725-12.348 8.91z",fill:"#EA4335"}),r.createElement("path",{d:"M2.25 31.742l7.396-5.67A12.975 12.975 0 0022 35c6.1 0 10.7-3.1 11.8-8.5H22V18h20.5c.3 1.3.5 2.7.5 4 0 14-10 22-21 22A21.935 21.935 0 012.25 31.742z",fill:"#34A853"}),r.createElement("path",{d:"M36.34 38.52l-7.025-5.437c2.297-1.45 3.895-3.685 4.485-6.583H22V18h20.5c.3 1.3.5 2.7.5 4 0 7.17-2.623 12.767-6.66 16.52z",fill:"#4285F4"}));t.a=function SvgLogoG(e){return r.createElement("svg",a({viewBox:"0 0 43 44"},e),i)}},158:function(e,t,n){"use strict";var r=n(0),a=n(57),i=Object(r.createContext)(a.a);t.a=i},159:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(0),s=n(3),l=n(13),u=n(7),d=n(19),g=n(32),f=n(37),p=n(36),m=n(18);function b(e){var t=Object(m.a)(),n=Object(s.useSelect)((function(t){return t(d.a).getModule(e)})),r=Object(s.useSelect)((function(e){return e(u.a).hasCapability(u.K)})),i=Object(s.useDispatch)(d.a).activateModule,b=Object(s.useDispatch)(g.a).navigateTo,v=Object(s.useDispatch)(l.c).setInternalServerError,h=Object(c.useCallback)(o()(a.a.mark((function n(){var r,o,c;return a.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,i(e);case 2:if(r=n.sent,o=r.error,c=r.response,o){n.next=13;break}return n.next=8,Object(p.b)("".concat(t,"_widget-activation-cta"),"activate_module",e);case 8:return n.next=10,Object(f.f)("module_setup",e,{ttl:300});case 10:b(c.moduleReauthURL),n.next=14;break;case 13:v({id:"".concat(e,"-setup-error"),description:o.message});case 14:case"end":return n.stop()}}),n)}))),[i,e,b,v,t]);return(null==n?void 0:n.name)&&r?h:null}},160:function(e,t,n){"use strict";var r=n(139),a=(r.a.Consumer,r.a.Provider);t.a=a},163:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RecoverableModules}));var r=n(1),a=n.n(r),i=n(2),o=n(3),c=n(19),s=n(95);function RecoverableModules(t){var n=t.moduleSlugs,r=Object(o.useSelect)((function(e){var t=e(c.a).getModules();if(void 0!==t)return n.map((function(e){return t[e].name}))}));if(void 0===r)return null;var a=1===r.length?Object(i.sprintf)(
/* translators: %s: Module name */
Object(i.__)("%s data was previously shared by an admin who no longer has access. Please contact another admin to restore it.","google-site-kit"),r[0]):Object(i.sprintf)(
/* translators: %s: List of module names */
Object(i.__)("The data for the following modules was previously shared by an admin who no longer has access: %s. Please contact another admin to restore it.","google-site-kit"),r.join(Object(i._x)(", ","Recoverable modules","google-site-kit")));return e.createElement(s.a,{title:Object(i.__)("Data Unavailable","google-site-kit"),description:a})}RecoverableModules.propTypes={moduleSlugs:a.a.arrayOf(a.a.string).isRequired}}).call(this,n(4))},169:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(2);function a(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},a=n.slug,i=void 0===a?"":a,o=n.name,c=void 0===o?"":o,s=n.owner,l=void 0===s?{}:s;if(!i||!c)return e;var u="",d="";return"analytics-4"===i?e.match(/account/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):"search-console"===i&&(u=Object(r.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),u||(u=Object(r.sprintf)(
/* translators: %s: module name */
Object(r.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),c)),l&&l.login&&(d=Object(r.sprintf)(
/* translators: %s: owner name */
Object(r.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),l.login)),d||(d=Object(r.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(u," ").concat(d)}},17:function(e,t,n){"use strict";var r=n(254);n.d(t,"i",(function(){return r.a}));var a=n(319);n.d(t,"f",(function(){return a.a}));var i=n(320);n.d(t,"h",(function(){return i.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var s=n(91),l=n.n(s);n.d(t,"b",(function(){return l.a})),n.d(t,"c",(function(){return s.DialogContent})),n.d(t,"d",(function(){return s.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},170:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportError}));var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(14),s=n(0),l=n(2),u=n(3),d=n(19),g=n(35),f=n(169),p=n(84),m=n(54),b=n(95),v=n(135),h=n(34);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportError(t){var n,r=t.moduleSlug,a=t.error,i=Object(h.a)(),o=Object(u.useSelect)((function(e){return e(d.a).getModule(r)})),O=Array.isArray(a)?a:[a],E=function(e){return Object(g.e)(e)?i?(n=Object(l.sprintf)(
/* translators: %s: module name */
Object(l.__)("Access lost to %s","google-site-kit"),null==o?void 0:o.name),Object(l.sprintf)(
/* translators: %s: module name */
Object(l.__)("The administrator sharing this module with you has lost access to the %s service, so you won’t be able to see stats from it on the Site Kit dashboard. You can contact them or another administrator to restore access.","google-site-kit"),null==o?void 0:o.name)):(n=Object(l.sprintf)(
/* translators: %s: module name */
Object(l.__)("Insufficient permissions in %s","google-site-kit"),null==o?void 0:o.name),Object(f.a)(e.message,o)):Object(g.b)(e)},k=Object(c.uniqWith)(O.map((function(e){var t;return y(y({},e),{},{message:E(e),reconnectURL:null===(t=e.data)||void 0===t?void 0:t.reconnectURL})})),(function(e,t){return e.message===t.message&&e.reconnectURL===t.reconnectURL})),j=O.some((function(e){return Object(g.e)(e)}));j||1!==k.length?!j&&k.length>1&&(n=Object(l.sprintf)(
/* translators: %s: module name */
Object(l.__)("Data errors in %s","google-site-kit"),null==o?void 0:o.name)):n=Object(l.sprintf)(
/* translators: %s: module name */
Object(l.__)("Data error in %s","google-site-kit"),null==o?void 0:o.name);var _=e.createElement(s.Fragment,null,k.map((function(t){var n,r=null==a||null===(n=a.data)||void 0===n?void 0:n.reconnectURL;return r?e.createElement(m.a,{key:t.message,message:t.message,reconnectURL:r}):e.createElement("p",{key:t.message},p.a.sanitize(t.message,{ALLOWED_TAGS:[]}))})));return e.createElement(b.a,{title:n,description:_,error:!0},e.createElement(v.a,{moduleSlug:r,error:a}))}ReportError.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired}}).call(this,n(4))},172:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GenericErrorHandlerActions}));var r=n(1),a=n.n(r),i=n(2),o=n(20),c=n(194);function GenericErrorHandlerActions(t){var n=t.message,r=t.componentStack;return e.createElement("div",{className:"googlesitekit-generic-error-handler-actions"},e.createElement(c.a,{message:n,componentStack:r}),e.createElement(o.a,{href:"https://wordpress.org/support/plugin/google-site-kit/",external:!0},Object(i.__)("Report this problem","google-site-kit")))}GenericErrorHandlerActions.propTypes={message:a.a.string,componentStack:a.a.string}}).call(this,n(4))},173:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(22),a=function(e){return r.f.includes(e)}},174:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportZero}));var r=n(1),a=n.n(r),i=n(2),o=n(3),c=n(19),s=n(95);function ReportZero(t){var n=t.moduleSlug,r=Object(o.useSelect)((function(e){return e(c.a).getModule(n)}));return e.createElement(s.a,{title:Object(i.sprintf)(
/* translators: %s: Module name */
Object(i.__)("%s Gathering Data","google-site-kit"),null==r?void 0:r.name),description:Object(i.sprintf)(
/* translators: %s: Module name */
Object(i.__)("%s data is not yet available, please check back later","google-site-kit"),null==r?void 0:r.name)})}ReportZero.propTypes={moduleSlug:a.a.string.isRequired}}).call(this,n(4))},18:function(e,t,n){"use strict";var r=n(0),a=n(61);t.a=function(){return Object(r.useContext)(a.b)}},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r="core/modules",a="insufficient_module_dependencies"},194:function(e,t,n){"use strict";(function(e){var r=n(15),a=n.n(r),i=n(195),o=n.n(i),c=n(1),s=n.n(c),l=n(0),u=n(2),d=n(266),g=n(423),f=n(424),p=n(10);function ReportErrorButton(t){var n=t.message,r=t.componentStack,i=Object(l.useState)(!1),c=a()(i,2),s=c[0],m=c[1];return e.createElement(p.Button,{"aria-label":s?Object(u.__)("Error message copied to clipboard. Click to copy the error message again.","google-site-kit"):void 0,onClick:function(){o()("`".concat(n,"\n").concat(r,"`")),m(!0)},trailingIcon:e.createElement(d.a,{className:"mdc-button__icon",icon:s?g.a:f.a})},s?Object(u.__)("Copied to clipboard","google-site-kit"):Object(u.__)("Copy error contents","google-site-kit"))}ReportErrorButton.propTypes={message:s.a.string,componentStack:s.a.string},t.a=ReportErrorButton}).call(this,n(4))},197:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerNotification}));var r=n(21),a=n.n(r),i=n(6),o=n.n(i),c=n(5),s=n.n(c),l=n(16),u=n.n(l),d=n(15),g=n.n(d),f=n(1),p=n.n(f),m=n(11),b=n.n(m),v=n(206),h=n(240),O=n(81),y=n(0),E=n(106),k=n(3),j=n(17),_=n(93),S=n(37),w=n(24),N=n(211),D=n(213),T=n(212),R=n(226),C=n(227),A=n(86),P=n(136),L=n(130),x=n(32),I=n(228),M=n(79);function BannerNotification(t){var n,r=t.badgeLabel,i=t.children,c=t.className,l=void 0===c?"":c,d=t.ctaLabel,f=t.ctaLink,p=t.ctaTarget,m=t.description,B=t.dismiss,W=t.dismissExpires,F=void 0===W?0:W,z=t.format,U=void 0===z?"":z,V=t.id,G=t.isDismissible,H=void 0===G||G,q=t.learnMoreDescription,K=t.learnMoreLabel,X=t.learnMoreURL,$=t.learnMoreTarget,J=void 0===$?A.a.EXTERNAL:$,Y=t.logo,Z=t.module,Q=t.moduleName,ee=t.onCTAClick,te=t.onView,ne=t.onDismiss,re=t.onLearnMoreClick,ae=t.showOnce,ie=void 0!==ae&&ae,oe=t.SmallImageSVG,ce=t.title,se=t.type,le=t.WinImageSVG,ue=t.showSmallWinImage,de=void 0===ue||ue,ge=t.smallWinImageSVGWidth,fe=void 0===ge?75:ge,pe=t.smallWinImageSVGHeight,me=void 0===pe?75:pe,be=t.mediumWinImageSVGWidth,ve=void 0===be?105:be,he=t.mediumWinImageSVGHeight,Oe=void 0===he?105:he,ye=t.rounded,Ee=void 0!==ye&&ye,ke=t.footer,je=t.secondaryPane,_e=t.ctaComponent,Se=Object(y.useState)(!1),we=g()(Se,2),Ne=we[0],De=we[1],Te=Object(y.useState)(!1),Re=g()(Te,2),Ce=Re[0],Ae=Re[1],Pe="notification::dismissed::".concat(V),Le=function(){return Object(S.f)(Pe,new Date,{ttl:null})},xe=Object(M.a)(),Ie=Object(w.e)(),Me=Object(v.a)(),Be=Object(y.useState)(!1),We=g()(Be,2),Fe=We[0],ze=We[1],Ue=Object(y.useRef)(),Ve=Object(h.a)(Ue,{rootMargin:"".concat(-Object(L.a)(Object(_.c)(Ie)),"px 0px 0px 0px"),threshold:0});Object(y.useEffect)((function(){!Fe&&(null==Ve?void 0:Ve.isIntersecting)&&("function"==typeof te&&te(),ze(!0))}),[V,te,Fe,Ve]);var Ge=xe>=600;Object(O.a)(u()(s.a.mark((function e(){var t,n;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(F>0)){e.next=3;break}return e.next=3,Ye();case 3:if(!H){e.next=9;break}return e.next=6,Object(S.d)(Pe);case 6:t=e.sent,n=t.cacheHit,Ae(n);case 9:if(!ie){e.next=12;break}return e.next=12,Le();case 12:case"end":return e.stop()}}),e)}))));var He=function(){var e=u()(s.a.mark((function e(t){return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),t.preventDefault(),!ne){e.next=5;break}return e.next=5,ne(t);case 5:Ke();case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),qe=Object(E.a)(f)&&"_blank"!==p,Ke=function(){return qe||De(!0),new Promise((function(e){setTimeout(u()(s.a.mark((function t(){var n;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Le();case 2:Me()&&Ae(!0),n=new Event("notificationDismissed"),document.dispatchEvent(n),e();case 6:case"end":return t.stop()}}),t)}))),350)}))},Xe=Object(k.useSelect)((function(e){return!!f&&e(x.a).isNavigatingTo(f)})),$e=Object(k.useDispatch)(x.a).navigateTo,Je=function(){var e=u()(s.a.mark((function e(t){var n,r,a;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.persist(),qe&&!t.defaultPrevented&&t.preventDefault(),n=!0,!ee){e.next=12;break}return e.next=6,ee(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:r=e.t0,a=r.dismissOnCTAClick,n=void 0===a||a;case 12:if(!H||!n){e.next=15;break}return e.next=15,Ke();case 15:qe&&$e(f);case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Ye=function(){var e=u()(s.a.mark((function e(){var t,n,r;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(S.d)(Pe);case 2:if(t=e.sent,!(n=t.value)){e.next=10;break}if((r=new Date(n)).setSeconds(r.getSeconds()+parseInt(F,10)),!(r<new Date)){e.next=10;break}return e.next=10,Object(S.c)(Pe);case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();if(!Xe&&H&&(void 0===Ce||Ce))return null;var Ze=!Xe&&Ne?"is-closed":"is-open",Qe=Object(P.d)(U),et=Object(P.c)(U),tt=Object(P.a)(U),nt=Object(P.b)({format:U,hasErrorOrWarning:"win-error"===se||"win-warning"===se,hasSmallImageSVG:!!oe,hasWinImageSVG:!!le});return e.createElement(N.a,{id:V,className:b()(l,(n={},o()(n,"googlesitekit-publisher-win--".concat(U),U),o()(n,"googlesitekit-publisher-win--".concat(se),se),o()(n,"googlesitekit-publisher-win--".concat(Ze),Ze),o()(n,"googlesitekit-publisher-win--rounded",Ee),n)),secondaryPane:je,ref:Ue},Y&&e.createElement(C.a,{module:Z,moduleName:Q}),oe&&e.createElement(j.a,{size:1,className:"googlesitekit-publisher-win__small-media"},e.createElement(oe,null)),e.createElement(j.a,a()({},nt,tt,{className:"googlesitekit-publisher-win__content"}),e.createElement(D.a,{title:ce,badgeLabel:r,smallWinImageSVGHeight:me,smallWinImageSVGWidth:fe,winImageFormat:U,WinImageSVG:!Ge&&de?le:void 0}),e.createElement(I.a,{description:m,learnMoreURL:X,learnMoreLabel:K,learnMoreTarget:J,learnMoreDescription:q,onLearnMoreClick:re}),i,e.createElement(T.a,{ctaLink:f,ctaLabel:d,ctaComponent:_e,ctaTarget:p,ctaCallback:Je,dismissLabel:H?B:void 0,dismissCallback:He}),ke&&e.createElement("div",{className:"googlesitekit-publisher-win__footer"},ke)),le&&(Ge||!de)&&e.createElement(j.a,a()({},Qe,et,{alignBottom:"larger"===U,className:"googlesitekit-publisher-win__image"}),e.createElement("div",{className:"googlesitekit-publisher-win__image-".concat(U)},e.createElement(le,{style:{maxWidth:ve,maxHeight:Oe}}))),e.createElement(R.a,{type:se}))}BannerNotification.propTypes={id:p.a.string.isRequired,className:p.a.string,title:p.a.string.isRequired,description:p.a.node,learnMoreURL:p.a.string,learnMoreDescription:p.a.string,learnMoreLabel:p.a.string,learnMoreTarget:p.a.oneOf(Object.values(A.a)),WinImageSVG:p.a.elementType,SmallImageSVG:p.a.elementType,format:p.a.string,ctaLink:p.a.string,ctaLabel:p.a.string,type:p.a.string,dismiss:p.a.string,isDismissible:p.a.bool,logo:p.a.bool,module:p.a.string,moduleName:p.a.string,dismissExpires:p.a.number,showOnce:p.a.bool,onCTAClick:p.a.func,onView:p.a.func,onDismiss:p.a.func,onLearnMoreClick:p.a.func,badgeLabel:p.a.string,rounded:p.a.bool,footer:p.a.node,secondaryPane:p.a.node,showSmallWinImage:p.a.bool,smallWinImageSVGWidth:p.a.number,smallWinImageSVGHeight:p.a.number,mediumWinImageSVGWidth:p.a.number,mediumWinImageSVGHeight:p.a.number}}).call(this,n(4))},198:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ModuleIcon}));var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(3),u=n(19);function ModuleIcon(t){var n=t.slug,r=t.size,i=o()(t,["slug","size"]),c=Object(l.useSelect)((function(e){return e(u.a).getModuleIcon(n)}));return c?e.createElement(c,a()({width:r,height:r},i)):null}ModuleIcon.propTypes={slug:s.a.string.isRequired,size:s.a.number},ModuleIcon.defaultProps={size:33}}).call(this,n(4))},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(146),g=n(0),f=n(2),p=n(126),m=n(127),b=n(128),v=n(70),h=n(76),O=Object(g.forwardRef)((function(t,n){var r,i=t["aria-label"],c=t.secondary,l=void 0!==c&&c,u=t.arrow,g=void 0!==u&&u,O=t.back,y=void 0!==O&&O,E=t.caps,k=void 0!==E&&E,j=t.children,_=t.className,S=void 0===_?"":_,w=t.danger,N=void 0!==w&&w,D=t.disabled,T=void 0!==D&&D,R=t.external,C=void 0!==R&&R,A=t.hideExternalIndicator,P=void 0!==A&&A,L=t.href,x=void 0===L?"":L,I=t.inverse,M=void 0!==I&&I,B=t.noFlex,W=void 0!==B&&B,F=t.onClick,z=t.small,U=void 0!==z&&z,V=t.standalone,G=void 0!==V&&V,H=t.linkButton,q=void 0!==H&&H,K=t.to,X=t.leadingIcon,$=t.trailingIcon,J=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),Y=x||K||!F?K?"ROUTER_LINK":C?"EXTERNAL_LINK":"LINK":T?"BUTTON_DISABLED":"BUTTON",Z="BUTTON"===Y||"BUTTON_DISABLED"===Y?"button":"ROUTER_LINK"===Y?d.b:"a",Q=("EXTERNAL_LINK"===Y&&(r=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===Y&&(r=Object(f._x)("(disabled)","screen reader text","google-site-kit")),r?i?"".concat(i," ").concat(r):"string"==typeof j?"".concat(j," ").concat(r):void 0:i),ee=X,te=$;return y&&(ee=e.createElement(b.a,{width:14,height:14})),C&&!P&&(te=e.createElement(v.a,{width:14,height:14})),g&&!M&&(te=e.createElement(p.a,{width:14,height:14})),g&&M&&(te=e.createElement(m.a,{width:14,height:14})),e.createElement(Z,a()({"aria-label":Q,className:s()("googlesitekit-cta-link",S,{"googlesitekit-cta-link--secondary":l,"googlesitekit-cta-link--inverse":M,"googlesitekit-cta-link--small":U,"googlesitekit-cta-link--caps":k,"googlesitekit-cta-link--danger":N,"googlesitekit-cta-link--disabled":T,"googlesitekit-cta-link--standalone":G,"googlesitekit-cta-link--link-button":q,"googlesitekit-cta-link--no-flex":!!W}),disabled:T,href:"LINK"!==Y&&"EXTERNAL_LINK"!==Y||T?void 0:x,onClick:F,rel:"EXTERNAL_LINK"===Y?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===Y?"_blank":void 0,to:K},J),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},j),!!te&&e.createElement(h.a,{marginLeft:5},te))}));O.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=O}).call(this,n(4))},211:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(0),s=n(17),l=Object(c.forwardRef)((function(t,n){var r=t.id,a=t.className,i=t.children,l=t.secondaryPane;return e.createElement("section",{id:r,className:o()(a,"googlesitekit-publisher-win"),ref:n},e.createElement(s.e,null,e.createElement(s.k,null,i)),l&&e.createElement(c.Fragment,null,e.createElement("div",{className:"googlesitekit-publisher-win__secondary-pane-divider"}),e.createElement(s.e,{className:"googlesitekit-publisher-win__secondary-pane"},e.createElement(s.k,null,e.createElement(s.a,{className:"googlesitekit-publisher-win__secondary-pane",size:12},l)))))}));l.displayName="Banner",l.propTypes={id:a.a.string,className:a.a.string,secondaryPane:a.a.node},t.a=l}).call(this,n(4))},212:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerActions}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(15),s=n.n(c),l=n(1),u=n.n(l),d=n(206),g=n(0),f=n(3),p=n(10),m=n(32);function BannerActions(t){var n=t.ctaLink,r=t.ctaLabel,i=t.ctaComponent,c=t.ctaTarget,l=t.ctaCallback,u=t.dismissLabel,b=t.dismissCallback,v=Object(g.useState)(!1),h=s()(v,2),O=h[0],y=h[1],E=Object(d.a)(),k=Object(f.useSelect)((function(e){return!!n&&e(m.a).isNavigatingTo(n)})),j=function(){var e=o()(a.a.mark((function e(){var t,n,r,i=arguments;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(y(!0),t=i.length,n=new Array(t),r=0;r<t;r++)n[r]=i[r];return e.next=4,null==l?void 0:l.apply(void 0,n);case 4:E()&&y(!1);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return n||u||i?e.createElement("div",{className:"googlesitekit-publisher-win__actions"},i,r&&e.createElement(p.SpinnerButton,{className:"googlesitekit-notification__cta",href:n,target:c,onClick:j,disabled:O||k,isSaving:O||k},r),u&&e.createElement(p.Button,{tertiary:n||i,onClick:b,disabled:O||k},u)):null}BannerActions.propTypes={ctaLink:u.a.string,ctaLabel:u.a.string,ctaComponent:u.a.element,ctaTarget:u.a.string,ctaCallback:u.a.func,dismissLabel:u.a.string,dismissCallback:u.a.func}}).call(this,n(4))},213:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerTitle}));var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(11),s=n.n(c),l=n(77);function BannerTitle(t){var n=t.title,r=t.badgeLabel,i=t.WinImageSVG,o=t.winImageFormat,c=void 0===o?"":o,u=t.smallWinImageSVGWidth,d=void 0===u?75:u,g=t.smallWinImageSVGHeight,f=void 0===g?75:g;return n?e.createElement("div",{className:"googlesitekit-publisher-win__title-image-wrapper"},e.createElement("h3",{className:"googlesitekit-heading-2 googlesitekit-publisher-win__title"},n,r&&e.createElement(l.a,{label:r})),i&&e.createElement("div",{className:s()(a()({},"googlesitekit-publisher-win__image-".concat(c),c))},e.createElement(i,{width:d,height:f}))):null}BannerTitle.propTypes={title:o.a.string,badgeLabel:o.a.string,WinImageSVG:o.a.elementType,winImageFormat:o.a.string,smallWinImageSVGWidth:o.a.number,smallWinImageSVGHeight:o.a.number}}).call(this,n(4))},22:function(e,t,n){"use strict";n.d(t,"n",(function(){return r})),n.d(t,"l",(function(){return a})),n.d(t,"o",(function(){return i})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return s})),n.d(t,"s",(function(){return l})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"u",(function(){return p})),n.d(t,"v",(function(){return m})),n.d(t,"q",(function(){return b})),n.d(t,"p",(function(){return v})),n.d(t,"b",(function(){return h})),n.d(t,"e",(function(){return O})),n.d(t,"a",(function(){return y})),n.d(t,"d",(function(){return E})),n.d(t,"c",(function(){return k})),n.d(t,"f",(function(){return j})),n.d(t,"g",(function(){return _}));var r="mainDashboard",a="entityDashboard",i="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",s="activation",l="splash",u="adminBar",d="adminBarViewOnly",g="settings",f="adBlockingRecovery",p="wpDashboard",m="wpDashboardViewOnly",b="moduleSetup",v="metricSelection",h="key-metrics",O="traffic",y="content",E="speed",k="monetization",j=[r,a,i,o,c,l,g,b,v],_=[i,o,d,m]},224:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Root}));var r=n(15),a=n.n(r),i=n(1),o=n.n(i),c=n(432),s=n(534),l=n(0),u=n(3),d=n.n(u),g=n(225),f=n(229),p=n(57),m=n(230),b=n(232),v=n(233),h=n(61),O=n(160),y=n(173);function Root(t){var n=t.children,r=t.registry,i=t.viewContext,o=void 0===i?null:i,d=c.a,E=Object(l.useState)({key:"Root",value:!0}),k=a()(E,1)[0];return e.createElement(l.StrictMode,null,e.createElement(O.a,{value:k},e.createElement(u.RegistryProvider,{value:r},e.createElement(f.a,{value:p.a},e.createElement(h.a,{value:o},e.createElement(s.a,{theme:d()},e.createElement(g.a,null,e.createElement(b.a,null,n,o&&e.createElement(v.a,null)),Object(y.a)(o)&&e.createElement(m.a,null))))))))}Root.propTypes={children:o.a.node,registry:o.a.object,viewContext:o.a.string.isRequired},Root.defaultProps={registry:d.a}}).call(this,n(4))},225:function(e,t,n){"use strict";(function(e,r){var a=n(51),i=n.n(a),o=n(53),c=n.n(o),s=n(68),l=n.n(s),u=n(69),d=n.n(u),g=n(49),f=n.n(g),p=n(1),m=n.n(p),b=n(0),v=n(2),h=n(172),O=n(61),y=n(197),E=n(9);function k(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=f()(e);if(t){var a=f()(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return d()(this,n)}}var j=function(t){l()(ErrorHandler,t);var n=k(ErrorHandler);function ErrorHandler(e){var t;return i()(this,ErrorHandler),(t=n.call(this,e)).state={error:null,info:null,copied:!1},t}return c()(ErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t,info:n}),Object(E.I)("react_error","handle_".concat(this.context||"unknown","_error"),"".concat(null==t?void 0:t.message,"\n").concat(null==n?void 0:n.componentStack).slice(0,500))}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.error,a=t.info;return n?r.createElement(y.a,{id:"googlesitekit-error",className:"googlesitekit-error-handler",title:Object(v.__)("Site Kit encountered an error","google-site-kit"),description:r.createElement(h.a,{message:n.message,componentStack:a.componentStack}),isDismissible:!1,format:"small",type:"win-error"},r.createElement("pre",{className:"googlesitekit-overflow-auto"},n.message,a.componentStack)):e}}]),ErrorHandler}(b.Component);j.contextType=O.b,j.propTypes={children:m.a.node.isRequired},t.a=j}).call(this,n(28),n(4))},226:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerIcon}));var r=n(1),a=n.n(r),i=n(58),o=n(148),c=n(17);function BannerIcon(t){var n=t.type;if("win-error"!==n&&"win-warning"!==n)return null;var r="win-warning"===n?e.createElement(i.a,{width:34}):e.createElement(o.a,{width:28});return e.createElement(c.a,{size:1,smOrder:3,mdOrder:3,lgOrder:3},e.createElement("div",{className:"googlesitekit-publisher-win__icons"},r))}BannerIcon.propTypes={type:a.a.string}}).call(this,n(4))},227:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerLogo}));var r=n(1),a=n.n(r),i=n(17),o=n(155),c=n(198);function BannerLogo(t){var n=t.module,r=t.moduleName;return e.createElement(i.a,{size:12},e.createElement("div",{className:"googlesitekit-publisher-win__logo"},n&&e.createElement(c.a,{slug:n,size:19}),!n&&e.createElement(o.a,{height:"34",width:"32"})),r&&e.createElement("div",{className:"googlesitekit-publisher-win__module-name"},r))}BannerLogo.propTypes={module:a.a.string,moduleName:a.a.string}}).call(this,n(4))},228:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BannerDescription}));var r=n(1),a=n.n(r),i=n(0),o=n(75),c=n(20),s=n(86);function BannerDescription(t){var n=t.description,r=t.learnMoreLabel,a=t.learnMoreURL,l=t.learnMoreTarget,u=t.learnMoreDescription,d=t.onLearnMoreClick;if(!n)return null;var g;return r&&(g=e.createElement(i.Fragment,null,e.createElement(c.a,{onClick:function(e){e.persist(),null==d||d()},href:a,external:l===s.a.EXTERNAL},r),u)),e.createElement("div",{className:"googlesitekit-publisher-win__desc"},Object(i.isValidElement)(n)?e.createElement(i.Fragment,null,n,g&&e.createElement("p",null,g)):e.createElement("p",null,e.createElement("span",{dangerouslySetInnerHTML:Object(o.a)(n,{ALLOWED_TAGS:["strong","em","br","a"],ALLOWED_ATTR:["href"]})})," ",g))}BannerDescription.propTypes={description:a.a.node,learnMoreURL:a.a.string,learnMoreDescription:a.a.string,learnMoreLabel:a.a.string,learnMoreTarget:a.a.oneOf(Object.values(s.a)),onLearnMoreClick:a.a.func}}).call(this,n(4))},229:function(e,t,n){"use strict";var r=n(158),a=(r.a.Consumer,r.a.Provider);t.a=a},23:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a}));var r="core/ui",a="activeContextID"},230:function(e,t,n){"use strict";(function(e){var r=n(3),a=n(231),i=n(7);t.a=function PermissionsModal(){return Object(r.useSelect)((function(e){return e(i.a).isAuthenticated()}))?e.createElement(a.a,null):null}}).call(this,n(4))},231:function(e,t,n){"use strict";(function(e,r){var a=n(5),i=n.n(a),o=n(16),c=n.n(o),s=n(2),l=n(0),u=n(3),d=n(109),g=n(29),f=n(32),p=n(7),m=n(129),b=n(72);t.a=function AuthenticatedPermissionsModal(){var t,n,a,o,v=Object(u.useRegistry)(),h=Object(u.useSelect)((function(e){return e(p.a).getPermissionScopeError()})),O=Object(u.useSelect)((function(e){return e(p.a).getUnsatisfiedScopes()})),y=Object(u.useSelect)((function(t){var n,r,a;return t(p.a).getConnectURL({additionalScopes:null==h||null===(n=h.data)||void 0===n?void 0:n.scopes,redirectURL:(null==h||null===(r=h.data)||void 0===r?void 0:r.redirectURL)||e.location.href,errorRedirectURL:null==h||null===(a=h.data)||void 0===a?void 0:a.errorRedirectURL})})),E=Object(u.useDispatch)(p.a).clearPermissionScopeError,k=Object(u.useDispatch)(f.a).navigateTo,j=Object(u.useDispatch)(g.a).setValues,_=Object(l.useCallback)((function(){E()}),[E]),S=Object(l.useCallback)(c()(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return j(p.d,{permissionsError:h}),e.next=3,Object(m.c)(v);case 3:k(y);case 4:case"end":return e.stop()}}),e)}))),[v,y,k,h,j]);return Object(l.useEffect)((function(){(function(){var e=c()(i.a.mark((function e(){var t,n,r;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(null==h||null===(t=h.data)||void 0===t?void 0:t.skipModal)||!(null==h||null===(n=h.data)||void 0===n||null===(r=n.scopes)||void 0===r?void 0:r.length)){e.next=3;break}return e.next=3,S();case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[S,h]),h?(null==h||null===(t=h.data)||void 0===t||null===(n=t.scopes)||void 0===n?void 0:n.length)?(null==h||null===(a=h.data)||void 0===a?void 0:a.skipModal)||O&&(null==h||null===(o=h.data)||void 0===o?void 0:o.scopes.every((function(e){return O.includes(e)})))?null:r.createElement(b.a,null,r.createElement(d.a,{title:Object(s.__)("Additional Permissions Required","google-site-kit"),subtitle:h.message,confirmButton:Object(s.__)("Proceed","google-site-kit"),dialogActive:!0,handleConfirm:S,handleDialog:_,medium:!0})):(e.console.warn("permissionsError lacks scopes array to use for redirect, so not showing the PermissionsModal. permissionsError was:",h),null):null}}).call(this,n(28),n(4))},232:function(e,t,n){"use strict";var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=n(15),s=n.n(c),l=n(0),u=n(3),d=n(129);t.a=function RestoreSnapshots(e){var t=e.children,n=Object(u.useRegistry)(),r=Object(l.useState)(!1),i=s()(r,2),c=i[0],g=i[1];return Object(l.useEffect)((function(){c||o()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(d.b)(n);case 2:g(!0);case 3:case"end":return e.stop()}}),e)})))()}),[n,c]),c?t:null}},233:function(e,t,n){"use strict";(function(e,r){n.d(t,"a",(function(){return FeatureTours}));var a=n(81),i=n(0),o=n(3),c=n(7),s=n(18),l=n(90);function FeatureTours(){var t=Object(s.a)(),n=Object(o.useDispatch)(c.a).triggerTourForView;Object(a.a)((function(){n(t)}));var u=Object(o.useSelect)((function(e){return e(c.a).getCurrentTour()}));return Object(i.useEffect)((function(){if(u){var t=document.getElementById("js-googlesitekit-main-dashboard");if(t){var n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}}),[u]),u?r.createElement(l.a,{tourID:u.slug,steps:u.steps,gaEventCategory:u.gaEventCategory,callback:u.callback}):null}}).call(this,n(28),n(4))},236:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(0),s=n(114),l=n(9),u=n(402),d=n(77),g=n(403),f=n(134);function DataBlock(t){var n=t.stat,r=void 0===n?null:n,a=t.className,i=void 0===a?"":a,p=t.title,m=void 0===p?"":p,b=t.datapoint,v=void 0===b?null:b,h=t.datapointUnit,O=void 0===h?"":h,y=t.change,E=void 0===y?null:y,k=t.changeDataUnit,j=void 0===k?"":k,_=t.context,S=void 0===_?"default":_,w=t.period,N=void 0===w?"":w,D=t.selected,T=void 0!==D&&D,R=t.source,C=t.sparkline,A=t.handleStatSelection,P=void 0===A?null:A,L=t.invertChangeColor,x=void 0!==L&&L,I=t.gatheringData,M=void 0!==I&&I,B=t.gatheringDataNoticeStyle,W=void 0===B?s.a.DEFAULT:B,F=t.badge,z=Object(c.useCallback)((function(){!M&&P&&P(r)}),[M,P,r]),U=Object(c.useCallback)((function(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),z())}),[z]),V=void 0===v?v:Object(l.B)(v,O),G="button"===S,H=G?"button":"";return e.createElement("div",{className:o()("googlesitekit-data-block",i,"googlesitekit-data-block--".concat(S),{"googlesitekit-data-block--selected":T,"googlesitekit-data-block--is-gathering-data":M}),tabIndex:G&&!M?"0":"-1",role:P&&H,onClick:z,onKeyDown:U,"aria-disabled":M||void 0,"aria-label":P&&m,"aria-pressed":P&&T},e.createElement("div",{className:"googlesitekit-data-block__title-datapoint-wrapper"},e.createElement("h3",{className:" googlesitekit-subheading-1 googlesitekit-data-block__title "},!0===F?e.createElement(d.a,{"aria-hidden":"true",className:"googlesitekit-badge--hidden",label:"X"}):F,e.createElement("span",{className:"googlesitekit-data-block__title-inner"},m)),!M&&e.createElement("div",{className:"googlesitekit-data-block__datapoint"},V)),!M&&C&&e.createElement(u.a,{sparkline:C,invertChangeColor:x}),!M&&e.createElement("div",{className:"googlesitekit-data-block__change-source-wrapper"},e.createElement(g.a,{change:E,changeDataUnit:j,period:N,invertChangeColor:x}),R&&e.createElement(f.a,{className:"googlesitekit-data-block__source",name:R.name,href:R.link,external:null==R?void 0:R.external})),M&&e.createElement(s.b,{style:W}))}DataBlock.propTypes={stat:a.a.number,className:a.a.string,title:a.a.string,datapoint:a.a.oneOfType([a.a.string,a.a.number]),datapointUnit:a.a.string,change:a.a.oneOfType([a.a.string,a.a.number]),changeDataUnit:a.a.oneOfType([a.a.string,a.a.bool]),context:a.a.string,period:a.a.string,selected:a.a.bool,handleStatSelection:a.a.func,invertChangeColor:a.a.bool,gatheringData:a.a.bool,gatheringDataNoticeStyle:a.a.oneOf(Object.values(s.a)),badge:a.a.oneOfType([a.a.bool,a.a.node])},t.a=DataBlock}).call(this,n(4))},24:function(e,t,n){"use strict";n.d(t,"d",(function(){return a})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return s}));var r=n(79),a="xlarge",i="desktop",o="tablet",c="small";function s(){var e=Object(r.a)();return e>1280?a:e>960?i:e>600?o:c}},243:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return p})),n.d(t,"c",(function(){return b})),n.d(t,"b",(function(){return v}));var r=n(21),a=n.n(r),i=n(63),o=n.n(i),c=n(267),s=n(322),l=n(323),u=n(244),d=n(268),g=n(324),f=n(0),p=o()((function(e){return{widgetSlug:e,Widget:m(e)(c.a),WidgetRecoverableModules:m(e)(d.a),WidgetReportZero:m(e)(s.a),WidgetReportError:m(e)(l.a),WidgetNull:m(e)(u.a)}}));function m(t){return function(n){var r=Object(f.forwardRef)((function(r,i){return e.createElement(n,a()({},r,{ref:i,widgetSlug:t}))}));return r.displayName="WithWidgetSlug",(n.displayName||n.name)&&(r.displayName+="(".concat(n.displayName||n.name,")")),r}}var b=function(t){var n=p(t);return function(t){function DecoratedComponent(r){return e.createElement(t,a()({},r,n))}return DecoratedComponent.displayName="WithWidgetComponentProps",(t.displayName||t.name)&&(DecoratedComponent.displayName+="(".concat(t.displayName||t.name,")")),DecoratedComponent}},v=function(t){return function(n){function DecoratedComponent(r){return e.createElement(n,a()({},r,{WPDashboardReportError:m(t)(g.a)}))}return DecoratedComponent.displayName="WithWPDashboardWidgetComponentProps",(n.displayName||n.name)&&(DecoratedComponent.displayName+="(".concat(n.displayName||n.name,")")),DecoratedComponent}}}).call(this,n(4))},244:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetNull}));var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(143),s=n(74);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var u={};function WidgetNull(t){var n=t.widgetSlug;return Object(c.a)(n,s.a,u),e.createElement(s.a,null)}WidgetNull.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:o.a.string.isRequired},s.a.propTypes)}).call(this,n(4))},267:function(e,t,n){"use strict";(function(e){var r=n(11),a=n.n(r),i=n(1),o=n.n(i),c=n(0),s=Object(c.forwardRef)((function(t,n){var r=t.children,i=t.className,o=t.widgetSlug,c=t.noPadding,s=t.Header,l=t.Footer;return e.createElement("div",{className:a()("googlesitekit-widget","googlesitekit-widget--".concat(o),{"googlesitekit-widget--no-padding":c},{"googlesitekit-widget--with-header":s},i),ref:n},s&&e.createElement("div",{className:"googlesitekit-widget__header"},e.createElement(s,null)),e.createElement("div",{className:"googlesitekit-widget__body"},r),l&&e.createElement("div",{className:"googlesitekit-widget__footer"},e.createElement(l,null)))}));s.defaultProps={children:void 0,noPadding:!1},s.propTypes={children:o.a.node,widgetSlug:o.a.string.isRequired,noPadding:o.a.bool,Header:o.a.elementType,Footer:o.a.elementType},t.a=s}).call(this,n(4))},268:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetRecoverableModules}));var r=n(6),a=n.n(r),i=n(21),o=n.n(i),c=n(27),s=n.n(c),l=n(25),u=n.n(l),d=n(1),g=n.n(d),f=n(0),p=n(143),m=n(163);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function WidgetRecoverableModules(t){var n=t.widgetSlug,r=t.moduleSlugs,a=u()(t,["widgetSlug","moduleSlugs"]),i=Object(f.useMemo)((function(){return{moduleSlug:s()(r).sort().join(","),moduleSlugs:r}}),[r]);return Object(p.a)(n,m.a,i),e.createElement(m.a,o()({moduleSlugs:r},a))}WidgetRecoverableModules.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:g.a.string.isRequired},m.a.propTypes)}).call(this,n(4))},29:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/forms"},3:function(e,t){e.exports=googlesitekit.data},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r="core/location"},322:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportZero}));var r=n(6),a=n.n(r),i=n(21),o=n.n(i),c=n(25),s=n.n(c),l=n(1),u=n.n(l),d=n(0),g=n(143),f=n(174);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function WidgetReportZero(t){var n=t.widgetSlug,r=t.moduleSlug,a=s()(t,["widgetSlug","moduleSlug"]),i=Object(d.useMemo)((function(){return{moduleSlug:r}}),[r]);return Object(g.a)(n,f.a,i),e.createElement(f.a,o()({moduleSlug:r},a))}WidgetReportZero.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:u.a.string.isRequired},f.a.propTypes)}).call(this,n(4))},323:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportError}));var r=n(6),a=n.n(r),i=n(25),o=n.n(i),c=n(1),s=n.n(c),l=n(170);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function WidgetReportError(t){t.widgetSlug;var n=o()(t,["widgetSlug"]);return e.createElement(l.a,n)}WidgetReportError.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:s.a.string.isRequired},l.a.propTypes)}).call(this,n(4))},324:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WPDashboardReportError}));var r=n(1),a=n.n(r),i=n(550),o=n(208),c=n(3),s=n(23),l=n(170);function WPDashboardReportError(t){var n=t.moduleSlug,r=t.error,a=Object(o.a)(WPDashboardReportError,"WPDashboardReportError"),u=Object(c.useDispatch)(s.b).setValue,d=r.message,g=Object(c.useSelect)((function(e){return e(s.b).getValue("WPDashboardReportError-".concat(n,"-").concat(d))}));return Object(i.a)((function(){u("WPDashboardReportError-".concat(n,"-").concat(d),a)}),(function(){u("WPDashboardReportError-".concat(n,"-").concat(d),void 0)})),g!==a?null:e.createElement(l.a,{moduleSlug:n,error:r})}WPDashboardReportError.propTypes={moduleSlug:a.a.string.isRequired,error:a.a.object.isRequired}}).call(this,n(4))},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(22),a=n(18);function i(){var e=Object(a.a)();return r.g.includes(e)}},35:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return d}));n(14);var r=n(2),a="missing_required_scopes",i="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===a}function s(e){var t;return[i,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function l(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||s(e)||c(e)||l(e))}function d(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(r.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(r.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return y})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return O}));var r=n(99),a=e._googlesitekitTrackingData||{},i=a.activeModules,o=void 0===i?[]:i,c=a.isSiteKitScreen,s=a.trackingEnabled,l=a.trackingID,u=a.referenceSiteURL,d=a.userIDHash,g=a.isAuthenticated,f={activeModules:o,trackingEnabled:s,trackingID:l,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:a.userRoles,isAuthenticated:g,pluginVersion:"1.151.0"},p=Object(r.a)(f),m=p.enableTracking,b=p.disableTracking,v=(p.isTrackingEnabled,p.initializeSnippet),h=p.trackEvent,O=p.trackEventOnce;function y(e){e?m():b()}c&&s&&v()}).call(this,n(28))},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return h})),n.d(t,"f",(function(){return O})),n.d(t,"c",(function(){return y})),n.d(t,"e",(function(){return E})),n.d(t,"b",(function(){return k}));var r=n(5),a=n.n(r),i=n(16),o=n.n(i),c=(n(27),n(9));function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw i}}}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var u,d="googlesitekit_",g="".concat(d).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),f=["sessionStorage","localStorage"],p=[].concat(f),m=function(){var t=o()(a.a.mark((function t(n){var r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,i="__storage_test__",r.setItem(i,i),r.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==r.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function b(){return v.apply(this,arguments)}function v(){return(v=o()(a.a.mark((function t(){var n,r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=s(p),t.prev=3,n.s();case 5:if((r=n.n()).done){t.next=15;break}if(i=r.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,m(i);case 11:if(!t.sent){t.next=13;break}u=e[i];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var h=function(){var e=o()(a.a.mark((function e(t){var n,r,i,o,c,s,l;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!(n=e.sent)){e.next=10;break}if(!(r=n.getItem("".concat(g).concat(t)))){e.next=10;break}if(i=JSON.parse(r),o=i.timestamp,c=i.ttl,s=i.value,l=i.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:s,isError:l});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),O=function(){var t=o()(a.a.mark((function t(n,r){var i,o,s,l,u,d,f,p,m=arguments;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=m.length>2&&void 0!==m[2]?m[2]:{},o=i.ttl,s=void 0===o?c.b:o,l=i.timestamp,u=void 0===l?Math.round(Date.now()/1e3):l,d=i.isError,f=void 0!==d&&d,t.next=3,b();case 3:if(!(p=t.sent)){t.next=14;break}return t.prev=5,p.setItem("".concat(g).concat(n),JSON.stringify({timestamp:u,ttl:s,value:r,isError:f})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),y=function(){var t=o()(a.a.mark((function t(n){var r,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(r=t.sent)){t.next=14;break}return t.prev=4,i=n.startsWith(d)?n:"".concat(g).concat(n),r.removeItem(i),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),E=function(){var t=o()(a.a.mark((function t(){var n,r,i,o;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,b();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,r=[],i=0;i<n.length;i++)0===(o=n.key(i)).indexOf(d)&&r.push(o);return t.abrupt("return",r);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),k=function(){var e=o()(a.a.mark((function e(){var t,n,r,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:if(!e.sent){e.next=25;break}return e.next=6,E();case 6:t=e.sent,n=s(t),e.prev=8,n.s();case 10:if((r=n.n()).done){e.next=16;break}return i=r.value,e.next=14,y(i);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r="_googlesitekitDataLayer",a="data-googlesitekit-gtag"},398:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r=n(0),a=n(3),i=n(7),o=n(19),c=n(32);function s(e){var t=Object(a.useSelect)((function(e){return e(i.a).hasCapability(i.K)})),n=Object(a.useSelect)((function(t){return t(o.a).getModuleStoreName(e)})),s=Object(a.useSelect)((function(e){var t;return null===(t=e(n))||void 0===t?void 0:t.getAdminReauthURL()})),l=Object(a.useDispatch)(c.a).navigateTo,u=Object(r.useCallback)((function(){return l(s)}),[s,l]);return s&&t?u:null}},402:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(0);function Sparkline(t){var n=t.sparkline,r=t.invertChangeColor,a=n;return a&&r&&(a=Object(i.cloneElement)(n,{invertChangeColor:r})),e.createElement("div",{className:"googlesitekit-data-block__sparkline"},a)}Sparkline.propTypes={sparkline:a.a.element,invertChangeColor:a.a.bool},t.a=Sparkline}).call(this,n(4))},403:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(2),s=n(9),l=n(85);function Change(t){var n=t.change,r=t.changeDataUnit,a=t.period,i=t.invertChangeColor,u=n;return r&&(u="%"===r?Object(s.B)(n,{style:"percent",signDisplay:"never",maximumFractionDigits:1}):Object(s.B)(n,r)),a&&(u=Object(c.sprintf)(a,u)),e.createElement("div",{className:o()("googlesitekit-data-block__change",{"googlesitekit-data-block__change--no-change":!n})},!!n&&e.createElement("span",{className:"googlesitekit-data-block__arrow"},e.createElement(l.a,{direction:0<parseFloat(n)?"up":"down",invertColor:i})),e.createElement("span",{className:"googlesitekit-data-block__value"},u))}Change.propTypes={change:a.a.oneOfType([a.a.string,a.a.number]),changeDataUnit:a.a.oneOfType([a.a.string,a.a.bool]),period:a.a.string,invertChangeColor:a.a.bool},t.a=Change}).call(this,n(4))},416:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PreviewGraph}));var r=n(1),a=n.n(r),i=n(577);function PreviewGraph(t){var n=t.title,r=t.GraphSVG,a=t.showIcons;return e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graph"},e.createElement("h3",{className:"googlesitekit-analytics-cta__preview-graph--title"},n),e.createElement("div",null,e.createElement(r,null)),a&&e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graph--icons"},e.createElement(i.a,{className:"googlesitekit-analytics-cta__preview-graph--up-arrow"}),e.createElement("span",{className:"googlesitekit-analytics-cta__preview-graph--bar"})))}PreviewGraph.propTypes={title:a.a.string.isRequired,GraphSVG:a.a.elementType.isRequired,showIcons:a.a.bool},PreviewGraph.defaultProps={showIcons:!0}}).call(this,n(4))},418:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(14);function a(e,t){return Object(r.sumBy)(e,t)||0}},44:function(e,t,n){"use strict";(function(e){var r=n(6),a=n.n(r),i=n(1),o=n.n(i),c=n(11),s=n.n(c),l=n(24);function PreviewBlock(t){var n,r,i=t.className,o=t.width,c=t.height,u=t.shape,d=t.padding,g=t.smallWidth,f=t.smallHeight,p=t.tabletWidth,m=t.tabletHeight,b=t.desktopWidth,v=t.desktopHeight,h=Object(l.e)(),O={width:(n={},a()(n,l.b,g),a()(n,l.c,p),a()(n,l.a,b),a()(n,l.d,b),n),height:(r={},a()(r,l.b,f),a()(r,l.c,m),a()(r,l.a,v),a()(r,l.d,b),r)};return e.createElement("div",{className:s()("googlesitekit-preview-block",i,{"googlesitekit-preview-block--padding":d}),style:{width:O.width[h]||o,height:O.height[h]||c}},e.createElement("div",{className:s()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(4))},47:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return i}));var r={BOXES:"boxes",COMPOSITE:"composite"},a={QUARTER:"quarter",HALF:"half",FULL:"full"},i="core/widgets"},54:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,r=t.reconnectURL,a=t.noPrefix;if(!n)return null;var s=n;void 0!==a&&a||(s=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),r&&Object(i.a)(r)&&(s=s+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),r));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(s,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:a.a.string.isRequired,reconnectURL:a.a.string,noPrefix:a.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},57:function(e,t,n){"use strict";(function(e){var r,a;n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var i=new Set((null===(r=e)||void 0===r||null===(a=r._googlesitekitBaseData)||void 0===a?void 0:a.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;return t instanceof Set&&t.has(e)}}).call(this,n(28))},576:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActivateAnalyticsCTA}));var r=n(15),a=n.n(r),i=n(1),o=n.n(i),c=n(0),s=n(2),l=n(10),u=n(3),d=n(19),g=n(8),f=n(32),p=n(159),m=n(398),b=n(121);function ActivateAnalyticsCTA(t){var n=t.children,r=Object(p.a)("analytics-4"),i=Object(m.a)("analytics-4"),o=Object(u.useSelect)((function(e){return e(d.a).isModuleActive("analytics-4")})),v=Object(u.useSelect)((function(e){return(0,e(d.a).isModuleAvailable)("analytics-4")&&!!e(g.r)})),h=Object(c.useState)(!1),O=a()(h,2),y=O[0],E=O[1],k=Object(u.useSelect)((function(e){if(!v)return!1;var t=e(g.r).getAdminReauthURL();return!!t&&e(f.a).isNavigatingTo(t)})),j=Object(u.useSelect)((function(e){return!!v&&e(d.a).isFetchingSetModuleActivation("analytics-4",!0)})),_=Object(b.a)(E,3e3);Object(c.useEffect)((function(){j||k?E(!0):_(!1)}),[j,k,_]);var S=o?i:r;return v&&S?e.createElement("div",{className:"googlesitekit-analytics-cta"},e.createElement("div",{className:"googlesitekit-analytics-cta__preview-graphs"},n),e.createElement("div",{className:"googlesitekit-analytics-cta__details"},e.createElement("p",{className:"googlesitekit-analytics-cta--description"},Object(s.__)("See how many people visit your site from Search and track how you’re achieving your goals","google-site-kit")),e.createElement(l.SpinnerButton,{onClick:S,isSaving:y},o?Object(s.__)("Complete setup","google-site-kit"):Object(s.__)("Set up Google Analytics","google-site-kit")))):null}ActivateAnalyticsCTA.propTypes={children:o.a.node.isRequired}}).call(this,n(4))},577:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M0 3.01l.443.387 1.755-1.534v3.344h.628V1.863L4.578 3.4l.446-.39L2.512.811 0 3.009z",fill:"currentColor"});t.a=function SvgArrowUp(e){return r.createElement("svg",a({viewBox:"0 0 6 6",fill:"none"},e),i)}},58:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=r.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return r.createElement("svg",a({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),i,o)}},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(39);function a(e){return function(){e[r.a]=e[r.a]||[],e[r.a].push(arguments)}}},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(0),a=Object(r.createContext)(""),i=(a.Consumer,a.Provider);t.b=a},66:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a}));var r="modules/search-console",a=1},681:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M1 24.053l5-4.825 4 2.193 5.333-2.193 7.334 6.579 6-1.754 3-4.825 4.666 6.579 3.334-1.754L47.333 28 55 19.228l4.333 2.193 5.334-3.509 2 1.316h6L81.333 3 84 9.579l2.333-1.754L89 13.088l12-5.263",stroke:"#CCC",strokeWidth:2});t.a=function SvgCtaGraphVisitors(e){return r.createElement("svg",a({viewBox:"0 0 102 30",fill:"none"},e),i)}},693:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M86.66 6.101a22.375 22.375 0 016.226-1.09l.215 7.871a14.544 14.544 0 00-4.046.709L86.66 6.1z",fill:"#DEDEDE"}),o=r.createElement("path",{d:"M75.423 14.275a22.544 22.544 0 0110.57-7.945l2.628 7.41a14.655 14.655 0 00-6.871 5.164l-6.327-4.63z",fill:"#C6C6C6"}),c=r.createElement("path",{d:"M75.317 40.725a22.482 22.482 0 01-4.226-12.872 22.637 22.637 0 013.925-13l6.47 4.426a14.714 14.714 0 00-2.552 8.45c.035 3.01.995 5.932 2.747 8.367l-6.364 4.63z",fill:"#F1F1F1"}),s=r.createElement("path",{d:"M106.601 45.702a22.401 22.401 0 01-16.346 4.074 22.282 22.282 0 01-14.517-8.485l6.217-4.827a14.483 14.483 0 009.436 5.515 14.562 14.562 0 0010.625-2.648l4.585 6.371z",fill:"#959595"}),l=r.createElement("path",{d:"M93.59 5c4.673 0 9.223 1.466 13.013 4.194a22.369 22.369 0 018.129 11.018 22.624 22.624 0 01-7.567 25.067l-4.783-6.223a14.703 14.703 0 004.919-16.293 14.538 14.538 0 00-5.284-7.162 14.477 14.477 0 00-8.458-2.726L93.59 5z",fill:"#C7C7C7"}),u=r.createElement("circle",{cx:83.5,cy:56.899,r:1.5,fill:"#959595"}),d=r.createElement("circle",{cx:90.5,cy:56.899,r:1.5,fill:"#C7C7C7"}),g=r.createElement("circle",{cx:97.5,cy:56.899,r:1.5,fill:"#DEDEDE"}),f=r.createElement("circle",{cx:104.5,cy:56.899,r:1.5,fill:"#F1F1F1"}),p=r.createElement("path",{stroke:"#ECE9F1",strokeWidth:.937,strokeLinecap:"round",d:"M.468 58.531h55.064"}),m=r.createElement("path",{stroke:"#ECE9F1",strokeWidth:.468,strokeLinecap:"round",d:"M.234 44.765h55.532M.234 30.765h55.532M.234 16.766h55.532"}),b=r.createElement("path",{opacity:.08,d:"M25.531 47.668c-4.138-1.288-5.95-4.746-9.87-5.24-4.053-.51-7.2 12.53-15.661 13.777V59h56V35.07c-2.25-.486-4.367-17.89-9.25-16.601-4.882 1.288-8.475 20.892-12.365 14.578-3.89-6.313-4.716 15.91-8.854 14.622z",fill:"url(#cta-graph-traffic_svg__paint0_linear_435_1677)"}),v=r.createElement("path",{d:"M2.766 1.116L.883 6.3h-.77L2.281.612h.496l-.011.504zM4.344 6.3L2.457 1.116 2.445.612h.496L5.117 6.3h-.773zm-.098-2.106v.618H1.051v-.618h3.195zM6.555.3v6h-.727v-6h.727zM8.5.3v6h-.727v-6H8.5zm5.73 5.023v-3.25h.727V6.3h-.691l-.036-.977zm.137-.89l.301-.008c0 .281-.03.541-.09.781a1.678 1.678 0 01-.281.617c-.13.175-.3.311-.512.41a1.845 1.845 0 01-.77.145c-.205 0-.394-.03-.566-.09a1.132 1.132 0 01-.437-.277 1.262 1.262 0 01-.285-.489 2.355 2.355 0 01-.098-.722V2.073h.723v2.735c0 .19.02.347.062.472.044.123.103.22.176.293.075.07.159.12.25.149.094.028.19.043.289.043.307 0 .55-.059.73-.176.18-.12.309-.28.387-.48.08-.204.121-.429.121-.676zm4.152.746c0-.104-.023-.2-.07-.29-.044-.09-.137-.173-.277-.245-.138-.076-.346-.141-.625-.196a4.95 4.95 0 01-.637-.176 1.931 1.931 0 01-.48-.246c-.13-.096-.23-.21-.301-.34a.948.948 0 01-.106-.457c0-.166.037-.324.11-.472.075-.149.18-.28.316-.395.138-.114.304-.204.496-.27.193-.064.408-.097.645-.097.338 0 .627.06.867.18s.423.28.55.48c.128.198.192.418.192.66h-.722a.62.62 0 00-.106-.34.779.779 0 00-.3-.277.971.971 0 00-.481-.11c-.2 0-.363.032-.488.095a.627.627 0 00-.27.23.58.58 0 00-.043.508c.029.06.078.116.148.168.07.05.17.096.297.14.128.045.29.089.489.133.346.078.631.172.855.282.224.109.39.243.5.402.11.159.164.351.164.578a1.126 1.126 0 01-.45.906 1.65 1.65 0 01-.515.258c-.198.06-.42.09-.668.09-.372 0-.687-.066-.945-.2a1.462 1.462 0 01-.586-.515c-.133-.21-.2-.434-.2-.668h.727c.01.198.068.356.172.473a.877.877 0 00.383.246c.151.047.3.07.45.07.197 0 .363-.026.495-.078a.69.69 0 00.31-.215.498.498 0 00.105-.312zm3.426 1.199a2.08 2.08 0 01-.8-.149 1.817 1.817 0 01-.614-.425c-.169-.183-.3-.399-.39-.649a2.38 2.38 0 01-.137-.82v-.164c0-.344.05-.65.152-.918.102-.27.24-.5.414-.688.175-.187.373-.329.594-.425.221-.097.45-.145.688-.145.302 0 .562.052.78.156.222.105.403.25.544.438.14.185.245.404.312.656.068.25.102.524.102.82v.325h-3.156V3.8h2.433v-.055c-.01-.187-.05-.37-.117-.547a.983.983 0 00-.313-.437c-.143-.115-.338-.172-.585-.172a.984.984 0 00-.809.41c-.099.135-.176.3-.23.496-.055.195-.082.42-.082.676v.164c0 .2.027.39.081.566.058.175.14.328.247.461.109.133.24.237.394.313.156.075.334.113.531.113.256 0 .472-.052.649-.156.177-.104.332-.244.465-.418l.437.348c-.091.138-.207.269-.348.394-.14.125-.313.227-.519.305a2.012 2.012 0 01-.723.117zm3.211-3.64V6.3h-.722V2.073h.703l.02.664zm1.32-.688l-.003.672a1.827 1.827 0 00-.352-.031c-.167 0-.314.025-.441.078a.914.914 0 00-.325.218 1.052 1.052 0 00-.21.336c-.05.128-.082.268-.098.422l-.203.117c0-.255.024-.494.074-.718.052-.224.131-.422.238-.594.107-.175.242-.31.407-.406a1.142 1.142 0 01.914-.094zm3.13 3.129a.61.61 0 00-.07-.29c-.045-.09-.138-.173-.278-.245-.138-.076-.346-.141-.625-.196a4.95 4.95 0 01-.637-.176 1.931 1.931 0 01-.48-.246c-.13-.096-.23-.21-.301-.34a.948.948 0 01-.106-.457c0-.166.037-.324.11-.472.075-.149.18-.28.316-.395.138-.114.303-.204.496-.27.193-.064.408-.097.645-.097.338 0 .627.06.867.18s.423.28.55.48c.128.198.192.418.192.66h-.723a.62.62 0 00-.105-.34.778.778 0 00-.3-.277.97.97 0 00-.481-.11c-.2 0-.363.032-.488.095a.627.627 0 00-.27.23.58.58 0 00-.043.508c.029.06.078.116.148.168.07.05.17.096.297.14.128.045.29.089.489.133.346.078.631.172.855.282.224.109.39.243.5.402.11.159.164.351.164.578a1.126 1.126 0 01-.45.906 1.65 1.65 0 01-.515.258c-.198.06-.42.09-.668.09-.372 0-.687-.066-.945-.2a1.462 1.462 0 01-.586-.515c-.133-.21-.2-.434-.2-.668h.727c.01.198.068.356.172.473a.876.876 0 00.383.246c.151.047.3.07.45.07.197 0 .363-.026.495-.078a.69.69 0 00.309-.215.498.498 0 00.105-.312z",fill:"#B8B8B8"}),h=r.createElement("defs",null,r.createElement("linearGradient",{id:"cta-graph-traffic_svg__paint0_linear_435_1677",x1:19.094,y1:18.399,x2:19.094,y2:66.554,gradientUnits:"userSpaceOnUse"},r.createElement("stop",{stopColor:"#4F4F4F"}),r.createElement("stop",{offset:1,stopColor:"#4F4F4F",stopOpacity:0})));t.a=function SvgCtaGraphTraffic(e){return r.createElement("svg",a({viewBox:"0 0 116 59",fill:"none"},e),i,o,c,s,l,u,d,g,f,p,m,b,v,h)}},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a})),n.d(t,"e",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return s})),n.d(t,"M",(function(){return l})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return g})),n.d(t,"J",(function(){return f})),n.d(t,"I",(function(){return p})),n.d(t,"N",(function(){return m})),n.d(t,"f",(function(){return b})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return O})),n.d(t,"l",(function(){return y})),n.d(t,"m",(function(){return E})),n.d(t,"n",(function(){return k})),n.d(t,"o",(function(){return j})),n.d(t,"q",(function(){return _})),n.d(t,"s",(function(){return S})),n.d(t,"r",(function(){return w})),n.d(t,"t",(function(){return N})),n.d(t,"w",(function(){return D})),n.d(t,"u",(function(){return T})),n.d(t,"v",(function(){return R})),n.d(t,"x",(function(){return C})),n.d(t,"y",(function(){return A})),n.d(t,"A",(function(){return P})),n.d(t,"B",(function(){return L})),n.d(t,"C",(function(){return x})),n.d(t,"D",(function(){return I})),n.d(t,"k",(function(){return M})),n.d(t,"F",(function(){return B})),n.d(t,"z",(function(){return W})),n.d(t,"G",(function(){return F})),n.d(t,"E",(function(){return z})),n.d(t,"i",(function(){return U})),n.d(t,"p",(function(){return V})),n.d(t,"Q",(function(){return G})),n.d(t,"P",(function(){return H}));var r="core/user",a="connected_url_mismatch",i="__global",o="temporary_persist_permission_error",c="adblocker_active",s="googlesitekit_authenticate",l="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",g="googlesitekit_read_shared_module_data",f="googlesitekit_manage_module_sharing_options",p="googlesitekit_delegate_module_sharing_management",m="googlesitekit_update_plugins",b="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",h="kmAnalyticsLeastEngagingPages",O="kmAnalyticsNewVisitors",y="kmAnalyticsPopularAuthors",E="kmAnalyticsPopularContent",k="kmAnalyticsPopularProducts",j="kmAnalyticsReturningVisitors",_="kmAnalyticsTopCities",S="kmAnalyticsTopCitiesDrivingLeads",w="kmAnalyticsTopCitiesDrivingAddToCart",N="kmAnalyticsTopCitiesDrivingPurchases",D="kmAnalyticsTopDeviceDrivingPurchases",T="kmAnalyticsTopConvertingTrafficSource",R="kmAnalyticsTopCountries",C="kmAnalyticsTopPagesDrivingLeads",A="kmAnalyticsTopRecentTrendingPages",P="kmAnalyticsTopTrafficSource",L="kmAnalyticsTopTrafficSourceDrivingAddToCart",x="kmAnalyticsTopTrafficSourceDrivingLeads",I="kmAnalyticsTopTrafficSourceDrivingPurchases",M="kmAnalyticsPagesPerVisit",B="kmAnalyticsVisitLength",W="kmAnalyticsTopReturningVisitorPages",F="kmSearchConsolePopularKeywords",z="kmAnalyticsVisitsPerVisitor",U="kmAnalyticsMostEngagingPages",V="kmAnalyticsTopCategories",G=[b,v,h,O,y,E,k,j,V,_,S,w,N,D,T,R,A,P,L,M,B,W,z,U,V],H=[].concat(G,[F])},70:function(e,t,n){"use strict";var r=n(0);function a(){return(a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=r.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return r.createElement("svg",a({viewBox:"0 0 24 24",fill:"currentColor"},e),i)}},72:function(e,t,n){"use strict";var r=n(15),a=n.n(r),i=n(265),o=n(1),c=n.n(o),s=n(0),l=n(144);function Portal(e){var t=e.children,n=e.slug,r=Object(s.useState)(document.createElement("div")),o=a()(r,1)[0];return Object(i.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(l.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},74:function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var r=n(33),a=n.n(r),i=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:i.a.sanitize(e,t)}};function c(e){var t,n="object"===a()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var r=n(1),a=n.n(r);function IconWrapper(t){var n=t.children,r=t.marginLeft,a=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:r,marginRight:a}},n)}IconWrapper.propTypes={children:a.a.node.isRequired,marginLeft:a.a.number,marginRight:a.a.number}}).call(this,n(4))},77:function(e,t,n){"use strict";(function(e){var r=n(21),a=n.n(r),i=n(25),o=n.n(i),c=n(11),s=n.n(c),l=n(1),u=n.n(l),d=n(0),g=Object(d.forwardRef)((function(t,n){var r=t.label,i=t.className,c=t.hasLeftSpacing,l=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",a()({ref:n},u,{className:s()("googlesitekit-badge",i,{"googlesitekit-badge--has-left-spacing":l})}),r)}));g.displayName="Badge",g.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=g}).call(this,n(4))},79:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(15),a=n.n(r),i=n(188),o=n(133),c={},s=void 0===e?null:e,l=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,r=e.initialWidth,u=void 0===r?0:r,d=e.initialHeight,g=void 0===d?0:d,f=Object(i.a)("undefined"==typeof document?[u,g]:l,t,n),p=a()(f,2),m=p[0],b=p[1],v=function(){return b(l)};return Object(o.a)(s,"resize",v),Object(o.a)(s,"orientationchange",v),m},d=function(e){return u(e)[0]}}).call(this,n(28))},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"s",(function(){return i})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return s})),n.d(t,"g",(function(){return l})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"m",(function(){return p})),n.d(t,"n",(function(){return m})),n.d(t,"h",(function(){return b})),n.d(t,"x",(function(){return v})),n.d(t,"w",(function(){return h})),n.d(t,"y",(function(){return O})),n.d(t,"u",(function(){return y})),n.d(t,"v",(function(){return E})),n.d(t,"f",(function(){return k})),n.d(t,"l",(function(){return j})),n.d(t,"e",(function(){return _})),n.d(t,"t",(function(){return S})),n.d(t,"c",(function(){return w})),n.d(t,"d",(function(){return N})),n.d(t,"b",(function(){return D}));var r="modules/analytics-4",a="account_create",i="property_create",o="webdatastream_create",c="analyticsSetup",s=10,l=1,u="https://www.googleapis.com/auth/tagmanager.readonly",d="enhanced-measurement-form",g="enhanced-measurement-enabled",f="enhanced-measurement-should-dismiss-activation-banner",p="analyticsAccountCreate",m="analyticsCustomDimensionsCreate",b="https://www.googleapis.com/auth/analytics.edit",v="dashboardAllTrafficWidgetDimensionName",h="dashboardAllTrafficWidgetDimensionColor",O="dashboardAllTrafficWidgetDimensionValue",y="dashboardAllTrafficWidgetActiveRowIndex",E="dashboardAllTrafficWidgetLoaded",k={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},j={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},_=[j.CONTACT,j.GENERATE_LEAD,j.SUBMIT_LEAD_FORM],S={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},w="audiencePermissionsSetup",N="audienceTileCustomDimensionCreate",D="audience-selection-panel-expirable-new-badge-"},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var r=n(106);function a(e){try{return new URL(e).pathname}catch(e){}return null}function i(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(r.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),a=e.replace(n.origin,"");if(a.length<t)return a;var i=a.length-Math.floor(t)+1;return"…"+a.substr(i)}},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return j})),n.d(t,"d",(function(){return _})),n.d(t,"e",(function(){return w})),n.d(t,"c",(function(){return N})),n.d(t,"b",(function(){return D}));var r=n(15),a=n.n(r),i=n(33),o=n.n(i),c=n(6),s=n.n(c),l=n(25),u=n.n(l),d=n(14),g=n(63),f=n.n(g),p=n(2);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){s()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=y(e,t),r=n.formatUnit,a=n.formatDecimal;try{return r()}catch(e){return a()}},h=function(e){var t=O(e),n=t.hours,r=t.minutes,a=t.seconds;return a=("0"+a).slice(-2),r=("0"+r).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(r,":").concat(a):"".concat(n,":").concat(r,":").concat(a)},O=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},y=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=O(e),r=n.hours,a=n.minutes,i=n.seconds;return{hours:r,minutes:a,seconds:i,formatUnit:function(){var n=t.unitDisplay,o=b(b({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?w(i,b(b({},o),{},{unit:"second"})):Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?w(i,b(b({},o),{},{unit:"second"})):"",a?w(a,b(b({},o),{},{unit:"minute"})):"",r?w(r,b(b({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(p.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(p.__)("%ds","google-site-kit"),i);if(0===e)return t;var n=Object(p.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(p.__)("%dm","google-site-kit"),a),o=Object(p.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(p.__)("%dh","google-site-kit"),r);return Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),i?t:"",a?n:"",r?o:"").trim()}}},E=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},k=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in millions.
Object(p.__)("%sM","google-site-kit"),w(E(e),e%10==0?{}:t)):1e4<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(E(e))):1e3<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),w(E(e),e%10==0?{}:t)):w(e,{signDisplay:"never",maximumFractionDigits:1})};function j(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=b({},e)),t}function _(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=j(t),r=n.style,a=void 0===r?"metric":r;return"metric"===a?k(e):"duration"===a?v(e,n):"durationISO"===a?h(e):w(e,n)}var S=f()(console.warn),w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?D():n,i=u()(t,["locale"]);try{return new Intl.NumberFormat(r,i).format(e)}catch(t){S("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(r),", ").concat(JSON.stringify(i)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],l={},d=0,g=Object.entries(i);d<g.length;d++){var f=a()(g[d],2),p=f[0],m=f[1];c[p]&&m===c[p]||(s.includes(p)||(l[p]=m))}try{return new Intl.NumberFormat(r,l).format(e)}catch(t){return new Intl.NumberFormat(r).format(e)}},N=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,r=void 0===n?D():n,a=t.style,i=void 0===a?"long":a,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(r,{style:i,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var l=Object(p.__)(", ","google-site-kit");return e.join(l)},D=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var r=n.match(/^(\w{2})?(_)?(\w{2})/);if(r&&r[0])return r[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a}));var r=n(149),a=n.n(r)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i);function ChangeArrow(t){var n=t.direction,r=t.invertColor,a=t.width,i=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":r}),width:a,height:i,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:a.a.string,invertColor:a.a.bool,width:a.a.number,height:a.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},86:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={EXTERNAL:"external",INTERNAL:"internal"}},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(12),a=n.n(r),i=function(e,t){var n=t.dateRangeLength;a()(Array.isArray(e),"report must be an array to partition."),a()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var r=-1*n;return{currentRange:e.slice(r),compareRange:e.slice(2*r,r)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return a.b})),n.d(t,"J",(function(){return a.c})),n.d(t,"F",(function(){return i.a})),n.d(t,"K",(function(){return i.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return m})),n.d(t,"j",(function(){return b})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return j})),n.d(t,"c",(function(){return _})),n.d(t,"e",(function(){return S})),n.d(t,"b",(function(){return w})),n.d(t,"a",(function(){return N})),n.d(t,"f",(function(){return D})),n.d(t,"n",(function(){return T})),n.d(t,"w",(function(){return R})),n.d(t,"p",(function(){return C})),n.d(t,"G",(function(){return A})),n.d(t,"s",(function(){return P})),n.d(t,"v",(function(){return L})),n.d(t,"k",(function(){return x})),n.d(t,"o",(function(){return I.b})),n.d(t,"h",(function(){return I.a})),n.d(t,"t",(function(){return M.b})),n.d(t,"q",(function(){return M.a})),n.d(t,"A",(function(){return M.c})),n.d(t,"x",(function(){return B})),n.d(t,"u",(function(){return W})),n.d(t,"E",(function(){return U})),n.d(t,"D",(function(){return V.a})),n.d(t,"g",(function(){return G})),n.d(t,"L",(function(){return H})),n.d(t,"l",(function(){return q}));var r=n(14),a=n(36),i=n(75),o=n(33),c=n.n(o),s=n(96),l=n.n(s),u=function(e){return l()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(r){var a=t[r];a&&"object"===c()(a)&&!Array.isArray(a)&&(a=e(a)),n[r]=a})),n}(e)))};n(97);var d=n(83);function g(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function f(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function p(e){return e.replace(/\n/gi,"<br>")}function m(e){for(var t=e,n=0,r=[g,f,p];n<r.length;n++){t=(0,r[n])(t)}return t}var b=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=n(15),O=n.n(h),y=n(12),E=n.n(y),k=n(2),j="Invalid dateString parameter, it must be a string.",_='Invalid date range, it must be a string with the format "last-x-days".',S=60,w=60*S,N=24*w,D=7*N;function T(){var e=function(e){return Object(k.sprintf)(
/* translators: %s: number of days */
Object(k._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function R(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(r.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(r.isDate)(n)&&!isNaN(n)}function C(e){E()(Object(r.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function A(e){E()(R(e),j);var t=e.split("-"),n=O()(t,3),r=n[0],a=n[1],i=n[2];return new Date(r,a-1,i)}function P(e,t){return C(x(e,t*N))}function L(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function x(e,t){E()(R(e)||Object(r.isDate)(e)&&!isNaN(e),j);var n=R(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var I=n(98),M=n(80);function B(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function W(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var F=n(27),z=n.n(F),U=function(e){return Array.isArray(e)?z()(e).sort():e},V=n(89);function G(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var r=(t-e)/e;return Number.isNaN(r)||!Number.isFinite(r)?null:r}var H=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},q=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(r.unescape)(t)}},90:function(e,t,n){"use strict";(function(e,r){n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return y})),n.d(t,"a",(function(){return TourTooltips}));var a=n(6),i=n.n(a),o=n(81),c=n(30),s=n(1),l=n.n(s),u=n(2),d=n(3),g=n(23),f=n(7),p=n(36),m=n(107),b=n(18);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var h={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},O={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},y={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},E="feature_tooltip_view",k="feature_tooltip_advance",j="feature_tooltip_return",_="feature_tooltip_dismiss",S="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,a=t.tourID,s=t.gaEventCategory,l=t.callback,u="".concat(a,"-step"),w="".concat(a,"-run"),N=Object(d.useDispatch)(g.b).setValue,D=Object(d.useDispatch)(f.a).dismissTour,T=Object(d.useRegistry)(),R=Object(b.a)(),C=Object(d.useSelect)((function(e){return e(g.b).getValue(u)})),A=Object(d.useSelect)((function(e){return e(g.b).getValue(w)&&!1===e(f.a).isTourDismissed(a)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(a)),N(w,!0)}));var P=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){i()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return r.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,r=e.lifecycle,a=e.size,i=e.status,o=e.type,l=t+1,u="function"==typeof s?s(R):s;o===c.b.TOOLTIP&&r===c.c.TOOLTIP?Object(p.b)(u,E,l):n===c.a.CLOSE&&r===c.c.COMPLETE?Object(p.b)(u,_,l):n===c.a.NEXT&&i===c.d.FINISHED&&o===c.b.TOUR_END&&a===l&&Object(p.b)(u,S,l),r===c.c.COMPLETE&&i!==c.d.FINISHED&&(n===c.a.PREV&&Object(p.b)(u,j,l),n===c.a.NEXT&&Object(p.b)(u,k,l))}(t);var n=t.action,r=t.index,i=t.status,o=t.step,d=t.type,g=n===c.a.CLOSE,f=!g&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),m=[c.d.FINISHED,c.d.SKIPPED].includes(i),b=g&&d===c.b.STEP_AFTER,v=m||b;if(c.b.STEP_BEFORE===d){var h,O,y=o.target;"string"==typeof o.target&&(y=e.document.querySelector(o.target)),null===(h=y)||void 0===h||null===(O=h.scrollIntoView)||void 0===O||O.call(h,{block:"center"})}f?function(e,t){N(u,e+(t===c.a.PREV?-1:1))}(r,n):v&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(a)),D(a)),l&&l(t,T)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:y,locale:O,run:A,showProgress:!0,stepIndex:C,steps:P,styles:h,tooltipComponent:m.a})}TourTooltips.propTypes={steps:l.a.arrayOf(l.a.object).isRequired,tourID:l.a.string.isRequired,gaEventCategory:l.a.oneOfType([l.a.string,l.a.func]).isRequired,callback:l.a.func}}).call(this,n(28),n(4))},93:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var r=n(24),a=n(130);function i(t,n){var r=document.querySelector(t);if(!r)return 0;var a=r.getBoundingClientRect().top,i=o(n);return a+e.scrollY-i}function o(e){var t=c(e),n=document.querySelectorAll(".googlesitekit-navigation, .googlesitekit-entity-header");return t+=Array.from(n).reduce((function(e,t){return e+t.offsetHeight}),0)}function c(t){var n=0,i=document.querySelector(".googlesitekit-header");return n=!!i&&"sticky"===e.getComputedStyle(i).position?function(e){var t=document.querySelector(".googlesitekit-header");if(t){if(e===r.b)return t.offsetHeight;var n=t.getBoundingClientRect().bottom;return n<0?0:n}return 0}(t):function(e){var t=document.querySelector("#wpadminbar");return t&&e!==r.b?t.offsetHeight:0}(t),(n=Object(a.a)(n))<0?0:n}}).call(this,n(28))},95:function(e,t,n){"use strict";(function(e){var r=n(1),a=n.n(r),i=n(11),o=n.n(i),c=n(10),s=n(20);function CTA(t){var n=t.title,r=t.headerText,a=t.headerContent,i=t.description,l=t.ctaLink,u=t.ctaLabel,d=t.ctaLinkExternal,g=t.ctaType,f=t.error,p=t.onClick,m=t["aria-label"],b=t.children;return e.createElement("div",{className:o()("googlesitekit-cta",{"googlesitekit-cta--error":f})},(r||a)&&e.createElement("div",{className:"googlesitekit-cta__header"},r&&e.createElement("h2",{className:"googlesitekit-cta__header_text"},r),a),e.createElement("div",{className:"googlesitekit-cta__body"},n&&e.createElement("h3",{className:"googlesitekit-cta__title"},n),i&&"string"==typeof i&&e.createElement("p",{className:"googlesitekit-cta__description"},i),i&&"string"!=typeof i&&e.createElement("div",{className:"googlesitekit-cta__description"},i),u&&"button"===g&&e.createElement(c.Button,{"aria-label":m,href:l,onClick:p},u),u&&"link"===g&&e.createElement(s.a,{href:l,onClick:p,"aria-label":m,external:d,hideExternalIndicator:d,arrow:!0},u),b))}CTA.propTypes={title:a.a.string.isRequired,headerText:a.a.string,description:a.a.oneOfType([a.a.string,a.a.node]),ctaLink:a.a.string,ctaLinkExternal:a.a.bool,ctaLabel:a.a.string,ctaType:a.a.string,"aria-label":a.a.string,error:a.a.bool,onClick:a.a.func,children:a.a.node,headerContent:a.a.node},CTA.defaultProps={title:"",headerText:"",headerContent:"",description:"",ctaLink:"",ctaLabel:"",ctaType:"link",error:!1,onClick:function(){}},t.a=CTA}).call(this,n(4))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o}));var r=n(239),a=n(85),i=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var i=n.invertColor,o=void 0!==i&&i;return Object(r.a)(e.createElement(a.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var r=n(6),a=n.n(r),i=n(14),o=n(100),c=n(101);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=l(l({},u),t);a.referenceSiteURL&&(a.referenceSiteURL=a.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(a,n),d=Object(c.a)(a,n,s,r),g={},f=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=JSON.stringify(t);g[r]||(g[r]=Object(i.once)(d)),g[r].apply(g,t)};return{enableTracking:function(){a.trackingEnabled=!0},disableTracking:function(){a.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!a.trackingEnabled},trackEvent:d,trackEventOnce:f}}}).call(this,n(28))}},[[1284,1,0]]]);