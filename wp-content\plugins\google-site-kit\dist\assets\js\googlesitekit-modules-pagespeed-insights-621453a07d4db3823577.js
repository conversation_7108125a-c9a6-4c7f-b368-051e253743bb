(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[21],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return o}));var n=r(59),i=r(39),a=r(57);function o(t,r){var o,c=Object(n.a)(r),s=t.activeModules,l=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,g=void 0===d?[]:d,f=t.isAuthenticated,p=t.pluginVersion;return function(){var r=e.document;if(void 0===o&&(o=!!r.querySelector("script[".concat(i.b,"]"))),!o){o=!0;var n=(null==g?void 0:g.length)?g.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:l,plugin_version:p||"",enabled_features:Array.from(a.a).join(","),active_modules:s.join(","),authenticated:f?"1":"0",user_properties:{user_roles:n,user_identifier:u}});var d=r.createElement("script");return d.setAttribute(i.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a),r.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(i.a)}}}}}).call(this,r(28))},101:function(e,t,r){"use strict";r.d(t,"a",(function(){return g}));var n=r(5),i=r.n(n),a=r(6),o=r.n(a),c=r(16),s=r.n(c),l=r(59);function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){o()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function g(e,t,r,n){var a=Object(l.a)(t);return function(){var t=s()(i.a.mark((function t(o,c,s,l){var u;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return r(),u={send_to:"site_kit",event_category:o,event_label:s,value:l},t.abrupt("return",new Promise((function(e){var t,r,i=setTimeout((function(){n.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),s=function(){clearTimeout(i),e()};a("event",c,d(d({},u),{},{event_callback:s})),(null===(t=n._gaUserPrefs)||void 0===t||null===(r=t.ioo)||void 0===r?void 0:r.call(t))&&s()})));case 6:case"end":return t.stop()}}),t)})));return function(e,r,n,i){return t.apply(this,arguments)}}()}},102:function(e,t,r){"use strict";var n=r(123);r.d(t,"a",(function(){return n.a}));var i=r(124);r.d(t,"c",(function(){return i.a}));var a=r(125);r.d(t,"b",(function(){return a.a}))},103:function(e,t,r){"use strict";r.d(t,"d",(function(){return n})),r.d(t,"f",(function(){return i})),r.d(t,"e",(function(){return a})),r.d(t,"b",(function(){return o})),r.d(t,"a",(function(){return c})),r.d(t,"c",(function(){return s})),r.d(t,"h",(function(){return l})),r.d(t,"g",(function(){return u}));var n="modules/pagespeed-insights",i="mobile",a="desktop",o="data_lab",c="data_field",s="data_recommendations",l="dashboardPageSpeedWidgetStrategy",u="dashboardPageSpeedWidgetDataSource"},1065:function(e,t,r){"use strict";(function(e){var n=r(1066),i=r(50);t.a=Object(i.a)({moduleName:"pagespeed-insights"})((function DashboardPageSpeedWidget(t){var r=t.Widget;return e.createElement(r,{className:"googlesitekit-pagespeed-widget",noPadding:!0},e.createElement(n.a,null))}))}).call(this,r(4))},1066:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return DashboardPageSpeed}));var n=r(5),i=r.n(n),a=r(16),o=r.n(a),c=r(6),s=r.n(c),l=r(15),u=r.n(l),d=r(11),g=r.n(d),f=r(240),p=r(0),m=r(2),v=r(45),b=r.n(v),h=r(10),O=r(3),_=r(1067),y=r(20),E=r(1070),k=r(1071),j=r(1073),w=r(1077),A=r(36),R=r(13),N=r(23),S=r(103),D=r(510),T=r(18),L=r(1078);function DashboardPageSpeed(){var t=Object(p.useRef)(),r=Object(p.useState)(!1),n=u()(r,2),a=n[0],c=n[1],l=Object(T.a)(),d=Object(O.useSelect)((function(e){return e(R.c).getCurrentReferenceURL()})),v=Object(O.useSelect)((function(e){return e(N.b).getValue(S.h)}))||S.f,P=Object(O.useSelect)((function(e){return e(N.b).getValue(S.g)}))||S.b,I=Object(O.useSelect)((function(e){var t=e(S.d);return{isFetchingMobile:!t.hasFinishedResolution("getReport",[d,S.f]),errorMobile:t.getErrorForSelector("getReport",[d,S.f]),isFetchingDesktop:!t.hasFinishedResolution("getReport",[d,S.e]),errorDesktop:t.getErrorForSelector("getReport",[d,S.e])}})),M=I.isFetchingMobile,C=I.isFetchingDesktop,x=I.errorMobile,H=I.errorDesktop,F=Object(O.useInViewSelect)((function(e){return e(S.d).getReport(d,S.f)}),[d]),B=Object(O.useInViewSelect)((function(e){return e(S.d).getReport(d,S.e)}),[d]),z=Object(O.useDispatch)(N.b).setValues,U=Object(O.useDispatch)(S.d).invalidateResolution,W=Object(f.a)(t,{threshold:.25}),q=!!(null==W?void 0:W.intersectionRatio),V=v===S.f?M:C;Object(p.useEffect)((function(){q&&!a&&(Object(A.b)("".concat(l,"_pagespeed-widget"),"widget_view"),Object(A.b)("".concat(l,"_pagespeed-widget"),"default_tab_view",P.replace("data_","")),c(!0))}),[q,P,l,a]);var Y=Object(p.useCallback)((function(e){var t;switch(e){case 0:z(s()({},S.g,S.b)),t="lab";break;case 1:z(s()({},S.g,S.a)),t="field";break;case 2:z(s()({},S.g,S.c)),t="recommendations"}Object(A.b)("".concat(l,"_pagespeed-widget"),"tab_select",t)}),[z,l]),G=Object(p.useCallback)((function(e){e.slug===S.e?z(s()({},S.h,S.e)):z(s()({},S.h,S.f))}),[z]),K=Object(p.useCallback)(function(){var e=o()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.preventDefault(),e.next=3,b.a.invalidateCache("modules","pagespeed-insights","pagespeed");case 3:U("getReport",[d,S.e]),U("getReport",[d,S.f]);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[U,d]),$=v===S.f?F:B,J=v===S.f?x:H,Z=Object(O.useSelect)((function(e){return e(S.d).hasFinishedResolution("getReport",[d,v])})),X=Object(O.useInViewSelect)((function(e){if(J)return[];var t=e(S.d).getAuditsWithStackPack(d,v,"wordpress");return t&&Object.keys(t).length?Object.values(t).filter((function(e){var t=e.scoreDisplayMode,r=e.score;return"metricSavings"===t&&r<1})).sort((function(e,t){return e.score===t.score?e.id<t.id?-1:1:e.score-t.score})).map((function(e){return{id:e.id,title:e.title}})):[]}),[d,v,Z]);Object(p.useEffect)((function(){var e,t;(null==F||null===(e=F.loadingExperience)||void 0===e?void 0:e.metrics)&&(null==B||null===(t=B.loadingExperience)||void 0===t?void 0:t.metrics)&&z(s()({},S.g,S.a))}),[F,B,z]);var Q=!d||V&&!$||!P,ee=P===S.a&&["LARGEST_CONTENTFUL_PAINT_MS","CUMULATIVE_LAYOUT_SHIFT_SCORE","FIRST_INPUT_DELAY_MS"].every((function(e){var t,r;return null==$||null===(t=$.loadingExperience)||void 0===t||null===(r=t.metrics)||void 0===r?void 0:r[e]}));return Q?e.createElement("div",{id:"googlesitekit-pagespeed-header",className:"googlesitekit-pagespeed-widget__content-wrapper googlesitekit-pagespeed-widget__content-wrapper--loading"},e.createElement(L.a,null)):e.createElement("div",{id:"googlesitekit-pagespeed-header",className:"googlesitekit-pagespeed-widget__content-wrapper"},e.createElement("div",{className:"googlesitekit-pagespeed-widget__content"},e.createElement("header",{className:"googlesitekit-pagespeed-widget__header",ref:t},e.createElement("div",{className:"googlesitekit-pagespeed-widget__data-src-tabs"},e.createElement(h.TabBar,{activeIndex:[S.b,S.a,S.c].indexOf(P),handleActiveIndexUpdate:Y},e.createElement(h.Tab,{focusOnActivate:!1,"aria-labelledby":"googlesitekit-pagespeed-widget__data-src-tab-".concat(S.b),disabled:V},e.createElement("span",{id:"googlesitekit-pagespeed-widget__data-src-tab-".concat(S.b),className:"mdc-tab__text-label"},Object(m.__)("In the Lab","google-site-kit"))),e.createElement(h.Tab,{focusOnActivate:!1,"aria-labelledby":"googlesitekit-pagespeed-widget__data-src-tab-".concat(S.a),disabled:V},e.createElement("span",{id:"googlesitekit-pagespeed-widget__data-src-tab-".concat(S.a),className:"mdc-tab__text-label"},Object(m.__)("In the Field","google-site-kit"))),e.createElement(h.Tab,{focusOnActivate:!1,"aria-labelledby":"googlesitekit-pagespeed-widget__data-src-tab-".concat(S.c),disabled:V},e.createElement("span",{id:"googlesitekit-pagespeed-widget__data-src-tab-".concat(S.c),className:"mdc-tab__text-label"},Object(m.__)("How to improve","google-site-kit"))))),e.createElement("div",{className:"googlesitekit-pagespeed-widget__device-size-tab-bar-wrapper"},e.createElement(_.a,{activeTab:v,disabled:V,handleDeviceSizeUpdate:G}))),V&&!Q&&e.createElement("div",{className:"googlesitekit-pagespeed-widget__refreshing-progress-bar-wrapper"},e.createElement(h.ProgressBar,{compress:!0})),e.createElement("section",{className:g()({"googlesitekit-pagespeed-widget__refreshing":V})},P===S.b&&e.createElement(E.a,{data:$,error:J}),P===S.a&&e.createElement(k.a,{data:$,error:J}),P===S.c&&e.createElement(j.a,{className:g()({"googlesitekit-pagespeed-widget__refreshing":V}),recommendations:X,referenceURL:d,strategy:v})),(P===S.b||ee)&&e.createElement("div",{className:"googlesitekit-pagespeed-report__row"},e.createElement(h.Button,{className:g()({"googlesitekit-pagespeed__recommendations-cta--hidden":!(null==X?void 0:X.length)}),disabled:V,onClick:function(){return Y(2)}},Object(m.__)("How to improve","google-site-kit"))),e.createElement("div",{className:g()("googlesitekit-pagespeed-report__footer",{"googlesitekit-pagespeed-report__footer--with-action":P===S.b})},P===S.b&&!Q&&e.createElement("div",null,e.createElement(y.a,{onClick:K,disabled:V},Object(m.__)("Run test again","google-site-kit")),e.createElement(D.a,{isSaving:V})),e.createElement(w.a,null))))}}).call(this,r(4))},1067:function(e,t,r){"use strict";(function(e){var n=r(1),i=r.n(n),a=r(0),o=r(2),c=r(10),s=r(1068),l=r(1069);function DeviceSizeTabBar(t){var r=t.activeTab,n=t.disabled,i=void 0!==n&&n,u=t.handleDeviceSizeUpdate,d=t.deviceSizes,g=void 0===d?[{slug:"mobile",label:Object(o.__)("Mobile","google-site-kit"),icon:e.createElement(s.a,{width:"15",height:"22"})},{slug:"desktop",label:Object(o.__)("Desktop","google-site-kit"),icon:e.createElement(l.a,{width:"23",height:"17"})}]:d,f=Object(a.useCallback)((function(e){var t=g[e];u(t,e)}),[g,u]);if(!(null==g?void 0:g.length))return null;var p=g.findIndex((function(e){return e.slug===r}));return e.createElement(c.TabBar,{className:"googlesitekit-device-size-tab-bar",activeIndex:p,handleActiveIndexUpdate:f},g.map((function(t,r){var n=t.icon,a=t.label;return e.createElement(c.Tab,{key:"google-sitekit-device-size-tab-key-".concat(r),"aria-label":a,disabled:i,focusOnActivate:!1},n)})))}DeviceSizeTabBar.propTypes={activeTab:i.a.string,disabled:i.a.bool,deviceSizes:i.a.arrayOf(i.a.shape({label:i.a.string,slug:i.a.string,icon:i.a.node})),handleDeviceSizeUpdate:i.a.func},DeviceSizeTabBar.defaultProps={handleDeviceSizeUpdate:function(){}},t.a=DeviceSizeTabBar}).call(this,r(4))},1068:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=n.createElement("path",{d:"M17 1.01L7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM17 19H7V5h10v14z",fill:"currentColor"});t.a=function SvgDeviceSizeMobileIcon(e){return n.createElement("svg",i({viewBox:"5 1 14 22"},e),a,o)}},1069:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=n.createElement("path",{d:"M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z",fill:"currentColor"});t.a=function SvgDeviceSizeDesktopIcon(e){return n.createElement("svg",i({viewBox:"0 4 24 16"},e),a,o)}},1070:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return LabReportMetrics}));var n=r(1),i=r.n(n),a=r(38),o=r(2),c=r(587),s=r(784),l=r(801),u=r(35),d=r(135),g=r(54);function LabReportMetrics(t){var r,n,i,f,p,m,v=t.data,b=t.error,h=null==v||null===(r=v.lighthouseResult)||void 0===r||null===(n=r.audits)||void 0===n?void 0:n["largest-contentful-paint"],O=null==v||null===(i=v.lighthouseResult)||void 0===i||null===(f=i.audits)||void 0===f?void 0:f["cumulative-layout-shift"],_=null==v||null===(p=v.lighthouseResult)||void 0===p||null===(m=p.audits)||void 0===m?void 0:m["total-blocking-time"];if(b){var y=Object(u.b)(b);return e.createElement("div",{className:"googlesitekit-pagespeed-insights-web-vitals-metrics"},e.createElement("div",{className:"googlesitekit-pagespeed-report__row googlesitekit-pagespeed-report__row--error"},e.createElement(g.a,{message:y}),e.createElement(d.a,{moduleSlug:"pagespeed-insights",error:b})))}return e.createElement("div",{className:"googlesitekit-pagespeed-insights-web-vitals-metrics"},e.createElement("div",{className:"googlesitekit-pagespeed-report__row googlesitekit-pagespeed-report__row--first"},e.createElement("p",null,Object(a.a)(Object(o.__)("Lab data is a snapshot of how your page performs right now, measured in tests we run in a controlled environment. <LearnMoreLink />","google-site-kit"),{LearnMoreLink:e.createElement(s.a,null)}))),e.createElement("table",{className:"googlesitekit-table googlesitekit-table--with-list"},e.createElement("thead",null,e.createElement("tr",null,e.createElement("th",null,Object(o.__)("Metric Name","google-site-kit")),e.createElement("th",null,Object(o.__)("Metric Value","google-site-kit")))),e.createElement("tbody",null,e.createElement(c.a,{title:Object(o._x)("Largest Contentful Paint","core web vitals name","google-site-kit"),description:Object(o.__)("Time it takes for the page to load","google-site-kit"),displayValue:(null==h?void 0:h.displayValue)||"0",category:Object(l.a)((null==h?void 0:h.score)||0)}),e.createElement(c.a,{title:Object(o._x)("Cumulative Layout Shift","core web vitals name","google-site-kit"),description:Object(o.__)("How stable the elements on the page are","google-site-kit"),displayValue:(null==O?void 0:O.displayValue)||"0",category:Object(l.a)((null==O?void 0:O.score)||0)}),e.createElement(c.a,{title:Object(o.__)("Total Blocking Time","google-site-kit"),description:Object(o.__)("How long people had to wait after the page loaded before they could click something","google-site-kit"),displayValue:(null==_?void 0:_.displayValue)||"0",category:Object(l.a)((null==_?void 0:_.score)||0),hintText:e.createElement("br",null),isLast:!0}))))}LabReportMetrics.propTypes={data:i.a.object,error:i.a.object}}).call(this,r(4))},1071:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return FieldReportMetrics}));var n=r(1),i=r.n(n),a=r(11),o=r.n(a),c=r(38),s=r(2),l=r(587),u=r(784),d=r(1072),g=r(54),f=r(135),p=r(35),m=r(531);function FieldReportMetrics(t){var r,n=t.data,i=t.error,a=(null==n||null===(r=n.loadingExperience)||void 0===r?void 0:r.metrics)||{},v=a.LARGEST_CONTENTFUL_PAINT_MS,b=a.CUMULATIVE_LAYOUT_SHIFT_SCORE,h=a.INTERACTION_TO_NEXT_PAINT;if(i){var O=Object(p.b)(i);return e.createElement("div",{className:"googlesitekit-pagespeed-insights-web-vitals-metrics"},e.createElement("div",{className:"googlesitekit-pagespeed-report__row googlesitekit-pagespeed-report__row--error"},e.createElement(g.a,{message:O}),e.createElement(f.a,{moduleSlug:"pagespeed-insights",error:i})))}if(!v&&!b&&!h)return e.createElement("div",{className:"googlesitekit-pagespeed-insights-web-vitals-metrics googlesitekit-pagespeed-insights-web-vitals-metrics--field-data-unavailable"},e.createElement("div",{className:"googlesitekit-pagespeed-insights-web-vitals-metrics__field-data-unavailable-content"},e.createElement("h3",null,Object(s.__)("Field data unavailable","google-site-kit")),e.createElement("p",null,Object(s.__)("Field data shows how real users actually loaded and interacted with your page. We don’t have enough real-world experience and speed data for this page. It may be new, or not enough users with Chrome browsers have visited it yet.","google-site-kit"))));var _=(Math.round((null==v?void 0:v.percentile)/100)/10).toFixed(1),y=((null==b?void 0:b.percentile)/100).toFixed(2);return e.createElement("div",{className:"googlesitekit-pagespeed-insights-web-vitals-metrics"},e.createElement("div",{className:"googlesitekit-pagespeed-report__row googlesitekit-pagespeed-report__row--first"},e.createElement("p",null,Object(c.a)(Object(s.__)("Field data shows how real users actually loaded and interacted with your page over time. <LearnMoreLink />","google-site-kit"),{LearnMoreLink:e.createElement(u.a,null)}))),e.createElement("table",{className:o()("googlesitekit-table","googlesitekit-table--with-list")},e.createElement("thead",null,e.createElement("tr",null,e.createElement("th",null,Object(s.__)("Metric Name","google-site-kit")),e.createElement("th",null,Object(s.__)("Metric Value","google-site-kit")))),e.createElement("tbody",null,e.createElement(l.a,{title:Object(s._x)("Largest Contentful Paint","core web vitals name","google-site-kit"),description:Object(s.__)("Time it takes for the page to load","google-site-kit"),displayValue:Object(s.sprintf)(
/* translators: %s: number of seconds */
Object(s._x)("%s s","duration","google-site-kit"),_),category:null==v?void 0:v.category,isUnavailable:!v}),e.createElement(l.a,{title:Object(s._x)("Cumulative Layout Shift","core web vitals name","google-site-kit"),description:Object(s.__)("How stable the elements on the page are","google-site-kit"),displayValue:y,category:null==b?void 0:b.category,isUnavailable:!b}),e.createElement(l.a,{title:Object(s._x)("Interaction to Next Paint","core web vitals name","google-site-kit"),description:Object(s.__)("How quickly your page responds when people interact with it","google-site-kit"),displayValue:Object(s.sprintf)(
/* translators: %s: number of milliseconds */
Object(s._x)("%s ms","duration","google-site-kit"),null==h?void 0:h.percentile),category:(null==h?void 0:h.category)||m.a,isLast:!0,isUnavailable:!h,hintText:Object(c.a)(Object(s.__)("INP is a new Core Web Vital that replaced FID in March 2024. <LearnMoreLink />","google-site-kit"),{LearnMoreLink:e.createElement(d.a,null)})}))))}FieldReportMetrics.propTypes={data:i.a.object,error:i.a.object}}).call(this,r(4))},1072:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return INPLearnMoreLink}));var n=r(2),i=r(20);function INPLearnMoreLink(){return e.createElement(i.a,{href:"https://web.dev/inp-cwv/",external:!0,"aria-label":Object(n.__)("Learn more about INP replacing FID.","google-site-kit")},Object(n.__)("Learn more","google-site-kit"))}}).call(this,r(4))},1073:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return Recommendations}));var n=r(1),i=r.n(n),a=r(11),o=r.n(a),c=r(2),s=r(103),l=r(1074),u=r(1075),d=r(17);function Recommendations(t){var r=t.className,n=t.recommendations,i=t.referenceURL,a=t.strategy;return(null==n?void 0:n.length)?e.createElement("div",{className:o()("googlesitekit-pagespeed--recommendations",r)},e.createElement("div",{className:"googlesitekit-pagespeed-recommendations__title"},Object(c.__)("Recommendations on how to improve your site","google-site-kit")),n.map((function(t){var r=t.id,n=t.title;return e.createElement(u.a,{key:r,auditID:r,title:n,referenceURL:i,strategy:a})}))):e.createElement(d.e,null,e.createElement(d.k,null,e.createElement(d.a,null,Object(c.__)("No recommendations for now","google-site-kit")),e.createElement(d.a,{className:"googlesitekit-pagespeed__zero-recommendations"},e.createElement(l.a,null))))}Recommendations.propTypes={className:i.a.string,recommendations:i.a.arrayOf(i.a.object),referenceURL:i.a.string.isRequired,strategy:i.a.oneOf([s.f,s.e]).isRequired},Recommendations.defaultProps={className:""}}).call(this,r(4))},1074:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("path",{d:"M281.11 316.17a16 16 0 01-20.69 15.23M118.86 316.17a16 16 0 0020.69 15.23M201 300.22h-66.19a16 16 0 00-16 15.95h0",stroke:"#F29900",strokeLinejoin:"round",strokeWidth:9.4}),o=n.createElement("path",{d:"M198.06 300.22h67.1a16 16 0 0115.95 15.95h0",stroke:"#F29900",strokeLinejoin:"round",strokeWidth:9.4}),c=n.createElement("path",{d:"M249.64 206.74c-1.42 24.59 12.3 59.39 35.05 71.31 15.3 8 34.85 1.6 37.66-16.54C324.46 247.84 316.82 233 308 223c0 0 14.73-2.8 14.73-21.22M150.36 206.74c1.42 24.59-12.3 59.39-35.05 71.31-15.3 8-34.85 1.6-37.66-16.54C75.54 247.84 83.18 233 92 223c0 0-14.73-2.8-14.73-21.22",stroke:"#FBBC04",strokeLinejoin:"round",strokeWidth:9.4}),s=n.createElement("path",{fill:"#F9AB00",d:"M145.23 76.68h109.53v219.7H145.23z"}),l=n.createElement("path",{d:"M172.74 142.94c13.38 16.48 37.19 21.05 54.28 0",stroke:"#FFF",strokeMiterlimit:10,strokeWidth:6.66}),u=n.createElement("path",{stroke:"#F29900",strokeLinejoin:"round",strokeWidth:9.4,d:"M260.42 331.4l-141.56-45.03-10.4 28.08"}),d=n.createElement("path",{stroke:"#F29900",strokeLinejoin:"round",strokeWidth:9.4,d:"M139.55 331.4l141.56-45.03 10.4 28.08"}),g=n.createElement("ellipse",{cx:200,cy:381.74,rx:79.51,ry:8.26,fill:"#F1F3F4"}),f=n.createElement("path",{stroke:"#E8EAED",strokeLinecap:"round",strokeMiterlimit:10,strokeWidth:6,d:"M200 10v31.42M105 35.46l15.71 27.2M35.46 105l27.2 15.71M10 200h31.42M35.46 295l27.2-15.71M364.54 295l-27.2-15.71M390 200h-31.42M364.54 105l-27.2 15.71M295 35.46l-15.71 27.2"});t.a=function SvgZeroStateYellow(e){return n.createElement("svg",i({viewBox:"0 0 400 400",fill:"none"},e),a,o,c,s,l,u,d,g,f)}},1075:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return Recommendation}));var n=r(1),i=r.n(n),a=r(0),o=r(3),c=r(103),s=r(1076),l=r(9),u=r(18);function Recommendation(t){var r=t.auditID,n=t.title,i=t.referenceURL,d=t.strategy,g=Object(u.a)(),f=Object(a.useCallback)((function(){Object(l.I)("".concat(g,"_pagespeed-widget"),"stack_pack_expand",r)}),[r,g]),p=Object(o.useSelect)((function(e){return e(c.d).getStackPackDescription(i,d,r,"wordpress")}));if(!p)return null;var m=Object(l.z)(p.description);return e.createElement(s.a,{id:r,title:n,onOpen:f},e.createElement("div",{dangerouslySetInnerHTML:Object(l.F)(m,{ALLOWED_TAGS:["a","p"],ALLOWED_ATTR:["href","rel","target"]})}))}Recommendation.propTypes={auditID:i.a.string.isRequired,title:i.a.string.isRequired,referenceURL:i.a.string.isRequired,strategy:i.a.oneOf([c.f,c.e]).isRequired}}).call(this,r(4))},1076:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return Accordion}));var n=r(15),i=r.n(n),a=r(1),o=r.n(a),c=r(11),s=r.n(c),l=r(0),u=r(56),d=r(680),g=r(76);function Accordion(t){var r=t.title,n=t.children,a=t.initialOpen,o=t.onOpen,c=t.onClose,f=t.disabled,p=Object(l.useState)(!!a),m=i()(p,2),v=m[0],b=m[1];Object(l.useEffect)((function(){v&&o&&"function"==typeof o?o():!v&&c&&"function"==typeof c&&c()}),[v,c,o]),Object(l.useEffect)((function(){f&&v&&b(!1)}),[f,v]);var h=Object(l.useCallback)((function(e){("keydown"!==e.type||[u.b,u.e].includes(e.keyCode))&&(e.preventDefault(),b(!v))}),[v]);return e.createElement("div",{className:s()("googlesitekit-accordion",{"googlesitekit-accordion--disabled":f})},e.createElement("div",{className:s()("googlesitekit-accordion__header",{"is-active":v}),onClick:h,onKeyDown:h,tabIndex:f?-1:0,role:"button"},r,e.createElement(g.a,null,e.createElement(d.a,{width:12,height:12}))),e.createElement("div",{className:s()("googlesitekit-accordion__content",{"is-active":v})},n))}Accordion.propTypes={title:o.a.node.isRequired,children:o.a.node.isRequired,initialOpen:o.a.bool,onOpen:o.a.func,onClose:o.a.func,disabled:o.a.bool}}).call(this,r(4))},1077:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return ReportDetailsLink}));var n=r(2),i=r(38),a=r(3),o=r(103),c=r(13),s=r(20);function ReportDetailsLink(){var t=Object(a.useSelect)((function(e){return e(c.c).getCurrentReferenceURL()})),r=Object(a.useSelect)((function(e){return e(o.d).getServiceURL({path:"report",query:{url:t}})}));return e.createElement("p",null,Object(i.a)(Object(n.sprintf)(
/* translators: %s: link with translated service name */
Object(n.__)("View details at %s","google-site-kit"),"<a>".concat(Object(n._x)("PageSpeed Insights","Service name","google-site-kit"),"</a>")),{a:e.createElement(s.a,{href:r,external:!0})}))}}).call(this,r(4))},1078:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return DashboardPageSpeedLoading}));var n=r(802),i=r(44);function DashboardPageSpeedLoading(){return e.createElement("div",{className:"googlesitekit-pagespeed-widget__content"},e.createElement("header",{className:"googlesitekit-pagespeed-widget__header"},e.createElement("div",{className:"googlesitekit-pagespeed-widget__data-src-tabs"},e.createElement(n.a,{count:3,smallWidth:"70px",smallHeight:"48px",width:"120px",height:"48px"})),e.createElement("div",{className:"googlesitekit-pagespeed-widget__device-size-tab-bar-wrapper"},e.createElement(n.a,{count:2,width:"56px",height:"32px"}))),e.createElement("section",{className:"googlesitekit-pagespeed-widget__values"},e.createElement(n.a,{count:5,smallWidth:"100%",smallHeight:"90px",width:"100%",height:"78px"})),e.createElement("div",{className:"googlesitekit-pagespeed-report__row"},e.createElement(i.a,{width:"130px",height:"40px"})),e.createElement("div",{className:"googlesitekit-pagespeed-report__footer"},e.createElement(i.a,{width:"224px",height:"40px"})))}}).call(this,r(4))},1079:function(e,t,r){"use strict";(function(e,n){r.d(t,"a",(function(){return SetupSuccessNotification}));var i=r(15),a=r.n(i),o=r(1),c=r.n(o),s=r(2),l=r(3),u=r(41),d=r(115),g=r(168),f=r(92),p=r(147),m=r(24),v=r(93),b=r(22);function SetupSuccessNotification(t){var r=t.id,i=t.Notification,o=Object(m.e)(),c=Object(p.a)("notification"),h=a()(c,2)[1],O=Object(p.a)("slug"),_=a()(O,2)[1],y=Object(l.useDispatch)(u.a).dismissNotification,E=function(){h(void 0),_(void 0)},k="#".concat(b.d);return n.createElement(i,null,n.createElement(d.a,{title:Object(s.__)("Congrats on completing the setup for PageSpeed Insights!","google-site-kit"),description:Object(s.__)("Jump to the bottom of the dashboard to see how fast your home page is","google-site-kit"),dismissCTA:n.createElement(f.a,{id:r,primary:!1,dismissLabel:Object(s.__)("Got it","google-site-kit"),onDismiss:E}),additionalCTA:n.createElement(g.a,{id:r,ctaLabel:Object(s.__)("Show me","google-site-kit"),onCTAClick:function(t){t.preventDefault(),y(r),E(),e.history.replaceState({},"",k),e.scrollTo({top:Object(v.a)(k,o),behavior:"smooth"})}})}))}SetupSuccessNotification.propTypes={id:c.a.string.isRequired,Notification:c.a.elementType.isRequired}}).call(this,r(28),r(4))},115:function(e,t,r){"use strict";(function(e){var n=r(1),i=r.n(n),a=r(11),o=r.n(a),c=r(0),s=r(137),l=r(58),u=r(131),d=r(17),g=Object(c.forwardRef)((function(t,r){var n=t.className,i=t.title,a=t.description,c=t.dismissCTA,g=t.additionalCTA,f=t.reverseCTAs,p=void 0!==f&&f,m=t.type,v=void 0===m?"success":m,b=t.icon;return e.createElement(d.e,{ref:r},e.createElement(d.k,null,e.createElement(d.a,{alignMiddle:!0,size:12,className:o()("googlesitekit-subtle-notification",n,{"googlesitekit-subtle-notification--success":"success"===v,"googlesitekit-subtle-notification--warning":"warning"===v,"googlesitekit-subtle-notification--new-feature":"new-feature"===v})},e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},b,"success"===v&&!b&&e.createElement(s.a,{width:24,height:24}),"warning"===v&&!b&&e.createElement(l.a,{width:24,height:24}),"new-feature"===v&&!b&&e.createElement(u.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,i),e.createElement("p",{className:"googlesitekit-subtle-notification__secondary_description"},a)),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},!p&&c,p&&g,!p&&g,p&&c))))}));g.propTypes={className:i.a.string,title:i.a.node,description:i.a.node,dismissCTA:i.a.node,additionalCTA:i.a.node,reverseCTAs:i.a.bool,type:i.a.oneOf(["success","warning","new-feature"]),icon:i.a.object},t.a=g}).call(this,r(4))},123:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return Cell}));var n=r(21),i=r.n(n),a=r(6),o=r.n(a),c=r(25),s=r.n(c),l=r(1),u=r.n(l),d=r(11),g=r.n(d);function Cell(t){var r,n=t.className,a=t.alignTop,c=t.alignMiddle,l=t.alignBottom,u=t.alignRight,d=t.alignLeft,f=t.smAlignRight,p=t.mdAlignRight,m=t.lgAlignRight,v=t.smSize,b=t.smStart,h=t.smOrder,O=t.mdSize,_=t.mdStart,y=t.mdOrder,E=t.lgSize,k=t.lgStart,j=t.lgOrder,w=t.size,A=t.children,R=s()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",i()({},R,{className:g()(n,"mdc-layout-grid__cell",(r={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":l,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":f,"mdc-layout-grid__cell--align-right-tablet":p,"mdc-layout-grid__cell--align-right-desktop":m},o()(r,"mdc-layout-grid__cell--span-".concat(w),12>=w&&w>0),o()(r,"mdc-layout-grid__cell--span-".concat(E,"-desktop"),12>=E&&E>0),o()(r,"mdc-layout-grid__cell--start-".concat(k,"-desktop"),12>=k&&k>0),o()(r,"mdc-layout-grid__cell--order-".concat(j,"-desktop"),12>=j&&j>0),o()(r,"mdc-layout-grid__cell--span-".concat(O,"-tablet"),8>=O&&O>0),o()(r,"mdc-layout-grid__cell--start-".concat(_,"-tablet"),8>=_&&_>0),o()(r,"mdc-layout-grid__cell--order-".concat(y,"-tablet"),8>=y&&y>0),o()(r,"mdc-layout-grid__cell--span-".concat(v,"-phone"),4>=v&&v>0),o()(r,"mdc-layout-grid__cell--start-".concat(b,"-phone"),4>=b&&b>0),o()(r,"mdc-layout-grid__cell--order-".concat(h,"-phone"),4>=h&&h>0),r))}),A)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,r(4))},124:function(e,t,r){"use strict";(function(e){var n=r(21),i=r.n(n),a=r(25),o=r.n(a),c=r(1),s=r.n(c),l=r(11),u=r.n(l),d=r(0),g=Object(d.forwardRef)((function(t,r){var n=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",i()({ref:r,className:u()("mdc-layout-grid__inner",n)},c),a)}));g.displayName="Row",g.propTypes={className:s.a.string,children:s.a.node},g.defaultProps={className:""},t.a=g}).call(this,r(4))},125:function(e,t,r){"use strict";(function(e){var n=r(21),i=r.n(n),a=r(25),o=r.n(a),c=r(1),s=r.n(c),l=r(11),u=r.n(l),d=r(0),g=Object(d.forwardRef)((function(t,r){var n=t.alignLeft,a=t.fill,c=t.className,s=t.children,l=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",i()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":n,"mdc-layout-grid--collapsed":l,"mdc-layout-grid--fill":a})},d,{ref:r}),s)}));g.displayName="Grid",g.propTypes={alignLeft:s.a.bool,fill:s.a.bool,className:s.a.string,collapsed:s.a.bool,children:s.a.node},g.defaultProps={className:""},t.a=g}).call(this,r(4))},126:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("g",{fill:"none",fillRule:"evenodd"},n.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),n.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return n.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},127:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("g",{fill:"none",fillRule:"evenodd"},n.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),n.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return n.createElement("svg",i({viewBox:"0 0 13 13"},e),a)}},128:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return n.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},1297:function(e,t,r){"use strict";r.r(t);var n=r(3),i=r.n(n),a=r(190),o=r.n(a),c=r(401),s=r.n(c),l=r(363),u=r.n(l),d=r(2),g=r(409),f=r(88),p=r(911),m=r(1065),v=r(0);function b(){return(b=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var h=v.createElement("defs",null,v.createElement("radialGradient",{id:"pagespeed-insights_svg__b",cx:1360.51,cy:10958.05,r:458.83,gradientTransform:"matrix(.5 0 0 .5 -675.69 -5476.24)",gradientUnits:"userSpaceOnUse"},v.createElement("stop",{offset:0,stopColor:"#fff",stopOpacity:.1}),v.createElement("stop",{offset:1,stopColor:"#fff",stopOpacity:0})),v.createElement("linearGradient",{id:"pagespeed-insights_svg__a",x1:1572.36,y1:11164.11,x2:1691.74,y2:11283.49,gradientTransform:"matrix(.5 0 0 .5 -675.69 -5476.24)",gradientUnits:"userSpaceOnUse"},v.createElement("stop",{offset:0,stopColor:"#212121",stopOpacity:.2}),v.createElement("stop",{offset:1,stopColor:"#212121",stopOpacity:0}))),O=v.createElement("path",{d:"M183.93 25.08v121.19a12.53 12.53 0 01-12.54 12.54H12.58A12.53 12.53 0 010 146.27V25.08z",fill:"#e1e1e1"}),_=v.createElement("path",{d:"M12.58 0h158.81a12.53 12.53 0 0112.54 12.54v12.54H0V12.54A12.53 12.53 0 0112.58 0z",fill:"#c2c2c2"}),y=v.createElement("path",{d:"M171.39 0H12.58A12.57 12.57 0 000 12.54v1A12.58 12.58 0 0112.58 1h158.81a12.59 12.59 0 0112.54 12.54v-1A12.58 12.58 0 00171.39 0z",fillOpacity:.2,fill:"#fff"}),E=v.createElement("path",{fill:"#212121",fillOpacity:.1,d:"M0 25.08h183.93v1H0z"}),k=v.createElement("path",{d:"M91.91 62.69a66.76 66.76 0 00-66.76 66.86c0 1.4-.09 3.14 0 4.18h29.13a36.26 36.26 0 010-4.18 37.64 37.64 0 0157.36-32l21-21a66.62 66.62 0 00-40.73-13.86z",fill:"#4285f4"}),j=v.createElement("path",{d:"M132.64 76.52l-21 21a37.59 37.59 0 0117.9 32 36.26 36.26 0 010 4.18h29.27c.08-1 0-2.78 0-4.18a66.73 66.73 0 00-26.17-53z",fill:"#f44336"}),w=v.createElement("circle",{fill:"#eee",cx:16.76,cy:12.54,r:4.18}),A=v.createElement("circle",{fill:"#eee",cx:33.48,cy:12.54,r:4.18}),R=v.createElement("path",{fill:"#212121",fillOpacity:.1,d:"M171.41 157.76H12.54A12.57 12.57 0 010 145.23v1a12.57 12.57 0 0012.54 12.54h158.87a12.58 12.58 0 0012.59-12.5v-1a12.58 12.58 0 01-12.54 12.53z"}),N=v.createElement("path",{d:"M132.86 79.06a2.06 2.06 0 00.39-1.22 2.1 2.1 0 00-2.09-2.09 2 2 0 00-1.28.46l-45.57 35.07a12.53 12.53 0 1018.37 16.41l30.17-48.65z",fill:"#9e9e9e"}),S=v.createElement("path",{d:"M132.82 76.58a2 2 0 01.43 1.26 2.06 2.06 0 01-.39 1.22l-30.17 48.65a12.53 12.53 0 01-18.84 3.05l28.08 28.07h59.47a12.53 12.53 0 0012.54-12.54v-18.61z",fill:"url(#pagespeed-insights_svg__a)"}),D=v.createElement("path",{d:"M183.93 12.54A12.53 12.53 0 00171.39 0H12.58A12.53 12.53 0 000 12.54v133.73a12.53 12.53 0 0012.54 12.54h158.85a12.53 12.53 0 0012.54-12.54z",fill:"url(#pagespeed-insights_svg__b)"});var T=function SvgPagespeedInsights(e){return v.createElement("svg",b({viewBox:"0 0 183.95 158.81"},e),h,O,_,y,E,k,j,w,A,R,N,v.createElement("path",{d:"M84.31 112.33l45.57-35.09a2.06 2.06 0 011.28-.46 2.09 2.09 0 012 1.55 2 2 0 00-2-2.59 2 2 0 00-1.28.46l-45.57 35.07a12.5 12.5 0 00-4.87 9.92v.56a12.49 12.49 0 014.87-9.42z",style:{isolation:"isolate"},opacity:.2,fill:"#fff"}),v.createElement("path",{d:"M132.86 79.06l-30.17 48.65a12.51 12.51 0 01-23.21-5.93v.48a12.52 12.52 0 0023.24 6.5l30.14-48.66a2 2 0 00.39-1.22 1.91 1.91 0 00-.08-.54 2.31 2.31 0 01-.31.72z",style:{isolation:"isolate"},opacity:.2,fill:"#212121"}),S,D)},L=r(103),P=r(41),I=r(22),M=r(1079),C=r(5),x=r.n(C),H=r(6),F=r.n(H),B=r(12),z=r.n(B),U=r(106),W=r(45),q=r.n(W),V=r(48);function Y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function G(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(r),!0).forEach((function(t){F()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Y(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var K,$=Object(V.a)({baseName:"getReport",controlCallback:function(e){var t=e.strategy,r=e.url;return q.a.get("modules","pagespeed-insights","pagespeed",{strategy:t,url:r})},reducerCallback:function(e,t,r){var n=r.strategy,i=r.url;return G(G({},e),{},{reports:G(G({},e.reports),{},F()({},"".concat(n,"::").concat(i),G({},t)))})},argsToParams:function(e,t){return{strategy:t,url:e}},validateParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.strategy,r=e.url;z()(Object(U.a)(r),"a valid url is required to fetch a report."),z()("string"==typeof t,"a valid strategy is required to fetch a report.")}}),J={getReport:x.a.mark((function e(t,r){return x.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t&&r){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,$.actions.fetchGetReport(t,r);case 4:case"end":return e.stop()}}),e)}))},Z={getReport:function(e,t,r){return e.reports["".concat(r,"::").concat(t)]},getAudits:Object(n.createRegistrySelector)((function(e){return function(t,r,n){var i=e(L.d).getReport(r,n);if(void 0!==i){var a=((i||{}).lighthouseResult||{}).audits;return a||{}}}})),getAuditsWithStackPack:Object(n.createRegistrySelector)((function(e){return function(t,r,n,i){var a=e(L.d).getAudits(r,n);if(!a)return{};var o={};return Object.keys(a).forEach((function(t){e(L.d).getStackPackDescription(r,n,t,i)&&(o[t]=a[t])})),o}})),getStackPackDescription:Object(n.createRegistrySelector)((function(e){return function(t,r,n,i,a){var o=e(L.d).getReport(r,n);if(void 0!==o){var c=((o||{}).lighthouseResult||[]).stackPacks;if(!Array.isArray(c))return null;var s=c.find((function(e){var t=e.id,r=e.descriptions;return t===a&&!!r[i]}));return s?{id:s.id,icon:s.iconDataURL,title:s.title,description:s.descriptions[i]}:null}}}))},X=Object(n.combineStores)($,{initialState:{reports:{}},resolvers:J,selectors:Z}),Q=(X.initialState,X.actions,X.controls,X.reducer,X.resolvers,X.selectors,X),ee=r(157),te={selectors:{getServiceURL:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.path,n=t.query,i="https://pagespeed.web.dev";if(r){var a=r.match(/^\//)?r:"/".concat(r);return Object(ee.a)("".concat(i).concat(a),n)}return Object(ee.a)(i,n)}}},re=o.a.createModuleStore("pagespeed-insights",{storeName:L.d,requiresSetup:!1,settingSlugs:["ownerID"]}),ne=Object(n.combineStores)(re,Q,te),ie={"setup-success-notification-psi":{Component:M.a,areaSlug:P.b.BANNERS_BELOW_NAV,viewContexts:[I.n],checkRequirements:function(){var e=Object(g.a)(location.href,"notification"),t=Object(g.a)(location.href,"slug");return"authentication_success"===e&&"pagespeed-insights"===t}}};i.a.registerStore(L.d,ne),o.a.registerModule("pagespeed-insights",{storeName:L.d,SettingsViewComponent:p.a,Icon:T,features:[Object(d.__)("Website performance reports for mobile and desktop will be disabled","google-site-kit")],overrideSetupSuccessNotification:!0}),(K=s.a).registerWidget("pagespeedInsightsWebVitals",{Component:m.a,width:K.WIDGET_WIDTHS.FULL,wrapWidget:!1,modules:["pagespeed-insights"]},[f.AREA_MAIN_DASHBOARD_SPEED_PRIMARY,f.AREA_ENTITY_DASHBOARD_SPEED_PRIMARY]),function(e){for(var t in ie)e.registerNotification(t,ie[t])}(u.a)},13:function(e,t,r){"use strict";r.d(t,"c",(function(){return n})),r.d(t,"a",(function(){return i})),r.d(t,"b",(function(){return a}));var n="core/site",i="primary",a="secondary"},130:function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));var n=r(14),i=function(e){return Object(n.isFinite)(e)?e:0}},131:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return n.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},132:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return InfoTooltip}));var n=r(11),i=r.n(n),a=r(1),o=r.n(a),c=r(10),s=r(325);function InfoTooltip(t){var r=t.onOpen,n=t.title,a=t.tooltipClassName;return n?e.createElement(c.Tooltip,{className:"googlesitekit-info-tooltip",tooltipClassName:i()("googlesitekit-info-tooltip__content",a),title:n,placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,interactive:!0,onOpen:r},e.createElement("span",null,e.createElement(s.a,{width:"16",height:"16"}))):null}InfoTooltip.propTypes={onOpen:o.a.func,title:o.a.oneOfType([o.a.string,o.a.element]),tooltipClassName:o.a.string}}).call(this,r(4))},135:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return ReportErrorActions}));var n=r(6),i=r.n(n),a=r(1),o=r.n(a),c=r(0),s=r(38),l=r(2),u=r(3),d=r(10),g=r(13),f=r(19),p=r(35),m=r(34),v=r(20);function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){i()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ReportErrorActions(t){var r=t.moduleSlug,n=t.error,i=t.GetHelpLink,a=t.hideGetHelpLink,o=t.buttonVariant,b=t.onRetry,O=t.onRequestAccess,_=t.getHelpClassName,y=t.RequestAccessButton,E=t.RetryButton,k=Object(m.a)(),j=Object(u.useSelect)((function(e){return e(f.a).getModuleStoreName(r)})),w=Object(u.useSelect)((function(e){var t;return"function"==typeof(null===(t=e(j))||void 0===t?void 0:t.getServiceEntityAccessURL)?e(j).getServiceEntityAccessURL():null})),A=Array.isArray(n)?n:[n],R=Object(u.useSelect)((function(e){return A.map((function(t){var r,n=null===(r=e(j))||void 0===r?void 0:r.getSelectorDataForError(t);return h(h({},t),{},{selectorData:n})}))})),N=null==R?void 0:R.filter((function(e){return Object(p.d)(e,e.selectorData)&&"getReport"===e.selectorData.name})),S=!!N.length,D=Object(u.useSelect)((function(e){var t=h({},S?N[0]:A[0]);return Object(p.e)(t)&&(t.code="".concat(r,"_insufficient_permissions")),e(g.c).getErrorTroubleshootingLinkURL(t)})),T=Object(u.useDispatch)(),L=A.some((function(e){return Object(p.e)(e)})),P=Object(c.useCallback)((function(){N.forEach((function(e){var t=e.selectorData;T(t.storeName).invalidateResolution(t.name,t.args)})),null==b||b()}),[T,N,b]),I=w&&L&&!k;return e.createElement("div",{className:"googlesitekit-report-error-actions"},I&&("function"==typeof y?e.createElement(y,{requestAccessURL:w}):e.createElement(d.Button,{onClick:O,href:w,target:"_blank",danger:"danger"===o,tertiary:"tertiary"===o},Object(l.__)("Request access","google-site-kit"))),S&&e.createElement(c.Fragment,null,"function"==typeof E?e.createElement(E,{handleRetry:P}):e.createElement(d.Button,{onClick:P,danger:"danger"===o,tertiary:"tertiary"===o},Object(l.__)("Retry","google-site-kit")),!a&&e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(s.a)(Object(l.__)("Retry didn’t work? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(v.a,{href:D,external:!0,hideExternalIndicator:!0},Object(l.__)("Get help","google-site-kit"))}))),!S&&!a&&e.createElement("div",{className:_},"function"==typeof i?e.createElement(i,{linkURL:D}):e.createElement(v.a,{href:D,external:!0,hideExternalIndicator:!0},Object(l.__)("Get help","google-site-kit"))))}ReportErrorActions.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired,GetHelpLink:o.a.elementType,hideGetHelpLink:o.a.bool,buttonVariant:o.a.string,onRetry:o.a.func,onRequestAccess:o.a.func,getHelpClassName:o.a.string,RequestAccessButton:o.a.elementType,RetryButton:o.a.elementType}}).call(this,r(4))},137:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return n.createElement("svg",i({viewBox:"0 0 24 24",fill:"none"},e),a)}},147:function(e,t,r){"use strict";(function(e){var n=r(6),i=r.n(n),a=r(15),o=r.n(a),c=r(0),s=r(409),l=r(157);t.a=function(t,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,a=Object(c.useState)(Object(s.a)(n.location.href,t)||r),u=o()(a,2),d=u[0],g=u[1],f=function(e){g(e);var r=Object(l.a)(n.location.href,i()({},t,e));n.history.replaceState(null,"",r)};return[d,f]}}).call(this,r(28))},168:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return CTALinkSubtle}));var n=r(5),i=r.n(n),a=r(16),o=r.n(a),c=r(1),s=r.n(c),l=r(11),u=r.n(l),d=r(73),g=r(10),f=r(70);function CTALinkSubtle(t){var r=t.id,n=t.ctaLink,a=t.ctaLabel,c=t.onCTAClick,s=t.isCTALinkExternal,l=void 0!==s&&s,p=t.gaTrackingEventArgs,m=t.tertiary,v=void 0!==m&&m,b=t.isSaving,h=void 0!==b&&b,O=Object(d.a)(r,null==p?void 0:p.category),_=function(){var e=o()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==c?void 0:c(t);case 2:O.confirm(null==p?void 0:p.label,null==p?void 0:p.value);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(g.Button,{className:u()("googlesitekit-subtle-notification__cta",{"googlesitekit-subtle-notification__cta--spinner__running":h}),href:n,onClick:_,target:l?"_blank":"_self",trailingIcon:l?e.createElement(f.a,{width:14,height:14}):void 0,icon:h?e.createElement(g.CircularProgress,{size:14}):void 0,tertiary:v},a)}CTALinkSubtle.propTypes={id:s.a.string,ctaLink:s.a.string,ctaLabel:s.a.string,onCTAClick:s.a.func,isCTALinkExternal:s.a.bool,gaTrackingEventArgs:s.a.shape({label:s.a.string,value:s.a.string}),tertiary:s.a.bool,isSaving:s.a.bool}}).call(this,r(4))},17:function(e,t,r){"use strict";var n=r(254);r.d(t,"i",(function(){return n.a}));var i=r(319);r.d(t,"f",(function(){return i.a}));var a=r(320);r.d(t,"h",(function(){return a.a}));var o=r(321);r.d(t,"j",(function(){return o.a}));var c=r(318);r.d(t,"g",(function(){return c.a}));var s=r(91),l=r.n(s);r.d(t,"b",(function(){return l.a})),r.d(t,"c",(function(){return s.DialogContent})),r.d(t,"d",(function(){return s.DialogFooter}));var u=r(102);r.d(t,"a",(function(){return u.a})),r.d(t,"e",(function(){return u.b})),r.d(t,"k",(function(){return u.c}))},18:function(e,t,r){"use strict";var n=r(0),i=r(61);t.a=function(){return Object(n.useContext)(i.b)}},19:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return i}));var n="core/modules",i="insufficient_module_dependencies"},190:function(e,t){e.exports=googlesitekit.modules},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,r){"use strict";(function(e){var n=r(21),i=r.n(n),a=r(25),o=r.n(a),c=r(11),s=r.n(c),l=r(1),u=r.n(l),d=r(146),g=r(0),f=r(2),p=r(126),m=r(127),v=r(128),b=r(70),h=r(76),O=Object(g.forwardRef)((function(t,r){var n,a=t["aria-label"],c=t.secondary,l=void 0!==c&&c,u=t.arrow,g=void 0!==u&&u,O=t.back,_=void 0!==O&&O,y=t.caps,E=void 0!==y&&y,k=t.children,j=t.className,w=void 0===j?"":j,A=t.danger,R=void 0!==A&&A,N=t.disabled,S=void 0!==N&&N,D=t.external,T=void 0!==D&&D,L=t.hideExternalIndicator,P=void 0!==L&&L,I=t.href,M=void 0===I?"":I,C=t.inverse,x=void 0!==C&&C,H=t.noFlex,F=void 0!==H&&H,B=t.onClick,z=t.small,U=void 0!==z&&z,W=t.standalone,q=void 0!==W&&W,V=t.linkButton,Y=void 0!==V&&V,G=t.to,K=t.leadingIcon,$=t.trailingIcon,J=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),Z=M||G||!B?G?"ROUTER_LINK":T?"EXTERNAL_LINK":"LINK":S?"BUTTON_DISABLED":"BUTTON",X="BUTTON"===Z||"BUTTON_DISABLED"===Z?"button":"ROUTER_LINK"===Z?d.b:"a",Q=("EXTERNAL_LINK"===Z&&(n=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===Z&&(n=Object(f._x)("(disabled)","screen reader text","google-site-kit")),n?a?"".concat(a," ").concat(n):"string"==typeof k?"".concat(k," ").concat(n):void 0:a),ee=K,te=$;return _&&(ee=e.createElement(v.a,{width:14,height:14})),T&&!P&&(te=e.createElement(b.a,{width:14,height:14})),g&&!x&&(te=e.createElement(p.a,{width:14,height:14})),g&&x&&(te=e.createElement(m.a,{width:14,height:14})),e.createElement(X,i()({"aria-label":Q,className:s()("googlesitekit-cta-link",w,{"googlesitekit-cta-link--secondary":l,"googlesitekit-cta-link--inverse":x,"googlesitekit-cta-link--small":U,"googlesitekit-cta-link--caps":E,"googlesitekit-cta-link--danger":R,"googlesitekit-cta-link--disabled":S,"googlesitekit-cta-link--standalone":q,"googlesitekit-cta-link--link-button":Y,"googlesitekit-cta-link--no-flex":!!F}),disabled:S,href:"LINK"!==Z&&"EXTERNAL_LINK"!==Z||S?void 0:M,onClick:B,rel:"EXTERNAL_LINK"===Z?"noopener noreferrer":void 0,ref:r,target:"EXTERNAL_LINK"===Z?"_blank":void 0,to:G},J),!!ee&&e.createElement(h.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},k),!!te&&e.createElement(h.a,{marginLeft:5},te))}));O.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=O}).call(this,r(4))},22:function(e,t,r){"use strict";r.d(t,"n",(function(){return n})),r.d(t,"l",(function(){return i})),r.d(t,"o",(function(){return a})),r.d(t,"m",(function(){return o})),r.d(t,"t",(function(){return c})),r.d(t,"h",(function(){return s})),r.d(t,"s",(function(){return l})),r.d(t,"i",(function(){return u})),r.d(t,"j",(function(){return d})),r.d(t,"r",(function(){return g})),r.d(t,"k",(function(){return f})),r.d(t,"u",(function(){return p})),r.d(t,"v",(function(){return m})),r.d(t,"q",(function(){return v})),r.d(t,"p",(function(){return b})),r.d(t,"b",(function(){return h})),r.d(t,"e",(function(){return O})),r.d(t,"a",(function(){return _})),r.d(t,"d",(function(){return y})),r.d(t,"c",(function(){return E})),r.d(t,"f",(function(){return k})),r.d(t,"g",(function(){return j}));var n="mainDashboard",i="entityDashboard",a="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",s="activation",l="splash",u="adminBar",d="adminBarViewOnly",g="settings",f="adBlockingRecovery",p="wpDashboard",m="wpDashboardViewOnly",v="moduleSetup",b="metricSelection",h="key-metrics",O="traffic",_="content",y="speed",E="monetization",k=[n,i,a,o,c,l,g,v,b],j=[a,o,d,m]},23:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"a",(function(){return i}));var n="core/ui",i="activeContextID"},24:function(e,t,r){"use strict";r.d(t,"d",(function(){return i})),r.d(t,"a",(function(){return a})),r.d(t,"c",(function(){return o})),r.d(t,"b",(function(){return c})),r.d(t,"e",(function(){return s}));var n=r(79),i="xlarge",a="desktop",o="tablet",c="small";function s(){var e=Object(n.a)();return e>1280?i:e>960?a:e>600?o:c}},3:function(e,t){e.exports=googlesitekit.data},325:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("path",{d:"M7.334 11.333h1.333v-4H7.334v4zM8.001 6a.658.658 0 00.667-.667.605.605 0 00-.2-.467.605.605 0 00-.467-.2.658.658 0 00-.667.667c0 .189.061.35.183.483A.69.69 0 008.001 6zm0 8.666a6.583 6.583 0 01-2.6-.516 6.85 6.85 0 01-2.117-1.434A6.85 6.85 0 011.851 10.6 6.582 6.582 0 011.334 8c0-.923.172-1.79.517-2.6a6.85 6.85 0 011.433-2.117c.6-.6 1.306-1.072 2.117-1.417A6.404 6.404 0 018 1.333c.922 0 1.789.178 2.6.533a6.618 6.618 0 012.116 1.417c.6.6 1.072 1.306 1.417 2.117.355.81.533 1.677.533 2.6 0 .922-.178 1.789-.533 2.6a6.619 6.619 0 01-1.417 2.116 6.85 6.85 0 01-2.116 1.434 6.583 6.583 0 01-2.6.516zm0-1.333c1.489 0 2.75-.517 3.783-1.55s1.55-2.294 1.55-3.783c0-1.49-.517-2.75-1.55-3.784-1.033-1.033-2.294-1.55-3.783-1.55-1.49 0-2.75.517-3.784 1.55C3.184 5.25 2.667 6.511 2.667 8c0 1.489.517 2.75 1.55 3.783 1.034 1.033 2.295 1.55 3.784 1.55z",fill:"currentColor"});t.a=function SvgInfoGreen(e){return n.createElement("svg",i({viewBox:"0 0 16 16",fill:"none"},e),a)}},34:function(e,t,r){"use strict";r.d(t,"a",(function(){return a}));var n=r(22),i=r(18);function a(){var e=Object(i.a)();return n.g.includes(e)}},35:function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"f",(function(){return c})),r.d(t,"e",(function(){return s})),r.d(t,"c",(function(){return l})),r.d(t,"d",(function(){return u})),r.d(t,"b",(function(){return d}));r(14);var n=r(2),i="missing_required_scopes",a="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===i}function s(e){var t;return[a,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function l(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||s(e)||c(e)||l(e))}function d(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(n.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(n.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},36:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return _})),r.d(t,"b",(function(){return h})),r.d(t,"c",(function(){return O}));var n=r(99),i=e._googlesitekitTrackingData||{},a=i.activeModules,o=void 0===a?[]:a,c=i.isSiteKitScreen,s=i.trackingEnabled,l=i.trackingID,u=i.referenceSiteURL,d=i.userIDHash,g=i.isAuthenticated,f={activeModules:o,trackingEnabled:s,trackingID:l,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:i.userRoles,isAuthenticated:g,pluginVersion:"1.151.0"},p=Object(n.a)(f),m=p.enableTracking,v=p.disableTracking,b=(p.isTrackingEnabled,p.initializeSnippet),h=p.trackEvent,O=p.trackEventOnce;function _(e){e?m():v()}c&&s&&b()}).call(this,r(28))},363:function(e,t){e.exports=googlesitekit.notifications},39:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return i}));var n="_googlesitekitDataLayer",i="data-googlesitekit-gtag"},401:function(e,t){e.exports=googlesitekit.widgets},41:function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return o})),r.d(t,"d",(function(){return c}));var n=r(22),i="core/notifications",a={ERRORS:"notification-area-errors",BANNERS_ABOVE_NAV:"notification-area-banners-above-nav",BANNERS_BELOW_NAV:"notification-area-banners-below-nav"},o={DEFAULT:"default",SETUP_CTAS:"setup-ctas"},c=[n.s,n.n,n.l,n.o,n.m]},44:function(e,t,r){"use strict";(function(e){var n=r(6),i=r.n(n),a=r(1),o=r.n(a),c=r(11),s=r.n(c),l=r(24);function PreviewBlock(t){var r,n,a=t.className,o=t.width,c=t.height,u=t.shape,d=t.padding,g=t.smallWidth,f=t.smallHeight,p=t.tabletWidth,m=t.tabletHeight,v=t.desktopWidth,b=t.desktopHeight,h=Object(l.e)(),O={width:(r={},i()(r,l.b,g),i()(r,l.c,p),i()(r,l.a,v),i()(r,l.d,v),r),height:(n={},i()(n,l.b,f),i()(n,l.c,m),i()(n,l.a,b),i()(n,l.d,v),n)};return e.createElement("div",{className:s()("googlesitekit-preview-block",a,{"googlesitekit-preview-block--padding":d}),style:{width:O.width[h]||o,height:O.height[h]||c}},e.createElement("div",{className:s()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,r(4))},45:function(e,t){e.exports=googlesitekit.api},48:function(e,t,r){"use strict";r.d(t,"a",(function(){return _}));var n=r(5),i=r.n(n),a=r(6),o=r.n(a),c=r(12),s=r.n(c),l=r(14),u=r(64),d=r(82),g=r(9);function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){o()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var m=function(e){return e},v=function(){return{}},b=function(){},h=u.a.clearError,O=u.a.receiveError,_=function(e){var t,r,n=i.a.mark(M),a=e.baseName,c=e.controlCallback,u=e.reducerCallback,f=void 0===u?m:u,_=e.argsToParams,y=void 0===_?v:_,E=e.validateParams,k=void 0===E?b:E;s()(a,"baseName is required."),s()("function"==typeof c,"controlCallback is required and must be a function."),s()("function"==typeof f,"reducerCallback must be a function."),s()("function"==typeof y,"argsToParams must be a function."),s()("function"==typeof k,"validateParams must be a function.");try{k(y()),r=!1}catch(e){r=!0}var j=Object(d.b)(a),w=Object(d.a)(a),A="FETCH_".concat(w),R="START_".concat(A),N="FINISH_".concat(A),S="CATCH_".concat(A),D="RECEIVE_".concat(w),T="fetch".concat(j),L="receive".concat(j),P="isFetching".concat(j),I=o()({},P,{});function M(e,t){var r,o;return i.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,{payload:{params:e},type:R};case 2:return n.next=4,h(a,t);case 4:return n.prev=4,n.next=7,{payload:{params:e},type:A};case 7:return r=n.sent,n.next=10,C[L](r,e);case 10:return n.next=12,{payload:{params:e},type:N};case 12:n.next=21;break;case 14:return n.prev=14,n.t0=n.catch(4),o=n.t0,n.next=19,O(o,a,t);case 19:return n.next=21,{payload:{params:e},type:S};case 21:return n.abrupt("return",{response:r,error:o});case 22:case"end":return n.stop()}}),n,null,[[4,14]])}var C=(t={},o()(t,T,(function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=y.apply(void 0,t);return k(n),M(n,t)})),o()(t,L,(function(e,t){return s()(void 0!==e,"response is required."),r?(s()(Object(l.isPlainObject)(t),"params is required."),k(t)):t={},{payload:{response:e,params:t},type:D}})),t),x=o()({},A,(function(e){var t=e.payload;return c(t.params)})),H=o()({},P,(function(e){if(void 0===e[P])return!1;var t;try{for(var r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];t=y.apply(void 0,n),k(t)}catch(e){return!1}return!!e[P][Object(g.H)(t)]}));return{initialState:I,actions:C,controls:x,reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case R:var i=n.params;return p(p({},e),{},o()({},P,p(p({},e[P]),{},o()({},Object(g.H)(i),!0))));case D:var a=n.response,c=n.params;return f(e,a,c);case N:var s=n.params;return p(p({},e),{},o()({},P,p(p({},e[P]),{},o()({},Object(g.H)(s),!1))));case S:var l=n.params;return p(p({},e),{},o()({},P,p(p({},e[P]),{},o()({},Object(g.H)(l),!1))));default:return e}},resolvers:{},selectors:H}}},50:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return o}));var n=r(3),i=r(19),a=r(82);function o(t){var r=t.moduleName,o=t.FallbackComponent,c=t.IncompleteComponent;return function(t){function WhenActiveComponent(a){var s=Object(n.useSelect)((function(e){return e(i.a).getModule(r)}),[r]);if(!s)return null;var l=o||a.WidgetNull||null;if(!1===s.active)return l&&e.createElement(l,a);if(!1===s.connected){var u=c||l;return u&&e.createElement(u,a)}return e.createElement(t,a)}return WhenActiveComponent.displayName="When".concat(Object(a.c)(r),"Active"),(t.displayName||t.name)&&(WhenActiveComponent.displayName+="(".concat(t.displayName||t.name,")")),WhenActiveComponent}}}).call(this,r(4))},510:function(e,t,r){"use strict";(function(e){var n=r(6),i=r.n(n),a=r(1),o=r.n(a);function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach((function(t){i()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Spinner(t){var r=t.isSaving,n=t.style,i=void 0===n?{}:n;return e.createElement("span",{className:"spinner",style:s({display:r?"inline-block":"none",float:"none",marginTop:"0",visibility:"visible"},i)})}Spinner.propTypes={isSaving:o.a.bool,style:o.a.object},t.a=Spinner}).call(this,r(4))},531:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"a",(function(){return i})),r.d(t,"c",(function(){return a}));var n="fast",i="average",a="slow"},54:function(e,t,r){"use strict";(function(e){var n=r(1),i=r.n(n),a=r(106),o=r(2),c=r(9);function ErrorText(t){var r=t.message,n=t.reconnectURL,i=t.noPrefix;if(!r)return null;var s=r;void 0!==i&&i||(s=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),r)),n&&Object(a.a)(n)&&(s=s+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),n));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(s,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:i.a.string.isRequired,reconnectURL:i.a.string,noPrefix:i.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,r(4))},57:function(e,t,r){"use strict";(function(e){var n,i;r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return o}));var a=new Set((null===(n=e)||void 0===n||null===(i=n._googlesitekitBaseData)||void 0===i?void 0:i.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,r(28))},58:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=n.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return n.createElement("svg",i({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),a,o)}},587:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return ReportMetric}));var n=r(6),i=r.n(n),a=r(1),o=r.n(a),c=r(11),s=r.n(c),l=r(2),u=r(531),d=r(77),g=r(132);function ReportMetric(t){var r,n=t.title,a=t.description,o=t.displayValue,c=t.category,f=t.experimental,p=t.isLast,m=t.isHidden,v=t.isUnavailable,b=t.hintText;return c=null===(r=c)||void 0===r?void 0:r.toLowerCase(),e.createElement("tr",{className:s()("googlesitekit-pagespeed-report__row","googlesitekit-pagespeed-report-metric",{"googlesitekit-pagespeed-report__row--last":p,"googlesitekit-pagespeed-report__row--hidden":m,"googlesitekit-pagespeed-report__row--unavailable":v})},e.createElement("td",null,e.createElement("div",{className:"googlesitekit-pagespeed-report-metric__title"},n,!!f&&e.createElement(d.a,{label:Object(l.__)("Experimental","google-site-kit"),className:"googlesitekit-pagespeed-report-metric__badge"}),v&&e.createElement(g.a,{title:Object(l.__)("Field data is still being gathered for this metric and will become available once your site gets sufficient traffic","google-site-kit")})),e.createElement("div",{className:"googlesitekit-pagespeed-report-metric__description"},a),b&&e.createElement("div",{className:"googlesitekit-pagespeed-report-metric__hint-text"},b)),e.createElement("td",{className:s()("googlesitekit-pagespeed-report-metric-value",i()({},"googlesitekit-pagespeed-report-metric--".concat(c),!!c))},e.createElement("div",{className:"googlesitekit-pagespeed-report-metric-value-container"},e.createElement("div",{className:"googlesitekit-pagespeed-report-metric-value__display-value"},v?"—":o),e.createElement("div",{className:"googlesitekit-pagespeed-report-metric-value__rating"},v&&e.createElement("span",null,Object(l.__)("gathering data","google-site-kit")),!v&&c===u.b&&e.createElement("span",null,Object(l._x)("Good","Performance rating","google-site-kit")),!v&&c===u.a&&e.createElement("span",null,Object(l._x)("Needs improvement","Performance rating","google-site-kit")),!v&&c===u.c&&e.createElement("span",null,Object(l._x)("Poor","Performance rating","google-site-kit"))))))}ReportMetric.propTypes={title:o.a.string.isRequired,description:o.a.string.isRequired,displayValue:o.a.string.isRequired,category:o.a.string,experimental:o.a.bool,isLast:o.a.bool,isHidden:o.a.bool,hintText:o.a.node}}).call(this,r(4))},59:function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));var n=r(39);function i(e){return function(){e[n.a]=e[n.a]||[],e[n.a].push(arguments)}}},61:function(e,t,r){"use strict";r.d(t,"a",(function(){return a}));var n=r(0),i=Object(n.createContext)(""),a=(i.Consumer,i.Provider);t.b=i},64:function(e,t,r){"use strict";r.d(t,"a",(function(){return v})),r.d(t,"b",(function(){return b}));var n=r(6),i=r.n(n),a=r(33),o=r.n(a),c=r(116),s=r(12),l=r.n(s),u=r(96),d=r.n(u),g=r(9);function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){i()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function m(e,t){if(t&&Array.isArray(t)){var r=t.map((function(e){return"object"===o()(e)?Object(g.H)(e):e}));return"".concat(e,"::").concat(d()(JSON.stringify(r)))}return e}var v={receiveError:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(e,"error is required."),l()(t,"baseName is required."),l()(r&&Array.isArray(r),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:r}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return l()(e,"baseName is required."),l()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function b(e){l()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(r,"selectorName is required."),t.getError(e,r,n)},getErrorForAction:function(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return l()(r,"actionName is required."),t.getError(e,r,n)},getError:function(e,t,r){var n=e.errors;return l()(t,"baseName is required."),n[m(t,r)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var r=Object.keys(e.errors).find((function(r){return e.errors[r]===t}));return r?{baseName:r.substring(0,r.indexOf("::")),args:e.errorArgs[r]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(r,n){var i=t(e).getMetaDataForError(n);if(i){var a=i.baseName,o=i.args;if(!!t(e)[a])return{storeName:e,name:a,args:o}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:v,controls:{},reducer:function(e,t){var r=t.type,n=t.payload;switch(r){case"RECEIVE_ERROR":var a=n.baseName,o=n.args,c=n.error,s=m(a,o);return p(p({},e),{},{errors:p(p({},e.errors||{}),{},i()({},s,c)),errorArgs:p(p({},e.errorArgs||{}),{},i()({},s,o))});case"CLEAR_ERROR":var l=n.baseName,u=n.args,d=p({},e),g=m(l,u);return d.errors=p({},e.errors||{}),d.errorArgs=p({},e.errorArgs||{}),delete d.errors[g],delete d.errorArgs[g],d;case"CLEAR_ERRORS":var f=n.baseName,v=p({},e);if(f)for(var b in v.errors=p({},e.errors||{}),v.errorArgs=p({},e.errorArgs||{}),v.errors)(b===f||b.startsWith("".concat(f,"::")))&&(delete v.errors[b],delete v.errorArgs[b]);else v.errors={},v.errorArgs={};return v;default:return e}},resolvers:{},selectors:t}}},680:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("path",{d:"M10.6.6L12 2 6 8 0 2 1.4.6 6 5.2z",fill:"currentColor",fillRule:"evenodd"});t.a=function SvgChevronDownV2(e){return n.createElement("svg",i({viewBox:"0 0 12 8"},e),a)}},70:function(e,t,r){"use strict";var n=r(0);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var a=n.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return n.createElement("svg",i({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},73:function(e,t,r){"use strict";r.d(t,"a",(function(){return o}));var n=r(0),i=r(18),a=r(9);function o(e,t){var r=Object(i.a)(),o=null!=t?t:"".concat(r,"_").concat(e);return{view:Object(n.useCallback)((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return a.I.apply(void 0,[o,"view_notification"].concat(t))}),[o]),confirm:Object(n.useCallback)((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return a.I.apply(void 0,[o,"confirm_notification"].concat(t))}),[o]),dismiss:Object(n.useCallback)((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return a.I.apply(void 0,[o,"dismiss_notification"].concat(t))}),[o]),clickLearnMore:Object(n.useCallback)((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return a.I.apply(void 0,[o,"click_learn_more_link"].concat(t))}),[o])}}},75:function(e,t,r){"use strict";r.d(t,"a",(function(){return o})),r.d(t,"b",(function(){return c}));var n=r(33),i=r.n(n),a=r(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,r="object"===i()(e)?e.toString():e;return null==r||null===(t=r.replace)||void 0===t?void 0:t.call(r,/\/+$/,"")}},76:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return IconWrapper}));var n=r(1),i=r.n(n);function IconWrapper(t){var r=t.children,n=t.marginLeft,i=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:n,marginRight:i}},r)}IconWrapper.propTypes={children:i.a.node.isRequired,marginLeft:i.a.number,marginRight:i.a.number}}).call(this,r(4))},77:function(e,t,r){"use strict";(function(e){var n=r(21),i=r.n(n),a=r(25),o=r.n(a),c=r(11),s=r.n(c),l=r(1),u=r.n(l),d=r(0),g=Object(d.forwardRef)((function(t,r){var n=t.label,a=t.className,c=t.hasLeftSpacing,l=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",i()({ref:r},u,{className:s()("googlesitekit-badge",a,{"googlesitekit-badge--has-left-spacing":l})}),n)}));g.displayName="Badge",g.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=g}).call(this,r(4))},784:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return MetricsLearnMoreLink}));var n=r(2),i=r(20);function MetricsLearnMoreLink(){return e.createElement(i.a,{href:"https://web.dev/user-centric-performance-metrics/#how-metrics-are-measured",external:!0,"aria-label":Object(n.__)("Learn more how metrics are measured.","google-site-kit")},Object(n.__)("Learn more","google-site-kit"))}}).call(this,r(4))},79:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return d}));var n=r(15),i=r.n(n),a=r(188),o=r(133),c={},s=void 0===e?null:e,l=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,r=e.leading,n=e.initialWidth,u=void 0===n?0:n,d=e.initialHeight,g=void 0===d?0:d,f=Object(a.a)("undefined"==typeof document?[u,g]:l,t,r),p=i()(f,2),m=p[0],v=p[1],b=function(){return v(l)};return Object(o.a)(s,"resize",b),Object(o.a)(s,"orientationchange",b),m},d=function(e){return u(e)[0]}}).call(this,r(28))},80:function(e,t,r){"use strict";r.d(t,"b",(function(){return i})),r.d(t,"a",(function(){return a})),r.d(t,"c",(function(){return o})),r.d(t,"d",(function(){return c}));var n=r(106);function i(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(n.a)(e))return e;if(e.length<=t)return e;var r=new URL(e),i=e.replace(r.origin,"");if(i.length<t)return i;var a=i.length-Math.floor(t)+1;return"…"+i.substr(a)}},801:function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));var n=r(531);function i(e){return.9<=e?n.b:.5<=e?n.a:n.c}},802:function(e,t,r){"use strict";(function(e){var n=r(1),i=r.n(n),a=r(44);function PreviewBlocks(t){for(var r=t.width,n=t.height,i=t.shape,o=t.count,c=t.smallWidth,s=t.smallHeight,l=t.tabletWidth,u=t.tabletHeight,d=t.desktopWidth,g=t.desktopHeight,f=[],p=0;p++<o;)f.push(e.createElement(a.a,{width:r,height:n,shape:i,smallWidth:c,smallHeight:s,tabletWidth:l,tabletHeight:u,desktopWidth:d,desktopHeight:g,key:p}));return f}PreviewBlocks.propTypes={width:i.a.string,height:i.a.string,shape:i.a.string,count:i.a.number,smallWidth:i.a.string,smallHeight:i.a.string,tabletWidth:i.a.string,tabletHeight:i.a.string,desktopWidth:i.a.string,desktopHeight:i.a.string},PreviewBlocks.defaultProps={width:"100px",height:"100px",shape:"square",count:1},t.a=PreviewBlocks}).call(this,r(4))},82:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"a",(function(){return i})),r.d(t,"c",(function(){return a}));var n=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},i=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function a(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},83:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return k})),r.d(t,"d",(function(){return j})),r.d(t,"e",(function(){return A})),r.d(t,"c",(function(){return R})),r.d(t,"b",(function(){return N}));var n=r(15),i=r.n(n),a=r(33),o=r.n(a),c=r(6),s=r.n(c),l=r(25),u=r.n(l),d=r(14),g=r(63),f=r.n(g),p=r(2);function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach((function(t){s()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var b=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=_(e,t),n=r.formatUnit,i=r.formatDecimal;try{return n()}catch(e){return i()}},h=function(e){var t=O(e),r=t.hours,n=t.minutes,i=t.seconds;return i=("0"+i).slice(-2),n=("0"+n).slice(-2),"00"===(r=("0"+r).slice(-2))?"".concat(n,":").concat(i):"".concat(r,":").concat(n,":").concat(i)},O=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},_=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=O(e),n=r.hours,i=r.minutes,a=r.seconds;return{hours:n,minutes:i,seconds:a,formatUnit:function(){var r=t.unitDisplay,o=v(v({unitDisplay:void 0===r?"short":r},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?A(a,v(v({},o),{},{unit:"second"})):Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?A(a,v(v({},o),{},{unit:"second"})):"",i?A(i,v(v({},o),{},{unit:"minute"})):"",n?A(n,v(v({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(p.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(p.__)("%ds","google-site-kit"),a);if(0===e)return t;var r=Object(p.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(p.__)("%dm","google-site-kit"),i),o=Object(p.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(p.__)("%dh","google-site-kit"),n);return Object(p.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(p._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",i?r:"",n?o:"").trim()}}},y=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},E=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in millions.
Object(p.__)("%sM","google-site-kit"),A(y(e),e%10==0?{}:t)):1e4<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),A(y(e))):1e3<=e?Object(p.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(p.__)("%sK","google-site-kit"),A(y(e),e%10==0?{}:t)):A(e,{signDisplay:"never",maximumFractionDigits:1})};function k(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=v({},e)),t}function j(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var r=k(t),n=r.style,i=void 0===n?"metric":n;return"metric"===i?E(e):"duration"===i?b(e,r):"durationISO"===i?h(e):A(e,r)}var w=f()(console.warn),A=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.locale,n=void 0===r?N():r,a=u()(t,["locale"]);try{return new Intl.NumberFormat(n,a).format(e)}catch(t){w("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(n),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},s=["signDisplay","compactDisplay"],l={},d=0,g=Object.entries(a);d<g.length;d++){var f=i()(g[d],2),p=f[0],m=f[1];c[p]&&m===c[p]||(s.includes(p)||(l[p]=m))}try{return new Intl.NumberFormat(n,l).format(e)}catch(t){return new Intl.NumberFormat(n).format(e)}},R=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.locale,n=void 0===r?N():r,i=t.style,a=void 0===i?"long":i,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var s=new Intl.ListFormat(n,{style:a,type:c});return s.format(e)}
/* translators: used between list items, there is a space after the comma. */var l=Object(p.__)(", ","google-site-kit");return e.join(l)},N=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,r=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(r){var n=r.match(/^(\w{2})?(_)?(\w{2})/);if(n&&n[0])return n[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,r(28))},84:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return i}));var n=r(149),i=r.n(n)()(e)}).call(this,r(28))},85:function(e,t,r){"use strict";(function(e){var n=r(1),i=r.n(n),a=r(11),o=r.n(a);function ChangeArrow(t){var r=t.direction,n=t.invertColor,i=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(r),{"googlesitekit-change-arrow--inverted-color":n}),width:i,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:i.a.string,invertColor:i.a.bool,width:i.a.number,height:i.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,r(4))},88:function(e,t,r){"use strict";r.r(t),r.d(t,"AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY",(function(){return n})),r.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY",(function(){return i})),r.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION",(function(){return a})),r.d(t,"AREA_MAIN_DASHBOARD_CONTENT_PRIMARY",(function(){return o})),r.d(t,"AREA_MAIN_DASHBOARD_SPEED_PRIMARY",(function(){return c})),r.d(t,"AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY",(function(){return s})),r.d(t,"AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY",(function(){return l})),r.d(t,"AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY",(function(){return u})),r.d(t,"AREA_ENTITY_DASHBOARD_SPEED_PRIMARY",(function(){return d})),r.d(t,"AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY",(function(){return g}));var n="mainDashboardKeyMetricsPrimary",i="mainDashboardTrafficPrimary",a="mainDashboardTrafficAudienceSegmentation",o="mainDashboardContentPrimary",c="mainDashboardSpeedPrimary",s="mainDashboardMonetizationPrimary",l="entityDashboardTrafficPrimary",u="entityDashboardContentPrimary",d="entityDashboardSpeedPrimary",g="entityDashboardMonetizationPrimary";t.default={AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY:n,AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY:i,AREA_MAIN_DASHBOARD_CONTENT_PRIMARY:o,AREA_MAIN_DASHBOARD_SPEED_PRIMARY:c,AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY:s,AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY:l,AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY:u,AREA_ENTITY_DASHBOARD_SPEED_PRIMARY:d,AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY:g}},89:function(e,t,r){"use strict";r.d(t,"a",(function(){return a}));var n=r(12),i=r.n(n),a=function(e,t){var r=t.dateRangeLength;i()(Array.isArray(e),"report must be an array to partition."),i()(Number.isInteger(r)&&r>0,"dateRangeLength must be a positive integer.");var n=-1*r;return{currentRange:e.slice(n),compareRange:e.slice(2*n,n)}}},9:function(e,t,r){"use strict";r.d(t,"I",(function(){return i.b})),r.d(t,"J",(function(){return i.c})),r.d(t,"F",(function(){return a.a})),r.d(t,"K",(function(){return a.b})),r.d(t,"H",(function(){return u})),r.d(t,"m",(function(){return d.a})),r.d(t,"B",(function(){return d.d})),r.d(t,"C",(function(){return d.e})),r.d(t,"y",(function(){return d.c})),r.d(t,"r",(function(){return d.b})),r.d(t,"z",(function(){return m})),r.d(t,"j",(function(){return v})),r.d(t,"i",(function(){return b})),r.d(t,"d",(function(){return k})),r.d(t,"c",(function(){return j})),r.d(t,"e",(function(){return w})),r.d(t,"b",(function(){return A})),r.d(t,"a",(function(){return R})),r.d(t,"f",(function(){return N})),r.d(t,"n",(function(){return S})),r.d(t,"w",(function(){return D})),r.d(t,"p",(function(){return T})),r.d(t,"G",(function(){return L})),r.d(t,"s",(function(){return P})),r.d(t,"v",(function(){return I})),r.d(t,"k",(function(){return M})),r.d(t,"o",(function(){return C.b})),r.d(t,"h",(function(){return C.a})),r.d(t,"t",(function(){return x.b})),r.d(t,"q",(function(){return x.a})),r.d(t,"A",(function(){return x.c})),r.d(t,"x",(function(){return H})),r.d(t,"u",(function(){return F})),r.d(t,"E",(function(){return U})),r.d(t,"D",(function(){return W.a})),r.d(t,"g",(function(){return q})),r.d(t,"L",(function(){return V})),r.d(t,"l",(function(){return Y}));var n=r(14),i=r(36),a=r(75),o=r(33),c=r.n(o),s=r(96),l=r.n(s),u=function(e){return l()(JSON.stringify(function e(t){var r={};return Object.keys(t).sort().forEach((function(n){var i=t[n];i&&"object"===c()(i)&&!Array.isArray(i)&&(i=e(i)),r[n]=i})),r}(e)))};r(97);var d=r(83);function g(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function f(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function p(e){return e.replace(/\n/gi,"<br>")}function m(e){for(var t=e,r=0,n=[g,f,p];r<n.length;r++){t=(0,n[r])(t)}return t}var v=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},b=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},h=r(15),O=r.n(h),_=r(12),y=r.n(_),E=r(2),k="Invalid dateString parameter, it must be a string.",j='Invalid date range, it must be a string with the format "last-x-days".',w=60,A=60*w,R=24*A,N=7*R;function S(){var e=function(e){return Object(E.sprintf)(
/* translators: %s: number of days */
Object(E._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function D(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(n.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var r=new Date(e);return Object(n.isDate)(r)&&!isNaN(r)}function T(e){y()(Object(n.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),r="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,r.length<2?"0".concat(r):r].join("-")}function L(e){y()(D(e),k);var t=e.split("-"),r=O()(t,3),n=r[0],i=r[1],a=r[2];return new Date(n,i-1,a)}function P(e,t){return T(M(e,t*R))}function I(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function M(e,t){y()(D(e)||Object(n.isDate)(e)&&!isNaN(e),k);var r=D(e)?Date.parse(e):e.getTime();return new Date(r-1e3*t)}var C=r(98),x=r(80);function H(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function F(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var B=r(27),z=r.n(B),U=function(e){return Array.isArray(e)?z()(e).sort():e},W=r(89);function q(e,t){var r=function(e){return"0"===e||0===e};if(r(e)&&r(t))return 0;if(r(e)||Number.isNaN(e))return null;var n=(t-e)/e;return Number.isNaN(n)||!Number.isFinite(n)?null:n}var V=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},Y=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(n.unescape)(t)}},911:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return SettingsView}));var n=r(2),i=r(3),a=r(9),o=r(103);function SettingsView(){var t=Object(i.useSelect)((function(e){return e(o.d).getAdminScreenURL()})),r=Object(n.sprintf)(
/* translators: %s: is the URL to the Site Kit dashboard. */
Object(n.__)('To view insights, <a href="%s">visit the dashboard</a>',"google-site-kit"),"".concat(t,"#speed"));return e.createElement("p",{dangerouslySetInnerHTML:Object(a.F)(r,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})})}}).call(this,r(4))},92:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return Dismiss}));var n=r(5),i=r.n(n),a=r(6),o=r.n(a),c=r(16),s=r.n(c),l=r(1),u=r.n(l),d=r(2),g=r(3),f=r(73),p=r(41),m=r(10);function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){o()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Dismiss(t){var r=t.id,n=t.primary,a=void 0===n||n,o=t.dismissLabel,c=void 0===o?Object(d.__)("OK, Got it!","google-site-kit"):o,l=t.dismissExpires,u=void 0===l?0:l,v=t.disabled,h=t.onDismiss,O=void 0===h?function(){}:h,_=t.gaTrackingEventArgs,y=t.dismissOptions,E=Object(f.a)(r,null==_?void 0:_.category),k=Object(g.useDispatch)(p.a).dismissNotification,j=function(){var e=s()(i.a.mark((function e(t){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==O?void 0:O(t);case 2:E.dismiss(null==_?void 0:_.label,null==_?void 0:_.value),k(r,b(b({},y),{},{expiresInSeconds:u}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(m.Button,{tertiary:!a,onClick:j,disabled:v},c)}Dismiss.propTypes={id:u.a.string,primary:u.a.bool,dismissLabel:u.a.string,dismissExpires:u.a.number,disabled:u.a.bool,onDismiss:u.a.func,gaTrackingEventArgs:u.a.shape({label:u.a.string,value:u.a.string})}}).call(this,r(4))},93:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return o})),r.d(t,"c",(function(){return c}));var n=r(24),i=r(130);function a(t,r){var n=document.querySelector(t);if(!n)return 0;var i=n.getBoundingClientRect().top,a=o(r);return i+e.scrollY-a}function o(e){var t=c(e),r=document.querySelectorAll(".googlesitekit-navigation, .googlesitekit-entity-header");return t+=Array.from(r).reduce((function(e,t){return e+t.offsetHeight}),0)}function c(t){var r=0,a=document.querySelector(".googlesitekit-header");return r=!!a&&"sticky"===e.getComputedStyle(a).position?function(e){var t=document.querySelector(".googlesitekit-header");if(t){if(e===n.b)return t.offsetHeight;var r=t.getBoundingClientRect().bottom;return r<0?0:r}return 0}(t):function(e){var t=document.querySelector("#wpadminbar");return t&&e!==n.b?t.offsetHeight:0}(t),(r=Object(i.a)(r))<0?0:r}}).call(this,r(28))},97:function(e,t,r){"use strict";(function(e){r(51),r(53)}).call(this,r(28))},98:function(e,t,r){"use strict";(function(e){r.d(t,"b",(function(){return a})),r.d(t,"a",(function(){return o}));var n=r(239),i=r(85),a=function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=r.invertColor,o=void 0!==a&&a;return Object(n.a)(e.createElement(i.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,r(4))},99:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return d}));var n=r(6),i=r.n(n),a=r(14),o=r(100),c=r(101);function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach((function(t){i()(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,i=l(l({},u),t);i.referenceSiteURL&&(i.referenceSiteURL=i.referenceSiteURL.toString().replace(/\/+$/,""));var s=Object(o.a)(i,r),d=Object(c.a)(i,r,s,n),g={},f=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=JSON.stringify(t);g[n]||(g[n]=Object(a.once)(d)),g[n].apply(g,t)};return{enableTracking:function(){i.trackingEnabled=!0},disableTracking:function(){i.trackingEnabled=!1},initializeSnippet:s,isTrackingEnabled:function(){return!!i.trackingEnabled},trackEvent:d,trackEventOnce:f}}}).call(this,r(28))}},[[1297,1,0]]]);