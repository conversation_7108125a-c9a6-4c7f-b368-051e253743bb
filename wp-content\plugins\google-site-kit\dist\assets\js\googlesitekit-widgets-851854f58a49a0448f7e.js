(window.__googlesitekit_webpackJsonp=window.__googlesitekit_webpackJsonp||[]).push([[31],{10:function(e,t){e.exports=googlesitekit.components},100:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var i=n(59),r=n(39),a=n(57);function o(t,n){var o,c=Object(i.a)(n),l=t.activeModules,s=t.referenceSiteURL,u=t.userIDHash,d=t.userRoles,g=void 0===d?[]:d,f=t.isAuthenticated,m=t.pluginVersion;return function(){var n=e.document;if(void 0===o&&(o=!!n.querySelector("script[".concat(r.b,"]"))),!o){o=!0;var i=(null==g?void 0:g.length)?g.join(","):"";c("js",new Date),c("config",t.trackingID,{groups:"site_kit",send_page_view:t.isSiteKitScreen,domain:s,plugin_version:m||"",enabled_features:Array.from(a.a).join(","),active_modules:l.join(","),authenticated:f?"1":"0",user_properties:{user_roles:i,user_identifier:u}});var d=n.createElement("script");return d.setAttribute(r.b,""),d.async=!0,d.src="https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(r.a),n.head.appendChild(d),{scriptTagSrc:"https://www.googletagmanager.com/gtag/js?id=".concat(t.trackingID,"&l=").concat(r.a)}}}}}).call(this,n(28))},101:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var i=n(5),r=n.n(i),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(59);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n,i){var a=Object(s.a)(t);return function(){var t=l()(r.a.mark((function t(o,c,l,s){var u;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.trackingEnabled){t.next=3;break}return t.abrupt("return");case 3:return n(),u={send_to:"site_kit",event_category:o,event_label:l,value:s},t.abrupt("return",new Promise((function(e){var t,n,r=setTimeout((function(){i.console.warn('Tracking event "'.concat(c,'" (category "').concat(o,'") took too long to fire.')),e()}),1e3),l=function(){clearTimeout(r),e()};a("event",c,d(d({},u),{},{event_callback:l})),(null===(t=i._gaUserPrefs)||void 0===t||null===(n=t.ioo)||void 0===n?void 0:n.call(t))&&l()})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,i,r){return t.apply(this,arguments)}}()}},1018:function(e,t,n){"use strict";(function(e){var i=n(6),r=n.n(i),a=n(12),o=n.n(a),c=n(3),l=n(47),s=n(1019),u=n(176);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f=Object.keys(l.b).map((function(e){return"WIDGET_AREA_STYLES.".concat(e)})).join(", "),m={assignWidgetArea:function(e,t){return{payload:{slug:e,contextSlugs:"string"==typeof t?[t]:t},type:"ASSIGN_WIDGET_AREA"}},registerWidgetArea:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.priority,i=void 0===n?10:n,r=t.style,a=void 0===r?l.b.BOXES:r,c=t.title,s=t.subtitle,u=t.Icon,d=t.hasNewBadge,g=void 0!==d&&d,m=t.CTA,p=t.Footer,h=t.filterActiveWidgets;return o()(e,"slug is required."),o()(Object.values(l.b).includes(a),"settings.style must be one of: ".concat(f,".")),{payload:{slug:e,settings:{priority:i,style:a,title:c,subtitle:s,Icon:u,hasNewBadge:g,CTA:m,Footer:p,filterActiveWidgets:h}},type:"REGISTER_WIDGET_AREA"}}},p=Object(u.a)((function(t,n){var i=n.type,r=n.payload;switch(i){case"ASSIGN_WIDGET_AREA":var a=r.slug;return r.contextSlugs.forEach((function(e){void 0===t.contextAssignments[e]&&(t.contextAssignments[e]=[]),t.contextAssignments[e].includes(a)||t.contextAssignments[e].push(a)})),t;case"REGISTER_WIDGET_AREA":var o=r.slug,c=r.settings;return void 0!==t.areas[o]?(e.console.warn('Could not register widget area with slug "'.concat(o,'". Widget area "').concat(o,'" is already registered.')),t):(t.areas[o]=g(g({},c),{},{slug:o}),t);default:return t}})),h={isWidgetAreaActive:Object(c.createRegistrySelector)((function(e){return function(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};o()(n,"widgetAreaSlug is required to check a widget area is active.");var r=i.modules,a=e(l.a).getWidgetArea(n),c=e(l.a).getWidgets(n,{modules:r});return a.filterActiveWidgets&&(c=a.filterActiveWidgets(e,c)),c.some((function(t){return e(l.a).isWidgetActive(t.slug)}))}})),isWidgetAreaRegistered:function(e,t){return void 0!==e.areas[t]},getWidgetAreas:function(e,t){o()(t,"contextSlug is required.");var n=e.areas,i=e.contextAssignments;return Object(s.a)(Object.values(n).filter((function(e){return i[t]&&i[t].includes(e.slug)})),"priority")},getWidgetArea:function(e,t){return o()(t,"slug is required."),e.areas[t]||null}};t.a={initialState:{areas:{},contextAssignments:{}},actions:m,controls:{},reducer:p,resolvers:{},selectors:h}}).call(this,n(28))},1019:function(e,t,n){"use strict";function i(e,t){return e.sort((function(e,n){return e[t]>n[t]?1:e[t]<n[t]?-1:0}))}n.d(t,"a",(function(){return i}))},102:function(e,t,n){"use strict";var i=n(123);n.d(t,"a",(function(){return i.a}));var r=n(124);n.d(t,"c",(function(){return r.a}));var a=n(125);n.d(t,"b",(function(){return a.a}))},1020:function(e,t,n){"use strict";(function(e){var i=n(6),r=n.n(i),a=n(12),o=n.n(a),c=n(14),l=n(374),s=n(3),u=n(246),d=n(47),g=n(176);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var p=Object.keys(d.c).map((function(e){return"WIDGET_WIDTHS.".concat(e)})).join(", "),h={assignWidget:function(e,t){return{payload:{slug:e,areaSlugs:"string"==typeof t?[t]:t},type:"ASSIGN_WIDGET"}},registerWidget:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.Component,i=t.priority,r=void 0===i?10:i,a=t.width,c=void 0===a?d.c.QUARTER:a,l=t.wrapWidget,s=void 0===l||l,g=t.modules,f=t.isActive,m=t.isPreloaded,h=t.hideOnBreakpoints,v=Object.values(d.c);return o()(n,"component is required to register a widget."),o()(Array.isArray(c)&&c.some(v.includes,v)||!Array.isArray(c)&&v.includes(c),"Widget width should be one of: ".concat(p,', but "').concat(c,'" was provided.')),{payload:{slug:e,settings:{Component:n,priority:r,width:c,wrapWidget:s,modules:Object(u.f)(g),isActive:f,isPreloaded:m,hideOnBreakpoints:h}},type:"REGISTER_WIDGET"}},setWidgetState:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return{payload:{slug:e,Component:t,metadata:n},type:"SET_WIDGET_STATE"}},unsetWidgetState:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return{payload:{slug:e,Component:t,metadata:n},type:"UNSET_WIDGET_STATE"}}},v=Object(g.a)((function(t,n){var i=n.type,r=n.payload;switch(i){case"ASSIGN_WIDGET":var a=r.slug;return r.areaSlugs.forEach((function(e){void 0===t.areaAssignments[e]&&(t.areaAssignments[e]=[]),t.areaAssignments[e].includes(a)||t.areaAssignments[e].push(a)})),t;case"REGISTER_WIDGET":var o=r.slug,c=r.settings;return void 0!==t.widgets[o]?(e.console.warn('Could not register widget with slug "'.concat(o,'". Widget "').concat(o,'" is already registered.')),t):(t.widgets[o]=m(m({},c),{},{slug:o}),t);case"SET_WIDGET_STATE":var s=r.slug,u=r.Component,d=r.metadata;return t.widgetStates[s]={Component:u,metadata:d},t;case"UNSET_WIDGET_STATE":var g,f,p,h,v=r.slug,b=r.Component,E=r.metadata;return(null===(g=t.widgetStates)||void 0===g||null===(f=g[v])||void 0===f?void 0:f.Component)===b&&Object(l.b)(null===(p=t.widgetStates)||void 0===p||null===(h=p[v])||void 0===h?void 0:h.metadata)===E&&delete t.widgetStates[v],t;default:return t}})),b={isWidgetActive:Object(s.createRegistrySelector)((function(e){return function(t,n){return o()(n,"slug is required to check a widget is active."),!Object(u.e)(e(d.a).getWidgetState(n))}})),isWidgetRegistered:function(e,t){return void 0!==e.widgets[t]},isWidgetPreloaded:Object(s.createRegistrySelector)((function(e){return function(t,n){var i,r;return!!(null===(i=t.widgets[n])||void 0===i||null===(r=i.isPreloaded)||void 0===r?void 0:r.call(i,e))}})),getWidgets:Object(s.createRegistrySelector)((function(e){return function(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=i.modules;o()(n,"widgetAreaSlug is required.");var a=t.areaAssignments,l=Object.values(t.widgets).filter((function(e){var t;return null===(t=a[n])||void 0===t?void 0:t.includes(e.slug)})).filter((function(t){return"function"!=typeof t.isActive||(!!t.isActive(e)||"function"==typeof t.isPreloaded&&t.isPreloaded(e))}));if(r){var s=Object(u.f)(r);l=l.filter((function(e){var t;return!(null===(t=e.modules)||void 0===t?void 0:t.length)||Object(c.intersection)(e.modules,s).length===e.modules.length}))}return l.sort((function(e,t){return e.priority-t.priority}))}})),getWidget:function(e,t){return o()(t,"slug is required to get a widget."),e.widgets[t]||null},getWidgetState:function(e,t){return e.widgetStates[t]||null},getWidgetStates:function(e){return e.widgetStates}};t.a={initialState:{areaAssignments:{},widgets:{},widgetStates:{}},actions:h,controls:{},reducer:v,resolvers:{},selectors:b}}).call(this,n(28))},1022:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M7.999 1.333v13.334M1.332 8h13.333",stroke:"currentColor",strokeWidth:2,strokeLinecap:"square"});t.a=function SvgPlus(e){return i.createElement("svg",r({viewBox:"0 0 16 16",fill:"none"},e),a)}},1023:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsNewBadge}));var i=n(15),r=n.n(i),a=n(0),o=n(2),c=n(3),l=n(13),s=n(77);function KeyMetricsNewBadge(){var t=Object(c.useSelect)((function(e){return e(l.c).getKeyMetricsSetupNew()})),n=Object(c.useSelect)((function(e){return e(l.c).isKeyMetricsSetupCompleted()})),i=Object(a.useState)(n),u=r()(i,1)[0],d=Object(a.useState)(t),g=r()(d,2),f=g[0],m=g[1];return Object(a.useEffect)((function(){!u&&n&&m(!0)}),[u,n]),n||!f?null:e.createElement(s.a,{className:"googlesitekit-new-badge",label:Object(o.__)("New","google-site-kit")})}}).call(this,n(4))},1024:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricsWidgetSubtitle}));var i=n(2),r=n(0),a=n(13),o=n(3);function MetricsWidgetSubtitle(){return Object(o.useSelect)((function(e){return e(a.c).isKeyMetricsSetupCompleted()}))?e.createElement(r.Fragment,null,Object(i.__)("Track progress towards your goals with tailored metrics and important user interaction metrics","google-site-kit")):null}}).call(this,n(4))},104:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z",fill:"currentColor"});t.a=function SvgClose(e){return i.createElement("svg",r({viewBox:"0 0 14 14",fill:"none"},e),a)}},105:function(e,t,n){"use strict";(function(e){var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s);function VisuallyHidden(t){var n=t.className,i=t.children,a=o()(t,["className","children"]);return i?e.createElement("span",r()({},a,{className:u()("screen-reader-text",n)}),i):null}VisuallyHidden.propTypes={className:l.a.string,children:l.a.node},VisuallyHidden.defaultProps={className:""},t.a=VisuallyHidden}).call(this,n(4))},107:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return TourTooltip}));var i=n(21),r=n.n(i),a=n(152),o=n.n(a),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(2),g=n(10),f=n(154),m=n(104);function TourTooltip(t){var n=t.backProps,i=t.closeProps,c=t.index,s=t.primaryProps,u=t.size,p=t.step,h=t.tooltipProps,v=u>1?Object(f.a)(u):[],b=function(e){return l()("googlesitekit-tooltip-indicator",{active:e===c})};return e.createElement("div",r()({className:l()("googlesitekit-tour-tooltip",p.className)},h),e.createElement(o.a,{className:"googlesitekit-tooltip-card"},e.createElement("div",{className:"googlesitekit-tooltip-body"},e.createElement("h2",{className:"googlesitekit-tooltip-title"},p.title),e.createElement("div",{className:"googlesitekit-tooltip-content"},p.content)),e.createElement(a.CardActions,{className:"googlesitekit-tooltip-actions"},e.createElement("ul",{className:"googlesitekit-tooltip-indicators"},v.map((function(t){return e.createElement("li",{key:"indicator-".concat(t),className:b(t)})}))),e.createElement("div",{className:"googlesitekit-tooltip-buttons"},0!==c&&e.createElement(g.Button,r()({className:"googlesitekit-tooltip-button",text:!0},n),n.title),p.cta,s.title&&e.createElement(g.Button,r()({className:"googlesitekit-tooltip-button",text:!0},s),s.title))),e.createElement(g.Button,{className:"googlesitekit-tooltip-close",text:!0,hideTooltipTitle:!0,icon:e.createElement(m.a,{width:"14",height:"14"}),onClick:i.onClick,"aria-label":Object(d.__)("Close","google-site-kit")})))}TourTooltip.propTypes={backProps:u.a.object.isRequired,closeProps:u.a.object.isRequired,index:u.a.number.isRequired,isLastStep:u.a.bool.isRequired,primaryProps:u.a.object.isRequired,size:u.a.number.isRequired,step:u.a.shape({content:u.a.node,title:u.a.node,cta:u.a.oneOfType([u.a.element,u.a.bool]),className:u.a.string}).isRequired,tooltipProps:u.a.object.isRequired}}).call(this,n(4))},108:function(e,t,n){"use strict";var i=n(434);n.d(t,"a",(function(){return i.a}));var r=n(435);n.d(t,"b",(function(){return r.a}));var a=n(436);n.d(t,"c",(function(){return a.a}));var o=n(437);n.d(t,"d",(function(){return o.a}));var c=n(438);n.d(t,"e",(function(){return c.a}));var l=n(439);n.d(t,"f",(function(){return l.a}));var s=n(272);n.d(t,"g",(function(){return s.a}));var u=n(202);n.d(t,"h",(function(){return u.a}));n(359)},109:function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(208),l=n(38),s=n(2),u=n(10),d=n(58);function ModalDialog(t){var n=t.className,i=void 0===n?"":n,r=t.dialogActive,a=void 0!==r&&r,g=t.handleDialog,f=void 0===g?null:g,m=t.onOpen,p=void 0===m?null:m,h=t.onClose,v=void 0===h?null:h,b=t.title,E=void 0===b?null:b,_=t.provides,O=t.handleConfirm,y=t.subtitle,k=t.confirmButton,j=void 0===k?null:k,S=t.dependentModules,A=t.danger,w=void 0!==A&&A,T=t.inProgress,C=void 0!==T&&T,N=t.small,D=void 0!==N&&N,R=t.medium,x=void 0!==R&&R,M=t.buttonLink,I=void 0===M?null:M,B=Object(c.a)(ModalDialog),P="googlesitekit-dialog-description-".concat(B),L=!(!_||!_.length);return e.createElement(u.Dialog,{open:a,onOpen:p,onClose:v,"aria-describedby":L?P:void 0,tabIndex:"-1",className:o()(i,{"googlesitekit-dialog-sm":D,"googlesitekit-dialog-md":x})},e.createElement(u.DialogTitle,null,w&&e.createElement(d.a,{width:28,height:28}),E),y?e.createElement("p",{className:"mdc-dialog__lead"},y):[],e.createElement(u.DialogContent,null,L&&e.createElement("section",{id:P,className:"mdc-dialog__provides"},e.createElement("ul",{className:"mdc-list mdc-list--underlined mdc-list--non-interactive"},_.map((function(t){return e.createElement("li",{className:"mdc-list-item",key:t},e.createElement("span",{className:"mdc-list-item__text"},t))})))),S&&e.createElement("p",{className:"mdc-dialog__dependencies"},Object(l.a)(Object(s.sprintf)(
/* translators: %s is replaced with the dependent modules. */
Object(s.__)("<strong>Note:</strong> %s","google-site-kit"),S),{strong:e.createElement("strong",null)}))),e.createElement(u.DialogFooter,null,e.createElement(u.Button,{className:"mdc-dialog__cancel-button",tertiary:!0,onClick:f,disabled:C},Object(s.__)("Cancel","google-site-kit")),I?e.createElement(u.Button,{href:I,onClick:O,target:"_blank",danger:w},j):e.createElement(u.SpinnerButton,{onClick:O,danger:w,disabled:C,isSaving:C},j||Object(s.__)("Disconnect","google-site-kit"))))}ModalDialog.displayName="Dialog",ModalDialog.propTypes={className:r.a.string,dialogActive:r.a.bool,handleDialog:r.a.func,handleConfirm:r.a.func.isRequired,onOpen:r.a.func,onClose:r.a.func,title:r.a.string,confirmButton:r.a.string,danger:r.a.bool,small:r.a.bool,medium:r.a.bool,buttonLink:r.a.string},t.a=ModalDialog}).call(this,n(4))},113:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return h}));var i=n(6),r=n.n(i),a=n(21),o=n.n(a),c=n(15),l=n.n(c),s=n(25),u=n.n(s),d=n(240),g=n(1),f=n.n(g),m=n(0);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function h(t){function WithIntersectionObserverComponent(n){var i=n.onInView,r=u()(n,["onInView"]),a=Object(m.useRef)(),c=Object(d.a)(a,{root:null,threshold:.45}),s=Object(m.useState)(!1),g=l()(s,2),f=g[0],p=g[1],h=!!(null==c?void 0:c.isIntersecting)&&!!(null==c?void 0:c.intersectionRatio);return Object(m.useEffect)((function(){c&&h&&!f&&(i(),p(!0))}),[f,h,c,i]),e.createElement(t,o()({ref:a},r))}return WithIntersectionObserverComponent.displayName="WithIntersectionObserverComponent",(t.displayName||t.name)&&(WithIntersectionObserverComponent.displayName+="(".concat(t.displayName||t.name,")")),WithIntersectionObserverComponent.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({onInView:f.a.func.isRequired},t.propTypes),WithIntersectionObserverComponent}}).call(this,n(4))},117:function(e,t,n){"use strict";var i=n(326),r=n(314);n.d(t,"b",(function(){return r.a}));var a=n(315);n.d(t,"c",(function(){return a.a}));var o=n(316);n.d(t,"d",(function(){return o.a}));var c=n(317);n.d(t,"a",(function(){return c.a})),t.e=i.a},118:function(e,t,n){"use strict";n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return d}));var i,r=n(6),a=n.n(r),o=n(47),c=n(174),l=n(163),s=(i={},a()(i,o.c.QUARTER,3),a()(i,o.c.HALF,6),a()(i,o.c.FULL,12),i),u="googlesitekit-hidden",d=[c.a,l.a]},120:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var i=n(1),r=n.n(i),a=n(0),o=n(2),c=n(3),l=n(10),s=n(35),u=n(54);function ErrorNotice(t){var n,i=t.error,r=t.hasButton,d=void 0!==r&&r,g=t.storeName,f=t.message,m=void 0===f?i.message:f,p=t.noPrefix,h=void 0!==p&&p,v=t.skipRetryMessage,b=t.Icon,E=Object(c.useDispatch)(),_=Object(c.useSelect)((function(e){return g?e(g).getSelectorDataForError(i):null})),O=Object(a.useCallback)((function(){E(_.storeName).invalidateResolution(_.name,_.args)}),[E,_]);if(!i||Object(s.f)(i))return null;var y=d&&Object(s.d)(i,_);return d||v||(m=Object(o.sprintf)(
/* translators: %1$s: Error message from Google API. */
Object(o.__)("%1$s%2$s Please try again.","google-site-kit"),m,m.endsWith(".")?"":".")),e.createElement(a.Fragment,null,b&&e.createElement("div",{className:"googlesitekit-error-notice__icon"},e.createElement(b,{width:"24",height:"24"})),e.createElement(u.a,{message:m,reconnectURL:null===(n=i.data)||void 0===n?void 0:n.reconnectURL,noPrefix:h}),y&&e.createElement(l.Button,{className:"googlesitekit-error-notice__retry-button",onClick:O},Object(o.__)("Retry","google-site-kit")))}ErrorNotice.propTypes={error:r.a.shape({message:r.a.string}),hasButton:r.a.bool,storeName:r.a.string,message:r.a.string,noPrefix:r.a.bool,skipRetryMessage:r.a.bool,Icon:r.a.elementType}}).call(this,n(4))},121:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n(219),r=n(14),a=n(0);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=Object(i.b)((function(){return r.debounce.apply(void 0,t)}),t);return Object(a.useEffect)((function(){return function(){return o.cancel()}}),[o]),o}},123:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Cell}));var i=n(21),r=n.n(i),a=n(6),o=n.n(a),c=n(25),l=n.n(c),s=n(1),u=n.n(s),d=n(11),g=n.n(d);function Cell(t){var n,i=t.className,a=t.alignTop,c=t.alignMiddle,s=t.alignBottom,u=t.alignRight,d=t.alignLeft,f=t.smAlignRight,m=t.mdAlignRight,p=t.lgAlignRight,h=t.smSize,v=t.smStart,b=t.smOrder,E=t.mdSize,_=t.mdStart,O=t.mdOrder,y=t.lgSize,k=t.lgStart,j=t.lgOrder,S=t.size,A=t.children,w=l()(t,["className","alignTop","alignMiddle","alignBottom","alignRight","alignLeft","smAlignRight","mdAlignRight","lgAlignRight","smSize","smStart","smOrder","mdSize","mdStart","mdOrder","lgSize","lgStart","lgOrder","size","children"]);return e.createElement("div",r()({},w,{className:g()(i,"mdc-layout-grid__cell",(n={"mdc-layout-grid__cell--align-top":a,"mdc-layout-grid__cell--align-middle":c,"mdc-layout-grid__cell--align-bottom":s,"mdc-layout-grid__cell--align-right":u,"mdc-layout-grid__cell--align-left":d,"mdc-layout-grid__cell--align-right-phone":f,"mdc-layout-grid__cell--align-right-tablet":m,"mdc-layout-grid__cell--align-right-desktop":p},o()(n,"mdc-layout-grid__cell--span-".concat(S),12>=S&&S>0),o()(n,"mdc-layout-grid__cell--span-".concat(y,"-desktop"),12>=y&&y>0),o()(n,"mdc-layout-grid__cell--start-".concat(k,"-desktop"),12>=k&&k>0),o()(n,"mdc-layout-grid__cell--order-".concat(j,"-desktop"),12>=j&&j>0),o()(n,"mdc-layout-grid__cell--span-".concat(E,"-tablet"),8>=E&&E>0),o()(n,"mdc-layout-grid__cell--start-".concat(_,"-tablet"),8>=_&&_>0),o()(n,"mdc-layout-grid__cell--order-".concat(O,"-tablet"),8>=O&&O>0),o()(n,"mdc-layout-grid__cell--span-".concat(h,"-phone"),4>=h&&h>0),o()(n,"mdc-layout-grid__cell--start-".concat(v,"-phone"),4>=v&&v>0),o()(n,"mdc-layout-grid__cell--order-".concat(b,"-phone"),4>=b&&b>0),n))}),A)}Cell.propTypes={smSize:u.a.number,smStart:u.a.number,smOrder:u.a.number,mdSize:u.a.number,mdStart:u.a.number,mdOrder:u.a.number,lgSize:u.a.number,lgStart:u.a.number,lgOrder:u.a.number,size:u.a.number,alignTop:u.a.bool,alignMiddle:u.a.bool,alignBottom:u.a.bool,alignRight:u.a.bool,alignLeft:u.a.bool,smAlignRight:u.a.bool,mdAlignRight:u.a.bool,lgAlignRight:u.a.bool,className:u.a.string,children:u.a.node},Cell.defaultProps={className:"",size:0,smSize:0,smStart:0,smOrder:0,mdSize:0,mdStart:0,mdOrder:0,lgSize:0,lgStart:0,lgOrder:0}}).call(this,n(4))},124:function(e,t,n){"use strict";(function(e){var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),g=Object(d.forwardRef)((function(t,n){var i=t.className,a=t.children,c=o()(t,["className","children"]);return e.createElement("div",r()({ref:n,className:u()("mdc-layout-grid__inner",i)},c),a)}));g.displayName="Row",g.propTypes={className:l.a.string,children:l.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(4))},125:function(e,t,n){"use strict";(function(e){var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(0),g=Object(d.forwardRef)((function(t,n){var i=t.alignLeft,a=t.fill,c=t.className,l=t.children,s=t.collapsed,d=o()(t,["alignLeft","fill","className","children","collapsed"]);return e.createElement("div",r()({className:u()("mdc-layout-grid",c,{"mdc-layout-grid--align-left":i,"mdc-layout-grid--collapsed":s,"mdc-layout-grid--fill":a})},d,{ref:n}),l)}));g.displayName="Grid",g.propTypes={alignLeft:l.a.bool,fill:l.a.bool,className:l.a.string,collapsed:l.a.bool,children:l.a.node},g.defaultProps={className:""},t.a=g}).call(this,n(4))},126:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{fill:"none",fillRule:"evenodd"},i.createElement("circle",{fill:"currentColor",cx:6.5,cy:6.5,r:6.5}),i.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"#FFF"}),".");t.a=function SvgArrow(e){return i.createElement("svg",r({viewBox:"0 0 13 13"},e),a)}},1267:function(e,t,n){"use strict";n.r(t),function(e){var i=n(3),r=n.n(i),a=n(811);Object(a.b)(r.a);var o=Object(a.a)(r.a);Object(a.c)(o),void 0===e.googlesitekit&&(e.googlesitekit={}),e.googlesitekit.widgets=o,t.default=o}.call(this,n(28))},127:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{fill:"none",fillRule:"evenodd"},i.createElement("circle",{fill:"#FFF",cx:6.5,cy:6.5,r:6.5}),i.createElement("path",{d:"M3.461 6.96h5.15L6.36 9.21a.464.464 0 00.325.79.459.459 0 00.325-.135l3.037-3.038a.459.459 0 000-.65L7.015 3.135a.46.46 0 00-.65.65L8.61 6.039H3.461a.462.462 0 00-.461.46c0 .254.207.462.461.462z",fill:"currentColor"}),".");t.a=function SvgArrowInverse(e){return i.createElement("svg",r({viewBox:"0 0 13 13"},e),a)}},128:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M12 20l-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6L12 20z"});t.a=function SvgBack(e){return i.createElement("svg",r({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},13:function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var i="core/site",r="primary",a="secondary"},130:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n(14),r=function(e){return Object(i.isFinite)(e)?e:0}},131:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M5.825 22l2.325-7.6L2 10h7.6L12 2l2.4 8H22l-6.15 4.4 2.325 7.6L12 17.3 5.825 22z",fill:"currentColor"});t.a=function SvgStarFill(e){return i.createElement("svg",r({viewBox:"0 0 24 24",fill:"none"},e),a)}},132:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InfoTooltip}));var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(10),l=n(325);function InfoTooltip(t){var n=t.onOpen,i=t.title,a=t.tooltipClassName;return i?e.createElement(c.Tooltip,{className:"googlesitekit-info-tooltip",tooltipClassName:r()("googlesitekit-info-tooltip__content",a),title:i,placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,interactive:!0,onOpen:n},e.createElement("span",null,e.createElement(l.a,{width:"16",height:"16"}))):null}InfoTooltip.propTypes={onOpen:o.a.func,title:o.a.oneOfType([o.a.string,o.a.element]),tooltipClassName:o.a.string}}).call(this,n(4))},134:function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(38),l=n(2),s=n(20),u=n(34);function SourceLink(t){var n=t.name,i=t.href,r=t.className,a=t.external;return Object(u.a)()?null:e.createElement("div",{className:o()("googlesitekit-source-link",r)},Object(c.a)(Object(l.sprintf)(
/* translators: %s: source link */
Object(l.__)("Source: %s","google-site-kit"),"<a>".concat(n,"</a>")),{a:e.createElement(s.a,{key:"link",href:i,external:a})}))}SourceLink.propTypes={name:r.a.string,href:r.a.string,className:r.a.string,external:r.a.bool},SourceLink.defaultProps={name:"",href:"",className:"",external:!1},t.a=SourceLink}).call(this,n(4))},135:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportErrorActions}));var i=n(6),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=n(38),s=n(2),u=n(3),d=n(10),g=n(13),f=n(19),m=n(35),p=n(34),h=n(20);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportErrorActions(t){var n=t.moduleSlug,i=t.error,r=t.GetHelpLink,a=t.hideGetHelpLink,o=t.buttonVariant,v=t.onRetry,E=t.onRequestAccess,_=t.getHelpClassName,O=t.RequestAccessButton,y=t.RetryButton,k=Object(p.a)(),j=Object(u.useSelect)((function(e){return e(f.a).getModuleStoreName(n)})),S=Object(u.useSelect)((function(e){var t;return"function"==typeof(null===(t=e(j))||void 0===t?void 0:t.getServiceEntityAccessURL)?e(j).getServiceEntityAccessURL():null})),A=Array.isArray(i)?i:[i],w=Object(u.useSelect)((function(e){return A.map((function(t){var n,i=null===(n=e(j))||void 0===n?void 0:n.getSelectorDataForError(t);return b(b({},t),{},{selectorData:i})}))})),T=null==w?void 0:w.filter((function(e){return Object(m.d)(e,e.selectorData)&&"getReport"===e.selectorData.name})),C=!!T.length,N=Object(u.useSelect)((function(e){var t=b({},C?T[0]:A[0]);return Object(m.e)(t)&&(t.code="".concat(n,"_insufficient_permissions")),e(g.c).getErrorTroubleshootingLinkURL(t)})),D=Object(u.useDispatch)(),R=A.some((function(e){return Object(m.e)(e)})),x=Object(c.useCallback)((function(){T.forEach((function(e){var t=e.selectorData;D(t.storeName).invalidateResolution(t.name,t.args)})),null==v||v()}),[D,T,v]),M=S&&R&&!k;return e.createElement("div",{className:"googlesitekit-report-error-actions"},M&&("function"==typeof O?e.createElement(O,{requestAccessURL:S}):e.createElement(d.Button,{onClick:E,href:S,target:"_blank",danger:"danger"===o,tertiary:"tertiary"===o},Object(s.__)("Request access","google-site-kit"))),C&&e.createElement(c.Fragment,null,"function"==typeof y?e.createElement(y,{handleRetry:x}):e.createElement(d.Button,{onClick:x,danger:"danger"===o,tertiary:"tertiary"===o},Object(s.__)("Retry","google-site-kit")),!a&&e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(l.a)(Object(s.__)("Retry didn’t work? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(h.a,{href:N,external:!0,hideExternalIndicator:!0},Object(s.__)("Get help","google-site-kit"))}))),!C&&!a&&e.createElement("div",{className:_},"function"==typeof r?e.createElement(r,{linkURL:N}):e.createElement(h.a,{href:N,external:!0,hideExternalIndicator:!0},Object(s.__)("Get help","google-site-kit"))))}ReportErrorActions.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired,GetHelpLink:o.a.elementType,hideGetHelpLink:o.a.bool,buttonVariant:o.a.string,onRetry:o.a.func,onRequestAccess:o.a.func,getHelpClassName:o.a.string,RequestAccessButton:o.a.elementType,RetryButton:o.a.elementType}}).call(this,n(4))},137:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.925 1 1 5.925 1 12s4.925 11 11 11 11-4.925 11-11S18.075 1 12 1zm4.806 8.592l.592-.806-1.612-1.184-.592.806-3.89 5.296c-.166.226-.36.296-.512.296-.152 0-.346-.07-.512-.296l-1.474-2.007-.592-.806-1.612 1.184.592.806 1.474 2.007C9.191 15.6 9.971 16 10.792 16c.821 0 1.6-.4 2.124-1.112l3.89-5.296z",fill:"currentColor"});t.a=function SvgCheckFill(e){return i.createElement("svg",r({viewBox:"0 0 24 24",fill:"none"},e),a)}},139:function(e,t,n){"use strict";var i=n(0),r=Object(i.createContext)(!1);t.a=r},143:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n(0),r=n(3),a=n(47);function o(e,t,n){var o=Object(r.useDispatch)(a.a),c=o.setWidgetState,l=o.unsetWidgetState;Object(i.useEffect)((function(){return c(e,t,n),function(){l(e,t,n)}}),[e,t,n,c,l])}},145:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var i=n(6),r=n.n(i),a=n(2),o=n(7),c=n(13),l=n(8);function s(e,t,n){return e(l.r).hasConversionReportingEvents(this.requiredConversionEventName)||e(o.a).isKeyMetricActive(n)}var u,d=n(26);function g(e,t){return!t||!(!t||!e(l.r).getAdSenseLinked())}function f(e,t){return!t||e(l.r).hasCustomDimensions(this.requiredCustomDimensions)}var m=(u={},r()(u,o.f,{title:Object(a.__)("Top earning pages","google-site-kit"),description:Object(a.__)("Pages that generated the most AdSense revenue","google-site-kit"),infoTooltip:Object(a.__)("Pages that generated the most AdSense revenue","google-site-kit"),displayInSelectionPanel:g,displayInList:g,metadata:{group:d.b.SLUG}}),r()(u,o.y,{title:Object(a.__)("Top recent trending pages","google-site-kit"),description:Object(a.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),infoTooltip:Object(a.__)("Pages with the most pageviews published in the last 3 days","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_date"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:d.b.SLUG}}),r()(u,o.l,{title:Object(a.__)("Most popular authors by pageviews","google-site-kit"),description:Object(a.__)("Authors whose posts got the most visits","google-site-kit"),infoTooltip:Object(a.__)("Authors whose posts got the most visits","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_author"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:d.b.SLUG}}),r()(u,o.p,{title:Object(a.__)("Top categories by pageviews","google-site-kit"),description:Object(a.__)("Categories that your site visitors viewed the most","google-site-kit"),infoTooltip:Object(a.__)("Categories that your site visitors viewed the most","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_categories"],displayInSelectionPanel:f,displayInWidgetArea:f,displayInList:f,metadata:{group:d.b.SLUG}}),r()(u,o.m,{title:Object(a.__)("Most popular content by pageviews","google-site-kit"),description:Object(a.__)("Pages that brought in the most visitors","google-site-kit"),infoTooltip:Object(a.__)("Pages your visitors read the most","google-site-kit"),metadata:{group:d.b.SLUG}}),r()(u,o.n,{title:Object(a.__)("Most popular products by pageviews","google-site-kit"),description:Object(a.__)("Products that brought in the most visitors","google-site-kit"),requiredCustomDimensions:["googlesitekit_post_type"],displayInSelectionPanel:function(e){return e(o.a).isKeyMetricActive(o.n)||e(c.c).getProductPostType()},displayInWidgetArea:f,metadata:{group:d.f.SLUG}}),r()(u,o.k,{title:Object(a.__)("Pages per visit","google-site-kit"),description:Object(a.__)("Number of pages visitors viewed per session on average","google-site-kit"),infoTooltip:Object(a.__)("Number of pages visitors viewed per session on average","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.F,{title:Object(a.__)("Visit length","google-site-kit"),description:Object(a.__)("Average duration of engaged visits","google-site-kit"),infoTooltip:Object(a.__)("Average duration of engaged visits","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.E,{title:Object(a.__)("Visits per visitor","google-site-kit"),description:Object(a.__)("Average number of sessions per site visitor","google-site-kit"),infoTooltip:Object(a.__)("Average number of sessions per site visitor","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.i,{title:Object(a.__)("Most engaging pages","google-site-kit"),description:Object(a.__)("Pages with the highest engagement rate","google-site-kit"),infoTooltip:Object(a.__)("Pages with the highest engagement rate","google-site-kit"),metadata:{group:d.b.SLUG}}),r()(u,o.h,{title:Object(a.__)("Least engaging pages","google-site-kit"),description:Object(a.__)("Pages with the highest percentage of visitors that left without engagement with your site","google-site-kit"),infoTooltip:Object(a.__)("Percentage of visitors that left without engagement with your site","google-site-kit"),metadata:{group:d.b.SLUG}}),r()(u,o.z,{title:Object(a.__)("Top pages by returning visitors","google-site-kit"),description:Object(a.__)("Pages that attracted the most returning visitors","google-site-kit"),infoTooltip:Object(a.__)("Pages that attracted the most returning visitors","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.j,{title:Object(a.__)("New visitors","google-site-kit"),description:Object(a.__)("How many new visitors you got and how the overall audience changed","google-site-kit"),infoTooltip:Object(a.__)("Portion of visitors who visited your site for the first time in this timeframe","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.o,{title:Object(a.__)("Returning visitors","google-site-kit"),description:Object(a.__)("Portion of people who visited your site more than once","google-site-kit"),infoTooltip:Object(a.__)("Portion of your site’s visitors that returned at least once in this timeframe","google-site-kit"),metadata:{group:d.h.SLUG}}),r()(u,o.A,{title:Object(a.__)("Top traffic source","google-site-kit"),description:Object(a.__)("Channel which brought in the most visitors to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most visitors to your site","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.B,{title:Object(a.__)("Top traffic source driving add to cart","google-site-kit"),description:Object(a.__)("Channel which brought in the most add to cart events to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most add to cart events to your site","google-site-kit"),requiredConversionEventName:[l.l.ADD_TO_CART],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.C,{title:Object(a.__)("Top traffic source driving leads","google-site-kit"),description:Object(a.__)("Channel which brought in the most leads to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most leads to your site","google-site-kit"),requiredConversionEventName:[l.l.SUBMIT_LEAD_FORM,l.l.CONTACT,l.l.GENERATE_LEAD],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.e.SLUG}}),r()(u,o.D,{title:Object(a.__)("Top traffic source driving purchases","google-site-kit"),description:Object(a.__)("Channel which brought in the most purchases to your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most purchases to your site","google-site-kit"),requiredConversionEventName:[l.l.PURCHASE],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.g,{title:Object(a.__)("Most engaged traffic source","google-site-kit"),description:Object(a.__)("Visitors coming via this channel spent the most time on your site","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in the most visitors who had a meaningful engagement with your site","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.u,{title:Object(a.__)("Top converting traffic source","google-site-kit"),description:Object(a.__)("Channel which brought in the most visits that resulted in conversions","google-site-kit"),infoTooltip:Object(a.__)("Channel (e.g. social, paid, search) that brought in visitors who generated the most conversions","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.q,{title:Object(a.__)("Top cities driving traffic","google-site-kit"),description:Object(a.__)("Which cities you get the most visitors from","google-site-kit"),infoTooltip:Object(a.__)("The cities where most of your visitors came from","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.s,{title:Object(a.__)("Top cities driving leads","google-site-kit"),description:Object(a.__)("Cities driving the most contact form submissions","google-site-kit"),infoTooltip:Object(a.__)("Cities driving the most contact form submissions","google-site-kit"),requiredConversionEventName:[l.l.SUBMIT_LEAD_FORM,l.l.CONTACT,l.l.GENERATE_LEAD],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.e.SLUG}}),r()(u,o.r,{title:Object(a.__)("Top cities driving add to cart","google-site-kit"),description:Object(a.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),infoTooltip:Object(a.__)("Cities where visitors most frequently add products to their carts","google-site-kit"),requiredConversionEventName:[l.l.ADD_TO_CART],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.t,{title:Object(a.__)("Top cities driving purchases","google-site-kit"),description:Object(a.__)("Cities driving the most purchases","google-site-kit"),infoTooltip:Object(a.__)("Cities driving the most purchases","google-site-kit"),requiredConversionEventName:[l.l.PURCHASE],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.w,{title:Object(a.__)("Top device driving purchases","google-site-kit"),description:Object(a.__)("Top device driving the most purchases","google-site-kit"),infoTooltip:Object(a.__)("Top device driving the most purchases","google-site-kit"),requiredConversionEventName:[l.l.PURCHASE],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.f.SLUG}}),r()(u,o.v,{title:Object(a.__)("Top countries driving traffic","google-site-kit"),description:Object(a.__)("Which countries you get the most visitors from","google-site-kit"),infoTooltip:Object(a.__)("The countries where most of your visitors came from","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.G,{title:Object(a.__)("Top performing keywords","google-site-kit"),description:Object(a.__)("What people searched for before they came to your site","google-site-kit"),infoTooltip:Object(a.__)("The top search queries for your site by highest clickthrough rate","google-site-kit"),metadata:{group:d.d.SLUG}}),r()(u,o.x,{title:Object(a.__)("Top pages driving leads","google-site-kit"),description:Object(a.__)("Pages on which forms are most frequently submitted","google-site-kit"),requiredConversionEventName:[l.l.SUBMIT_LEAD_FORM,l.l.CONTACT,l.l.GENERATE_LEAD],displayInSelectionPanel:s,displayInList:s,metadata:{group:d.e.SLUG}}),u)},151:function(e,t,n){"use strict";(function(e,i){var r=n(51),a=n.n(r),o=n(53),c=n.n(o),l=n(68),s=n.n(l),u=n(69),d=n.n(u),g=n(49),f=n.n(g),m=n(1),p=n.n(m),h=n(0),v=n(2),b=n(54);function E(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=f()(e);if(t){var r=f()(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return d()(this,n)}}var _=function(t){s()(MediaErrorHandler,t);var n=E(MediaErrorHandler);function MediaErrorHandler(e){var t;return a()(this,MediaErrorHandler),(t=n.call(this,e)).state={error:null},t}return c()(MediaErrorHandler,[{key:"componentDidCatch",value:function(t,n){e.console.error("Caught an error:",t,n),this.setState({error:t})}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.errorMessage;return this.state.error?i.createElement(b.a,{message:n}):t}}]),MediaErrorHandler}(h.Component);_.defaultProps={errorMessage:Object(v.__)("Failed to load media","google-site-kit")},_.propTypes={children:p.a.node.isRequired,errorMessage:p.a.string.isRequired},t.a=_}).call(this,n(28),n(4))},154:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i=function(e){return new Array(null!=e?e:0).fill().map((function(e,t){return t}))}},159:function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(0),l=n(3),s=n(13),u=n(7),d=n(19),g=n(32),f=n(37),m=n(36),p=n(18);function h(e){var t=Object(p.a)(),n=Object(l.useSelect)((function(t){return t(d.a).getModule(e)})),i=Object(l.useSelect)((function(e){return e(u.a).hasCapability(u.K)})),a=Object(l.useDispatch)(d.a).activateModule,h=Object(l.useDispatch)(g.a).navigateTo,v=Object(l.useDispatch)(s.c).setInternalServerError,b=Object(c.useCallback)(o()(r.a.mark((function n(){var i,o,c;return r.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,a(e);case 2:if(i=n.sent,o=i.error,c=i.response,o){n.next=13;break}return n.next=8,Object(m.b)("".concat(t,"_widget-activation-cta"),"activate_module",e);case 8:return n.next=10,Object(f.f)("module_setup",e,{ttl:300});case 10:h(c.moduleReauthURL),n.next=14;break;case 13:v({id:"".concat(e,"-setup-error"),description:o.message});case 14:case"end":return n.stop()}}),n)}))),[a,e,h,v,t]);return(null==n?void 0:n.name)&&i?b:null}},160:function(e,t,n){"use strict";var i=n(139),r=(i.a.Consumer,i.a.Provider);t.a=r},163:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RecoverableModules}));var i=n(1),r=n.n(i),a=n(2),o=n(3),c=n(19),l=n(95);function RecoverableModules(t){var n=t.moduleSlugs,i=Object(o.useSelect)((function(e){var t=e(c.a).getModules();if(void 0!==t)return n.map((function(e){return t[e].name}))}));if(void 0===i)return null;var r=1===i.length?Object(a.sprintf)(
/* translators: %s: Module name */
Object(a.__)("%s data was previously shared by an admin who no longer has access. Please contact another admin to restore it.","google-site-kit"),i[0]):Object(a.sprintf)(
/* translators: %s: List of module names */
Object(a.__)("The data for the following modules was previously shared by an admin who no longer has access: %s. Please contact another admin to restore it.","google-site-kit"),i.join(Object(a._x)(", ","Recoverable modules","google-site-kit")));return e.createElement(l.a,{title:Object(a.__)("Data Unavailable","google-site-kit"),description:r})}RecoverableModules.propTypes={moduleSlugs:r.a.arrayOf(r.a.string).isRequired}}).call(this,n(4))},165:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var i=n(2),r="audience-segmentation-info-notice-ui",a="audience-segmentation-info-notice",o=[{slug:"new-visitors",content:Object(i.__)("The higher the portion of new visitors you have, the more your audience is growing. Looking at what content brings them to your site may give you insights on how to reach even more people.","google-site-kit")},{slug:"compare-metrics",content:Object(i.__)("Select up to three visitor groups to display on the dashboard and easily compare metrics between them.","google-site-kit")},{slug:"custom-audiences",content:Object(i.__)("Configure your own custom audiences in Analytics to gain deeper insights into visitor behavior, for example consider creating an “Existing customers” or “Subscribers” segment, depending on what goals you have for your site.","google-site-kit")},{slug:"purchasers",content:Object(i.__)("Select the Purchasers visitor group to gain insights into which visitors bring the most revenue to your site.","google-site-kit")},{slug:"returning-visitors",content:Object(i.__)("The more returning visitors your site has, the stronger and more loyal an audience you’re building. Check which content brings people back to your site - it might help you create a strategy to build a community.","google-site-kit")},{slug:"compare-new-returning",content:Object(i.__)("Compare the ratio of “new” to “returning” visitors – this can give you insights on whether you have more people stopping by as a one-off, or more loyal visitors.","google-site-kit")},{slug:"compare-cities",content:Object(i.__)("Check the cities which bring you more new vs more returning visitors – there might be new audiences you could engage with in locations you hadn’t thought about.","google-site-kit")}]},169:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n(2);function r(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t||{},r=n.slug,a=void 0===r?"":r,o=n.name,c=void 0===o?"":o,l=n.owner,s=void 0===l?{}:l;if(!a||!c)return e;var u="",d="";return"analytics-4"===a?e.match(/account/i)?u=Object(i.__)("Your Google account does not have sufficient permissions for this Analytics account, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/property/i)?u=Object(i.__)("Your Google account does not have sufficient permissions for this Analytics property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"):e.match(/view/i)&&(u=Object(i.__)("Your Google account does not have sufficient permissions for this Analytics view, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")):"search-console"===a&&(u=Object(i.__)("Your Google account does not have sufficient permissions for this Search Console property, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit")),u||(u=Object(i.sprintf)(
/* translators: %s: module name */
Object(i.__)("Your Google account does not have sufficient permissions to access %s data, so you won’t be able to see stats from it on the Site Kit dashboard.","google-site-kit"),c)),s&&s.login&&(d=Object(i.sprintf)(
/* translators: %s: owner name */
Object(i.__)('This service was originally connected by the administrator "%s" — you can contact them for more information.',"google-site-kit"),s.login)),d||(d=Object(i.__)("This service was originally connected by an administrator — you can contact them for more information.","google-site-kit")),"".concat(u," ").concat(d)}},17:function(e,t,n){"use strict";var i=n(254);n.d(t,"i",(function(){return i.a}));var r=n(319);n.d(t,"f",(function(){return r.a}));var a=n(320);n.d(t,"h",(function(){return a.a}));var o=n(321);n.d(t,"j",(function(){return o.a}));var c=n(318);n.d(t,"g",(function(){return c.a}));var l=n(91),s=n.n(l);n.d(t,"b",(function(){return s.a})),n.d(t,"c",(function(){return l.DialogContent})),n.d(t,"d",(function(){return l.DialogFooter}));var u=n(102);n.d(t,"a",(function(){return u.a})),n.d(t,"e",(function(){return u.b})),n.d(t,"k",(function(){return u.c}))},170:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportError}));var i=n(6),r=n.n(i),a=n(1),o=n.n(a),c=n(14),l=n(0),s=n(2),u=n(3),d=n(19),g=n(35),f=n(169),m=n(84),p=n(54),h=n(95),v=n(135),b=n(34);function E(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?E(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):E(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ReportError(t){var n,i=t.moduleSlug,r=t.error,a=Object(b.a)(),o=Object(u.useSelect)((function(e){return e(d.a).getModule(i)})),E=Array.isArray(r)?r:[r],O=function(e){return Object(g.e)(e)?a?(n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Access lost to %s","google-site-kit"),null==o?void 0:o.name),Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("The administrator sharing this module with you has lost access to the %s service, so you won’t be able to see stats from it on the Site Kit dashboard. You can contact them or another administrator to restore access.","google-site-kit"),null==o?void 0:o.name)):(n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Insufficient permissions in %s","google-site-kit"),null==o?void 0:o.name),Object(f.a)(e.message,o)):Object(g.b)(e)},y=Object(c.uniqWith)(E.map((function(e){var t;return _(_({},e),{},{message:O(e),reconnectURL:null===(t=e.data)||void 0===t?void 0:t.reconnectURL})})),(function(e,t){return e.message===t.message&&e.reconnectURL===t.reconnectURL})),k=E.some((function(e){return Object(g.e)(e)}));k||1!==y.length?!k&&y.length>1&&(n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Data errors in %s","google-site-kit"),null==o?void 0:o.name)):n=Object(s.sprintf)(
/* translators: %s: module name */
Object(s.__)("Data error in %s","google-site-kit"),null==o?void 0:o.name);var j=e.createElement(l.Fragment,null,y.map((function(t){var n,i=null==r||null===(n=r.data)||void 0===n?void 0:n.reconnectURL;return i?e.createElement(p.a,{key:t.message,message:t.message,reconnectURL:i}):e.createElement("p",{key:t.message},m.a.sanitize(t.message,{ALLOWED_TAGS:[]}))})));return e.createElement(h.a,{title:n,description:j,error:!0},e.createElement(v.a,{moduleSlug:i,error:r}))}ReportError.propTypes={moduleSlug:o.a.string.isRequired,error:o.a.oneOfType([o.a.arrayOf(o.a.object),o.a.object]).isRequired}}).call(this,n(4))},174:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ReportZero}));var i=n(1),r=n.n(i),a=n(2),o=n(3),c=n(19),l=n(95);function ReportZero(t){var n=t.moduleSlug,i=Object(o.useSelect)((function(e){return e(c.a).getModule(n)}));return e.createElement(l.a,{title:Object(a.sprintf)(
/* translators: %s: Module name */
Object(a.__)("%s Gathering Data","google-site-kit"),null==i?void 0:i.name),description:Object(a.sprintf)(
/* translators: %s: Module name */
Object(a.__)("%s data is not yet available, please check back later","google-site-kit"),null==i?void 0:i.name)})}ReportZero.propTypes={moduleSlug:r.a.string.isRequired}}).call(this,n(4))},175:function(e,t,n){"use strict";var i=n(216);n.d(t,"b",(function(){return i.a}));var r=n(221);n.d(t,"a",(function(){return r.a}))},176:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n(374);function r(e){return Object(i.a)(e)}},177:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileLoading}));var i=n(44);function AudienceTileLoading(){return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-loading"},e.createElement(i.a,{width:"100%",height:"20px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}),e.createElement(i.a,{width:"100%",height:"52px"}))}}).call(this,n(4))},179:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ActionsCTALinkDismiss}));var i=n(1),r=n.n(i),a=n(2),o=n(0),c=n(3),l=n(32),s=n(92),u=n(187);function ActionsCTALinkDismiss(t){var n=t.id,i=t.className,r=void 0===i?"googlesitekit-publisher-win__actions":i,d=t.ctaLink,g=t.ctaLabel,f=t.ctaDisabled,m=void 0!==f&&f,p=t.onCTAClick,h=t.ctaDismissOptions,v=t.isSaving,b=void 0!==v&&v,E=t.onDismiss,_=void 0===E?function(){}:E,O=t.dismissLabel,y=void 0===O?Object(a.__)("OK, Got it!","google-site-kit"):O,k=t.dismissOnCTAClick,j=void 0===k||k,S=t.dismissExpires,A=void 0===S?0:S,w=t.dismissOptions,T=void 0===w?{}:w,C=t.gaTrackingEventArgs,N=void 0===C?{}:C,D=Object(c.useSelect)((function(e){return!!d&&e(l.a).isNavigatingTo(d)}));return e.createElement(o.Fragment,null,e.createElement("div",{className:r},e.createElement(u.a,{id:n,ctaLink:d,ctaLabel:g,onCTAClick:p,dismissOnCTAClick:j,dismissExpires:A,dismissOptions:h,gaTrackingEventArgs:N,isSaving:b,isDisabled:m}),e.createElement(s.a,{id:n,primary:!1,dismissLabel:y,dismissExpires:A,disabled:D,onDismiss:_,dismissOptions:T,gaTrackingEventArgs:N})))}ActionsCTALinkDismiss.propTypes={id:r.a.string,className:r.a.string,ctaDisabled:r.a.bool,ctaLink:r.a.string,ctaLabel:r.a.string,onCTAClick:r.a.func,isSaving:r.a.bool,onDismiss:r.a.func,ctaDismissOptions:r.a.object,dismissLabel:r.a.string,dismissOnCTAClick:r.a.bool,dismissExpires:r.a.number,dismissOptions:r.a.object,gaTrackingEventArgs:r.a.object}}).call(this,n(4))},18:function(e,t,n){"use strict";var i=n(0),r=n(61);t.a=function(){return Object(i.useContext)(r.b)}},184:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeBadge}));var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(9);function ChangeBadge(t){var n=t.previousValue,i=t.currentValue,r=t.isAbsolute?i-n:Object(c.g)(n,i),a=r<0,l=0===r;return null===r?null:e.createElement("div",{className:o()("googlesitekit-change-badge",{"googlesitekit-change-badge--negative":a,"googlesitekit-change-badge--zero":l})},Object(c.B)(r,{style:"percent",signDisplay:"exceptZero",maximumFractionDigits:1}))}ChangeBadge.propTypes={isAbsolute:r.a.bool,previousValue:r.a.number.isRequired,currentValue:r.a.number.isRequired}}).call(this,n(4))},187:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CTALink}));var i=n(5),r=n.n(i),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(15),u=n.n(s),d=n(1),g=n.n(d),f=n(206),m=n(0),p=n(3),h=n(41),v=n(32),b=n(13),E=n(73),_=n(10);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function CTALink(t){var n=t.id,i=t.ctaLink,a=t.ctaLabel,o=t.onCTAClick,c=t.isSaving,s=t.dismissOnCTAClick,d=void 0!==s&&s,g=t.dismissExpires,O=void 0===g?0:g,k=t.dismissOptions,j=void 0===k?{}:k,S=t.gaTrackingEventArgs,A=t.isDisabled,w=void 0!==A&&A,T=Object(m.useState)(!1),C=u()(T,2),N=C[0],D=C[1],R=Object(f.a)(),x=Object(E.a)(n,null==S?void 0:S.category),M=Object(p.useSelect)((function(e){return!!i&&e(v.a).isNavigatingTo(i)})),I=Object(p.useDispatch)(b.c),B=I.clearError,P=I.receiveError,L=Object(p.useDispatch)(h.a).dismissNotification,F=Object(p.useDispatch)(v.a).navigateTo,z=function(){var e=l()(r.a.mark((function e(t){var a,c,l;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return B("notificationAction",[n]),t.persist(),!t.defaultPrevented&&i&&t.preventDefault(),D(!0),e.next=6,null==o?void 0:o(t);case 6:if(e.t0=e.sent,e.t0){e.next=9;break}e.t0={};case 9:if(a=e.t0,c=a.error,R()&&D(!1),!c){e.next=15;break}return P(c,"notificationAction",[n]),e.abrupt("return");case 15:return l=[x.confirm()],d&&l.push(L(n,y(y({},j),{},{expiresInSeconds:O}))),e.next=19,Promise.all(l);case 19:i&&F(i);case 20:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(_.SpinnerButton,{className:"googlesitekit-notification__cta",href:i,onClick:z,disabled:N||M||w,isSaving:N||M||c},a)}CTALink.propTypes={id:g.a.string,ctaLink:g.a.string,ctaLabel:g.a.string,onCTAClick:g.a.func,dismissOnCTAClick:g.a.bool,dismissExpires:g.a.number,dismissOptions:g.a.object,isDisabled:g.a.bool}}).call(this,n(4))},19:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return r}));var i="core/modules",r="insufficient_module_dependencies"},2:function(e,t){e.exports=googlesitekit.i18n},20:function(e,t,n){"use strict";(function(e){var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(146),g=n(0),f=n(2),m=n(126),p=n(127),h=n(128),v=n(70),b=n(76),E=Object(g.forwardRef)((function(t,n){var i,a=t["aria-label"],c=t.secondary,s=void 0!==c&&c,u=t.arrow,g=void 0!==u&&u,E=t.back,_=void 0!==E&&E,O=t.caps,y=void 0!==O&&O,k=t.children,j=t.className,S=void 0===j?"":j,A=t.danger,w=void 0!==A&&A,T=t.disabled,C=void 0!==T&&T,N=t.external,D=void 0!==N&&N,R=t.hideExternalIndicator,x=void 0!==R&&R,M=t.href,I=void 0===M?"":M,B=t.inverse,P=void 0!==B&&B,L=t.noFlex,F=void 0!==L&&L,z=t.onClick,W=t.small,V=void 0!==W&&W,U=t.standalone,G=void 0!==U&&U,H=t.linkButton,q=void 0!==H&&H,K=t.to,Y=t.leadingIcon,X=t.trailingIcon,Z=o()(t,["aria-label","secondary","arrow","back","caps","children","className","danger","disabled","external","hideExternalIndicator","href","inverse","noFlex","onClick","small","standalone","linkButton","to","leadingIcon","trailingIcon"]),$=I||K||!z?K?"ROUTER_LINK":D?"EXTERNAL_LINK":"LINK":C?"BUTTON_DISABLED":"BUTTON",J="BUTTON"===$||"BUTTON_DISABLED"===$?"button":"ROUTER_LINK"===$?d.b:"a",Q=("EXTERNAL_LINK"===$&&(i=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit")),"BUTTON_DISABLED"===$&&(i=Object(f._x)("(disabled)","screen reader text","google-site-kit")),i?a?"".concat(a," ").concat(i):"string"==typeof k?"".concat(k," ").concat(i):void 0:a),ee=Y,te=X;return _&&(ee=e.createElement(h.a,{width:14,height:14})),D&&!x&&(te=e.createElement(v.a,{width:14,height:14})),g&&!P&&(te=e.createElement(m.a,{width:14,height:14})),g&&P&&(te=e.createElement(p.a,{width:14,height:14})),e.createElement(J,r()({"aria-label":Q,className:l()("googlesitekit-cta-link",S,{"googlesitekit-cta-link--secondary":s,"googlesitekit-cta-link--inverse":P,"googlesitekit-cta-link--small":V,"googlesitekit-cta-link--caps":y,"googlesitekit-cta-link--danger":w,"googlesitekit-cta-link--disabled":C,"googlesitekit-cta-link--standalone":G,"googlesitekit-cta-link--link-button":q,"googlesitekit-cta-link--no-flex":!!F}),disabled:C,href:"LINK"!==$&&"EXTERNAL_LINK"!==$||C?void 0:I,onClick:z,rel:"EXTERNAL_LINK"===$?"noopener noreferrer":void 0,ref:n,target:"EXTERNAL_LINK"===$?"_blank":void 0,to:K},Z),!!ee&&e.createElement(b.a,{marginRight:5},ee),e.createElement("span",{className:"googlesitekit-cta-link__contents"},k),!!te&&e.createElement(b.a,{marginLeft:5},te))}));E.propTypes={arrow:u.a.bool,back:u.a.bool,caps:u.a.bool,children:u.a.node,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,external:u.a.bool,hideExternalIndicator:u.a.bool,href:u.a.string,inverse:u.a.bool,leadingIcon:u.a.node,linkButton:u.a.bool,noFlex:u.a.bool,onClick:u.a.func,small:u.a.bool,standalone:u.a.bool,to:u.a.string,trailingIcon:u.a.node},t.a=E}).call(this,n(4))},202:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileWrapper}));var i=n(11),r=n.n(i),a=n(14),o=n(1),c=n.n(o),l=n(0),s=n(2),u=n(145),d=n(455),g=n(456),f=n(303),m=n(381),p=n(135),h=n(35),v=n(9),b=n(18);function MetricTileWrapper(t){var n,i,o,c=t.className,E=t.children,_=t.error,O=t.loading,y=t.moduleSlug,k=t.Widget,j=t.widgetSlug,S=t.title,A=void 0===S?null===(n=u.a[j])||void 0===n?void 0:n.title:S,w=t.infoTooltip,T=void 0===w?(null===(i=u.a[j])||void 0===i?void 0:i.infoTooltip)||(null===(o=u.a[j])||void 0===o?void 0:o.description):w,C=Object(b.a)(),N=!!_&&Object(a.castArray)(_).some(h.e),D=Object(l.useCallback)((function(){Object(v.I)("".concat(C,"_kmw"),"data_loading_error_retry")}),[C]);return Object(l.useEffect)((function(){_&&Object(v.I)("".concat(C,"_kmw"),"data_loading_error")}),[C,_]),_?e.createElement(f.a,{title:N?Object(s.__)("Insufficient permissions","google-site-kit"):Object(s.__)("Data loading failed","google-site-kit"),headerText:A,infoTooltip:T},e.createElement(p.a,{moduleSlug:y,error:_,onRetry:D,GetHelpLink:N?d.a:void 0,getHelpClassName:"googlesitekit-error-retry-text"})):e.createElement(k,{noPadding:!0},e.createElement("div",{className:r()("googlesitekit-km-widget-tile",c)},e.createElement(m.a,{title:A,infoTooltip:T,loading:O}),e.createElement("div",{className:"googlesitekit-km-widget-tile__body"},O&&e.createElement(g.a,null),!O&&E)))}MetricTileWrapper.propTypes={Widget:c.a.elementType.isRequired,loading:c.a.bool,title:c.a.string,infoTooltip:c.a.oneOfType([c.a.string,c.a.element]),moduleSlug:c.a.string.isRequired}}).call(this,n(4))},203:function(e,t,n){"use strict";(function(e){var i=n(14),r=n(1),a=n.n(r),o=n(0),c=n(50),l=n(3),s=n(35),u=n(23),d=n(165),g=n(457),f=n(113),m=n(9),p=n(18),h=Object(f.a)(g.a);function AudienceSegmentationErrorWidget(t){var n=t.Widget,r=t.errors,a=t.onRetry,c=t.showRetryButton,g=Object(p.a)(),f=Object(l.useDispatch)(u.b).setValue,v=r?Object(i.castArray)(r):[],b=v.some(s.e);return Object(o.useEffect)((function(){f(d.b,!0)}),[f]),e.createElement(h,{Widget:n,errors:v,onRetry:function(){Object(m.I)("".concat(g,"_audiences-all-tiles"),"data_loading_error_retry").finally((function(){f(d.b,!1),null==a||a()}))},onRequestAccess:function(){Object(m.I)("".concat(g,"_audiences-all-tiles"),"insufficient_permissions_error_request_access")},showRetryButton:c,onInView:function(){var e=b?"insufficient_permissions_error":"data_loading_error";Object(m.I)("".concat(g,"_audiences-all-tiles"),e)}})}AudienceSegmentationErrorWidget.propTypes={Widget:a.a.elementType.isRequired,errors:a.a.oneOfType([a.a.object,a.a.arrayOf(a.a.object)]).isRequired,onRetry:a.a.func,showRetryButton:a.a.bool},t.a=Object(c.a)({moduleName:"analytics-4"})(AudienceSegmentationErrorWidget)}).call(this,n(4))},204:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return OverlayNotification}));var i=n(591),r=n(11),a=n.n(r),o=n(1),c=n.n(o),l=n(0),s=n(3),u=n(23),d=n(24);function OverlayNotification(t){var n=t.className,r=t.children,o=t.GraphicDesktop,c=t.GraphicMobile,g=t.notificationID,f=t.onShow,m=t.shouldShowNotification,p=Object(d.e)(),h=Object(s.useSelect)((function(e){return e(u.b).isShowingOverlayNotification(g)})),v=Object(s.useDispatch)(u.b).setOverlayNotificationToShow;if(Object(l.useEffect)((function(){m&&!h&&(v(g),null==f||f())}),[h,g,f,v,m]),!m||!h)return null;var b=a()("googlesitekit-overlay-notification",n);return p===d.b?e.createElement("div",{className:b},r,c&&e.createElement(c,null)):e.createElement(i.a,{direction:"up",in:h},e.createElement("div",{className:b},o&&e.createElement(o,null),r))}OverlayNotification.propTypes={className:c.a.string,children:c.a.node,GraphicDesktop:c.a.elementType,GraphicMobile:c.a.elementType,onShow:c.a.func,notificationID:c.a.string.isRequired,shouldShowNotification:c.a.bool}}).call(this,n(4))},207:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5 22c-1.522 0-2.952-.284-4.29-.852a11.303 11.303 0 01-3.493-2.366 11.303 11.303 0 01-2.365-3.492A10.86 10.86 0 01.5 11c0-1.522.284-2.952.853-4.29a11.302 11.302 0 012.364-3.493A10.92 10.92 0 017.21.88 10.567 10.567 0 0111.5 0c1.522 0 2.952.293 4.29.88a10.92 10.92 0 013.492 2.337c.99.99 1.77 2.155 2.338 3.493.587 1.338.88 2.768.88 4.29 0 1.522-.293 2.952-.88 4.29a10.92 10.92 0 01-2.338 3.492c-.99.99-2.154 1.779-3.492 2.366A10.86 10.86 0 0111.5 22zm0-14.3c.312 0 .569-.1.77-.303.22-.22.33-.485.33-.797a.999.999 0 00-.33-.77.999.999 0 00-.77-.33c-.311 0-.577.11-.797.33a1.043 1.043 0 00-.303.77c0 .312.101.578.303.798.22.201.486.302.797.302zm-1.1 8.8V9.9h2.2v6.6h-2.2z",fill:"currentColor"});t.a=function SvgInfoCircle(e){return i.createElement("svg",r({viewBox:"0 0 23 22",fill:"none"},e),a)}},214:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M2 5.309l1.474 2.14c.69 1.001 1.946 1.001 2.636 0L10 1.8",stroke:"currentColor",strokeWidth:1.6,strokeLinecap:"square"});t.a=function SvgCheck2(e){return i.createElement("svg",r({viewBox:"0 0 12 9",fill:"none"},e),a)}},216:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var i=n(5),r=n.n(i),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(0),u=n(3),d=n(13),g=n(23);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e){var t=Object(u.useDispatch)(g.b).setValue,n=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.2")})),i=Object(u.useSelect)((function(e){return e(d.c).hasMinimumWordPressVersion("6.4")}));return Object(s.useCallback)(l()(r.a.mark((function a(){var o,c,l,s;return r.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(o=document.querySelector("#adminmenu").offsetHeight>0){r.next=7;break}if(!(c=document.getElementById("wp-admin-bar-menu-toggle"))){r.next=7;break}return c.firstChild.click(),r.next=7,new Promise((function(e){setTimeout(e,0)}));case 7:"#adminmenu [href*='page=googlesitekit-dashboard']",(l=!!document.querySelector("".concat("#adminmenu [href*='page=googlesitekit-dashboard']","[aria-haspopup=true]")))&&document.querySelector("#adminmenu [href*='page=googlesitekit-dashboard']").click(),n&&!i&&(s=document.hasFocus,document.hasFocus=function(){return document.hasFocus=s,!1}),t("admin-menu-tooltip",m({isTooltipVisible:!0,rehideAdminMenu:!o,rehideAdminSubMenu:l},e));case 12:case"end":return r.stop()}}),a)}))),[n,i,t,e])}},22:function(e,t,n){"use strict";n.d(t,"n",(function(){return i})),n.d(t,"l",(function(){return r})),n.d(t,"o",(function(){return a})),n.d(t,"m",(function(){return o})),n.d(t,"t",(function(){return c})),n.d(t,"h",(function(){return l})),n.d(t,"s",(function(){return s})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"r",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"u",(function(){return m})),n.d(t,"v",(function(){return p})),n.d(t,"q",(function(){return h})),n.d(t,"p",(function(){return v})),n.d(t,"b",(function(){return b})),n.d(t,"e",(function(){return E})),n.d(t,"a",(function(){return _})),n.d(t,"d",(function(){return O})),n.d(t,"c",(function(){return y})),n.d(t,"f",(function(){return k})),n.d(t,"g",(function(){return j}));var i="mainDashboard",r="entityDashboard",a="mainDashboardViewOnly",o="entityDashboardViewOnly",c="userInput",l="activation",s="splash",u="adminBar",d="adminBarViewOnly",g="settings",f="adBlockingRecovery",m="wpDashboard",p="wpDashboardViewOnly",h="moduleSetup",v="metricSelection",b="key-metrics",E="traffic",_="content",O="speed",y="monetization",k=[i,r,a,o,c,s,g,h,v],j=[a,o,d,p]},221:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AdminMenuTooltip}));var i=n(0),r=n(3),a=n(223),o=n(23),c=n(9),l=n(18);function AdminMenuTooltip(){var t=Object(l.a)(),n=Object(r.useDispatch)(o.b).setValue,s=Object(r.useSelect)((function(e){return e(o.b).getValue("admin-menu-tooltip")||{isTooltipVisible:!1}})),u=s.isTooltipVisible,d=void 0!==u&&u,g=s.rehideAdminMenu,f=void 0!==g&&g,m=s.rehideAdminSubMenu,p=void 0!==m&&m,h=s.tooltipSlug,v=s.title,b=s.content,E=s.dismissLabel,_=Object(i.useCallback)((function(){var e;f&&(document.querySelector("#adminmenu").offsetHeight>0&&(null===(e=document.getElementById("wp-admin-bar-menu-toggle"))||void 0===e||e.click()));p&&document.querySelector("body").click(),h&&Object(c.I)("".concat(t,"_").concat(h),"tooltip_dismiss"),n("admin-menu-tooltip",void 0)}),[f,p,n,h,t]);return d?e.createElement(a.a,{target:'#adminmenu [href*="page=googlesitekit-settings"]',slug:"ga4-activation-banner-admin-menu-tooltip",title:v,content:b,dismissLabel:E,onView:function(){Object(c.I)("".concat(t,"_").concat(h),"tooltip_view")},onDismiss:_}):null}}).call(this,n(4))},223:function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return JoyrideTooltip}));var r=n(6),a=n.n(r),o=n(15),c=n.n(o),l=n(1),s=n(30),u=n(421),d=n(0),g=n(107),f=n(72),m=n(90);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function JoyrideTooltip(t){var n=t.title,r=t.content,a=t.dismissLabel,o=t.target,l=t.cta,p=void 0!==l&&l,v=t.className,b=t.styles,E=void 0===b?{}:b,_=t.slug,O=void 0===_?"":_,y=t.onDismiss,k=void 0===y?function(){}:y,j=t.onView,S=void 0===j?function(){}:j,A=t.onTourStart,w=void 0===A?function(){}:A,T=t.onTourEnd,C=void 0===T?function(){}:T,N=function(){return!!e.document.querySelector(o)},D=Object(d.useState)(N),R=c()(D,2),x=R[0],M=R[1];if(Object(u.a)((function(){N()&&M(!0)}),x?null:250),Object(d.useEffect)((function(){if(x&&e.ResizeObserver){var t=e.document.querySelector(o),n=new ResizeObserver((function(){e.dispatchEvent(new Event("resize"))}));return n.observe(t),function(){n.disconnect()}}}),[o,x]),!x)return null;var I=[{title:n,target:o,content:r,disableBeacon:!0,isFixed:!0,placement:"auto",cta:p,className:v}],B={close:a,last:a};return i.createElement(f.a,{slug:O},i.createElement(s.e,{callback:function(t){switch(t.type){case s.b.TOUR_START:w(),e.document.body.classList.add("googlesitekit-showing-tooltip");break;case s.b.TOUR_END:C(),e.document.body.classList.remove("googlesitekit-showing-tooltip");break;case s.b.STEP_AFTER:k();break;case s.b.TOOLTIP:S()}},disableOverlay:!0,disableScrolling:!0,spotlightPadding:0,floaterProps:m.b,locale:B,steps:I,styles:h(h(h({},m.c),E),{},{options:h(h({},m.c.options),null==E?void 0:E.options),spotlight:h(h({},m.c.spotlight),null==E?void 0:E.spotlight)}),tooltipComponent:g.a,run:!0}))}JoyrideTooltip.propTypes={title:l.PropTypes.node,content:l.PropTypes.string,dismissLabel:l.PropTypes.string,target:l.PropTypes.string.isRequired,onDismiss:l.PropTypes.func,onShow:l.PropTypes.func,className:l.PropTypes.string,styles:l.PropTypes.object,slug:l.PropTypes.string,onView:l.PropTypes.func}}).call(this,n(28),n(4))},23:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return r}));var i="core/ui",r="activeContextID"},24:function(e,t,n){"use strict";n.d(t,"d",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"e",(function(){return l}));var i=n(79),r="xlarge",a="desktop",o="tablet",c="small";function l(){var e=Object(i.a)();return e>1280?r:e>960?a:e>600?o:c}},242:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return NotificationWithSVG}));var i=n(21),r=n.n(i),a=n(11),o=n.n(a),c=n(24),l=n(17),s=n(255);function NotificationWithSVG(t){var n=t.id,i=t.title,a=t.description,u=t.actions,d=t.SVG,g=t.primaryCellSizes,f=t.SVGCellSizes,m=Object(c.e)(),p={mdSize:(null==f?void 0:f.md)||8,lgSize:(null==f?void 0:f.lg)||6};return m===c.c&&(p={mdSize:(null==f?void 0:f.md)||8}),m===c.b&&(p={smSize:(null==f?void 0:f.sm)||12}),e.createElement("div",{className:"googlesitekit-widget-context"},e.createElement(l.e,{className:"googlesitekit-widget-area"},e.createElement(l.k,null,e.createElement(l.a,{size:12},e.createElement("div",{className:o()("googlesitekit-widget","googlesitekit-widget--no-padding","googlesitekit-setup-cta-banner","googlesitekit-setup-cta-banner--".concat(n))},e.createElement("div",{className:"googlesitekit-widget__body"},e.createElement(l.e,{collapsed:!0},e.createElement(l.k,null,e.createElement(l.a,{smSize:(null==g?void 0:g.sm)||12,mdSize:(null==g?void 0:g.md)||8,lgSize:(null==g?void 0:g.lg)||6,className:"googlesitekit-setup-cta-banner__primary-cell"},e.createElement("h3",{className:"googlesitekit-setup-cta-banner__title"},i),a,e.createElement(s.a,{id:n}),u),e.createElement(l.a,r()({alignBottom:!0,className:"googlesitekit-setup-cta-banner__svg-wrapper--".concat(n)},p),e.createElement(d,null))))))))))}}).call(this,n(4))},243:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return m})),n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return v}));var i=n(21),r=n.n(i),a=n(63),o=n.n(a),c=n(267),l=n(322),s=n(323),u=n(244),d=n(268),g=n(324),f=n(0),m=o()((function(e){return{widgetSlug:e,Widget:p(e)(c.a),WidgetRecoverableModules:p(e)(d.a),WidgetReportZero:p(e)(l.a),WidgetReportError:p(e)(s.a),WidgetNull:p(e)(u.a)}}));function p(t){return function(n){var i=Object(f.forwardRef)((function(i,a){return e.createElement(n,r()({},i,{ref:a,widgetSlug:t}))}));return i.displayName="WithWidgetSlug",(n.displayName||n.name)&&(i.displayName+="(".concat(n.displayName||n.name,")")),i}}var h=function(t){var n=m(t);return function(t){function DecoratedComponent(i){return e.createElement(t,r()({},i,n))}return DecoratedComponent.displayName="WithWidgetComponentProps",(t.displayName||t.name)&&(DecoratedComponent.displayName+="(".concat(t.displayName||t.name,")")),DecoratedComponent}},v=function(t){return function(n){function DecoratedComponent(i){return e.createElement(n,r()({},i,{WPDashboardReportError:p(t)(g.a)}))}return DecoratedComponent.displayName="WithWPDashboardWidgetComponentProps",(n.displayName||n.name)&&(DecoratedComponent.displayName+="(".concat(n.displayName||n.name,")")),DecoratedComponent}}}).call(this,n(4))},244:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetNull}));var i=n(6),r=n.n(i),a=n(1),o=n.n(a),c=n(143),l=n(74);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}var u={};function WidgetNull(t){var n=t.widgetSlug;return Object(c.a)(n,l.a,u),e.createElement(l.a,null)}WidgetNull.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:o.a.string.isRequired},l.a.propTypes)}).call(this,n(4))},246:function(e,t,n){"use strict";n.d(t,"d",(function(){return g})),n.d(t,"b",(function(){return m})),n.d(t,"c",(function(){return p.a})),n.d(t,"g",(function(){return p.c})),n.d(t,"a",(function(){return c.a})),n.d(t,"f",(function(){return h})),n.d(t,"e",(function(){return s}));var i=n(15),r=n.n(i),a=n(27),o=n.n(a),c=n(118),l=n(74);function s(e){return!!e&&e.Component===l.a}function u(e,t){if(9!==t)return[e,t];for(var n=(e=o()(e)).length-1;0!==t&&n>=0;)3===e[n]?(t-=3,e[n]=4):6===e[n]&&(t-=6,e[n]=8),n--;return[e,t]}function d(e,t){return(Array.isArray(t.width)?t.width:[t.width]).map((function(t){return{counter:e+c.c[t],width:t}}))}function g(e,t){var n=[],i=[];if(!(null==e?void 0:e.length))return{columnWidths:n,rowIndexes:i};var a=0,o=0,l=function(e,t){return e.counter-t.counter},g=function(e,t){var n=e.counter;return t.counter-n},f=function(e){return e.counter<=12};if(e.forEach((function(m,p){if(s(t[m.slug]))return n.push(0),void i.push(o);var h=d(a,m),v=function(e,t,n){for(;++e<t.length;)if(!s(n[t[e].slug]))return t[e];return null}(p,e,t);null!==v&&0!==d(h.sort(l)[0].counter,v).filter(f).length||h.some(f)&&(h=(h=h.sort(g)).filter(f));var b=h[0].width;if(i.push(o),(a+=c.c[b])>12){if(a-=c.c[b],i[p]++,9===a){var E=u(n,a),_=r()(E,2);n=_[0],a=_[1]}a=c.c[b],o++}else 12===a&&(a=0,o++);n.push(c.c[b])})),9===a){var m=u(n,a),p=r()(m,2);n=p[0],a=p[1]}return{columnWidths:n,rowIndexes:i}}var f=n(14);function m(e,t,n){var i=n.columnWidths,r=n.rowIndexes,a=[],l=o()(i);if(!(null==e?void 0:e.length))return{gridColumnWidths:l,overrideComponents:a};var s=null,u=-1,d=[];if(function(e,t){for(var n={},i=0;i<e.length;i++){var r,a=e[i],o=null==t?void 0:t[a.slug],l=null==o?void 0:o.Component,s=null==o||null===(r=o.metadata)||void 0===r?void 0:r.moduleSlug,u=c.b.includes(l);if(!l||!s||!u)return!1;if(n[s]){if(n[s]!==l)return!1}else n[s]=l}return!(Object.keys(n).length>1)}(e,t)){var g=Array.from({length:e.length-1}).fill(0);return{overrideComponents:[t[e[0].slug]],gridColumnWidths:[12].concat(o()(g))}}return e.forEach((function(n,o){var c,g,m,p,h;if(a.push(null),s=t[n.slug],u=r[o],s)if(g=s,m=t[null===(c=e[o+1])||void 0===c?void 0:c.slug],p=u,h=r[o+1],p===h&&Object(f.isEqual)(g,m))d.push(i[o]),l[o]=0;else if(d.length>0){d.push(i[o]);var v=d.reduce((function(e,t){return e+t}),0);a[o]=s,l[o]=v,d=[]}})),{gridColumnWidths:l,overrideComponents:a}}var p=n(243);function h(e){return(Array.isArray(e)?e:[e]).filter((function(e){return"string"==typeof e&&e.length>0}))}},247:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceErrorModal}));var i=n(1),r=n.n(i),a=n(38),o=n(2),c=n(3),l=n(20),s=n(109),u=n(72),d=n(13),g=n(8),f=n(35),m=n(9);function AudienceErrorModal(t){var n=t.apiErrors,i=t.hasOAuthError,r=t.inProgress,p=t.title,h=t.description,v=t.trackEventCategory,b=t.onCancel,E=void 0===b?function(){}:b,_=t.onRetry,O=void 0===_?function(){}:_,y=Array.isArray(n)?n:[n],k=Object(c.useSelect)((function(e){return e(d.c).getErrorTroubleshootingLinkURL({code:"analytics-4_insufficient_permissions"})})),j=Object(c.useSelect)((function(e){return e(g.r).getServiceEntityAccessURL()})),S=Object(c.useSelect)((function(e){return e(d.c).getErrorTroubleshootingLinkURL({code:"access_denied"})}));if(!y.length&&!i)return null;var A,w,T,C,N=y.some((function(e){return Object(f.e)(e)}));return i?(A=Object(o.__)("Analytics update failed","google-site-kit"),w=Object(a.a)(Object(o.__)("Setup was interrupted because you did not grant the necessary permissions. <HelpLink />","google-site-kit"),{HelpLink:e.createElement(l.a,{href:S,external:!0,hideExternalIndicator:!0},Object(o.__)("Get help","google-site-kit"))}),T=Object(o.__)("Retry","google-site-kit")):N?(A=Object(o.__)("Insufficient permissions","google-site-kit"),w=Object(a.a)(Object(o.__)("You’ll need to contact your administrator. Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(l.a,{href:k,external:!0,hideExternalIndicator:!0},Object(o.__)("Get help","google-site-kit"))}),T=Object(o.__)("Request access","google-site-kit"),C=j):(A=p||Object(o.__)("Failed to set up visitor groups","google-site-kit"),w=h||Object(o.__)("Oops! Something went wrong. Retry enabling groups.","google-site-kit"),T=Object(o.__)("Retry","google-site-kit")),e.createElement(u.a,null,e.createElement(s.a,{dialogActive:!0,buttonLink:C,title:A,subtitle:w,handleConfirm:function(){var e;e=i?"auth_error_retry":N?"insufficient_permissions_error_request_access":"setup_error_retry",Object(m.I)(v,e).finally((function(){N||O()}))},confirmButton:T,handleDialog:function(){var e;e=i?"auth_error_cancel":N?"insufficient_permissions_error_cancel":"setup_error_cancel",Object(m.I)(v,e).finally(E)},onOpen:function(){var e;e=i?"auth_error":N?"insufficient_permissions_error":"setup_error",Object(m.I)(v,e)},onClose:E,danger:!0,inProgress:r}))}AudienceErrorModal.propTypes={apiErrors:r.a.oneOfType([r.a.arrayOf(r.a.object),r.a.object,r.a.array]),hasOAuthError:r.a.bool,inProgress:r.a.bool,title:r.a.string,description:r.a.string,trackEventCategory:r.a.string,onCancel:r.a.func,onRetry:r.a.func}}).call(this,n(4))},248:function(e,t,n){"use strict";(function(e){var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=n(10),s=n(492),u=Object(c.forwardRef)((function(t,n){var i=t.className,a=t.content,o=t.dismissLabel,c=t.Icon,u=void 0===c?s.a:c,d=t.onDismiss;return e.createElement("div",{ref:n,className:r()("googlesitekit-audience-segmentation-info-notice",i)},e.createElement(u,{width:"20",height:"20"}),e.createElement("div",{className:"googlesitekit-audience-segmentation-info-notice__body"},e.createElement("p",null,a),o&&e.createElement(l.Button,{tertiary:!0,onClick:d,className:"googlesitekit-audience-segmentation-info-notice__dismiss"},o)))}));u.propTypes={className:o.a.string,content:o.a.string.isRequired,dismissLabel:o.a.string,Icon:o.a.elementType,onDismiss:o.a.func},t.a=u}).call(this,n(4))},250:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return BadgeWithTooltip}));var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(132);function BadgeWithTooltip(t){var n=t.className,i=void 0===n?"":n,r=t.label,a=t.onTooltipOpen,l=t.tooltipTitle;return e.createElement("span",{className:o()("googlesitekit-badge-with-tooltip","googlesitekit-badge",i)},r,l&&e.createElement(c.a,{onOpen:a,title:l}))}BadgeWithTooltip.propTypes={onTooltipOpen:r.a.func,tooltipTitle:r.a.node,className:r.a.string,label:r.a.node.isRequired}}).call(this,n(4))},251:function(e,t,n){"use strict";n.d(t,"a",(function(){return SurveyViewTrigger}));var i=n(0),r=n(1),a=n.n(r),o=n(3),c=n(13),l=n(7);function SurveyViewTrigger(e){var t=e.triggerID,n=e.ttl,r=void 0===n?0:n,a=Object(o.useSelect)((function(e){return e(c.c).isUsingProxy()})),s=Object(o.useDispatch)(l.a).triggerSurvey;return Object(i.useEffect)((function(){a&&s(t,{ttl:r})}),[a,t,r,s]),null}SurveyViewTrigger.propTypes={triggerID:a.a.string.isRequired,ttl:a.a.number}},253:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"e",(function(){return o})),n.d(t,"g",(function(){return c})),n.d(t,"k",(function(){return l})),n.d(t,"j",(function(){return s})),n.d(t,"h",(function(){return u})),n.d(t,"i",(function(){return d})),n.d(t,"f",(function(){return g})),n.d(t,"d",(function(){return f}));var i=1,r=2,a=3,o="enhanced-measurement-activation-banner-tooltip-state",c="enhanced-measurement-activation-banner-dismissed-item",l="_r.explorerCard..selmet",s="_r.explorerCard..seldim",u="_r..dataFilters",d="_r..nav",g="key-metrics-connect-ga4-cta-widget",f="ads-conversion-id-notice-dismissed-item"},255:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Error}));var i=n(1),r=n.n(i),a=n(0),o=n(3),c=n(13),l=n(54);function Error(t){var n=t.id,i=Object(o.useSelect)((function(e){return e(c.c).getError("notificationAction",[n])})),r=Object(o.useDispatch)(c.c).clearError;return Object(a.useEffect)((function(){return function(){r("notificationAction",[n])}}),[r,n]),i?e.createElement(l.a,{message:i.message}):null}Error.propTypes={id:r.a.string}}).call(this,n(4))},258:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return f}));var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=n(10),s=n(137),u=n(70),d=n(58),g=n(207),f={SUCCESS:"success",WARNING:"warning",INFO:"info"},m=Object(c.forwardRef)((function(t,n){var i=t.title,a=t.description,o=t.Icon,c=t.ctaLink,m=t.ctaLabel,p=t.className,h=t.onCTAClick,v=t.isCTALinkExternal,b=t.dismissLabel,E=t.onDismiss,_=t.variant,O=void 0===_?f.SUCCESS:_,y=t.hideIcon,k=void 0!==y&&y;return e.createElement("div",{ref:n,className:r()("googlesitekit-subtle-notification",{"googlesitekit-subtle-notification--success":O===f.SUCCESS,"googlesitekit-subtle-notification--warning":O===f.WARNING,"googlesitekit-subtle-notification--info":O===f.INFO},p)},!k&&e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},o&&e.createElement(o,{width:24,height:24}),!o&&O===f.SUCCESS&&e.createElement(s.a,{width:24,height:24}),!o&&O===f.WARNING&&e.createElement(d.a,{width:24,height:24}),!o&&O===f.INFO&&e.createElement(g.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,i),a&&e.createElement("p",{className:"googlesitekit-subtle-notification__secondary_description"},a)),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},b&&e.createElement(l.Button,{tertiary:!0,onClick:E},b),m&&e.createElement(l.Button,{className:"googlesitekit-subtle-notification__cta",href:c,onClick:h,target:v?"_blank":"_self",trailingIcon:v?e.createElement(u.a,{width:14,height:14}):void 0},m)))}));m.propTypes={title:o.a.node.isRequired,description:o.a.string,Icon:o.a.elementType,ctaLink:o.a.string,ctaLabel:o.a.string,className:o.a.string,onCTAClick:o.a.func,isCTALinkExternal:o.a.bool,dismissLabel:o.a.string,onDismiss:o.a.func,variant:o.a.oneOf(Object.values(f)),hideIcon:o.a.bool},t.b=m}).call(this,n(4))},26:function(e,t,n){"use strict";n.d(t,"l",(function(){return r})),n.d(t,"k",(function(){return a})),n.d(t,"j",(function(){return o})),n.d(t,"i",(function(){return c})),n.d(t,"a",(function(){return l})),n.d(t,"o",(function(){return s})),n.d(t,"n",(function(){return u})),n.d(t,"m",(function(){return d})),n.d(t,"c",(function(){return g})),n.d(t,"g",(function(){return f})),n.d(t,"h",(function(){return m})),n.d(t,"d",(function(){return p})),n.d(t,"e",(function(){return h})),n.d(t,"f",(function(){return v})),n.d(t,"b",(function(){return b}));var i=n(2),r="key-metrics-setup-cta-widget",a="googlesitekit-key-metrics-selection-panel-opened",o="key-metrics-selection-form",c="key-metrics-selected",l="key-metrics-effective-selection",s="key-metrics-unstaged-selection",u=2,d=8,g={SLUG:"current-selection",LABEL:Object(i.__)("Current selection","google-site-kit")},f={SLUG:"suggested",LABEL:Object(i.__)("Suggested","google-site-kit")},m={SLUG:"visitors",LABEL:Object(i.__)("Visitors","google-site-kit")},p={SLUG:"driving-traffic",LABEL:Object(i.__)("Driving traffic","google-site-kit")},h={SLUG:"generating-leads",LABEL:Object(i.__)("Generating leads","google-site-kit")},v={SLUG:"selling-products",LABEL:Object(i.__)("Selling products","google-site-kit")},b={SLUG:"content-performance",LABEL:Object(i.__)("Content performance","google-site-kit")}},260:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return f})),n.d(t,"b",(function(){return SpinnerButton}));var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(344),g=n(261),f={BEFORE:"before",AFTER:"after"};function SpinnerButton(t){var n=t.className,i=t.onClick,a=void 0===i?function(){}:i,c=t.isSaving,l=void 0!==c&&c,s=t.spinnerPosition,m=void 0===s?f.AFTER:s,p=o()(t,["className","onClick","isSaving","spinnerPosition"]);return e.createElement(d.a,r()({className:u()(n,"googlesitekit-button-icon--spinner",{"googlesitekit-button-icon--spinner__running":l,"googlesitekit-button-icon--spinner__before":m===f.BEFORE,"googlesitekit-button-icon--spinner__after":m===f.AFTER}),icon:l&&m===f.BEFORE?e.createElement(g.a,{size:14}):void 0,trailingIcon:l&&m===f.AFTER?e.createElement(g.a,{size:14}):void 0,onClick:a},p))}SpinnerButton.propTypes={className:l.a.string,onClick:l.a.func,isSaving:l.a.bool,spinnerPosition:l.a.oneOf(Object.values(f))}}).call(this,n(4))},261:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return CircularProgress}));var i=n(640);function CircularProgress(t){return e.createElement(i.a,t)}}).call(this,n(4))},267:function(e,t,n){"use strict";(function(e){var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=Object(c.forwardRef)((function(t,n){var i=t.children,a=t.className,o=t.widgetSlug,c=t.noPadding,l=t.Header,s=t.Footer;return e.createElement("div",{className:r()("googlesitekit-widget","googlesitekit-widget--".concat(o),{"googlesitekit-widget--no-padding":c},{"googlesitekit-widget--with-header":l},a),ref:n},l&&e.createElement("div",{className:"googlesitekit-widget__header"},e.createElement(l,null)),e.createElement("div",{className:"googlesitekit-widget__body"},i),s&&e.createElement("div",{className:"googlesitekit-widget__footer"},e.createElement(s,null)))}));l.defaultProps={children:void 0,noPadding:!1},l.propTypes={children:o.a.node,widgetSlug:o.a.string.isRequired,noPadding:o.a.bool,Header:o.a.elementType,Footer:o.a.elementType},t.a=l}).call(this,n(4))},268:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetRecoverableModules}));var i=n(6),r=n.n(i),a=n(21),o=n.n(a),c=n(27),l=n.n(c),s=n(25),u=n.n(s),d=n(1),g=n.n(d),f=n(0),m=n(143),p=n(163);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function WidgetRecoverableModules(t){var n=t.widgetSlug,i=t.moduleSlugs,r=u()(t,["widgetSlug","moduleSlugs"]),a=Object(f.useMemo)((function(){return{moduleSlug:l()(i).sort().join(","),moduleSlugs:i}}),[i]);return Object(m.a)(n,p.a,a),e.createElement(p.a,o()({moduleSlugs:i},r))}WidgetRecoverableModules.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:g.a.string.isRequired},p.a.propTypes)}).call(this,n(4))},269:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));n(8);function i(e){var t;return 400===(null==e?void 0:e.code)&&(null==e||null===(t=e.message)||void 0===t?void 0:t.includes("is not a valid dimension"))}},272:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileText}));var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(184),u=n(9),d=n(202);function MetricTileText(t){var n=t.metricValue,i=t.metricValueFormat,a=t.subText,c=t.previousValue,l=t.currentValue,g=o()(t,["metricValue","metricValueFormat","subText","previousValue","currentValue"]),f=Object(u.m)(i);return e.createElement(d.a,r()({className:"googlesitekit-km-widget-tile--text"},g),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-container"},e.createElement("div",{className:"googlesitekit-km-widget-tile__metric"},n),e.createElement("p",{className:"googlesitekit-km-widget-tile__subtext"},a)),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-change-container"},e.createElement(s.a,{previousValue:c,currentValue:l,isAbsolute:"percent"===(null==f?void 0:f.style)})))}MetricTileText.propTypes={metricValue:l.a.oneOfType([l.a.string,l.a.number]),subtext:l.a.string,previousValue:l.a.number,currentValue:l.a.number}}).call(this,n(4))},29:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i="core/forms"},3:function(e,t){e.exports=googlesitekit.data},302:function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(0),l=n(2),s=n(10),u=n(77),d=n(20);function NewBadge(t){var n=t.tooltipTitle,i=t.learnMoreLink,r=t.forceOpen,a=t.hasLeftSpacing,g=t.hasNoSpacing,f=t.onLearnMoreClick,m=void 0===f?function(){}:f,p=e.createElement(u.a,{className:o()("googlesitekit-new-badge",{"googlesitekit-new-badge--has-no-spacing":g}),label:Object(l.__)("New","google-site-kit"),hasLeftSpacing:a});return n?e.createElement(s.Tooltip,{tooltipClassName:"googlesitekit-new-badge__tooltip",title:e.createElement(c.Fragment,null,n,e.createElement("br",null),e.createElement(d.a,{href:i,onClick:m,external:!0,hideExternalIndicator:!0},Object(l.__)("Learn more","google-site-kit"))),placement:"top",enterTouchDelay:0,leaveTouchDelay:5e3,interactive:!0,open:r},p):p}NewBadge.propTypes={tooltipTitle:r.a.string,learnMoreLink:r.a.string,forceOpen:r.a.bool,onLearnMoreClick:r.a.func,hasLeftSpacing:r.a.bool,hasNoSpacing:r.a.bool},t.a=NewBadge}).call(this,n(4))},303:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileError}));var i=n(95),r=n(132);function MetricTileError(t){var n=t.children,a=t.headerText,o=t.infoTooltip,c=t.title;return e.createElement("div",{className:"googlesitekit-km-widget-tile--error"},e.createElement(i.a,{title:c,headerText:a,headerContent:o&&e.createElement(r.a,{title:o}),description:"",error:!0},n))}}).call(this,n(4))},306:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileMetric}));var i=n(1),r=n.n(i),a=n(9);function AudienceTileMetric(t){var n=t.TileIcon,i=t.title,r=t.metricValue,o=t.Badge,c=t.metricValueFormat;return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__icon"},e.createElement(n,null)),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__container"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__value"},Object(a.B)(r,c)),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__title"},i)),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__badge-container"},e.createElement(o,null)))}AudienceTileMetric.propTypes={TileIcon:r.a.elementType.isRequired,title:r.a.string.isRequired,metricValue:r.a.number.isRequired,Badge:r.a.elementType.isRequired,metricValueFormat:r.a.object}}).call(this,n(4))},310:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.149 7.96l-5.166 5.166a.344.344 0 00-.094.176l-.35 1.755a.344.344 0 00.404.404l1.755-.35a.344.344 0 00.175-.095l5.166-5.165-1.89-1.89zm2.301-1.814a1.031 1.031 0 00-1.458 0L6.497 12.64a1.031 1.031 0 00-.282.527l-.35 1.755a1.031 1.031 0 001.213 1.213l1.754-.35c.2-.04.383-.139.527-.283l6.495-6.494a1.031 1.031 0 000-1.459L14.45 6.146z"}),o=i.createElement("path",{d:"M12.149 7.96l.117-.116a.165.165 0 00-.234 0l.117.117zm-5.166 5.166l-.116-.116.116.116zm-.094.176l.162.033-.162-.033zm-.35 1.755l.161.032-.162-.032zm.404.404l.032.162-.032-.162zm1.755-.35l.032.161-.032-.162zm.175-.095l.117.117-.117-.117zm5.166-5.165l.116.116a.165.165 0 000-.233l-.116.117zm-1.047-3.705l.116.116-.116-.116zm1.458 0l-.116.116.116-.116zM6.497 12.64l.117.117-.117-.117zm-.282.527l-.162-.032.162.032zm-.35 1.755l.161.032-.162-.032zm1.213 1.213l-.033-.162.033.162zm1.754-.35l.033.161-.033-.162zm.527-.283l.117.117-.117-.117zm6.495-6.494l-.117-.117.117.117zm0-1.459l.117-.116-.117.116zm-3.822.295L6.867 13.01l.233.233 5.166-5.165-.234-.234zM6.867 13.01a.509.509 0 00-.14.26l.324.065a.18.18 0 01.05-.092l-.234-.233zm-.14.26l-.35 1.754.323.065.351-1.755-.323-.064zm-.35 1.754a.509.509 0 00.598.599l-.064-.324a.179.179 0 01-.21-.21l-.324-.065zm.598.599l1.755-.35-.065-.325-1.754.351.064.324zm1.755-.35a.508.508 0 00.26-.14l-.233-.233a.18.18 0 01-.092.048l.065.324zm.26-.14l5.165-5.166-.233-.233L8.757 14.9l.233.233zm3.042-7.055l1.89 1.89.233-.234-1.89-1.89-.233.234zm1.076-1.816a.866.866 0 011.226 0l.233-.233a1.196 1.196 0 00-1.692 0l.233.233zm-6.494 6.495l6.494-6.495-.233-.233-6.494 6.495.233.233zm-.237.443a.866.866 0 01.237-.443l-.233-.233c-.167.167-.281.38-.328.61l.324.066zm-.35 1.754l.35-1.754-.324-.065-.35 1.755.323.064zm1.018 1.02a.866.866 0 01-1.019-1.02l-.323-.065a1.196 1.196 0 001.407 1.408l-.065-.324zm1.755-.351l-1.755.35.065.324 1.755-.35-.065-.324zm.443-.237a.866.866 0 01-.443.237l.065.323c.231-.046.444-.16.611-.327l-.233-.233zm6.494-6.495l-6.494 6.495.233.233 6.495-6.494-.234-.234zm0-1.225a.866.866 0 010 1.225l.234.234a1.196 1.196 0 000-1.692l-.234.233zm-1.403-1.404l1.403 1.404.234-.233-1.404-1.404-.233.233z"});t.a=function SvgPencilAlt(e){return i.createElement("svg",r({viewBox:"0 0 22 22",fill:"currentColor"},e),a,o)}},314:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelHeader}));var i=n(1),r=n.n(i),a=n(20),o=n(104);function SelectionPanelHeader(t){var n=t.children,i=t.title,r=t.onCloseClick;return e.createElement("header",{className:"googlesitekit-selection-panel-header"},e.createElement("div",{className:"googlesitekit-selection-panel-header__row"},e.createElement("h3",null,i),e.createElement(a.a,{className:"googlesitekit-selection-panel-header__close",onClick:r,linkButton:!0},e.createElement(o.a,{width:"15",height:"15"}))),n)}SelectionPanelHeader.propTypes={children:r.a.node,title:r.a.string,onCloseClick:r.a.func}}).call(this,n(4))},315:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelItem}));var i=n(1),r=n.n(i),a=n(2),o=n(328),c=n(77);function SelectionPanelItem(t){var n=t.children,i=t.id,r=t.slug,l=t.title,s=t.description,u=t.isItemSelected,d=t.isItemDisabled,g=t.onCheckboxChange,f=t.subtitle,m=t.suffix,p=t.badge,h=t.isNewlyDetected;return e.createElement("div",{className:"googlesitekit-selection-panel-item"},e.createElement(o.a,{badge:p,checked:u,disabled:d,id:i,onChange:g,title:l,value:r},f&&e.createElement("span",{className:"googlesitekit-selection-panel-item__subtitle"},f),s,n),h&&e.createElement(c.a,{label:Object(a.__)("New","google-site-kit")}),m&&e.createElement("span",{className:"googlesitekit-selection-panel-item__suffix"},m))}SelectionPanelItem.propTypes={children:r.a.node,id:r.a.string,slug:r.a.string,title:r.a.string,description:r.a.string,isItemSelected:r.a.bool,isItemDisabled:r.a.bool,onCheckboxChange:r.a.func,subtitle:r.a.string,suffix:r.a.node,badge:r.a.node,isNewlyDetected:r.a.bool}}).call(this,n(4))},316:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelItems}));var i=n(21),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=n(2);function SelectionPanelItems(t){var n=t.currentSelectionTitle,i=void 0===n?Object(l.__)("Current selection","google-site-kit"):n,a=t.availableItemsTitle,o=void 0===a?Object(l.__)("Additional items","google-site-kit"):a,s=t.savedItemSlugs,u=void 0===s?[]:s,d=t.availableSavedItems,g=void 0===d?{}:d,f=t.availableUnsavedItems,m=void 0===f?{}:f,p=t.ItemComponent,h=t.notice,v=function(t){return Object.keys(t).map((function(n){return e.createElement(p,r()({key:n,slug:n,savedItemSlugs:u},t[n]))}))},b=Object.keys(m).length;return e.createElement("div",{className:"googlesitekit-selection-panel-items"},0!==u.length&&e.createElement(c.Fragment,null,e.createElement("p",{className:"googlesitekit-selection-panel-items__subheading"},i),e.createElement("div",{className:"googlesitekit-selection-panel-items__subsection"},v(g)),b>0&&e.createElement("p",{className:"googlesitekit-selection-panel-items__subheading"},o)),b>0&&e.createElement("div",{className:"googlesitekit-selection-panel-items__subsection"},v(m)),h)}SelectionPanelItems.propTypes={currentSelectionTitle:o.a.string,availableItemsTitle:o.a.string,savedItemSlugs:o.a.array,availableSavedItems:o.a.object,availableUnsavedItems:o.a.object,ItemComponent:o.a.elementType,notice:o.a.node}}).call(this,n(4))},317:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanelFooter}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(14),u=n(1),d=n.n(u),g=n(0),f=n(38),m=n(2),p=n(3),h=n(10),v=n(120),b=n(9),E=n(8),_=n(44),O=n(54);function SelectionPanelFooter(t){var n=t.savedItemSlugs,i=void 0===n?[]:n,a=t.selectedItemSlugs,c=void 0===a?[]:a,u=t.saveSettings,d=void 0===u?function(){}:u,y=t.saveError,k=t.itemLimitError,j=t.minSelectedItemCount,S=void 0===j?0:j,A=t.maxSelectedItemCount,w=void 0===A?0:A,T=t.isBusy,C=t.onSaveSuccess,N=void 0===C?function(){}:C,D=t.onCancel,R=void 0===D?function(){}:D,x=t.isOpen,M=t.closePanel,I=void 0===M?function(){}:M,B=Object(g.useState)(null),P=l()(B,2),L=P[0],F=P[1],z=Object(g.useState)(!1),W=l()(z,2),V=W[0],U=W[1],G=Object(p.useSelect)((function(e){return e(E.r).isFetchingSyncAvailableAudiences()})),H=Object(g.useMemo)((function(){return!Object(s.isEqual)(Object(b.E)(c),Object(b.E)(i))}),[i,c]),q=(null==i?void 0:i.length)>0&&H?Object(m.__)("Apply changes","google-site-kit"):Object(m.__)("Save selection","google-site-kit"),K=Object(g.useCallback)(o()(r.a.mark((function e(){var t;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,d(c);case 2:t=e.sent,t.error||(N(),I(),F(q),U(!0));case 5:case"end":return e.stop()}}),e)}))),[d,c,N,I,q]),Y=Object(g.useCallback)((function(){I(),R()}),[I,R]),X=Object(g.useState)(null),Z=l()(X,2),$=Z[0],J=Z[1];Object(g.useEffect)((function(){null!==$&&$!==x&&x&&(F(null),U(!1)),J(x)}),[x,$]);var Q=(null==c?void 0:c.length)||0,ee=G?e.createElement(_.a,{width:"89px",height:"20px"}):e.createElement("p",{className:"googlesitekit-selection-panel-footer__item-count"},Object(f.a)(Object(m.sprintf)(
/* translators: 1: Number of selected items. 2: Maximum number of items that can be selected. */
Object(m.__)("%1$d selected <MaxCount>(up to %2$d)</MaxCount>","google-site-kit"),Q,w),{MaxCount:e.createElement("span",{className:"googlesitekit-selection-panel-footer__item-count--max-count"})}));return e.createElement("footer",{className:"googlesitekit-selection-panel-footer"},y&&e.createElement(v.a,{error:y}),e.createElement("div",{className:"googlesitekit-selection-panel-footer__content"},H&&k?e.createElement(O.a,{noPrefix:!0,message:k}):ee,e.createElement("div",{className:"googlesitekit-selection-panel-footer__actions"},e.createElement(h.Button,{tertiary:!0,onClick:Y,disabled:T},Object(m.__)("Cancel","google-site-kit")),e.createElement(h.SpinnerButton,{onClick:K,isSaving:T,disabled:Q<S||Q>w||T||!x&&V},L||q))))}SelectionPanelFooter.propTypes={savedItemSlugs:d.a.array,selectedItemSlugs:d.a.array,saveSettings:d.a.func,saveError:d.a.object,itemLimitError:d.a.string,minSelectedItemCount:d.a.number,maxSelectedItemCount:d.a.number,isBusy:d.a.bool,onSaveSuccess:d.a.func,onCancel:d.a.func,isOpen:d.a.bool,closePanel:d.a.func}}).call(this,n(4))},32:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i="core/location"},322:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportZero}));var i=n(6),r=n.n(i),a=n(21),o=n.n(a),c=n(25),l=n.n(c),s=n(1),u=n.n(s),d=n(0),g=n(143),f=n(174);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function WidgetReportZero(t){var n=t.widgetSlug,i=t.moduleSlug,r=l()(t,["widgetSlug","moduleSlug"]),a=Object(d.useMemo)((function(){return{moduleSlug:i}}),[i]);return Object(g.a)(n,f.a,a),e.createElement(f.a,o()({moduleSlug:i},r))}WidgetReportZero.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:u.a.string.isRequired},f.a.propTypes)}).call(this,n(4))},323:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WidgetReportError}));var i=n(6),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(170);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function WidgetReportError(t){t.widgetSlug;var n=o()(t,["widgetSlug"]);return e.createElement(s.a,n)}WidgetReportError.propTypes=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({widgetSlug:l.a.string.isRequired},s.a.propTypes)}).call(this,n(4))},324:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return WPDashboardReportError}));var i=n(1),r=n.n(i),a=n(550),o=n(208),c=n(3),l=n(23),s=n(170);function WPDashboardReportError(t){var n=t.moduleSlug,i=t.error,r=Object(o.a)(WPDashboardReportError,"WPDashboardReportError"),u=Object(c.useDispatch)(l.b).setValue,d=i.message,g=Object(c.useSelect)((function(e){return e(l.b).getValue("WPDashboardReportError-".concat(n,"-").concat(d))}));return Object(a.a)((function(){u("WPDashboardReportError-".concat(n,"-").concat(d),r)}),(function(){u("WPDashboardReportError-".concat(n,"-").concat(d),void 0)})),g!==r?null:e.createElement(s.a,{moduleSlug:n,error:i})}WPDashboardReportError.propTypes={moduleSlug:r.a.string.isRequired,error:r.a.object.isRequired}}).call(this,n(4))},325:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M7.334 11.333h1.333v-4H7.334v4zM8.001 6a.658.658 0 00.667-.667.605.605 0 00-.2-.467.605.605 0 00-.467-.2.658.658 0 00-.667.667c0 .189.061.35.183.483A.69.69 0 008.001 6zm0 8.666a6.583 6.583 0 01-2.6-.516 6.85 6.85 0 01-2.117-1.434A6.85 6.85 0 011.851 10.6 6.582 6.582 0 011.334 8c0-.923.172-1.79.517-2.6a6.85 6.85 0 011.433-2.117c.6-.6 1.306-1.072 2.117-1.417A6.404 6.404 0 018 1.333c.922 0 1.789.178 2.6.533a6.618 6.618 0 012.116 1.417c.6.6 1.072 1.306 1.417 2.117.355.81.533 1.677.533 2.6 0 .922-.178 1.789-.533 2.6a6.619 6.619 0 01-1.417 2.116 6.85 6.85 0 01-2.116 1.434 6.583 6.583 0 01-2.6.516zm0-1.333c1.489 0 2.75-.517 3.783-1.55s1.55-2.294 1.55-3.783c0-1.49-.517-2.75-1.55-3.784-1.033-1.033-2.294-1.55-3.783-1.55-1.49 0-2.75.517-3.784 1.55C3.184 5.25 2.667 6.511 2.667 8c0 1.489.517 2.75 1.55 3.783 1.034 1.033 2.295 1.55 3.784 1.55z",fill:"currentColor"});t.a=function SvgInfoGreen(e){return i.createElement("svg",r({viewBox:"0 0 16 16",fill:"none"},e),a)}},326:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionPanel}));var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(327);function SelectionPanel(t){var n=t.children,i=t.isOpen,a=t.isLoading,o=t.onOpen,l=t.closePanel,s=t.className,u=null==s?void 0:s.split(/\s+/).map((function(e){return".".concat(e)})).join(""),d=u?"".concat(u," .googlesitekit-selection-panel-item .googlesitekit-selection-box input"):".googlesitekit-selection-panel-item .googlesitekit-selection-box input";return e.createElement(c.a,{className:r()("googlesitekit-selection-panel",s),isOpen:i,isLoading:a,onOpen:o,closeSheet:l,focusTrapOptions:{initialFocus:d}},n)}SelectionPanel.propTypes={children:o.a.node,isOpen:o.a.bool,isLoading:o.a.bool,onOpen:o.a.func,closePanel:o.a.func,className:o.a.string}}).call(this,n(4))},327:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SideSheet}));var i=n(6),r=n.n(i),a=n(11),o=n.n(a),c=n(405),l=n.n(c),s=n(1),u=n.n(s),d=n(209),g=n(392),f=n(0),m=n(56),p=n(72);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function SideSheet(t){var n=t.className,i=t.children,r=t.isOpen,a=t.isLoading,c=t.onOpen,s=void 0===c?function(){}:c,u=t.closeSheet,h=void 0===u?function(){}:u,b=t.focusTrapOptions,E=void 0===b?{}:b,_=Object(f.useRef)();return Object(f.useEffect)((function(){r?(s(),document.body.classList.add("googlesitekit-side-sheet-scroll-lock")):document.body.classList.remove("googlesitekit-side-sheet-scroll-lock")}),[r,s]),Object(d.a)(_,h),Object(g.a)((function(e){return r&&m.c===e.keyCode}),h),e.createElement(p.a,null,e.createElement(l.a,{active:!!r&&!a,focusTrapOptions:v({fallbackFocus:"body"},E)},e.createElement("section",{ref:_,className:o()("googlesitekit-side-sheet",n,{"googlesitekit-side-sheet--open":r}),role:"dialog","aria-modal":"true","aria-hidden":!r,tabIndex:"0"},i)),r&&e.createElement("span",{className:"googlesitekit-side-sheet-overlay"}))}SideSheet.propTypes={className:u.a.string,children:u.a.node,isOpen:u.a.bool,isLoading:u.a.bool,onOpen:u.a.func,closeSheet:u.a.func,focusTrapOptions:u.a.object}}).call(this,n(4))},328:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SelectionBox}));var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(10);function SelectionBox(t){var n=t.badge,i=t.checked,r=t.children,a=t.disabled,l=t.id,s=t.onChange,u=t.title,d=t.value;return e.createElement("div",{className:o()("googlesitekit-selection-box",{"googlesitekit-selection-box--disabled":a})},e.createElement(c.Checkbox,{checked:i,description:r,disabled:a,id:l,name:l,onChange:s,value:d,badge:n},u))}SelectionBox.propTypes={badge:r.a.node,checked:r.a.bool,children:r.a.node,disabled:r.a.bool,id:r.a.string,onChange:r.a.func,title:r.a.string,value:r.a.string}}).call(this,n(4))},34:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n(22),r=n(18);function a(){var e=Object(r.a)();return i.g.includes(e)}},343:function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(3),o=n(50),c=n(8),l=n(7),s=n(486),u=n(113),d=n(9),g=n(18),f=Object(u.a)(s.a);function NoAudienceBannerWidget(t){var n=t.Widget,i=t.WidgetNull,r=Object(g.a)(),o=Object(a.useSelect)((function(e){var t=e(c.r).getAvailableAudiences();return null==t?void 0:t.map((function(e){return e.name}))})),s=Object(a.useSelect)((function(e){return e(l.a).getConfiguredAudiences()})),u=Object(a.useSelect)((function(e){return e(l.a).didSetAudiences()})),m=null==s?void 0:s.every((function(e){return Array.isArray(o)&&!o.includes(e)}));return s&&(0===(null==s?void 0:s.length)||m)?e.createElement(n,{noPadding:!0},e.createElement(f,{onInView:function(){Object(d.I)("".concat(r,"_audiences-no-audiences"),"view_banner",u?"no-longer-available":"none-selected")}})):e.createElement(i,null)}NoAudienceBannerWidget.propTypes={Widget:r.a.elementType.isRequired,WidgetNull:r.a.elementType.isRequired},t.a=Object(o.a)({moduleName:"analytics-4"})(NoAudienceBannerWidget)}).call(this,n(4))},344:function(e,t,n){"use strict";(function(e){var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(406),g=n(0),f=n(2),m=n(17),p=n(345),h=Object(g.forwardRef)((function(t,n){var i=t.children,a=t.href,c=t.text,s=t.className,u=t.danger,h=t.disabled,v=t.target,b=t.icon,E=t.trailingIcon,_=t["aria-label"],O=t.title,y=t.customizedTooltip,k=t.tooltip,j=t.inverse,S=t.hideTooltipTitle,A=void 0!==S&&S,w=t.tooltipEnterDelayInMS,T=void 0===w?100:w,C=t.tertiary,N=void 0!==C&&C,D=t.callout,R=t.calloutStyle,x=o()(t,["children","href","text","className","danger","disabled","target","icon","trailingIcon","aria-label","title","customizedTooltip","tooltip","inverse","hideTooltipTitle","tooltipEnterDelayInMS","tertiary","callout","calloutStyle"]),M=Object(g.useCallback)((function(e){null!==e&&m.i.attachTo(e)}),[]),I=Object(d.a)(n,M),B=a&&!h?"a":"button",P=e.createElement(B,r()({className:l()("mdc-button",s,{"mdc-button--raised":!c&&!N&&!D,"mdc-button--danger":u,"mdc-button--inverse":j,"mdc-button--tertiary":N,"mdc-button--callout":D,"mdc-button--callout-primary":D||"primary"===R,"mdc-button--callout-warning":"warning"===R,"mdc-button--callout-error":"error"===R}),href:h?void 0:a,ref:I,disabled:!!h,"aria-label":function(){var e=_;if("_blank"!==v)return e;var t=Object(f._x)("(opens in a new tab)","screen reader text","google-site-kit");return"string"==typeof i&&(e=e||i),e?"".concat(e," ").concat(t):t}(),target:v||"_self",role:"a"===B?"button":void 0},x),b,i&&e.createElement("span",{className:"mdc-button__label"},i),E),L=A?null:O||y||_;return!h&&(k&&L||b&&L&&void 0===i)?e.createElement(p.a,{title:L,enterDelay:T},P):P}));h.displayName="Button",h.propTypes={onClick:u.a.func,children:u.a.node,href:u.a.string,text:u.a.bool,className:u.a.string,danger:u.a.bool,disabled:u.a.bool,icon:u.a.element,trailingIcon:u.a.element,title:u.a.string,customizedTooltip:u.a.element,tooltip:u.a.bool,inverse:u.a.bool,hideTooltipTitle:u.a.bool,callout:u.a.bool,calloutStyle:u.a.oneOf(["primary","warning","error"])},h.defaultProps={onClick:null,href:null,text:!1,className:"",danger:!1,disabled:!1,icon:null,trailingIcon:null,title:null,customizedTooltip:null,tooltip:!1,inverse:!1,calloutStyle:null,callout:null},t.a=h}).call(this,n(4))},345:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Tooltip}));var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(11),u=n.n(s),d=n(641),g=n(0);function Tooltip(t){var n=t.children,i=t.popperClassName,a=t.tooltipClassName,c=t.onOpen,l=t.onClose,s=o()(t,["children","popperClassName","tooltipClassName","onOpen","onClose"]),f=Object(g.useRef)(!1),m=c?function(){f.current||(f.current=!0,null==c||c())}:void 0,p=c?function(){f.current=!1,null==l||l()}:l;return e.createElement(d.a,r()({classes:{popper:u()("googlesitekit-tooltip-popper",i),tooltip:u()("googlesitekit-tooltip",a)},arrow:!0,onOpen:m,onClose:p},s),n)}Tooltip.propTypes={children:l.a.node,popperClassName:l.a.string,tooltipClassName:l.a.string,onOpen:l.a.func,onClose:l.a.func}}).call(this,n(4))},346:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GetHelpLink}));var i=n(1),r=n.n(i),a=n(38),o=n(2),c=n(20);function GetHelpLink(t){var n=t.linkURL;return Object(a.a)(Object(o.__)("Contact your administrator. Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(c.a,{href:n,external:!0,hideExternalIndicator:!0},Object(o.__)("Get help","google-site-kit"))})}GetHelpLink.propTypes={linkURL:r.a.string.isRequired}}).call(this,n(4))},347:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileNoData}));var i=n(2);function AudienceTileNoData(){return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__no-data"},Object(i.__)("No data to show yet","google-site-kit"))}}).call(this,n(4))},348:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PartialDataNotice}));var i=n(1),r=n.n(i);function PartialDataNotice(t){var n=t.content;return e.createElement("span",{className:"googlesitekit-audience-segmentation-partial-data-notice"},n)}PartialDataNotice.propTypes={content:r.a.node}}).call(this,n(4))},349:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M2.675 72.31a29.248 29.248 0 005.678 8.74c9.451 9.955 23.416 10.799 36.223 8.308a88.838 88.838 0 0035.776-15.752c6.09-4.513 12.104-10.113 20.167-10.363 3.027-.093 6.158.741 8.445 2.71 4.753 4.063 4.668 11.012 8.377 15.829 4.932 6.405 12.026 8.389 19.764 9.128 21.862 2.086 47.902-4.758 62.939-21.412 13.426-14.868 15.038-38.526-1.214-52.08-7.425-6.192-17.606-9.03-27.216-7.584-7.345 1.105-14.801 4.467-22.404 5.401-8.954 1.103-14.49-2.659-21.734-7.04C114.852.58 98.164-2.345 83.874 2.072 70.195 6.301 60.35 17.846 47.04 22.918c-11.502 4.385-25.089 3.717-35.082 10.86C.133 42.228-2.84 59.286 2.675 72.31z",fill:"#B8E6CA"}),o=i.createElement("path",{d:"M108.273 109c54.612 0 98.883-1.735 98.883-3.874 0-2.14-44.271-3.875-98.883-3.875-54.611 0-98.882 1.735-98.882 3.875 0 2.139 44.27 3.874 98.882 3.874z",fill:"#161B18",opacity:.1}),c=i.createElement("path",{d:"M108.273 109c54.612 0 98.883-1.735 98.883-3.874 0-2.14-44.271-3.875-98.883-3.875-54.611 0-98.882 1.735-98.882 3.875 0 2.139 44.27 3.874 98.882 3.874z",fill:"#CBD0D3"}),l=i.createElement("path",{d:"M134.765 53.225c-1.065 16.927-6.936 32.112-3.012 51.193h-4.468M139.814 104.418h-4.47l7.9-51.193",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),s=i.createElement("path",{d:"M120.504 36.651c-3.814 1.73-11.135 5.58-11.135 13.398M147.266 35.787c3.493 1.787 11.06 7.678 11.977 13.225",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),u=i.createElement("path",{d:"M151.555 75.952l-3.102.282-18.926 1.719-11.063 1.002-1.049-24.735-1.825-43.046 34.528-1.262.697 32.058.094 4.316.642 29.565.004.1z",fill:"#77AD8C"}),d=i.createElement("path",{d:"M148.453 76.234l1.78-.162 1.323-.12-1.439-66.042-1.983.091 1.281 63.2-24.976 2.127.862-19.15-7.844-1.074.86 20.438.107 2.465.041.946 11.063-1.002 18.927-1.719-.002.002z",fill:"#5C9271"}),g=i.createElement("path",{d:"M135.891 70.752c.032.916-.392-20.197-.629-27.044-6.628-3.008-13.797-3.559-20.67-1.228l.628 27.043c6.874-2.33 14.043-1.779 20.671 1.23z",fill:"#CBD0D3"}),f=i.createElement("path",{d:"M135.926 70.752c.01.916-.547-20.194-.629-27.044 6.481-3.306 13.617-4.182 20.592-2.166l.629 27.043c-6.975-2.015-14.111-1.139-20.592 2.167z",fill:"#EBEEF0"}),m=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M133.233 60.796c-.083-3.907-.202-9.298-.333-14.96l.196-.005a8403.731 8403.731 0 01.43 19.559l.023 1.18.004.262a.337.337 0 010 .046v.004l-.001.008a.179.179 0 01-.*************** 0 01-.1-.08l-.002-.007v-.004-.003l-.001-.02h.001v-.025l-.004-.26-.023-1.18-.097-4.598zm.126 6.063a.19.19 0 01.093-.08.19.19 0 01.097.073l-.19.007zm.194.01v-.001.001zM130.917 60.007c-.084-3.912-.203-9.29-.334-14.911l.197-.004a10934.46 10934.46 0 01.432 19.528l.024 1.198.004.271.001.05v.008l-.004.02c-.054.062-.17.035-.189-.002a.054.054 0 01-.003-.015l-.001-.005v-.018-.035l-.005-.27-.024-1.198c-.022-1.071-.055-2.66-.098-4.617zm.129 6.12l.193-.006-.001-.006c-.02-.037-.135-.063-.19-.002a.079.079 0 00-.002.014zM128.613 59.401c-.086-3.923-.206-9.285-.335-14.834l.197-.005a9889.942 9889.942 0 01.459 20.704l.005.284.001.055v.008l-.001.006a.179.179 0 01-.*************** 0 01-.1-.082l-.001-.007-.001-.017h.001l-.001-.047-.005-.283-.025-1.221-.1-4.648zm.131 6.199l.195-.007a.192.192 0 00-.1-.081.187.187 0 00-.095.086v.002zM126.303 58.977c-.087-3.938-.207-9.284-.334-14.744l.196-.005c.127 5.46.248 10.807.335 14.744l.102 4.688a412.921 412.921 0 01.032 1.545l.001.061v.012l-.001.009c-.005.02-.109.084-.191.017a.156.156 0 01-.004-.02v-.004-.002-.002l-.001-.007h.001v-.001l-.001-.06-.006-.296-.026-1.248-.103-4.688zm.136 6.292l.195-.007a.08.08 0 00-.004-.018c-.081-.066-.186-.003-.19.017l-.001.008zm.196-.002zM124.002 58.73c-.088-3.958-.209-9.292-.334-14.653l.196-.004a14128.617 14128.617 0 01.439 19.391l.028 1.28.006.312.002.068v.015l-.002.014c-.08.078-.19.014-.194-.004l-.001-.007v-.002-.007l.196-.006v-.005c-.005-.018-.114-.082-.194-.004a.157.157 0 00-.002.014v-.006l-.001-.066-.006-.312-.028-1.28-.105-4.738zM121.709 58.698c-.09-3.97-.211-9.281-.333-14.54l.196-.005a15730.945 15730.945 0 01.44 19.316l.029 1.306.006.327.002.074v.021a.191.191 0 01-.021.056.165.165 0 01-.175-.05v-.006l-.001-.004.197-.007-.001-.003a.163.163 0 00-.174-.05.193.193 0 00-.021.056v.003l-.001-.013-.001-.073-.007-.326-.028-1.307-.107-4.775zM119.414 58.855c-.091-3.986-.212-9.28-.332-14.44l.196-.005c.12 5.16.242 10.454.333 14.44l.109 4.818.03 1.336.**************.001.017v.006c0 .004-.098.096-.196.009l-.001-.007v-.002l.197-.006-.001-.006c-.098-.087-.196.005-.196.01v.001V65.43l-.002-.081-.007-.34-.03-1.336-.11-4.819zM116.797 44.873l.196-.004.489 21.033h-.01a271.267 271.267 0 01-.186.005l-.489-21.034z",fill:"#CBD0D3"}),p=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M133.317 67.22c-5.153-1.86-10.563-2.237-15.85-.939a.388.388 0 11-.188-.754c5.447-1.338 11.016-.948 16.306.963a.388.388 0 11-.268.73zM133.267 64.222c-5.159-1.867-10.574-2.245-15.866-.946a.388.388 0 11-.189-.754c5.454-1.34 11.027-.947 16.323.97a.387.387 0 01.234.498.392.392 0 01-.502.232zM133.204 61.217c-5.162-1.867-10.576-2.245-15.866-.946a.393.393 0 01-.475-.284.389.389 0 01.287-.47c5.45-1.339 11.023-.946 16.323.97a.388.388 0 01.234.498.393.393 0 01-.503.232zM133.134 58.212c-5.163-1.866-10.576-2.245-15.866-.946a.388.388 0 11-.189-.755c5.451-1.338 11.024-.945 16.323.97a.386.386 0 01.234.499.392.392 0 01-.502.232zM133.071 55.213c-5.163-1.872-10.584-2.252-15.881-.95a.388.388 0 11-.189-.754c5.459-1.342 11.038-.947 16.339.974a.388.388 0 01.234.498.393.393 0 01-.503.232zM133.001 52.208c-5.164-1.87-10.584-2.252-15.882-.95a.389.389 0 11-.188-.755c5.458-1.34 11.038-.945 16.339.975a.388.388 0 11-.269.73z",fill:"#B8BDB9"}),h=i.createElement("path",{d:"M116.886 44.87c5.378-1.32 10.878-.934 16.11.963l.054 2.999c-5.227-1.891-10.721-2.276-16.094-.957l-.07-3.004z",fill:"#B8BDB9"}),v=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M117.284 45.175l.051 2.211c5.118-1.161 10.331-.806 15.312.893l-.039-2.17c-4.989-1.759-10.21-2.127-15.324-.934zm-.494-.68c5.459-1.342 11.038-.948 16.339.974l.253.091.069 3.832-.536-.194c-5.159-1.867-10.574-2.245-15.866-.945l-.475.116-.089-3.8.305-.075z",fill:"#B8BDB9"}),b=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M137.87 60.692a7073.26 7073.26 0 01-.362-14.961l.196-.005a8741.186 8741.186 0 00.48 19.558 754.986 754.986 0 00.04 1.44v.025h.001l.001.02-.001.003v.012a.187.187 0 01-.*************** 0 01-.097-.08l-.002-.008v-.003l-.002-.046-.008-.262-.032-1.18c-.028-1.06-.068-2.644-.117-4.597zm.352 6.057a.189.189 0 00-.096-.076.184.184 0 00-.093.078l.189-.002zm-.193.018v0zM140.148 59.798c-.098-3.912-.228-9.29-.359-14.91l.196-.005a10306.992 10306.992 0 00.507 20.724l.008.27.001.036h.001v.023a.13.13 0 01-.002.015c-.018.038-.132.07-.189.01l-.005-.02v-.004-.004l-.002-.05a1047.874 1047.874 0 01-.04-1.47c-.027-1.07-.068-2.659-.116-4.615zm.352 6.115l-.003-.014c-.057-.06-.172-.027-.189.01l-.001.006.193-.002zM142.426 59.087c-.097-3.922-.227-9.284-.356-14.834l.197-.004c.129 5.55.258 10.911.355 14.834l.116 4.647.032 1.22.008.284.002.047v.024a.185.185 0 01-.*************** 0 01-.099-.082l-.001-.006v-.008l-.002-.055a22.176 22.176 0 01-.008-.284l-.032-1.22-.116-4.648zm.353 6.194v-.002a.187.187 0 00-.099-.082.18.18 0 00-.095.086l.194-.002zM144.711 58.559c-.096-3.937-.225-9.283-.352-14.744l.197-.004c.127 5.46.255 10.806.351 14.743l.115 4.688.032 1.247.008.297.002.059v.001h.001V64.857l-.001.004a.121.121 0 01-.003.02c-.078.07-.186.012-.191-.008l-.002-.01v-.004-.007l-.002-.06-.008-.298-.032-1.248-.115-4.687zm.353 6.287l-.001-.008c-.006-.02-.113-.078-.191-.008l-.004.018.196-.002zm-.196.007c0 .001 0 0 0 0z",fill:"#EBEEF0"}),E=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M137.979 66.391c5.195-2.149 10.74-2.792 16.244-1.703a.389.389 0 11-.153.762c-5.342-1.057-10.728-.435-15.789 1.659a.394.394 0 01-.513-.21.388.388 0 01.211-.508zM137.889 63.393c5.201-2.155 10.751-2.8 16.261-1.71a.39.39 0 01.308.457.392.392 0 01-.461.305c-5.348-1.058-10.739-.434-15.806 1.665a.392.392 0 01-.512-.209.387.387 0 01.21-.508zM137.823 60.389c5.2-2.155 10.749-2.8 16.256-1.71a.388.388 0 11-.153.762c-5.345-1.057-10.735-.434-15.801 1.665a.394.394 0 01-.513-.21.388.388 0 01.211-.508zM137.749 57.383c5.2-2.154 10.748-2.8 16.256-1.71a.388.388 0 11-.154.762c-5.344-1.057-10.735-.433-15.8 1.665a.393.393 0 01-.513-.209.388.388 0 01.211-.508zM137.663 54.385c5.206-2.16 10.761-2.807 16.276-1.716a.389.389 0 11-.153.763c-5.352-1.06-10.75-.434-15.821 1.67a.394.394 0 01-.513-.208.388.388 0 01.211-.509zM137.596 51.38c5.206-2.16 10.762-2.808 16.277-1.716a.39.39 0 01.308.457.393.393 0 01-.462.305c-5.352-1.06-10.749-.432-15.82 1.67a.393.393 0 01-.513-.208.387.387 0 01.21-.508zM137.542 48.368c5.201-2.154 10.751-2.8 16.26-1.71a.389.389 0 11-.153.763c-5.346-1.058-10.739-.434-15.805 1.665a.393.393 0 01-.513-.21.388.388 0 01.211-.508zM137.456 45.37c5.206-2.16 10.761-2.808 16.276-1.716a.39.39 0 01.308.457.392.392 0 01-.461.306c-5.353-1.06-10.75-.434-15.821 1.67a.394.394 0 01-.513-.209.388.388 0 01.211-.508z",fill:"#CBD0D3"}),_=i.createElement("path",{d:"M137.608 45.729c5.139-2.133 10.618-2.768 16.049-1.693l.21 9.014c-5.434-1.075-10.91-.439-16.049 1.693l-.21-9.014z",fill:"#CBD0D3"}),O=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M138.005 45.986l.19 8.184c4.901-1.928 10.097-2.52 15.267-1.592l-.191-8.22c-5.162-.96-10.364-.356-15.266 1.628zm-.55-.616c5.206-2.16 10.765-2.807 16.277-1.716a.39.39 0 01.315.372l.21 9.015a.387.387 0 01-.14.307.394.394 0 01-.329.083c-5.352-1.06-10.749-.434-15.82 1.67a.395.395 0 01-.365-.032.387.387 0 01-.178-.317l-.21-9.015a.389.389 0 01.24-.367z",fill:"#CBD0D3"}),y=i.createElement("path",{d:"M159.24 49.011c.761 4.603-4.117 7.506-7.486 6.434M109.37 50.05c.001 4.55 5.159 7.83 8.838 6.226",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),k=i.createElement("path",{d:"M31.757 63.326l-5.175 13.74a22.86 22.86 0 00-.534 1.593c-1.686 5.718-.919 11.872 1.862 17.155l4.678 8.598h-4.473",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),j=i.createElement("path",{d:"M39.988 67.196l.768 37.221",stroke:"#161B18",strokeWidth:1.472,strokeMiterlimit:10,strokeLinecap:"round"}),S=i.createElement("path",{d:"M31.757 63.326a27.536 27.536 0 00-2.058 5.225",stroke:"#1967D2",strokeWidth:2.748,strokeLinejoin:"round"}),A=i.createElement("path",{d:"M47.8 52.642c4.738 2.161 9.71 4.53 10.766 11.237M13.781 61.718c-2.557 3.62-6.986 9.225-5.039 14.72",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),w=i.createElement("path",{d:"M1.535 51.315L54.34 35.316s5.107 29.415-22.04 34.95c-.009 0-18.561 4.185-30.764-18.952z",fill:"#70B2F5"}),T=i.createElement("path",{d:"M1.535 51.315L54.34 35.316s5.107 29.415-22.04 34.95c-.009 0-18.561 4.185-30.764-18.952z",fill:"#77AD8C"}),C=i.createElement("path",{d:"M33.856 67.557S16.353 71.503 4.163 50.519l-2.628.796C13.738 74.452 32.29 70.266 32.29 70.266c10.329-2.105 15.985-7.67 19.032-13.753-3.297 4.975-8.696 9.256-17.466 11.044z",fill:"#5C9271"}),N=i.createElement("path",{d:"M45.22 104.418h-4.47",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),D=i.createElement("path",{d:"M38.095 85.802c.172.861-3.494-19.049-4.776-25.49-6.736-1.845-13.604-1.284-19.746 1.959l4.776 25.49c6.143-3.243 13.01-3.804 19.746-1.96z",fill:"#CBD0D3"}),R=i.createElement("path",{d:"M38.129 85.796c.152.865-3.64-19.022-4.776-25.491 5.62-4.106 12.236-6.013 19.146-5.159l4.776 25.49c-6.91-.853-13.525 1.053-19.146 5.16z",fill:"#EBEEF0"}),x=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M34.042 76.784c-.683-3.684-1.629-8.766-2.629-14.104l.185-.033A7851.454 7851.454 0 0135.233 82.2a55.731 55.731 0 01.052.29v.011a.174.174 0 01-.*************** 0 01-.106-.06l-.003-.007v-.003l-.001-.003v-.001a3796.705 3796.705 0 00-1.057-5.736zm1.057 5.717a.18.18 0 01.075-.09c.*************.103.055l-.178.035zm.184-.02zM31.73 76.386A9015.12 9015.12 0 0029.11 62.33l.184-.034a9007.929 9007.929 0 013.637 19.54 80.445 80.445 0 01.055.307v.024c-.042.066-.155.058-.18.026a.162.162 0 01-.004-.013l-.001-.006a.528.528 0 01-.004-.016h.001l-.006-.034a4684.226 4684.226 0 00-1.06-5.738zm1.068 5.771l.181-.035a.058.058 0 00-.002-.005c-.024-.032-.137-.04-.178.026l-.001.014zM29.453 76.162c-.687-3.698-1.63-8.753-2.61-13.984l.185-.033a10403.315 10403.315 0 013.636 19.517 66.685 66.685 0 01.058.32v.008l.001.006a.176.176 0 01-.*************** 0 01-.107-.063l-.002-.006-.003-.016a4941.913 4941.913 0 00-1.082-5.845zm1.083 5.845l.183-.035a.181.181 0 00-.107-.063.176.176 0 00-.076.096v.002zM27.204 76.11c-.69-3.712-1.631-8.752-2.596-13.898l.185-.034a13167.584 13167.584 0 013.687 19.775l.01.057.002.007v.013c0 .02-.09.095-.177.045a.185.185 0 01-.008-.023v-.001-.002l-.002-.007a6801.028 6801.028 0 00-1.1-5.932zm1.101 5.932l.184-.035a.257.257 0 00-.006-.017c-.088-.05-.177.025-.178.045v.007zm.185-.03v-.001zM24.99 76.224c-.695-3.732-1.634-8.76-2.58-13.813l.184-.033a16040.945 16040.945 0 013.637 19.486l.055.294c.005.03.01.052.011.064a.39.39 0 01.003.028c-.063.086-.176.042-.183.025a.13.13 0 01-.002-.006v-.003l-.002-.005.184-.036v-.005c-.008-.016-.12-.06-.184.026V82.254a16.67 16.67 0 00-.066-.357l-.224-1.206-.832-4.467zM22.813 76.54c-.698-3.743-1.634-8.749-2.563-13.706l.185-.034a20172.54 20172.54 0 013.701 19.818l.003.014v.005a.19.19 0 01-.01.057c-.09.047-.172-.018-.174-.022v-.006l-.002-.002.185-.037-.001-.003c-.001-.004-.083-.069-.173-.021a.191.191 0 00-.01.059l-.003-.012-.012-.07a302.24 302.24 0 00-.057-.307l-.23-1.232-.839-4.501zM20.668 77.034l-2.547-13.61.184-.034a29372.29 29372.29 0 013.69 19.733l.015.076.003.017v.005c.001.004-.077.105-.183.038a.117.117 0 01-.001-.006v-.002l.184-.035-.002-.006c-.106-.067-.184.035-.183.038v.002-.002l-.003-.016-.014-.076-.06-.32-.235-1.26-.848-4.542zM16.023 64.202l.185-.034 3.715 19.826-.01.001-.165.03-.01.002-3.715-19.825z",fill:"#CBD0D3"}),M=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M35.112 82.85c-5.163-.983-10.34-.522-15.14 1.505a.377.377 0 01-.493-.197.37.37 0 01.198-.488c4.947-2.089 10.276-2.56 15.576-1.551a.373.373 0 01.298.436.375.375 0 01-.44.295zM34.601 80.02c-5.17-.987-10.35-.527-15.157 1.502a.377.377 0 01-.493-.197.37.37 0 01.198-.488c4.953-2.09 10.287-2.561 15.593-1.548a.373.373 0 01.299.436.375.375 0 01-.44.296zM34.078 77.187c-5.173-.986-10.353-.526-15.157 1.501a.377.377 0 01-.493-.197.37.37 0 01.198-.488c4.95-2.09 10.283-2.56 15.593-1.547a.373.373 0 11-.141.731zM33.547 74.355c-5.173-.986-10.354-.527-15.157 1.5a.377.377 0 01-.493-.196.371.371 0 01.198-.489c4.95-2.089 10.282-2.559 15.593-1.547a.373.373 0 01.298.436.375.375 0 01-.44.296zM33.023 71.527c-5.175-.991-10.362-.532-15.172 1.5a.377.377 0 01-.493-.197.37.37 0 01.198-.489c4.957-2.093 10.296-2.563 15.609-1.545a.373.373 0 11-.142.73zM32.492 68.695c-5.175-.99-10.362-.532-15.172 1.5a.377.377 0 01-.494-.198.37.37 0 01.198-.488c4.957-2.093 10.297-2.562 15.61-1.546a.373.373 0 11-.142.732z",fill:"#B8BDB9"}),I=i.createElement("path",{d:"M16.109 64.187c4.884-2.062 10.147-2.527 15.39-1.523l.515 2.83c-5.238-1-10.495-.535-15.374 1.525l-.531-2.832z",fill:"#B8BDB9"}),B=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.538 64.414l.39 2.084c4.663-1.872 9.65-2.323 14.625-1.468l-.373-2.047c-4.991-.91-9.988-.47-14.642 1.431zm-.572-.57c4.957-2.093 10.296-2.563 15.609-1.545l.253.048.658 3.614-.538-.102c-5.169-.987-10.35-.527-15.157 1.502l-.43.182-.672-3.582.277-.117z",fill:"#B8BDB9"}),P=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M38.41 75.985c-.698-3.682-1.656-8.762-2.656-14.1l.185-.033a7931.958 7931.958 0 003.477 18.43 592.343 592.343 0 00.26 1.357l.005.023h.001a.711.711 0 01.004.022v.011a.173.173 0 01-.*************** 0 01-.105-.06.16.16 0 01-.003-.01 3677.586 3677.586 0 01-1.092-5.733zm1.269 5.677a.182.182 0 00-.102-.057.181.181 0 00-.077.088l.179-.03zm-.18.046v0zM40.426 74.796c-.697-3.687-1.652-8.755-2.645-14.053l.185-.034a9325.56 9325.56 0 003.684 19.53l.049.255.006.034h.001a.53.53 0 01.003.016v.019c-.01.04-.113.087-.176.04a.164.164 0 01-.008-.023l-.001-.004-.01-.046-.049-.256c-.046-.242-.12-.624-.215-1.128a5105.31 5105.31 0 01-.824-4.35zm1.278 5.732a.194.194 0 00-.005-.013c-.063-.047-.166 0-.177.039v.005l.182-.031zM42.474 73.78c-.698-3.697-1.65-8.75-2.63-13.98l.184-.034a10585.806 10585.806 0 003.678 19.51l.05.266.01.044.003.016a.07.07 0 010 .006.176.176 0 01-.*************** 0 01-.106-.063l-.002-.006a2.407 2.407 0 01-.012-.06l-.051-.266-.219-1.15c-.194-1.023-.48-2.532-.828-4.38zm1.291 5.806v-.001a.178.178 0 00-.106-.063.178.178 0 00-.077.096l.183-.032zM44.553 72.934c-.7-3.71-1.647-8.748-2.612-13.895l.185-.034a13096.468 13096.468 0 003.722 19.768l.011.055v.001l.002.007v.027c-.062.078-.172.039-.181.021a.116.116 0 01-.005-.02l-.011-.056a264.523 264.523 0 01-.277-1.456l-.834-4.418zm1.306 5.895l-.002-.007c-.008-.017-.119-.057-.182.021v.018l.184-.032zm-.184.037z",fill:"#EBEEF0"}),L=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M39.398 81.36c4.582-2.818 9.729-4.263 15.104-*************.37.18.362.386a.374.374 0 01-.389.358c-5.217-.193-10.217 1.208-14.681 3.953a.378.378 0 01-.517-.12.37.37 0 01.12-.513zM38.848 78.537c4.587-2.824 9.738-4.273 15.12-*************.369.18.361.386a.374.374 0 01-.389.358c-5.222-.193-10.227 1.211-14.695 3.962a.378.378 0 01-.518-.12.37.37 0 01.12-.513zM38.316 75.705c4.587-2.824 9.737-4.272 15.115-*************.37.18.362.386a.374.374 0 01-.389.358c-5.22-.193-10.223 1.211-14.69 3.962a.378.378 0 01-.518-.12.37.37 0 01.12-.513zM37.789 72.873c4.587-2.824 9.736-4.272 15.115-*************.37.18.362.386a.374.374 0 01-.389.358c-5.22-.193-10.223 1.211-14.691 3.962a.378.378 0 01-.517-.12.37.37 0 01.12-.513zM37.243 70.05c4.591-2.83 9.747-4.282 15.134-*************.37.18.362.386a.374.374 0 01-.39.358c-5.227-.194-10.236 1.213-14.709 3.97a.378.378 0 01-.517-.12.37.37 0 01.12-.513zM36.712 67.216c4.591-2.829 9.747-4.281 15.134-************.369.18.361.385a.374.374 0 01-.389.358c-5.227-.194-10.236 1.214-14.71 3.97a.378.378 0 01-.516-.12.37.37 0 01.12-.513zM36.195 64.376c4.588-2.824 9.74-4.273 15.12-4.074.207.008.37.181.362.386a.374.374 0 01-.39.359c-5.22-.194-10.226 1.21-14.695 3.961a.378.378 0 01-.517-.12.37.37 0 01.12-.513zM35.65 61.552c4.59-2.83 9.747-4.281 15.133-*************.37.18.362.386a.374.374 0 01-.39.358c-5.227-.194-10.236 1.213-14.709 3.97a.378.378 0 01-.517-.12.37.37 0 01.12-.513z",fill:"#CBD0D3"}),F=i.createElement("path",{d:"M35.851 61.868c4.532-2.793 9.618-4.222 14.922-4.025l1.592 8.497c-5.307-.198-10.39 1.232-14.922 4.025l-1.592-8.497z",fill:"#CBD0D3"}),z=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M36.268 62.051l1.446 7.715c4.338-2.565 9.162-3.91 14.197-3.812l-1.452-7.749c-5.032-.127-9.86 1.23-14.191 3.846zm-.615-.5c4.591-2.83 9.75-4.28 15.134-4.08a.375.375 0 01.355.304l1.593 8.497a.37.37 0 01-.085.31.376.376 0 01-.298.13c-5.228-.195-10.237 1.212-14.71 3.97a.378.378 0 01-.568-.25l-1.592-8.496a.37.37 0 01.171-.384z",fill:"#CBD0D3"}),W=i.createElement("path",{d:"M58.565 63.879c.876 5.566-4.736 9.076-8.612 7.78M8.738 76.438c1.616 4.56 7.623 6.458 13.652 0",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),V=i.createElement("path",{d:"M160.68 68.356c.934 6.676 1.531 14.409 0 20.996M171.536 72.568l-3.781 31.849h-4.47",stroke:"#161B18",strokeWidth:1.472,strokeMiterlimit:10,strokeLinecap:"round"}),U=i.createElement("path",{d:"M187.455 104.418h-4.471c.637-10.18 1.817-24.67 1.817-24.67",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),G=i.createElement("path",{d:"M199.766 66.904c2.35 3.645 6.395 13.017 4.381 17.69M161.004 59.99c-3.656 2.734-9.85 8.336-9.904 15.127",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),H=i.createElement("path",{d:"M161.004 59.99c-3.656 2.734-9.85 8.336-9.904 15.127-.045 5.634 4.35 10.804 12.101 6.915",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),q=i.createElement("path",{d:"M188.416 36.69c5.324 1.935 9.926 5.533 12.45 10.684 5.586 11.402.195 27.178-11.38 32.714-10.499 5.032-24.499 1.152-30.83-8.532-6.33-9.683-4.157-23.882 4.792-31.286 6.7-5.538 16.706-6.574 24.968-3.58z",fill:"#77AD8C"}),K=i.createElement("path",{d:"M200.868 47.374a19.785 19.785 0 00-4.03-5.505c.638.885 1.2 1.822 1.68 2.8 5.587 11.402.196 27.177-11.38 32.714-9.081 4.35-20.778 2.028-27.868-4.939 6.584 9.03 20.044 12.517 30.218 7.648 11.568-5.54 16.966-21.316 11.38-32.718z",fill:"#5C9271"}),Y=i.createElement("path",{d:"M182.034 67.78c-2.79 3.71-7.987 4.925-11.944.505",stroke:"#161B18",strokeWidth:1.105,strokeMiterlimit:10,strokeLinecap:"round"}),X=i.createElement("path",{d:"M175.979 96.185c-.089.946 2.303-20.818 2.977-27.89-6.413-3.959-13.71-5.464-21.09-3.967l-2.976 27.888c7.38-1.496 14.677.009 21.089 3.969z",fill:"#CBD0D3"}),Z=i.createElement("path",{d:"M176.015 96.189c-.112.943 2.142-20.835 2.976-27.89 7.107-2.55 14.561-2.518 21.463.468l-2.976 27.888c-6.902-2.985-14.356-3.017-21.463-.466z",fill:"#EBEEF0"}),$=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M174.58 85.6c.437-4.028 1.037-9.587 1.66-15.427l.203.022a8024.607 8024.607 0 01-2.178 20.167 469.931 469.931 0 01-.165 1.486l-.006.048-.001.004a.035.035 0 01-.*************** 0 01-.*************** 0 01-.093-.094v-.009-.003l.001-.003v-.001l.002-.02h.001l.003-.026.03-.268.135-1.217.517-4.74zm-.683 6.25a.196.196 0 01.106-.07.2.2 0 01.09.089l-.196-.018zm.198.037v-.002.002zM172.305 84.486c.438-4.034 1.035-9.579 1.654-15.376l.203.022a10437.849 10437.849 0 01-2.172 20.136l-.136 1.235-.031.28-.006.05-.001.005-.001.004-.007.02c-.063.057-.179.014-.194-.027l-.001-.015.001-.006.001-.018h.001l.004-.036.032-.28.135-1.234c.121-1.104.3-2.743.518-4.76zm-.688 6.31l.2.019-.001-.006c-.015-.04-.13-.083-.194-.028a.101.101 0 00-.005.015zM170.011 83.561c.437-4.044 1.033-9.574 1.643-15.297l.203.022a10697.615 10697.615 0 01-2.163 20.089l-.138 1.259-.033.292-.007.057-.001.008-.001.007a.196.196 0 01-.************* 0 01-.092-.097v-.007l.001-.018h.001l.006-.049.032-.291.138-1.258.52-4.793zm-.696 6.391l.201.019a.192.192 0 00-.091-.097.189.189 0 00-.109.077l-.001.001zM167.698 82.823c.438-4.06 1.03-9.573 1.631-15.204l.202.021a14091.811 14091.811 0 01-2.153 20.038l-.141 1.287-.033.306-.008.063-.001.007v.004a.05.05 0 01-.002.01c-.008.02-.124.072-.199-.008-.001-.008-.002-.019-.001-.021v-.004-.002-.002l.001-.007v-.001l.007-.061.034-.306.14-1.287c.124-1.136.304-2.803.523-4.833zm-.704 6.488l.202.018a.171.171 0 00-.002-.018c-.074-.08-.19-.028-.198-.008l-.002.008zm.202.024zM165.365 82.269c.44-4.083 1.03-9.583 1.62-15.111l.202.021a15535.246 15535.246 0 01-2.146 19.997l-.143 1.319-.035.322-.008.07-.002.015-.004.015c-.092.07-.196-.01-.198-.03v-.01l.001-.006.202.019v-.005c-.002-.02-.106-.1-.198-.03a.088.088 0 00-.004.014v.002l.001-.008.007-.068.036-.322.143-1.319.526-4.885zM163.009 81.934c.44-4.093 1.027-9.57 1.606-14.994l.202.021a31558.74 31558.74 0 01-2.135 19.918l-.146 1.348-.037.336-.008.077-.002.016-.001.006a.204.204 0 01-.029.055.17.17 0 01-.173-.075v-.007l.001-.003.202.019.001-.004c0-.005-.066-.097-.173-.075a.225.225 0 00-.03.055v.003l.001-.012.009-.076.037-.337.145-1.347.53-4.924zM160.626 81.796c.441-4.111 1.025-9.57 1.593-14.891l.203.02c-.568 5.322-1.153 10.781-1.593 14.892l-.533 4.968-.148 1.378-.038.35a8.768 8.768 0 01-.009.084l-.002.018-.001.006c0 .004-.113.086-.202-.016V88.596l.203.02v-.007c-.089-.102-.202-.02-.202-.016l-.001.002.001-.002.001-.017a8.44 8.44 0 00.009-.084l.038-.35.148-1.378.533-4.968zM159.809 67.077l.202.02-2.315 21.692h-.01l-.182-.02h-.01l2.315-21.692z",fill:"#CBD0D3"}),J=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M173.801 92.217c-5.049-2.588-10.56-3.683-16.171-3.04a.406.406 0 01-.45-.354.405.405 0 01.358-.447c5.78-.662 11.454.468 16.637 3.125a.401.401 0 01.174.543.408.408 0 01-.548.173zM174.149 89.126c-5.054-2.595-10.57-3.692-16.187-3.048a.406.406 0 01-.45-.354.405.405 0 01.358-.447c5.787-.663 11.465.47 16.654 3.134a.401.401 0 01.173.543.408.408 0 01-.548.172zM174.49 86.028c-5.059-2.594-10.575-3.691-16.188-3.048a.405.405 0 01-.45-.354.405.405 0 01.358-.447c5.783-.662 11.461.47 16.653 3.134a.4.4 0 01.174.543.407.407 0 01-.547.172zM174.822 82.93c-5.059-2.595-10.575-3.692-16.188-3.049a.405.405 0 01-.45-.354.405.405 0 01.358-.446c5.783-.663 11.461.47 16.653 3.133a.4.4 0 01.174.543.407.407 0 01-.547.173zM175.157 79.838c-5.059-2.6-10.581-3.7-16.202-3.055a.404.404 0 11-.093-.8c5.792-.665 11.476.47 16.669 3.14a.4.4 0 01.174.543.409.409 0 01-.548.172zM175.485 76.739c-5.059-2.6-10.581-3.7-16.202-3.056a.404.404 0 11-.093-.8c5.792-.665 11.476.472 16.67 3.14a.401.401 0 01.173.543.408.408 0 01-.548.173z",fill:"#B8BDB9"}),Q=i.createElement("path",{d:"M159.902 67.086c5.707-.655 11.31.463 16.436 3.098l-.346 3.09c-5.121-2.628-10.719-3.743-16.42-3.09l.33-3.098z",fill:"#B8BDB9"}),ee=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M160.27 67.451l-.243 2.28c5.417-.524 10.73.523 15.624 2.922l.251-2.237c-4.894-2.461-10.214-3.522-15.632-2.965zm-.416-.765c5.792-.665 11.476.47 16.67 3.14l.247.127-.442 3.949-.526-.27c-5.054-2.594-10.571-3.691-16.187-3.047l-.504.057.419-3.919.323-.037z",fill:"#B8BDB9"}),te=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M179.36 86.1c.423-4.031 1.009-9.591 1.632-15.431l.203.021c-.623 5.84-1.21 11.4-1.632 15.43-.211 2.015-.382 3.648-.495 4.742l-.125 1.218-.027.269-.002.025h.001a.222.222 0 01-.003.02l-.001.004v.004a.035.035 0 01-.*************** 0 01-.************** 0 01-.09-.094v-.01-.002l.004-.049.027-.27.125-1.217.495-4.743zm-.449 6.274a.193.193 0 00-.088-.09.197.197 0 00-.107.067l.195.023zm-.201-.007v0zM181.821 85.478c.424-4.035 1.01-9.582 1.628-15.378l.203.02a9452.743 9452.743 0 00-2.255 21.376l-.029.28a.595.595 0 01-.003.036h.001a.189.189 0 01-.003.018l-.001.006-.004.015c-.023.037-.145.055-.195-.013-.002-.008-.002-.02-.002-.022v-.004-.004l.005-.052.028-.28.128-1.236.499-4.762zm-.457 6.334a.084.084 0 00-.002-.015c-.051-.068-.172-.05-.196-.013l-.001.005.199.023zM184.261 85.046c.426-4.045 1.011-9.576 1.622-15.299l.202.021c-.61 5.723-1.195 11.254-1.621 15.3-.213 2.022-.387 3.674-.503 4.793l-.131 1.26-.03.291-.005.05h.001l-.002.017a.024.024 0 01-.************* 0 01-.************** 0 01-.091-.097l.001-.007v-.008l.005-.057.03-.293.131-1.26.503-4.794zm-.466 6.415v-.002a.189.189 0 00-.09-.097.193.193 0 00-.11.076l.2.023zM186.679 84.801c.429-4.06 1.013-9.574 1.614-15.205l.202.02a14313.317 14313.317 0 00-2.288 21.634l-.007.061v.001h.001l-.001.008v.002l-.001.002v.003l-.006.021c-.09.062-.193-.012-.196-.033v-.01-.005l.001-.007.006-.063.031-.306.135-1.288.509-4.835zm-.479 6.511v-.008c-.003-.02-.106-.095-.195-.033l-.006.018.201.023zm-.202-.018z",fill:"#EBEEF0"}),ne=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M178.707 91.973c5.63-1.53 11.417-1.465 16.931.375.212.07.327.3.255.51a.408.408 0 01-.515.254c-5.351-1.786-10.973-1.852-16.457-.361a.407.407 0 01-.499-.282.403.403 0 01.285-.496zM179.019 88.88c5.636-1.536 11.429-1.473 16.948.368a.403.403 0 11-.259.764c-5.357-1.787-10.984-1.851-16.475-.356a.403.403 0 11-.214-.777zM179.351 85.78c5.635-1.534 11.427-1.471 16.943.369a.403.403 0 11-.259.764c-5.354-1.786-10.98-1.85-16.47-.356a.403.403 0 11-.214-.777zM179.683 82.682c5.635-1.535 11.427-1.472 16.943.368a.403.403 0 11-.259.764c-5.354-1.786-10.98-1.85-16.469-.355a.403.403 0 11-.215-.777zM179.994 79.588c5.643-1.54 11.442-1.479 16.966.365.213.071.327.3.255.51a.407.407 0 01-.515.254c-5.361-1.79-10.994-1.853-16.49-.352a.403.403 0 11-.216-.777zM180.323 76.489c5.642-1.54 11.441-1.479 16.965.365.213.072.327.3.255.51a.407.407 0 01-.515.254c-5.361-1.79-10.994-1.852-16.49-.352a.403.403 0 11-.215-.777zM180.671 73.385c5.637-1.534 11.43-1.471 16.949.37.212.071.327.3.255.51a.407.407 0 01-.515.254c-5.356-1.787-10.984-1.852-16.474-.357a.402.402 0 11-.215-.777zM180.987 70.291c5.642-1.54 11.441-1.478 16.965.366.213.071.327.3.255.51a.406.406 0 01-.514.254c-5.362-1.79-10.995-1.853-16.491-.353a.403.403 0 11-.215-.777z",fill:"#CBD0D3"}),ie=i.createElement("path",{d:"M181.096 70.68c5.569-1.52 11.288-1.457 16.728.358l-.992 9.297c-5.443-1.817-11.159-1.88-16.728-.36l.992-9.296z",fill:"#CBD0D3"}),re=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M181.469 70.996l-.901 8.44c5.297-1.34 10.719-1.27 15.911.361l.905-8.477c-5.179-1.662-10.609-1.722-15.915-.323zm-.482-.705c5.642-1.54 11.444-1.477 16.965.366.18.06.294.236.274.424l-.992 9.296a.404.404 0 01-.534.34c-5.361-1.79-10.994-1.853-16.49-.352a.409.409 0 01-.371-.082.402.402 0 01-.141-.349l.992-9.296a.404.404 0 01.297-.347z",fill:"#CBD0D3"}),ae=i.createElement("path",{d:"M204.146 84.595c-1.671 3.879-7.751 2.74-10.354-.297M151.096 75.116c-.045 5.635 4.349 10.805 12.1 6.915",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),oe=i.createElement("path",{d:"M91.429 81.618c-.729 8.2-.457 15.965 1.975 22.796h4.47M80.227 81.238c-.76 8.178-.245 15.966 2.153 23.178h-4.478",stroke:"#161B18",strokeWidth:1.472,strokeLinecap:"round",strokeLinejoin:"round"}),ce=i.createElement("path",{d:"M118.418 79.006c7.32 3.89 10.2 8.445 9.473 12.335M60.719 71.227c-7.51 3.313-11.627 6.373-11.627 13.398",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"}),le=i.createElement("path",{d:"M57.84 49.763l-3.38 33.985 68.342 6.303 1.443-32.81-66.405-7.478z",fill:"#77AD8C"}),se=i.createElement("path",{d:"M57.476 82.268l2.638-32.25-2.273-.255-3.38 33.985 68.341 6.303.127-2.876-65.453-4.907z",fill:"#5C9271"}),ue=i.createElement("path",{d:"M80.625 72.749c3.832 4.721 11.357 6.736 17.468 1.896",stroke:"#161B18",strokeWidth:1.105,strokeMiterlimit:10,strokeLinecap:"round"}),de=i.createElement("path",{d:"M86.74 98.236c-.1.945 2.55-20.79 3.308-27.852-6.365-4.035-13.644-5.624-21.041-4.213L65.7 94.023c7.397-1.41 14.676.179 21.04 4.213z",fill:"#CBD0D3"}),ge=i.createElement("path",{d:"M86.776 98.24c-.123.942 2.39-20.808 3.308-27.852 7.137-2.468 14.59-2.35 21.455.717l-3.307 27.851c-6.865-3.065-14.319-3.184-21.456-.716z",fill:"#EBEEF0"}),fe=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M85.467 87.637c.485-4.024 1.151-9.575 1.844-15.407l.202.023a8546.627 8546.627 0 01-2.418 20.141 653.021 653.021 0 01-.192 1.544.19.19 0 01-.*************** 0 01-.091-.095v-.009-.003-.005l.002-.02h.001l.004-.025.033-.268.15-1.215.573-4.733zm-.757 6.242a.197.197 0 01.106-.069c.*************.089.09l-.195-.021zm.197.038v-.001.001zM83.205 86.496c.485-4.028 1.149-9.566 1.836-15.355l.202.023a9660.973 9660.973 0 01-2.561 21.343 80.824 80.824 0 01-.042.335l-.001.004a.187.187 0 01-.007.02c-.065.056-.18.012-.194-.03a.164.164 0 010-.02c0-.005 0-.011.002-.019l.005-.036.035-.278.15-1.233.575-4.754zm-.763 6.302l.199.02v-.005c-.015-.041-.13-.085-.194-.03a.183.183 0 00-.005.015zM80.927 85.544c.485-4.04 1.146-9.561 1.825-15.277l.202.023A11320.95 11320.95 0 0180.4 91.61a113.556 113.556 0 01-.************ 0 01-.*************** 0 01-.************ 0 01-.09-.1.14.14 0 01.001-.023l.007-.05.036-.29.153-1.257.577-4.786zm-.772 6.383l.201.02a.193.193 0 00-.09-.097.194.194 0 00-.11.075v.002zM78.619 84.779c.486-4.055 1.144-9.56 1.811-15.184l.203.024a14137.83 14137.83 0 01-2.593 21.664l-.001.007a.093.093 0 01-.003.014c-.008.02-.124.07-.198-.01a.187.187 0 01-.002-.025v-.002-.001l.001-.008.001-.001.008-.06.037-.306.156-1.285.58-4.827zm-.781 6.48l.202.02-.002-.019c-.074-.08-.19-.03-.198-.01l-.002.008zm.201.026zM76.295 84.198c.488-4.077 1.144-9.57 1.8-15.091l.201.023a17204.58 17204.58 0 01-2.59 21.68l-.002.015-.004.014c-.093.07-.196-.012-.198-.032V90.798v-.007l.203.022v-.005c-.001-.02-.104-.101-.198-.033a.244.244 0 00-.004.015v.001l.001-.007.009-.069c.008-.067.02-.174.039-.32l.158-1.318c.14-1.157.341-2.84.585-4.88zM73.94 83.836c.489-4.088 1.14-9.558 1.784-14.975l.202.024a21886.98 21886.98 0 01-2.584 21.65l-.002.017v.005a.206.206 0 01-.03.055.17.17 0 01-.172-.077v-.01l.203.021v-.003a.17.17 0 00-.172-.077.208.208 0 00-.03.054v.004l.001-.013.01-.076.04-.336.162-1.346.588-4.917zM71.563 83.67l1.77-14.872.201.024a33930.12 33930.12 0 01-2.568 21.56l-.01.083-.002.018v.006c-.001.004-.115.084-.203-.019V90.461l.203.022v-.006c-.088-.103-.201-.023-.202-.019v.002-.001l.002-.018.01-.083.042-.35.165-1.376.592-4.962zM70.918 68.942l.202.023-2.572 21.664-.01-.001a70.908 70.908 0 01-.182-.022h-.01l2.572-21.664z",fill:"#CBD0D3"}),me=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M84.613 94.243c-5.017-2.647-10.516-3.805-16.133-3.227a.405.405 0 01-.446-.36.404.404 0 01.362-.442c5.789-.596 11.448.6 16.6 3.318a.401.401 0 01.167.545.408.408 0 01-.55.166zM84.997 91.157c-5.023-2.653-10.527-3.815-16.15-3.236a.405.405 0 01-.446-.36.404.404 0 01.363-.442c5.794-.596 11.459.603 16.615 3.327a.4.4 0 01.167.545.408.408 0 01-.55.166zM85.372 88.063c-5.028-2.653-10.53-3.814-16.15-3.236a.405.405 0 01-.446-.36.404.404 0 01.363-.442c5.791-.595 11.455.604 16.615 3.327a.4.4 0 01.167.545.408.408 0 01-.55.166zM85.739 84.969c-5.027-2.653-10.53-3.815-16.15-3.237a.405.405 0 01-.446-.359.404.404 0 01.363-.442c5.791-.596 11.455.603 16.615 3.326a.401.401 0 01.168.545.408.408 0 01-.55.167zM86.113 81.88c-5.027-2.658-10.536-3.822-16.165-3.243a.405.405 0 01-.445-.359.404.404 0 01.362-.442c5.8-.598 11.47.604 16.631 3.333a.401.401 0 01.167.546.408.408 0 01-.55.165zM86.477 78.786c-5.028-2.658-10.537-3.823-16.165-3.243a.405.405 0 01-.446-.36.404.404 0 01.362-.442c5.8-.597 11.47.606 16.632 3.334a.401.401 0 01.167.545.408.408 0 01-.55.166z",fill:"#B8BDB9"}),pe=i.createElement("path",{d:"M71.012 68.953c5.714-.588 11.303.594 16.398 3.288l-.383 3.087c-5.09-2.688-10.674-3.868-16.383-3.28l.368-3.095z",fill:"#B8BDB9"}),he=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M71.377 69.322l-.27 2.277c5.424-.461 10.724.647 15.589 3.103l.277-2.234c-4.864-2.518-10.171-3.64-15.596-3.146zm-.406-.77c5.8-.597 11.47.604 16.63 3.334l.247.13-.49 3.943-.522-.276c-5.023-2.652-10.526-3.814-16.15-3.235l-.504.052.465-3.915.323-.033z",fill:"#B8BDB9"}),ve=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M90.243 88.191c.47-4.025 1.123-9.578 1.816-15.41l.202.023a8576.285 8576.285 0 00-2.367 20.146 755.542 755.542 0 00-.172 1.51h.001a.8.8 0 01-.003.02v.006l-.001.002a.145.145 0 01-.************* 0 01-.************* 0 01-.089-.096v-.008-.004l.005-.047.03-.27.14-1.216.551-4.737zm-.523 6.269a.194.194 0 00-.088-.091.197.197 0 00-.107.066l.195.025zm-.201-.01zM92.712 87.598c.472-4.03 1.124-9.57 1.811-15.359l.203.024a9910.386 9910.386 0 00-2.51 21.348l-.031.279-.004.036c0 .008 0 .014-.002.018v.006l-.005.015c-.024.036-.146.053-.195-.016a.181.181 0 01-.002-.021v-.005-.004l.006-.051.031-.28.143-1.234.555-4.756zm-.532 6.328c0-.004 0-.01-.002-.015-.05-.069-.171-.052-.195-.016l-.002.006.199.025zM95.153 87.195c.475-4.041 1.125-9.564 1.804-15.28l.202.023a11129.421 11129.421 0 00-2.509 21.325 89.368 89.368 0 00-.038.34.504.504 0 01-.002.018l-.002.007a.192.192 0 01-.************* 0 01-.09-.098v-.007l.001-.008.007-.057.033-.293a5635.92 5635.92 0 01.706-6.046zm-.542 6.409v-.002c0-.007-.03-.07-.09-.099a.194.194 0 00-.11.075l.2.026zM97.576 86.978c.478-4.056 1.127-9.562 1.795-15.186l.202.023a13876.904 13876.904 0 00-2.546 21.606l-.007.06v.013l-.001.004a.203.203 0 01-.007.02c-.09.062-.192-.014-.195-.035v-.01-.004l.001-.008.007-.063.035-.306.15-1.285.566-4.83zm-.556 6.505v-.008c-.003-.021-.104-.097-.195-.036a.18.18 0 00-.006.018l.201.026zm-.202-.02z",fill:"#EBEEF0"}),be=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M89.522 94.057c5.648-1.465 11.434-1.333 16.925.57a.401.401 0 01.249.514.407.407 0 01-.517.247c-5.33-1.848-10.95-1.978-16.452-.552a.403.403 0 11-.205-.78zM89.865 90.966c5.655-1.47 11.446-1.34 16.943.566a.4.4 0 01.249.514.407.407 0 01-.517.247c-5.336-1.85-10.961-1.979-16.47-.547a.403.403 0 11-.206-.78zM90.236 87.872c5.654-1.47 11.444-1.34 16.938.565a.4.4 0 01.249.513.407.407 0 01-.517.248c-5.333-1.849-10.957-1.978-16.464-.547a.403.403 0 11-.205-.78zM90.603 84.776c5.654-1.469 11.444-1.339 16.939.566a.402.402 0 11-.269.76c-5.332-1.848-10.957-1.977-16.464-.546a.403.403 0 11-.206-.78zM90.954 81.687c5.66-1.474 11.458-1.346 16.96.563a.402.402 0 01.249.513.408.408 0 01-.518.247c-5.339-1.852-10.971-1.98-16.485-.544a.403.403 0 11-.206-.78zM91.317 78.592c5.66-1.473 11.458-1.346 16.961.563a.402.402 0 11-.269.76c-5.34-1.852-10.972-1.979-16.486-.544a.403.403 0 11-.206-.78zM91.705 75.492c5.654-1.469 11.447-1.339 16.943.567a.402.402 0 01.249.513.407.407 0 01-.518.247c-5.334-1.849-10.96-1.978-16.469-.548a.403.403 0 11-.205-.78zM92.056 72.402c5.66-1.474 11.458-1.345 16.96.563a.402.402 0 01.249.513.407.407 0 01-.518.248c-5.34-1.852-10.972-1.98-16.486-.544a.403.403 0 11-.206-.78z",fill:"#CBD0D3"}),Ee=i.createElement("path",{d:"M92.158 72.792c5.587-1.455 11.305-1.326 16.723.553l-1.103 9.284c-5.421-1.88-11.136-2.008-16.723-.553l1.103-9.284z",fill:"#CBD0D3"}),_e=i.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M92.53 73.113l-1.002 8.43c5.313-1.28 10.734-1.147 15.906.545l1.005-8.467c-5.159-1.721-10.587-1.844-15.91-.508zm-.475-.71c5.66-1.475 11.461-1.345 16.961.562.179.062.291.24.269.427l-1.103 9.285a.4.4 0 01-.188.294.41.41 0 01-.35.039c-5.339-1.852-10.971-1.98-16.485-.544a.408.408 0 01-.37-.086.4.4 0 01-.136-.35l1.102-9.285c.02-.164.139-.3.3-.343z",fill:"#CBD0D3"}),Oe=i.createElement("path",{d:"M127.892 91.34c-1.329 7.115-12.918 8.843-24.256 0M49.088 84.625c0 6.05 9.181 11.081 24.545 3.457",stroke:"#000",strokeWidth:1.468,strokeLinecap:"round"});t.a=function SvgNoAudienceBannerGraphic(e){return i.createElement("svg",r({viewBox:"0 0 211 109",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p,h,v,b,E,_,O,y,k,j,S,A,w,T,C,N,D,R,x,M,I,B,P,L,F,z,W,V,U,G,H,q,K,Y,X,Z,$,J,Q,ee,te,ne,ie,re,ae,oe,ce,le,se,ue,de,ge,fe,me,pe,he,ve,be,Ee,_e,Oe)}},35:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"f",(function(){return c})),n.d(t,"e",(function(){return l})),n.d(t,"c",(function(){return s})),n.d(t,"d",(function(){return u})),n.d(t,"b",(function(){return d}));n(14);var i=n(2),r="missing_required_scopes",a="insufficientPermissions",o="forbidden";function c(e){return(null==e?void 0:e.code)===r}function l(e){var t;return[a,o].includes(null==e||null===(t=e.data)||void 0===t?void 0:t.reason)}function s(e){var t;return!!(null==e||null===(t=e.data)||void 0===t?void 0:t.reconnectURL)}function u(e,t){return!(!(null==t?void 0:t.storeName)||l(e)||c(e)||s(e))}function d(e){return"internal_server_error"===(null==e?void 0:e.code)?Object(i.__)("There was a critical error on this website while fetching data","google-site-kit"):"invalid_json"===(null==e?void 0:e.code)?Object(i.__)("The server provided an invalid response","google-site-kit"):null==e?void 0:e.message}},350:function(e,t,n){"use strict";(function(e){var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=Object(c.forwardRef)((function(t,n){var i=t.className,a=t.children,o=t.Icon,c=t.SVGGraphic;return e.createElement("div",{ref:n,className:r()("googlesitekit-lean-cta-banner",i)},e.createElement("div",{className:"googlesitekit-lean-cta-banner__body"},o&&e.createElement("div",{className:"googlesitekit-lean-cta-banner__body-icon"},e.createElement(o,{width:"32",height:"32"})),e.createElement("div",{className:"googlesitekit-lean-cta-banner__body-content"},a)),c&&e.createElement("div",{className:"googlesitekit-lean-cta-banner__graphic"},e.createElement(c,null)))}));l.propTypes={className:o.a.string,children:o.a.node.isRequired,Icon:o.a.elementType,SVGGraphic:o.a.elementType},t.a=l}).call(this,n(4))},351:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M2.956 80.014a32.365 32.365 0 006.283 9.673c10.459 11.015 25.911 11.949 40.083 9.193A98.307 98.307 0 0088.91 81.449c6.738-4.994 13.394-11.19 22.316-11.467 3.35-.103 6.814.82 9.345 2.998 5.259 4.497 5.165 12.186 9.269 17.516 5.458 7.088 13.308 9.283 21.87 10.101 24.191 2.309 53.006-5.265 69.646-23.694 14.857-16.452 16.64-42.63-1.343-57.629-8.216-6.852-19.483-9.992-30.117-8.392-8.127 1.223-16.378 4.942-24.791 5.977-9.908 1.22-16.033-2.943-24.05-7.79C127.086.641 108.62-2.597 92.807 2.292 77.671 6.972 66.777 19.747 52.048 25.36c-12.727 4.852-27.762 4.114-38.82 12.017C.143 46.727-3.146 65.603 2.956 80.014z",fill:"#F3F5F7"}),o=i.createElement("path",{d:"M118.945 116.194c32.642 0 59.104-1.654 59.104-3.694s-26.462-3.694-59.104-3.694c-32.643 0-59.105 1.654-59.105 3.694s26.462 3.694 59.105 3.694z",fill:"#161B18",opacity:.1}),c=i.createElement("path",{d:"M118.945 116.194c32.642 0 59.104-1.654 59.104-3.694s-26.462-3.694-59.104-3.694c-32.643 0-59.105 1.654-59.105 3.694s26.462 3.694 59.105 3.694z",fill:"#CBD0D3"}),l=i.createElement("path",{d:"M99.725 51.387c1.758 6.518 7.872 11.126 14.356 13.01 6.484 1.882 13.377 1.514 20.12 1.177 3.188-.158 6.449-.298 9.503.627 3.054.925 5.912 3.137 6.724 6.222.466 1.773.121 3.686-.787 5.274",stroke:"#161B18",strokeWidth:1.396,strokeLinecap:"round",strokeLinejoin:"round"}),s=i.createElement("path",{d:"M87.114 62.487c-1.015 16.075-6.61 30.497-2.87 48.618h-4.26M91.929 111.105h-4.261l7.53-48.618",stroke:"#161B18",strokeWidth:1.4,strokeLinecap:"round",strokeLinejoin:"round"}),u=i.createElement("path",{d:"M73.527 57.419c-3.635 1.642-10.613 5.299-10.613 12.724",stroke:"#000",strokeWidth:1.396,strokeLinecap:"round"}),d=i.createElement("path",{d:"M103.118 84.07l-2.957.269-18.04 1.632-10.545.952-1-23.491-1.74-40.88 32.912-1.199.664 30.445.09 4.099.612 28.078.004.095z",fill:"#CBD0D3"}),g=i.createElement("path",{d:"M100.163 84.338l1.697-.154 1.261-.114-1.371-62.719-1.891.087 1.017 59.457-29.439 2.786.103 2.34.04.9 10.544-.952 18.041-1.632-.002.001z",fill:"#999F9B"}),f=i.createElement("path",{d:"M62.912 70.143c0 4.321 4.917 7.437 8.424 5.913",stroke:"#000",strokeWidth:1.396,strokeLinecap:"round"}),m=i.createElement("path",{d:"M159.169 21.79l-22.985 89.068",stroke:"#7B807D",strokeWidth:3.607,strokeMiterlimit:10,strokeLinecap:"round"}),p=i.createElement("path",{d:"M157.57 14.896l-34.151 34.351a3.61 3.61 0 00.016 5.1 3.608 3.608 0 001.62.929l46.822 12.4a3.606 3.606 0 004.404-4.435l-12.674-46.745a3.599 3.599 0 00-2.557-2.542 3.605 3.605 0 00-3.48.942z",fill:"#E77D5B"}),h=i.createElement("path",{d:"M153.345 35.252l2.003-7.566 3.905 1.034-2.003 7.566-2.874 9.163-3.103-.822 2.072-9.375zm-2.709 18.123a2.77 2.77 0 01-1.715-1.274 2.768 2.768 0 01-.259-2.121c.197-.744.619-1.304 1.265-1.68a2.77 2.77 0 012.121-.259c.744.197 1.304.619 1.68 1.266.375.646.465 1.342.268 2.085a2.77 2.77 0 01-1.275 1.715c-.646.376-1.342.465-2.085.268z",fill:"#962C0A"}),v=i.createElement("path",{d:"M149.639 77.697a6.848 6.848 0 01-3.747 3.098c-3.335 1.14-7.399-.673-8.778-3.916",stroke:"#161B18",strokeWidth:1.396,strokeLinecap:"round",strokeLinejoin:"round"});t.a=function SvgAudienceSegmentationErrorFullWidth(e){return i.createElement("svg",r({viewBox:"0 0 233 117",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p,h,v)}},359:function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return ChipTabGroup}));var r,a=n(21),o=n.n(a),c=n(27),l=n.n(c),s=n(15),u=n.n(s),d=n(6),g=n.n(d),f=n(81),m=n(420),p=n(0),h=n(422),v=n(2),b=n(3),E=n(10),_=n(26),O=n(29),y=n(8),k=n(23),j=n(7),S=n(19),A=n(360),w=n(361),T=n(362),C=n(24),N=n(121),D=n(214),R=n(131),x=n(74),M=(r={},g()(r,_.c.SLUG,D.a),g()(r,_.g.SLUG,R.a),r);function ChipTabGroup(t){var n=t.allMetricItems,r=t.savedItemSlugs,a=Object(p.useRef)(),c=Object(p.useState)(_.c.SLUG),s=u()(c,2),d=s[0],D=s[1],R=Object(p.useState)(0),I=u()(R,2),B=I[0],P=I[1],L=Object(C.e)()===C.b,F=Object(b.useSelect)((function(e){return e(O.a).getValue(_.j,_.i)})),z=Object(b.useSelect)((function(e){return e(O.a).getValue(_.j,_.a)||[]})),W=Object(b.useSelect)((function(e){return e(O.a).getValue(_.j,_.o)||[]})),V=Object(b.useSelect)((function(e){return e(j.a).isUserInputCompleted()})),U=Object(b.useSelect)((function(e){var t,n=e(j.a).getUserPickedMetrics();if(null==n?void 0:n.length){var i=e(y.r).getKeyMetricsConversionEventWidgets();return Object.keys(i).filter((function(e){return n.some((function(t){return i[e].includes(t)}))}))}var r=e(j.a).getUserInputSettings();return null==r||null===(t=r.includeConversionEvents)||void 0===t?void 0:t.values})),G=Object(b.useSelect)((function(e){return e(S.a).isModuleConnected("analytics-4")})),H=Object(b.useSelect)((function(e){return G?e(y.r).getDetectedEvents():[]})),q=Object(b.useSelect)((function(e){return e(j.a).getAnswerBasedMetrics(null,[].concat(l()(U||[]),l()(H||[])))})),K=[y.l.SUBMIT_LEAD_FORM,y.l.CONTACT,y.l.GENERATE_LEAD].filter((function(e){return(null==H?void 0:H.includes(e))||(null==U?void 0:U.includes(e))})),Y=[y.l.ADD_TO_CART,y.l.PURCHASE].filter((function(e){return(null==H?void 0:H.includes(e))||(null==U?void 0:U.includes(e))})),X=Object(p.useMemo)((function(){return[_.h,_.d].concat(l()((null==K?void 0:K.length)?[_.e]:[]),l()((null==Y?void 0:Y.length)?[_.f]:[]),[_.b])}),[K,Y]),Z=Object(p.useMemo)((function(){return V&&(null==q?void 0:q.length)?[_.c,_.g]:[_.c]}),[V,q]),$=Object(p.useMemo)((function(){return[].concat(l()(Z),l()(X))}),[Z,X]),J=Object(b.useSelect)((function(e){if(!G)return[];var t=e(y.r).getNewBadgeEvents();if((null==H?void 0:H.length)&&(null==t?void 0:t.length)){var n=H.filter((function(e){return y.e.includes(e)})),i=t.filter((function(e){return y.e.includes(e)})),r=t.filter((function(e){return!y.e.includes(e)}));if((null==n?void 0:n.length)>1&&i.length>0)return r}return t})),Q=Object(b.useSelect)((function(e){return G?e(y.r).getKeyMetricsConversionEventWidgets():[]})),ee=Object(p.useCallback)((function(){var e,t,n,i=null===(e=a.current)||void 0===e?void 0:e.querySelector(".mdc-tab-scroller__scroll-content");if(L){var r=null===(t=a.current)||void 0===t?void 0:t.querySelectorAll(".googlesitekit-chip-tab-group__tab-items .mdc-tab");if((null==r?void 0:r.length)&&i){var o=null===(n=a.current)||void 0===n?void 0:n.getBoundingClientRect(),c=[];r.forEach((function(e,t){var n=e.getBoundingClientRect();n.left>=o.left&&n.right<=o.right&&c.push(t)}));var l=r[c.length];if(l){var s=l.getBoundingClientRect();(s.left>=o.right||s.left-o.right<0&&-(s.left-o.right)<=20)&&("2px"===i.style.columnGap?i.style.columnGap="20px":i.style.columnGap="2px",ee())}}}}),[L]),te=g()({},_.c.SLUG,0),ne={},ie={},re=function(e){var t,i=n[e].group;if((i===d||d===_.c.SLUG&&z.includes(e))&&(ne[e]=n[e]),d===_.g.SLUG&&q.includes(e)&&q.includes(e)&&(ne[e]=n[e]),!te[i]){var r=Object.keys(n).filter((function(e){return!(n[e].group!==i||!(null==F?void 0:F.includes(e)))})).length;te[i]=r}(null==J?void 0:J.length)&&(J.some((function(t){return Q[t].includes(e)}))&&(ie[i]=[].concat(l()(null!==(t=ie[i])&&void 0!==t?t:[]),[e])))};for(var ae in n)re(ae);var oe=Object(b.useDispatch)(O.a).setValues,ce=Object(p.useCallback)((function(){var e;oe(_.j,(e={},g()(e,_.i,F),g()(e,_.a,[].concat(l()(z),l()(W))),g()(e,_.o,[]),e))}),[F,z,W,oe]),le=Object(p.useCallback)((function(e,t){if(e)D(e);else{var n=$[t];P(t),D(n.SLUG)}W.length&&ce()}),[$,W,D,ce]),se=Object(b.useSelect)((function(e){return e(k.b).getValue(_.k)})),ue=Object(h.a)(se),de=Object.keys(ie);Object(p.useEffect)((function(){if(!ue&&se)if(D(_.c.SLUG),P(0),de.length&&L){var e=$.find((function(e){return e.SLUG===de[0]}));P($.indexOf(e)),D(e.SLUG)}else P(0),D(_.c.SLUG);ue&&!se&&ce(),!ue&&se&&ee()}),[se,ue,W,$,L,de,ce,ee]);var ge=Object(N.a)(ee,50);Object(f.a)((function(){e.addEventListener("resize",ge)})),Object(m.a)((function(){return e.removeEventListener("resize",ge)}));var fe=[[].concat(l()(Z),l()(X.slice(0,2))),l()(X.slice(2))];return i.createElement("div",{className:"googlesitekit-chip-tab-group"},i.createElement("div",{className:"googlesitekit-chip-tab-group__tab-items",ref:a},!L&&fe.map((function(e){return i.createElement("div",{key:"row-".concat(e[0].SLUG),className:"googlesitekit-chip-tab-group__tab-items-row"},e.map((function(e){return i.createElement(A.a,{key:e.SLUG,slug:e.SLUG,label:e.LABEL,hasNewBadge:!!(null==ie?void 0:ie[e.SLUG]),isActive:e.SLUG===d,onClick:le,selectedCount:te[e.SLUG]})})))})),L&&i.createElement(E.TabBar,{activeIndex:B,handleActiveIndexUpdate:function(e){return le(null,e)}},$.map((function(e,t){var n=M[e.SLUG]||x.a;return i.createElement(E.Tab,{key:t,"aria-label":e.LABEL},i.createElement(n,{width:12,height:12,className:"googlesitekit-chip-tab-group__chip-item-svg googlesitekit-chip-tab-group__tab-item-mobile-svg googlesitekit-chip-tab-group__chip-item-svg__".concat(e.SLUG)}),e.LABEL,te[e.SLUG]>0&&i.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-count"},"(",te[e.SLUG],")"),!!(null==ie?void 0:ie[e.SLUG])&&i.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-new-dot"}))})))),i.createElement("div",{className:"googlesitekit-chip-tab-group__tab-item"},Object.keys(ne).map((function(e){var t,n=ne[e].group,a=null==ie||null===(t=ie[n])||void 0===t?void 0:t.includes(e);return i.createElement(w.a,o()({key:e,slug:e,savedItemSlugs:r,isNewlyDetected:a},ne[e]))})),!Object.keys(ne).length&&i.createElement("div",{className:"googlesitekit-chip-tab-group__graphic"},i.createElement(T.a,{height:250}),i.createElement("p",null,Object(v.__)("No metrics were selected yet","google-site-kit")))))}}).call(this,n(28),n(4))},36:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return _})),n.d(t,"b",(function(){return b})),n.d(t,"c",(function(){return E}));var i=n(99),r=e._googlesitekitTrackingData||{},a=r.activeModules,o=void 0===a?[]:a,c=r.isSiteKitScreen,l=r.trackingEnabled,s=r.trackingID,u=r.referenceSiteURL,d=r.userIDHash,g=r.isAuthenticated,f={activeModules:o,trackingEnabled:l,trackingID:s,referenceSiteURL:u,userIDHash:d,isSiteKitScreen:c,userRoles:r.userRoles,isAuthenticated:g,pluginVersion:"1.151.0"},m=Object(i.a)(f),p=m.enableTracking,h=m.disableTracking,v=(m.isTrackingEnabled,m.initializeSnippet),b=m.trackEvent,E=m.trackEventOnce;function _(e){e?p():h()}c&&l&&v()}).call(this,n(28))},360:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Chip}));var i,r=n(6),a=n.n(r),o=n(1),c=n.n(o),l=n(11),s=n.n(l),u=n(10),d=n(26),g=n(214),f=n(131),m=n(74),p=(i={},a()(i,d.c.SLUG,g.a),a()(i,d.g.SLUG,f.a),i);function Chip(t){var n=t.slug,i=t.label,r=t.isActive,a=t.onClick,o=t.hasNewBadge,c=void 0!==o&&o,l=t.selectedCount,d=void 0===l?0:l,g=p[n]||m.a;return e.createElement(u.Button,{className:s()("googlesitekit-chip-tab-group__chip-item",{"googlesitekit-chip-tab-group__chip-item--active":r}),icon:e.createElement(g,{width:12,height:12,className:"googlesitekit-chip-tab-group__chip-item-svg googlesitekit-chip-tab-group__chip-item-svg__".concat(n)}),trailingIcon:d>0?e.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-count"},"(",d,")"):null,onClick:function(){return a(n)}},i,c&&e.createElement("span",{className:"googlesitekit-chip-tab-group__chip-item-new-dot"}))}Chip.propTypes={slug:c.a.string.isRequired,label:c.a.string.isRequired,isActive:c.a.bool,hasNewBadge:c.a.bool,selectedCount:c.a.number,onClick:c.a.func.isRequired}}).call(this,n(4))},361:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricItem}));var i=n(6),r=n.n(i),a=n(27),o=n.n(a),c=n(1),l=n.n(c),s=n(0),u=n(2),d=n(3),g=n(29),f=n(47),m=n(19),p=n(26),h=n(117);function MetricItem(t){var n=t.slug,i=t.title,a=t.description,c=t.isNewlyDetected,l=t.savedItemSlugs,v=void 0===l?[]:l,b=Object(d.useSelect)((function(e){var t=e(m.a).getModule,i=e(f.a).getWidget(n);return null==i?void 0:i.modules.reduce((function(e,n){var i=t(n);return(null==i?void 0:i.connected)||!(null==i?void 0:i.name)?e:[].concat(o()(e),[i.name])}),[])})),E=Object(d.useSelect)((function(e){return e(g.a).getValue(p.j,p.i)})),_=Object(d.useSelect)((function(e){return e(g.a)})).getValue,O=Object(d.useDispatch)(g.a).setValues,y=Object(s.useCallback)((function(e){var t,i=_(p.j,p.i),a=e.target.checked?i.concat([n]):i.filter((function(e){return e!==n}));O(p.j,(t={},r()(t,p.i,a),r()(t,p.o,a),t))}),[_,O,n]),k=null==E?void 0:E.includes(n),j=!v.includes(n)&&b.length>0,S="key-metric-selection-checkbox-".concat(n);return e.createElement(h.c,{id:S,slug:n,title:i,description:a,isNewlyDetected:c,isItemSelected:k,isItemDisabled:j,onCheckboxChange:y},b.length>0&&e.createElement("div",{className:"googlesitekit-selection-panel-item-error"},Object(u.sprintf)(
/* translators: %s: module names. */
Object(u._n)("%s is disconnected, no data to show","%s are disconnected, no data to show",b.length,"google-site-kit"),b.join(Object(u.__)(" and ","google-site-kit")))))}MetricItem.propTypes={slug:l.a.string.isRequired,title:l.a.string.isRequired,description:l.a.string.isRequired,isNewlyDetected:l.a.bool,savedItemSlugs:l.a.array}}).call(this,n(4))},362:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M59.238 58.571c-2.136 20.178 4.272 29.099 20.48 53.216 16.209 24.118-29.092 62.914 5.475 101.268 33.827 37.532 69.419.009 111.314-4.555 29.443-3.208 57.819 12.98 90.86 5.9 33.04-7.08 46.385-42.599 43.153-68.059-5.59-44.041-26.24-49.107-34.893-66.461-8.654-17.354 2.902-52.997-30.287-73.16-33.19-20.163-76.71 14.42-112.503 12.37-20.651-1.182-40.932-4.995-59.264.86-18.53 5.918-32.662 22.571-34.335 38.621z",fill:"#B8E6CA"}),o=i.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter0_d_2200_11981)"},i.createElement("rect",{x:242.455,y:45.266,width:130.621,height:89.651,rx:10.957,transform:"rotate(15 242.455 45.266)",fill:"#fff"})),c=i.createElement("rect",{x:253.726,y:64.785,width:24.903,height:7.969,rx:3.985,transform:"rotate(15 253.726 64.785)",fill:"#EBEEF0"}),l=i.createElement("rect",{x:249.342,y:81.144,width:49.806,height:19.923,rx:9.961,transform:"rotate(15 249.342 81.144)",fill:"#FFDED3"}),s=i.createElement("rect",{x:240.436,y:114.357,width:99.428,height:8.773,rx:3.985,transform:"rotate(15 240.436 114.357)",fill:"#EBEEF0"}),u=i.createElement("path",{d:"M256.195 90.198l4.644 8.044m0 0l1.412-4.986m-1.412 4.986l-5.023-1.27",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),d=i.createElement("rect",{x:268.706,y:93.551,width:19.923,height:5.977,rx:1.992,transform:"rotate(15 268.706 93.55)",fill:"#fff"}),g=i.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter1_d_2200_11981)"},i.createElement("rect",{x:13.887,y:79.094,width:130.621,height:89.68,rx:10.957,transform:"rotate(-15 13.887 79.094)",fill:"#fff"})),f=i.createElement("rect",{x:32.989,y:90.122,width:62.386,height:7.798,rx:3.899,transform:"rotate(-15 32.99 90.122)",fill:"#EBEEF0"}),m=i.createElement("rect",{x:37.691,y:106.902,width:49.806,height:19.923,rx:9.961,transform:"rotate(-15 37.691 106.902)",fill:"#FFDED3"}),p=i.createElement("rect",{x:46.612,y:140.967,width:99.428,height:7.798,rx:3.899,transform:"rotate(-15 46.612 140.967)",fill:"#EBEEF0"}),h=i.createElement("path",{d:"M48.152 111.318l8.044 4.645m0 0l-1.27-5.024m1.27 5.024l-4.986 1.411",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),v=i.createElement("rect",{x:60.663,y:107.966,width:19.923,height:5.977,rx:1.992,transform:"rotate(-15 60.663 107.966)",fill:"#fff"}),b=i.createElement("g",{filter:"url(#key-metrics-no-selected-items_svg__filter2_d_2200_11981)"},i.createElement("rect",{x:126.251,y:37.4,width:130.621,height:89.68,rx:10.957,fill:"#fff"})),E=i.createElement("rect",{x:143.013,y:53.134,width:98.333,height:7.867,rx:3.933,fill:"#EBEEF0"}),_=i.createElement("rect",{x:142.369,y:70.423,width:49.806,height:19.923,rx:9.961,fill:"#B8E6CA"}),O=i.createElement("rect",{x:143.013,y:105.84,width:33.04,height:7.867,rx:3.933,fill:"#EBEEF0"}),y=i.createElement("path",{d:"M151.336 84.036l6.568-6.567m0 0l-5.182-.073m5.182.073l.073 5.18",stroke:"#fff",strokeWidth:1.494,strokeLinecap:"round",strokeLinejoin:"round"}),k=i.createElement("rect",{x:164.287,y:77.395,width:19.923,height:5.977,rx:1.992,fill:"#fff"}),j=i.createElement("path",{d:"M59.237 58.571C57.1 78.75 63.509 87.67 79.717 111.787c16.209 24.118-29.091 62.914 5.475 101.268 33.827 37.532 69.419.009 111.314-4.555 29.444-3.208 57.82 12.98 90.86 5.9s46.385-42.599 43.153-68.059c-5.59-44.041-26.24-49.107-34.893-66.461-8.654-17.354 2.902-52.997-30.287-73.16-33.19-20.163-76.71 14.42-112.503 12.37-20.651-1.182-40.932-4.995-59.264.86C75.042 25.867 60.91 42.52 59.237 58.57z",fill:"#B8E6CA"}),S=i.createElement("g",{mask:"url(#key-metrics-no-selected-items_svg__a)"},i.createElement("path",{d:"M227.674 108.973l11.312-8.418M218.925 98.852l2.868-12.68M205.623 102.87l-5.375-13.037",stroke:"#CBD0D3",strokeWidth:3.147,strokeMiterlimit:10}),i.createElement("path",{d:"M63.953 190.487c16.127 12.193 38.716 10.349 55.335 5.162 16.618-5.187 31.107-14.61 45.314-23.791 6.717-4.337 13.617-8.738 21.496-11.119 7.878-2.381 17.057-2.39 22.958 1.658 3.392 2.328 5.205 5.923 5.36 9.702",stroke:"#3C7251",strokeWidth:9.44,strokeLinejoin:"round"}),i.createElement("path",{d:"M215.831 109.67l-19.169 71.73",stroke:"#CBD0D3",strokeWidth:9.44,strokeMiterlimit:10,strokeLinecap:"round"}),i.createElement("path",{d:"M213.975 116.472l-19.169 71.731",stroke:"#161B18",strokeWidth:9.44,strokeMiterlimit:10})),A=i.createElement("defs",null,i.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter0_d_2200_11981",x:205.773,y:35.772,width:176.33,height:147.36,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:3.985}),i.createElement("feGaussianBlur",{stdDeviation:7.969}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})),i.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter1_d_2200_11981",x:.409,y:35.793,width:176.337,height:147.388,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:3.985}),i.createElement("feGaussianBlur",{stdDeviation:7.969}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})),i.createElement("filter",{id:"key-metrics-no-selected-items_svg__filter2_d_2200_11981",x:110.313,y:25.447,width:162.497,height:121.556,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:3.985}),i.createElement("feGaussianBlur",{stdDeviation:7.969}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2200_11981"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2200_11981",result:"shape"})));t.a=function SvgKeyMetricsNoSelectedItems(e){return i.createElement("svg",r({viewBox:"0 0 383 238",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p,h,v,b,E,_,O,y,k,i.createElement("mask",{id:"key-metrics-no-selected-items_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:58,y:0,width:273,height:230},j),S,A)}},37:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"d",(function(){return b})),n.d(t,"f",(function(){return E})),n.d(t,"c",(function(){return _})),n.d(t,"e",(function(){return O})),n.d(t,"b",(function(){return y}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=(n(27),n(9));function l(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var i=0,r=function(){};return{s:r,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){c=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(c)throw a}}}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}var u,d="googlesitekit_",g="".concat(d).concat("1.151.0","_").concat(e._googlesitekitBaseData.storagePrefix,"_"),f=["sessionStorage","localStorage"],m=[].concat(f),p=function(){var t=o()(r.a.mark((function t(n){var i,a;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(i=e[n]){t.next=3;break}return t.abrupt("return",!1);case 3:return t.prev=3,a="__storage_test__",i.setItem(a,a),i.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(3),t.abrupt("return",t.t0 instanceof DOMException&&(22===t.t0.code||1014===t.t0.code||"QuotaExceededError"===t.t0.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.t0.name)&&0!==i.length);case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}();function h(){return v.apply(this,arguments)}function v(){return(v=o()(r.a.mark((function t(){var n,i,a;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===u){t.next=2;break}return t.abrupt("return",u);case 2:n=l(m),t.prev=3,n.s();case 5:if((i=n.n()).done){t.next=15;break}if(a=i.value,!u){t.next=9;break}return t.abrupt("continue",13);case 9:return t.next=11,p(a);case 11:if(!t.sent){t.next=13;break}u=e[a];case 13:t.next=5;break;case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(3),n.e(t.t0);case 20:return t.prev=20,n.f(),t.finish(20);case 23:return void 0===u&&(u=null),t.abrupt("return",u);case 25:case"end":return t.stop()}}),t,null,[[3,17,20,23]])})))).apply(this,arguments)}var b=function(){var e=o()(r.a.mark((function e(t){var n,i,a,o,c,l,s;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h();case 2:if(!(n=e.sent)){e.next=10;break}if(!(i=n.getItem("".concat(g).concat(t)))){e.next=10;break}if(a=JSON.parse(i),o=a.timestamp,c=a.ttl,l=a.value,s=a.isError,!o||c&&!(Math.round(Date.now()/1e3)-o<c)){e.next=10;break}return e.abrupt("return",{cacheHit:!0,value:l,isError:s});case 10:return e.abrupt("return",{cacheHit:!1,value:void 0});case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),E=function(){var t=o()(r.a.mark((function t(n,i){var a,o,l,s,u,d,f,m,p=arguments;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=p.length>2&&void 0!==p[2]?p[2]:{},o=a.ttl,l=void 0===o?c.b:o,s=a.timestamp,u=void 0===s?Math.round(Date.now()/1e3):s,d=a.isError,f=void 0!==d&&d,t.next=3,h();case 3:if(!(m=t.sent)){t.next=14;break}return t.prev=5,m.setItem("".concat(g).concat(n),JSON.stringify({timestamp:u,ttl:l,value:i,isError:f})),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(5),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[5,10]])})));return function(e,n){return t.apply(this,arguments)}}(),_=function(){var t=o()(r.a.mark((function t(n){var i,a;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,h();case 2:if(!(i=t.sent)){t.next=14;break}return t.prev=4,a=n.startsWith(d)?n:"".concat(g).concat(n),i.removeItem(a),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",!1);case 14:return t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(e){return t.apply(this,arguments)}}(),O=function(){var t=o()(r.a.mark((function t(){var n,i,a,o;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,h();case 2:if(!(n=t.sent)){t.next=14;break}for(t.prev=4,i=[],a=0;a<n.length;a++)0===(o=n.key(a)).indexOf(d)&&i.push(o);return t.abrupt("return",i);case 10:return t.prev=10,t.t0=t.catch(4),e.console.warn("Encountered an unexpected storage error:",t.t0),t.abrupt("return",[]);case 14:return t.abrupt("return",[]);case 15:case"end":return t.stop()}}),t,null,[[4,10]])})));return function(){return t.apply(this,arguments)}}(),y=function(){var e=o()(r.a.mark((function e(){var t,n,i,a;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h();case 2:if(!e.sent){e.next=25;break}return e.next=6,O();case 6:t=e.sent,n=l(t),e.prev=8,n.s();case 10:if((i=n.n()).done){e.next=16;break}return a=i.value,e.next=14,_(a);case 14:e.next=10;break;case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(8),n.e(e.t0);case 21:return e.prev=21,n.f(),e.finish(21);case 24:return e.abrupt("return",!0);case 25:return e.abrupt("return",!1);case 26:case"end":return e.stop()}}),e,null,[[8,18,21,24]])})));return function(){return e.apply(this,arguments)}}()}).call(this,n(28))},371:function(e,t,n){"use strict";(function(e,i){var r=n(2),a=n(10),o=n(3),c=n(399),l=n(400),s=n(204),u=n(93),d=n(24),g=n(23),f=n(7),m=n(19),p=n(18),h=n(34),v=n(9),b=n(8),E=n(52),_=n(50);t.a=Object(_.a)({moduleName:"analytics-4"})((function AudienceSegmentationIntroductoryOverlayNotification(){var t=Object(p.a)(),n=Object(h.a)(),_=Object(d.e)(),O=Object(E.c)(),y=Object(o.useSelect)((function(e){return e(f.a).isDismissingItem("audienceSegmentationIntroductoryOverlayNotification")})),k=Object(o.useSelect)((function(e){var t=e(f.a).isItemDismissed("audienceSegmentationIntroductoryOverlayNotification"),i=e(f.a).isAudienceSegmentationWidgetHidden(),r=e(m.a).isModuleActive("analytics-4"),a=!n||e(f.a).canViewSharedModule("analytics-4"),o=e(b.r).getAudienceSegmentationSetupCompletedBy(),c=e(f.a).getID();return E.b===O&&!1===t&&!1===i&&r&&a&&Number.isInteger(o)&&o!==c})),j=Object(o.useDispatch)(g.b).dismissOverlayNotification,S=function(){j("audienceSegmentationIntroductoryOverlayNotification")};return i.createElement(s.a,{shouldShowNotification:k,GraphicDesktop:c.a,GraphicMobile:l.a,notificationID:"audienceSegmentationIntroductoryOverlayNotification",onShow:function(){Object(v.I)("".concat(t,"_audiences-secondary-user-intro"),"view_notification")}},i.createElement("div",{className:"googlesitekit-overlay-notification__body"},i.createElement("h3",null,Object(r.__)("New! Visitor groups","google-site-kit")),i.createElement("p",null,Object(r.__)("You can now learn more about your site visitor groups by comparing different metrics","google-site-kit"))),i.createElement("div",{className:"googlesitekit-overlay-notification__actions"},i.createElement(a.Button,{tertiary:!0,disabled:y,onClick:function(){Object(v.I)("".concat(t,"_audiences-secondary-user-intro"),"dismiss_notification").finally((function(){S()}))}},Object(r.__)("Got it","google-site-kit")),i.createElement(a.Button,{disabled:y,onClick:function(n){n.preventDefault();setTimeout((function(){e.scrollTo({top:Object(u.a)(".googlesitekit-widget-area--mainDashboardTrafficAudienceSegmentation",_),behavior:"smooth"})}),0),Object(v.I)("".concat(t,"_audiences-secondary-user-intro"),"confirm_notification").finally((function(){S()}))}},Object(r.__)("Show me","google-site-kit"))))}))}).call(this,n(28),n(4))},378:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return I}));var i,r=n(21),a=n.n(r),o=n(15),c=n.n(o),l=n(6),s=n.n(l),u=n(1),d=n.n(u),g=n(377),f=n(2),m=n(0),p=n(3),h=n(50),v=n(29),b=n(7),E=n(13),_=n(41),O=n(8),y=n(412),k=n(18),j=n(175),S=n(246),A=n(9),w=n(413),T=n(247),C=n(242),N=n(179),D=n(459),R=n(460),x=n(461),M=n(24),I="audience_segmentation_setup_cta-notification",B=(i={},s()(i,M.b,x.a),s()(i,M.c,R.a),i);function AudienceSegmentationSetupCTAWidget(t){var n,i=t.id,r=t.Notification,o=Object(k.a)(),l=Object(M.e)(),s="".concat(o,"_audiences-setup-cta-dashboard"),u=Object(p.useDispatch)(_.a),d=u.invalidateResolution,g=u.dismissNotification,h=Object(p.useDispatch)(v.a).setValues,S={tooltipSlug:i,title:Object(f.__)("You can always enable groups in Settings later","google-site-kit"),content:Object(f.__)("The visitors group section will be added to your dashboard once you set it up.","google-site-kit"),dismissLabel:Object(f.__)("Got it","google-site-kit")},R=Object(j.b)(S),x=Object(p.useSelect)((function(e){return e(_.a).isNotificationDismissalFinal(i)})),I=Object(p.useSelect)((function(e){return e(v.a).getValue(O.c,"autoSubmit")})),P=Object(m.useState)(!1),L=c()(P,2),F=L[0],z=L[1],W=Object(p.useDispatch)(b.a).dismissItem,V=Object(m.useCallback)((function(){d("getQueuedNotifications",[o,_.c.DEFAULT]),g(i),W(y.a)}),[W,i,d,g,o]),U=Object(m.useCallback)((function(){z(!0)}),[z]),G=Object(w.a)({onSuccess:V,onError:U}),H=G.apiErrors,q=G.failedAudiences,K=G.isSaving,Y=G.onEnableGroups,X=Object(p.useDispatch)(b.a).clearPermissionScopeError,Z=Object(p.useDispatch)(E.c).setSetupErrorCode,$=Object(m.useCallback)((function(){h(O.c,{autoSubmit:!1}),X(),Z(null),z(!1)}),[X,Z,h]),J=Object(p.useSelect)((function(e){return e(E.c).getSetupErrorCode()})),Q=I&&"access_denied"===J,ee={gaTrackingEventArgs:{category:s}};return e.createElement(m.Fragment,null,e.createElement(r,ee,e.createElement(C.a,{id:i,title:Object(f.__)("Learn how different types of visitors interact with your site","google-site-kit"),description:e.createElement("p",null,Object(f.__)("Understand what brings new visitors to your site and keeps them coming back. Site Kit can now group your site visitors into relevant segments like “new“ and “returning“. To set up these new groups, Site Kit needs to update your Google Analytics property.","google-site-kit")),actions:e.createElement(N.a,a()({id:i,className:"googlesitekit-setup-cta-banner__actions-wrapper",ctaLabel:K?Object(f.__)("Enabling groups","google-site-kit"):Object(f.__)("Enable groups","google-site-kit"),onCTAClick:Y,isSaving:K,dismissOnCTAClick:!1,dismissLabel:x?Object(f.__)("Don’t show again","google-site-kit"):Object(f.__)("Maybe later","google-site-kit"),onDismiss:R,ctaDismissOptions:{skipHidingFromQueue:!0},dismissExpires:x?0:2*A.f},ee)),SVG:null!==(n=B[l])&&void 0!==n?n:D.a,primaryCellSizes:{lg:7,md:8},SVGCellSizes:{lg:5}})),(F||Q)&&e.createElement(T.a,{hasOAuthError:Q,apiErrors:H.length?H:q,onRetry:Y,inProgress:K,onCancel:Q?$:function(){return z(!1)},trackEventCategory:"".concat(o,"_audiences-setup")}))}AudienceSegmentationSetupCTAWidget.propTypes={id:d.a.string,Notification:d.a.elementType},t.b=Object(g.a)(Object(h.a)({moduleName:"analytics-4"}),Object(S.g)("audienceSegmentationSetupCTA"))(AudienceSegmentationSetupCTAWidget)}).call(this,n(4))},379:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsCTAContent}));var i=n(15),r=n.n(i),a=n(11),o=n.n(a),c=n(1),l=n.n(c),s=n(240),u=n(0),d=n(3),g=n(13),f=n(7),m=n(24),p=n(9),h=n(18),v=n(17),b=n(450),E=n(451),_=n(452),O=n(453),y=n(79);function KeyMetricsCTAContent(t){var n=t.className,i=t.title,a=t.description,c=t.actions,l=t.ga4Connected,k=Object(u.useRef)(),j=Object(m.e)(),S=Object(y.a)(),A=Object(h.a)(),w=j===m.b,T=j===m.c&&S<960,C=S>=1280,N=S>=960&&S<1280;l||(T=j===m.c&&S<800,N=S>=800&&S<1280);var D=Object(s.a)(k,{threshold:.25}),R=Object(u.useState)(!1),x=r()(R,2),M=x[0],I=x[1],B=!!(null==D?void 0:D.intersectionRatio),P=Object(d.useDispatch)(f.a).triggerSurvey,L=Object(d.useSelect)((function(e){return e(g.c).isUsingProxy()}));return Object(u.useEffect)((function(){B&&!M&&(l&&Object(p.I)("".concat(A,"_kmw-cta-notification"),"view_notification"),L&&P("view_kmw_setup_cta",{ttl:p.f}),I(!0))}),[B,A,l,M,L,P]),e.createElement("section",{ref:k,className:o()("googlesitekit-setup__wrapper","googlesitekit-setup__wrapper--key-metrics-setup-cta",n)},e.createElement(v.e,null,e.createElement(v.k,null,e.createElement(v.a,{smSize:5,mdSize:6,lgSize:5,className:"googlesitekit-widget-key-metrics-content__wrapper"},e.createElement("div",{className:"googlesitekit-widget-key-metrics-text__wrapper"},e.createElement("h3",{className:"googlesitekit-publisher-win__title"},i),e.createElement("p",null,a)),e.createElement("div",{className:"googlesitekit-widget-key-metrics-actions__wrapper"},c),T&&e.createElement(v.a,{className:"googlesitekit-widget-key-metrics-svg__wrapper"},e.createElement(_.a,null)),w&&e.createElement(v.a,{className:"googlesitekit-widget-key-metrics-svg__wrapper"},e.createElement(O.a,null))),N&&e.createElement(v.a,{className:"googlesitekit-widget-key-metrics-svg__wrapper"},e.createElement(E.a,null)),C&&e.createElement(v.a,{className:"googlesitekit-widget-key-metrics-svg__wrapper",smSize:6,mdSize:3,lgSize:6},e.createElement(b.a,null)))))}KeyMetricsCTAContent.propTypes={title:l.a.string,description:l.a.string,actions:l.a.node}}).call(this,n(4))},380:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsCTAFooter}));var i=n(1),r=n.n(i),a=n(2),o=n(17),c=n(20);function KeyMetricsCTAFooter(t){var n=t.onActionClick,i=void 0===n?function(){}:n,r=t.showDismiss;return e.createElement(o.k,{className:"googlesitekit-widget-key-metrics-footer"},e.createElement(o.a,{size:12,className:"googlesitekit-widget-key-metrics-footer__cta-wrapper"},!r&&e.createElement("span",null,Object(a.__)("Interested in specific metrics?","google-site-kit")),e.createElement(c.a,{onClick:i},r?Object(a.__)("Maybe later","google-site-kit"):Object(a.__)("Select your own metrics","google-site-kit"))))}KeyMetricsCTAFooter.propTypes={onActionClick:r.a.func}}).call(this,n(4))},381:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileHeader}));var i=n(1),r=n.n(i),a=n(132),o=n(105);function MetricTileHeader(t){var n=t.title,i=t.infoTooltip,r=t.loading;return e.createElement("div",{className:"googlesitekit-km-widget-tile__title-container"},e.createElement("h3",{className:"googlesitekit-km-widget-tile__title"},n),r?e.createElement(o.a,null,e.createElement(a.a,{title:i})):e.createElement(a.a,{title:i}))}MetricTileHeader.propTypes={title:r.a.string,infoTooltip:r.a.oneOfType([r.a.string,r.a.element]),loading:r.a.bool}}).call(this,n(4))},382:function(e,t,n){"use strict";function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,n=e.filter((function(e){var t=e.dimensionValues;return"(not set)"!==t[0].value&&""!==t[0].value}));return n.slice(0,t)}n.d(t,"a",(function(){return i}))},384:function(e,t,n){"use strict";var i=n(247);n.d(t,"b",(function(){return i.a}));n(203),n(371),n(378);var r=n(440);n.d(t,"d",(function(){return r.a}));var a=n(441);n.d(t,"c",(function(){return a.a}));var o=n(442);n.d(t,"a",(function(){return o.a}));var c=n(443);n.d(t,"e",(function(){return c.a}));var l=n(444);n.d(t,"f",(function(){return l.a}));var s=n(445);n.d(t,"g",(function(){return s.a}));n(248),n(343);var u=n(446);n.d(t,"h",(function(){return u.a}))},385:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTooltipMessage}));var i=n(1),r=n.n(i),a=n(0),o=n(38),c=n(2),l=n(3),s=n(20),u=n(13);function AudienceTooltipMessage(t){var n=t.audienceSlug,i=t.audienceName,r=Object(l.useSelect)((function(e){return e(u.c).getDocumentationLinkURL("visitor-group-insights")}));return Object(a.useMemo)((function(){switch(n){case"new-visitors":return Object(o.a)(Object(c.sprintf)(
/* translators: %s: is the audience name */
Object(c.__)('%s are people who visited your site for the first time. Note that under some circumstances it\'s possible for a visitor to be counted in both the "new" and "returning" groups. <link>Learn more</link>',"google-site-kit"),"<strong>New visitors</strong>"),{strong:e.createElement("strong",null),link:e.createElement(s.a,{href:r,external:!0,hideExternalIndicator:!0})});case"returning-visitors":return Object(o.a)(Object(c.sprintf)(
/* translators: %s: is the audience name */
Object(c.__)('%s are people who have visited your site at least once before. Note that under some circumstances it\'s possible for a visitor to be counted in both the "new" and "returning" groups. <link>Learn more</link>',"google-site-kit"),"<strong>Returning visitors</strong>"),{strong:e.createElement("strong",null),link:e.createElement(s.a,{href:r,external:!0,hideExternalIndicator:!0})});default:return Object(o.a)(Object(c.sprintf)(
/* translators: %s: is the audience name */
Object(c.__)("%s is an audience that already exists in your Analytics property. Note that it's possible for a visitor to be counted in more than one group. <link>Learn more</link>","google-site-kit"),"<strong>".concat(i,"</strong>")),{strong:e.createElement("strong",null),link:e.createElement(s.a,{href:r,external:!0,hideExternalIndicator:!0})})}}),[n,i,r])}AudienceTooltipMessage.propTypes={audienceSlug:r.a.string.isRequired}}).call(this,n(4))},386:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RetryButton}));var i=n(1),r=n.n(i),a=n(2),o=n(10),c=n(9),l=n(18);function RetryButton(t){var n=t.handleRetry,i=Object(l.a)();return e.createElement(o.Button,{className:"googlesitekit-audience-selection-panel__error-notice-action",onClick:function(){n(),Object(c.I)("".concat(i,"_audiences-sidebar"),"data_loading_error_retry")},tertiary:!0},Object(a.__)("Retry","google-site-kit"))}RetryButton.propTypes={handleRetry:r.a.func.isRequired}}).call(this,n(4))},39:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return r}));var i="_googlesitekitDataLayer",r="data-googlesitekit-gtag"},397:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n(3),r=n(19),a=n(7),o=n(26),c=n(8),l=n(66);function s(){return Object(i.useSelect)((function(e){var t=e(a.a).isItemDismissed(o.l),n=e(a.a).isDismissingItem(o.l),i=u(e,"search-console",l.b),r=u(e,"analytics-4",c.r);return!1===t&&!1===n&&i&&r}),[])}function u(e,t,n){if(e(r.a).isModuleConnected(t)){var i=e(n),a=i.isGatheringData,o=i.isDataAvailableOnLoad;return a(),o()}}},398:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var i=n(0),r=n(3),a=n(7),o=n(19),c=n(32);function l(e){var t=Object(r.useSelect)((function(e){return e(a.a).hasCapability(a.K)})),n=Object(r.useSelect)((function(t){return t(o.a).getModuleStoreName(e)})),l=Object(r.useSelect)((function(e){var t;return null===(t=e(n))||void 0===t?void 0:t.getAdminReauthURL()})),s=Object(r.useDispatch)(c.a).navigateTo,u=Object(i.useCallback)((function(){return s(l)}),[l,s]);return l&&t?u:null}},399:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{clipPath:"url(#audience-segmentation-introductory-graphic-desktop_svg__clip0_1395_20972)"},i.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h264c8.837 0 16 7.163 16 16v147H0V16z",fill:"#B8E6CA"}),i.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-desktop_svg__filter0_d_1395_20972)"},i.createElement("rect",{x:-10,y:25,width:153,height:174,rx:11,fill:"#fff"})),i.createElement("rect",{x:9.031,y:110.641,width:53.016,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:9.031,y:95.688,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:9.031,y:148.703,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("path",{d:"M94.672 108.602a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 010 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),i.createElement("rect",{x:9,y:46,width:36,height:9,rx:4.5,fill:"#EBEEF0"}),i.createElement("path",{d:"M94.672 161.617a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 110 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#FFDED3"}),i.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-desktop_svg__filter1_d_1395_20972)"},i.createElement("rect",{x:152,y:25,width:153,height:174,rx:11,fill:"#fff"})),i.createElement("rect",{x:170.955,y:110.641,width:52.805,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:170.955,y:95.688,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:170.955,y:148.703,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("path",{d:"M256.256 108.602a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),i.createElement("rect",{x:171,y:46,width:36,height:9,rx:4.5,fill:"#EBEEF0"}),i.createElement("path",{d:"M295 73.5H152",stroke:"#EBEEF0",strokeWidth:2}),i.createElement("path",{d:"M256.256 161.617a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836z",fill:"#FFDED3"}),i.createElement("path",{d:"M143 73.5H0",stroke:"#EBEEF0",strokeWidth:2})),o=i.createElement("defs",null,i.createElement("filter",{id:"audience-segmentation-introductory-graphic-desktop_svg__filter0_d_1395_20972",x:-26,y:13,width:185,height:206,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1395_20972"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1395_20972",result:"shape"})),i.createElement("filter",{id:"audience-segmentation-introductory-graphic-desktop_svg__filter1_d_1395_20972",x:136,y:13,width:185,height:206,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1395_20972"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1395_20972",result:"shape"})),i.createElement("clipPath",{id:"audience-segmentation-introductory-graphic-desktop_svg__clip0_1395_20972"},i.createElement("path",{d:"M0 16C0 7.163 7.163 0 16 0h264c8.837 0 16 7.163 16 16v147H0V16z",fill:"#fff"})));t.a=function SvgAudienceSegmentationIntroductoryGraphicDesktop(e){return i.createElement("svg",r({viewBox:"0 0 296 163",fill:"none"},e),a,o)}},40:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"i",(function(){return r})),n.d(t,"h",(function(){return a})),n.d(t,"f",(function(){return o})),n.d(t,"g",(function(){return c})),n.d(t,"d",(function(){return l})),n.d(t,"e",(function(){return s})),n.d(t,"k",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"b",(function(){return g})),n.d(t,"c",(function(){return f}));var i="audience-segmentation-add-group-notice",r="googlesitekit-audience-selection-panel-opened",a="audience-selection-form",o="audience-selected",c="audience-selection-changed",l="audience-segmentation-creation-notice",s="audience-segmentation-creation-success-notice",u=1,d=3,g="audience-creation-edit-scope-notice",f="audience-creation-form"},400:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M41.064 27.146a64.127 64.127 0 016.51-5.868C69.418 4.126 87.464 4.153 112.45 7.283c16.891 2.116 26.759 10.166 49.788 8.9 23.029-1.266 28.929-7.127 57.117-5.25 22.315 1.487 32.324 5.897 52.163 16.213 18.36 9.549 35.031 26.324 43.408 48.509 14.361 38.026-11.243 106.466-45.58 109.693-24.881 2.339-45.414-25.243-70.527-18.855-15.47 3.936-24.646 20.444-36.581 31.339-13.925 12.711-43.922 11.912-60.227 5.129-15.538-6.464-30.653-19.276-35.728-38.145-3.863-14.369-4.916-31.498-15.733-44.622-13.09-15.883-21.087-22.968-25.581-44.54-3.903-18.734 4.494-36.505 16.095-48.508z",fill:"#B8E6CA"}),o=i.createElement("path",{d:"M41.064 27.146a64.127 64.127 0 016.51-5.868C69.418 4.126 87.464 4.153 112.45 7.283c16.891 2.116 26.759 10.166 49.788 8.9 23.029-1.266 28.929-7.127 57.117-5.25 22.315 1.487 32.324 5.897 52.163 16.213 18.36 9.549 35.031 26.324 43.408 48.509 14.361 38.026-11.243 106.466-45.58 109.693-24.881 2.339-45.414-25.243-70.527-18.855-15.47 3.936-24.646 20.444-36.581 31.339-13.925 12.711-43.922 11.912-60.227 5.129-15.538-6.464-30.653-19.276-35.728-38.145-3.863-14.369-4.916-31.498-15.733-44.622-13.09-15.883-21.087-22.968-25.581-44.54-3.903-18.734 4.494-36.505 16.095-48.508z",fill:"#B8E6CA"}),c=i.createElement("g",{mask:"url(#audience-segmentation-introductory-graphic-mobile_svg__a)"},i.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-mobile_svg__filter0_d_2898_16651)"},i.createElement("rect",{x:71.449,y:21.433,width:100.401,height:136.493,rx:7.218,fill:"#fff"})),i.createElement("rect",{x:83.941,y:77.631,width:34.79,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("rect",{x:83.941,y:67.819,width:12.489,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("rect",{x:83.941,y:99.983,width:12.489,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("path",{d:"M140.133 76.293a5.798 5.798 0 015.798-5.798h8.921a5.798 5.798 0 010 11.596h-8.921a5.798 5.798 0 01-5.798-5.798z",fill:"#B8E6CA"}),i.createElement("rect",{x:83.926,y:35.213,width:23.624,height:5.906,rx:2.953,fill:"#EBEEF0"}),i.createElement("path",{d:"M140.133 108.458a5.798 5.798 0 015.798-5.798h8.921a5.798 5.798 0 010 11.597h-8.921a5.798 5.798 0 01-5.798-5.799z",fill:"#FFDED3"}),i.createElement("rect",{x:83.043,y:109.796,width:36.574,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("path",{d:"M171.848 53.259H72.103",stroke:"#EBEEF0",strokeWidth:1.312}),i.createElement("g",{filter:"url(#audience-segmentation-introductory-graphic-mobile_svg__filter1_d_2898_16651)"},i.createElement("rect",{x:184.973,y:21.433,width:100.401,height:136.493,rx:7.218,fill:"#fff"})),i.createElement("rect",{x:197.414,y:77.631,width:34.652,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("rect",{x:197.414,y:67.819,width:12.439,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("rect",{x:197.41,y:99.983,width:12.439,height:6.244,rx:3.122,fill:"#EBEEF0"}),i.createElement("path",{d:"M253.391 76.293a5.798 5.798 0 015.798-5.798h8.839a5.798 5.798 0 010 11.596h-8.839a5.798 5.798 0 01-5.798-5.798z",fill:"#B8E6CA"}),i.createElement("rect",{x:197.449,y:35.213,width:23.624,height:5.906,rx:2.953,fill:"#EBEEF0"}),i.createElement("path",{d:"M278.82 53.259h-93.838",stroke:"#EBEEF0",strokeWidth:1.312}),i.createElement("path",{d:"M253.391 108.458a5.798 5.798 0 015.798-5.798h8.839a5.798 5.798 0 010 11.597h-8.839a5.798 5.798 0 01-5.798-5.799z",fill:"#FFDED3"}),i.createElement("rect",{x:196.523,y:109.796,width:36.429,height:6.244,rx:3.122,fill:"#EBEEF0"})),l=i.createElement("defs",null,i.createElement("filter",{id:"audience-segmentation-introductory-graphic-mobile_svg__filter0_d_2898_16651",x:55.449,y:9.433,width:132.402,height:168.493,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2898_16651"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2898_16651",result:"shape"})),i.createElement("filter",{id:"audience-segmentation-introductory-graphic-mobile_svg__filter1_d_2898_16651",x:168.973,y:9.433,width:132.402,height:168.493,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2898_16651"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2898_16651",result:"shape"})),i.createElement("clipPath",{id:"audience-segmentation-introductory-graphic-mobile_svg__clip0_2898_16651"},i.createElement("path",{fill:"#fff",d:"M0 0h343v128H0z"})));t.a=function SvgAudienceSegmentationIntroductoryGraphicMobile(e){return i.createElement("svg",r({viewBox:"0 0 343 123",fill:"none"},e),i.createElement("g",{clipPath:"url(#audience-segmentation-introductory-graphic-mobile_svg__clip0_2898_16651)"},a,i.createElement("mask",{id:"audience-segmentation-introductory-graphic-mobile_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:24,y:5,width:295,height:203},o),c),l)}},41:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var i=n(22),r="core/notifications",a={ERRORS:"notification-area-errors",BANNERS_ABOVE_NAV:"notification-area-banners-above-nav",BANNERS_BELOW_NAV:"notification-area-banners-below-nav"},o={DEFAULT:"default",SETUP_CTAS:"setup-ctas"},c=[i.s,i.n,i.l,i.o,i.m]},412:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return SetupSuccess}));var i=n(0),r=n(3),a=n(7),o=n(18),c=n(113),l=n(9),s=n(458),u=Object(c.a)(s.a),d="settings_visitor_groups_setup_success_notification";function SetupSuccess(){var t=Object(o.a)(),n=Object(r.useDispatch)(a.a).dismissItem,c=Object(r.useSelect)((function(e){return e(a.a).isAudienceSegmentationWidgetHidden()})),s=Object(r.useSelect)((function(e){return e(a.a).isItemDismissed(d)})),g=c&&!1===s;return Object(i.useEffect)((function(){g&&n(d)}),[n,g]),void 0===s||s||g?null:e.createElement(u,{onInView:function(){Object(l.I)("".concat(t,"_audiences-setup-cta-settings-success"),"view_notification")}})}}).call(this,n(4))},413:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return b}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(206),u=n(2),d=n(0),g=n(157),f=n(3),m=n(29),p=n(7),h=n(35),v=n(8);function b(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.redirectURL,i=t.onSuccess,a=t.onError,c=Object(s.a)(),b=Object(d.useState)([]),E=l()(b,2),_=E[0],O=E[1],y=Object(d.useState)([]),k=l()(y,2),j=k[0],S=k[1],A=Object(d.useState)(!1),w=l()(A,2),T=w[0],C=w[1],N=Object(f.useSelect)((function(e){return e(p.a).hasScope(v.h)})),D=Object(f.useSelect)((function(e){return e(m.a).getValue(v.c,"autoSubmit")})),R=Object(f.useDispatch)(m.a),x=R.setValues,M=Object(f.useDispatch)(p.a),I=M.setPermissionScopeError,B=Object(f.useDispatch)(v.r),P=B.enableAudienceGroup,L=B.fetchSyncAvailableCustomDimensions,F=B.determineNeedForAnalytics4EditScope,z=B.syncAvailableAudiences;n||(n=Object(g.a)(e.location.href,{notification:"audience_segmentation"}));var W=Object(d.useCallback)(o()(r.a.mark((function e(){var t,n,i,a,o,c,l,s,u,d;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,z();case 2:if(t=e.sent,!(n=t.error)){e.next=6;break}return e.abrupt("return",{error:n});case 6:return e.next=8,L();case 8:if(i=e.sent,!(a=i.error)){e.next=12;break}return e.abrupt("return",{error:a});case 12:if(N){e.next=24;break}return e.next=15,F();case 15:if(o=e.sent,c=o.error,l=o.needsScope,!c){e.next=22;break}return e.abrupt("return",{error:c});case 22:if(!l){e.next=24;break}return e.abrupt("return",{needsScope:!0});case 24:return x(v.c,{autoSubmit:!1}),e.next=27,P(j);case 27:if(e.t0=e.sent,e.t0){e.next=30;break}e.t0={};case 30:return s=e.t0,u=s.error,d=s.failedSiteKitAudienceSlugs,e.abrupt("return",{error:u,failedSiteKitAudienceSlugs:d});case 34:case"end":return e.stop()}}),e)}))),[P,j,L,N,F,x,z]),V=Object(d.useCallback)(o()(r.a.mark((function t(){var o,l,s,d,g;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return C(!0),t.next=3,W();case 3:if(o=t.sent,l=o.error,s=o.needsScope,d=o.failedSiteKitAudienceSlugs,!s){t.next=11;break}return x(v.c,{autoSubmit:!0}),I({code:h.a,message:Object(u.__)("Additional permissions are required to create new audiences in Analytics.","google-site-kit"),data:{status:403,scopes:[v.h],skipModal:!0,skipDefaultErrorNotifications:!0,redirectURL:n,errorRedirectURL:e.location.href}}),t.abrupt("return");case 11:l||d?null==a||a():null==i||i(),c()&&(g=function _newArrayIfNotEmpty(e){return e.length?[]:e},l?(O([l]),S(g)):Array.isArray(d)?(S(d),O(g)):(O(g),S(g)),C(!1));case 13:case"end":return t.stop()}}),t)}))),[W,c,x,I,n,a,i]);return Object(d.useEffect)((function(){N&&D&&V()}),[N,D,V]),{apiErrors:_,failedAudiences:j,isSaving:T,onEnableGroups:V}}}).call(this,n(28))},434:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeMetricsLink}));var i=n(0),r=n(2),a=n(3),o=n(23),c=n(7),l=n(26),s=n(20),u=n(310),d=n(449),g=n(9),f=n(18),m=n(511);function ChangeMetricsLink(){var t=Object(a.useSelect)((function(e){return e(c.a).getKeyMetrics()})),n=Object(f.a)(),p=Object(a.useDispatch)(o.b).setValue,h=Object(i.useCallback)((function(){p(l.k,!0),Object(g.I)("".concat(n,"_kmw"),"change_metrics")}),[p,n]),v=Array.isArray(t)&&(null==t?void 0:t.length)>0;return Object(m.a)(v),v?e.createElement(i.Fragment,null,e.createElement(s.a,{secondary:!0,linkButton:!0,className:"googlesitekit-widget-area__cta-link googlesitekit-km-change-metrics-cta",onClick:h,leadingIcon:e.createElement(u.a,{width:22,height:22})},Object(r.__)("Change metrics","google-site-kit")),e.createElement(d.a,null)):null}}).call(this,n(4))},435:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return InsufficientPermissionsError}));var i=n(1),r=n.n(i),a=n(0),o=n(38),c=n(2),l=n(3),s=n(13),u=n(20),d=n(303),g=n(9),f=n(18);function InsufficientPermissionsError(t){var n=t.moduleSlug,i=t.onRetry,r=t.infoTooltip,m=t.headerText,p=Object(f.a)(),h=Object(l.useSelect)((function(e){return e(s.c).getErrorTroubleshootingLinkURL({code:"".concat(n,"_insufficient_permissions")})}));Object(a.useEffect)((function(){Object(g.J)("".concat(p,"_kmw"),"insufficient_permissions_error")}),[p]);var v=Object(a.useCallback)((function(){Object(g.I)("".concat(p,"_kmw"),"insufficient_permissions_error_retry"),null==i||i()}),[i,p]);return e.createElement(d.a,{title:Object(c.__)("Insufficient permissions","google-site-kit"),headerText:m,infoTooltip:r},e.createElement("div",{className:"googlesitekit-report-error-actions"},e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(o.a)(Object(c.__)("Permissions updated? <a>Retry</a>","google-site-kit"),{a:e.createElement(u.a,{onClick:v})})),e.createElement("span",{className:"googlesitekit-error-retry-text"},Object(o.a)(Object(c.__)("You’ll need to contact your administrator. <a>Learn more</a>","google-site-kit"),{a:e.createElement(u.a,{href:h,external:!0,hideExternalIndicator:!0})}))))}InsufficientPermissionsError.propTypes={moduleSlug:r.a.string.isRequired,onRetry:r.a.func.isRequired,headerText:r.a.string,infoTooltip:r.a.string}}).call(this,n(4))},436:function(e,t,n){"use strict";(function(e){var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(1),l=n.n(c),s=n(2),u=n(0),d=n(3),g=n(10),f=n(379),m=n(380),p=n(7),h=n(13),v=n(26),b=n(50),E=n(175),_=n(9),O=n(18),y=n(397),k=n(454),j=n(32);function KeyMetricsSetupCTAWidget(t){var n=t.Widget,i=t.WidgetNull,a=Object(O.a)(),c=Object(y.a)(),l=Object(d.useSelect)((function(e){return e(h.c).getAdminURL("googlesitekit-user-input")})),b=Object(d.useSelect)((function(e){return e(h.c).getAdminURL("googlesitekit-metric-selection")})),S={tooltipSlug:v.l,title:Object(s.__)("You can always set up goals in Settings later","google-site-kit"),content:Object(s.__)("The Key Metrics section will be added back to your dashboard once you set your goals in Settings","google-site-kit"),dismissLabel:Object(s.__)("Got it","google-site-kit")},A=Object(E.b)(S),w=Object(d.useDispatch)(p.a).dismissItem,T=function(){var e=o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(_.I)("".concat(a,"_kmw-cta-notification"),"dismiss_notification");case 2:return A(),e.next=5,w(v.l);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),C=Object(d.useDispatch)(j.a).navigateTo,N=Object(u.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(_.I)("".concat(a,"_kmw-cta-notification"),"confirm_pick_own_metrics");case 2:C(b);case 3:case"end":return e.stop()}}),e)}))),[C,b,a]),D=Object(u.useCallback)((function(){Object(_.I)("".concat(a,"_kmw-cta-notification"),"confirm_get_tailored_metrics")}),[a]);return c?e.createElement(n,{noPadding:!0,Footer:function Footer(){return e.createElement(m.a,{onActionClick:N})}},e.createElement(f.a,{title:Object(s.__)("Get personalized suggestions for user interaction metrics based on your goals","google-site-kit"),description:Object(s.__)("Answer 3 questions and we’ll suggest relevant metrics for your dashboard. These metrics will help you track how users interact with your site.","google-site-kit"),actions:e.createElement(u.Fragment,null,e.createElement(k.a,null),e.createElement(g.Button,{className:"googlesitekit-key-metrics-cta-button",href:l,onClick:D},Object(s.__)("Get tailored metrics","google-site-kit")),e.createElement(g.Button,{tertiary:!0,onClick:T},Object(s.__)("Maybe later","google-site-kit"))),ga4Connected:!0})):e.createElement(i,null)}KeyMetricsSetupCTAWidget.propTypes={Widget:l.a.elementType.isRequired,WidgetNull:l.a.elementType},t.a=Object(b.a)({moduleName:"analytics-4"})(KeyMetricsSetupCTAWidget)}).call(this,n(4))},437:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileNumeric}));var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(9),u=n(184),d=n(202);function MetricTileNumeric(t){var n=t.metricValue,i=t.metricValueFormat,a=t.subText,c=t.previousValue,l=t.currentValue,g=o()(t,["metricValue","metricValueFormat","subText","previousValue","currentValue"]),f=Object(s.m)(i);return e.createElement(d.a,r()({className:"googlesitekit-km-widget-tile--numeric"},g),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-container"},e.createElement("div",{className:"googlesitekit-km-widget-tile__metric"},Object(s.B)(n,f)),e.createElement("p",{className:"googlesitekit-km-widget-tile__subtext"},a)),e.createElement("div",{className:"googlesitekit-km-widget-tile__metric-change-container"},e.createElement(u.a,{previousValue:c,currentValue:l,isAbsolute:"percent"===(null==f?void 0:f.style)})))}MetricTileNumeric.propTypes={metricValue:l.a.oneOfType([l.a.string,l.a.number]),metricValueFormat:l.a.oneOfType([l.a.string,l.a.object]),subtext:l.a.string,previousValue:l.a.number,currentValue:l.a.number}}).call(this,n(4))},438:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileTable}));var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(1),l=n.n(c),s=n(14),u=n(11),d=n.n(u),g=n(202);function MetricTileTable(t){var n=t.rows,i=void 0===n?[]:n,a=t.columns,c=void 0===a?[]:a,l=t.limit,u=t.ZeroState,f=o()(t,["rows","columns","limit","ZeroState"]),m=null;return(null==i?void 0:i.length)>0?m=i.slice(0,l||i.length).map((function(t,n){return e.createElement("div",{key:n,className:"googlesitekit-table__body-row"},c.map((function(n,i){var r=n.Component,a=n.field,o=n.className,c=void 0!==a?Object(s.get)(t,a):void 0;return e.createElement("div",{key:i,className:d()("googlesitekit-table__body-item",o)},r&&e.createElement(r,{row:t,fieldValue:c}),!r&&c)})))})):u&&(m=e.createElement("div",{className:"googlesitekit-table__body-row googlesitekit-table__body-row--no-data"},e.createElement("div",{className:"googlesitekit-table__body-zero-data"},e.createElement(u,null)))),e.createElement(g.a,r()({className:"googlesitekit-km-widget-tile--table"},f),e.createElement("div",{className:"googlesitekit-km-widget-tile__table"},m))}MetricTileTable.propTypes={rows:l.a.array,columns:l.a.array,limit:l.a.number,ZeroState:l.a.elementType}}).call(this,n(4))},439:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileTablePlainText}));var i=n(1),r=n.n(i);function MetricTileTablePlainText(t){var n=t.content;return e.createElement("p",{className:"googlesitekit-km-widget-tile__table-plain-text"},n)}MetricTileTablePlainText.propTypes={content:r.a.string.isRequired}}).call(this,n(4))},44:function(e,t,n){"use strict";(function(e){var i=n(6),r=n.n(i),a=n(1),o=n.n(a),c=n(11),l=n.n(c),s=n(24);function PreviewBlock(t){var n,i,a=t.className,o=t.width,c=t.height,u=t.shape,d=t.padding,g=t.smallWidth,f=t.smallHeight,m=t.tabletWidth,p=t.tabletHeight,h=t.desktopWidth,v=t.desktopHeight,b=Object(s.e)(),E={width:(n={},r()(n,s.b,g),r()(n,s.c,m),r()(n,s.a,h),r()(n,s.d,h),n),height:(i={},r()(i,s.b,f),r()(i,s.c,p),r()(i,s.a,v),r()(i,s.d,h),i)};return e.createElement("div",{className:l()("googlesitekit-preview-block",a,{"googlesitekit-preview-block--padding":d}),style:{width:E.width[b]||o,height:E.height[b]||c}},e.createElement("div",{className:l()("googlesitekit-preview-block__wrapper",{"googlesitekit-preview-block__wrapper--circle":"circular"===u})}))}PreviewBlock.propTypes={className:o.a.string,width:o.a.string,height:o.a.string,shape:o.a.string,padding:o.a.bool,smallWidth:o.a.string,smallHeight:o.a.string,tabletWidth:o.a.string,tabletHeight:o.a.string,desktopWidth:o.a.string,desktopHeight:o.a.string},PreviewBlock.defaultProps={className:void 0,width:"100px",height:"100px",shape:"square",padding:!1,smallWidth:void 0,smallHeight:void 0,tabletWidth:void 0,tabletHeight:void 0,desktopWidth:void 0,desktopHeight:void 0},t.a=PreviewBlock}).call(this,n(4))},440:function(e,t,n){"use strict";(function(e){var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(1),u=n.n(s),d=n(0),g=n(3),f=n(50),m=n(8),p=n(7),h=n(462),v=n(177),b=n(203),E=n(343),_=n(244),O=n(35);function AudienceTilesWidget(t){var n=t.Widget,i=Object(g.useSelect)((function(e){var t=e(m.r).getAvailableAudiences();return null==t?void 0:t.map((function(e){return e.name}))})),a=Object(g.useSelect)((function(e){return e(p.a).getConfiguredAudiences()})),c=Object(d.useState)(!1),s=l()(c,2),u=s[0],f=s[1],y=Object(g.useDispatch)(m.r),k=y.clearErrors,j=y.maybeSyncAvailableAudiences,S=y.syncAvailableAudiences,A=Object(g.useSelect)((function(e){return e(m.r).isSettingUpAudiences()})),w=Object(g.useSelect)((function(e){return e(m.r).getErrorForAction("syncAvailableAudiences")}));if(Object(d.useEffect)((function(){u||A||function(){var e=o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,j();case 2:f(!0);case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()()}),[u,A,j]),w){var T=Object(O.e)(w);return e.createElement(b.a,{errors:w,Widget:n,onRetry:T?void 0:o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,k("syncAvailableAudiences");case 2:return e.next=4,S();case 4:case"end":return e.stop()}}),e)}))),showRetryButton:!T})}return(null==a?void 0:a.some((function(e){return null==i?void 0:i.includes(e)})))?e.createElement(h.a,{Widget:n,widgetLoading:!u||!i||!a}):u?e.createElement(E.a,{Widget:n,WidgetNull:_.a}):e.createElement(n,{className:"googlesitekit-widget-audience-tiles",noPadding:!0},e.createElement("div",{className:"googlesitekit-widget-audience-tiles__body"},e.createElement(n,{noPadding:!0},e.createElement(v.a,null)),e.createElement(n,{noPadding:!0},e.createElement(v.a,null))))}AudienceTilesWidget.propTypes={Widget:u.a.elementType.isRequired,WidgetNull:u.a.elementType.isRequired},t.a=Object(f.a)({moduleName:"analytics-4"})(AudienceTilesWidget)}).call(this,n(4))},441:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceSelectionPanel}));var i=n(3),r=n(40),a=n(23),o=n(160),c=n(487);function AudienceSelectionPanel(){var t=Object(i.useSelect)((function(e){return e(a.b).getValue(r.i)}));return e.createElement(o.a,{value:{key:"AudienceSelectionPanel",value:!!t}},e.createElement(c.a,null))}}).call(this,n(4))},442:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceAreaFooter}));var i=n(2),r=n(3),a=n(7),o=n(8),c=n(19),l=n(134),s=n(34);function AudienceAreaFooter(){var t=Object(s.a)(),n=Object(r.useSelect)((function(e){return e(a.a).getDateRangeDates({offsetDays:o.g})})),u=Object(r.useSelect)((function(e){return t?null:e(o.r).getServiceReportURL("audiences",{dates:n})}));return Object(r.useSelect)((function(e){return e(c.a).isModuleConnected("analytics-4")}))?e.createElement(l.a,{className:"googlesitekit-audience-widget__source",name:Object(i._x)("Analytics","Service name","google-site-kit"),href:u,external:!0}):null}}).call(this,n(4))},443:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ChangeGroupsLink}));var i=n(0),r=n(2),a=n(3),o=n(18),c=n(9),l=n(40),s=n(23),u=n(8),d=n(20),g=n(310);function ChangeGroupsLink(){var t=Object(o.a)(),n=Object(a.useInViewSelect)((function(e){return e(u.r).getConfigurableAudiences()}),[]),f=Object(a.useDispatch)(s.b).setValue,m=Object(i.useCallback)((function(){f(l.i,!0),Object(c.I)("".concat(t,"_audiences-sidebar"),"change_groups")}),[f,t]);return Array.isArray(n)&&(null==n?void 0:n.length)>0?e.createElement(i.Fragment,null,e.createElement(d.a,{secondary:!0,linkButton:!0,className:"googlesitekit-widget-area__cta-link",onClick:m,leadingIcon:e.createElement(g.a,{width:22,height:22})},Object(r.__)("Change groups","google-site-kit"))):null}}).call(this,n(4))},444:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConnectAnalyticsCTAWidget}));var i=n(1),r=n.n(i),a=n(38),o=n(0),c=n(2),l=n(3),s=n(501),u=n(502),d=n(20),g=n(19),f=n(159),m=n(24),p=n(350);function ConnectAnalyticsCTAWidget(t){var n=t.Widget,i=Object(m.e)()===m.c,r=Object(f.a)("analytics-4"),h=Object(l.useSelect)((function(e){return e(g.a).getModuleIcon("analytics-4")})),v=i?e.createElement("p",null,Object(a.a)(Object(c.__)("Google Analytics is disconnected, your audience metrics can’t be displayed. <a>Connect Google Analytics</a>","google-site-kit"),{a:e.createElement(d.a,{secondary:!0,onClick:r})})):e.createElement(o.Fragment,null,e.createElement("p",null,Object(c.__)("Google Analytics is disconnected, your audience metrics can’t be displayed","google-site-kit")),e.createElement(d.a,{secondary:!0,onClick:r},Object(c.__)("Connect Google Analytics","google-site-kit")));return e.createElement(n,{noPadding:!0},e.createElement(p.a,{Icon:h,SVGGraphic:i?u.a:s.a},v))}ConnectAnalyticsCTAWidget.propTypes={Widget:r.a.elementType.isRequired}}).call(this,n(4))},445:function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(0),o=n(2),c=n(3),l=n(7),s=n(9),u=n(50),d=n(248),g=n(165),f=n(8),m=n(23),p=n(113),h=n(18),v=Object(p.a)(d.a);function InfoNoticeWidget(t){var n=t.Widget,i=t.WidgetNull,r=Object(h.a)(),u=Object(c.useInViewSelect)((function(e){var t=e(f.r).getAvailableAudiences();return null==t?void 0:t.map((function(e){return e.name}))}),[]),d=Object(c.useInViewSelect)((function(e){return e(l.a).getConfiguredAudiences()}),[]),p=null==d?void 0:d.some((function(e){return null==u?void 0:u.includes(e)})),b=g.a.length,E=Object(c.useInViewSelect)((function(e){return e(l.a).isPromptDismissed(g.c)}),[]),_=Object(c.useSelect)((function(e){return e(m.b).getValue(g.b)})),O=Object(c.useInViewSelect)((function(e){return e(l.a).getPromptDismissCount(g.c)}),[]),y=Object(c.useDispatch)(l.a).dismissPrompt,k=Object(a.useCallback)((function(){void 0!==O&&Object(s.I)("".concat(r,"_audiences-info-notice"),"dismiss_notice",g.a[O].slug).finally((function(){var e=2*s.f,t=O+1<b?e:0;y(g.c,{expiresInSeconds:t})}))}),[O,y,b,r]);if(!0!==p||E||void 0===O||O>=b||!0===_)return e.createElement(i,null);var j=g.a[O],S=j.slug,A=j.content;return e.createElement(n,{noPadding:!0},e.createElement(v,{content:A,dismissLabel:Object(o.__)("Got it","google-site-kit"),onDismiss:k,onInView:function(){Object(s.I)("".concat(r,"_audiences-info-notice"),"view_notice",S)}}))}InfoNoticeWidget.propTypes={Widget:r.a.elementType.isRequired,WidgetNull:r.a.elementType.isRequired},t.a=Object(u.a)({moduleName:"analytics-4"})(InfoNoticeWidget)}).call(this,n(4))},446:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SecondaryUserSetupWidget}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(1),u=n.n(s),d=n(81),g=n(0),f=n(3),m=n(177),p=n(8),h=n(203),v=n(35);function SecondaryUserSetupWidget(t){var n=t.Widget,i=Object(g.useState)(null),a=l()(i,2),c=a[0],s=a[1],u=Object(f.useSelect)((function(e){return e(p.r).isSettingUpAudiences()})),b=Object(f.useDispatch)(p.r).enableSecondaryUserAudienceGroup,E=function(){var e=o()(r.a.mark((function e(){var t,n;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return s(null),e.next=3,b();case 3:t=e.sent,(n=t.error)&&s(n);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return Object(d.a)((function(){u||o()(r.a.mark((function e(){var t,n;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,b();case 2:t=e.sent,(n=t.error)&&s(n);case 5:case"end":return e.stop()}}),e)})))()})),c?e.createElement(h.a,{Widget:n,errors:c,onRetry:E,showRetryButton:!Object(v.e)(c)}):e.createElement(n,{className:"googlesitekit-widget-audience-tiles",noPadding:!0},e.createElement("div",{className:"googlesitekit-widget-audience-tiles__body"},e.createElement(n,{noPadding:!0},e.createElement(m.a,null)),e.createElement(n,{noPadding:!0},e.createElement(m.a,null))))}SecondaryUserSetupWidget.propTypes={Widget:u.a.elementType.isRequired}}).call(this,n(4))},449:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return SetupCompletedSurveyTrigger}));var i=n(0),r=n(3),a=n(13),o=n(7),c=n(9),l=n(251);function SetupCompletedSurveyTrigger(){var t=Object(r.useSelect)((function(e){return e(a.c).isKeyMetricsSetupCompleted()})),n=Object(r.useSelect)((function(e){return e(a.c).getKeyMetricsSetupCompletedBy()})),s=Object(r.useSelect)((function(e){return e(o.a).getID()}));return t?e.createElement(i.Fragment,null,e.createElement(l.a,{triggerID:"view_kmw",ttl:c.f}),n===s&&e.createElement(l.a,{triggerID:"view_kmw_setup_completed",ttl:c.f})):null}}).call(this,n(4))},450:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsSetupDesktopSVG}));var i=n(0),r=n(2),a=n(44),o=n(151),c=Object(i.lazy)((function(){return n.e(38).then(n.bind(null,814))}));function KeyMetricsSetupDesktopSVG(){return e.createElement(i.Suspense,{fallback:e.createElement(a.a,{width:"100%",height:"235px"})},e.createElement(o.a,{errorMessage:Object(r.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},451:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsSetupSmallDesktopSVG}));var i=n(0),r=n(2),a=n(44),o=n(151),c=Object(i.lazy)((function(){return n.e(40).then(n.bind(null,815))}));function KeyMetricsSetupSmallDesktopSVG(){return e.createElement(i.Suspense,{fallback:e.createElement(a.a,{width:"100%",height:"235px"})},e.createElement(o.a,{errorMessage:Object(r.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},452:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsSetupTabletSVG}));var i=n(0),r=n(2),a=n(44),o=n(151),c=Object(i.lazy)((function(){return n.e(41).then(n.bind(null,816))}));function KeyMetricsSetupTabletSVG(){return e.createElement(i.Suspense,{fallback:e.createElement(a.a,{width:"100%",height:"235px"})},e.createElement(o.a,{errorMessage:Object(r.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},453:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return KeyMetricsSetupMobileSVG}));var i=n(0),r=n(2),a=n(44),o=n(151),c=Object(i.lazy)((function(){return n.e(39).then(n.bind(null,817))}));function KeyMetricsSetupMobileSVG(){return e.createElement(i.Suspense,{fallback:e.createElement(a.a,{width:"100%",height:"235px"})},e.createElement(o.a,{errorMessage:Object(r.__)("Failed to load graphic","google-site-kit")},e.createElement(c,null)))}}).call(this,n(4))},454:function(e,t,n){"use strict";n.d(t,"a",(function(){return KeyMetricsSetupCTARenderedEffect}));var i=n(265),r=n(3),a=n(23);function KeyMetricsSetupCTARenderedEffect(){var e=Object(r.useDispatch)(a.b).setValue;return Object(i.a)((function(){e("KEY_METRICS_SETUP_CTA_RENDERED",!0)})),null}},455:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return GetHelpLink}));var i=n(1),r=n.n(i),a=n(38),o=n(2),c=n(20);function GetHelpLink(t){var n=t.linkURL;return Object(a.a)(
/* translators: %s: get help text. */
Object(o.__)("Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(c.a,{href:n,external:!0,hideExternalIndicator:!0},Object(o.__)("Get help","google-site-kit"))})}GetHelpLink.propTypes={linkURL:r.a.string.isRequired}}).call(this,n(4))},456:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MetricTileLoader}));var i=n(44);function MetricTileLoader(){return e.createElement("div",{className:"googlesitekit-km-widget-tile__loading"},e.createElement(i.a,{className:"googlesitekit-km-widget-tile__loading-header",width:"100%",height:"14px"}),e.createElement(i.a,{className:"googlesitekit-km-widget-tile__loading-body",width:"100%",height:"53px"}))}}).call(this,n(4))},457:function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(0),o=n(2),c=n(10),l=n(17),s=n(24),u=n(351),d=n(35),g=n(135),f=n(346),m=Object(a.forwardRef)((function(t,n){var i=t.Widget,r=t.errors,a=t.onRetry,m=t.onRequestAccess,p=t.showRetryButton,h=Object(s.e)(),v=h===s.b,b=h===s.c,E=r.some(d.e);return e.createElement(i,{ref:n,noPadding:!0,className:"googlesitekit-audience-segmentation-error-widget"},e.createElement(l.e,{collapsed:!0,className:"googlesitekit-audience-segmentation-error__widget-primary-cell"},e.createElement(l.k,null,e.createElement(l.a,{smSize:6,mdSize:8,lgSize:7},e.createElement("h3",{className:"googlesitekit-publisher-win__title"},E?Object(o.__)("Insufficient permissions","google-site-kit"):Object(o.__)("Your visitor groups data loading failed","google-site-kit")),e.createElement("div",{className:"googlesitekit-widget-audience-segmentation-error__actions"},p&&a?e.createElement(c.Button,{onClick:a,danger:!0},Object(o.__)("Retry","google-site-kit")):e.createElement(g.a,{moduleSlug:"analytics-4",error:r,GetHelpLink:E?f.a:void 0,hideGetHelpLink:!E,buttonVariant:"danger",getHelpClassName:"googlesitekit-error-retry-text",onRetry:a,onRequestAccess:m}))),!v&&!b&&e.createElement(l.a,{className:"googlesitekit-widget-audience-segmentation-error__svg-wrapper",smSize:6,mdSize:3,lgSize:5},e.createElement(u.a,{width:"233px"})),b&&e.createElement(l.a,{className:"googlesitekit-widget-audience-segmentation-error__svg-wrapper",mdSize:8},e.createElement(u.a,{width:"233px"})),v&&e.createElement(l.a,{className:"googlesitekit-widget-audience-segmentation-error__svg-wrapper",smSize:8},e.createElement(u.a,{width:"233px"})))))}));m.propTypes={Widget:r.a.elementType.isRequired,errors:r.a.arrayOf(r.a.object).isRequired,onRetry:r.a.func.isRequired,onRequestAccess:r.a.func.isRequired,showRetryButton:r.a.bool},t.a=m}).call(this,n(4))},458:function(e,t,n){"use strict";(function(e){var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(0),l=n(2),s=n(157),u=n(88),d=n(32),g=n(13),f=n(7),m=n(9),p=n(18),h=n(3),v=n(10),b=n(137),E=Object(c.forwardRef)((function(t,n){var i=Object(p.a)(),a=Object(h.useSelect)((function(e){var t=e(g.c).getAdminURL("googlesitekit-dashboard");return Object(s.a)(t,{widgetArea:u.AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION})})),c=Object(h.useDispatch)(d.a).navigateTo,E=Object(h.useDispatch)(f.a).dismissItem;function _(){return E("settings_visitor_groups_setup_success_notification")}return e.createElement("div",{ref:n,className:"googlesitekit-settings-visitor-groups__setup-success googlesitekit-subtle-notification"},e.createElement("div",{className:"googlesitekit-subtle-notification__icon"},e.createElement(b.a,{width:24,height:24})),e.createElement("div",{className:"googlesitekit-subtle-notification__content"},e.createElement("p",null,Object(l.__)("We’ve added the audiences section to your dashboard!","google-site-kit"))),e.createElement("div",{className:"googlesitekit-subtle-notification__action"},e.createElement(v.Button,{tertiary:!0,onClick:function(){Object(m.I)("".concat(i,"_audiences-setup-cta-settings-success"),"dismiss_notification").finally(_)}},Object(l.__)("Got it","google-site-kit")),e.createElement(v.Button,{onClick:function(){Object(m.I)("".concat(i,"_audiences-setup-cta-settings-success"),"confirm_notification").finally(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,_();case 2:c(a);case 3:case"end":return e.stop()}}),e)}))))}},Object(l.__)("Show me","google-site-kit"))))}));t.a=E}).call(this,n(4))},459:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{clipPath:"url(#audience-segmentation-setup-desktop_svg__clip0_1782_21770)"},i.createElement("path",{d:"M39.03 49.706a97.776 97.776 0 019.93-8.943c33.321-26.138 60.851-26.096 98.964-21.325C173.69 22.662 188.743 34.928 223.871 33 259 31.07 268 22.14 311 25c34.038 2.265 49.308 8.985 79.57 24.706 28.007 14.55 53.437 40.114 66.216 73.922 21.906 57.948-17.152 162.243-69.53 167.16-37.953 3.565-69.275-38.468-107.583-28.733-23.599 5.998-37.595 31.155-55.802 47.757-21.241 19.371-66.999 18.153-91.871 7.816-23.702-9.851-46.759-29.374-54.5-58.128-5.893-21.897-7.5-48-24-68-19.968-24.204-32.167-35-39.022-67.872-5.954-28.55 6.856-55.631 24.552-73.922z",fill:"#B8E6CA"}),i.createElement("rect",{x:85,y:41,width:153,height:208,rx:11,fill:"#fff"}),i.createElement("rect",{x:104.031,y:126.641,width:53.016,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:104.031,y:111.688,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:104.031,y:160.703,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:104.031,y:207.703,width:19.031,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("path",{d:"M189.672 124.602a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 110 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),i.createElement("rect",{x:104,y:62,width:36,height:9,rx:4.5,fill:"#EBEEF0"}),i.createElement("path",{d:"M189.672 173.617a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 110 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#FFDED3"}),i.createElement("path",{d:"M189.672 220.617a8.836 8.836 0 018.836-8.836h13.594a8.836 8.836 0 110 17.672h-13.594a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),i.createElement("rect",{x:102.672,y:175.656,width:55.734,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("path",{d:"M238 89.5H86",stroke:"#EBEEF0",strokeWidth:2}),i.createElement("rect",{x:258,y:41,width:153,height:208,rx:11,fill:"#fff"}),i.createElement("rect",{x:276.955,y:126.641,width:52.805,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:276.955,y:111.688,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:276.955,y:160.703,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("rect",{x:276.955,y:207.703,width:18.956,height:9.516,rx:4.758,fill:"#EBEEF0"}),i.createElement("path",{d:"M362.256 124.602a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836z",fill:"#B8E6CA"}),i.createElement("rect",{x:277,y:62,width:36,height:9,rx:4.5,fill:"#EBEEF0"}),i.createElement("path",{d:"M401 89.5H258",stroke:"#EBEEF0",strokeWidth:2}),i.createElement("path",{d:"M362.256 173.617a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836zM362.256 220.617a8.836 8.836 0 018.836-8.836h13.47a8.836 8.836 0 018.835 8.836 8.836 8.836 0 01-8.835 8.836h-13.47a8.836 8.836 0 01-8.836-8.836z",fill:"#FFDED3"}),i.createElement("rect",{x:275.602,y:175.656,width:55.513,height:9.516,rx:4.758,fill:"#EBEEF0"})),o=i.createElement("defs",null,i.createElement("clipPath",{id:"audience-segmentation-setup-desktop_svg__clip0_1782_21770"},i.createElement("path",{fill:"#fff",d:"M0 0h496v216H0z"})));t.a=function SvgAudienceSegmentationSetupDesktop(e){return i.createElement("svg",r({viewBox:"0 0 496 216",fill:"none"},e),a,o)}},460:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{clipPath:"url(#audience-segmentation-setup-tablet_svg__clip0_1925_26844)"},i.createElement("path",{d:"M67.728 34.59a71.982 71.982 0 017.31-6.584c24.527-19.24 44.792-19.21 72.848-15.698 18.966 2.374 30.047 11.404 55.905 9.984C229.65 20.872 234.5 10.117 271 14.308 307.5 18.5 322.5-1.5 360.5 1.5s52 32 72 42 37 8.5 50 35 4 83.5-36 106-99.355 25.386-122.439 27.553c-27.938 2.624-50.995-28.317-79.194-21.151-17.371 4.415-27.674 22.934-41.076 35.155-15.636 14.258-49.319 13.362-67.627 5.752-17.448-7.25-34.42-21.622-40.118-42.788-4.338-16.119-5.521-35.333-17.667-50.056-14.698-17.816-23.679-25.763-28.725-49.961-4.382-21.016 5.047-40.95 18.074-54.414z",fill:"#93C9A8"}),i.createElement("rect",{x:93,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:107.008,y:92.222,width:39.025,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:107.008,y:81.214,width:14.009,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:107.008,y:117.295,width:14.009,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M170.051 90.72a6.504 6.504 0 016.504-6.504h10.007a6.504 6.504 0 010 13.009h-10.007a6.504 6.504 0 01-6.504-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:106.984,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M170.051 126.802a6.504 6.504 0 016.504-6.505h10.007a6.504 6.504 0 010 13.009h-10.007a6.504 6.504 0 01-6.504-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:106.008,y:128.303,width:41.027,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M205.625 64.882H93.736",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("rect",{x:220.348,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:234.301,y:92.222,width:38.871,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:234.301,y:81.214,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:234.301,y:117.295,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M297.094 90.72a6.504 6.504 0 016.504-6.504h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.504-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:234.332,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M325.613 64.882H220.349",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("path",{d:"M297.094 126.802a6.504 6.504 0 016.504-6.505h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.504-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:233.305,y:128.303,width:40.864,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:347.695,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:361.648,y:92.222,width:38.871,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:361.648,y:81.214,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:361.648,y:117.295,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M424.441 90.72a6.504 6.504 0 016.505-6.504h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.505-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:361.68,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M452.961 64.882H347.697",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("path",{d:"M424.441 126.802a6.505 6.505 0 016.505-6.505h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.505-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:360.652,y:128.303,width:40.864,height:7.005,rx:3.502,fill:"#EBEEF0"})),o=i.createElement("defs",null,i.createElement("clipPath",{id:"audience-segmentation-setup-tablet_svg__clip0_1925_26844"},i.createElement("path",{fill:"#fff",d:"M0 0h553v158H0z"})));t.a=function SvgAudienceSegmentationSetupTablet(e){return i.createElement("svg",r({viewBox:"0 0 553 146",fill:"none"},e),a,o)}},461:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("g",{clipPath:"url(#audience-segmentation-setup-mobile_svg__clip0_1657_37513)"},i.createElement("g",{filter:"url(#audience-segmentation-setup-mobile_svg__filter0_d_1657_37513)"},i.createElement("rect",{x:-16.047,width:111.697,height:151.85,rx:8.031,fill:"#fff"}),i.createElement("rect",{x:-15.682,y:.365,width:110.967,height:151.12,rx:7.665,stroke:"#EBEEF0",strokeWidth:.73})),i.createElement("rect",{x:-2.152,y:62.522,width:38.704,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("rect",{x:-2.152,y:51.605,width:13.894,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("rect",{x:-2.152,y:90.309,width:13.894,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("path",{d:"M60.367 61.033a6.45 6.45 0 016.45-6.45h9.925a6.45 6.45 0 010 12.9h-9.924a6.45 6.45 0 01-6.45-6.45z",fill:"#B8E6CA"}),i.createElement("rect",{x:-2.176,y:15.331,width:26.282,height:6.57,rx:3.285,fill:"#EBEEF0"}),i.createElement("path",{d:"M60.367 99.737a6.45 6.45 0 016.45-6.45h9.925a6.45 6.45 0 010 12.9h-9.924a6.45 6.45 0 01-6.45-6.45z",fill:"#FFDED3"}),i.createElement("rect",{x:-3.145,y:101.226,width:40.689,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("path",{d:"M95.648 35.407H-15.319",stroke:"#EBEEF0",strokeWidth:1.46}),i.createElement("g",{filter:"url(#audience-segmentation-setup-mobile_svg__filter1_d_1657_37513)"},i.createElement("rect",{x:115.648,width:111.697,height:151.85,rx:8.031,fill:"#fff"}),i.createElement("rect",{x:116.013,y:.365,width:110.967,height:151.12,rx:7.665,stroke:"#EBEEF0",strokeWidth:.73})),i.createElement("rect",{x:129.488,y:62.522,width:38.55,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("rect",{x:129.488,y:51.605,width:13.839,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("rect",{x:129.488,y:90.309,width:13.839,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("path",{d:"M191.762 61.033a6.45 6.45 0 016.45-6.45h9.834a6.451 6.451 0 010 12.9h-9.834a6.45 6.45 0 01-6.45-6.45z",fill:"#B8E6CA"}),i.createElement("rect",{x:129.52,y:15.331,width:26.282,height:6.57,rx:3.285,fill:"#EBEEF0"}),i.createElement("path",{d:"M220.047 35.407H115.65",stroke:"#EBEEF0",strokeWidth:1.46}),i.createElement("path",{d:"M191.762 99.737a6.45 6.45 0 016.45-6.45h9.834a6.451 6.451 0 010 12.9h-9.834a6.45 6.45 0 01-6.45-6.45z",fill:"#FFDED3"}),i.createElement("rect",{x:128.5,y:101.226,width:40.527,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("g",{filter:"url(#audience-segmentation-setup-mobile_svg__filter2_d_1657_37513)"},i.createElement("rect",{x:247.348,width:111.697,height:151.85,rx:8.031,fill:"#fff"}),i.createElement("rect",{x:247.713,y:.365,width:110.967,height:151.12,rx:7.665,stroke:"#EBEEF0",strokeWidth:.73})),i.createElement("rect",{x:261.188,y:62.522,width:38.55,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("rect",{x:261.188,y:51.605,width:13.839,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("rect",{x:261.188,y:90.309,width:13.839,height:6.947,rx:3.473,fill:"#EBEEF0"}),i.createElement("path",{d:"M323.461 61.033a6.45 6.45 0 016.451-6.45h9.833a6.45 6.45 0 110 12.9h-9.833a6.45 6.45 0 01-6.451-6.45z",fill:"#B8E6CA"}),i.createElement("rect",{x:261.219,y:15.331,width:26.282,height:6.57,rx:3.285,fill:"#EBEEF0"}),i.createElement("path",{d:"M351.746 35.407H247.349",stroke:"#EBEEF0",strokeWidth:1.46}),i.createElement("path",{d:"M323.461 99.737a6.45 6.45 0 016.451-6.45h9.833a6.45 6.45 0 110 12.9h-9.833a6.45 6.45 0 01-6.451-6.45z",fill:"#FFDED3"}),i.createElement("rect",{x:260.199,y:101.226,width:40.527,height:6.947,rx:3.473,fill:"#EBEEF0"})),o=i.createElement("defs",null,i.createElement("filter",{id:"audience-segmentation-setup-mobile_svg__filter0_d_1657_37513",x:-16.047,y:0,width:114.616,height:155.5,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dx:2.92,dy:3.65}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0.921569 0 0 0 0 0.933333 0 0 0 0 0.941176 0 0 0 1 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1657_37513"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1657_37513",result:"shape"})),i.createElement("filter",{id:"audience-segmentation-setup-mobile_svg__filter1_d_1657_37513",x:115.648,y:0,width:114.616,height:155.5,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dx:2.92,dy:3.65}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0.921569 0 0 0 0 0.933333 0 0 0 0 0.941176 0 0 0 1 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1657_37513"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1657_37513",result:"shape"})),i.createElement("filter",{id:"audience-segmentation-setup-mobile_svg__filter2_d_1657_37513",x:247.348,y:0,width:114.616,height:155.5,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dx:2.92,dy:3.65}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0.921569 0 0 0 0 0.933333 0 0 0 0 0.941176 0 0 0 1 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1657_37513"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1657_37513",result:"shape"})),i.createElement("clipPath",{id:"audience-segmentation-setup-mobile_svg__clip0_1657_37513"},i.createElement("path",{fill:"#fff",d:"M0 0h343v157.69H0z"})));t.a=function SvgAudienceSegmentationSetupMobile(e){return i.createElement("svg",r({viewBox:"0 0 343 120",fill:"none"},e),a,o)}},462:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTiles}));var i=n(27),r=n.n(i),a=n(15),o=n.n(a),c=n(1),l=n.n(c),s=n(0),u=n(10),d=n(3),g=n(24),f=n(7),m=n(8),p=n(463),h=n(132),v=n(385),b=n(203),E=n(480),_=n(177),O=n(483),y=n(485),k=n(269),j=n(18),S=n(34),A=n(9),w=n(382),T=function(e,t){var n,i,r,a=null==e||null===(n=e.rows)||void 0===n?void 0:n.find((function(e){var n,i;return(null===(n=e.dimensionValues)||void 0===n||null===(i=n[0])||void 0===i?void 0:i.value)===t}));return 0===((null==a||null===(i=a.metricValues)||void 0===i||null===(r=i[0])||void 0===r?void 0:r.value)||0)};function AudienceTiles(t){var n=t.Widget,i=t.widgetLoading,a=Object(j.a)(),c=Object(S.a)(),l=Object(g.e)(),C=l===g.b||l===g.c,N=Object(d.useInViewSelect)((function(e){return e(f.a).getConfiguredAudiences()}),[]),D=Object(d.useInViewSelect)((function(e){return e(m.r).getAvailableAudiences()}),[]),R=Object(d.useSelect)((function(e){return e(m.r).getConfiguredSiteKitAndOtherAudiences()}))||[[],[]],x=o()(R,2),M=x[0],I=x[1],B=Object(d.useSelect)((function(e){return e(m.r).hasAudiencePartialData(M)})),P=Object(y.a)({isSiteKitAudiencePartialData:B,siteKitAudiences:M,otherAudiences:I}),L=P.report,F=P.reportLoaded,z=P.reportError,W=P.siteKitAudiencesReport,V=P.siteKitAudiencesReportLoaded,U=P.siteKitAudiencesReportError,G=P.totalPageviews,H=P.totalPageviewsReportLoaded,q=P.totalPageviewsReportError,K=P.topCitiesReport,Y=P.topCitiesReportLoaded,X=P.topCitiesReportErrors,Z=P.topContentReport,$=P.topContentReportLoaded,J=P.topContentReportErrors,Q=P.topContentPageTitlesReport,ee=P.topContentPageTitlesReportLoaded,te=P.topContentPageTitlesReportErrors,ne=function(e,t){var n,i,r,a,o,c,l=(null==D||null===(n=D.filter((function(t){return t.name===e})))||void 0===n||null===(i=n[0])||void 0===i?void 0:i.displayName)||"",s=(null==D||null===(r=D.filter((function(t){return t.name===e})))||void 0===r||null===(a=r[0])||void 0===a?void 0:a.audienceSlug)||"",u=function(e){var t,n=M.some((function(t){return t.name===e})),i=null===(t=M.find((function(t){return t.name===e})))||void 0===t?void 0:t.audienceSlug,r=function(t){var r,a,o,c,l,s,u,d,g,f,m,p,h;if(n&&B){var v,b="new-visitors"===i?"new":"returning";h=null==W||null===(v=W.rows)||void 0===v?void 0:v.find((function(e){var n,i,r=e.dimensionValues;return(null==r||null===(n=r[0])||void 0===n?void 0:n.value)===b&&(null==r||null===(i=r[1])||void 0===i?void 0:i.value)===t}))}else{var E;h=null==L||null===(E=L.rows)||void 0===E?void 0:E.find((function(n){var i,r,a=n.dimensionValues;return(null==a||null===(i=a[0])||void 0===i?void 0:i.value)===e&&(null==a||null===(r=a[1])||void 0===r?void 0:r.value)===t}))}return[Number((null===(r=h)||void 0===r||null===(a=r.metricValues)||void 0===a||null===(o=a[0])||void 0===o?void 0:o.value)||0),Number((null===(c=h)||void 0===c||null===(l=c.metricValues)||void 0===l||null===(s=l[1])||void 0===s?void 0:s.value)||0),Number((null===(u=h)||void 0===u||null===(d=u.metricValues)||void 0===d||null===(g=d[2])||void 0===g?void 0:g.value)||0),Number((null===(f=h)||void 0===f||null===(m=f.metricValues)||void 0===m||null===(p=m[3])||void 0===p?void 0:p.value)||0)]};return{current:r("date_range_0"),previous:r("date_range_1")}}(e),d=u.current,g=u.previous,f=d[0],m=g[0],p=d[1],h=g[1],v=d[2],b=g[2],E=d[3],_=g[3],O=null==K?void 0:K[t],y=null==Z?void 0:Z[t],k=(null==Q||null===(o=Q[t])||void 0===o||null===(c=o.rows)||void 0===c?void 0:c.reduce((function(e,t){return e[t.dimensionValues[0].value]=t.dimensionValues[1].value,e}),{}))||{},j=M.some((function(t){return t.name===e})),S=L,A=e;return j&&B&&(S=W,A="new-visitors"===s?"new":"returning"),{audienceName:l,audienceSlug:s,visitors:f,prevVisitors:m,visitsPerVisitors:p,prevVisitsPerVisitors:h,pagesPerVisit:v,prevPagesPerVisit:b,pageviews:E,prevPageviews:_,topCities:O,topContent:y,topContentTitles:k,isZeroData:T(S,A),isPartialData:!j&&le[e]}},ie=null==N?void 0:N.reduce((function(e,t){return e[t]=[],[X,J,te].forEach((function(n){var i=n[t];i&&!Object(k.a)(i)&&e[t].push(i)})),e}),{}),re=Object(d.useSelect)((function(e){return e(f.a).getDismissedItems()})),ae=Object(d.useSelect)((function(e){return e(f.a)})).isDismissingItem,oe=Object(d.useDispatch)(f.a).dismissItem,ce=Object(s.useCallback)((function(e){oe("audience-tile-".concat(e))}),[oe]),le=Object(d.useInViewSelect)((function(e){return null==N?void 0:N.reduce((function(t,n){return t[n]=e(m.r).isAudiencePartialData(n),t}),{})}),[N]),se=Object(s.useRef)({}),ue=Object(s.useMemo)((function(){for(var e=[],t=[],n=null==N?void 0:N.slice().filter((function(e){return D.some((function(t){return t.name===e}))})),i=function(){var i,r=n.shift(),a=null==re?void 0:re.includes("audience-tile-".concat(r)),o=M.some((function(e){return e.name===r})),c=L,l=r;o&&B&&(c=W,l="new-visitors"===(null===(i=M.find((function(e){return e.name===r})))||void 0===i?void 0:i.audienceSlug)?"new":"returning");var s=T(c,l),u=n.length+t.length>0;if(a&&s&&u)return"continue";a&&!s&&e.push(r),t.push(r)};(null==n?void 0:n.length)>0;)i();return[e,t]}),[D,N,re,B,L,M,W]),de=o()(ue,2),ge=de[0],fe=de[1];var me,pe=(me=[],L&&me.push(z),W&&me.push(U),!(!me.every(Boolean)&&!q)||(null==N?void 0:N.every((function(e){return ie[e].length>0}))));Object(s.useEffect)((function(){ge.forEach((function(e){var t="audience-tile-".concat(e);se.current[t]||(oe(t,{expiresInSeconds:1}),se.current[t]=!0)}))}),[ge,oe,ae]);var he=Object(d.useSelect)((function(e){return e(m.r).isFetchingSyncAvailableCustomDimensions()})),ve=Object(d.useDispatch)(m.r).fetchSyncAvailableCustomDimensions,be=Object.values(J).some(k.a)||Object.values(te).some(k.a);Object(s.useEffect)((function(){!c&&be&&ve()}),[ve,be,c]);var Ee=Object(s.useState)(fe[0]),_e=o()(Ee,2),Oe=_e[0],ye=_e[1],ke=Object(s.useCallback)((function(e){var t=fe.indexOf(e);return-1===t?0:t}),[fe]);Object(s.useEffect)((function(){fe.includes(Oe)||ye(fe[0])}),[Oe,fe]);var je=ke(Oe),Se=i||!F||!V||!H||!Y||!$||!ee||he,Ae=0;return e.createElement(n,{className:"googlesitekit-widget-audience-tiles",noPadding:!0},!1===pe&&!Se&&C&&fe.length>0&&e.createElement(u.TabBar,{key:fe.length,className:"googlesitekit-widget-audience-tiles__tabs googlesitekit-tab-bar--start-aligned-high-contrast",activeIndex:je,handleActiveIndexUpdate:function(e){return ye(fe[e])}},fe.map((function(t,n){var i,r,o,c,l=(null==D||null===(i=D.filter((function(e){return e.name===t})))||void 0===i||null===(r=i[0])||void 0===r?void 0:r.displayName)||"",s=(null==D||null===(o=D.filter((function(e){return e.name===t})))||void 0===o||null===(c=o[0])||void 0===c?void 0:c.audienceSlug)||"",d=e.createElement(v.a,{audienceName:l,audienceSlug:s});return e.createElement(u.Tab,{key:n,"aria-label":l},l,e.createElement(h.a,{title:d,tooltipClassName:"googlesitekit-info-tooltip__content--audience",onOpen:function(){Object(A.I)("".concat(a,"_audiences-tile"),"view_tile_tooltip",s)}}))}))),e.createElement("div",{className:"googlesitekit-widget-audience-tiles__body"},pe&&!Se&&e.createElement(b.a,{Widget:n,errors:[].concat(r()(Object.values(ie).flat(2)),[z,q])}),(!1===pe||Se)&&fe.map((function(t,i){var r,a,o,c,l,s,u,d,g,f,m,h,b,O,y,k,j,S,A,T,N,D,R,x,M,I,B,P,L,F;if(C&&i!==je)return null;var z=ne(t,i),W=z.audienceName,V=z.audienceSlug,U=z.visitors,H=z.prevVisitors,q=z.visitsPerVisitors,K=z.prevVisitsPerVisitors,Y=z.pagesPerVisit,X=z.prevPagesPerVisit,Z=z.pageviews,$=z.prevPageviews,J=z.topCities,Q=z.topContent,ee=z.topContentTitles,te=z.isZeroData,re=z.isPartialData,ae=(null==J?void 0:J.rows)?Object(w.a)(J.rows):[];return Se||void 0===te||void 0===re?e.createElement(n,{key:t,noPadding:!0},e.createElement(_.a,null)):ie[t].length>0?e.createElement(E.a,{key:t,audienceSlug:V,errors:ie[t]}):e.createElement(p.a,{key:t,audienceTileNumber:Ae++,audienceSlug:V,title:W,infoTooltip:e.createElement(v.a,{audienceName:W,audienceSlug:V}),visitors:{currentValue:U,previousValue:H},visitsPerVisitor:{currentValue:q,previousValue:K},pagesPerVisit:{currentValue:Y,previousValue:X},pageviews:{currentValue:Z,previousValue:$},percentageOfTotalPageViews:0!==G?Z/G:0,topCities:{dimensionValues:[null==ae||null===(r=ae[0])||void 0===r||null===(a=r.dimensionValues)||void 0===a?void 0:a[0],null==ae||null===(o=ae[1])||void 0===o||null===(c=o.dimensionValues)||void 0===c?void 0:c[0],null==ae||null===(l=ae[2])||void 0===l||null===(s=l.dimensionValues)||void 0===s?void 0:s[0]],metricValues:[null==ae||null===(u=ae[0])||void 0===u||null===(d=u.metricValues)||void 0===d?void 0:d[0],null==ae||null===(g=ae[1])||void 0===g||null===(f=g.metricValues)||void 0===f?void 0:f[0],null==ae||null===(m=ae[2])||void 0===m||null===(h=m.metricValues)||void 0===h?void 0:h[0]],total:U},topContent:{dimensionValues:[null==Q||null===(b=Q.rows)||void 0===b||null===(O=b[0])||void 0===O||null===(y=O.dimensionValues)||void 0===y?void 0:y[0],null==Q||null===(k=Q.rows)||void 0===k||null===(j=k[1])||void 0===j||null===(S=j.dimensionValues)||void 0===S?void 0:S[0],null==Q||null===(A=Q.rows)||void 0===A||null===(T=A[2])||void 0===T||null===(N=T.dimensionValues)||void 0===N?void 0:N[0]],metricValues:[null==Q||null===(D=Q.rows)||void 0===D||null===(R=D[0])||void 0===R||null===(x=R.metricValues)||void 0===x?void 0:x[0],null==Q||null===(M=Q.rows)||void 0===M||null===(I=M[1])||void 0===I||null===(B=I.metricValues)||void 0===B?void 0:B[0],null==Q||null===(P=Q.rows)||void 0===P||null===(L=P[2])||void 0===L||null===(F=L.metricValues)||void 0===F?void 0:F[0]]},topContentTitles:ee,hasInvalidCustomDimensionError:be,Widget:n,audienceResourceName:t,isZeroData:te,isPartialData:re,isTileHideable:fe.length>1,onHideTile:function(){return ce(t)}})})),!C&&e.createElement(O.a,{Widget:n,loading:Se,allTilesError:pe,visibleAudienceCount:fe.length})))}AudienceTiles.propTypes={Widget:l.a.elementType.isRequired,widgetLoading:l.a.bool.isRequired}}).call(this,n(4))},463:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTile}));var i=n(11),r=n.n(i),a=n(1),o=n.n(a),c=n(2),l=n(3),s=n(24),u=n(34),d=n(8),g=n(464),f=n(465),m=n(466),p=n(467),h=n(468),v=n(469),b=n(306),E=n(470),_=n(471),O=n(184),y=n(132),k=n(348),j=n(9),S=n(250),A=n(18),w=n(474);function AudienceTile(t){var n=t.audienceTileNumber,i=void 0===n?0:n,a=t.audienceSlug,o=t.title,T=t.infoTooltip,C=t.visitors,N=t.visitsPerVisitor,D=t.pagesPerVisit,R=t.pageviews,x=t.percentageOfTotalPageViews,M=t.topCities,I=t.topContent,B=t.topContentTitles,P=t.hasInvalidCustomDimensionError,L=t.Widget,F=t.audienceResourceName,z=t.isZeroData,W=t.isPartialData,V=t.isTileHideable,U=t.onHideTile,G=Object(s.e)(),H=Object(A.a)(),q=Object(u.a)(),K=Object(l.useInViewSelect)((function(e){var t=e(d.r).getPropertyID();return t&&e(d.r).isPropertyPartialData(t)})),Y=Object(l.useSelect)((function(e){return e(d.r).isSiteKitAudience(F)})),X=Object(l.useInViewSelect)((function(e){return!Y&&void 0!==K&&(!K&&F&&e(d.r).isAudiencePartialData(F))}),[K,Y,F]),Z=Object(l.useInViewSelect)((function(e){return void 0!==K&&(!K&&!X&&e(d.r).isCustomDimensionPartialData("googlesitekit_post_type"))}),[X]),$=Object(l.useInViewSelect)((function(e){return e(d.r).hasCustomDimensions("googlesitekit_post_type")}),[]),J=[s.b,s.c].includes(G);return W&&z?e.createElement(w.a,{Widget:L,audienceSlug:a,title:o,infoTooltip:T,isMobileBreakpoint:J,isTileHideable:V,onHideTile:U}):e.createElement(L,{noPadding:!0},e.createElement("div",{className:r()("googlesitekit-audience-segmentation-tile",{"googlesitekit-audience-segmentation-tile--partial-data":X})},!J&&e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__header"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__header-title"},o,T&&e.createElement(y.a,{title:T,tooltipClassName:"googlesitekit-info-tooltip__content--audience",onOpen:function(){return Object(j.I)("".concat(H,"_audiences-tile"),"view_tile_tooltip",a)}})),X&&e.createElement(S.a,{className:"googlesitekit-audience-segmentation-partial-data-badge",label:Object(c.__)("Partial data","google-site-kit"),tooltipTitle:Object(c.__)("Still collecting full data for this timeframe, partial data is displayed for this group","google-site-kit"),onTooltipOpen:function(){Object(j.I)("".concat(H,"_audiences-tile"),"view_tile_partial_data_tooltip",a)}})),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__metrics"},J&&X&&e.createElement(k.a,{content:Object(c.__)("Still collecting full data for this timeframe, partial data is displayed for this group","google-site-kit")}),e.createElement(b.a,{TileIcon:g.a,title:Object(c.__)("Visitors","google-site-kit"),metricValue:C.currentValue,Badge:function Badge(){return e.createElement(O.a,{previousValue:C.previousValue,currentValue:C.currentValue})}}),e.createElement(b.a,{TileIcon:f.a,title:Object(c.__)("Visits per visitor","google-site-kit"),metricValue:N.currentValue,Badge:function Badge(){return e.createElement(O.a,{previousValue:N.previousValue,currentValue:N.currentValue})}}),e.createElement(b.a,{TileIcon:m.a,title:Object(c.__)("Pages per visit","google-site-kit"),metricValue:D.currentValue,Badge:function Badge(){return e.createElement(O.a,{previousValue:D.previousValue,currentValue:D.currentValue})},metricValueFormat:{style:"decimal",maximumFractionDigits:2}}),e.createElement(b.a,{TileIcon:p.a,title:Object(c.sprintf)(
/* translators: %s: is a percentage value such as 33.3%. */
Object(c.__)("%s of total pageviews","google-site-kit"),Object(j.B)(x,{style:"percent",maximumFractionDigits:1})),metricValue:R.currentValue,Badge:function Badge(){return e.createElement(O.a,{previousValue:R.previousValue,currentValue:R.currentValue})}}),e.createElement(E.a,{TileIcon:h.a,title:Object(c.__)("Cities with the most visitors","google-site-kit"),topCities:M}),(!q||$&&!P)&&e.createElement(_.a,{audienceTileNumber:i,audienceSlug:a,TileIcon:v.a,title:Object(c.__)("Top content by pageviews","google-site-kit"),topContentTitles:B,topContent:I,isTopContentPartialData:Z}))))}AudienceTile.propTypes={audienceTileNumber:o.a.number,audienceSlug:o.a.string.isRequired,title:o.a.string.isRequired,infoTooltip:o.a.oneOfType([o.a.string,o.a.element]),visitors:o.a.object,visitsPerVisitor:o.a.object,pagesPerVisit:o.a.object,pageviews:o.a.object,percentageOfTotalPageViews:o.a.number,topCities:o.a.object,topContent:o.a.object,topContentTitles:o.a.object,hasInvalidCustomDimensionError:o.a.bool,Widget:o.a.elementType.isRequired,audienceResourceName:o.a.string.isRequired,isZeroData:o.a.bool,isPartialData:o.a.bool,isTileHideable:o.a.bool,onHideTile:o.a.func}}).call(this,n(4))},464:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M.833 16.667v-2.334c0-.472.118-.902.354-1.291a2.48 2.48 0 01.98-.917c.86-.43 1.735-.75 2.624-.958a11.126 11.126 0 012.709-.333c.916 0 1.82.11 2.708.333.889.208 1.764.528 2.625.958.403.209.722.514.958.917.25.389.375.82.375 1.292v2.333H.833zm15 0v-2.5c0-.611-.174-1.195-.52-1.75-.334-.57-.813-1.056-1.438-1.458.708.083 1.375.229 2 .437a9.852 9.852 0 011.75.73c.5.277.882.59 1.145.937.264.333.396.701.396 1.104v2.5h-3.333zM7.5 10a3.21 3.21 0 01-2.354-.979 3.21 3.21 0 01-.98-2.354c0-.917.327-1.702.98-2.354a3.21 3.21 0 012.354-.98 3.21 3.21 0 012.354.98 3.21 3.21 0 01.979 2.354 3.21 3.21 0 01-.98 2.354 3.21 3.21 0 01-2.353.98zm8.333-3.333a3.21 3.21 0 01-.98 2.354 3.21 3.21 0 01-2.353.98c-.153 0-.348-.015-.584-.042a6.732 6.732 0 01-.583-.125c.375-.445.66-.938.854-1.48a4.662 4.662 0 00.313-1.687c0-.583-.104-1.146-.313-1.688a4.784 4.784 0 00-.854-1.479c.194-.07.389-.11.583-.125a4.12 4.12 0 01.584-.042 3.21 3.21 0 012.354.98 3.21 3.21 0 01.979 2.354zM2.5 15h10v-.666a.735.735 0 00-.125-.417.737.737 0 00-.292-.292 10.446 10.446 0 00-2.27-.833 9.342 9.342 0 00-4.626 0c-.764.18-1.52.458-2.27.833a.894.894 0 00-.313.292.843.843 0 00-.104.417V15zm5-6.666c.458 0 .847-.16 1.166-.48.334-.333.5-.729.5-1.187 0-.458-.166-.847-.5-1.167-.32-.333-.708-.5-1.166-.5-.459 0-.854.167-1.188.5-.32.32-.479.708-.479 1.167 0 .458.16.854.48 1.187.333.32.728.48 1.187.48z",fill:"currentColor"});t.a=function SvgAudienceMetricIconVisitors(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},465:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M10 17.5a7.443 7.443 0 01-2.938-.583 8.045 8.045 0 01-2.375-1.605 8.045 8.045 0 01-1.604-2.374A7.443 7.443 0 012.5 10c0-1.042.194-2.014.583-2.917a7.7 7.7 0 011.604-2.375 7.548 7.548 0 012.375-1.604A7.221 7.221 0 0110 2.5c1.139 0 2.215.243 3.23.73a7.252 7.252 0 012.603 2.062V3.333H17.5v5h-5V6.667h2.292a6.194 6.194 0 00-2.104-1.834A5.625 5.625 0 0010 4.167c-1.625 0-3.007.57-4.146 1.708C4.73 7 4.167 8.375 4.167 10s.562 3.007 1.687 4.146c1.14 1.125 2.521 1.687 4.146 1.687 1.458 0 2.73-.472 3.813-1.416 1.097-.945 1.743-2.14 1.937-3.584h1.708c-.208 1.903-1.027 3.493-2.458 4.771-1.417 1.264-3.083 1.896-5 1.896zm2.333-4l-3.166-3.167v-4.5h1.666v3.834l2.667 2.666-1.167 1.167z",fill:"currentColor"});t.a=function SvgAudienceMetricIconVisitsPerVisitor(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},466:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M13.125 16.667H3.334c-.459 0-.855-.16-1.188-.48-.32-.333-.479-.729-.479-1.187V5c0-.458.16-.847.48-1.167.332-.333.728-.5 1.187-.5h13.333c.458 0 .847.167 1.167.5.333.32.5.709.5 1.167v10c0 .459-.167.854-.5 1.188-.32.32-.709.479-1.167.479H15.5l-3.916-3.917c-.292.195-.611.34-.959.438-.333.097-.68.146-1.041.146-1.042 0-1.93-.362-2.667-1.084-.722-.736-1.083-1.625-1.083-2.666 0-1.042.36-1.924 1.083-2.646a3.633 3.633 0 012.667-1.104c1.041 0 1.923.368 2.646 1.104.736.722 1.104 1.604 1.104 2.646 0 .375-.049.729-.146 1.062a3.393 3.393 0 01-.438.938L16.167 15h.5V5H3.334v10h8.125l1.666 1.667zm-3.541-5c.583 0 1.076-.202 1.479-.604.403-.403.604-.896.604-1.48 0-.583-.201-1.076-.604-1.479a2.012 2.012 0 00-1.48-.604c-.583 0-1.076.202-1.479.604a2.012 2.012 0 00-.604 1.48c0 .583.202 1.076.604 1.479.403.402.896.604 1.48.604zM3.334 15V5v10z",fill:"currentColor"});t.a=function SvgAudienceMetricIconPagesPerVisit(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},467:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M4.167 17.5c-.459 0-.854-.16-1.188-.48-.32-.332-.479-.728-.479-1.187V4.167c0-.459.16-.848.48-1.167.333-.333.728-.5 1.187-.5h11.666c.459 0 .848.167 1.167.5.333.32.5.708.5 1.167v11.666c0 .459-.167.854-.5 1.188-.32.32-.708.479-1.167.479H4.167zm0-1.667h11.666v-10H4.167v10zM10 14.167c-1.139 0-2.16-.306-3.063-.917A5.398 5.398 0 015 10.833a5.271 5.271 0 011.938-2.396C7.84 7.814 8.86 7.5 10 7.5c1.139 0 2.153.313 3.042.938A5.229 5.229 0 0115 10.832a5.353 5.353 0 01-1.958 2.417c-.89.611-1.903.917-3.042.917zm0-1.25c.778 0 1.486-.18 2.125-.542a4 4 0 001.5-1.542 3.854 3.854 0 00-1.5-1.52A4.12 4.12 0 0010 8.75a4.12 4.12 0 00-2.125.563 3.854 3.854 0 00-1.5 1.52 4 4 0 001.5 1.542 4.243 4.243 0 002.125.542zm0-.834a1.26 1.26 0 01-.896-.354 1.26 1.26 0 01-.354-.896c0-.347.118-.639.354-.875.25-.25.549-.375.896-.375s.639.125.875.375c.25.236.375.528.375.875 0 .348-.125.646-.375.896a1.189 1.189 0 01-.875.354z",fill:"currentColor"});t.a=function SvgAudienceMetricIconPageviews(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},468:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M10 18.333a.776.776 0 01-.5-.166 1.012 1.012 0 01-.312-.438 11.597 11.597 0 00-1-2.188c-.39-.68-.938-1.479-1.646-2.395-.708-.917-1.285-1.792-1.73-2.625-.43-.834-.645-1.84-.645-3.021 0-1.625.562-3 1.687-4.125C6.994 2.235 8.375 1.667 10 1.667s3 .569 4.125 1.708c1.14 1.125 1.709 2.5 1.709 4.125 0 1.264-.243 2.32-.73 3.166-.472.834-1.02 1.66-1.645 2.48-.75 1-1.32 1.833-1.709 2.5a11.765 11.765 0 00-.937 2.083.94.94 0 01-.334.458.814.814 0 01-.479.146zm0-2.979c.236-.472.5-.938.792-1.396.306-.458.75-1.07 1.333-1.833a19.693 19.693 0 001.459-2.146c.389-.667.583-1.493.583-2.48 0-1.152-.41-2.131-1.23-2.937-.805-.82-1.784-1.229-2.937-1.229-1.152 0-2.139.41-2.958 1.23-.806.805-1.208 1.784-1.208 2.937 0 .986.187 1.812.562 2.479.39.653.882 1.368 1.48 2.146.583.764 1.02 1.375 1.312 1.833.305.458.576.924.812 1.396zm0-5.77c.584 0 1.077-.202 1.48-.605.402-.403.604-.896.604-1.48 0-.582-.202-1.076-.604-1.478A2.012 2.012 0 0010 5.417c-.583 0-1.076.2-1.479.604A2.012 2.012 0 007.917 7.5c0 .583.201 1.076.604 1.479.403.403.896.604 1.48.604z",fill:"currentColor"});t.a=function SvgAudienceMetricIconCities(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},469:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M3.334 16.667c-.459 0-.855-.16-1.188-.48-.32-.333-.479-.729-.479-1.187V5c0-.458.16-.847.48-1.167.332-.333.728-.5 1.187-.5h13.333c.458 0 .847.167 1.167.5.333.32.5.709.5 1.167v10c0 .459-.167.854-.5 1.188-.32.32-.709.479-1.167.479H3.334zm0-1.667h8.75v-2.916h-8.75V15zm10.416 0h2.917V7.5H13.75V15zM3.334 10.417h8.75V7.5h-8.75v2.917z",fill:"currentColor"});t.a=function SvgAudienceMetricIconTopContent(e){return i.createElement("svg",r({viewBox:"0 0 20 20",fill:"none"},e),a)}},47:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a}));var i={BOXES:"boxes",COMPOSITE:"composite"},r={QUARTER:"quarter",HALF:"half",FULL:"full"},a="core/widgets"},470:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileCitiesMetric}));var i=n(1),r=n.n(i),a=n(9),o=n(347);function AudienceTileCitiesMetric(t){var n,i=t.TileIcon,r=t.title,c=t.topCities,l=(null==c||null===(n=c.dimensionValues)||void 0===n?void 0:n.filter(Boolean))||[],s=!!l.length;return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric googlesitekit-audience-segmentation-tile-metric--cities"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__icon"},e.createElement(i,null)),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__container"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__title"},r),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__content"},!s&&e.createElement(o.a,null),s&&l.map((function(t,n){var i;return e.createElement("div",{key:null==t?void 0:t.value,className:"googlesitekit-audience-segmentation-tile-metric__cities-metric"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__cities-metric-name"},null==t?void 0:t.value),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__cities-metric-value"},Object(a.B)((null==c||null===(i=c.metricValues[n])||void 0===i?void 0:i.value)/(null==c?void 0:c.total),{style:"percent",maximumFractionDigits:1})))})))))}AudienceTileCitiesMetric.propTypes={TileIcon:r.a.elementType.isRequired,title:r.a.string.isRequired,topCities:r.a.object}}).call(this,n(4))},471:function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return AudienceTilePagesMetric}));var r=n(1),a=n.n(r),o=n(0),c=n(157),l=n(2),s=n(3),u=n(24),d=n(29),g=n(13),f=n(7),m=n(8),p=n(35),h=n(250),v=n(472),b=n(247),E=n(88),_=n(18),O=n(9);function AudienceTilePagesMetric(t){var n=t.audienceTileNumber,r=t.audienceSlug,a=t.TileIcon,y=t.title,k=t.topContent,j=t.topContentTitles,S=t.isTopContentPartialData,A=Object(u.e)(),w=Object(_.a)(),T=m.f.googlesitekit_post_type.parameterName,C=Object(s.useSelect)((function(e){return!e(m.r).hasCustomDimensions(T)})),N=Object(s.useSelect)((function(e){return e(f.a).hasScope(m.h)})),D=Object(c.a)(e.location.href,{notification:"audience_segmentation",widgetArea:E.AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION}),R=Object(c.a)(e.location.href,{widgetArea:E.AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION}),x=Object(s.useSelect)((function(e){return e(d.a).getValue(m.d,"isAutoCreatingCustomDimensionsForAudience")})),M=Object(s.useSelect)((function(e){return e(m.r).isCreatingCustomDimension(T)})),I=Object(s.useSelect)((function(e){return e(m.r).isFetchingSyncAvailableCustomDimensions()})),B=Object(s.useSelect)((function(e){return e(m.r).getCreateCustomDimensionError(T)})),P=Object(s.useSelect)((function(e){return e(m.r).getPropertyID()})),L=Object(s.useDispatch)(m.r).clearError,F=Object(s.useDispatch)(d.a).setValues,z=Object(s.useDispatch)(f.a),W=z.setPermissionScopeError,V=z.clearPermissionScopeError,U=Object(s.useSelect)((function(e){return e(d.a).getValue(m.d,"isRetrying")})),G=Object(s.useSelect)((function(e){return e(d.a).getValue(m.d,"autoSubmit")})),H=Object(s.useSelect)((function(e){return e(g.c).getSetupErrorCode()})),q=Object(s.useDispatch)(g.c).setSetupErrorCode,K=G&&"access_denied"===H,Y=Object(o.useCallback)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.isRetrying;F(m.d,{autoSubmit:!0,isRetrying:t}),N||W({code:p.a,message:Object(l.__)("Additional permissions are required to create new audiences in Analytics.","google-site-kit"),data:{status:403,scopes:[m.h],skipModal:!0,skipDefaultErrorNotifications:!0,redirectURL:D,errorRedirectURL:R}})}),[N,D,R,W,F]),X=Object(o.useCallback)((function(){F(m.d,{autoSubmit:!1,isRetrying:!1}),q(null),V(),L("createCustomDimension",[P,m.f.googlesitekit_post_type])}),[L,V,P,q,F]),Z=[u.b,u.c].includes(A),$=x||M||I;return i.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric googlesitekit-audience-segmentation-tile-metric--top-content"},i.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__icon"},i.createElement(a,null)),i.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__container"},i.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__title"},y,!Z&&S&&i.createElement(h.a,{className:"googlesitekit-audience-segmentation-partial-data-badge",label:Object(l.__)("Partial data","google-site-kit"),onTooltipOpen:function(){Object(O.I)("".concat(w,"_audiences-tile"),"view_top_content_partial_data_tooltip",r)},tooltipTitle:Object(l.__)("Still collecting full data for this timeframe, partial data is displayed for this metric","google-site-kit")})),i.createElement(v.a,{topContentTitles:j,topContent:k,isTopContentPartialData:S,hasCustomDimension:!C,onCreateCustomDimension:Y,isSaving:$}),0===n&&(B&&!$||U&&!x||K)&&i.createElement(b.a,{apiErrors:[B],title:Object(l.__)("Failed to enable metric","google-site-kit"),description:Object(l.__)("Oops! Something went wrong. Retry enabling the metric.","google-site-kit"),onRetry:function(){return Y({isRetrying:!0})},onCancel:X,inProgress:$,hasOAuthError:K,trackEventCategory:"".concat(w,"_audiences-top-content-cta")})))}AudienceTilePagesMetric.propTypes={audienceTileNumber:a.a.number,audienceSlug:a.a.string.isRequired,TileIcon:a.a.elementType.isRequired,title:a.a.string.isRequired,topContent:a.a.object,topContentTitles:a.a.object,isTopContentPartialData:a.a.bool}}).call(this,n(28),n(4))},472:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTilePagesMetricContent}));var i=n(1),r=n.n(i),a=n(2),o=n(3),c=n(24),l=n(7),s=n(8),u=n(347),d=n(20),g=n(348),f=n(9),m=n(113),p=n(18),h=n(34),v=n(473),b=Object(m.a)(v.a);function AudienceTilePagesMetricContent(t){var n,i=t.topContentTitles,r=t.topContent,m=t.isTopContentPartialData,v=t.hasCustomDimension,E=t.onCreateCustomDimension,_=t.isSaving,O=Object(p.a)(),y=Object(h.a)(),k=Object(c.e)(),j=[c.b,c.c].includes(k),S=(null==r||null===(n=r.dimensionValues)||void 0===n?void 0:n.filter(Boolean))||[],A=!!S.length,w=Object(o.useSelect)((function(e){return e(l.a).getDateRangeDates({offsetDays:s.g})}));function ContentLinkComponent(t){var n=t.content,r=i[null==n?void 0:n.value],a=null==n?void 0:n.value,c=Object(o.useSelect)((function(e){return y?null:e(s.r).getServiceReportURL("all-pages-and-screens",{filters:{unifiedPagePathScreen:a},dates:w})}));return y?e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__top-content-metric-name"},r):e.createElement(d.a,{href:c,title:r,external:!0,hideExternalIndicator:!0},r)}return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__content"},!v&&e.createElement(b,{onClick:function(){Object(f.I)("".concat(O,"_audiences-top-content-cta"),"create_custom_dimension").finally(E)},isSaving:_,onInView:function(){Object(f.I)("".concat(O,"_audiences-top-content-cta"),"view_cta")}}),v&&!A&&e.createElement(u.a,null),v&&A&&S.map((function(t,n){var i;return e.createElement("div",{key:null==t?void 0:t.value,className:"googlesitekit-audience-segmentation-tile-metric__page-metric-container"},e.createElement(ContentLinkComponent,{content:t}),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-metric__page-metric-value"},Object(f.B)(null==r||null===(i=r.metricValues[n])||void 0===i?void 0:i.value)))})),j&&m&&e.createElement(g.a,{content:Object(a.__)("Still collecting full data for this timeframe, partial data is displayed for this metric","google-site-kit")}))}AudienceTilePagesMetricContent.propTypes={topContentTitles:r.a.object,topContent:r.a.object,isTopContentPartialData:r.a.bool,hasCustomDimension:r.a.bool,onCreateCustomDimension:r.a.func,isSaving:r.a.bool}}).call(this,n(4))},473:function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(0),o=n(2),c=n(10),l=Object(a.forwardRef)((function(t,n){var i=t.onClick,r=t.isSaving;return e.createElement("div",{ref:n,className:"googlesitekit-audience-segmentation-tile-metric__no-data"},Object(o.__)("No data to show","google-site-kit"),e.createElement("p",null,Object(o.__)("Update Analytics to track metric","google-site-kit")),e.createElement(c.SpinnerButton,{danger:!0,onClick:i,isSaving:r,disabled:r},Object(o.__)("Update","google-site-kit")))}));l.propTypes={onClick:r.a.func.isRequired,isSaving:r.a.bool},t.a=l}).call(this,n(4))},474:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileZeroData}));var i=n(1),r=n.n(i),a=n(18),o=n(113),c=n(9),l=n(475),s=Object(o.a)(l.a);function AudienceTileZeroData(t){var n=t.Widget,i=t.audienceSlug,r=t.title,o=t.infoTooltip,l=t.isMobileBreakpoint,u=t.isTileHideable,d=t.onHideTile,g=Object(a.a)();return e.createElement(s,{Widget:n,audienceSlug:i,title:r,infoTooltip:o,isMobileBreakpoint:l,isTileHideable:u,onHideTile:function(){Object(c.I)("".concat(g,"_audiences-tile"),"temporarily_hide",i).finally(d)},onInView:function(){Object(c.I)("".concat(g,"_audiences-tile"),"view_tile_collecting_data",i)}})}AudienceTileZeroData.propTypes={Widget:r.a.elementType.isRequired,audienceSlug:r.a.string.isRequired,title:r.a.string.isRequired,infoTooltip:r.a.oneOfType([r.a.string,r.a.element]),isMobileBreakpoint:r.a.bool,isTileHideable:r.a.bool,onHideTile:r.a.func}}).call(this,n(4))},475:function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(0),o=n(18),c=n(9),l=n(132),s=n(476),u=n(478),d=Object(a.forwardRef)((function(t,n){var i=t.Widget,r=t.audienceSlug,a=t.title,d=t.infoTooltip,g=t.isMobileBreakpoint,f=t.isTileHideable,m=t.onHideTile,p=Object(o.a)();return e.createElement(i,{ref:n,noPadding:!0},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__zero-data-container"},!g&&e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__header"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__header-title"},a,d&&e.createElement(l.a,{title:d,tooltipClassName:"googlesitekit-info-tooltip__content--audience",onOpen:function(){return Object(c.I)("".concat(p,"_audiences-tile"),"view_tile_tooltip",r)}}))),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile__zero-data-content"},e.createElement(s.a,null),f&&e.createElement(u.a,{onHideTile:m})))))}));d.propTypes={Widget:r.a.elementType.isRequired,audienceSlug:r.a.string.isRequired,title:r.a.string.isRequired,infoTooltip:r.a.oneOfType([r.a.string,r.a.element]),isMobileBreakpoint:r.a.bool,isTileHideable:r.a.bool,onHideTile:r.a.func},t.a=d}).call(this,n(4))},476:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileCollectingData}));var i=n(0),r=n(2),a=n(477);function AudienceTileCollectingData(){return e.createElement(i.Fragment,null,e.createElement(a.a,{className:"googlesitekit-audience-segmentation-tile__zero-data-image"}),e.createElement("p",{className:"googlesitekit-audience-segmentation-tile__zero-data-description"},Object(r.__)("Site Kit is collecting data for this group.","google-site-kit")))}}).call(this,n(4))},477:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M54.323 93.529c21.908 0 39.667-17.76 39.667-39.667 0-21.908-17.76-39.667-39.667-39.667s-39.667 17.76-39.667 39.667 17.76 39.667 39.667 39.667z",fill:"#EBEEF0"}),o=i.createElement("path",{d:"M37.717 38.469l52.162 18.445a12.955 12.955 0 0016.533-7.896v0a12.948 12.948 0 00-3.553-13.955 12.952 12.952 0 00-4.349-2.582L87.023 28.42l-.014.042c.3-1.877.323-3.787.07-5.67",stroke:"#161B18",strokeWidth:2.578,strokeLinecap:"round",strokeLinejoin:"round"}),c=i.createElement("path",{d:"M70.947 38.469L18.785 56.914A12.955 12.955 0 012.25 49.018v0a12.955 12.955 0 017.902-16.537L21.64 28.42l.016.042a19.431 19.431 0 01-.07-5.668",stroke:"#161B18",strokeWidth:2.578,strokeLinecap:"round",strokeLinejoin:"round"}),l=i.createElement("path",{d:"M27.61.51l.497 83.126a39.625 39.625 0 0053.598-1.071l1.19-82.11L27.61.51z",fill:"#70B2F5"}),s=i.createElement("path",{d:"M27.61.51l.497 83.126a39.625 39.625 0 0053.598-1.071l1.19-82.11L27.61.51z",fill:"#77AD8C"}),u=i.createElement("path",{d:"M82.648 17.112l.24-16.66h-5.853l-1.033 86.633a39.782 39.782 0 005.702-4.526l.944-65.447z",fill:"#77AD8C",opacity:.2}),d=i.createElement("path",{d:"M44.723 46.377c4.916 3.946 11.868 4.892 19.218.273",stroke:"#161B18",strokeWidth:1.785,strokeLinecap:"round",strokeLinejoin:"round"}),g=i.createElement("path",{d:"M80.38 24.992c0-9.563-11.446-17.056-26.059-17.056-14.613 0-26.06 7.49-26.06 17.056h52.12z",fill:"#7B807D"}),f=i.createElement("path",{d:"M39.588 39.736c8.143 0 14.744-6.6 14.744-14.744 0-8.143-6.601-14.744-14.744-14.744s-14.744 6.601-14.744 14.744 6.601 14.744 14.744 14.744z",fill:"#fff",stroke:"#464B48",strokeWidth:4.363,strokeLinejoin:"round"}),m=i.createElement("path",{d:"M69.076 39.736c8.143 0 14.745-6.6 14.745-14.744 0-8.143-6.602-14.744-14.745-14.744-8.143 0-14.744 6.601-14.744 14.744s6.601 14.744 14.744 14.744z",fill:"#fff",stroke:"#464B48",strokeWidth:4.363,strokeLinejoin:"round"}),p=i.createElement("path",{d:"M30.86 24.992a8.739 8.739 0 018.726-8.726M60.348 24.992a8.738 8.738 0 018.726-8.726",stroke:"#B8BDB9",strokeWidth:3.173,strokeLinejoin:"round"});t.a=function SvgAudienceSegmentationCollectingData(e){return i.createElement("svg",r({viewBox:"0 0 109 94",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p)}},478:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileCollectingDataHideable}));var i=n(1),r=n.n(i),a=n(0),o=n(2),c=n(20),l=n(479);function AudienceTileCollectingDataHideable(t){var n=t.onHideTile;return e.createElement(a.Fragment,null,e.createElement("p",{className:"googlesitekit-audience-segmentation-tile__zero-data-description"},Object(o.__)("You can hide this group until data is available.","google-site-kit")),e.createElement(c.a,{secondary:!0,linkButton:!0,className:"googlesitekit-audience-segmentation-tile-hide-cta",onClick:n,leadingIcon:e.createElement(l.a,{width:22,height:22})},Object(o.__)("Temporarily hide","google-site-kit")))}AudienceTileCollectingDataHideable.propTypes={onHideTile:r.a.func.isRequired}}).call(this,n(4))},479:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M12.907 10.523l-1.088-1.088c.113-.587-.056-1.137-.506-1.65-.45-.512-1.031-.712-1.743-.6L8.482 6.098c.213-.1.425-.175.638-.225.225-.05.462-.075.712-.075.938 0 1.731.331 2.381.994.663.65.994 1.443.994 2.381 0 .25-.025.487-.075.712-.05.213-.125.425-.225.638zm2.4 2.362l-1.088-1.05a8.11 8.11 0 001.257-1.18 6.69 6.69 0 00.956-1.482 7.287 7.287 0 00-2.7-3c-1.162-.75-2.462-1.125-3.9-1.125-.362 0-.719.025-1.069.075-.35.05-.693.125-1.031.225L6.57 4.185a7.851 7.851 0 011.575-.468 8.22 8.22 0 011.687-.169c1.887 0 3.569.525 5.044 1.575a8.87 8.87 0 013.206 4.05 8.766 8.766 0 01-1.144 2.063 8.16 8.16 0 01-1.631 1.65zm.375 4.613l-3.15-3.113a9.803 9.803 0 01-1.331.32 9.675 9.675 0 01-1.369.093c-1.887 0-3.569-.519-5.044-1.556a8.983 8.983 0 01-3.206-4.07 8.663 8.663 0 01.994-1.837c.4-.575.856-1.087 1.369-1.537l-2.063-2.1 1.05-1.05 13.8 13.8-1.05 1.05zM4.995 6.848A8.054 8.054 0 004 7.917c-.3.387-.556.806-.769 1.256a7.46 7.46 0 002.681 3.019c1.175.737 2.482 1.106 3.919 1.106.25 0 .494-.013.731-.038.238-.037.481-.075.731-.112l-.675-.713a5.889 5.889 0 01-.393.094 3.96 3.96 0 01-.394.019c-.937 0-1.737-.325-2.4-.975-.65-.662-.975-1.463-.975-2.4 0-.138.006-.269.019-.394.025-.125.056-.256.094-.394L4.995 6.848z",fill:"currentColor"});t.a=function SvgVisibility(e){return i.createElement("svg",r({viewBox:"0 0 19 19",fill:"none"},e),a)}},480:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceTileError}));var i=n(1),r=n.n(i),a=n(35),o=n(481),c=n(113),l=n(18),s=n(9),u=Object(c.a)(o.a);function AudienceTileError(t){var n=t.audienceSlug,i=t.errors,r=Object(l.a)(),o=i.some((function(e){return Object(a.e)(e)}));return e.createElement(u,{errors:i,onInView:function(){var e=o?"insufficient_permissions_error":"data_loading_error";Object(s.I)("".concat(r,"_audiences-tile"),e,n)},onRetry:function(){Object(s.I)("".concat(r,"_audiences-tile"),"data_loading_error_retry",n)},onRequestAccess:function(){Object(s.I)("".concat(r,"_audiences-tile"),"insufficient_permissions_error_request_access",n)}})}AudienceTileError.propTypes={audienceSlug:r.a.string.isRequired,errors:r.a.array.isRequired}}).call(this,n(4))},481:function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(0),o=n(2),c=n(35),l=n(482),s=n(135),u=n(346),d=Object(a.forwardRef)((function(t,n){var i=t.errors,r=t.onRetry,a=t.onRequestAccess,d=i.some((function(e){return Object(c.e)(e)}));return e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error",ref:n},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error__container"},e.createElement(l.a,{className:"googlesitekit-audience-segmentation-tile-error__image"}),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error__body"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error__message"},e.createElement("h3",{className:"googlesitekit-audience-segmentation-tile-error__title"},d?Object(o.__)("Insufficient permissions","google-site-kit"):Object(o.__)("Data loading failed","google-site-kit"))),e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-error__actions"},e.createElement(s.a,{moduleSlug:"analytics-4",error:i,GetHelpLink:d?u.a:void 0,hideGetHelpLink:!d,buttonVariant:"danger",onRetry:r,onRequestAccess:a})))))}));d.propTypes={errors:r.a.array.isRequired,onRetry:r.a.func.isRequired,onRequestAccess:r.a.func.isRequired},t.a=d}).call(this,n(4))},482:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M11.755 110.799a44.227 44.227 0 004.457 4.095c14.958 11.965 27.316 11.946 44.424 9.762 15.862-2.025 17.862-10.923 35.362-10.923 17.5 0 25.665 6.38 46 5s38.081-23.761 44.757-41.774c9.833-26.528-4.519-57.596-24.82-66.096-18.699-8.5-31.437.01-51.437-3.63C84.998 2.59 79.998-4.051 60.636 4c-20.53 8.701-20.455 23.533-32.699 38.667C18.974 53.747 4.956 56.312.734 76.959c-2.673 13.07 3.077 25.467 11.021 33.84z",fill:"#F3F5F7"}),o=i.createElement("path",{d:"M96.07 141.772c39.765 0 72-2.014 72-4.5 0-2.485-32.235-4.5-72-4.5-39.764 0-72 2.015-72 4.5 0 2.486 32.236 4.5 72 4.5z",fill:"#161B18",opacity:.1}),c=i.createElement("path",{d:"M96.07 141.772c39.765 0 72-2.014 72-4.5 0-2.485-32.235-4.5-72-4.5-39.764 0-72 2.015-72 4.5 0 2.486 32.236 4.5 72 4.5z",fill:"#CBD0D3"}),l=i.createElement("path",{d:"M72.657 62.826c2.14 7.94 9.59 13.553 17.488 15.847 7.898 2.295 16.295 1.846 24.51 1.435 3.883-.192 7.855-.363 11.576.764 3.72 1.127 7.202 3.821 8.191 7.58.568 2.16.147 4.49-.959 6.424",stroke:"#161B18",strokeWidth:1.7,strokeLinecap:"round",strokeLinejoin:"round"}),s=i.createElement("path",{d:"M57.306 76.348c-1.237 19.582-8.053 37.15-3.497 59.224h-5.188M63.171 135.572h-5.19l9.173-59.224",stroke:"#161B18",strokeWidth:1.705,strokeLinecap:"round",strokeLinejoin:"round"}),u=i.createElement("path",{d:"M40.754 70.174c-4.429 2-12.93 6.455-12.929 15.5",stroke:"#000",strokeWidth:1.7,strokeLinecap:"round"}),d=i.createElement("path",{d:"M76.804 102.64l-3.602.327-21.976 1.988-12.845 1.16-1.22-28.616-2.118-49.8 40.092-1.46.81 37.088.109 4.993.745 34.204.005.116z",fill:"#CBD0D3"}),g=i.createElement("path",{d:"M73.203 102.967l2.067-.188 1.537-.139-1.671-76.403-2.303.105 1.24 72.43-35.862 3.393.125 2.852.048 1.095 12.845-1.159 21.977-1.989-.003.003z",fill:"#999F9B"}),f=i.createElement("path",{d:"M27.827 85.674c0 5.264 5.99 9.06 10.262 7.203",stroke:"#000",strokeWidth:1.7,strokeLinecap:"round"}),m=i.createElement("path",{d:"M145.07 26.773l-28 108.499",stroke:"#7B807D",strokeWidth:4.393,strokeMiterlimit:10,strokeLinecap:"round"}),p=i.createElement("path",{d:"M143.121 18.374L101.519 60.22a4.387 4.387 0 00-1.124 4.247 4.395 4.395 0 003.116 3.096l57.038 15.105a4.394 4.394 0 005.365-5.402l-15.439-56.943a4.393 4.393 0 00-7.354-1.949z",fill:"#E77D5B"}),h=i.createElement("path",{d:"M138.137 42.556l2.44-9.216 4.756 1.26-2.44 9.215-3.501 11.163-3.78-1.001 2.525-11.421zm-3.301 22.078a3.372 3.372 0 01-2.088-1.553 3.37 3.37 0 01-.316-2.584c.24-.906.753-1.588 1.541-2.046a3.375 3.375 0 012.584-.316c.906.24 1.588.754 2.046 1.542.458.788.567 1.635.327 2.54a3.375 3.375 0 01-1.553 2.09c-.788.457-1.635.567-2.541.327z",fill:"#962C0A"}),v=i.createElement("path",{d:"M133.461 94.876a8.345 8.345 0 01-4.565 3.774c-4.063 1.39-9.013-.82-10.694-4.77",stroke:"#161B18",strokeWidth:1.7,strokeLinecap:"round",strokeLinejoin:"round"});t.a=function SvgAnalyticsAudienceSegmentationTileError(e){return i.createElement("svg",r({viewBox:"0 0 190 142",fill:"none"},e),a,o,c,l,s,u,d,g,f,m,p,h,v)}},483:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return MaybePlaceholderTile}));var i=n(1),r=n.n(i),a=n(177),o=n(484);function MaybePlaceholderTile(t){var n=t.Widget,i=t.loading,r=t.allTilesError,c=t.visibleAudienceCount;return!1!==r&&!i||1!==c?null:i?e.createElement(n,{noPadding:!0},e.createElement(a.a,null)):e.createElement(o.a,{Widget:n})}MaybePlaceholderTile.propTypes={Widget:r.a.elementType.isRequired,loading:r.a.bool.isRequired,allTilesError:r.a.bool,visibleAudienceCount:r.a.number.isRequired}}).call(this,n(4))},484:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return PlaceholderTile}));var i=n(11),r=n.n(i),a=n(38),o=n(2),c=n(3),l=n(13),s=n(23),u=n(7),d=n(8),g=n(20),f=n(349),m=n(40);function PlaceholderTile(t){var n=t.Widget,i=Object(c.useSelect)((function(e){var t=e(u.a).getConfiguredAudiences();return e(d.r).getConfigurableAudiences().some((function(e){return"DEFAULT_AUDIENCE"!==e.audienceType&&!t.includes(e.name)}))})),p=Object(c.useSelect)((function(e){return e(l.c).getGoogleSupportURL({path:"/analytics/answer/12799087"})})),h=Object(c.useDispatch)(s.b).setValue,v=e.createElement(g.a,{secondary:!0,href:p,external:!0});return e.createElement(n,{className:"googlesitekit-audience-segmentation-tile-placeholder"},e.createElement("div",{className:"googlesitekit-audience-segmentation-tile-placeholder__container"},e.createElement(f.a,{className:"googlesitekit-audience-segmentation-tile-placeholder__image"}),e.createElement("div",{className:r()("googlesitekit-audience-segmentation-tile-placeholder__body",{"googlesitekit-audience-segmentation-tile-placeholder__body--without-selectable-audiences":!i})},e.createElement("h3",{className:"googlesitekit-audience-segmentation-tile-placeholder__title"},i?Object(o.__)("Compare your group to other groups","google-site-kit"):Object(o.__)("Create more visitor groups","google-site-kit")),e.createElement("p",{className:"googlesitekit-audience-segmentation-tile-placeholder__description"},i?Object(a.a)(Object(o.__)("<SelectGroupLink>Select</SelectGroupLink> another group to compare with your current group or learn more about how to group site visitors in <AnalyticsLink>Analytics</AnalyticsLink>","google-site-kit"),{AnalyticsLink:v,SelectGroupLink:e.createElement(g.a,{secondary:!0,onClick:function(){return h(m.i,!0)}})}):Object(a.a)(Object(o.__)("Learn more about how to group site visitors in <AnalyticsLink>Analytics</AnalyticsLink>","google-site-kit"),{AnalyticsLink:v})))))}}).call(this,n(4))},485:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var i=n(6),r=n.n(i),a=n(3),o=n(7),c=n(8);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t){return Object(a.useSelect)((function(n){return t.every((function(t){var i=n(c.r).getPartialDataSiteKitAudience(t);if(void 0===i)return!1;var r={};return i?r.newVsReturning="new-visitors"===i.audienceSlug?"new":"returning":r.audienceResourceName=t,n(c.r).hasFinishedResolution("getReport",[s(s({},e),{},{dimensionFilters:s(s({},e.dimensionFilters),r)})])}))}))}function d(e,t){return Object(a.useSelect)((function(n){return t.reduce((function(t,i){var r=n(c.r).getPartialDataSiteKitAudience(i);if(void 0===r)return t;var a={};r?a.newVsReturning="new-visitors"===r.audienceSlug?"new":"returning":a.audienceResourceName=i;var o=n(c.r).getErrorForSelector("getReport",[s(s({},e),{},{dimensionFilters:s(s({},e.dimensionFilters),a)})]);return o&&(t[i]=o),t}),{})}))}function g(e){var t,n,i,r,l=e.isSiteKitAudiencePartialData,g=e.siteKitAudiences,f=e.otherAudiences,m=Object(a.useSelect)((function(e){return e(o.a).getConfiguredAudiences()})),p={audienceResourceName:m},h=Object(a.useSelect)((function(e){return e(o.a).getDateRangeDates({offsetDays:c.g,compare:!0})})),v=h.startDate,b=h.endDate,E=void 0===l?void 0:f.length>0||!1===l,_=g.length>0&&l,O=s(s({},h),{},{dimensions:[{name:"audienceResourceName"}],dimensionFilters:p,metrics:[{name:"totalUsers"},{name:"sessionsPerUser"},{name:"screenPageViewsPerSession"},{name:"screenPageViews"}]}),y=Object(a.useInViewSelect)((function(e){if(void 0!==E)return E?e(c.r).getReport(O):null}),[E,O]),k=Object(a.useSelect)((function(e){if(void 0!==E)return!E||e(c.r).hasFinishedResolution("getReport",[O])})),j=Object(a.useSelect)((function(e){if(void 0!==E)return E?e(c.r).getErrorForSelector("getReport",[O]):null})),S=s(s({},h),{},{dimensions:[{name:"newVsReturning"}],dimensionFilters:{newVsReturning:["new","returning"]},metrics:[{name:"totalUsers"},{name:"sessionsPerUser"},{name:"screenPageViewsPerSession"},{name:"screenPageViews"}]}),A=Object(a.useInViewSelect)((function(e){if(void 0!==_)return _?e(c.r).getReport(S):null}),[_,S]),w=Object(a.useSelect)((function(e){if(void 0!==_)return!_||e(c.r).hasFinishedResolution("getReport",[S])})),T=Object(a.useSelect)((function(e){if(void 0!==_)return _?e(c.r).getErrorForSelector("getReport",[S]):null})),C={startDate:v,endDate:b,metrics:[{name:"screenPageViews"}]},N=Object(a.useInViewSelect)((function(e){return e(c.r).getReport(C)})),D=Object(a.useSelect)((function(e){return e(c.r).hasFinishedResolution("getReport",[C])})),R=Object(a.useSelect)((function(e){return e(c.r).getErrorForSelector("getReport",[C])})),x=Number(null==N||null===(t=N.totals)||void 0===t||null===(n=t[0])||void 0===n||null===(i=n.metricValues)||void 0===i||null===(r=i[0])||void 0===r?void 0:r.value)||0,M={startDate:v,endDate:b,dimensions:["city"],metrics:[{name:"totalUsers"}],orderby:[{metric:{metricName:"totalUsers"},desc:!0}],limit:4},I=Object(a.useInViewSelect)((function(e){return e(c.r).getReportForAllAudiences(M,m)})),B=u(M,m),P=d(M,m),L={startDate:v,endDate:b,dimensions:["pagePath"],metrics:[{name:"screenPageViews"}],dimensionFilters:{"customEvent:googlesitekit_post_type":{filterType:"stringFilter",matchType:"EXACT",value:"post"}},orderby:[{metric:{metricName:"screenPageViews"},desc:!0}],limit:3},F=Object(a.useInViewSelect)((function(e){return e(c.r).getReportForAllAudiences(L,m)})),z=u(L,m),W=d(L,m),V={startDate:v,endDate:b,dimensions:["pagePath","pageTitle"],metrics:[{name:"screenPageViews"}],dimensionFilters:{"customEvent:googlesitekit_post_type":{filterType:"stringFilter",matchType:"EXACT",value:"post"}},orderby:[{metric:{metricName:"screenPageViews"},desc:!0}],limit:15};return{report:y,reportLoaded:k,reportError:j,siteKitAudiencesReport:A,siteKitAudiencesReportLoaded:w,siteKitAudiencesReportError:T,totalPageviews:x,totalPageviewsReportLoaded:D,totalPageviewsReportError:R,topCitiesReport:I,topCitiesReportLoaded:B,topCitiesReportErrors:P,topContentReport:F,topContentReportLoaded:z,topContentReportErrors:W,topContentPageTitlesReport:Object(a.useInViewSelect)((function(e){return e(c.r).getReportForAllAudiences(V,m)})),topContentPageTitlesReportLoaded:u(V,m),topContentPageTitlesReportErrors:d(V,m)}}},486:function(e,t,n){"use strict";(function(e){var i=n(0),r=n(38),a=n(2),o=n(3),c=n(349),l=n(20),s=n(19),u=n(350),d=n(32),g=n(13),f=n(23),m=n(7),p=n(40),h=n(18),v=n(34),b=n(9),E=Object(i.forwardRef)((function(t,n){var i=Object(h.a)(),E=Object(v.a)(),_=Object(o.useSelect)((function(e){return e(m.a).didSetAudiences()})),O=Object(o.useSelect)((function(e){return e(s.a).getModuleIcon("analytics-4")})),y=Object(o.useSelect)((function(e){return e(g.c).getAdminURL("googlesitekit-settings")})),k=Object(o.useDispatch)(f.b).setValue,j=Object(o.useDispatch)(d.a).navigateTo,S=_?"no-longer-available":"none-selected";function A(){Object(b.I)("".concat(i,"_audiences-no-audiences"),"select_groups",S).finally((function(){k(p.i,!0)}))}return e.createElement(u.a,{ref:n,className:"googlesitekit-no-audience-banner",Icon:O,SVGGraphic:c.a},e.createElement("p",null,_&&Object(r.a)(Object(a.__)("It looks like your visitor groups aren’t available anymore. <a>Select other groups</a>.","google-site-kit"),{a:e.createElement(l.a,{secondary:!0,onClick:A})}),!_&&Object(r.a)(Object(a.__)("You don’t have any visitor groups selected. <a>Select groups</a>.","google-site-kit"),{a:e.createElement(l.a,{secondary:!0,onClick:A})})),!E&&e.createElement("p",null,Object(r.a)(Object(a.__)("You can deactivate this widget in <a>Settings</a>.","google-site-kit"),{a:e.createElement(l.a,{secondary:!0,onClick:function(){Object(b.I)("".concat(i,"_audiences-no-audiences"),"change_settings",S).finally((function(){j("".concat(y,"#/admin-settings"))}))}})})))}));t.a=E}).call(this,n(4))},487:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Panel}));var i=n(6),r=n.n(i),a=n(0),o=n(3),c=n(18),l=n(9),s=n(40),u=n(29),d=n(23),g=n(7),f=n(8),m=n(488),p=n(495),h=n(497),v=n(498),b=n(499),E=n(117),_=n(500);function Panel(){var t=Object(c.a)(),n=Object(o.useSelect)((function(e){return e(d.b).getValue(s.i)})),i=Object(o.useSelect)((function(e){return e(f.r).isFetchingSyncAvailableAudiences()})),O=Object(o.useInViewSelect)((function(e){var t=e(f.r).getConfigurableAudiences,n=(0,e(g.a).getConfiguredAudiences)()||[],i=t()||[];return i.length&&n.length?i.filter((function(e){var t=e.name;return n.includes(t)})).map((function(e){return e.name})):[]})),y=Object(o.useSelect)((function(e){return e(u.a).getValue(s.c,"autoSubmit")})),k=Object(o.useDispatch)(u.a).setValues,j=Object(o.useDispatch)(d.b).setValue,S=Object(a.useCallback)((function(){var e;k(s.h,(e={},r()(e,s.f,O),r()(e,s.g,!1),e)),Object(l.I)("".concat(t,"_audiences-sidebar"),"audiences_sidebar_view")}),[O,k,t]),A=Object(a.useCallback)((function(){n&&(j(s.i,!1),j(s.e,!1))}),[j,n]);return e.createElement(E.e,{className:"googlesitekit-audience-selection-panel",closePanel:A,isOpen:n||y,isLoading:i,onOpen:S},e.createElement(v.a,{closePanel:A}),e.createElement(m.a,{savedItemSlugs:O}),e.createElement(b.a,null),e.createElement(p.a,null),e.createElement(_.a,null),e.createElement(h.a,{closePanel:A,isOpen:n,savedItemSlugs:O}))}}).call(this,n(4))},488:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceItems}));var i=n(6),r=n.n(i),a=n(5),o=n.n(a),c=n(16),l=n.n(c),s=n(15),u=n.n(s),d=n(1),g=n.n(d),f=n(812),m=n(2),p=n(0),h=n(3),v=n(40),b=n(23),E=n(7),_=n(8),O=n(9),y=n(489),k=n(117),j=n(490),S=n(491),A=n(34),w=n(493);function T(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function C(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?T(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):T(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function AudienceItems(t){var n=t.savedItemSlugs,i=void 0===n?[]:n,a=Object(p.useState)(!0),c=u()(a,2),s=c[0],d=c[1],g=Object(h.useDispatch)(E.a).setExpirableItemTimers,T=Object(h.useDispatch)(_.r).syncAvailableAudiences,N=Object(A.a)(),D=Object(h.useSelect)((function(e){return e(b.b).getValue(v.i)})),R=Object(h.useSelect)((function(e){return e(_.r).isFetchingSyncAvailableAudiences()}));Object(p.useEffect)((function(){if(s&&D){var e=function(){var e=l()(o.a.mark((function e(){return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,T();case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();d(!1),e()}}),[s,D,T]);var x=Object(h.useInViewSelect)((function(e){var t=e(_.r),n=t.getConfigurableAudiences,i=t.getReport,r=t.getAudiencesUserCountReportOptions,a=t.getConfigurableSiteKitAndOtherAudiences,o=t.hasAudiencePartialData,c=n();if(void 0!==c){if(!c.length)return[];var l=a(),s=u()(l,2),d=s[0],g=s[1],f=o(d),m=e(E.a).getDateRangeDates({offsetDays:_.g}),p=f&&i(C(C({},m),{},{metrics:[{name:"totalUsers"}],dimensions:[{name:"newVsReturning"}]})),h=!1===f||!0===f&&(null==g?void 0:g.length)>0?i(r(f?g:c)):{},v=(p||{}).rows,b=void 0===v?[]:v,O=(h||{}).rows,y=void 0===O?[]:O;return c.map((function(e){var t,n,i,r;return r="SITE_KIT_AUDIENCE"===e.audienceType&&f?k(b,"new-visitors"===e.audienceSlug?"new":"returning"):k(y,e.name),C(C({},e),{},{userCount:Number(null===(t=r)||void 0===t||null===(n=t.metricValues)||void 0===n||null===(i=n[0])||void 0===i?void 0:i.value)||0})}))}function k(e,t){return e.find((function(e){var n,i;return(null==e||null===(n=e.dimensionValues)||void 0===n||null===(i=n[0])||void 0===i?void 0:i.value)===t}))}})),M=function(e,t){var n=t.audienceType,i=t.description,a=t.displayName,o=t.name,c=t.userCount,l="";switch(n){case"DEFAULT_AUDIENCE":l=Object(m.__)("Created by default by Google Analytics","google-site-kit"),i="";break;case"SITE_KIT_AUDIENCE":l=Object(m.__)("Created by Site Kit","google-site-kit");break;case"USER_AUDIENCE":l=Object(m.__)("Already exists in your Analytics property","google-site-kit")}return C(C({},e),{},r()({},o,{title:a,subtitle:i,description:l,userCount:c,audienceType:n}))},I=null==x?void 0:x.filter((function(e){var t=e.name;return i.includes(t)})).reduce(M,{}),B=null==x?void 0:x.filter((function(e){var t=e.name;return!i.includes(t)})).reduce(M,{}),P=Object(h.useSelect)((function(e){if(void 0!==x){var t=e(E.a),n=t.hasFinishedResolution,i=t.hasExpirableItem;if(n("getExpirableItems"))return x.filter((function(e){var t=e.audienceType,n=e.name;return"DEFAULT_AUDIENCE"!==t&&!i("".concat(_.b).concat(n))})).map((function(e){var t=e.name;return"".concat(_.b).concat(t)}))}}));return Object(f.a)((function(){D&&void 0!==P&&P.length&&g(P.map((function(e){return{slug:e,expiresInSeconds:4*O.f}})))}),[D,g,P]),e.createElement(k.d,{availableItemsTitle:Object(m.__)("Additional groups","google-site-kit"),availableSavedItems:I,availableUnsavedItems:B,ItemComponent:R?j.a:y.a,savedItemSlugs:i,notice:e.createElement(p.Fragment,null,e.createElement(S.a,null),!N&&e.createElement(w.a,null))})}AudienceItems.propTypes={savedItemSlugs:g.a.array}}).call(this,n(4))},489:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceItem}));var i=n(6),r=n.n(i),a=n(15),o=n.n(a),c=n(1),l=n.n(c),s=n(0),u=n(2),d=n(3),g=n(40),f=n(29),m=n(7),p=n(8),h=n(9),v=n(302),b=n(117),E=n(250);function AudienceItem(t){var n=t.slug,i=t.title,a=t.description,c=t.subtitle,l=t.userCount,_=t.audienceType,O="".concat(p.b).concat(n),y=Object(d.useSelect)((function(e){return e(f.a).getValue(g.h,g.f)})),k=Object(d.useSelect)((function(e){return e(m.a).hasExpirableItem(O)})),j=Object(d.useSelect)((function(e){return e(m.a).isExpirableItemActive(O)})),S=Object(d.useSelect)((function(e){return e(p.r).getAudienceUserCountReportErrors()}))||[],A=o()(S,2),w=A[0],T=A[1],C=[];T&&C.push(T),w&&C.push(w);var N=Object(d.useDispatch)(f.a).setValues,D=Object(d.useSelect)((function(e){return e(m.a).isItemDismissed("audience-tile-".concat(n))})),R=Object(s.useCallback)((function(e){var t;N(g.h,(t={},r()(t,g.f,e.target.checked?y.concat([n]):y.filter((function(e){return e!==n}))),r()(t,g.g,!0),t))}),[y,N,n]),x="DEFAULT_AUDIENCE"!==_&&(!1===k||j),M=null==y?void 0:y.includes(n),I="audience-selection-checkbox-".concat(n);function ItemBadge(){return D?e.createElement(E.a,{label:Object(u.__)("Temporarily hidden","google-site-kit"),tooltipTitle:Object(u.__)("Site Kit is collecting data for this group. Once data is available the group will be added to your dashboard.","google-site-kit")}):x?e.createElement(v.a,null):null}return e.createElement(b.c,{id:I,slug:n,title:i,subtitle:c,description:a,isItemSelected:M,onCheckboxChange:R,suffix:C.length?"-":Object(h.B)(l),badge:(D||x)&&e.createElement(ItemBadge,null)})}AudienceItem.propTypes={slug:l.a.string.isRequired,title:l.a.string.isRequired,description:l.a.string.isRequired,subtitle:l.a.string,userCount:l.a.number.isRequired,audienceType:l.a.string.isRequired}}).call(this,n(4))},490:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceItemPreviewBlock}));var i=n(44);function AudienceItemPreviewBlock(){return e.createElement("div",{className:"googlesitekit-selection-panel__loading"},e.createElement("div",{className:"googlesitekit-selection-panel__loading-left"},e.createElement(i.a,{width:"90px",height:"20px",className:"googlesitekit-selection-panel__loading-item"}),e.createElement(i.a,{width:"293px",height:"15px",className:"googlesitekit-selection-panel__loading-item"})),e.createElement("div",{className:"googlesitekit-selection-panel__loading-right"},e.createElement(i.a,{width:"43px",height:"20px",className:"googlesitekit-selection-panel__loading-item"})))}}).call(this,n(4))},491:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AddGroupNotice}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(1),u=n.n(s),d=n(0),g=n(2),f=n(3),m=n(40),p=n(29),h=n(7),v=n(207),b=n(248),E=n(23),_=n(8);function AddGroupNotice(){var t=Object(d.useState)(!1),n=l()(t,2),i=n[0],a=n[1],c=Object(f.useInViewSelect)((function(e){return e(h.a).isItemDismissed(m.a)})),s=Object(f.useSelect)((function(e){return e(E.b).getValue(m.i)})),u=Object(f.useSelect)((function(e){return e(_.r).isFetchingSyncAvailableAudiences()})),O=Object(f.useSelect)((function(e){return e(p.a).getValue(m.h,m.f)})),y=Object(f.useDispatch)(h.a).dismissItem,k=Object(d.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,y(m.a);case 2:case"end":return e.stop()}}),e)}))),[y]);return Object(d.useEffect)((function(){Array.isArray(O)&&(O.length>1&&a(!0),s||1!==(null==O?void 0:O.length)||a(!1))}),[O,s,a]),c||i||u||!(null==O?void 0:O.length)?null:e.createElement(b.a,{className:"googlesitekit-audience-selection-panel__add-group-notice",content:Object(g.__)("By adding another group to your dashboard, you will be able to compare them and understand which content brings back users from each group","google-site-kit"),dismissLabel:Object(g.__)("Got it","google-site-kit"),Icon:v.a,onDismiss:k})}AddGroupNotice.propTypes={savedItemSlugs:u.a.array}}).call(this,n(4))},492:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{fill:"currentColor",d:"M10 18.333c-.458 0-.854-.16-1.188-.479a1.66 1.66 0 01-.479-1.188h3.334c0 .459-.167.855-.5 1.188-.32.32-.709.48-1.167.48zm-3.333-2.5v-1.667h6.666v1.667H6.667zm.208-2.5a6.47 6.47 0 01-2.292-2.292c-.555-.958-.833-2-.833-3.125 0-1.736.604-3.208 1.813-4.416C6.784 2.278 8.262 1.667 10 1.667c1.736 0 3.208.61 4.417 1.833 1.222 1.208 1.833 2.68 1.833 4.417a6.008 6.008 0 01-.854 3.124 6.303 6.303 0 01-2.271 2.292h-6.25zm.5-1.667h5.25a4.528 4.528 0 001.438-1.645c.347-.653.52-1.355.52-2.105 0-1.277-.444-2.36-1.333-3.25-.889-.888-1.972-1.333-3.25-1.333s-2.361.445-3.25 1.333c-.889.89-1.333 1.973-1.333 3.25 0 .75.166 1.452.5 2.105a4.722 4.722 0 001.458 1.645z"});t.a=function SvgLightbulb(e){return i.createElement("svg",r({fill:"none"},e),a)}},493:function(e,t,n){"use strict";(function(e,i){n.d(t,"a",(function(){return AudienceCreationNotice}));var r=n(5),a=n.n(r),o=n(16),c=n.n(o),l=n(15),s=n.n(l),u=n(2),d=n(0),g=n(157),f=n(3),m=n(18),p=n(9),h=n(40),v=n(29),b=n(13),E=n(7),_=n(23),O=n(8),y=n(35),k=n(20),j=n(104),S=n(260),A=n(258),w=n(494);function AudienceCreationNotice(){var t=Object(m.a)(),n=Object(d.useState)(!1),r=s()(n,2),o=r[0],l=r[1],T=Object(f.useInViewSelect)((function(e){var t=(0,e(O.r).getConfigurableAudiences)();if(void 0!==t)return t.length?t.filter((function(e){return"SITE_KIT_AUDIENCE"===e.audienceType})):[]})),C=Object(f.useDispatch)(E.a).dismissItem,N=Object(f.useDispatch)(_.b).setValue,D=Object(f.useInViewSelect)((function(e){return e(E.a).isItemDismissed(h.d)})),R=Object(f.useInViewSelect)((function(e){return e(E.a).isItemDismissed(h.b)})),x=Object(f.useInViewSelect)((function(e){return e(E.a).hasScope(O.h)})),M=Object(f.useSelect)((function(e){return e(_.b).getValue(h.i)})),I=Object(g.a)(e.location.href,{notification:"audience_segmentation"}),B=Object(f.useDispatch)(v.a).setValues,P=Object(f.useDispatch)(E.a).setPermissionScopeError,L=Object(f.useDispatch)(O.r),F=L.createAudience,z=L.syncAvailableAudiences,W=Object(f.useSelect)((function(e){return e(v.a).getValue(h.c,"autoSubmit")})),V=Object(f.useSelect)((function(e){return e(v.a).getValue(h.c,"audienceToCreate")})),U=Object(d.useState)([]),G=s()(U,2),H=G[0],q=G[1],K=Object(d.useCallback)(function(){var e=c()(a.a.mark((function e(t){var n,i;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(l(t),x){e.next=5;break}return B(h.c,{autoSubmit:!0,audienceToCreate:t}),P({code:y.a,message:Object(u.__)("Additional permissions are required to create a new audience in Analytics.","google-site-kit"),data:{status:403,scopes:[O.h],skipModal:!0,redirectURL:I}}),e.abrupt("return");case 5:return B(h.c,{autoSubmit:!1,audienceToCreate:void 0}),e.next=8,F(O.t[t]);case 8:return n=e.sent,i=n.error,q(i?[i]:[]),e.next=13,z();case 13:l(!1),i||N(h.e,!0);case 15:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[x,F,z,B,P,I,N]),Y=Object(f.useSelect)((function(e){return e(b.c).getSetupErrorCode()})),X=W&&"access_denied"===Y;Object(d.useEffect)((function(){function e(){return(e=c()(a.a.mark((function e(){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!x||!W){e.next=4;break}return N(h.i,!0),e.next=4,K(V);case 4:case"end":return e.stop()}}),e)})))).apply(this,arguments)}!function(){e.apply(this,arguments)}()}),[V,K,x,W,N]);var Z=!D&&(null==T?void 0:T.length)<2;if(Object(d.useEffect)((function(){M&&Z&&Object(p.I)("".concat(t,"_audiences-sidebar-create-audiences"),"view_notice")}),[M,Z,t]),Object(d.useEffect)((function(){!M||x||R||Object(p.I)("".concat(t,"_audiences-sidebar-create-audiences"),"view_oauth_notice")}),[x,R,M,t]),!Z)return null;var $=Object.keys(O.t).filter((function(e){return!T.some((function(t){return t.audienceSlug===e}))}));return i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice"},i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-header"},i.createElement("p",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-title"},Object(u.__)("Create groups suggested by Site Kit","google-site-kit")),i.createElement(k.a,{className:"googlesitekit-audience-selection-panel__audience-creation-notice-close",onClick:function(){C(h.d)},linkButton:!0},i.createElement(j.a,{width:"15",height:"15"}))),i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-body"},$&&$.map((function(e){return i.createElement("div",{key:e,className:"googlesitekit-audience-selection-panel__audience-creation-notice-audience"},i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-audience-details"},i.createElement("h3",null,O.t[e].displayName),i.createElement("p",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-audience-description"},O.t[e].description)),i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-audience-button"},i.createElement(S.b,{spinnerPosition:S.a.BEFORE,onClick:function(){Object(p.I)("".concat(t,"_audiences-sidebar-create-audiences"),"create_audience",e).finally((function(){K(e)}))},isSaving:o===e},Object(u.__)("Create","google-site-kit"))))}))),!x&&!R&&i.createElement("div",{className:"googlesitekit-audience-selection-panel__audience-creation-notice-info"},i.createElement(A.b,{title:Object(u.__)("Creating these groups require more data tracking. You will be directed to update your Analytics property.","google-site-kit"),dismissLabel:Object(u.__)("Got it","google-site-kit"),onDismiss:function(){Object(p.I)("".concat(t,"_audiences-sidebar-create-audiences"),"dismiss_oauth_notice").finally((function(){C(h.b)}))},variant:A.a.WARNING,hideIcon:!0})),(H.length>0||X)&&i.createElement(w.a,{apiErrors:H,hasOAuthError:X}))}}).call(this,n(28),n(4))},494:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceCreationErrorNotice}));var i=n(1),r=n.n(i),a=n(0),o=n(38),c=n(2),l=n(3),s=n(18),u=n(35),d=n(9),g=n(40),f=n(13),m=n(23),p=n(8),h=n(10),v=n(20),b=n(58);function AudienceCreationErrorNotice(t){var n,i,r=t.apiErrors,E=t.hasOAuthError,_=Object(s.a)(),O=Array.isArray(r)?r:[r],y=Object(l.useSelect)((function(e){return e(f.c).getErrorTroubleshootingLinkURL({code:"analytics-4_insufficient_permissions"})})),k=Object(l.useSelect)((function(e){return e(p.r).getServiceEntityAccessURL()})),j=Object(l.useSelect)((function(e){return e(f.c).getErrorTroubleshootingLinkURL({code:"access_denied"})})),S=Object(l.useSelect)((function(e){return e(m.b).getValue(g.i)})),A=O.length>0,w=O.some((function(e){return Object(u.e)(e)}));return Object(a.useEffect)((function(){if(S&&(A||E)){var e="setup_error";E?e="auth_error":w&&(e="insufficient_permissions_error"),Object(d.I)("".concat(_,"_audiences-sidebar-create-audiences"),e)}}),[A,w,E,S,_]),O.length||E?(E?i=Object(o.a)(Object(c.__)("Setup was interrupted because you didn’t grant the necessary permissions. Click on Create again to retry. If that doesn’t work, <HelpLink />","google-site-kit"),{HelpLink:e.createElement(v.a,{href:j,external:!0,hideExternalIndicator:!0},Object(c.__)("get help","google-site-kit"))}):w?(n=Object(c.__)("Insufficient permissions","google-site-kit"),i=Object(o.a)(Object(c.__)("Contact your administrator. Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(v.a,{href:y,external:!0,hideExternalIndicator:!0},Object(c.__)("Get help","google-site-kit"))})):(n=Object(c.__)("Analytics update failed","google-site-kit"),i=Object(c.__)("Click on Create to try again.","google-site-kit")),e.createElement("div",{className:"googlesitekit-audience-creation-error-notice"},e.createElement(b.a,{width:24,height:24}),e.createElement("div",{className:"googlesitekit-audience-creation-error-notice__content"},n&&e.createElement("p",{className:"googlesitekit-audience-creation-error-notice__title"},n),e.createElement("p",{className:"googlesitekit-audience-creation-error-notice__description"},i)),w&&e.createElement("div",{className:"googlesitekit-audience-creation-error-notice__actions"},e.createElement(h.Button,{href:k,target:"_blank",danger:!0,onClick:function(){Object(d.I)("".concat(_,"_audiences-sidebar-create-audiences"),"insufficient_permissions_error_request_access")}},Object(c.__)("Request access","google-site-kit"))))):null}AudienceCreationErrorNotice.propTypes={apiErrors:r.a.oneOfType([r.a.arrayOf(r.a.object),r.a.object,r.a.array]),hasOAuthError:r.a.bool}}).call(this,n(4))},495:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ErrorNotice}));var i=n(5),r=n.n(i),a=n(16),o=n.n(a),c=n(15),l=n.n(c),s=n(0),u=n(38),d=n(2),g=n(3),f=n(18),m=n(9),p=n(40),h=n(13),v=n(23),b=n(8),E=n(35),_=n(20),O=n(135),y=n(496),k=n(386);function ErrorNotice(){var t=Object(f.a)(),n=Object(g.useSelect)((function(e){return e(b.r).getErrorForAction("syncAvailableAudiences")})),i=Object(g.useInViewSelect)((function(e){return e(b.r).getAudienceUserCountReportErrors()}))||[],a=l()(i,2),c=a[0],j=a[1],S=Object(g.useSelect)((function(e){return e(h.c).getErrorTroubleshootingLinkURL({code:"analytics-4_insufficient_permissions"})})),A=Object(g.useSelect)((function(e){return e(v.b).getValue(p.i)})),w=Object(g.useDispatch)(b.r),T=w.clearError,C=w.syncAvailableAudiences,N=Object(s.useCallback)(o()(r.a.mark((function e(){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,T("syncAvailableAudiences");case 2:C();case 3:case"end":return e.stop()}}),e)}))),[T,C]),D=[];n&&D.push(n),j&&D.push(j),c&&D.push(c);var R=D.length>0,x=D.some((function(e){return Object(E.e)(e)}));if(Object(s.useEffect)((function(){A&&R&&Object(m.I)("".concat(t,"_audiences-sidebar"),x?"insufficient_permissions_error":"data_loading_error")}),[R,x,A,t]),!D.length)return null;var M=[j,c].some((function(e){return!!e}));return e.createElement("div",{className:"googlesitekit-audience-selection-panel__error-notice"},e.createElement("p",null,x?Object(u.a)(Object(d.__)("Insufficient permissions, contact your administrator. Trouble getting access? <HelpLink />","google-site-kit"),{HelpLink:e.createElement(_.a,{href:S,external:!0,hideExternalIndicator:!0},Object(d.__)("Get help","google-site-kit"))}):Object(d.__)("Data loading failed","google-site-kit")),e.createElement("div",{className:"googlesitekit-audience-selection-panel__error-notice-actions"},x||M?e.createElement(O.a,{moduleSlug:"analytics-4",error:D,hideGetHelpLink:!0,buttonVariant:"danger",RequestAccessButton:y.a,RetryButton:k.a}):e.createElement(k.a,{handleRetry:N})))}}).call(this,n(4))},496:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return RequestAccessButton}));var i=n(1),r=n.n(i),a=n(2),o=n(10),c=n(9),l=n(18);function RequestAccessButton(t){var n=t.requestAccessURL,i=Object(l.a)();return e.createElement(o.Button,{className:"googlesitekit-audience-selection-panel__error-notice-action",tertiary:!0,href:n,target:"_blank",onClick:function(){Object(c.I)("".concat(i,"_audiences-sidebar"),"insufficient_permissions_error_request_access")}},Object(a.__)("Request access","google-site-kit"))}RequestAccessButton.propTypes={requestAccessURL:r.a.string.isRequired}}).call(this,n(4))},497:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Footer}));var i=n(5),r=n.n(i),a=n(27),o=n.n(a),c=n(16),l=n.n(c),s=n(15),u=n.n(s),d=n(6),g=n.n(d),f=n(1),m=n.n(f),p=n(0),h=n(2),v=n(3),b=n(18),E=n(9),_=n(40),O=n(29),y=n(7),k=n(8),j=n(117);function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function A(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach((function(t){g()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Footer(t){var n,i=t.isOpen,a=t.closePanel,c=t.savedItemSlugs,s=Object(b.a)(),d=Object(v.useSelect)((function(e){return e(O.a).getValue(_.h,_.f)})),g=Object(v.useInViewSelect)((function(e){return e(y.a).getUserAudienceSettings()})),f=Object(v.useSelect)((function(e){return e(y.a).getErrorForAction("saveUserAudienceSettings",[A(A({},g),{},{configuredAudiences:d})])})),m=Object(v.useSelect)((function(e){return e(y.a).isSavingUserAudienceSettings()})),S=Object(v.useInViewSelect)((function(e){var t=e(y.a).getDismissedItems();return null==t?void 0:t.filter((function(e){return e.startsWith("audience-tile-")}))})),w=Object(v.useSelect)((function(e){return e(k.r).getAvailableAudiences()})),T=Object(v.useDispatch)(y.a),C=T.saveUserAudienceSettings,N=T.removeDismissedItems,D=Object(v.useSelect)(y.a).getConfiguredAudiences,R=(null==d?void 0:d.length)||0;R<_.k?n=Object(h.sprintf)(
/* translators: 1: Minimum number of groups that can be selected. 2: Number of selected groups. */
Object(h._n)("Select at least %1$d group (%2$d selected)","Select at least %1$d groups (%2$d selected)",_.k,"google-site-kit"),_.k,R):R>_.j&&(n=Object(h.sprintf)(
/* translators: 1: Maximum number of groups that can be selected. 2: Number of selected groups. */
Object(h.__)("Select up to %1$d groups (%2$d selected)","google-site-kit"),_.j,R));var x=Object(p.useState)(null),M=u()(x,2),I=M[0],B=M[1],P=Object(p.useCallback)(function(){var e=l()(r.a.mark((function e(t){var n,i,a,c;return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return B(null),e.next=3,C({configuredAudiences:t});case 3:if(n=e.sent,i=n.error){e.next=14;break}if(a=(null==S?void 0:S.filter((function(e){var n=e.replace("audience-tile-","");return!t.includes(n)})))||[],t.every((function(e){return null==S?void 0:S.includes("audience-tile-".concat(e))}))&&a.push("audience-tile-".concat(t[0])),!((null==a?void 0:a.length)>0)){e.next=14;break}return e.next=11,N.apply(void 0,o()(a));case 11:c=e.sent,(i=c.error)&&B(i);case 14:return e.abrupt("return",{error:i});case 15:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[S,N,C]),L=Object(p.useCallback)((function(){var e={USER_AUDIENCE:"user",SITE_KIT_AUDIENCE:"site-kit",DEFAULT_AUDIENCE:"default"},t=D(),n=Object.keys(e).map((function(n){var i=t.filter((function(e){var t=null==w?void 0:w.find((function(t){var n=t.name;return e===n}));return(null==t?void 0:t.audienceType)===n}));return"".concat(e[n],":").concat(i.length)})).join(",");Object(E.I)("".concat(s,"_audiences-sidebar"),"audiences_sidebar_save",n)}),[w,D,s]),F=Object(p.useCallback)((function(){Object(E.I)("".concat(s,"_audiences-sidebar"),"audiences_sidebar_cancel")}),[s]);return e.createElement(j.a,{savedItemSlugs:c,selectedItemSlugs:d,saveSettings:P,saveError:f||I,itemLimitError:n,minSelectedItemCount:_.k,maxSelectedItemCount:_.j,isBusy:m,isOpen:i,closePanel:a,onSaveSuccess:L,onCancel:F})}Footer.propTypes={isOpen:m.a.bool,closePanel:m.a.func.isRequired,savedItemSlugs:m.a.array}}).call(this,n(4))},498:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Header}));var i=n(1),r=n.n(i),a=n(0),o=n(38),c=n(2),l=n(3),s=n(32),u=n(13),d=n(7),g=n(34),f=n(20),m=n(117);function Header(t){var n=t.closePanel,i=Object(g.a)(),r=Object(l.useSelect)((function(e){return e(u.c).getAdminURL("googlesitekit-settings")})),p=Object(l.useSelect)((function(e){return e(d.a).isSavingUserAudienceSettings()})),h=Object(l.useDispatch)(s.a).navigateTo,v=Object(a.useCallback)((function(){return h("".concat(r,"#/admin-settings"))}),[h,r]);return e.createElement(m.b,{title:Object(c.__)("Select visitor groups","google-site-kit"),onCloseClick:n},!i&&e.createElement("p",null,Object(o.a)(Object(c.__)("You can deactivate this widget in <link><strong>Settings</strong></link>","google-site-kit"),{link:e.createElement(f.a,{secondary:!0,onClick:v,disabled:p}),strong:e.createElement("strong",null)})))}Header.propTypes={closePanel:r.a.func.isRequired}}).call(this,n(4))},499:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return LearnMoreLink}));var i=n(2),r=n(38),a=n(3),o=n(13),c=n(20);function LearnMoreLink(){var t=Object(a.useSelect)((function(e){return e(o.c).getGoogleSupportURL({path:"/analytics/answer/12799087"})}));return e.createElement("div",{className:"googlesitekit-audience-selection-panel__learn-more"},Object(r.a)(Object(i.__)("Learn more about grouping site visitors and audiences in <link><strong>Analytics</strong></link>","google-site-kit"),{link:e.createElement(c.a,{secondary:!0,href:t,external:!0}),strong:e.createElement("strong",null)}))}}).call(this,n(4))},50:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));var i=n(3),r=n(19),a=n(82);function o(t){var n=t.moduleName,o=t.FallbackComponent,c=t.IncompleteComponent;return function(t){function WhenActiveComponent(a){var l=Object(i.useSelect)((function(e){return e(r.a).getModule(n)}),[n]);if(!l)return null;var s=o||a.WidgetNull||null;if(!1===l.active)return s&&e.createElement(s,a);if(!1===l.connected){var u=c||s;return u&&e.createElement(u,a)}return e.createElement(t,a)}return WhenActiveComponent.displayName="When".concat(Object(a.c)(n),"Active"),(t.displayName||t.name)&&(WhenActiveComponent.displayName+="(".concat(t.displayName||t.name,")")),WhenActiveComponent}}}).call(this,n(4))},500:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AudienceCreationSuccessNotice}));var i=n(0),r=n(2),a=n(3),o=n(18),c=n(9),l=n(40),s=n(23),u=n(10),d=n(137);function AudienceCreationSuccessNotice(){var t=Object(o.a)(),n=Object(a.useDispatch)(s.b).setValue,g=Object(a.useSelect)((function(e){return e(s.b).getValue(l.e)})),f=Object(a.useSelect)((function(e){return e(s.b).getValue(l.i)}));return Object(i.useEffect)((function(){f&&g&&Object(c.I)("".concat(t,"_audiences-sidebar-create-audiences-success"),"view_notification")}),[f,g,t]),g?e.createElement("div",{className:"googlesitekit-audience-selection-panel__success-notice"},e.createElement("div",{className:"googlesitekit-audience-selection-panel__success-notice-icon"},e.createElement(d.a,{width:24,height:24})),e.createElement("p",{className:"googlesitekit-audience-selection-panel__success-notice-message"},Object(r.__)("Visitor group created successfully!","google-site-kit")),e.createElement("div",{className:"googlesitekit-audience-selection-panel__success-notice-actions"},e.createElement(u.Button,{tertiary:!0,onClick:function(){Object(c.I)("".concat(t,"_audiences-sidebar-create-audiences-success"),"dismiss_notification").finally((function(){n(l.e,!1)}))}},Object(r.__)("Got it","google-site-kit")))):null}}).call(this,n(4))},501:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("defs",null,i.createElement("filter",{id:"audience-connect-analytics-cta-graphic_svg__c",x:109.551,y:18.171,width:144.59,height:185.064,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1731_24094"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1731_24094",result:"shape"})),i.createElement("filter",{id:"audience-connect-analytics-cta-graphic_svg__d",x:236.859,y:18.171,width:144.59,height:185.064,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_1731_24094"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_1731_24094",result:"shape"})),i.createElement("clipPath",{id:"audience-connect-analytics-cta-graphic_svg__a"},i.createElement("path",{fill:"#fff",d:"M0 0h480v150H0z"}))),o=i.createElement("path",{d:"M91.722 36.579a71.937 71.937 0 017.307-6.582c24.521-19.234 44.779-19.204 72.826-15.693 18.961 2.373 30.038 11.4 55.889 9.98 25.851-1.42 32.474-7.992 64.117-5.887 25.048 1.667 36.285 6.612 58.554 18.182 20.61 10.707 39.324 29.519 48.728 54.397 16.12 42.644-12.622 119.393-51.166 123.012-27.93 2.623-50.979-28.308-79.169-21.145-17.366 4.414-27.666 22.927-41.064 35.144-15.631 14.255-49.304 13.359-67.607 5.751-17.442-7.248-34.409-21.615-40.106-42.775-4.337-16.114-5.519-35.322-17.661-50.04-14.694-17.811-23.672-25.756-28.716-49.947-4.382-21.009 5.045-40.938 18.068-54.397z",fill:"#B8E6CA"}),c=i.createElement("path",{d:"M91.722 36.579a71.937 71.937 0 017.307-6.582c24.521-19.234 44.779-19.204 72.826-15.693 18.961 2.373 30.038 11.4 55.889 9.98 25.851-1.42 32.474-7.992 64.117-5.887 25.048 1.667 36.285 6.612 58.554 18.182 20.61 10.707 39.324 29.519 48.728 54.397 16.12 42.644-12.622 119.393-51.166 123.012-27.93 2.623-50.979-28.308-79.169-21.145-17.366 4.414-27.666 22.927-41.064 35.144-15.631 14.255-49.304 13.359-67.607 5.751-17.442-7.248-34.409-21.615-40.106-42.775-4.337-16.114-5.519-35.322-17.661-50.04-14.694-17.811-23.672-25.756-28.716-49.947-4.382-21.009 5.045-40.938 18.068-54.397z",fill:"#B8E6CA"}),l=i.createElement("g",{mask:"url(#audience-connect-analytics-cta-graphic_svg__b)"},i.createElement("g",{filter:"url(#audience-connect-analytics-cta-graphic_svg__c)"},i.createElement("rect",{x:125.551,y:30.171,width:112.591,height:153.065,rx:8.095,fill:"#fff"})),i.createElement("rect",{x:139.555,y:93.193,width:39.014,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("rect",{x:139.555,y:82.189,width:14.005,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("rect",{x:139.555,y:118.259,width:14.005,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("path",{d:"M202.578 91.693a6.502 6.502 0 016.502-6.503h10.004a6.502 6.502 0 010 13.005H209.08a6.502 6.502 0 01-6.502-6.502z",fill:"#B8E6CA"}),i.createElement("rect",{x:139.535,y:45.625,width:26.492,height:6.623,rx:3.311,fill:"#EBEEF0"}),i.createElement("path",{d:"M202.578 127.763a6.502 6.502 0 016.502-6.502h10.004a6.502 6.502 0 110 13.004H209.08a6.502 6.502 0 01-6.502-6.502z",fill:"#FFDED3"}),i.createElement("rect",{x:138.555,y:129.263,width:41.014,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("path",{d:"M238.141 65.862H126.286",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("g",{filter:"url(#audience-connect-analytics-cta-graphic_svg__d)"},i.createElement("rect",{x:252.859,y:30.171,width:112.591,height:153.065,rx:8.095,fill:"#fff"})),i.createElement("rect",{x:266.809,y:93.193,width:38.859,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("rect",{x:266.809,y:82.189,width:13.949,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("rect",{x:266.805,y:118.259,width:13.949,height:7.002,rx:3.501,fill:"#EBEEF0"}),i.createElement("path",{d:"M329.582 91.693a6.502 6.502 0 016.502-6.503h9.912a6.502 6.502 0 110 13.005h-9.912a6.502 6.502 0 01-6.502-6.502z",fill:"#B8E6CA"}),i.createElement("rect",{x:266.844,y:45.625,width:26.492,height:6.623,rx:3.311,fill:"#EBEEF0"}),i.createElement("path",{d:"M358.094 65.862H252.862",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("path",{d:"M329.582 127.763a6.502 6.502 0 016.502-6.502h9.912a6.502 6.502 0 110 13.004h-9.912a6.502 6.502 0 01-6.502-6.502z",fill:"#FFDED3"}),i.createElement("rect",{x:265.812,y:129.263,width:40.852,height:7.002,rx:3.501,fill:"#EBEEF0"}));t.a=function SvgAudienceConnectAnalyticsCtaGraphic(e){return i.createElement("svg",r({viewBox:"-3 1 333.666 149.252",fill:"none"},e),a,i.createElement("g",{clipPath:"url(#audience-connect-analytics-cta-graphic_svg__a)",transform:"translate(-73)"},o,i.createElement("mask",{id:"audience-connect-analytics-cta-graphic_svg__b",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:72,y:12,width:332,height:228},c),l))}},502:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M67.728 34.59a71.982 71.982 0 017.31-6.584c24.527-19.24 44.792-19.21 72.848-15.698 18.966 2.374 30.047 11.404 55.905 9.984C229.65 20.872 234.5 10.117 271 14.308 307.5 18.5 322.5-1.5 360.5 1.5s52 32 72 42 37 8.5 50 35 4 83.5-36 106-99.355 25.386-122.439 27.553c-27.938 2.624-50.995-28.317-79.194-21.151-17.371 4.415-27.674 22.934-41.076 35.155-15.636 14.258-49.319 13.362-67.627 5.752-17.448-7.25-34.42-21.622-40.118-42.788-4.338-16.119-5.521-35.333-17.667-50.056-14.698-17.816-23.679-25.763-28.725-49.961-4.382-21.016 5.047-40.95 18.074-54.414z",fill:"#B8E6CA"}),o=i.createElement("path",{d:"M67.728 34.59a71.982 71.982 0 017.31-6.584c24.527-19.24 44.792-19.21 72.848-15.698 18.966 2.374 30.047 11.404 55.905 9.984C229.65 20.872 234.5 10.117 271 14.308 307.5 18.5 322.5-1.5 360.5 1.5s52 32 72 42 37 8.5 50 35 4 83.5-36 106-99.355 25.386-122.439 27.553c-27.938 2.624-50.995-28.317-79.194-21.151-17.371 4.415-27.674 22.934-41.076 35.155-15.636 14.258-49.319 13.362-67.627 5.752-17.448-7.25-34.42-21.622-40.118-42.788-4.338-16.119-5.521-35.333-17.667-50.056-14.698-17.816-23.679-25.763-28.725-49.961-4.382-21.016 5.047-40.95 18.074-54.414z",fill:"#B8E6CA"}),c=i.createElement("g",{filter:"url(#audience-connect-analytics-cta-graphic-tablet_svg__filter0_d_2898_16714)",mask:"url(#audience-connect-analytics-cta-graphic-tablet_svg__a)"},i.createElement("rect",{x:93,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:107.008,y:92.222,width:39.025,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:107.008,y:81.214,width:14.009,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:107.008,y:117.295,width:14.009,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M170.051 90.72a6.504 6.504 0 016.504-6.504h10.007a6.504 6.504 0 010 13.009h-10.007a6.504 6.504 0 01-6.504-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:106.984,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M170.051 126.802a6.504 6.504 0 016.504-6.505h10.007a6.504 6.504 0 010 13.009h-10.007a6.504 6.504 0 01-6.504-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:106.008,y:128.303,width:41.027,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M205.625 64.882H93.736",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("rect",{x:220.348,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:234.301,y:92.222,width:38.871,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:234.301,y:81.214,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:234.301,y:117.295,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M297.094 90.72a6.504 6.504 0 016.504-6.504h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.504-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:234.332,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M325.613 64.882H220.349",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("path",{d:"M297.094 126.802a6.504 6.504 0 016.504-6.505h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.504-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:233.305,y:128.303,width:40.864,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:347.695,y:29.181,width:112.625,height:153.111,rx:8.097,fill:"#fff"}),i.createElement("rect",{x:361.648,y:92.222,width:38.871,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:361.648,y:81.214,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("rect",{x:361.648,y:117.295,width:13.954,height:7.005,rx:3.502,fill:"#EBEEF0"}),i.createElement("path",{d:"M424.441 90.72a6.504 6.504 0 016.505-6.504h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.505-6.504z",fill:"#FFDED3"}),i.createElement("rect",{x:361.68,y:44.639,width:26.5,height:6.625,rx:3.313,fill:"#EBEEF0"}),i.createElement("path",{d:"M452.961 64.882H347.697",stroke:"#EBEEF0",strokeWidth:1.472}),i.createElement("path",{d:"M424.441 126.802a6.505 6.505 0 016.505-6.505h9.915a6.504 6.504 0 010 13.009h-9.915a6.504 6.504 0 01-6.505-6.504z",fill:"#B8E6CA"}),i.createElement("rect",{x:360.652,y:128.303,width:40.864,height:7.005,rx:3.502,fill:"#EBEEF0"})),l=i.createElement("defs",null,i.createElement("clipPath",{id:"audience-connect-analytics-cta-graphic-tablet_svg__clip0_2898_16714"},i.createElement("path",{fill:"#fff",d:"M0 0h553v158H0z"})),i.createElement("filter",{id:"audience-connect-analytics-cta-graphic-tablet_svg__filter0_d_2898_16714",x:77,y:17.181,width:399.32,height:185.111,filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},i.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),i.createElement("feColorMatrix",{in:"SourceAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),i.createElement("feOffset",{dy:4}),i.createElement("feGaussianBlur",{stdDeviation:8}),i.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),i.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),i.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_2898_16714"}),i.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_2898_16714",result:"shape"})));t.a=function SvgAudienceConnectAnalyticsCtaGraphicTablet(e){return i.createElement("svg",r({viewBox:"0 0 553 146",fill:"none"},e),i.createElement("g",{clipPath:"url(#audience-connect-analytics-cta-graphic-tablet_svg__clip0_2898_16714)"},a,i.createElement("mask",{id:"audience-connect-analytics-cta-graphic-tablet_svg__a",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:48,y:1,width:441,height:237},o),c),l)}},511:function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var i=n(0),r=n(3),a=n(13),o=n(7),c=n(2),l=n(22),s={slug:"sharedKeyMetrics",contexts:[l.n,l.o,l.l,l.m],gaEventCategory:function(e){return"".concat(e,"_shared_key-metrics")},steps:[{target:".googlesitekit-km-change-metrics-cta",title:Object(c.__)("Personalize your key metrics","google-site-kit"),content:Object(c.__)("Another admin has set up these tailored metrics for your site. Click here to personalize them.","google-site-kit"),placement:"bottom-start"}]},u=function(e){var t=Object(r.useSelect)((function(e){return e(a.c).getKeyMetricsSetupCompletedBy()})),n=Object(r.useSelect)((function(e){return e(o.a).getID()})),c=Object(r.useDispatch)(o.a).triggerOnDemandTour,l=Number.isInteger(t)&&Number.isInteger(n)&&t>0&&n!==t;Object(i.useEffect)((function(){e&&l&&c(s)}),[e,l,c])}},52:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return c}));var i=n(22),r=n(18),a=i.n,o=i.l;function c(){var e=Object(r.a)();return e===i.n||e===i.o?a:e===i.l||e===i.m?o:null}},54:function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(106),o=n(2),c=n(9);function ErrorText(t){var n=t.message,i=t.reconnectURL,r=t.noPrefix;if(!n)return null;var l=n;void 0!==r&&r||(l=Object(o.sprintf)(
/* translators: %s: Error message */
Object(o.__)("Error: %s","google-site-kit"),n)),i&&Object(a.a)(i)&&(l=l+" "+Object(o.sprintf)(
/* translators: %s: Reconnect URL */
Object(o.__)('To fix this, <a href="%s">redo the plugin setup</a>.',"google-site-kit"),i));return e.createElement("div",{className:"googlesitekit-error-text"},e.createElement("p",{dangerouslySetInnerHTML:Object(c.F)(l,{ALLOWED_TAGS:["a"],ALLOWED_ATTR:["href"]})}))}ErrorText.propTypes={message:r.a.string.isRequired,reconnectURL:r.a.string,noPrefix:r.a.bool},ErrorText.defaultProps={reconnectURL:"",noPrefix:!1},t.a=ErrorText}).call(this,n(4))},57:function(e,t,n){"use strict";(function(e){var i,r;n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=new Set((null===(i=e)||void 0===i||null===(r=i._googlesitekitBaseData)||void 0===r?void 0:r.enabledFeatures)||[]),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return t instanceof Set&&t.has(e)}}).call(this,n(28))},58:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),o=i.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"});t.a=function SvgWarning(e){return i.createElement("svg",r({fill:"currentColor",viewBox:"0 0 24 24","aria-labelledby":"warning-title warning-desc"},e),a,o)}},59:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n(39);function r(e){return function(){e[i.a]=e[i.a]||[],e[i.a].push(arguments)}}},61:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n(0),r=Object(i.createContext)(""),a=(r.Consumer,r.Provider);t.b=r},638:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return AddMetricCTATile}));var i=n(15),r=n.n(i),a=n(1),o=n.n(a),c=n(0),l=n(240),s=n(2),u=n(56),d=n(3),g=n(23),f=n(26),m=n(1022),p=n(9),h=n(18);function AddMetricCTATile(t){var n=t.Widget,i=Object(c.useRef)(),a=Object(d.useDispatch)(g.b).setValue,o=Object(h.a)(),v="".concat(o,"_kmw"),b=Object(c.useCallback)((function(e){("keydown"!==e.type||[u.b,u.e].includes(e.keyCode))&&(e.preventDefault(),a(f.k,!0),Object(p.I)(v,"add_metric_click"))}),[a,v]),E=Object(l.a)(i,{threshold:.25}),_=Object(c.useState)(!1),O=r()(_,2),y=O[0],k=O[1],j=!!(null==E?void 0:E.intersectionRatio);return Object(c.useEffect)((function(){j&&!y&&(Object(p.I)(v,"add_metric_view"),k(!0))}),[j,v,y]),e.createElement(n,{className:"googlesitekit-widget--addMetricCTATile",noPadding:!0},e.createElement("div",{ref:i,className:"googlesitekit-km-add-metric-cta-tile",onClick:b,onKeyDown:b,tabIndex:0,role:"button"},e.createElement("div",{className:"googlesitekit-km-add-metric-cta-tile__icon"},e.createElement(m.a,{width:16,height:16})),e.createElement("p",{className:"googlesitekit-km-add-metric-cta-tile__text"},Object(s.__)("Add a metric","google-site-kit"))))}AddMetricCTATile.propTypes={Widget:o.a.elementType.isRequired}}).call(this,n(4))},64:function(e,t,n){"use strict";n.d(t,"a",(function(){return h})),n.d(t,"b",(function(){return v}));var i=n(6),r=n.n(i),a=n(33),o=n.n(a),c=n(116),l=n(12),s=n.n(l),u=n(96),d=n.n(u),g=n(9);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t){if(t&&Array.isArray(t)){var n=t.map((function(e){return"object"===o()(e)?Object(g.H)(e):e}));return"".concat(e,"::").concat(d()(JSON.stringify(n)))}return e}var h={receiveError:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return s()(e,"error is required."),s()(t,"baseName is required."),s()(n&&Array.isArray(n),"args must be an array."),{type:"RECEIVE_ERROR",payload:{error:e,baseName:t,args:n}}},clearError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return s()(e,"baseName is required."),s()(t&&Array.isArray(t),"args must be an array."),{type:"CLEAR_ERROR",payload:{baseName:e,args:t}}},clearErrors:function(e){return{type:"CLEAR_ERRORS",payload:{baseName:e}}}};function v(e){s()(e,"storeName must be defined.");var t={getErrorForSelector:function(e,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return s()(n,"selectorName is required."),t.getError(e,n,i)},getErrorForAction:function(e,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return s()(n,"actionName is required."),t.getError(e,n,i)},getError:function(e,t,n){var i=e.errors;return s()(t,"baseName is required."),i[p(t,n)]},getErrors:function(e){var t=new Set(Object.values(e.errors));return Array.from(t)},getMetaDataForError:function(e,t){var n=Object.keys(e.errors).find((function(n){return e.errors[n]===t}));return n?{baseName:n.substring(0,n.indexOf("::")),args:e.errorArgs[n]}:null},getSelectorDataForError:Object(c.b)((function(t){return function(n,i){var r=t(e).getMetaDataForError(i);if(r){var a=r.baseName,o=r.args;if(!!t(e)[a])return{storeName:e,name:a,args:o}}return null}})),hasErrors:function(e){return t.getErrors(e).length>0}};return{initialState:{errors:{},errorArgs:{}},actions:h,controls:{},reducer:function(e,t){var n=t.type,i=t.payload;switch(n){case"RECEIVE_ERROR":var a=i.baseName,o=i.args,c=i.error,l=p(a,o);return m(m({},e),{},{errors:m(m({},e.errors||{}),{},r()({},l,c)),errorArgs:m(m({},e.errorArgs||{}),{},r()({},l,o))});case"CLEAR_ERROR":var s=i.baseName,u=i.args,d=m({},e),g=p(s,u);return d.errors=m({},e.errors||{}),d.errorArgs=m({},e.errorArgs||{}),delete d.errors[g],delete d.errorArgs[g],d;case"CLEAR_ERRORS":var f=i.baseName,h=m({},e);if(f)for(var v in h.errors=m({},e.errors||{}),h.errorArgs=m({},e.errorArgs||{}),h.errors)(v===f||v.startsWith("".concat(f,"::")))&&(delete h.errors[v],delete h.errorArgs[v]);else h.errors={},h.errorArgs={};return h;default:return e}},resolvers:{},selectors:t}}},652:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return ConnectGA4CTAWidget}));var i=n(15),r=n.n(i),a=n(2),o=n(0),c=n(3),l=n(10),s=n(379),u=n(380),d=n(19),g=n(7),f=n(47),m=n(88),p=n(8),h=n(32),v=n(13),b=n(253),E=n(159),_=n(398),O=n(121);function ConnectGA4CTAWidget(t){var n=t.Widget,i=t.WidgetNull,y=Object(c.useSelect)((function(e){var t=e(g.a).getKeyMetrics(),n=e(f.a).getWidgets(m.AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY);return t&&n?n.filter((function(e){var n=e.slug,i=e.modules;return t.includes(n)&&i.includes("analytics-4")})):[]})),k=Object(c.useSelect)((function(e){return e(d.a).isModuleActive("analytics-4")})),j=Object(c.useSelect)((function(e){var t=e(p.r).getAdminReauthURL();return!!t&&e(h.a).isNavigatingTo(t)})),S=Object(c.useSelect)((function(e){return e(d.a).isFetchingSetModuleActivation("analytics-4",!0)})),A=Object(c.useSelect)((function(e){var t=e(v.c).getAdminURL("googlesitekit-settings");return"".concat(t,"#connected-services/analytics-4/edit")})),w=Object(c.useSelect)((function(e){return e(h.a).isNavigatingTo(A)})),T=Object(c.useDispatch)(g.a).dismissItem,C=Object(E.a)("analytics-4"),N=Object(_.a)("analytics-4"),D=Object(o.useCallback)((function(){if(k)return N();C()}),[C,N,k]),R=Object(o.useState)(!1),x=r()(R,2),M=x[0],I=x[1],B=Object(O.a)(I,3e3);return Object(o.useEffect)((function(){S||j||w?I(!0):B(!1)}),[S,j,B,w]),!1!==Object(c.useSelect)((function(e){return e(g.a).isItemDismissed(b.f)}))||y.length<4?e.createElement(i,null):e.createElement(n,{noPadding:!0,Footer:function Footer(){return e.createElement(u.a,{onActionClick:function(){return T(b.f)},showDismiss:!0})}},e.createElement(s.a,{className:"googlesitekit-km-connect-ga4-cta",title:Object(a.__)("Analytics is disconnected","google-site-kit"),description:Object(a.__)("Metrics cannot be displayed without Analytics","google-site-kit"),actions:e.createElement(l.SpinnerButton,{onClick:D,isSaving:M,disabled:M},Object(a.__)("Connect Analytics","google-site-kit"))}))}}).call(this,n(4))},66:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return r}));var i="modules/search-console",r=1},7:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return r})),n.d(t,"e",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"H",(function(){return l})),n.d(t,"M",(function(){return s})),n.d(t,"O",(function(){return u})),n.d(t,"K",(function(){return d})),n.d(t,"L",(function(){return g})),n.d(t,"J",(function(){return f})),n.d(t,"I",(function(){return m})),n.d(t,"N",(function(){return p})),n.d(t,"f",(function(){return h})),n.d(t,"g",(function(){return v})),n.d(t,"h",(function(){return b})),n.d(t,"j",(function(){return E})),n.d(t,"l",(function(){return _})),n.d(t,"m",(function(){return O})),n.d(t,"n",(function(){return y})),n.d(t,"o",(function(){return k})),n.d(t,"q",(function(){return j})),n.d(t,"s",(function(){return S})),n.d(t,"r",(function(){return A})),n.d(t,"t",(function(){return w})),n.d(t,"w",(function(){return T})),n.d(t,"u",(function(){return C})),n.d(t,"v",(function(){return N})),n.d(t,"x",(function(){return D})),n.d(t,"y",(function(){return R})),n.d(t,"A",(function(){return x})),n.d(t,"B",(function(){return M})),n.d(t,"C",(function(){return I})),n.d(t,"D",(function(){return B})),n.d(t,"k",(function(){return P})),n.d(t,"F",(function(){return L})),n.d(t,"z",(function(){return F})),n.d(t,"G",(function(){return z})),n.d(t,"E",(function(){return W})),n.d(t,"i",(function(){return V})),n.d(t,"p",(function(){return U})),n.d(t,"Q",(function(){return G})),n.d(t,"P",(function(){return H}));var i="core/user",r="connected_url_mismatch",a="__global",o="temporary_persist_permission_error",c="adblocker_active",l="googlesitekit_authenticate",s="googlesitekit_setup",u="googlesitekit_view_dashboard",d="googlesitekit_manage_options",g="googlesitekit_read_shared_module_data",f="googlesitekit_manage_module_sharing_options",m="googlesitekit_delegate_module_sharing_management",p="googlesitekit_update_plugins",h="kmAnalyticsAdSenseTopEarningContent",v="kmAnalyticsEngagedTrafficSource",b="kmAnalyticsLeastEngagingPages",E="kmAnalyticsNewVisitors",_="kmAnalyticsPopularAuthors",O="kmAnalyticsPopularContent",y="kmAnalyticsPopularProducts",k="kmAnalyticsReturningVisitors",j="kmAnalyticsTopCities",S="kmAnalyticsTopCitiesDrivingLeads",A="kmAnalyticsTopCitiesDrivingAddToCart",w="kmAnalyticsTopCitiesDrivingPurchases",T="kmAnalyticsTopDeviceDrivingPurchases",C="kmAnalyticsTopConvertingTrafficSource",N="kmAnalyticsTopCountries",D="kmAnalyticsTopPagesDrivingLeads",R="kmAnalyticsTopRecentTrendingPages",x="kmAnalyticsTopTrafficSource",M="kmAnalyticsTopTrafficSourceDrivingAddToCart",I="kmAnalyticsTopTrafficSourceDrivingLeads",B="kmAnalyticsTopTrafficSourceDrivingPurchases",P="kmAnalyticsPagesPerVisit",L="kmAnalyticsVisitLength",F="kmAnalyticsTopReturningVisitorPages",z="kmSearchConsolePopularKeywords",W="kmAnalyticsVisitsPerVisitor",V="kmAnalyticsMostEngagingPages",U="kmAnalyticsTopCategories",G=[h,v,b,E,_,O,y,k,U,j,S,A,w,T,C,N,R,x,M,P,L,F,W,V,U],H=[].concat(G,[z])},70:function(e,t,n){"use strict";var i=n(0);function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}var a=i.createElement("path",{d:"M5 21a1.99 1.99 0 01-1.425-.575A1.99 1.99 0 013 19V5c0-.55.192-1.017.575-1.4.4-.4.875-.6 1.425-.6h7v2H5v14h14v-7h2v7c0 .55-.2 1.025-.6 1.425-.383.383-.85.575-1.4.575H5zm4.7-5.3l-1.4-1.4L17.6 5H14V3h7v7h-2V6.4l-9.3 9.3z"});t.a=function SvgExternal(e){return i.createElement("svg",r({viewBox:"0 0 24 24",fill:"currentColor"},e),a)}},71:function(e,t,n){"use strict";n.r(t),n.d(t,"CONTEXT_MAIN_DASHBOARD_KEY_METRICS",(function(){return i})),n.d(t,"CONTEXT_MAIN_DASHBOARD_TRAFFIC",(function(){return r})),n.d(t,"CONTEXT_MAIN_DASHBOARD_CONTENT",(function(){return a})),n.d(t,"CONTEXT_MAIN_DASHBOARD_SPEED",(function(){return o})),n.d(t,"CONTEXT_MAIN_DASHBOARD_MONETIZATION",(function(){return c})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_TRAFFIC",(function(){return l})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_CONTENT",(function(){return s})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_SPEED",(function(){return u})),n.d(t,"CONTEXT_ENTITY_DASHBOARD_MONETIZATION",(function(){return d}));var i="mainDashboardKeyMetrics",r="mainDashboardTraffic",a="mainDashboardContent",o="mainDashboardSpeed",c="mainDashboardMonetization",l="entityDashboardTraffic",s="entityDashboardContent",u="entityDashboardSpeed",d="entityDashboardMonetization";t.default={CONTEXT_MAIN_DASHBOARD_KEY_METRICS:i,CONTEXT_MAIN_DASHBOARD_TRAFFIC:r,CONTEXT_MAIN_DASHBOARD_CONTENT:a,CONTEXT_MAIN_DASHBOARD_SPEED:o,CONTEXT_MAIN_DASHBOARD_MONETIZATION:c,CONTEXT_ENTITY_DASHBOARD_TRAFFIC:l,CONTEXT_ENTITY_DASHBOARD_CONTENT:s,CONTEXT_ENTITY_DASHBOARD_SPEED:u,CONTEXT_ENTITY_DASHBOARD_MONETIZATION:d}},72:function(e,t,n){"use strict";var i=n(15),r=n.n(i),a=n(265),o=n(1),c=n.n(o),l=n(0),s=n(144);function Portal(e){var t=e.children,n=e.slug,i=Object(l.useState)(document.createElement("div")),o=r()(i,1)[0];return Object(a.a)((function(){n&&o.classList.add("googlesitekit-portal-".concat(n));var e=document.querySelector(".googlesitekit-plugin")||document.body;return e.appendChild(o),function(){return e.removeChild(o)}})),Object(s.createPortal)(t,o)}Portal.propTypes={slug:c.a.string,children:c.a.node},Portal.defaultProps={slug:"",children:null},t.a=Portal},73:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n(0),r=n(18),a=n(9);function o(e,t){var n=Object(r.a)(),o=null!=t?t:"".concat(n,"_").concat(e);return{view:Object(i.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"view_notification"].concat(t))}),[o]),confirm:Object(i.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"confirm_notification"].concat(t))}),[o]),dismiss:Object(i.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"dismiss_notification"].concat(t))}),[o]),clickLearnMore:Object(i.useCallback)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.I.apply(void 0,[o,"click_learn_more_link"].concat(t))}),[o])}}},74:function(e,t,n){"use strict";function Null(){return null}n.d(t,"a",(function(){return Null}))},75:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c}));var i=n(33),r=n.n(i),a=n(84),o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{__html:a.a.sanitize(e,t)}};function c(e){var t,n="object"===r()(e)?e.toString():e;return null==n||null===(t=n.replace)||void 0===t?void 0:t.call(n,/\/+$/,"")}},76:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return IconWrapper}));var i=n(1),r=n.n(i);function IconWrapper(t){var n=t.children,i=t.marginLeft,r=t.marginRight;return e.createElement("span",{className:"googlesitekit-icon-wrapper",style:{marginLeft:i,marginRight:r}},n)}IconWrapper.propTypes={children:r.a.node.isRequired,marginLeft:r.a.number,marginRight:r.a.number}}).call(this,n(4))},77:function(e,t,n){"use strict";(function(e){var i=n(21),r=n.n(i),a=n(25),o=n.n(a),c=n(11),l=n.n(c),s=n(1),u=n.n(s),d=n(0),g=Object(d.forwardRef)((function(t,n){var i=t.label,a=t.className,c=t.hasLeftSpacing,s=void 0!==c&&c,u=o()(t,["label","className","hasLeftSpacing"]);return e.createElement("span",r()({ref:n},u,{className:l()("googlesitekit-badge",a,{"googlesitekit-badge--has-left-spacing":s})}),i)}));g.displayName="Badge",g.propTypes={label:u.a.string.isRequired,hasLeftSpacing:u.a.bool},t.a=g}).call(this,n(4))},79:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var i=n(15),r=n.n(i),a=n(188),o=n(133),c={},l=void 0===e?null:e,s=function(){return[e.innerWidth,e.innerHeight]},u=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=e.fps,n=e.leading,i=e.initialWidth,u=void 0===i?0:i,d=e.initialHeight,g=void 0===d?0:d,f=Object(a.a)("undefined"==typeof document?[u,g]:s,t,n),m=r()(f,2),p=m[0],h=m[1],v=function(){return h(s)};return Object(o.a)(l,"resize",v),Object(o.a)(l,"orientationchange",v),p},d=function(e){return u(e)[0]}}).call(this,n(28))},8:function(e,t,n){"use strict";n.d(t,"r",(function(){return i})),n.d(t,"a",(function(){return r})),n.d(t,"s",(function(){return a})),n.d(t,"z",(function(){return o})),n.d(t,"o",(function(){return c})),n.d(t,"q",(function(){return l})),n.d(t,"g",(function(){return s})),n.d(t,"p",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return g})),n.d(t,"k",(function(){return f})),n.d(t,"m",(function(){return m})),n.d(t,"n",(function(){return p})),n.d(t,"h",(function(){return h})),n.d(t,"x",(function(){return v})),n.d(t,"w",(function(){return b})),n.d(t,"y",(function(){return E})),n.d(t,"u",(function(){return _})),n.d(t,"v",(function(){return O})),n.d(t,"f",(function(){return y})),n.d(t,"l",(function(){return k})),n.d(t,"e",(function(){return j})),n.d(t,"t",(function(){return S})),n.d(t,"c",(function(){return A})),n.d(t,"d",(function(){return w})),n.d(t,"b",(function(){return T}));var i="modules/analytics-4",r="account_create",a="property_create",o="webdatastream_create",c="analyticsSetup",l=10,s=1,u="https://www.googleapis.com/auth/tagmanager.readonly",d="enhanced-measurement-form",g="enhanced-measurement-enabled",f="enhanced-measurement-should-dismiss-activation-banner",m="analyticsAccountCreate",p="analyticsCustomDimensionsCreate",h="https://www.googleapis.com/auth/analytics.edit",v="dashboardAllTrafficWidgetDimensionName",b="dashboardAllTrafficWidgetDimensionColor",E="dashboardAllTrafficWidgetDimensionValue",_="dashboardAllTrafficWidgetActiveRowIndex",O="dashboardAllTrafficWidgetLoaded",y={googlesitekit_post_date:{parameterName:"googlesitekit_post_date",displayName:"WordPress Post Date",description:"Created by Site Kit: Date when a post was published",scope:"EVENT"},googlesitekit_post_author:{parameterName:"googlesitekit_post_author",displayName:"WordPress Post Author",description:"Created by Site Kit: WordPress name of the post author",scope:"EVENT"},googlesitekit_post_categories:{parameterName:"googlesitekit_post_categories",displayName:"WordPress Post Categories",description:"Created by Site Kit: Names of categories assigned to a post",scope:"EVENT"},googlesitekit_post_type:{parameterName:"googlesitekit_post_type",displayName:"WordPress Post Type",description:"Created by Site Kit: Content type of a post",scope:"EVENT"}},k={ADD_TO_CART:"add_to_cart",PURCHASE:"purchase",SUBMIT_LEAD_FORM:"submit_lead_form",GENERATE_LEAD:"generate_lead",CONTACT:"contact"},j=[k.CONTACT,k.GENERATE_LEAD,k.SUBMIT_LEAD_FORM],S={"new-visitors":{description:"People who visited the site for the first time",displayName:"New visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"new"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:new_visitors"}}}}]}}]}}}}]},"returning-visitors":{description:"People who have visited your site at least once before",displayName:"Returning visitors",membershipDurationDays:-1,filterClauses:[{clauseType:"INCLUDE",simpleFilter:{scope:"AUDIENCE_FILTER_SCOPE_ACROSS_ALL_SESSIONS",filterExpression:{andGroup:{filterExpressions:[{orGroup:{filterExpressions:[{dimensionOrMetricFilter:{fieldName:"newVsReturning",stringFilter:{matchType:"EXACT",value:"returning"}}}]}},{orGroup:{filterExpressions:[{notExpression:{dimensionOrMetricFilter:{fieldName:"groupId",stringFilter:{matchType:"EXACT",value:"created_by_googlesitekit:returning_visitors"}}}}]}}]}}}}]}},A="audiencePermissionsSetup",w="audienceTileCustomDimensionCreate",T="audience-selection-panel-expirable-new-badge-"},80:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return c}));var i=n(106);function r(e){try{return new URL(e).pathname}catch(e){}return null}function a(e,t){try{return new URL(t,e).href}catch(e){}return("string"==typeof e?e:"")+("string"==typeof t?t:"")}function o(e){return"string"!=typeof e?e:e.replace(/^https?:\/\/(www\.)?/i,"").replace(/\/$/,"")}function c(e,t){if(!Object(i.a)(e))return e;if(e.length<=t)return e;var n=new URL(e),r=e.replace(n.origin,"");if(r.length<t)return r;var a=r.length-Math.floor(t)+1;return"…"+r.substr(a)}},811:function(e,t,n){"use strict";n.d(t,"b",(function(){return g})),n.d(t,"c",(function(){return f.a})),n.d(t,"a",(function(){return m}));var i=n(47),r=n(3),a=n(1018),o=n(1020),c=n(12),l=n.n(c),s={selectors:{isWidgetContextActive:Object(r.createRegistrySelector)((function(e){return function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};l()(n,"contextSlug is required to check a widget context is active.");var a=r.modules;return e(i.a).getWidgetAreas(n).some((function(t){return e(i.a).isWidgetAreaActive(t.slug,{modules:a})}))}}))}},u=n(64),d=Object(r.combineStores)(r.commonStore,a.a,o.a,s,Object(u.b)(i.a)),g=function(e){e.registerStore(i.a,d)},f=n(855);function m(e){var t=e.dispatch,n=e.select,r={WIDGET_AREA_STYLES:i.b,WIDGET_WIDTHS:i.c,registerWidgetArea:function(e,n,a){t(i.a).registerWidgetArea(e,n),a&&r.assignWidgetArea(e,a)},registerWidget:function(e,n,a){t(i.a).registerWidget(e,n),a&&r.assignWidget(e,a)},assignWidgetArea:function(e,n){t(i.a).assignWidgetArea(e,n)},assignWidget:function(e,n){t(i.a).assignWidget(e,n)},isWidgetAreaRegistered:function(e){return n(i.a).isWidgetAreaRegistered(e)},isWidgetRegistered:function(e){return n(i.a).isWidgetRegistered(e)}};return r}},82:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return r})),n.d(t,"c",(function(){return a}));var i=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},r=function(e){return e.replace(/([a-z0-9]{1})([A-Z]{1})/g,"$1_$2").toUpperCase()};function a(e){return e.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")}},83:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return k})),n.d(t,"d",(function(){return j})),n.d(t,"e",(function(){return A})),n.d(t,"c",(function(){return w})),n.d(t,"b",(function(){return T}));var i=n(15),r=n.n(i),a=n(33),o=n.n(a),c=n(6),l=n.n(c),s=n(25),u=n.n(s),d=n(14),g=n(63),f=n.n(g),m=n(2);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=_(e,t),i=n.formatUnit,r=n.formatDecimal;try{return i()}catch(e){return r()}},b=function(e){var t=E(e),n=t.hours,i=t.minutes,r=t.seconds;return r=("0"+r).slice(-2),i=("0"+i).slice(-2),"00"===(n=("0"+n).slice(-2))?"".concat(i,":").concat(r):"".concat(n,":").concat(i,":").concat(r)},E=function(e){return e=parseInt(e,10),Number.isNaN(e)&&(e=0),{hours:Math.floor(e/60/60),minutes:Math.floor(e/60%60),seconds:Math.floor(e%60)}},_=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=E(e),i=n.hours,r=n.minutes,a=n.seconds;return{hours:i,minutes:r,seconds:a,formatUnit:function(){var n=t.unitDisplay,o=h(h({unitDisplay:void 0===n?"short":n},u()(t,["unitDisplay"])),{},{style:"unit"});return 0===e?A(a,h(h({},o),{},{unit:"second"})):Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?A(a,h(h({},o),{},{unit:"second"})):"",r?A(r,h(h({},o),{},{unit:"minute"})):"",i?A(i,h(h({},o),{},{unit:"hour"})):"").trim()},formatDecimal:function(){var t=Object(m.sprintf)(// translators: %s: number of seconds with "s" as the abbreviated unit.
Object(m.__)("%ds","google-site-kit"),a);if(0===e)return t;var n=Object(m.sprintf)(// translators: %s: number of minutes with "m" as the abbreviated unit.
Object(m.__)("%dm","google-site-kit"),r),o=Object(m.sprintf)(// translators: %s: number of hours with "h" as the abbreviated unit.
Object(m.__)("%dh","google-site-kit"),i);return Object(m.sprintf)(
/* translators: 1: formatted seconds, 2: formatted minutes, 3: formatted hours */
Object(m._x)("%3$s %2$s %1$s","duration of time: hh mm ss","google-site-kit"),a?t:"",r?n:"",i?o:"").trim()}}},O=function(e){return 1e6<=e?Math.round(e/1e5)/10:1e4<=e?Math.round(e/1e3):1e3<=e?Math.round(e/100)/10:e},y=function(e){var t={minimumFractionDigits:1,maximumFractionDigits:1};return 1e6<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in millions.
Object(m.__)("%sM","google-site-kit"),A(O(e),e%10==0?{}:t)):1e4<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),A(O(e))):1e3<=e?Object(m.sprintf)(// translators: %s: an abbreviated number in thousands.
Object(m.__)("%sK","google-site-kit"),A(O(e),e%10==0?{}:t)):A(e,{signDisplay:"never",maximumFractionDigits:1})};function k(e){var t={};return"%"===e?t={style:"percent",maximumFractionDigits:2}:"s"===e?t={style:"duration",unitDisplay:"narrow"}:e&&"string"==typeof e?t={style:"currency",currency:e}:Object(d.isPlainObject)(e)&&(t=h({},e)),t}function j(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=Object(d.isFinite)(e)?e:Number(e),Object(d.isFinite)(e)||(console.warn("Invalid number",e,o()(e)),e=0);var n=k(t),i=n.style,r=void 0===i?"metric":i;return"metric"===r?y(e):"duration"===r?v(e,n):"durationISO"===r?b(e):A(e,n)}var S=f()(console.warn),A=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,i=void 0===n?T():n,a=u()(t,["locale"]);try{return new Intl.NumberFormat(i,a).format(e)}catch(t){S("Site Kit numberFormat error: Intl.NumberFormat( ".concat(JSON.stringify(i),", ").concat(JSON.stringify(a)," ).format( ").concat(o()(e)," )"),t.message)}for(var c={currencyDisplay:"narrow",currencySign:"accounting",style:"unit"},l=["signDisplay","compactDisplay"],s={},d=0,g=Object.entries(a);d<g.length;d++){var f=r()(g[d],2),m=f[0],p=f[1];c[m]&&p===c[m]||(l.includes(m)||(s[m]=p))}try{return new Intl.NumberFormat(i,s).format(e)}catch(t){return new Intl.NumberFormat(i).format(e)}},w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.locale,i=void 0===n?T():n,r=t.style,a=void 0===r?"long":r,o=t.type,c=void 0===o?"conjunction":o;if(Intl.ListFormat){var l=new Intl.ListFormat(i,{style:a,type:c});return l.format(e)}
/* translators: used between list items, there is a space after the comma. */var s=Object(m.__)(", ","google-site-kit");return e.join(s)},T=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e,n=Object(d.get)(t,["_googlesitekitLegacyData","locale"]);if(n){var i=n.match(/^(\w{2})?(_)?(\w{2})/);if(i&&i[0])return i[0].replace(/_/g,"-")}return t.navigator.language}}).call(this,n(28))},84:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return r}));var i=n(149),r=n.n(i)()(e)}).call(this,n(28))},85:function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(11),o=n.n(a);function ChangeArrow(t){var n=t.direction,i=t.invertColor,r=t.width,a=t.height;return e.createElement("svg",{className:o()("googlesitekit-change-arrow","googlesitekit-change-arrow--".concat(n),{"googlesitekit-change-arrow--inverted-color":i}),width:r,height:a,viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M5.625 10L5.625 2.375L9.125 5.875L10 5L5 -1.76555e-07L-2.7055e-07 5L0.875 5.875L4.375 2.375L4.375 10L5.625 10Z",fill:"currentColor"}))}ChangeArrow.propTypes={direction:r.a.string,invertColor:r.a.bool,width:r.a.number,height:r.a.number},ChangeArrow.defaultProps={direction:"up",invertColor:!1,width:9,height:9},t.a=ChangeArrow}).call(this,n(4))},855:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return y}));var i=n(21),r=n.n(i),a=n(0),o=n(2),c=n(71),l=n(88),s=n(7),u=n(47),d=n(19),g=n(13),f=n(108),m=n(638),p=n(1023),h=n(1024),v=n(652),b=n(384),E=n(24),_=r()({},c),O=r()({},l);function y(t){var n=_.CONTEXT_MAIN_DASHBOARD_KEY_METRICS,i=_.CONTEXT_MAIN_DASHBOARD_TRAFFIC,r=_.CONTEXT_MAIN_DASHBOARD_CONTENT,c=_.CONTEXT_MAIN_DASHBOARD_SPEED,l=_.CONTEXT_MAIN_DASHBOARD_MONETIZATION,y=_.CONTEXT_ENTITY_DASHBOARD_TRAFFIC,k=_.CONTEXT_ENTITY_DASHBOARD_CONTENT,j=_.CONTEXT_ENTITY_DASHBOARD_SPEED,S=_.CONTEXT_ENTITY_DASHBOARD_MONETIZATION,A=O.AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY,w=O.AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY,T=O.AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION,C=O.AREA_MAIN_DASHBOARD_CONTENT_PRIMARY,N=O.AREA_MAIN_DASHBOARD_SPEED_PRIMARY,D=O.AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY,R=O.AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY,x=O.AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY,M=O.AREA_ENTITY_DASHBOARD_SPEED_PRIMARY,I=O.AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY;t.registerWidgetArea(A,{title:e.createElement(a.Fragment,null,Object(o.__)("Key metrics","google-site-kit"),e.createElement(p.a,null)),subtitle:h.a,style:u.b.BOXES,priority:1,CTA:f.a,filterActiveWidgets:function(e,t){return 1===t.length&&s.P.includes(t[0].slug)?[]:t}},n),t.registerWidgetArea(w,{title:Object(o.__)("Find out how your audience is growing","google-site-kit"),subtitle:Object(o.__)("Track your site’s traffic over time","google-site-kit"),style:u.b.BOXES,priority:1},i),t.registerWidgetArea(T,{subtitle:Object(o.__)("Understand how different visitor groups interact with your site","google-site-kit"),hasNewBadge:!0,style:u.b.BOXES,priority:2,CTA:b.e,Footer:b.a,filterActiveWidgets:function(e,t){var n=e(s.a).isAudienceSegmentationWidgetHidden();return void 0===n||n?[]:t}},i),t.registerWidgetArea(C,{title:Object(o.__)("See how your content is doing","google-site-kit"),subtitle:Object(o.__)("Keep track of your most popular pages and how people found them from Search","google-site-kit"),style:u.b.BOXES,priority:1},r),t.registerWidgetArea(N,{title:Object(o.__)("Find out how visitors experience your site","google-site-kit"),subtitle:Object(o.__)("Keep track of how fast your pages are and get specific recommendations on what to improve","google-site-kit"),style:u.b.BOXES,priority:1},c),t.registerWidgetArea(D,{title:Object(o.__)("Find out how much you’re earning from your content","google-site-kit"),subtitle:Object(o.__)("Track your AdSense revenue over time","google-site-kit"),style:u.b.BOXES,priority:1},l),t.registerWidgetArea(R,{title:Object(o.__)("Find out how your audience is growing","google-site-kit"),subtitle:Object(o.__)("Track traffic to this page over time","google-site-kit"),style:u.b.BOXES,priority:1},y),t.registerWidgetArea(x,{title:Object(o.__)("See how your content is doing","google-site-kit"),subtitle:Object(o.__)("Understand how people found this page from Search","google-site-kit"),style:u.b.BOXES,priority:1},k),t.registerWidgetArea(M,{title:Object(o.__)("Find out how visitors experience this page","google-site-kit"),subtitle:Object(o.__)("Keep track of how fast your page is and get specific recommendations on what to improve","google-site-kit"),style:u.b.BOXES,priority:1},j),t.registerWidgetArea(I,{title:Object(o.__)("Find out how much you’re earning from your content","google-site-kit"),subtitle:Object(o.__)("Track your AdSense revenue over time","google-site-kit"),style:u.b.BOXES,priority:1},S),t.registerWidget("keyMetricsSetupCTA",{Component:f.c,width:[t.WIDGET_WIDTHS.FULL],priority:1,wrapWidget:!1,isActive:function(e){return e(s.a).isAuthenticated()&&!1===e(g.c).isKeyMetricsSetupCompleted()}},[A]),t.registerWidget("keyMetricsConnectGA4All",{Component:v.a,width:[t.WIDGET_WIDTHS.FULL],priority:1,wrapWidget:!1,isActive:function(e){var t=e(s.a).getKeyMetrics();return!(e(d.a).isModuleConnected("analytics-4")||!Array.isArray(t))&&t.filter((function(e){return s.Q.includes(e)})).length>3}},[A]),t.registerWidget("keyMetricsAddMetricFirst",{Component:m.a,width:[t.WIDGET_WIDTHS.QUARTER],priority:3,wrapWidget:!1,isActive:function(e){var t=e(s.a).getKeyMetrics();return!(!Array.isArray(t)||t.length<2)&&t.length<4}},[A]),t.registerWidget("keyMetricsAddMetricSecond",{Component:m.a,width:[t.WIDGET_WIDTHS.QUARTER],priority:3,wrapWidget:!1,isActive:function(e){var t=e(s.a).getKeyMetrics();return!(!Array.isArray(t)||t.length<2)&&t.length<3}},[A]),t.registerWidget("keyMetricsAddMetricThird",{Component:m.a,width:[t.WIDGET_WIDTHS.QUARTER],priority:3,wrapWidget:!1,isActive:function(e){var t=e(s.a).getKeyMetrics();return!(!Array.isArray(t)||t.length<5)&&t.length<8}},[A]),t.registerWidget("keyMetricsAddMetricFourth",{Component:m.a,width:[t.WIDGET_WIDTHS.QUARTER],priority:3,wrapWidget:!1,hideOnBreakpoints:[E.b],isActive:function(e){var t=e(s.a).getKeyMetrics();return!(!Array.isArray(t)||t.length<5)&&t.length<7}},[A]),t.registerWidget("keyMetricsAddMetricFifth",{Component:m.a,width:[t.WIDGET_WIDTHS.QUARTER],priority:3,wrapWidget:!1,hideOnBreakpoints:[E.b],isActive:function(e){var t=e(s.a).getKeyMetrics();return!(!Array.isArray(t)||t.length<5)&&t.length<6}},[A])}}).call(this,n(4))},88:function(e,t,n){"use strict";n.r(t),n.d(t,"AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY",(function(){return i})),n.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY",(function(){return r})),n.d(t,"AREA_MAIN_DASHBOARD_TRAFFIC_AUDIENCE_SEGMENTATION",(function(){return a})),n.d(t,"AREA_MAIN_DASHBOARD_CONTENT_PRIMARY",(function(){return o})),n.d(t,"AREA_MAIN_DASHBOARD_SPEED_PRIMARY",(function(){return c})),n.d(t,"AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY",(function(){return l})),n.d(t,"AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY",(function(){return s})),n.d(t,"AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY",(function(){return u})),n.d(t,"AREA_ENTITY_DASHBOARD_SPEED_PRIMARY",(function(){return d})),n.d(t,"AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY",(function(){return g}));var i="mainDashboardKeyMetricsPrimary",r="mainDashboardTrafficPrimary",a="mainDashboardTrafficAudienceSegmentation",o="mainDashboardContentPrimary",c="mainDashboardSpeedPrimary",l="mainDashboardMonetizationPrimary",s="entityDashboardTrafficPrimary",u="entityDashboardContentPrimary",d="entityDashboardSpeedPrimary",g="entityDashboardMonetizationPrimary";t.default={AREA_MAIN_DASHBOARD_KEY_METRICS_PRIMARY:i,AREA_MAIN_DASHBOARD_TRAFFIC_PRIMARY:r,AREA_MAIN_DASHBOARD_CONTENT_PRIMARY:o,AREA_MAIN_DASHBOARD_SPEED_PRIMARY:c,AREA_MAIN_DASHBOARD_MONETIZATION_PRIMARY:l,AREA_ENTITY_DASHBOARD_TRAFFIC_PRIMARY:s,AREA_ENTITY_DASHBOARD_CONTENT_PRIMARY:u,AREA_ENTITY_DASHBOARD_SPEED_PRIMARY:d,AREA_ENTITY_DASHBOARD_MONETIZATION_PRIMARY:g}},89:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n(12),r=n.n(i),a=function(e,t){var n=t.dateRangeLength;r()(Array.isArray(e),"report must be an array to partition."),r()(Number.isInteger(n)&&n>0,"dateRangeLength must be a positive integer.");var i=-1*n;return{currentRange:e.slice(i),compareRange:e.slice(2*i,i)}}},9:function(e,t,n){"use strict";n.d(t,"I",(function(){return r.b})),n.d(t,"J",(function(){return r.c})),n.d(t,"F",(function(){return a.a})),n.d(t,"K",(function(){return a.b})),n.d(t,"H",(function(){return u})),n.d(t,"m",(function(){return d.a})),n.d(t,"B",(function(){return d.d})),n.d(t,"C",(function(){return d.e})),n.d(t,"y",(function(){return d.c})),n.d(t,"r",(function(){return d.b})),n.d(t,"z",(function(){return p})),n.d(t,"j",(function(){return h})),n.d(t,"i",(function(){return v})),n.d(t,"d",(function(){return k})),n.d(t,"c",(function(){return j})),n.d(t,"e",(function(){return S})),n.d(t,"b",(function(){return A})),n.d(t,"a",(function(){return w})),n.d(t,"f",(function(){return T})),n.d(t,"n",(function(){return C})),n.d(t,"w",(function(){return N})),n.d(t,"p",(function(){return D})),n.d(t,"G",(function(){return R})),n.d(t,"s",(function(){return x})),n.d(t,"v",(function(){return M})),n.d(t,"k",(function(){return I})),n.d(t,"o",(function(){return B.b})),n.d(t,"h",(function(){return B.a})),n.d(t,"t",(function(){return P.b})),n.d(t,"q",(function(){return P.a})),n.d(t,"A",(function(){return P.c})),n.d(t,"x",(function(){return L})),n.d(t,"u",(function(){return F})),n.d(t,"E",(function(){return V})),n.d(t,"D",(function(){return U.a})),n.d(t,"g",(function(){return G})),n.d(t,"L",(function(){return H})),n.d(t,"l",(function(){return q}));var i=n(14),r=n(36),a=n(75),o=n(33),c=n.n(o),l=n(96),s=n.n(l),u=function(e){return s()(JSON.stringify(function e(t){var n={};return Object.keys(t).sort().forEach((function(i){var r=t[i];r&&"object"===c()(r)&&!Array.isArray(r)&&(r=e(r)),n[i]=r})),n}(e)))};n(97);var d=n(83);function g(e){return e.replace(new RegExp("\\[([^\\]]+)\\]\\((https?://[^/]+\\.\\w+/?.*?)\\)","gi"),'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')}function f(e){return"<p>".concat(e.replace(/\n{2,}/g,"</p><p>"),"</p>")}function m(e){return e.replace(/\n/gi,"<br>")}function p(e){for(var t=e,n=0,i=[g,f,m];n<i.length;n++){t=(0,i[n])(t)}return t}var h=function(e){return e=parseFloat(e),isNaN(e)||0===e?[0,0,0,0]:[Math.floor(e/60/60),Math.floor(e/60%60),Math.floor(e%60),Math.floor(1e3*e)-1e3*Math.floor(e)]},v=function(e){var t=e&&!Number.isInteger(e)?new Date(e).getTime():e;return isNaN(t)||!t?0:t},b=n(15),E=n.n(b),_=n(12),O=n.n(_),y=n(2),k="Invalid dateString parameter, it must be a string.",j='Invalid date range, it must be a string with the format "last-x-days".',S=60,A=60*S,w=24*A,T=7*w;function C(){var e=function(e){return Object(y.sprintf)(
/* translators: %s: number of days */
Object(y._n)("Last %s day","Last %s days",e,"google-site-kit"),e)};return{"last-7-days":{slug:"last-7-days",label:e(7),days:7},"last-14-days":{slug:"last-14-days",label:e(14),days:14},"last-28-days":{slug:"last-28-days",label:e(28),days:28},"last-90-days":{slug:"last-90-days",label:e(90),days:90}}}function N(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!Object(i.isString)(e))return!1;var t=e.split("-");if(3!==t.length)return!1;var n=new Date(e);return Object(i.isDate)(n)&&!isNaN(n)}function D(e){O()(Object(i.isDate)(e)&&!isNaN(e),"Date param must construct to a valid date instance or be a valid date instance itself.");var t="".concat(e.getMonth()+1),n="".concat(e.getDate());return[e.getFullYear(),t.length<2?"0".concat(t):t,n.length<2?"0".concat(n):n].join("-")}function R(e){O()(N(e),k);var t=e.split("-"),n=E()(t,3),i=n[0],r=n[1],a=n[2];return new Date(i,r-1,a)}function x(e,t){return D(I(e,t*w))}function M(e){var t=e.split("-");return 3===t.length&&"last"===t[0]&&!Number.isNaN(t[1])&&!Number.isNaN(parseFloat(t[1]))&&"days"===t[2]}function I(e,t){O()(N(e)||Object(i.isDate)(e)&&!isNaN(e),k);var n=N(e)?Date.parse(e):e.getTime();return new Date(n-1e3*t)}var B=n(98),P=n(80);function L(e){var t=parseFloat(e)||0;return!!Number.isInteger(t)&&t>0}function F(e){if("number"==typeof e)return!0;var t=(e||"").toString();return!!t&&!isNaN(t)}var z=n(27),W=n.n(z),V=function(e){return Array.isArray(e)?W()(e).sort():e},U=n(89);function G(e,t){var n=function(e){return"0"===e||0===e};if(n(e)&&n(t))return 0;if(n(e)||Number.isNaN(e))return null;var i=(t-e)/e;return Number.isNaN(i)||!Number.isFinite(i)?null:i}var H=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},q=function(e){if(!e)return"";var t=e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(t)})).replace(/(\\)/g,"");return Object(i.unescape)(t)}},90:function(e,t,n){"use strict";(function(e,i){n.d(t,"c",(function(){return b})),n.d(t,"b",(function(){return _})),n.d(t,"a",(function(){return TourTooltips}));var r=n(6),a=n.n(r),o=n(81),c=n(30),l=n(1),s=n.n(l),u=n(2),d=n(3),g=n(23),f=n(7),m=n(36),p=n(107),h=n(18);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}var b={options:{arrowColor:"#3c7251",backgroundColor:"#3c7251",overlayColor:"rgba(0, 0, 0, 0.6)",textColor:"#fff",zIndex:2e4},spotlight:{border:"2px solid #3c7251",backgroundColor:"#fff"}},E={back:Object(u.__)("Back","google-site-kit"),close:Object(u.__)("Close","google-site-kit"),last:Object(u.__)("Got it","google-site-kit"),next:Object(u.__)("Next","google-site-kit")},_={disableAnimation:!0,styles:{arrow:{length:8,margin:56,spread:16},floater:{filter:"drop-shadow(rgba(60, 64, 67, 0.3) 0px 1px 2px) drop-shadow(rgba(60, 64, 67, 0.15) 0px 2px 6px)"}}},O="feature_tooltip_view",y="feature_tooltip_advance",k="feature_tooltip_return",j="feature_tooltip_dismiss",S="feature_tooltip_complete";function TourTooltips(t){var n=t.steps,r=t.tourID,l=t.gaEventCategory,s=t.callback,u="".concat(r,"-step"),A="".concat(r,"-run"),w=Object(d.useDispatch)(g.b).setValue,T=Object(d.useDispatch)(f.a).dismissTour,C=Object(d.useRegistry)(),N=Object(h.a)(),D=Object(d.useSelect)((function(e){return e(g.b).getValue(u)})),R=Object(d.useSelect)((function(e){return e(g.b).getValue(A)&&!1===e(f.a).isTourDismissed(r)}));Object(o.a)((function(){e.document.body.classList.add("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(r)),w(A,!0)}));var x=n.map((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({disableBeacon:!0,isFixed:!0,placement:"auto"},e)}));return i.createElement(c.e,{callback:function(t){!function(e){var t=e.index,n=e.action,i=e.lifecycle,r=e.size,a=e.status,o=e.type,s=t+1,u="function"==typeof l?l(N):l;o===c.b.TOOLTIP&&i===c.c.TOOLTIP?Object(m.b)(u,O,s):n===c.a.CLOSE&&i===c.c.COMPLETE?Object(m.b)(u,j,s):n===c.a.NEXT&&a===c.d.FINISHED&&o===c.b.TOUR_END&&r===s&&Object(m.b)(u,S,s),i===c.c.COMPLETE&&a!==c.d.FINISHED&&(n===c.a.PREV&&Object(m.b)(u,k,s),n===c.a.NEXT&&Object(m.b)(u,y,s))}(t);var n=t.action,i=t.index,a=t.status,o=t.step,d=t.type,g=n===c.a.CLOSE,f=!g&&[c.b.STEP_AFTER,c.b.TARGET_NOT_FOUND].includes(d),p=[c.d.FINISHED,c.d.SKIPPED].includes(a),h=g&&d===c.b.STEP_AFTER,v=p||h;if(c.b.STEP_BEFORE===d){var b,E,_=o.target;"string"==typeof o.target&&(_=e.document.querySelector(o.target)),null===(b=_)||void 0===b||null===(E=b.scrollIntoView)||void 0===E||E.call(b,{block:"center"})}f?function(e,t){w(u,e+(t===c.a.PREV?-1:1))}(i,n):v&&(e.document.body.classList.remove("googlesitekit-showing-feature-tour","googlesitekit-showing-feature-tour--".concat(r)),T(r)),s&&s(t,C)},continuous:!0,disableOverlayClose:!0,disableScrolling:!0,floaterProps:_,locale:E,run:R,showProgress:!0,stepIndex:D,steps:x,styles:b,tooltipComponent:p.a})}TourTooltips.propTypes={steps:s.a.arrayOf(s.a.object).isRequired,tourID:s.a.string.isRequired,gaEventCategory:s.a.oneOfType([s.a.string,s.a.func]).isRequired,callback:s.a.func}}).call(this,n(28),n(4))},92:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return Dismiss}));var i=n(5),r=n.n(i),a=n(6),o=n.n(a),c=n(16),l=n.n(c),s=n(1),u=n.n(s),d=n(2),g=n(3),f=n(73),m=n(41),p=n(10);function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Dismiss(t){var n=t.id,i=t.primary,a=void 0===i||i,o=t.dismissLabel,c=void 0===o?Object(d.__)("OK, Got it!","google-site-kit"):o,s=t.dismissExpires,u=void 0===s?0:s,h=t.disabled,b=t.onDismiss,E=void 0===b?function(){}:b,_=t.gaTrackingEventArgs,O=t.dismissOptions,y=Object(f.a)(n,null==_?void 0:_.category),k=Object(g.useDispatch)(m.a).dismissNotification,j=function(){var e=l()(r.a.mark((function e(t){return r.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,null==E?void 0:E(t);case 2:y.dismiss(null==_?void 0:_.label,null==_?void 0:_.value),k(n,v(v({},O),{},{expiresInSeconds:u}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return e.createElement(p.Button,{tertiary:!a,onClick:j,disabled:h},c)}Dismiss.propTypes={id:u.a.string,primary:u.a.bool,dismissLabel:u.a.string,dismissExpires:u.a.number,disabled:u.a.bool,onDismiss:u.a.func,gaTrackingEventArgs:u.a.shape({label:u.a.string,value:u.a.string})}}).call(this,n(4))},93:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c}));var i=n(24),r=n(130);function a(t,n){var i=document.querySelector(t);if(!i)return 0;var r=i.getBoundingClientRect().top,a=o(n);return r+e.scrollY-a}function o(e){var t=c(e),n=document.querySelectorAll(".googlesitekit-navigation, .googlesitekit-entity-header");return t+=Array.from(n).reduce((function(e,t){return e+t.offsetHeight}),0)}function c(t){var n=0,a=document.querySelector(".googlesitekit-header");return n=!!a&&"sticky"===e.getComputedStyle(a).position?function(e){var t=document.querySelector(".googlesitekit-header");if(t){if(e===i.b)return t.offsetHeight;var n=t.getBoundingClientRect().bottom;return n<0?0:n}return 0}(t):function(e){var t=document.querySelector("#wpadminbar");return t&&e!==i.b?t.offsetHeight:0}(t),(n=Object(r.a)(n))<0?0:n}}).call(this,n(28))},95:function(e,t,n){"use strict";(function(e){var i=n(1),r=n.n(i),a=n(11),o=n.n(a),c=n(10),l=n(20);function CTA(t){var n=t.title,i=t.headerText,r=t.headerContent,a=t.description,s=t.ctaLink,u=t.ctaLabel,d=t.ctaLinkExternal,g=t.ctaType,f=t.error,m=t.onClick,p=t["aria-label"],h=t.children;return e.createElement("div",{className:o()("googlesitekit-cta",{"googlesitekit-cta--error":f})},(i||r)&&e.createElement("div",{className:"googlesitekit-cta__header"},i&&e.createElement("h2",{className:"googlesitekit-cta__header_text"},i),r),e.createElement("div",{className:"googlesitekit-cta__body"},n&&e.createElement("h3",{className:"googlesitekit-cta__title"},n),a&&"string"==typeof a&&e.createElement("p",{className:"googlesitekit-cta__description"},a),a&&"string"!=typeof a&&e.createElement("div",{className:"googlesitekit-cta__description"},a),u&&"button"===g&&e.createElement(c.Button,{"aria-label":p,href:s,onClick:m},u),u&&"link"===g&&e.createElement(l.a,{href:s,onClick:m,"aria-label":p,external:d,hideExternalIndicator:d,arrow:!0},u),h))}CTA.propTypes={title:r.a.string.isRequired,headerText:r.a.string,description:r.a.oneOfType([r.a.string,r.a.node]),ctaLink:r.a.string,ctaLinkExternal:r.a.bool,ctaLabel:r.a.string,ctaType:r.a.string,"aria-label":r.a.string,error:r.a.bool,onClick:r.a.func,children:r.a.node,headerContent:r.a.node},CTA.defaultProps={title:"",headerText:"",headerContent:"",description:"",ctaLink:"",ctaLabel:"",ctaType:"link",error:!1,onClick:function(){}},t.a=CTA}).call(this,n(4))},97:function(e,t,n){"use strict";(function(e){n(51),n(53)}).call(this,n(28))},98:function(e,t,n){"use strict";(function(e){n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o}));var i=n(239),r=n(85),a=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Number.isNaN(Number(t)))return"";var a=n.invertColor,o=void 0!==a&&a;return Object(i.a)(e.createElement(r.a,{direction:t>0?"up":"down",invertColor:o}))},o=function(e,t){return e>0&&t>0?e/t-1:e>0?1:t>0?-1:0}}).call(this,n(4))},99:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return d}));var i=n(6),r=n.n(i),a=n(14),o=n(100),c=n(101);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){r()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={activeModules:[],isAuthenticated:!1,referenceSiteURL:"",trackingEnabled:!1,trackingID:"",userIDHash:"",userRoles:[]};function d(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,r=s(s({},u),t);r.referenceSiteURL&&(r.referenceSiteURL=r.referenceSiteURL.toString().replace(/\/+$/,""));var l=Object(o.a)(r,n),d=Object(c.a)(r,n,l,i),g={},f=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=JSON.stringify(t);g[i]||(g[i]=Object(a.once)(d)),g[i].apply(g,t)};return{enableTracking:function(){r.trackingEnabled=!0},disableTracking:function(){r.trackingEnabled=!1},initializeSnippet:l,isTrackingEnabled:function(){return!!r.trackingEnabled},trackEvent:d,trackEventOnce:f}}}).call(this,n(28))}},[[1267,1,0]]]);